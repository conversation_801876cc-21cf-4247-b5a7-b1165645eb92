import datetime
import itertools
import multiprocessing
import pprint

prices = {
    1: 5000.00 / 1 * 1,
    2: 15000.00 / 2 * 1,
    3: 50000.00 / 3 * 1,
    4: 150000.00 / 4 * 1,
    5: 250000.00 / 5 * 1,
    6: 500000.00 / 6 * 1,
    7: 750000.00 / 7 * 1,
    8: 900000.00 / 8 * 1,
    9: 1250000.00 / 9 * 1,
    10: 5000000.00 / 10 * 1,
}


plays = [
    [1, [12, 45, 17, 31, 47]],
    [1, [3, 12, 8, 36, 38]],
    [1, [22, 31, 47, 25, 13]],
    [1, [12, 26, 17, 49, 31]],
    [1, [41, 21, 49, 39, 26]],
    [1, [31, 2, 27, 29, 8]],
    [1, [36, 10, 31, 16, 23]],
    [1, [2, 34, 21, 7, 41]],
    [1, [27, 49, 15, 8, 26]],
    [1, [49, 46, 36, 16, 8]],
    [1, [48, 36, 28, 7, 41]],
    [1, [49, 16, 42, 6, 11]],
    [1, [6, 44, 23, 48, 7]],
    [1, [32, 14, 49, 33, 37]],
    [1, [47, 33, 4, 5, 3]],
    [1, [46, 12, 6, 2, 36]],
    [1, [27, 5, 42, 20, 12]],
    [1, [47, 33, 19, 5, 40]],
    [1, [17, 23, 21, 9, 43]],
    [1, [7, 35, 44, 39, 16]],
    [1, [13, 48, 31, 49, 43]],
]


winnings_dict = []


def search_number_occurences(numlist1: list, numlist2: list, number_of_match_limit=3) -> int:
    """COUNT COMMON NUMBERS"""
    match_count = 0

    for number in numlist1:
        if number in numlist2[1]:
            match_count += 1

    return match_count if match_count > (number_of_match_limit - 1) else False


def filter_winnings(combo, plays, prices, jackpot_amount):
    occurences = map(
        lambda user_play: search_number_occurences(combo, user_play, 3), plays
    )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

    play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
    over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

    play_occurences_with_amount = map(
        lambda played: get_potential_winning(played, prices, jackpot_amount),
        over3ocurrences,
    )
    data = list(play_occurences_with_amount)

    return data


def get_potential_winning(data, prices, jackpot):
    """
    data[1][0] : number_of_lines
    data[0] : number_of_matches
    """

    base_price = prices[data[1][0]]
    sharing = {5: 1, 4: 1, 3: 0.6, 2: 0.6 * 0.5}

    if data[0] == 5:
        winning = jackpot

    else:
        winning = (base_price * sharing[data[0]]) if not sharing[data[0]] == 5 else jackpot

    response = [*data, int(winning)]

    return response


then = datetime.datetime.now()
contribution = 600000000
best_combo = []
best_combo_amount = 0
winners = []
combo_dict = {}


def play(target_set, winnings_dict, plays):
    print(len(target_set))
    for index, combo in enumerate(target_set):
        winnings = filter_winnings(combo, plays, prices, 9999999999)

        check = index % 10000

        if check == 0 and index >= 10000:
            print(index)

        print(len(winnings))

        if len(winnings) == 0:
            t_amount = 0
            winnings_dict[combo] = t_amount
        else:
            _, __, amount = zip(*winnings)
            t_amount = sum(amount)
            winnings_dict[combo] = t_amount

            # if t_amount > 1000000:
            #     print(combo, ":", len(winnings), "Amount->", t_amount)

        if combo == (19, 20, 33, 37, 49):
            print(combo, ":", len(winnings))
            pprint.pprint(winnings)


def run_draw(plays, rtp, prices, jackpot_amount):
    then = datetime.datetime.now()
    step = 90000
    jobs = []
    random_combo = list(itertools.combinations(range(1, 20), 5))
    manager = multiprocessing.Manager()
    winnings_dict = manager.dict()

    for i in range(0, 2000000, step):
        proccess = multiprocessing.Process(target=play, args=(random_combo[i : i + step], winnings_dict, plays))
        jobs.append(proccess)
        proccess.start()

    for proc in jobs:
        proc.join()

    pprint.pprint(dict(winnings_dict))

    now = datetime.datetime.now()
    print((now - then).total_seconds())

    return dict(winnings_dict)


if __name__ == "__main__":
    winnings = run_draw(plays=plays, rtp=30000, prices=prices, jackpot_amount=9012100)
    print(winnings)
