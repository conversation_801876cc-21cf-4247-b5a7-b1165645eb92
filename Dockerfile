FROM python:3.10-alpine

WORKDIR /app

RUN apk add vim python3 py3-pip

COPY . /app

# install dependencies
RUN apk update && apk add --no-cache \
    python3-dev \
    py3-pip \
    build-base \
    musl-dev \ 
    lapack-dev \
    openblas-dev \
    py3-wheel \
    gfortran \
    && pip install --no-cache-dir ruamel.yaml.clib \
    && pip install --no-cache-dir pandas \
    && pip install --no-cache-dir -r docker.requirements.txt \
    && apk del build-base gfortran


EXPOSE 8000