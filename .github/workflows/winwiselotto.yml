name: Django CI

on:
  push:
    branches: [master, dev]
  pull_request:
    branches: [master, dev]

jobs:
  build-lint-and-test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: github_actions
        ports:
          - 5432:5432
        options: >-
          --health-cmd "pg_isready -U postgres"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:latest
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    env:
      ENVIRONMENT: ci
      SECRET_KEY: ${{ secrets.SECRET_KEY }}
      ALLOWED_HOSTS: .localhost,127.0.0.1,0.0.0.0
      DATABASE_NAME: github_actions
      DATABASE_USER: postgres
      DATABASE_PASSWORD: postgres
      DATABASE_HOST: localhost
      WOVEN_BANK_CODE_SWITCH: ${{ secrets.WOVEN_BANK_CODE_SWITCH }}
      DB_HOST: ${{ secrets.DB_HOST }}
      DB_PORT: 5432
      DEBUG: 1
      LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE_WITH_ACCOUNT: ${{ secrets.LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE_WITH_ACCOUNT }}
      LIBERTY_LOTTO_CHECK_WINNERS_LINK_LIST: ${{ secrets.LIBERTY_LOTTO_CHECK_WINNERS_LINK_LIST }}
      LIBERTY_LOTTO_WINNERS_LIST: ${{ secrets.LIBERTY_LOTTO_WINNERS_LIST }}
      LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE: ${{ secrets.LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE }}
      LIBERTY_LOTTO_PAYMENT_RECEIPT_TEMPLATE: ${{ secrets.LIBERTY_LOTTO_PAYMENT_RECEIPT_TEMPLATE }}
      LIBERTY_LOTTO_DEMO_SMS_FOR_NON_WINNERS: ${{ secrets.LIBERTY_LOTTO_DEMO_SMS_FOR_NON_WINNERS }}
      WATUPAY_PUBLIC_KEY: ${{ secrets.WATUPAY_PUBLIC_KEY }}
      WATUPAY_MERCHANT_REFERENCE: ${{ secrets.WATUPAY_MERCHANT_REFERENCE }}
      PABBLY_AUTH_USER: ${{ secrets.PABBLY_AUTH_USER }}
      PABBLY_AUTH_PASS: ${{ secrets.PABBLY_AUTH_PASS }}
      PABBLY_AUTH_USERNAME: ${{ secrets.PABBLY_AUTH_USERNAME }}
      PABBLY_AUTH_PASSWORD: ${{ secrets.PABBLY_AUTH_PASSWORD }}
      BITLY_TOKEN: ${{ secrets.BITLY_TOKEN }}
      WOVEN_DEVELOPMENT_MODE: 1
      TEST_DESTINATION_BANK: ${{ secrets.TEST_DESTINATION_BANK }}
      LIVE_DESTINATION_BANK: ${{ secrets.LIVE_DESTINATION_BANK }}
      WOVEN_API_KEY_TEST: ${{ secrets.WOVEN_API_KEY_TEST }}
      WOVEN_API_KEY_LIVE: ${{ secrets.WOVEN_API_KEY_LIVE }}
      WHISPER_KEY: ${{ secrets.WHISPER_KEY }}
      LIBERTY_LOTTO_PAYMENT_COLLECTION: ${{ secrets.LIBERTY_LOTTO_PAYMENT_COLLECTION }}
      PAYSTACK_BEARER: ${{ secrets.PAYSTACK_BEARER }}
      PAYSTACK_PUBLIC_KEY: ${{ secrets.PAYSTACK_PUBLIC_KEY }}
      PAYSTACK_ENVIRONMENT: test
      PAYSTACK_BS64: ${{ secrets.PAYSTACK_BS64 }}
      WOVEN_PAYOUT_PIN: ${{ secrets.WOVEN_PAYOUT_PIN }}
      WOVEN_DISBURSEMENT_SOURCE_ACCOUNT: ${{ secrets.WOVEN_DISBURSEMENT_SOURCE_ACCOUNT }}
      WOVEN_DISBURSEMENT_PAYMENT_PIN: ${{ secrets.WOVEN_DISBURSEMENT_PAYMENT_PIN }}
      WOVEN_DISBURSEMENT_API_SECRET: ${{ secrets.WOVEN_DISBURSEMENT_API_SECRET }}
      LOAN_DISK_SEC_KEY: ${{ secrets.LOAN_DISK_SEC_KEY }}
      LOAN_DISK_PUBLICK_KEY: ${{ secrets.LOAN_DISK_PUBLICK_KEY }}
      LOAN_DISK_BRANCH_ID: ${{ secrets.LOAN_DISK_BRANCH_ID }}
      WINWISE_LOTTO_COLLECTION_S4L_YCASH: ${{ secrets.WINWISE_LOTTO_COLLECTION_S4L_YCASH }}
      WINWISE_LOTTO_COLLECTION_INSTANTCASH: ${{ secrets.WINWISE_LOTTO_COLLECTION_INSTANTCASH }}
      PICKY_ASSIST_TOKEN: ${{ secrets.PICKY_ASSIST_TOKEN }}
      WEMA_BANK_CODE: ${{ secrets.WEMA_BANK_CODE }}
      CMB_BANK_CODE: ${{ secrets.CMB_BANK_CODE }}
      SPARKLE_BANK_CODE: ${{ secrets.SPARKLE_BANK_CODE }}
      WOVEN_CALL_BACK: ${{ secrets.WOVEN_CALL_BACK }}
      LIBERTY_LOTTO_RESEND_ACCOUNT_COLLECT_SMS: ${{ secrets.LIBERTY_LOTTO_RESEND_ACCOUNT_COLLECT_SMS }}
      CLOUDINARY_CLOUD_NAME: ${{ secrets.CLOUDINARY_CLOUD_NAME }}
      CLOUDINARY_API_KEY: ${{ secrets.CLOUDINARY_API_KEY }}
      CLOUDINARY_API_SECRET: ${{ secrets.CLOUDINARY_API_SECRET }}
      TRANSACTION_OTP_TEMPLATE: ${{ secrets.TRANSACTION_OTP_TEMPLATE }}
      REFERRAL_SMS_TEMPLATE: ${{ secrets.REFERRAL_SMS_TEMPLATE }}
      RawPayStack: ${{ secrets.RawPayStack }}
      LOTTO_FRONTEND_LINK: ${{ secrets.LOTTO_FRONTEND_LINK }}
      libertyassured: ${{ secrets.libertyassured }}
      libertypay: ${{ secrets.libertypay }}
      libertycredi: ${{ secrets.libertycredi }}
      MAILGUN_API_KEY: ${{ secrets.MAILGUN_API_KEY }}
      AGENCY_BANKING_BASE_URL: ${{ secrets.AGENCY_BANKING_BASE_URL }}
      AGENCY_BANKING_USEREMAIL: ${{ secrets.AGENCY_BANKING_USEREMAIL }}
      AGENCY_BANKING_PASSWORD: ${{ secrets.AGENCY_BANKING_PASSWORD }}
      RED_BILLER_PRIVATE_KEY: ${{ secrets.RED_BILLER_PRIVATE_KEY }}
      RED_BILLER_AUTH_BEARER: ${{ secrets.RED_BILLER_AUTH_BEARER }}
      CORAL_PAY_USERNAME: ${{ secrets.CORAL_PAY_USERNAME }}
      CORAL_PAY_PASSWORD: ${{ secrets.CORAL_PAY_PASSWORD }}
      CORAL_PAY_MERCHANT_ID: ${{ secrets.CORAL_PAY_MERCHANT_ID }}
      CORAL_PAY_AUTH_USERNAME: ${{ secrets.CORAL_PAY_AUTH_USERNAME }}
      CORAL_PAY_AUTH_PASSWORD: ${{ secrets.CORAL_PAY_AUTH_PASSWORD }}
      ENGAGE_API_KEY: ${{ secrets.ENGAGE_API_KEY }}
      ENGAGE_SECRET_KEY: ${{ secrets.ENGAGE_SECRET_KEY }}
      CUTTLY_API_KEY: ${{ secrets.CUTTLY_API_KEY }}
      LIBERTY_VAS_BASE_URL: ${{ secrets.LIBERTY_VAS_BASE_URL }}
      LIBERTY_VAS_AUTH_USERNAME: ${{ secrets.LIBERTY_VAS_AUTH_USERNAME }}
      LIBERTY_VAS_AUTH_PASSWORD: ${{ secrets.LIBERTY_VAS_AUTH_PASSWORD }}
      USER_BVN: ${{ secrets.USER_BVN }}
      CHAMPIONS_LEAGUE_SEASON_YEAR: ${{ secrets.CHAMPIONS_LEAGUE_SEASON_YEAR }}
      ENGLISH_LEAGUE_SEASON_YEAR: ${{ secrets.ENGLISH_LEAGUE_SEASON_YEAR }}
      EUROPA_LEAGUE_SEASON_YEAR: ${{ secrets.EUROPA_LEAGUE_SEASON_YEAR }}
      LA_LIGA_SEASON_YEAR: ${{ secrets.LA_LIGA_SEASON_YEAR }}
      WORLD_CUP_SEASON_YEAR: ${{ secrets.WORLD_CUP_SEASON_YEAR }}
      FOOTBALL_API_KEY: ${{ secrets.FOOTBALL_API_KEY }}
      TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
      ACTIVATE_BOT: False
      MAILGUN_WEBHOOK_SIGNING_KEY: ${{ secrets.MAILGUN_WEBHOOK_SIGNING_KEY }}
      LIBERTYPAY_USER_AUTH: ${{ secrets.LIBERTYPAY_USER_AUTH }}
      LIBERTYPAY_AGENTS_AUTH: ${{ secrets.LIBERTYPAY_AGENTS_AUTH }}
      PAYSTACK_CALLBACK_URL: ${{ secrets.PAYSTACK_CALLBACK_URL }}
      AGENCY_BANKING_TRANSACTION_PIN: ${{ secrets.AGENCY_BANKING_TRANSACTION_PIN }}
      IP_WHITELIST: ${{ secrets.IP_WHITELIST }}
      DOMAIN_WHITELIST: ${{ secrets.DOMAIN_WHITELIST }}
      WHITELISTING_TO_USE: ${{ secrets.WHITELISTING_TO_USE }}
      AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID: ${{ secrets.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID }}
      SELF_SERVICE_AUTH: ${{ secrets.SELF_SERVICE_AUTH }}
      REMITTANCE_EMAIL_LIST: ${{ secrets.REMITTANCE_EMAIL_LIST }}
      BBC_PARTNER_ID: ${{ secrets.BBC_PARTNER_ID }}
      BBC_PARTNER_PASSWORD: ${{ secrets.BBC_PARTNER_PASSWORD }}
      BBC_SERVICE_ID: ${{ secrets.BBC_SERVICE_ID }}
      BBC_USSD_SERVICE_ACTIVATION_NUMBER: ${{ secrets.BBC_USSD_SERVICE_ACTIVATION_NUMBER }}
      BBC_IP: ${{ secrets.BBC_IP }}
      BBC_ON_DEMAND_SERVICE_USERNAME: ${{ secrets.BBC_ON_DEMAND_SERVICE_USERNAME }}
      BBC_ON_DEMAND_SERVICE_PASSWORD: ${{ secrets.BBC_ON_DEMAND_SERVICE_PASSWORD }}
      BBC_ON_DEMAND_SERVICE_ID: ${{ secrets.BBC_ON_DEMAND_SERVICE_ID }}
      BBC_ON_DEMAND_SERVICE_PRODUCT_ID: ${{ secrets.BBC_ON_DEMAND_SERVICE_PRODUCT_ID }}
      BBC_SMS_SERVICE_USERNAME: ${{ secrets.BBC_SMS_SERVICE_USERNAME }}
      BBC_SMS_SERVICE_PASSWORD: ${{ secrets.BBC_SMS_SERVICE_PASSWORD }}
      SCRATCH_CARD_EMAIL: ${{ secrets.SCRATCH_CARD_EMAIL }}
      SCRATCH_CARD_PASSWORD: ${{ secrets.SCRATCH_CARD_PASSWORD }}
      EXTERNAL_DB_NAME: ${{ secrets.EXTERNAL_DB_NAME }}
      EXTERNAL_DB_PASSWORD: ${{ secrets.EXTERNAL_DB_PASSWORD }}
      EXTERNAL_DB_USERNAME: ${{ secrets.EXTERNAL_DB_USERNAME }}
      EXTERNAL_DB_HOST: ${{ secrets.EXTERNAL_DB_HOST }}
      ZENITH_USER: ${{ secrets.ZENITH_USER }}
      ZENITH_PROTECTOR: ${{ secrets.ZENITH_PROTECTOR }}
      ZENITH_SOURCE_ACCOUNT: ${{ secrets.ZENITH_SOURCE_ACCOUNT }}
      SECURED_D_IP_ADDRESS: ${{ secrets.SECURED_D_IP_ADDRESS }}
      CORE_BANKING_EMAIL: ${{ secrets.CORE_BANKING_EMAIL }}
      CORE_BANKING_PASSWORD: ${{ secrets.CORE_BANKING_PASSWORD }}
      LIBERTY_USSD_BASE_URL: ${{ secrets.LIBERTY_USSD_BASE_URL }}
      BRANCH_LOCATION_CODES: ${{ secrets.BRANCH_LOCATION_CODES }}
      AIRTIME_VENDING_AK: ${{ secrets.AIRTIME_VENDING_AK }}
      AIRTIME_VENDING_SERVER: ${{ secrets.AIRTIME_VENDING_SERVER }}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      COMMISSION_AGENCY_BANKING_USEREMAIL: ${{ secrets.COMMISSION_AGENCY_BANKING_USEREMAIL }}
      COMMISSION_AGENCY_BANKING_PASSWORD: ${{ secrets.COMMISSION_AGENCY_BANKING_PASSWORD }}
      EXTERNAL_DB2_NAME: ${{ secrets.EXTERNAL_DB2_NAME }}
      EXTERNAL_DB2_USERNAME: ${{ secrets.EXTERNAL_DB2_USERNAME }}
      EXTERNAL_DB2_PASSWORD: ${{ secrets.EXTERNAL_DB2_PASSWORD }}
      EXTERNAL_DB2_HOST: ${{ secrets.EXTERNAL_DB2_HOST }}
      DIGITALOCEAN_KEY: ${{ secrets.DIGITALOCEAN_KEY }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      PERPLEXITY_API_TOKEN: ${{ secrets.PERPLEXITY_API_TOKEN }}
      REDIS_HOST: localhost
      WINWISE_LOTTO_BACKEND_URL: xxx
      ACCOUNT_ACTIVATION_OTP_TEMPLATE: xx
      AGENCY_BANKING_SUPER_TOKEN: xx
      AGENCY_BANKING_TOKEN: xxx
      POS_INSTANT_CASHOUT_TEST_FOR_POS_AGENT: xxx
      DEFAULT_AGENT_ID: xxx
      UVERIFY_TOKEN: xxx
      DEV_TEST_USERS: xxx
      DJANGO_SETTINGS_MODULE: liberty_lotto.test_settings

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          cache: 'pip'

      - name: Wait for PostgreSQL to be ready
        run: |
          until pg_isready -h localhost -p 5432 -U postgres; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 1
          done

      - name: Create PostgreSQL database and user
        run: |
          psql -h localhost -U postgres -c "CREATE DATABASE test_db;"
          psql -h localhost -U postgres -c "CREATE USER test_user WITH PASSWORD 'test_password';"
          psql -h localhost -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE test_db TO test_user;"
        env:
          PGPASSWORD: postgres

      - name: Install dependencies with specific version constraints
        run: |
          python -m pip install --upgrade pip setuptools wheel
          pip install "Django<5.1,>=4.2" "flake8<6.1.0" "pycodestyle<2.12.0,>=2.11.0" "autopep8<2.3.0"
          grep -v -E "^(autopep8|flake8|pycodestyle|Django|p)==" requirements.txt > requirements_filtered.txt || true
          if [ -s requirements_filtered.txt ]; then pip install -r requirements_filtered.txt; fi

      - name: Install additional dependencies
        run: python -m pip install black pytest pytest-django pre-commit autoflake

      - name: Install autoflake
        run: pip install autoflake

      - name: Run pre-commit
        run: |
          python manage.py makemigrations account main ads_tracker pos_app referral_system referral_system sport_app wallet_app web_app wyse_ussd awoof_app banker_lottery resources_app sms_campaign ticket_price 
          python manage.py migrate
          python manage.py collectstatic --noinput
          pre-commit run --all-files || echo "pre-commit run failed"

      