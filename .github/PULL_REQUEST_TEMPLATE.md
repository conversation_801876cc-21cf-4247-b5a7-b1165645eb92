# Pull Request Template

# Description
<!-- Provide a concise description of what your changes do and why -->

# Related Issue
<!-- Link to the related issue(s) this PR addresses -->
Closes #

# Type of Change
<!-- Check all that apply -->
- [ ] Bug fix (non-breaking change that fixes an issue)
- [ ] New feature (non-breaking change that adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Database migration
- [ ] Documentation update
- [ ] Refactoring (no functional changes)
- [ ] Performance improvement
- [ ] Test improvements

# Django-specific Changes
<!-- Check all that apply -->
- [ ] Model changes
- [ ] View/URL changes
- [ ] Template changes
- [ ] Form changes
- [ ] Admin interface changes
- [ ] Settings changes
- [ ] Middleware changes
- [ ] Migration generation
- [ ] Static file changes
- [ ] Management command changes

# How Has This Been Tested?
<!-- Describe the tests you've run to verify your changes -->
- [ ] Unit tests added/modified
- [ ] Integration tests added/modified
- [ ] Manual testing performed
- [ ] Browser testing performed

