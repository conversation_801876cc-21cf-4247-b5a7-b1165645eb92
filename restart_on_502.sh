#!/bin/bash

URL="https://libertydraw.com/admin"
LOG_FILE="/var/log/restart_services.log"

# Ensure the log file is writable
sudo touch "$LOG_FILE"
sudo chmod 666 "$LOG_FILE"

# Get the final HTTP status code after following redirects
STATUS_CODE=$(curl -L -s -o /dev/null -w "%{http_code}" "$URL")

# Log the status check
echo "$(date): Checked $URL - Final Status Code: $STATUS_CODE" | sudo tee -a "$LOG_FILE"

# If status code is 502, restart services
if [ "$STATUS_CODE" -eq 502 ]; then
  echo "$(date): Bad Gateway detected! Restarting gunicorn2 and uvicorn..." | sudo tee -a "$LOG_FILE"
  sudo systemctl restart gunicorn2 uvicorn
  echo "$(date): Restart command executed." | sudo tee -a "$LOG_FILE"
fi