import itertools
import random
from pprint import pprint

from utils import gen_rand_n_num_lists, get_potential_winning, search_number_occurences

from overide_print import print

number_of_plays = 10000
average_stake_perplay = 950
jackpot_stakes = True
total_stake = number_of_plays * average_stake_perplay
rto = 0.3
rtp = (1 - rto) * total_stake
best_match = 0
best_match_combo: list

best_match_with_jkpt = 0
best_match_with_jkpt_combo: list


best_match_jkpt = 0
best_match_combo_jkpt: list

count5s = 0

prices = {1: 5000.00, 2: 15000.00, 3: 50000.00, 4: 150000.00, 5: 250000.00, 6: 500000.00, 7: 750000.00, 8: 900000.00, 9: 1250000.00, 10: 1500000.00}


def draw(plays):
    random_combo = list(itertools.combinations(range(1, 10), 5))
    random.shuffle(random_combo)

    best_match = 0
    best_match_combo = []

    best_match_with_jkpt = 0
    best_match_with_jkpt_combo = []

    best_match_witho_jkpt = 0
    best_match_witho_jkpt_combo = []

    for index, combo in enumerate(random_combo):
        occurences = map(
            lambda user_play: search_number_occurences(combo, user_play), plays
        )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

        play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
        over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

        play_occurences_with_amount = map(lambda played: get_potential_winning(played, prices), over3ocurrences)

        total_sum = 0
        play_occurences_with_amount = list(play_occurences_with_amount)
        # CALCULATE THE TOTAL WINNING AMOUNT
        for index, ocurrence in enumerate(play_occurences_with_amount):
            total_sum += ocurrence[-1]

        has_jkpt = bool(list(filter(lambda x: x[0] == 5, play_occurences_with_amount)))
        print(list(filter(lambda x: x[0] == 5, play_occurences_with_amount)))
        print(has_jkpt)
        match = total_sum / rtp * 100

        if match > 95 and match < 105:
            input()

        if match > best_match and match < 100:
            best_match = match
            best_match_combo = combo
        if match > best_match_with_jkpt and match < 100 and has_jkpt:
            best_match_with_jkpt = match
            best_match_with_jkpt_combo = combo
        if match > best_match_witho_jkpt and match < 100 and (not has_jkpt):
            best_match_witho_jkpt = match
            best_match_witho_jkpt_combo = combo

    return (best_match, best_match_combo, best_match_with_jkpt, best_match_with_jkpt_combo, best_match_witho_jkpt, best_match_witho_jkpt_combo)


if __name__ == "__main__":
    plays = gen_rand_n_num_lists(number_of_plays)

    best_match, best_match_combo, _, __, ___, ____ = draw(plays)
    print(best_match, best_match_combo, _, __, ___, ____)

    best_match, best_match_combo, _, __, ___, ____ = draw(plays)
    print(best_match, best_match_combo, _, __, ___, ____)

    occurences = map(
        lambda user_play: search_number_occurences(best_match_combo, user_play), plays
    )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

    play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
    over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

    play_occurences_with_amount = map(lambda played: get_potential_winning(played, prices), over3ocurrences)
    data = list(play_occurences_with_amount)
    pprint(data)
    # print(len(data))
    # print("%", len(data)/number_of_plays*100)

    # best_match = 0
    # best_match_combo=[]
    # print("++++++++++++++++++++++++++++")
    # pprint(list(filter(lambda x: x[0]==5, data)))
    # print("++++++++++++++++++++++++++++")
    # pprint(plays[:5])
