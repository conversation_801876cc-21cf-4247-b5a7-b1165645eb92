# import numbers
# import random
# from utils import gen_rand_n_num_lists, search_number_occurences, get_potential_winning
# import itertools, functools
# from pprint import pprint

# number_of_plays = 10000
# average_stake_perplay = 950
# jackpot_stakes = True
# total_stake = number_of_plays * average_stake_perplay
# rto = 0.3
# rtp = (1-rto) * total_stake
# best_match = 0
# best_match_combo:list


# best_match_jkpt = 0
# best_match_combo_jkpt:list

# plays = gen_rand_n_num_lists(number_of_plays)
# count5s = 0

# prices =  {
#             1:5000.00,
#             2:15000.00,
#             3:50000.00,
#             4:150000.00,

#             5:250000.00,
#             6:500000.00,
#             7:750000.00,
#             8:900000.00,
#             9:1250000.00,
#             10:1500000.00
#         }


# def draw():

#     random_combo = list(itertools.combinations(range(1, 49), 5))
#     random.shuffle(random_combo)

#     best_match = 0
#     best_match_combo=[]

#     for index, combo in enumerate(random_combo):

#         # print(combo, index)
#         occurences = map(lambda user_play: search_number_occurences(combo,user_play), plays) # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

#         play_occurences = (zip(occurences, plays)) # Match number of occurences to number of matches found in selected combination
#         over3ocurrences = list(filter(lambda x: x[0], play_occurences)) # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

#         play_occurences_with_amount = map(
#                                                 lambda played: get_potential_winning(played, prices),
#                                                 over3ocurrences
#                                             )

#         total_sum = 0

#         # CALCULATE THE TOTAL WINNING AMOUNT
#         for index, ocurrence in enumerate(play_occurences_with_amount):
#             total_sum += ocurrence[-1]

#         match = total_sum/rtp*100

#         # print("Winnings :", total_sum)
#         # print("Winners : ", index)
#         # print("Players : ", number_of_plays)
#         # print("Win % : ", index/number_of_plays*100)
#         # print("RTP : ", rtp)
#         # print("Match : ", match, "%")
#         # print("                   ")
#         # print("                   ")
#         # print("                   ")

#         if match > 95 and match < 105:
#             input()

#         if match > best_match and match < 100: best_match = match;best_match_combo=combo

#     return (best_match, best_match_combo)

# def draw2():

#     random_combo = list(itertools.combinations(range(1, 12), 5))
#     random.shuffle(random_combo)

#     best_match = 0
#     best_match_combo=[]

#     for index, combo in enumerate(random_combo):

#         # print(combo, index)
#         occurences = map(lambda user_play: search_number_occurences(combo,user_play), plays) # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

#         play_occurences = (zip(occurences, plays)) # Match number of occurences to number of matches found in selected combination
#         over3ocurrences = list(filter(lambda x: x[0], play_occurences)) # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

#         play_occurences_with_amount = map(
#                                                 lambda played: get_potential_winning(played, prices),
#                                                 over3ocurrences
#                                             )

#         # print(list(filter(lambda x: x[0]==5, play_occurences_with_amount)))

#         total_sum = 0

#         # CALCULATE THE TOTAL WINNING AMOUNT
#         for index, ocurrence in enumerate(play_occurences_with_amount):
#             total_sum += ocurrence[-1]

#         match = total_sum/rtp*100

#         play_occurences_with_amount = list(play_occurences_with_amount)

#         has_jackpot = False # bool(list(filter(lambda x: x[0]==5, play_occurences_with_amount)))
#         print(list(filter(lambda x: x[0]==5, play_occurences_with_amount)))
#         if has_jackpot:
#             print((match, combo))

#         # print("Winnings :", total_sum)
#         # print("Winners : ", index)
#         # print("Players : ", number_of_plays)
#         # print("Win % : ", index/number_of_plays*100)
#         # print("RTP : ", rtp)
#         # print("Match : ", match, "%")
#         # print("                   ")
#         # print("                   ")
#         # print("                   ")

#         if match > 95 and match < 105:
#             input()

#         if match > best_match and match < 100: best_match = match;best_match_combo=combo

#     return (best_match, best_match_combo)

# best_match, best_match_combo = draw()
# print(best_match, best_match_combo)

# occurences = map(lambda user_play: search_number_occurences(best_match_combo, user_play), plays) # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

# play_occurences = (zip(occurences, plays)) # Match number of occurences to number of matches found in selected combination
# over3ocurrences = list(filter(lambda x: x[0], play_occurences)) # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

# play_occurences_with_amount = map(
#                                     lambda played: get_potential_winning(played, prices),
#                                     over3ocurrences
#                                 )
# data = list(play_occurences_with_amount)
# pprint(data)
# print(len(data))
# print("%", len(data)/number_of_plays*100)

# best_match = 0
# best_match_combo=[]
# print("++++++++++++++++++++++++++++")
# pprint(list(filter(lambda x: x[0]==5, data)))
# print("++++++++++++++++++++++++++++")

# if not list(filter(lambda x: x[0]==5, data)):

#     best_match, best_match_combo = draw2()
#     print(best_match, best_match_combo)

#     occurences = map(lambda user_play: search_number_occurences(best_match_combo, user_play), plays) # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

#     play_occurences = (zip(occurences, plays)) # Match number of occurences to number of matches found in selected combination
#     over3ocurrences = list(filter(lambda x: x[0], play_occurences)) # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

#     play_occurences_with_amount = map(
#                                         lambda played: get_potential_winning(played, prices),
#                                         over3ocurrences
#                                     )
#     data = list(play_occurences_with_amount)
#     pprint(data)
#     print(len(data))
#     print("%", len(data)/number_of_plays*100)

#     best_match = 0
#     best_match_combo=[]


import multiprocessing
import time


def basic_func(x):
    if x == 0:
        return "zero"
    elif x % 2 == 0:
        return "even"
    else:
        return "odd"


def multiprocessing_func(x):
    x * x
    # time.sleep(2)
    # print('{} squared results in a/an {} number'.format(x, basic_func(y)))


size = 30000

if __name__ == "__main__":
    starttime = time.time()
    processes = []
    for i in range(0, size):
        p = multiprocessing.Process(target=multiprocessing_func, args=(i,))
        processes.append(p)
        p.start()

    for process in processes:
        process.join()

    print("Async took {} seconds".format(time.time() - starttime))

    starttime = time.time()
    processes = []
    for i in range(0, size):
        multiprocessing_func(i)

    print("Sync took {} seconds".format(time.time() - starttime))
