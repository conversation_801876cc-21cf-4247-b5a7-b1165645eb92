import math
import random

import pandas as pd

from main.models import ConstantVariable


class WyseCash:
    _ORIGINAL_REWARDS = {10: 10000, 50: 50000, 100: 100000, 200: 200000}

    range_diff = ConstantVariable.wysecash_percentage_range()

    _rto_rtp_const = ConstantVariable.rto_rtp()
    _RTO_PERCENT = _rto_rtp_const["rto"]
    _RTP_PERCENT = _rto_rtp_const["rtp"]

    _REWARDS_PERCENT = {
        10: (random.choice(range(range_diff[10]["min"], 110, range_diff[10]["step"])) / 100),
        50: (random.choice(list(range(range_diff[50]["min"], 110, range_diff[50]["step"])) + [100]) / 100),
        100: (random.choice(range(range_diff[100]["min"], 110, range_diff[100]["step"])) / 100),
        200: (random.choice(range(range_diff[200]["min"], 110, range_diff[200]["step"])) / 100),
    }

    # _REWARDS_PERCENT = {
    #     10: (random.choice(range(50, 110, 10)) / 100),# mininum, step
    #     50: (random.choice(list(range(30, 110, 30)) + [100]) / 100),
    #     100: (random.choice(range(20, 110, 20)) / 100),
    #     200: (random.choice(range(20, 110, 20)) / 100),
    # }

    # _ORIGINAL_REWARDS = {
    #     10: 10000 ,
    #     50: 50000 ,
    #     100: 100000 ,
    #     200: 200000
    # }

    # _REWARD_FACTORS = {
    #     10: 50 ,
    #     50: 30 ,
    #     100: 20 ,
    #     200: 20
    # }

    _REWARD_FACTORS = ConstantVariable.objects.all().last().wyse_cash_running_balance

    REWARDS = {
        10: 10000 * _REWARD_FACTORS[str(10)] / 100 * _REWARDS_PERCENT[10],
        50: 50000 * _REWARD_FACTORS[str(50)] / 100 * _REWARDS_PERCENT[50],
        100: 100000 * _REWARD_FACTORS[str(100)] / 100 * _REWARDS_PERCENT[100],
        200: 200000 * _REWARD_FACTORS[str(200)] / 100 * _REWARDS_PERCENT[200],
    }

    _UPPER_TIER_JACKPOT_PERCENT = 25
    _MIDDLE_TIER_JACKPOT_PERCENT = 25
    _LOWER_TIER_JACKPOT_PERCENT = 20
    _LAST_TIER_JACKPOT_PERCENT = 15

    # REWARDS = {
    #     10: (10000 * 0.5),
    #     50: (50000 * 0.3),
    #     100: (100000 * 0.2),
    #     200: (200000 * 0.2),
    # }

    def __init__(self, query_set) -> None:
        self.query_set = query_set
        self.decision_dict: dict = {}

    def run_decisioning(self):
        # i'm using pandas' data frame in order to avoid
        # different query thereofore i can use the
        # dataframe created to do heavy manipulation
        # and decisionings with less queries count --
        #                                            |
        #                                            v

        game_df = pd.DataFrame.from_records(self.query_set.values())
        # print(game_df)

        # total_player = game_df["id"].count()  # total player count
        total_revenue = game_df["stake_amount"].sum()  # Estimate total amount stake

        # rto = total_revenue * self._RTO_PERCENT  # return to owner
        rtp = total_revenue * self._RTP_PERCENT  # return to player
        GLOBAL_JACKPOT = rtp * 0.05

        rtp = rtp - GLOBAL_JACKPOT

        calc_percent = lambda amount: round(100 * amount / float(total_revenue), 2)  # noqa  # returns percentage (0.5) i.e 0.5 == 50%

        calc_percent_of_amount = lambda amount, number: round(number / 100 * float(amount), 2)  # noqa

        #

        filter_pool_sum_up_stake_amnt = lambda pool: game_df[game_df["pool"] == pool]["stake_amount"].sum()  # noqa

        sum_last_tier_contribution = filter_pool_sum_up_stake_amnt("TEN_THOUSAND")
        # get the percent at which 10,000 band contributed to the total revenue
        last_tier_percentage_contrib = calc_percent(sum_last_tier_contribution)

        sum_lower_tier_contribution = filter_pool_sum_up_stake_amnt("FIFTY_THOUSAND")
        # get the percent at which 50,000 band contributed to the total revenue
        lower_tier_percentage_contrib = calc_percent(sum_lower_tier_contribution)

        sum_middle_tier_contribution = filter_pool_sum_up_stake_amnt("ONE_HUNDRED_THOUSAND")
        # get the percent at which 100,000 band contributed to the total revenue
        middle_tier_percentage_contrib = calc_percent(sum_middle_tier_contribution)

        sum_upper_tier_contribution = filter_pool_sum_up_stake_amnt("TWO_HUNDRED_THOUSAND")
        upper_tier_percentage_contrib = calc_percent(sum_upper_tier_contribution)

        #

        ten_thousand_amnt = calc_percent_of_amount(last_tier_percentage_contrib, rtp)  # Amount
        ten_thousand_jackpot_amount = calc_percent_of_amount(self._LAST_TIER_JACKPOT_PERCENT, ten_thousand_amnt)  # Jackpot
        last_tier_jackpot_and_amount_diff = round((ten_thousand_amnt - ten_thousand_jackpot_amount), 2)  # amount - jackpot

        fifty_thousand_amnt = calc_percent_of_amount(lower_tier_percentage_contrib, rtp)  # Amount
        fifty_thousand_jackpot_amount = calc_percent_of_amount(self._LOWER_TIER_JACKPOT_PERCENT, fifty_thousand_amnt)  # Jackpot
        lower_tier_jackpot_and_amount_diff = round((fifty_thousand_amnt - fifty_thousand_jackpot_amount), 2)  # amount - jackpot

        hundred_thousand_amnt = calc_percent_of_amount(middle_tier_percentage_contrib, rtp)  # Amount
        hundred_thousand_jackpot_amount = calc_percent_of_amount(self._MIDDLE_TIER_JACKPOT_PERCENT, hundred_thousand_amnt)  # Jackpot
        middle_tier_jackpot_and_amount_diff = round((hundred_thousand_amnt - hundred_thousand_jackpot_amount), 2)  # amount - jackpot

        two_hundred_thousand_amnt = calc_percent_of_amount(upper_tier_percentage_contrib, rtp)  # Amount
        two_hundred_thousand_jackpot_amount = calc_percent_of_amount(self._UPPER_TIER_JACKPOT_PERCENT, two_hundred_thousand_amnt)  # Jackpot
        upper_tier_jackpot_and_amount_diff = round((two_hundred_thousand_amnt - two_hundred_thousand_jackpot_amount), 2)  # amount - jackpot

        #
        upper_tier = round((upper_tier_jackpot_and_amount_diff / self.REWARDS[200]), 2)
        disbursement_amount = math.floor(upper_tier) * self.REWARDS[200]
        upper_tier_balance = upper_tier_jackpot_and_amount_diff - disbursement_amount

        middle_tier = round(
            (middle_tier_jackpot_and_amount_diff + upper_tier_balance) / self.REWARDS[100],
            2,
        )
        disbursement_amount = math.floor(middle_tier) * self.REWARDS[100]
        middle_tier_balance = (middle_tier_jackpot_and_amount_diff + upper_tier_balance) - disbursement_amount

        lower_tier = round(
            (lower_tier_jackpot_and_amount_diff + middle_tier_balance) / self.REWARDS[50],
            2,
        )
        disbursement_amount = math.floor(lower_tier) * self.REWARDS[50]
        lower_tier_balance = (lower_tier_jackpot_and_amount_diff + middle_tier_balance) - disbursement_amount

        last_tier = round(
            (last_tier_jackpot_and_amount_diff + lower_tier_balance) / self.REWARDS[10],
            2,
        )
        disbursement_amount = math.floor(last_tier) * self.REWARDS[10]
        (last_tier_jackpot_and_amount_diff + lower_tier_balance) - disbursement_amount

        #

        upper_tier_jackpot = round((two_hundred_thousand_jackpot_amount / self._ORIGINAL_REWARDS[200]), 2)
        disbursement_amount = math.floor(upper_tier_jackpot) * self._ORIGINAL_REWARDS[200]
        upper_tier_jackpot_balance = two_hundred_thousand_jackpot_amount - disbursement_amount
        # print("UPPER TIER BAL", "__________________", upper_tier_jackpot_balance)

        middle_tier_jackpot = round(
            (hundred_thousand_jackpot_amount + upper_tier_jackpot_balance) / self._ORIGINAL_REWARDS[100],
            2,
        )
        disbursement_amount = math.floor(middle_tier_jackpot) * self._ORIGINAL_REWARDS[100]
        middle_tier_jackpot_balance = (hundred_thousand_jackpot_amount + upper_tier_jackpot_balance) - disbursement_amount

        # print("MIDDLE TIER BAL", "__________________", middle_tier_jackpot_balance)

        lower_tier_jackpot = round(
            (fifty_thousand_jackpot_amount + middle_tier_jackpot_balance) / self._ORIGINAL_REWARDS[50],
            2,
        )
        disbursement_amount = math.floor(lower_tier_jackpot) * self._ORIGINAL_REWARDS[50]
        lower_tier_jackpot_balance = (fifty_thousand_jackpot_amount + middle_tier_jackpot_balance) - disbursement_amount
        # print("DISB : :", disbursement_amount, "WINNERS : :", math.floor(lower_tier_jackpot), "VALUE : :", self._ORIGINAL_REWARDS[50])

        # print("LOWER TIER BAL", "__________________", lower_tier_jackpot_balance)

        last_tier_jackpot = round(
            (ten_thousand_jackpot_amount + lower_tier_jackpot_balance) / self._ORIGINAL_REWARDS[10],
            2,
        )
        disbursement_amount = math.floor(last_tier_jackpot) * self._ORIGINAL_REWARDS[10]
        (ten_thousand_jackpot_amount + lower_tier_jackpot_balance) - disbursement_amount
        # print("LAST TIER BAL", "__________________", last_tier_jackpot_balance)

        #

        # print(
        #     f"""
        #         =================================================================
        #         Target Unique Players           | {total_player}
        #         -----------------------------------------------------------------
        #         Estimated Total Revenue         | {total_revenue}
        #         -----------------------------------------------------------------
        #         Return to Owner                 | {rto}
        #         -----------------------------------------------------------------
        #         Return to Player                | {rtp}
        #         -----------------------------------------------------------------
        #         Global Jackpot                  | {GLOBAL_JACKPOT}
        #         ========================================================================================================
        #         ___________________________________SOCIAL PROOF_____AMOUNT______JACKPOT_______BALANCE___________
        #         10,000 Band player count        | {last_tier_percentage_contrib}%  |      {ten_thousand_amnt} |  {ten_thousand_jackpot_amount}    |  {last_tier_jackpot_and_amount_diff}
        #         --------------------------------------------------------------------------------------------------------
        #         50,000 Band player count        | {lower_tier_percentage_contrib}%  |      {fifty_thousand_amnt} |  {fifty_thousand_jackpot_amount}     |  {lower_tier_jackpot_and_amount_diff}
        #         --------------------------------------------------------------------------------------------------------
        #         100,000 Band player count       | {middle_tier_percentage_contrib}%   |      {hundred_thousand_amnt}  |  {hundred_thousand_jackpot_amount}   |  {middle_tier_jackpot_and_amount_diff}
        #         --------------------------------------------------------------------------------------------------------
        #         200,000 Band player count       | {upper_tier_percentage_contrib}%   |      {two_hundred_thousand_amnt}  |  {two_hundred_thousand_jackpot_amount}  |  {upper_tier_jackpot_and_amount_diff}
        #         --------------------------------------------------------------------------------------------------------
        #         ========================================================================================================
        #         Upper Tier                      | {upper_tier} | REWARDS {self.REWARDS[200]} {self._REWARDS_PERCENT[200]}
        #         --------------------------------------------------------------------------------------------------------
        #         Middle Tier                     | {middle_tier} | REWARDS {self.REWARDS[100]}  {self._REWARDS_PERCENT[100]}
        #         --------------------------------------------------------------------------------------------------------
        #         Lower Tier                      | {lower_tier} | REWARDS {self.REWARDS[50]}  {self._REWARDS_PERCENT[50]}
        #         --------------------------------------------------------------------------------------------------------
        #         Last Tier                       | {last_tier} | REWARDS {self.REWARDS[10]}  {self._REWARDS_PERCENT[10]}
        #         --------------------------------------------------------------------------------------------------------
        #         =======================================================================================================
        #         Upper Tier jackpot                     | {upper_tier_jackpot}
        #         --------------------------------------------------------------------------------------------------------
        #         Middle Tier jackpot                   | {middle_tier_jackpot}
        #         --------------------------------------------------------------------------------------------------------
        #         Lower Tier jackpot                      | {lower_tier_jackpot}
        #         --------------------------------------------------------------------------------------------------------
        #         Last Tier jackpot                      | {last_tier_jackpot}
        #         --------------------------------------------------------------------------------------------------------
        #         """
        # )

        result = {
            "upper_tier": {
                "count": upper_tier,
                "reward": self.REWARDS[200],
                "pool": "TWO_HUNDRED_THOUSAND",
                "jackpot": two_hundred_thousand_jackpot_amount,
                "jkpt_count": upper_tier_jackpot,
            },
            "middle_tier": {
                "count": middle_tier,
                "reward": self.REWARDS[100],
                "pool": "ONE_HUNDRED_THOUSAND",
                "jackpot": hundred_thousand_jackpot_amount,
                "jkpt_count": middle_tier_jackpot,
            },
            "lower_tier": {
                "count": lower_tier,
                "reward": self.REWARDS[50],
                "pool": "FIFTY_THOUSAND",
                "jackpot": fifty_thousand_jackpot_amount,
                "jkpt_count": lower_tier_jackpot,
            },
            "last_tier": {
                "count": last_tier,
                "reward": self.REWARDS[10],
                "pool": "TEN_THOUSAND",
                "jackpot": ten_thousand_jackpot_amount,
                "jkpt_count": last_tier_jackpot,
            },
        }

        return result

    def draw_winners(self):
        decision_dict = self.run_decisioning()

        for key, value in decision_dict.items():
            # print(key, value)

            tier_players = self.query_set.filter(pool=value["pool"]).values_list("game_play_id", flat=True)
            winners = random.sample(list(tier_players), math.floor(value["count"]))

            decision_dict[key]["winners"] = winners

        # pprint(decision_dict)

        for key, value in decision_dict.items():
            # print(key, value)

            tier_players = self.query_set.filter(pool=value["pool"]).values_list("game_play_id", flat=True)
            winners = random.sample(list(tier_players), math.floor(value["jkpt_count"]))

            decision_dict[key]["jkpt_winners"] = winners

        # pprint(decision_dict)
        return decision_dict
