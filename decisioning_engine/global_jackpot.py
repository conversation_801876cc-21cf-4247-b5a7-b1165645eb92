import datetime

from django.db.models import Q

from main.helpers.redis_storage import RedisStore
from main.models import (
    ConstantVariable,
    Jackpot,
    JackpotConstantVariable,
    LotteryModel,
    LottoTicket,
)
from overide_print import print

round_to_nearest_thousand = lambda num: round(num / 1000) * 1000  # noqa


class JackpotWinning:
    """
    This code defines the class JackpotWinning which determines
    if a jackpot prize can be won based on the lottery game type, the number of tickets purchased,
    and if the tickets have been paid for. The class has three methods: instant_cash_out, use_count, and draw.
    The instant_cash_out method checks if the conditions for winning a jackpot prize for the "INSTANT_CASHOUT"
    game type are met and returns a dictionary indicating if the jackpot was won, the amount won, and the band (amount category) won.
    The use_count method checks if the conditions for winning a jackpot prize for the "WYSE_CASH" and
    "SALARY_FOR_LIFE" game types are met and returns a dictionary indicating if the jackpot was won, the amount won, and the band won.
    The draw method determines which of the above methods to call based on the game type set in the ConstantVariable class and returns the result of that method.
    """

    def __init__(self, game_instance, lotto_winner_ins):
        self.game_instance = game_instance
        self.lotto_winner_ins = lotto_winner_ins
        self.percent = JackpotConstantVariable.get_icash_jackpot_sharing_perc()

    @staticmethod
    def percentage_amount(amount: int, percent: float):
        return amount * percent  # 0.37 simply means thirty percent of the amount

    def instant_cash_out(self, jackpot, channel):
        band = self.game_instance.number_of_ticket
        print(self.game_instance.channel, channel, band)

        if (
            jackpot.drawn_200_band is False
            and band == 1
            and self.game_instance.lottery_type == "INSTANT_CASHOUT"
            and self.game_instance.channel == channel
        ):
            amount = jackpot.contributed_amount
            print("AMOUNT ==", amount)

            if amount > 0:
                # get ticket count == 5 before winning jackpot
                # get what count the ticket is being played on
                redis_object = RedisStore()
                datetime_str = redis_object.get_data(key="i_cash__filter_date_string")
                print(
                    "jackpot id :",
                    jackpot.id,
                )
                print("Redis datetime str ----->", datetime_str)

                if datetime_str:
                    i_cash_started_count_time_string = datetime_str.decode()

                    datetime_object = datetime.datetime.fromisoformat(i_cash_started_count_time_string)

                    lotto_qs_count = LottoTicket.objects.filter(
                        lottery_type=self.game_instance.lottery_type, updated_at__gte=datetime_object, channel=channel, paid=True
                    ).count()
                    print("winning count **********", lotto_qs_count)
                    if lotto_qs_count >= 10:
                        # give winning and reset time
                        const_ins = JackpotConstantVariable.objects.last()
                        const_ins.count_icash_jkpt = True
                        const_ins.save()

                        percentage = self.percent[0]
                        win_amount = (percentage / 100) * amount
                        # win_amount = self.percentage_amount(amount, 0.25)
                        print("amount ==", win_amount)
                        return {"jackpot": True, "amount": round_to_nearest_thousand(win_amount), "band": 200}

                    else:
                        return {"jackpot": False, "amount": 0.0, "band": None}

                else:
                    return {"jackpot": False, "amount": 0.0, "band": None}

            else:
                return {"jackpot": False, "amount": 0.0, "band": None}

        if (
            jackpot.drawn_400_band is False
            and band == 2
            and self.game_instance.lottery_type == "INSTANT_CASHOUT"
            and self.game_instance.channel == channel
        ):
            amount = jackpot.contributed_amount

            if amount > 0:
                percentage = self.percent[1]
                win_amount = (percentage / 100) * amount
                return {"jackpot": True, "amount": round_to_nearest_thousand(win_amount), "band": 400}

            else:
                return {"jackpot": False, "amount": 0.0, "band": None}

        if (
            jackpot.drawn_800_band is False
            and band == 4
            and self.game_instance.lottery_type == "INSTANT_CASHOUT"
            and self.game_instance.channel == channel
        ):
            amount = jackpot.contributed_amount

            if amount > 0:
                percentage = self.percent[2]
                win_amount = (percentage / 100) * amount
                # win_amount = self.percentage_amount(amount, 0.25)
                return {"jackpot": True, "amount": round_to_nearest_thousand(win_amount), "band": 800}

            else:
                return {"jackpot": False, "amount": 0.0, "band": None}

        if (
            jackpot.drawn_1400_band is False
            and band == 7
            and self.game_instance.lottery_type == "INSTANT_CASHOUT"
            and self.game_instance.channel == channel
        ):
            amount = jackpot.contributed_amount

            if amount > 0:
                percentage = self.percent[3]
                win_amount = (percentage / 100) * amount
                # win_amount = self.percentage_amount(amount, 0.25)
                return {"jackpot": True, "amount": round_to_nearest_thousand(win_amount), "band": 1400}

            else:
                return {"jackpot": False, "amount": 0.0, "band": None}

        else:
            return {"jackpot": False, "amount": 0.0, "band": None}

    @staticmethod
    def use_count(jackpot, channel, paid_ticket_count):
        # date = jackpot.updated_at.date()
        # WYSE CASH
        """total count of paid lottery ticket with unique game play id"""

        wyse_cash = (
            LotteryModel.objects.filter(
                lottery_type="WYSE_CASH",
                channel=channel,
                # paid_date__date=date,
                paid=True,
            )
            .values_list("game_play_id", flat=True)
            .distinct()
            .count()
        )

        # SALARY FOR LIFE, INSTANT CASHOUT FAST FAST
        """total count of paid lotto ticket with unique game play id"""
        salary_for_life_and_instant_cash_out = (
            LottoTicket.objects.filter(
                Q(lottery_type="SALARY_FOR_LIFE") | Q(lottery_type="INSTANT_CASHOUT"),
                channel=channel,
                # updated_at__date=date,
                paid=True,
            )
            .values_list("game_play_id", flat=True)
            .distinct()
            .count()
        )

        total_game_count = wyse_cash + salary_for_life_and_instant_cash_out

        print("Total game count: ", total_game_count)
        print("wyse cash game count: ", wyse_cash)
        print(
            "salary for life and instant cash out game count: ",
            salary_for_life_and_instant_cash_out,
        )
        print("Expected to meet game count : ", paid_ticket_count)

        if total_game_count == paid_ticket_count:
            result = {
                "jackpot": True,
                "amount": jackpot.contributed_amount,
                "band": None,
            }

        else:
            result = {"jackpot": False, "amount": 0.0, "band": None}

        return result

    @staticmethod
    def toggle_drawn_status(run_jackpot_decisioning, jackpot_instance, amount_won):
        if run_jackpot_decisioning["band"] == 200 and jackpot_instance.drawn_200_band is False:
            jackpot_instance.drawn_200_band = True
            jackpot_instance.total_amount_given_out += amount_won
            jackpot_instance.save()

        elif run_jackpot_decisioning["band"] == 400 and jackpot_instance.drawn_400_band is False:
            jackpot_instance.drawn_400_band = True
            jackpot_instance.total_amount_given_out += amount_won
            jackpot_instance.save()

        elif run_jackpot_decisioning["band"] == 800 and jackpot_instance.drawn_800_band is False:
            jackpot_instance.drawn_800_band = True
            jackpot_instance.total_amount_given_out += amount_won
            jackpot_instance.save()

        elif run_jackpot_decisioning["band"] == 1400 and jackpot_instance.drawn_1400_band is False:
            jackpot_instance.drawn_1400_band = True
            jackpot_instance.total_amount_given_out += amount_won
            jackpot_instance.save()

        elif run_jackpot_decisioning["jackpot"] is True and run_jackpot_decisioning["band"] is None:
            jackpot_instance.is_drawn = True
            jackpot_instance.save()

        if (
            jackpot_instance.drawn_800_band is True
            and jackpot_instance.drawn_200_band is True
            and jackpot_instance.drawn_400_band is True
            and jackpot_instance.drawn_1400_band is True
        ):
            jackpot_instance.is_drawn = True
            jackpot_instance.save()

    def format_band(self):
        if self.game_instance.number_of_ticket == 1:
            return "200"
        elif self.game_instance.number_of_ticket == 2:
            return "400"
        elif self.game_instance.number_of_ticket == 4:
            return "800"
        elif self.game_instance.number_of_ticket == 7:
            return "1400"

    def draw(self):
        print("# DRAW #")
        const = ConstantVariable.get_jackpot_constants()
        print(const)
        jackpot_game_choice = const.get("lotto_type")

        jackpot = Jackpot.is_available()
        print(jackpot)

        if jackpot is None:
            return {"jackpot": False, "amount": 0.0, "band": None}

        if jackpot_game_choice == "ALL":
            print("ALL")
            return self.use_count(jackpot=jackpot, channel=const["channel"], paid_ticket_count=const["paid_ticket_count"])

        elif jackpot_game_choice == "INSTANT_CASHOUT":
            # check if winning is a 200 band player can move to the next available jackpot game

            if self.lotto_winner_ins.count() > 0 and self.lotto_winner_ins.last().stake_amount == 200:
                # get the next available jackpot game
                next_available_ticket = jackpot.next_available_jackpot()
                if next_available_ticket is not None:
                    return self.instant_cash_out(jackpot=next_available_ticket, channel=const["channel"])
                else:
                    return {"jackpot": False, "amount": 0.0, "band": None}

            if self.lotto_winner_ins.count() > 0 and self.lotto_winner_ins.last().stake_amount != 200:
                print("\n\n agent has an existing winning on jackpot draw \n\n")
                return {"jackpot": False, "amount": 0.0, "band": None}

            else:
                print("INSTANT_CASHOUT")
            return self.instant_cash_out(jackpot=jackpot, channel=const["channel"])
