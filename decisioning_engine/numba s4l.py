import random
import time

# get the start time
st = time.time()


def monte_carlo_pi(nsamples):
    acc = 0
    for i in range(nsamples):
        x = random.random()
        y = random.random()
        if (x**2 + y**2) < 1.0:
            acc += 1
    return 4.0 * acc / nsamples


print(monte_carlo_pi(100000000))

# get the end time
et = time.time()

# get the execution time
elapsed_time = et - st
print("Execution time:", elapsed_time / 60, "Mins")
