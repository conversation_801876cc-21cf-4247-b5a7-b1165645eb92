# from wyse_ussd.models import SoccerPrediction
# from django.db.models import Q
#
# # update prediction object
#
# game_object = ""
# correct_prediction_fields = {
#     "football_table": game_object,
#     "paid": True,
#     "freemium": False,
#     "is_drawn": False,
#     "score_checked": False,
#     "active": True,
#     "away_choice": "",
#     "home_choice": ""
#
# }
#
# correct_score_conditions = Q(**correct_prediction_fields)
# predictions = SoccerPrediction.objects.filter(correct_score_conditions).exclude("SHARED")
#
