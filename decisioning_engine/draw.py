import datetime
import itertools
import math
import random
import time
from dataclasses import dataclass
from functools import reduce

import pandas as pd

from main.models import ConstantVariable
from overide_print import print
from collections import Counter
import random
from .utils import (
    get_banker_potential_winning,
    get_potential_winning,
    search_number_occurences,
)


class SalaryForLifeDraw:
    @staticmethod
    def draw(plays, rtp, line_prices, jackpot_amount, disburse_jackpot=False) -> dict:
        random_combo = list(itertools.combinations(range(1, 50), 5))
        random.shuffle(random_combo)
        const_obj = ConstantVariable.objects.all().last()
        best_match = 0
        best_winners = 0
        best_match_combo = []

        datetime.datetime.now()

        restrict_or_not = const_obj.restrict_s4l_to_lower_wins
        max_win_lim_ratio = const_obj.max_s4l_single_win_to_rtp

        best_match_with_jkpt = 0
        best_match_with_jkpt_combo = []

        best_match_witho_jkpt = 0
        best_match_witho_jkpt_combo = []

        # print(line_prices)

        # for combo in plays:
        #     print(combo)
        # for index, combo in enumerate(random_combo):

        #     occurences = map(
        #         lambda user_play: search_number_occurences(combo, user_play), plays
        #     )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

        #     play_occurences = zip(
        #         occurences, plays
        #     )  # Match number of occurences to number of matches found in selected combination
        #     over3ocurrences = list(
        #         filter(lambda x: x[0], play_occurences)
        #     )  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

        #     play_occurences_with_amount = map(
        #         lambda played: get_potential_winning(
        #             played, line_prices, jackpot_amount
        #         ),
        #         over3ocurrences,
        #     )

        #     total_sum = 0
        #     play_occurences_with_amount = list(play_occurences_with_amount)
        #     # CALCULATE THE TOTAL WINNING AMOUNT
        #     for index, ocurrence in enumerate(play_occurences_with_amount):
        #         total_sum += ocurrence[-1]

        #     has_jkpt = bool(
        #         list(filter(lambda x: x[0] == 5, play_occurences_with_amount))
        #     )
        #     match = total_sum / rtp * 100

        #     # print("TOTAL SUM ::", total_sum, "RTP ::", rtp)

        #     # if match > 95 and match < 105:
        #     #     input()

        #     # if match > best_match and match < 100:

        #     if match > best_match and match < 100:
        #         best_match = match
        #         best_match_combo = combo
        #         best_total_sum = total_sum
        #     if match > best_match_with_jkpt and match < 100 and has_jkpt:
        #         best_match_with_jkpt = match
        #         best_match_with_jkpt_combo = combo
        #     if match > best_match_witho_jkpt and match < 100 and (not has_jkpt):
        #         best_match_witho_jkpt = match
        #         best_match_witho_jkpt_combo = combo

        for index, combo in enumerate(random_combo):
            occurences = map(
                lambda user_play: search_number_occurences(combo, user_play, const_obj.s4l_number_of_match_limit), plays
            )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

            play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
            over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

            # NOW FILTER HAS BEEN ALTERED TO ALLOW 2 AND ABOVE WIN COMBOs

            play_occurences_with_amount = map(
                lambda played: get_potential_winning(played, line_prices, jackpot_amount),
                over3ocurrences,
            )

            total_sum = 0
            play_occurences_with_amount = list(play_occurences_with_amount)
            # CALCULATE THE TOTAL WINNING AMOUNT
            for index, ocurrence in enumerate(play_occurences_with_amount):
                total_sum += ocurrence[-1]

                if rtp / ocurrence[-1] < max_win_lim_ratio:
                    total_sum = ***********
                    break

            if total_sum >= ***********:
                continue

            has_jkpt = bool(list(filter(lambda x: x[0] == 5, play_occurences_with_amount)))
            match = total_sum / rtp * 100
            winners = len(over3ocurrences)

            # if match > 10 and match < 100 and len(over3ocurrences) >= 3: print(match, "-->", winners, "-->", int(match*winners))

            # print("TOTAL SUM ::", total_sum, "RTP ::", rtp)

            # if match > 95 and match < 105:
            #     input()

            # if match > best_match and match < 100:

            if (winners >= best_winners or match > best_match) and match < 100:
                if winners == best_winners and match < best_match and restrict_or_not:
                    # print("SWITCHING MATCH :::", best_match, "-->", match)
                    # print("SWITCHING WINN :::", best_winners, "-->", winners)
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo

                elif winners > best_winners:
                    # print("SWITCHING MATCH :::", best_match, "-->", match)
                    # print("SWITCHING WINN :::", best_winners, "-->", winners)
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo

                elif best_winners == 0:
                    # print("SWITCHING MATCH :::", best_match, "-->", match)
                    # print("SWITCHING WINN :::", best_winners, "-->", winners)
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo

                if match > best_match and winners >= best_winners:
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo

            # if match < 20:win_n_matches.append((int(best_match), best_winners))

            if match > best_match_with_jkpt and match < 20 and has_jkpt:
                best_match_with_jkpt = match
                best_match_with_jkpt_combo = combo
            if match > best_match_witho_jkpt and match < 20 and (not has_jkpt):
                best_match_witho_jkpt = match
                best_match_witho_jkpt_combo = combo

        #     with open("s4ldraw.txt", "a") as file:
        #         file.write(f"TIME :: {now.strftime('%d-%m %H:%M')}, BEST MATCH ::{best_match}, MATCH ::{match}, COMBO ::{best_match_combo} , TOTAL SUM ::{best_total_sum}\n")

        # print("BEST MATCH ::", best_match, "MATCH ::", match, "COMBO ::", best_match_combo , "TOTAL SUM ::", best_total_sum)

        # prices = SalaryForLifeDraw.filter_winnings(best_match_combo, plays, line_prices, jackpot_amount)
        # from pprint import pprint

        # pprint(prices)

        return dict(
            best_match=best_match,
            best_match_combo=best_match_combo,
            best_match_with_jkpt=best_match_with_jkpt,
            best_match_with_jkpt_combo=best_match_with_jkpt_combo,
            best_match_witho_jkpt=best_match_witho_jkpt,
            best_match_witho_jkpt_combo=best_match_witho_jkpt_combo,
        )

    @staticmethod
    def banker_decisioning(plays, rtp, line_prices, jackpot_amount, disburse_jackpot=False) -> dict:
        # print(":::::::::::NEW-TP:::", rtp)
        print(plays)
        random_combo = list(itertools.combinations(range(1, 49), 5))
        random.shuffle(random_combo)
        const_obj = ConstantVariable.objects.all().last()
        best_match = 0
        best_winners = 0
        best_match_combo = []

        LIMIT_WINING_AMOUNT = const_obj.limit_wining_amount
        const_obj.limit_wining_amount = not const_obj.limit_wining_amount
        const_obj.save()
        # print(LIMIT_WINING_AMOUNT , "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n")
        restrict_or_not = const_obj.restrict_s4l_to_lower_wins
        max_win_lim_ratio = const_obj.max_s4l_single_win_to_rtp

        best_match_with_jkpt = 0
        best_match_with_jkpt_combo = []

        best_match_witho_jkpt = 0
        best_match_witho_jkpt_combo = []

        best_total_sum = 0
        for combo in plays:
            print(combo)

        for index, combo in enumerate(random_combo):
            occurences = map(
                lambda user_play: search_number_occurences(combo, user_play, const_obj.s4l_number_of_match_limit), plays
            )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

            play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
            over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

            # NOW FILTER HAS BEEN ALTERED TO ALLOW 2 AND ABOVE WIN COMBOs

            play_occurences_with_amount = map(
                lambda played: get_banker_potential_winning(played, line_prices, jackpot_amount, ticket_rtp=played[1][2]),
                over3ocurrences,
            )

            # for ticket in play_occurences_with_amount:
            #     print(ticket)

            total_sum = 0
            play_occurences_with_amount = list(play_occurences_with_amount)
            # CALCULATE THE TOTAL WINNING AMOUNT
            for index, ocurrence in enumerate(play_occurences_with_amount):
                if LIMIT_WINING_AMOUNT:
                    break

                total_sum += ocurrence[-1]

                if rtp / ocurrence[-1] < max_win_lim_ratio:
                    total_sum = ***********
                    break

            if total_sum >= ***********:
                continue

            has_jkpt = bool(list(filter(lambda x: x[0] == 5, play_occurences_with_amount)))
            match = total_sum / rtp * 100
            winners = len(over3ocurrences)

            if (winners >= best_winners or match > best_match) and match < 100:
                # print("MATCH ::#:: ", match)

                if winners == best_winners and match < best_match and restrict_or_not:
                    # print("SWITCHING MATCH :::", best_match, "-->", match)
                    # print("SWITCHING WINN :::", best_winners, "-->", winners)
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo
                    best_total_sum = total_sum

                elif winners > best_winners:
                    # print("SWITCHING MATCH :::", best_match, "-->", match)
                    # print("SWITCHING WINN :::", best_winners, "-->", winners)
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo
                    best_total_sum = total_sum

                elif best_winners == 0:
                    # print("SWITCHING MATCH :::", best_match, "-->", match)
                    # print("SWITCHING WINN :::", best_winners, "-->", winners)
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo
                    best_total_sum = total_sum

                if match > best_match and winners >= best_winners:
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo
                    best_total_sum = total_sum

            if match > best_match_with_jkpt and match < 20 and has_jkpt:
                best_match_with_jkpt = match
                best_match_with_jkpt_combo = combo

            if match > best_match_witho_jkpt and match < 20 and (not has_jkpt):
                best_match_witho_jkpt = match
                best_match_witho_jkpt_combo = combo

            # print("COMBO:::", combo, "INDEX:::", index)

        if best_match == 0:
            best_match_combo = []

        print("BEST MATCH ::", best_match, "MATCH ::", match, "COMBO ::", best_match_combo, "TOTAL SUM ::", best_total_sum)

        prices = SalaryForLifeDraw.filter_banker_winnings(best_match_combo, plays, line_prices, jackpot_amount)

        filterd_winners = SalaryForLifeDraw.filter_banker_winnings(best_match_combo, plays, line_prices, jackpot_amount)
        total_winning = SalaryForLifeDraw.deep_sum(filterd_winners)

        if total_winning > rtp:
            best_match_combo = SalaryForLifeDraw.least_occurring_numbers(plays)
            print("BEST MATCH COMBO ::", best_match_combo)
            return


        from pprint import pprint

        pprint(prices)
        # raise SyntaxError
        return dict(
            best_match=best_match,
            best_match_combo=best_match_combo,
            best_match_with_jkpt=best_match_with_jkpt,
            best_match_with_jkpt_combo=best_match_with_jkpt_combo,
            best_match_witho_jkpt=best_match_witho_jkpt,
            best_match_witho_jkpt_combo=best_match_witho_jkpt_combo,
            LIMIT_WINING_AMOUNT=LIMIT_WINING_AMOUNT,
        )

    @staticmethod
    def least_occurring_numbers(data):
        all_numbers = list(range(1,51))

        for item in data:
            if isinstance(item, tuple) and len(item) == 3:
                all_numbers.extend(item[1])

        # Count occurrences of each number
        number_counts = Counter(all_numbers)

        # Return all numbers with their occurrences, sorted by frequency
        numbers, _ = zip(*sorted(number_counts.items(), key=lambda x: x[1]))
        combo = numbers[:10]
        selection = random.sample(combo, k=5)

        return selection

    @staticmethod
    def deep_sum(data):
        total_winning_amount = 0

        for item in data:
            if isinstance(item, list) and len(item) == 3:
                total_winning_amount += item[2]  # Sum the winning_amount

        return total_winning_amount

    @staticmethod
    def filter_winnings(combo, plays, prices, jackpot_amount):
        const_obj = ConstantVariable.objects.all().last()
        occurences = map(
            lambda user_play: search_number_occurences(combo, user_play, const_obj.s4l_number_of_match_limit), plays
        )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

        play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
        over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

        play_occurences_with_amount = map(
            lambda played: get_potential_winning(played, prices, jackpot_amount),
            over3ocurrences,
        )
        data = list(play_occurences_with_amount)

        return data

    @staticmethod
    def filter_banker_winnings(combo, plays, prices, jackpot_amount):
        const_obj = ConstantVariable.objects.all().last()
        occurences = map(
            lambda user_play: search_number_occurences(combo, user_play, const_obj.s4l_number_of_match_limit), plays
        )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

        play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
        over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

        play_occurences_with_amount = map(
            lambda played: get_banker_potential_winning(played, prices, jackpot_amount),
            over3ocurrences,
        )
        data = list(play_occurences_with_amount)

        return data


@dataclass
class InstantCashoutDraw:
    TOTAL_CONTRIBUTION = 0
    RUNNING_BALANCE = 0
    BATCH_CONTRIBUTION = 0

    SHARE_RATIO = 0.4, 0.35, 0.25

    PLAYS = ***********
    BASE_N = 40
    N = 40
    BASE_PPS = 0  # BASE PLAYS PER SECOND
    MOST_RECENT_ELAPSED_TIME = 0
    BATCH = 0
    SURGE = False

    PLAYS_LIST = {}

    sub_batch_time_elaspsed = 0

    WIN_PROGRESSION_FACTOR = 0.833  # moving factor for winning from 3/4 -> 4/4 match

    play_times = []

    WINNINGS = {
        150: {"min_win": 4500, "mid_win": 5400, "max_win": 11250},
        300: {"min_win": 7500, "mid_win": 9000, "max_win": 13500},
        450: {"min_win": 9000, "mid_win": 10800, "max_win": 15750},
        600: {"min_win": 10800, "mid_win": 13000, "max_win": 18000},
        750: {"min_win": 12000, "mid_win": 14000, "max_win": 18750},
        900: {"min_win": 13500, "mid_win": 16000, "max_win": 22500},
        1000: {"min_win": 15000, "mid_win": 18000, "max_win": 25000},
    }

    for band in WINNINGS.keys():
        band_winning_values = list(WINNINGS[band].values())

        WINNINGS[band]["total_possible_for_3"] = sum(band_winning_values)
        WINNINGS[band]["total_possible_for_2"] = band_winning_values[0] + band_winning_values[1]

    AVAILABLE_BANDS = list(WINNINGS.keys())

    seed = [1, 1, 1, 3, 5, 6, 10]
    random.shuffle(seed)
    skew = [[band] * seed_val for seed_val, band in zip(seed, AVAILABLE_BANDS)]
    skew_list = reduce(lambda l1, l2: l1 + l2, skew)

    PAYOUTS = dict(WINNINGS.items())

    def sharemonies(self, bands, share_values) -> dict:
        print("....SHARING MONIES ....")

        bands_n_shares = zip(bands, share_values)
        winnin_tiers = []
        print(bands, share_values)
        BALANCE = 0

        for band, share in bands_n_shares:
            print("SHARE-BALANCE ::", share, "-", BALANCE)
            share = share + BALANCE
            payout_to_band_tiers = self.WINNINGS[band]

            print(f"{band} PAYOUT :::", payout_to_band_tiers)

            if share > payout_to_band_tiers["total_possible_for_3"]:
                BALANCE = share - (payout_to_band_tiers["min_win"] + payout_to_band_tiers["mid_win"] + payout_to_band_tiers["max_win"])

                winnin_tiers.append(("min_win", band, payout_to_band_tiers["min_win"]))
                winnin_tiers.append(("mid_win", band, payout_to_band_tiers["mid_win"]))
                winnin_tiers.append(("max_win", band, payout_to_band_tiers["max_win"]))

            elif share > payout_to_band_tiers["total_possible_for_2"]:
                BALANCE = share - (payout_to_band_tiers["min_win"] + payout_to_band_tiers["mid_win"])
                winnin_tiers.append(("min_win", band, payout_to_band_tiers["min_win"]))
                winnin_tiers.append(("mid_win", band, payout_to_band_tiers["mid_win"]))

            elif share > payout_to_band_tiers["min_win"]:
                BALANCE = share - (payout_to_band_tiers["min_win"])
                winnin_tiers.append(("min_win", band, payout_to_band_tiers["min_win"]))

            else:
                print("BALANCE ::", BALANCE)
                return dict(requesting_for_2=True, winners=winnin_tiers, balance=BALANCE)

        print("BALANCE ::", BALANCE)

        return dict(requesting_for_2=False, winners=winnin_tiers, balance=BALANCE)

    def draw(self):
        for i in range(self.PLAYS):
            self.play_times.append(datetime.datetime.now())

            time_delay = open("variables.txt", "r").read() or 1
            time.sleep(float(time_delay))

            picks = random.sample(range(1, 41), 4)

            play = random.choice(self.skew_list)
            self.TOTAL_CONTRIBUTION += play
            self.BATCH_CONTRIBUTION += play

            band_play = self.PLAYS_LIST.get(play, 0)

            if band_play:
                self.PLAYS_LIST[play] += 1
            else:
                self.PLAYS_LIST[play] = 1

            print(
                f"#{self.BATCH}",
                play,
                picks,
                "---->",
                self.TOTAL_CONTRIBUTION,
                self.BATCH_CONTRIBUTION,
            )
            self.BATCH += 1

            if self.BATCH in list(range(0, int(self.N) + 1, 5)) and self.MOST_RECENT_ELAPSED_TIME != 0:
                play_times_recent_N_section = self.play_times[int(-self.BATCH) :]
                sub_batch_time_elaspsed: datetime.timedelta = play_times_recent_N_section[-1] - play_times_recent_N_section[0]

                if sub_batch_time_elaspsed.total_seconds() > self.MOST_RECENT_ELAPSED_TIME * 1.1:
                    print(
                        "BATCH TIMES: ",
                        sub_batch_time_elaspsed.total_seconds(),
                        self.MOST_RECENT_ELAPSED_TIME * 1.5,
                    )
                    print("OOUPS SURGE..!!!", "BATCH : ", -self.BATCH)

                    if self.BATCH >= self.BASE_N:
                        pass

                else:
                    pass

            ":::::::::::::"
            if self.BATCH >= self.N or self.SURGE:
                print("BREAKING FROM BATCH")
                print("N=", self.N, "BATCH=", self.BATCH)
                # batch_play_times_recent_N_section = self.play_times[int(-self.BATCH) :]
                # batch_time_elaspsed: datetime.timedelta = batch_play_times_recent_N_section[-1] - batch_play_times_recent_N_section[0]
                # print(batch_play_times_recent_N_section[0])
                # print(batch_play_times_recent_N_section[-1])

                play_times_recent_N_section = self.play_times[int(-self.BATCH) :]
                time_elaspsed: datetime.timedelta = play_times_recent_N_section[-1] - play_times_recent_N_section[0]
                play_frequency = self.N / time_elaspsed.total_seconds()
                time_elaspsed.total_seconds()

                random.shuffle(self.AVAILABLE_BANDS)
                print("AVAILABLE_BANDS ::", self.AVAILABLE_BANDS)

                [[key] * value for key, value in self.PLAYS_LIST.items()]
                payout_skew_list = reduce(lambda l1, l2: l1 + l2, self.skew)
                print("NORMALIZED_PAY : ", payout_skew_list)

                while True:
                    payout_bands = random.sample(payout_skew_list, 3)
                    if len(set(payout_bands)) == 3:
                        break

                payout_bands = sorted(payout_bands)

                print(payout_bands)
                print("ORIGINAL CONTRIB:::", self.BATCH_CONTRIBUTION)
                print("RUNNING_BALANCE:::", self.RUNNING_BALANCE)
                self.BATCH_CONTRIBUTION += self.RUNNING_BALANCE
                print("UPDATED CONTRIB:::", self.BATCH_CONTRIBUTION)

                SHARE_VALUES = (
                    self.SHARE_RATIO[0] * self.BATCH_CONTRIBUTION,
                    self.SHARE_RATIO[1] * self.BATCH_CONTRIBUTION,
                    self.SHARE_RATIO[2] * self.BATCH_CONTRIBUTION,
                )

                print("SHARE VALUES : ", SHARE_VALUES)
                print("BATCH CONTRIB : ", self.BATCH_CONTRIBUTION)

                print(self.PLAYS_LIST)
                payout_bands = sorted(payout_bands, reverse=True)
                SHARE_VALUES = sorted(SHARE_VALUES, reverse=True)

                share_money_result = self.sharemonies(payout_bands, SHARE_VALUES)

                print("::::SHARE RES::::", share_money_result)
                print(f"""\n\n# {len(share_money_result["winners"])}WINNERS #\n\n""")

                if share_money_result.get("requesting_for_2"):
                    print("\n\n:::::::::::::REQUESTED FOR 2::::::::::::::\n]\n")

                    SHARE_VALUES[1] = SHARE_VALUES[1] + SHARE_VALUES[0] * 0.6
                    SHARE_VALUES[2] = SHARE_VALUES[2] + SHARE_VALUES[0] * 0.4

                    share_money_result = self.sharemonies(
                        [payout_bands[1], payout_bands[2]],
                        [SHARE_VALUES[1], SHARE_VALUES[2]],
                    )
                    print(f"""\n\n# {len(share_money_result["winners"])}WINNERS #\n\n""")

                    print(
                        "SHARE AFTER RESOLVE\n\n",
                        [payout_bands[1], payout_bands[2]],
                        [SHARE_VALUES[1], SHARE_VALUES[2]],
                    )

                RUNNING_BALANCE = share_money_result.get("balance", 0)
                print(
                    "\n\n\n=============================\nSHARES ::\n\n\n",
                    share_money_result,
                    "\n\n\n",
                )
                if share_money_result.get("winners") == []:
                    RUNNING_BALANCE = self.BATCH_CONTRIBUTION

                print("RUNNING_BALANCE::::", RUNNING_BALANCE)

                if self.BASE_PPS == 0:
                    self.BASE_PPS = play_frequency
                else:
                    pps_delta = play_frequency / self.BASE_PPS
                    NEW_N = self.BASE_N * pps_delta
                    N = NEW_N if self.BASE_N < NEW_N else self.BASE_N

                    # if SURGE:
                    #     N = BATCH if BASE_N < BATCH else BASE_N
                    print("DELTA : ", (pps_delta), "%")
                    print("N : ", N, "players")

                print("BATCH REACHED")
                print(play_times_recent_N_section[0])
                print(play_times_recent_N_section[-1])
                print(round(time_elaspsed.total_seconds(), 2), "Secs")

                if self.sub_batch_time_elaspsed:
                    print(round(sub_batch_time_elaspsed.total_seconds(), 2), "Secs @batch")

                print(play_frequency, "ps/sec", end="\n\n")

                if self.SURGE:
                    input("Surge please press enter")

                self.BATCH_CONTRIBUTION = 0
            # if BATCH/N > 0: # IF THE THRESHOLD N VALUE HAS BEEN MET IN TERMS OF NUMBER OF PLAYS

            #     print("TIME TO ATTEMPT PAYOUT")
            # print(play, picks)

        print(self.TOTAL_CONTRIBUTION)
        return self.TOTAL_CONTRIBUTION


class WyseCashDraw:
    from main.models import ConstantVariable

    def __init__(self, query_set) -> None:
        self._ORIGINAL_REWARDS = {10: 10000, 50: 50000, 100: 100000, 200: 200000}
        self._JACKPOT_REWARDS = {10: 50000, 50: 250000, 100: 500000, 200: 1000000}

        self.range_diff = ConstantVariable.wysecash_percentage_range()

        self._rto_rtp_const = ConstantVariable.rto_rtp()
        self._RTO_PERCENT = self._rto_rtp_const["rto"]
        self._RTP_PERCENT = self._rto_rtp_const["rtp"]

        self._REWARDS_PERCENT = {
            10: (random.choice(range(self.range_diff[10]["min"], 110, self.range_diff[10]["step"])) / 100),
            50: (random.choice(list(range(self.range_diff[50]["min"], 110, self.range_diff[50]["step"])) + [100]) / 100),
            100: (random.choice(range(self.range_diff[100]["min"], 110, self.range_diff[100]["step"])) / 100),
            200: (random.choice(range(self.range_diff[200]["min"], 110, self.range_diff[200]["step"])) / 100),
        }

        # reward_factors = {"10": 50, "50": 30, "100": 20, "200": 20}
        self.reward_factors = ConstantVariable.objects.all().last().wyse_cash_win_factor

        self.REWARDS = {
            10: 10000 * self.reward_factors["10"] / 100 * self._REWARDS_PERCENT[10],
            50: 50000 * self.reward_factors["50"] / 100 * self._REWARDS_PERCENT[50],
            100: 100000 * self.reward_factors["100"] / 100 * self._REWARDS_PERCENT[100],
            200: 200000 * self.reward_factors["200"] / 100 * self._REWARDS_PERCENT[200],
        }

        # print(_REWARDS_PERCENT)
        # print(REWARDS)

        # print("::::::::::::::::REWARDS::::::::::::::::", REWARDS)
        self._UPPER_TIER_JACKPOT_PERCENT = 25
        self._MIDDLE_TIER_JACKPOT_PERCENT = 25
        self._LOWER_TIER_JACKPOT_PERCENT = 20
        self._LAST_TIER_JACKPOT_PERCENT = 15

        self.query_set = query_set
        self.decision_dict: dict = {}

    def run_decisioning(self):
        # i'm using pandas' data frame in order to avoid
        # different query thereofore i can use the
        # dataframe created to do heavy manipulation
        # and decisionings with less queries count --
        #                                            |
        #                                            v

        game_df = pd.DataFrame.from_records(self.query_set.values())
        print(game_df)
        # print(game_df)

        total_player = game_df["id"].count()  # total player count
        # total_revenue = game_df["amount_paid"].sum()  # Estimate total amount stake

        # get constant variables
        constant_variable = ConstantVariable.objects.all().last()

        if constant_variable is None:
            constant_variable = ConstantVariable.objects.create(game_threshold=1)

        total_revenue = game_df["rtp"].sum() + constant_variable.wyse_cash_running_balance  # Estimate total amount stake

        rto = game_df["rto"].sum()  # return to owner
        # rtp = game_df["stake_amount"].sum()  # return to player
        rtp = total_revenue
        print(game_df["rtp"].sum())
        GLOBAL_JACKPOT = rtp * 0.05
        print("RTP : ", game_df["rtp"].sum())
        print("Brought Forward : ", constant_variable.wyse_cash_running_balance)
        print("Total RTP : ", rtp)

        # rtp = rtp - GLOBAL_JACKPOT

        def calc_percent(amount):
            return round(100 * amount / float(total_revenue), 2)

        calc_percent_of_amount = lambda amount, number: round(number / 100 * float(amount), 2)  # noqa

        #

        filter_pool_sum_up_stake_amnt = lambda pool: game_df[game_df["pool"] == pool]["rtp"].sum()  # noqa

        sum_last_tier_contribution = filter_pool_sum_up_stake_amnt("TEN_THOUSAND") + (constant_variable.wyse_cash_running_balance / 4)
        # get the percent at which 10,000 band contributed to the total revenue
        last_tier_percentage_contrib = calc_percent(sum_last_tier_contribution)  # noqa

        sum_lower_tier_contribution = filter_pool_sum_up_stake_amnt("FIFTY_THOUSAND") + (constant_variable.wyse_cash_running_balance / 4)
        # get the percent at which 50,000 band contributed to the total revenue
        lower_tier_percentage_contrib = calc_percent(sum_lower_tier_contribution)  # noqa

        sum_middle_tier_contribution = filter_pool_sum_up_stake_amnt("ONE_HUNDRED_THOUSAND") + (constant_variable.wyse_cash_running_balance / 4)
        # get the percent at which 100,000 band contributed to the total revenue
        middle_tier_percentage_contrib = calc_percent(sum_middle_tier_contribution)  # noqa

        sum_upper_tier_contribution = filter_pool_sum_up_stake_amnt("TWO_HUNDRED_THOUSAND") + (constant_variable.wyse_cash_running_balance / 4)
        upper_tier_percentage_contrib = calc_percent(sum_upper_tier_contribution)  # noqa

        print("1.", sum_last_tier_contribution)
        print("2.", sum_lower_tier_contribution)
        print("3.", sum_middle_tier_contribution)
        print("4.", sum_upper_tier_contribution)

        #

        ten_thousand_amnt = calc_percent_of_amount(last_tier_percentage_contrib, rtp)  # Amount
        ten_thousand_jackpot_amount = calc_percent_of_amount(self._LAST_TIER_JACKPOT_PERCENT, ten_thousand_amnt)  # Jackpot
        last_tier_jackpot_and_amount_diff = round((ten_thousand_amnt - ten_thousand_jackpot_amount), 2)  # amount - jackpot

        fifty_thousand_amnt = calc_percent_of_amount(lower_tier_percentage_contrib, rtp)  # Amount
        fifty_thousand_jackpot_amount = calc_percent_of_amount(self._LOWER_TIER_JACKPOT_PERCENT, fifty_thousand_amnt)  # Jackpot
        lower_tier_jackpot_and_amount_diff = round((fifty_thousand_amnt - fifty_thousand_jackpot_amount), 2)  # amount - jackpot

        hundred_thousand_amnt = calc_percent_of_amount(middle_tier_percentage_contrib, rtp)  # Amount
        hundred_thousand_jackpot_amount = calc_percent_of_amount(self._MIDDLE_TIER_JACKPOT_PERCENT, hundred_thousand_amnt)  # Jackpot
        middle_tier_jackpot_and_amount_diff = round((hundred_thousand_amnt - hundred_thousand_jackpot_amount), 2)  # amount - jackpot

        two_hundred_thousand_amnt = calc_percent_of_amount(upper_tier_percentage_contrib, rtp)  # Amount
        two_hundred_thousand_jackpot_amount = calc_percent_of_amount(self._UPPER_TIER_JACKPOT_PERCENT, two_hundred_thousand_amnt)  # Jackpot
        upper_tier_jackpot_and_amount_diff = round((two_hundred_thousand_amnt - two_hundred_thousand_jackpot_amount), 2)  # amount - jackpot

        print(
            ten_thousand_amnt,
            last_tier_jackpot_and_amount_diff,
            ten_thousand_jackpot_amount,
        )
        print(
            fifty_thousand_amnt,
            lower_tier_jackpot_and_amount_diff,
            fifty_thousand_jackpot_amount,
        )
        print(hundred_thousand_amnt, middle_tier_jackpot_and_amount_diff)
        print(
            two_hundred_thousand_amnt,
            upper_tier_jackpot_and_amount_diff,
            two_hundred_thousand_jackpot_amount,
        )
        #
        upper_tier = round((upper_tier_jackpot_and_amount_diff / self.REWARDS[200]), 2)
        disbursement_amount = math.floor(upper_tier) * self.REWARDS[200]
        upper_tier_balance = upper_tier_jackpot_and_amount_diff - disbursement_amount

        print("Upper", upper_tier)
        middle_tier = round(
            (middle_tier_jackpot_and_amount_diff + upper_tier_balance) / self.REWARDS[100],
            2,
        )
        print("middle ", middle_tier)
        disbursement_amount = math.floor(middle_tier) * self.REWARDS[100]
        middle_tier_balance = (middle_tier_jackpot_and_amount_diff + upper_tier_balance) - disbursement_amount

        lower_tier = round(
            (lower_tier_jackpot_and_amount_diff + middle_tier_balance) / self.REWARDS[50],
            2,
        )
        print("lower", lower_tier)
        disbursement_amount = math.floor(lower_tier) * self.REWARDS[50]
        lower_tier_balance = (lower_tier_jackpot_and_amount_diff + middle_tier_balance) - disbursement_amount

        last_tier = round(
            (last_tier_jackpot_and_amount_diff + lower_tier_balance) / self.REWARDS[10],
            2,
        )
        print("last ", last_tier)
        disbursement_amount = math.floor(last_tier) * self.REWARDS[10]
        last_tier_balance = (last_tier_jackpot_and_amount_diff + lower_tier_balance) - disbursement_amount
        print("last_tier_balance", last_tier_balance)
        #
        tjkpt = 0
        upper_tier_jackpot = round((two_hundred_thousand_jackpot_amount / self._JACKPOT_REWARDS[200]), 2)
        disbursement_amount = math.floor(upper_tier_jackpot) * self._JACKPOT_REWARDS[200]
        tjkpt += disbursement_amount
        upper_tier_jackpot_balance = two_hundred_thousand_jackpot_amount - disbursement_amount
        # print("UPPER TIER BAL", "__________________", upper_tier_jackpot_balance)
        print(
            "TOTALS",
            "__________________",
            two_hundred_thousand_jackpot_amount,
            self._JACKPOT_REWARDS[200],
        )
        print(
            "BUILD VALS",
            "__________________",
            two_hundred_thousand_jackpot_amount,
            disbursement_amount,
        )
        print(
            "UPPER TIER JKPT",
            "__________________",
            upper_tier_jackpot_balance,
        )

        middle_tier_jackpot = round(
            (hundred_thousand_jackpot_amount + upper_tier_jackpot_balance) / self._JACKPOT_REWARDS[100],
            2,
        )
        disbursement_amount = math.floor(middle_tier_jackpot) * self._JACKPOT_REWARDS[100]
        middle_tier_jackpot_balance = (hundred_thousand_jackpot_amount + upper_tier_jackpot_balance) - disbursement_amount
        tjkpt += disbursement_amount
        # print("MIDDLE TIER BAL", "__________________", middle_tier_jackpot_balance)
        print(
            "\n\nTOTALS",
            "__________________",
            hundred_thousand_jackpot_amount,
            self._JACKPOT_REWARDS[100],
        )
        print(
            "BUILD VALS",
            "__________________",
            hundred_thousand_jackpot_amount,
            upper_tier_jackpot_balance,
            disbursement_amount,
        )
        print(
            "MIDDLE TIER JKPT",
            "__________________",
            middle_tier_jackpot_balance,
        )

        lower_tier_jackpot = round(
            (fifty_thousand_jackpot_amount + middle_tier_jackpot_balance) / self._JACKPOT_REWARDS[50],
            2,
        )
        disbursement_amount = math.floor(lower_tier_jackpot) * self._JACKPOT_REWARDS[50]
        tjkpt += disbursement_amount
        lower_tier_jackpot_balance = (fifty_thousand_jackpot_amount + middle_tier_jackpot_balance) - disbursement_amount
        # print("DISB : :", disbursement_amount, "WINNERS : :", math.floor(lower_tier_jackpot), "VALUE : :", self._JACKPOT_REWARDS[50])

        print(
            "\n\nTOTALS",
            "__________________",
            fifty_thousand_jackpot_amount,
            self._JACKPOT_REWARDS[50],
        )
        print(
            "BUILD VALS",
            "__________________",
            fifty_thousand_jackpot_amount,
            middle_tier_jackpot_balance,
            disbursement_amount,
        )
        print("LOWER TIER BAL", "__________________", lower_tier_jackpot_balance)

        last_tier_jackpot = round(
            (ten_thousand_jackpot_amount + lower_tier_jackpot_balance) / self._JACKPOT_REWARDS[10],
            2,
        )
        disbursement_amount = math.floor(last_tier_jackpot) * self._JACKPOT_REWARDS[10]
        last_tier_jackpot_balance = (ten_thousand_jackpot_amount + lower_tier_jackpot_balance) - disbursement_amount

        tjkpt += disbursement_amount

        print(
            "\n\nTOTALS",
            "__________________",
            ten_thousand_jackpot_amount,
            self._JACKPOT_REWARDS[10],
        )
        print(
            "BUILD VALS",
            "__________________",
            ten_thousand_jackpot_amount,
            lower_tier_jackpot_balance,
            disbursement_amount,
        )
        print("Jkpt_bal", "__________________", last_tier_jackpot_balance)
        print("FINAL BALANCE : ", tjkpt)

        print(
            f"""
                =================================================================
                Target Unique Players           | {total_player}
                -----------------------------------------------------------------
                Estimated Total Revenue         | {total_revenue}
                -----------------------------------------------------------------
                Return to Owner                 | {rto}
                -----------------------------------------------------------------
                Return to Player                | {rtp}
                -----------------------------------------------------------------
                Global Jackpot                  | {GLOBAL_JACKPOT}
                ========================================================================================================
                ___________________________________SOCIAL PROOF_____AMOUNT______JACKPOT_______BALANCE___________
                10,000 Band player count        | {last_tier_percentage_contrib}%  |      {ten_thousand_amnt} |  {ten_thousand_jackpot_amount}    |  {last_tier_jackpot_and_amount_diff}
                --------------------------------------------------------------------------------------------------------
                50,000 Band player count        | {lower_tier_percentage_contrib}%  |      {fifty_thousand_amnt} |  {fifty_thousand_jackpot_amount}     |  {lower_tier_jackpot_and_amount_diff}
                --------------------------------------------------------------------------------------------------------
                100,000 Band player count       | {middle_tier_percentage_contrib}%   |      {hundred_thousand_amnt}  |  {hundred_thousand_jackpot_amount}   |  {middle_tier_jackpot_and_amount_diff}
                --------------------------------------------------------------------------------------------------------
                200,000 Band player count       | {upper_tier_percentage_contrib}%   |      {two_hundred_thousand_amnt}  |  {two_hundred_thousand_jackpot_amount}  |  {upper_tier_jackpot_and_amount_diff}
                --------------------------------------------------------------------------------------------------------
                ========================================================================================================
                Upper Tier                      | {upper_tier} | REWARDS {self.REWARDS[200]} {self._REWARDS_PERCENT[200]}
                --------------------------------------------------------------------------------------------------------
                Middle Tier                     | {middle_tier} | REWARDS {self.REWARDS[100]}  {self._REWARDS_PERCENT[100]}
                --------------------------------------------------------------------------------------------------------
                Lower Tier                      | {lower_tier} | REWARDS {self.REWARDS[50]}  {self._REWARDS_PERCENT[50]}
                --------------------------------------------------------------------------------------------------------
                Last Tier                       | {last_tier} | REWARDS {self.REWARDS[10]}  {self._REWARDS_PERCENT[10]}
                --------------------------------------------------------------------------------------------------------
                =======================================================================================================
                Upper Tier jackpot                     | {upper_tier_jackpot}
                --------------------------------------------------------------------------------------------------------
                Middle Tier jackpot                   | {middle_tier_jackpot}
                --------------------------------------------------------------------------------------------------------
                Lower Tier jackpot                      | {lower_tier_jackpot}
                --------------------------------------------------------------------------------------------------------
                Last Tier jackpot                      | {last_tier_jackpot}
                --------------------------------------------------------------------------------------------------------
                """
        )

        # total_payout = sum(
        #     (self.REWARDS[200]), (two_hundred_thousand_jackpot_amount),
        #     (self.REWARDS[100]), (hundred_thousand_jackpot_amount),
        #     (self.REWARDS[50]), (fifty_thousand_jackpot_amount),
        #     (self.REWARDS[10]), (ten_thousand_jackpot_amount)
        # )

        result = {
            "upper_tier": {
                "count": upper_tier,
                "reward": self.REWARDS[200],
                "pool": "TWO_HUNDRED_THOUSAND",
                "jackpot": self._JACKPOT_REWARDS[200],
                "jkpt_count": upper_tier_jackpot,
            },
            "middle_tier": {
                "count": middle_tier,
                "reward": self.REWARDS[100],
                "pool": "ONE_HUNDRED_THOUSAND",
                "jackpot": self._JACKPOT_REWARDS[100],
                "jkpt_count": middle_tier_jackpot,
            },
            "lower_tier": {
                "count": lower_tier,
                "reward": self.REWARDS[50],
                "pool": "FIFTY_THOUSAND",
                "jackpot": self._JACKPOT_REWARDS[50],
                "jkpt_count": lower_tier_jackpot,
            },
            "last_tier": {
                "count": last_tier,
                "reward": self.REWARDS[10],
                "pool": "TEN_THOUSAND",
                "jackpot": self._JACKPOT_REWARDS[10],
                "jkpt_count": last_tier_jackpot,
            },
            "rtp": rtp,
        }

        return result

    def draw(self):
        decision_dict = self.run_decisioning()

        for key, value in decision_dict.items():
            print(key, value)
            if key == "rtp":
                continue

            tier_players = self.query_set.filter(pool=value["pool"]).values_list("game_play_id", flat=True)
            sample = math.floor(value["count"])
            if len(list(tier_players)) < math.floor(value["count"]):
                sample = len(list(tier_players))

            print("list(tier_players)", list(tier_players))
            print('math.floor(value["count"])', math.floor(value["count"]), "\n\n\n\n\n")

            winners = random.sample(list(tier_players), sample)

            decision_dict[key]["winners"] = winners

        # pprint(decision_dict)

        for key, value in decision_dict.items():
            # print(key, value)
            if key == "rtp":
                continue

            tier_players = self.query_set.filter(pool=value["pool"]).values_list("game_play_id", flat=True)

            sample = math.floor(value["jkpt_count"])
            if len(list(tier_players)) < math.floor(value["jkpt_count"]):
                sample = len(list(tier_players))

            print("list(tier_players)", list(tier_players))
            print(
                'math.floor(value["count"])',
                math.floor(value["jkpt_count"]),
                "\n\n\n\n\n",
            )
            winners = random.sample(list(tier_players), sample)

            decision_dict[key]["jkpt_winners"] = winners

        # pprint(decision_dict)

        """

            {
            "upper_tier":{
                "count":2.59,
                "reward":40000.0,
                "pool":"TWO_HUNDRED_THOUSAND",
                "jackpot":34577.9,
                "jkpt_count":0.17,
                "winners":[
                    "400g3A9",
                    "f100T19"
                ],
                "jkpt_winners":[

                ]
            },
        "middle_tier":{
            "count":1.47,
            "reward":80000.0,
            "pool":"ONE_HUNDRED_THOUSAND",
            "jackpot":31397.24,
            "jkpt_count":0.66,
            "winners":[
                "v8005W6"
            ],
            "jkpt_winners":[

            ]
        },
        "lower_tier":{
            "count":4.55,
            "reward":30000.0,
            "pool":"FIFTY_THOUSAND",
            "jackpot":24649.61,
            "jkpt_count":1.81,
            "winners":[
                "9H2n222",
                "439Wq69",
                "4L540P1",
                "V4622j8"
            ],
            "jkpt_winners":[
                "5N01s79"
            ]
        },
        "last_tier":{
            "count":25.97,
            "reward":5000.0,
            "pool":"TEN_THOUSAND",
            "jackpot":20001.96,
            "jkpt_count":6.06,
            "winners":[
                "r4L9699",
                "18J0b16",
                "J4K0057",
                "Z6116P5",
                "6E3g156",
                "56S29H7",
                "N534c72",
                "618J6G4",
                "996O6E8",
                "6He5201",
                "92NS392",
                "49I3963",
                "o672H63",
                "67C662u",
                "585gL58",
                "2069bC3",
                "634X13n",
                "F16u926",
                "H8926t7",
                "H61U771",
                "A68Y505",
                "2V78n63",
                "78409Ld",
                "7x45U96",
                "0467bD1"
            ],
            "jkpt_winners":[
                "0347E7w",
                "991G4N0",
                "2Ag9035",
                "196Yj85",
                "4949J8e",
                "S8695s0"
            ]
        }
        }
        """

        # total_payout_amount = 0

        # for key, value in decision_dict.items():
        #     if key == "rtp":
        #         continue
        #     elif value["winners"]:
        #         for game_id in value["winners"]:
        #             total_payout_amount += value["reward"]

        #     elif value["jkpt_winners"]:
        #         for game_id in value:
        #             total_payout_amount += value["jackpot"]
        #     # total_payout_amount += value["jackpot"]

        total_payout_amount = 0

        for key, value in decision_dict.items():
            print(key)
            if key == "rtp":
                continue
            amount = len(value["winners"]) * value["reward"]
            jamount = len(value["jkpt_winners"]) * value["jackpot"]

            total_payout_amount += amount + jamount

        print(total_payout_amount)

        running_bal = decision_dict["rtp"] - total_payout_amount
        print("Final Vals", decision_dict["rtp"], total_payout_amount)

        constant_variable = ConstantVariable.objects.all().last()
        constant_variable.wyse_cash_running_balance = running_bal
        constant_variable.save()

        del decision_dict["rtp"]

        return decision_dict
