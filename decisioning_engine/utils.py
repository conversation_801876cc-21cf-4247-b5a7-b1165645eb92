import random
from pprint import pprint

LOW = 1
HIGH = 49
LINE_LENGTH = 5  # lines on column
MAX_NUM_LINES = 5  # lines on row

# gen_rand_list = []


def gen_rand_list() -> list:
    rand_nums = random.sample(range(LOW, HIGH), LINE_LENGTH)
    return rand_nums


def gen_rand_n_num_lists(n):
    """Requires quantity of numbers to be created."""
    return [(random.randint(1, MAX_NUM_LINES), gen_rand_list()) for _ in range(n)]


def search_number_occurences(numlist1: list, numlist2: list, number_of_match_limit=3) -> int:
    """COUNT COMMON NUMBERS"""
    match_count = 0
    # number_of_match_limit = 2 # random.choice([2, 1, 2, 1, 2, 1, 2])

    for number in numlist1:
        if number in numlist2[1]:
            match_count += 1

    return match_count if match_count > (number_of_match_limit - 1) else False


def get_potential_winning(data, prices, jackpot):
    """
    data[1][0] : number_of_lines
    data[0] : number_of_matches
    """

    base_price = prices[data[1][0]]
    sharing = {5: 1, 4: 1, 3: 0.6, 2: 0.6 * 0.5}

    if data[0] == 5:
        winning = jackpot

    else:
        winning = (
            (base_price * sharing[data[0]])
            # (base_price / data[1][0] * sharing[data[0]])
            if not sharing[data[0]] == 5
            else jackpot
        )

    response = [*data, int(winning)]

    return response


def get_banker_potential_winning(data, prices=0, jackpot=0, ticket_rtp=0):
    """
    data[1][0] : number_of_lines
    data[0] : number_of_matches
    """

    # print(prices)
    # print("DATA +++++++>", data)
    # print([data[1][2]])

    if data[0] == 5:
        winning = jackpot

    else:
        # print(base_price * sharing[data[0]])
        # print(base_price, "  :::  ", sharing[data[0]])
        winning = prices[data[1][2]][data[0]]

    response = [*data, int(winning)]
    # print(response)

    return response


if __name__ == "__main__":
    nums = gen_rand_n_num_lists(1000000)
    pprint(len(nums))
