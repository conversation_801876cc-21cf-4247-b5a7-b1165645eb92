import datetime
import random
import time
from functools import reduce
from pprint import pprint

from overide_print import print

TOTAL_CONTRIBUTION = 0
RUNNING_BALANCE = 0
BATCH_CONTRIBUTION = 0
PREVIOUS_BATCH_PLAYS = 0

SHARE_RATIO = 0.4, 0.35, 0.25

PLAYS = 2000000
BASE_N = 40
N = 40
BASE_PPS = 0  # BASE PLAYS PER SECOND
MOST_RECENT_ELAPSED_TIME = 0
BATCH = 0
SURGE = False

PLAYS_LIST = {}

sub_batch_time_elaspsed = 0

WIN_PROGRESSION_FACTOR = 0.833  # moving factor for winning from 3/4 -> 4/4 match

play_times = []

WINNINGS = {
    150: {"min_win": 4500, "mid_win": 5400, "max_win": 11250},
    300: {"min_win": 7500, "mid_win": 9000, "max_win": 13500},
    450: {"min_win": 9000, "mid_win": 10800, "max_win": 15750},
    600: {"min_win": 10800, "mid_win": 13000, "max_win": 18000},
    750: {"min_win": 12000, "mid_win": 14000, "max_win": 18750},
    900: {"min_win": 13500, "mid_win": 16000, "max_win": 22500},
    1000: {"min_win": 15000, "mid_win": 18000, "max_win": 25000},
}

for band in WINNINGS.keys():
    band_winning_values = list(WINNINGS[band].values())

    WINNINGS[band]["total_possible_for_3"] = sum(band_winning_values)
    WINNINGS[band]["total_possible_for_2"] = band_winning_values[0] + band_winning_values[1]

AVAILABLE_BANDS = list(WINNINGS.keys())

seed = [1, 1, 1, 3, 5, 6, 10]
random.shuffle(seed)
skew = [[band] * seed_val for seed_val, band in zip(seed, AVAILABLE_BANDS)]
skew_list = reduce(lambda l1, l2: l1 + l2, skew)

PAYOUTS = dict(WINNINGS.items())


def sharemonies(bands, share_values, requesting_for_2=False):
    print("....SHARING MONIES ....")
    print(f"BNDS....{bands} ....")
    print(f"SH_V....{share_values} ....")
    print(f"REQ2....{requesting_for_2} ....")

    bands_n_shares = zip(bands, share_values)
    winnin_tiers = []
    print(bands, share_values)
    BALANCE = 0

    for band, share in bands_n_shares:
        print("SHARE-BALANCE ::", share, "-", BALANCE)
        share = share + BALANCE
        payout_to_band_tiers = WINNINGS[band]

        print(f"{band} PAYOUT :::", payout_to_band_tiers)

        if share > payout_to_band_tiers["total_possible_for_3"]:
            BALANCE = share - (payout_to_band_tiers["min_win"] + payout_to_band_tiers["mid_win"] + payout_to_band_tiers["max_win"])

            winnin_tiers.append(("min_win", band, payout_to_band_tiers["min_win"]))
            winnin_tiers.append(("mid_win", band, payout_to_band_tiers["mid_win"]))
            winnin_tiers.append(("max_win", band, payout_to_band_tiers["max_win"]))

        elif share > payout_to_band_tiers["total_possible_for_2"]:
            BALANCE = share - (payout_to_band_tiers["min_win"] + payout_to_band_tiers["mid_win"])
            winnin_tiers.append(("min_win", band, payout_to_band_tiers["min_win"]))
            winnin_tiers.append(("mid_win", band, payout_to_band_tiers["mid_win"]))

        elif share > payout_to_band_tiers["min_win"]:
            BALANCE = share - (payout_to_band_tiers["min_win"])
            winnin_tiers.append(("min_win", band, payout_to_band_tiers["min_win"]))

        elif requesting_for_2 is False:
            print("BALANCE ::", BALANCE)
            return dict(requesting_for_1=False, requesting_for_2=True, winners=winnin_tiers, balance=BALANCE)

        else:
            print("BALANCE ::", BALANCE)
            return dict(requesting_for_1=True, requesting_for_2=False, winners=winnin_tiers, balance=BALANCE)

    print("BALANCE ::", BALANCE)

    return dict(requesting_for_2=False, winners=winnin_tiers, balance=BALANCE)


def get_unique_random(values, qty):
    while True:
        payout_bands = random.sample(values, qty)
        if len(set(payout_bands)) == 3:
            break

    return payout_bands


for i in range(PLAYS):
    play_times.append(datetime.datetime.now())

    time_delay = open("variables.txt", "r").read() or 1
    time.sleep(float(time_delay))

    picks = random.sample(range(1, 41), 4)

    play = random.choice(skew_list)
    TOTAL_CONTRIBUTION += play
    BATCH_CONTRIBUTION += play

    band_play = PLAYS_LIST.get(play, 0)

    if band_play:
        PLAYS_LIST[play] += 1
    else:
        PLAYS_LIST[play] = 1

    print(f"#{BATCH}", play, picks, "---->", TOTAL_CONTRIBUTION, BATCH_CONTRIBUTION)
    BATCH += 1

    if BATCH in list(range(0, int(N) + 1, 5)) and MOST_RECENT_ELAPSED_TIME != 0:
        play_times_recent_N_section = play_times[int(-BATCH) :]
        sub_batch_time_elaspsed: datetime.timedelta = play_times_recent_N_section[-1] - play_times_recent_N_section[0]

        if sub_batch_time_elaspsed.total_seconds() > MOST_RECENT_ELAPSED_TIME * 1.1:
            print("BATCH TIMES: ", sub_batch_time_elaspsed.total_seconds(), MOST_RECENT_ELAPSED_TIME * 1.5)
            print("OOUPS SURGE..!!!", "BATCH : ", -BATCH)

            if BATCH >= BASE_N:
                SURGE = True

        else:
            pass

    if BATCH >= N or SURGE:
        print("BREAKING FROM BATCH")
        print("N=", N, "BATCH=", BATCH)
        batch_play_times_recent_N_section = play_times[int(-BATCH) :]
        batch_time_elaspsed: datetime.timedelta = batch_play_times_recent_N_section[-1] - batch_play_times_recent_N_section[0]
        print(batch_play_times_recent_N_section[0])
        print(batch_play_times_recent_N_section[-1])

        play_times_recent_N_section = play_times[int(-BATCH) :]
        time_elaspsed: datetime.timedelta = play_times_recent_N_section[-1] - play_times_recent_N_section[0]
        play_frequency = N / time_elaspsed.total_seconds()
        MOST_RECENT_ELAPSED_TIME = time_elaspsed.total_seconds()

        def prepare_payout(BATCH_CONTRIBUTION, RUNNING_BALANCE):
            random.shuffle(AVAILABLE_BANDS)
            # print("AVAILABLE_BANDS ::", AVAILABLE_BANDS)

            payout_skew = [[key] * value for key, value in PLAYS_LIST.items()]
            payout_skew_list = reduce(lambda l1, l2: l1 + l2, payout_skew)
            # print("NORMALIZED_PAY : ", payout_skew_list)

            payout_bands = sorted(get_unique_random(payout_skew_list, 3))

            print(payout_bands)
            print("ORIGINAL CONTRIB:::", BATCH_CONTRIBUTION)
            print("RUNNING_BALANCE:::", RUNNING_BALANCE)

            BATCH_CONTRIBUTION += RUNNING_BALANCE

            print("UPDATED CONTRIB:::", BATCH_CONTRIBUTION)

            SHARE_VALUES = (SHARE_RATIO[0] * BATCH_CONTRIBUTION, SHARE_RATIO[1] * BATCH_CONTRIBUTION, SHARE_RATIO[2] * BATCH_CONTRIBUTION)

            print("SHARE VALUES : ", SHARE_VALUES)
            print("BATCH CONTRIB : ", BATCH_CONTRIBUTION)

            payout_bands = sorted(payout_bands, reverse=True)
            SHARE_VALUES = sorted(SHARE_VALUES, reverse=True)

            share_money_result = sharemonies(payout_bands, SHARE_VALUES)

            print("::::SHARE RES::::", share_money_result)
            print(f"""\n\n# {len(share_money_result["winners"])}WINNERS #\n\n""")

            if share_money_result.get("requesting_for_2"):
                print("\n\n:::::::::::::REQUESTED FOR 2::::::::::::::\n]\n")

                SHARE_VALUES[1] = SHARE_VALUES[1] + SHARE_VALUES[0] * 0.6
                SHARE_VALUES[2] = SHARE_VALUES[2] + SHARE_VALUES[0] * 0.4

                share_money_result = sharemonies([payout_bands[1], payout_bands[2]], [SHARE_VALUES[1], SHARE_VALUES[2]], requesting_for_2=True)
                print(f"""\n\n# {len(share_money_result["winners"])}WINNERS #\n\n""")

                print("SHARE AFTER RESOLVE\n\n", [payout_bands[1], payout_bands[2]], [SHARE_VALUES[1], SHARE_VALUES[2]])

            RUNNING_BALANCE = share_money_result.get("balance", 0)
            if share_money_result.get("requesting_for_1"):
                print("\n\n:::::::::::::REQUESTED FOR 1::::::::::::::\n]\n")

                share_money_result = sharemonies([payout_bands[2]], [SHARE_VALUES[1] + SHARE_VALUES[2]])
                print(f"""\n\n# {len(share_money_result["winners"])}WINNERS #\n\n""")

                print("SHARE AFTER RESOLVE FOR 1\n\n", [payout_bands[2]], [SHARE_VALUES[1] + SHARE_VALUES[2]])

            print("::::SHARE RES 2::::")
            pprint(share_money_result)

            RUNNING_BALANCE = share_money_result.get("balance", 0)
            print("\n\n\n=============================\nSHARES ::\n\n\n", share_money_result, "\n\n\n")
            if share_money_result.get("winners") == []:
                RUNNING_BALANCE = BATCH_CONTRIBUTION

            print("RUNNING_BALANCE::::", RUNNING_BALANCE)

            return share_money_result, RUNNING_BALANCE

        share_money_result, RUNNING_BALANCE = prepare_payout(BATCH_CONTRIBUTION, RUNNING_BALANCE)
        print("\n\n\n=============================BLAKKA ::\n\n\n", "\n\n\n")
        print("====|->", share_money_result, RUNNING_BALANCE)
        print("\n\n\n=============================BLAKKA ::\n\n\n", "\n\n\n")
        print("\n\n\n=============================BLAKKA ::\n\n\n", "\n\n\n")

        while RUNNING_BALANCE > 4500:
            print("\n\n\n=============================START SECOND RUN ", i)
            print("============================SECOND RUN ")
            print("============================SECOND RUN ")
            print("============================SECOND RUN ")

            share_money_result, RUNNING_BALANCE = prepare_payout(0, RUNNING_BALANCE)
            print("============================BLAKKA ::\n\n\n", "\n\n\n")
            print("====|->", share_money_result, RUNNING_BALANCE)
            print("============================BLAKKA ::\n\n\n", "\n\n\n")

            print("============================SECOND RUN ")
            print("============================SECOND RUN ")
            print("============================SECOND RUN ")
            print("============================ ENDED SECOND RUN ")

        if BASE_PPS == 0:
            BASE_PPS = play_frequency
        else:
            pps_delta = play_frequency / BASE_PPS
            NEW_N = BASE_N * pps_delta
            N = NEW_N if BASE_N < NEW_N else BASE_N

            if SURGE and BATCH < (PREVIOUS_BATCH_PLAYS * 0.5):
                N = BATCH if BASE_N < BATCH else BASE_N
                print("OOOPSIE DOWNWARD SURGE..!!!!")
                print(BATCH, PREVIOUS_BATCH_PLAYS, (PREVIOUS_BATCH_PLAYS * 1.5))
                input()

            print("DELTA : ", (pps_delta), "%")
            print("N : ", N, "players")

        PREVIOUS_BATCH_PLAYS = BATCH
        BATCH = 0

        print("BATCH REACHED")
        print(play_times_recent_N_section[0])
        print(play_times_recent_N_section[-1])
        print(round(time_elaspsed.total_seconds(), 2), "Secs")

        if sub_batch_time_elaspsed:
            print(round(sub_batch_time_elaspsed.total_seconds(), 2), "Secs @batch")

        print(play_frequency, "ps/sec", end="\n\n")

        if SURGE:
            input("Surge please press enter")
            SURGE = False

        BATCH_CONTRIBUTION = 0
    # if BATCH/N > 0: # IF THE THRESHOLD N VALUE HAS BEEN MET IN TERMS OF NUMBER OF PLAYS

    #     print("TIME TO ATTEMPT PAYOUT")
    # print(play, picks)

print(TOTAL_CONTRIBUTION)
