from django.urls import path

from africa_lotto.views import (
    AgainstMarketCalculatorView,
    BankerBallCalculatorView,
    CheckAfricaLottoWinningNumberApiView,
    FetchGameDraws,
    FetchDrawNumberUsingBatchNumberApiView,
    FetchGhanaDrawNumbersApiView,
    FetchKNowDrawNumbersApiView,
    FetchKenya30DrawNumbersApiView,
    FetchKenyaDrawNumbersApiView,
    FetchWebKenya30DrawNumbersApiView,
    ManuallyDrawGhanaGamesApiView,
    ManuallyDrawKenya30GamesApiView,
    ManuallyFilterWinningsView,
    NAPCalculatorView,
    PERMCalculatorView,
    PlayGhanaLottoGameView,
    PlayKNowLottoGameView,
    PlayKenya30LottoGameView,
    PlayKenyaLottoGameView,
    TempPublishFireBallSocketView,
    TempUpdateKNowDrawNumbersSocketNotificationView,
    TempUpdateKNowNextDrawSocketNotificationView,
)

urlpatterns = [
    path("play_gh_game/", PlayGhanaLottoGameView.as_view()),
    path("play_ke_game/", PlayKenyaLottoGameView.as_view()),
    path("play_ke_30_game/", PlayKenya30LottoGameView.as_view()),
    path("play_k_now_game/", PlayKNowLottoGameView.as_view()),
    path("calculate/nap/", NAPCalculatorView.as_view()),
    path("calculate/perm/", PERMCalculatorView.as_view()),
    path("calculate/banker-ball/", BankerBallCalculatorView.as_view()),
    path("calculate/against-market/", AgainstMarketCalculatorView.as_view()),
    path("fetch_ghana_draw_numbers/", FetchGhanaDrawNumbersApiView.as_view()),
    path("fetch_kenya_draw_numbers/", FetchKenyaDrawNumbersApiView.as_view()),
    path("fetch_30_kenya_draw_numbers/", FetchKenya30DrawNumbersApiView.as_view()),
    path("fetch_k_now_draw_numbers/", FetchKNowDrawNumbersApiView.as_view()),
    path("fetch_web_30_kenya_draw_numbers/", FetchWebKenya30DrawNumbersApiView.as_view()),
    path("manual_run_ghana_draw/", ManuallyDrawGhanaGamesApiView.as_view()),
    path("manual_filter_winnings/", ManuallyFilterWinningsView.as_view()),
    path("manual_run_kenya_30_draw/", ManuallyDrawKenya30GamesApiView.as_view()),
    path("get_draw_number_using_batch_number/", FetchDrawNumberUsingBatchNumberApiView.as_view()),

    path("check_africa_lotto_winnings/", CheckAfricaLottoWinningNumberApiView.as_view()),
    path("fetch_latest_game_draws/", FetchGameDraws.as_view()),
]
