from rest_framework import serializers, status
from rest_framework.exceptions import APIException

from africa_lotto.helpers import (
    AgainstMarketCalculator,
    PermCalculator,
    calculate_banker_ball,
    calculate_nap_ticket,
)
from africa_lotto.models import (
    AfricaLotteryType,
    AfricaLotto,
    AfricaLottoBatch,
    AfricaLottoConstants,
    AfricaLottoDrawNumbers,
    AfricaLottoGameType,
)
from pos_app.models import PosLotteryWinners


class CustomValidationError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "Bad request"

    def __init__(self, detail=None, status_code=None):
        self.detail = {"message": detail}
        if status_code is not None:
            self.status_code = status_code


class GhanaLottoGamePlayChildTicketSerializers(serializers.Serializer):

    lucky_number = serializers.ListField(child=serializers.IntegerField())
    bottom_ticket = serializers.ListField(required=False, child=serializers.IntegerField())
    lottery_type = serializers.ChoiceField(choices=AfricaLotteryType.choices, required=True)
    purchase_amount = serializers.FloatField()
    double_chance = serializers.BooleanField(default = False)


class PlayGhanaLottoGameSerializers(serializers.Serializer):
    lottery_data = GhanaLottoGamePlayChildTicketSerializers(many=True)

    def validate(self, attrs):
        lottery_data = attrs.get("lottery_data")
        total_amount = 0

        minimum_purchase_amount = AfricaLottoConstants.get_minimum_stake()

        new_lottery_data = []

        games_combined = set()

        if len(lottery_data) > 10:
            raise CustomValidationError("You can only play a maximum of 10 games at a time")

        for data in lottery_data:
            lottery_type = data.get("lottery_type")
            lucky_number = data.get("lucky_number")
            bottom_ticket = data.get("bottom_ticket")
            purchase_amount = data.get("purchase_amount")
            double_chance = data.get("double_chance")

            purchase_amount_to_store = purchase_amount

            potential_winnings_to_store = None
            multiplier_to_store = None
            stake_per_line_to_store = 0
            winning_scenarios_to_store = None

            if lottery_type not in [AfricaLotteryType.PERM2, AfricaLotteryType.PERM2, AfricaLotteryType.PERM2, AfricaLotteryType.PERM2]:

                if purchase_amount < minimum_purchase_amount:
                    raise CustomValidationError(f"Purchase amount must be at least {minimum_purchase_amount}")
            else:
                per_minimum_purchase_amount = AfricaLottoConstants.get_perm_minimum_stake()
                if purchase_amount < per_minimum_purchase_amount:
                    raise CustomValidationError(f"Purchase amount must be at least {per_minimum_purchase_amount}")

            if purchase_amount > 50000:
                raise CustomValidationError("Purchase amount must not exceed 50,000")

            total_amount += purchase_amount

            if double_chance is True:
                total_amount += purchase_amount

            len_of_lucky_number = len(lucky_number)

            if AfricaLotteryType.NAP2 == lottery_type:
                games_combined.add(lottery_type)
                if len_of_lucky_number != 2:
                    raise CustomValidationError("Lucky number must be 2 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(2, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                print("get_calculation_details", get_calculation_details)

                print(
                    f"""
                        get_calculation_details: {type(get_calculation_details)}\n\n
                        """
                )

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP3 == lottery_type:
                games_combined.add(lottery_type)
                if len_of_lucky_number != 3:

                    raise CustomValidationError("Lucky number must be 3 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(3, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP4 == lottery_type:
                games_combined.add(lottery_type)
                if len_of_lucky_number != 4:
                    raise CustomValidationError("Lucky number must be 4 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(4, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP5 == lottery_type:
                games_combined.add(lottery_type)
                if len_of_lucky_number != 5:
                    raise CustomValidationError("Lucky number must be 5 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(5, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if "PERM" in lottery_type:
                games_combined.add(lottery_type)

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount

                perm_calculator = PermCalculator()
                try:
                    get_calculation_details = perm_calculator.calculate_ticket(
                        perm_type=lottery_type, selected_numbers=lucky_number, stake_per_line=purchase_amount
                    )
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["max_potential_win"]
                multiplier_to_store = get_calculation_details["multiplier"]
                purchase_amount_to_store = get_calculation_details["total_cost"]

                total_amount += get_calculation_details["total_cost"]

                

                stake_per_line_to_store = get_calculation_details["stake_per_line"]

                if double_chance is True:
                    total_amount += get_calculation_details["total_cost"]
                    stake_per_line_to_store += get_calculation_details["stake_per_line"] / 2

            if AfricaLotteryType.BANKER == lottery_type:
                games_combined.add(lottery_type)
                if len_of_lucky_number != 1:
                    raise CustomValidationError("Lucky number must be 1 digits")

                try:
                    get_calculation_details = calculate_banker_ball(purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                print("get_calculation_details", get_calculation_details, "\n\n\n")

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount

                

                total_amount += get_calculation_details["total_stake"]

            

                purchase_amount_to_store = get_calculation_details["total_stake"]

                if double_chance is True:
                    total_amount += get_calculation_details["total_stake"]
                    purchase_amount_to_store += get_calculation_details["total_stake"] / 2

            if AfricaLotteryType.AGAINST == lottery_type:
                games_combined.add(lottery_type)
                bottom_ticket = data.get("bottom_ticket")

                if bottom_ticket is None:
                    raise CustomValidationError("Bottom ticket is required for against")

                bottom_ticket = data.get("bottom_ticket")
                if isinstance(bottom_ticket, list):
                    splitted_bottom_ticket = bottom_ticket
                else:
                    splitted_bottom_ticket = bottom_ticket.split(",")

                if isinstance(lucky_number, list):
                    splitted_lucky_number = lucky_number
                else:
                    splitted_lucky_number = lucky_number.split(",")

                # check if any exist in any of the list
                if any(x in splitted_lucky_number for x in splitted_bottom_ticket):
                    raise CustomValidationError("Top and bottom numbers must not be the same")

                against_calculator = AgainstMarketCalculator()

                try:
                    ticket = against_calculator.calculate_ticket(
                        top_numbers=lucky_number, bottom_numbers=bottom_ticket, stake_per_line=purchase_amount
                    )

                except ValueError as e:
                    raise CustomValidationError(str(e))

                print("ticket, ticket", ticket, "\n\n\n")

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount

                total_amount += ticket["total_stake"]

                

                purchase_amount_to_store = ticket["total_stake"]

                if double_chance is True:
                    total_amount += ticket["total_stake"]
                    purchase_amount_to_store += ticket["total_stake"]

                potential_winnings_to_store = 0

            new_lottery_data.append(
                {
                    "lucky_number": lucky_number,
                    "bottom_ticket": bottom_ticket,
                    "lottery_type": lottery_type,
                    "purchase_amount": purchase_amount_to_store,
                    "potential_winnings": potential_winnings_to_store,
                    "multiplier": multiplier_to_store,
                    "stake_per_line": stake_per_line_to_store,
                    "winning_scenarios": winning_scenarios_to_store,
                    "double_chance": double_chance
                }
            )

        # VALIDATE AMOUNT
        played_ten_naira = False
        played_more_than_or_at_least_50 = False
        for data in new_lottery_data:
            lottery_type = data["lottery_type"]
            if lottery_type in [AfricaLotteryType.NAP2, AfricaLotteryType.NAP3, AfricaLotteryType.NAP4, AfricaLotteryType.NAP5]:
                if data["purchase_amount"] == 10:
                    played_ten_naira = True

                if data["purchase_amount"] >= minimum_purchase_amount:
                    played_more_than_or_at_least_50 = True

        if played_ten_naira is True and played_more_than_or_at_least_50 is False:
            raise CustomValidationError("You can not play 10 naira without playing at least 50 naira ticket")

        # convert set to list
        games_combined = list(games_combined)

        # generate game id.

        combine_ticket_minimum_stake = AfricaLottoConstants.get_combine_ticket_minimum_stake()
        if total_amount < combine_ticket_minimum_stake:
            raise CustomValidationError(f"Total amount for combined ticket must be at least {combine_ticket_minimum_stake}")
        

        attrs["lottery_data"] = new_lottery_data
        attrs["total_amount"] = total_amount
        return attrs


class PlayKenyaLottoGameSerializers(serializers.Serializer):
    lottery_data = GhanaLottoGamePlayChildTicketSerializers(many=True)

    def validate(self, attrs):
        lottery_data = attrs.get("lottery_data")
        total_amount = 0

        minimum_purchase_amount = AfricaLottoConstants.get_minimum_stake()

        new_lottery_data = []

        if len(lottery_data) > 10:
            raise CustomValidationError("You can only play a maximum of 10 games at a time")

        for data in lottery_data:
            lottery_type = data.get("lottery_type")
            lucky_number = data.get("lucky_number")
            bottom_ticket = data.get("bottom_ticket")
            purchase_amount = data.get("purchase_amount")
            double_chance = data.get("double_chance")

            purchase_amount_to_store = purchase_amount

            potential_winnings_to_store = None
            multiplier_to_store = None
            stake_per_line_to_store = 0
            winning_scenarios_to_store = None

            if lottery_type not in [AfricaLotteryType.PERM2, AfricaLotteryType.PERM2, AfricaLotteryType.PERM2, AfricaLotteryType.PERM2]:

                if purchase_amount < minimum_purchase_amount:
                    raise CustomValidationError(f"Purchase amount must be at least {minimum_purchase_amount}")
            else:
                per_minimum_purchase_amount = AfricaLottoConstants.get_perm_minimum_stake()
                if purchase_amount < per_minimum_purchase_amount:
                    raise CustomValidationError(f"Purchase amount must be at least {per_minimum_purchase_amount}")

            if purchase_amount > 50000:
                raise CustomValidationError("Purchase amount must not exceed 50,000")

            total_amount += purchase_amount

            if double_chance is True:
                total_amount += purchase_amount

            len_of_lucky_number = len(lucky_number)

            if AfricaLotteryType.NAP2 == lottery_type:
                if len_of_lucky_number != 2:
                    raise CustomValidationError("Lucky number must be 2 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(2, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                print("get_calculation_details", get_calculation_details)

                print(
                    f"""
                        get_calculation_details: {type(get_calculation_details)}\n\n
                        """
                )

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP3 == lottery_type:
                if len_of_lucky_number != 3:
                    raise CustomValidationError("Lucky number must be 3 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(3, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP4 == lottery_type:
                if len_of_lucky_number != 4:
                    raise CustomValidationError("Lucky number must be 4 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(4, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP5 == lottery_type:
                if len_of_lucky_number != 5:
                    raise CustomValidationError("Lucky number must be 5 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(5, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if "PERM" in lottery_type:

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount

                perm_calculator = PermCalculator()
                try:
                    get_calculation_details = perm_calculator.calculate_ticket(
                        perm_type=lottery_type, selected_numbers=lucky_number, stake_per_line=purchase_amount
                    )
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["max_potential_win"]
                multiplier_to_store = get_calculation_details["multiplier"]
                purchase_amount_to_store = get_calculation_details["total_cost"]

                total_amount += get_calculation_details["total_cost"]


                stake_per_line_to_store = get_calculation_details["stake_per_line"]

                if double_chance is True:
                    total_amount += get_calculation_details["total_cost"]
                    stake_per_line_to_store +=  get_calculation_details["total_cost"]

            if AfricaLotteryType.BANKER == lottery_type:
                if len_of_lucky_number != 1:
                    raise CustomValidationError("Lucky number must be 1 digits")

                try:
                    get_calculation_details = calculate_banker_ball(purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                print("get_calculation_details", get_calculation_details, "\n\n\n")

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount

                total_amount += get_calculation_details["total_stake"]


                purchase_amount_to_store = get_calculation_details["total_stake"]

                if double_chance is True:
                    total_amount += get_calculation_details["total_cost"]
                    purchase_amount_to_store += get_calculation_details["total_cost"]

            if AfricaLotteryType.AGAINST == lottery_type:
                bottom_ticket = data.get("bottom_ticket")

                if isinstance(bottom_ticket, list):
                    splitted_bottom_ticket = bottom_ticket
                else:
                    splitted_bottom_ticket = bottom_ticket.split(",")

                if isinstance(lucky_number, list):
                    splitted_lucky_number = lucky_number
                else:
                    splitted_lucky_number = lucky_number.split(",")

                if bottom_ticket is None:
                    raise CustomValidationError("Bottom ticket is required for against")

                # check if any exist in any of the list
                if any(x in splitted_lucky_number for x in splitted_bottom_ticket):
                    raise CustomValidationError("Top and bottom numbers must not be the same")

                against_calculator = AgainstMarketCalculator()

                try:
                    ticket = against_calculator.calculate_ticket(
                        top_numbers=lucky_number, bottom_numbers=bottom_ticket, stake_per_line=purchase_amount
                    )

                except ValueError as e:
                    raise CustomValidationError(str(e))

                print("ticket, ticket", ticket, "\n\n\n")

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount

                total_amount += ticket["total_stake"]

                

                purchase_amount_to_store = ticket["total_stake"]

                if double_chance is True:
                    total_amount += ticket["total_stake"]
                    purchase_amount_to_store += ticket["total_stake"]

                potential_winnings_to_store = 0

            new_lottery_data.append(
                {
                    "lucky_number": lucky_number,
                    "bottom_ticket": bottom_ticket,
                    "lottery_type": lottery_type,
                    "purchase_amount": purchase_amount_to_store,
                    "potential_winnings": potential_winnings_to_store,
                    "multiplier": multiplier_to_store,
                    "stake_per_line": stake_per_line_to_store,
                    "winning_scenarios": winning_scenarios_to_store,
                    "double_chance": double_chance
                }
            )

        # VALIDATE AMOUNT
        played_ten_naira = False
        played_more_than_or_at_least_50 = False
        for data in new_lottery_data:
            lottery_type = data["lottery_type"]
            if lottery_type in [AfricaLotteryType.NAP2, AfricaLotteryType.NAP3, AfricaLotteryType.NAP4, AfricaLotteryType.NAP5]:
                if data["purchase_amount"] == 10:
                    played_ten_naira = True

                if data["purchase_amount"] >= minimum_purchase_amount:
                    played_more_than_or_at_least_50 = True

        if played_ten_naira is True and played_more_than_or_at_least_50 is False:
            raise CustomValidationError("You can not play 10 naira without playing at least 50 naira ticket")

        # generate game id.

        combine_ticket_minimum_stake = AfricaLottoConstants.get_combine_ticket_minimum_stake()
        if total_amount < combine_ticket_minimum_stake:
            raise CustomValidationError(f"Total amount for combined ticket must be at least {combine_ticket_minimum_stake}")

        attrs["lottery_data"] = new_lottery_data
        attrs["total_amount"] = total_amount
        return attrs


class PlayKenya30LottoGameSerializers(serializers.Serializer):
    lottery_data = GhanaLottoGamePlayChildTicketSerializers(many=True)

    def validate(self, attrs):
        lottery_data = attrs.get("lottery_data")
        total_amount = 0

        minimum_purchase_amount = AfricaLottoConstants.get_minimum_stake()

        new_lottery_data = []

        if len(lottery_data) > 10:
            raise CustomValidationError("You can only play a maximum of 10 games at a time")

        for data in lottery_data:
            lottery_type = data.get("lottery_type")
            lucky_number = data.get("lucky_number")
            bottom_ticket = data.get("bottom_ticket")
            purchase_amount = data.get("purchase_amount")
            double_chance = data.get("double_chance")

            purchase_amount_to_store = purchase_amount

            potential_winnings_to_store = None
            multiplier_to_store = None
            stake_per_line_to_store = 0
            winning_scenarios_to_store = None

            # validate that none of the lucky number is more than 60
            for i in lucky_number:
                if i > 60:
                    raise CustomValidationError("Lucky number must not be more than 60")

            if bottom_ticket:
                for i in bottom_ticket:
                    if i > 60:
                        raise CustomValidationError("Bottom ticket must not be more than 60")

            if lottery_type not in [AfricaLotteryType.PERM2, AfricaLotteryType.PERM2, AfricaLotteryType.PERM2, AfricaLotteryType.PERM2]:

                if purchase_amount < minimum_purchase_amount:
                    raise CustomValidationError(f"Purchase amount must be at least {minimum_purchase_amount}")
            else:
                per_minimum_purchase_amount = AfricaLottoConstants.get_perm_minimum_stake()
                if purchase_amount < per_minimum_purchase_amount:
                    raise CustomValidationError(f"Purchase amount must be at least {per_minimum_purchase_amount}")

            if purchase_amount > 50000:
                raise CustomValidationError("Purchase amount must not exceed 50,000")

            total_amount += purchase_amount

            if double_chance is True:
                total_amount += purchase_amount
                purchase_amount_to_store += purchase_amount

            len_of_lucky_number = len(lucky_number)

            if AfricaLotteryType.NAP2 == lottery_type:
                if len_of_lucky_number != 2:
                    raise CustomValidationError("Lucky number must be 2 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(2, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                print("get_calculation_details", get_calculation_details)

                print(
                    f"""
                        get_calculation_details: {type(get_calculation_details)}\n\n
                        """
                )

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP3 == lottery_type:
                if len_of_lucky_number != 3:
                    raise CustomValidationError("Lucky number must be 3 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(3, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP4 == lottery_type:
                if len_of_lucky_number != 4:
                    raise CustomValidationError("Lucky number must be 4 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(4, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"] / 2
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP5 == lottery_type:
                if len_of_lucky_number != 5:
                    raise CustomValidationError("Lucky number must be 5 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(5, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"] / 2
                multiplier_to_store = get_calculation_details["multiplier"]

            if "PERM" in lottery_type:

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount

                perm_calculator = PermCalculator()
                try:
                    get_calculation_details = perm_calculator.calculate_ticket(
                        perm_type=lottery_type, selected_numbers=lucky_number, stake_per_line=purchase_amount
                    )
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["max_potential_win"] / 2
                multiplier_to_store = get_calculation_details["multiplier"]
                purchase_amount_to_store = get_calculation_details["total_cost"]

                total_amount += get_calculation_details["total_cost"]

                

                stake_per_line_to_store = get_calculation_details["stake_per_line"]

                if double_chance is True:
                    total_amount += get_calculation_details["total_cost"] 
                    stake_per_line_to_store += get_calculation_details["stake_per_line"]
                    purchase_amount_to_store += get_calculation_details["total_cost"]



            if AfricaLotteryType.BANKER == lottery_type:
                if len_of_lucky_number != 1:
                    raise CustomValidationError("Lucky number must be 1 digits")

                try:
                    get_calculation_details = calculate_banker_ball(purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                # print("get_calculation_details", get_calculation_details, "\n\n\n")

                potential_winnings_to_store = get_calculation_details["potential_winnings"] / 2
                multiplier_to_store = get_calculation_details["multiplier"]

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount 

                total_amount += get_calculation_details["total_stake"]

                purchase_amount_to_store = get_calculation_details["total_stake"]

                if double_chance is True:
                    total_amount += get_calculation_details["total_stake"]
                    purchase_amount_to_store += get_calculation_details["total_stake"]



            if AfricaLotteryType.AGAINST == lottery_type:
                # bottom_ticket = data.get("bottom_ticket")

                if isinstance(bottom_ticket, list):
                    splitted_bottom_ticket = bottom_ticket
                else:
                    splitted_bottom_ticket = bottom_ticket.split(",")

                if isinstance(lucky_number, list):
                    splitted_lucky_number = lucky_number
                else:
                    splitted_lucky_number = lucky_number.split(",")

                if bottom_ticket is None:
                    raise CustomValidationError("Bottom ticket is required for against")

                # check if any exist in any of the list
                if any(x in splitted_lucky_number for x in splitted_bottom_ticket):
                    raise CustomValidationError("Top and bottom numbers must not be the same")

                against_calculator = AgainstMarketCalculator()

                try:
                    ticket = against_calculator.calculate_ticket(
                        top_numbers=lucky_number, bottom_numbers=bottom_ticket, stake_per_line=purchase_amount
                    )

                except ValueError as e:
                    raise CustomValidationError(str(e))

                # print("ticket, ticket", ticket, "\n\n\n")

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount

                total_amount += ticket["total_stake"]

                purchase_amount_to_store = ticket["total_stake"]

                if double_chance is True:
                    total_amount += ticket["total_stake"] 
                    purchase_amount_to_store += ticket["total_stake"]

                potential_winnings_to_store = 0

            new_lottery_data.append(
                {
                    "lucky_number": lucky_number,
                    "bottom_ticket": bottom_ticket,
                    "lottery_type": lottery_type,
                    "purchase_amount": purchase_amount_to_store,
                    "potential_winnings": potential_winnings_to_store,
                    "multiplier": multiplier_to_store,
                    "stake_per_line": stake_per_line_to_store,
                    "winning_scenarios": winning_scenarios_to_store,
                    "double_chance": double_chance
                }
            )

        # VALIDATE AMOUNT
        played_ten_naira = False
        played_more_than_or_at_least_50 = False
        for data in new_lottery_data:
            lottery_type = data["lottery_type"]
            if lottery_type in [AfricaLotteryType.NAP2, AfricaLotteryType.NAP3, AfricaLotteryType.NAP4, AfricaLotteryType.NAP5]:
                if data["purchase_amount"] == 10:
                    played_ten_naira = True

                if data["purchase_amount"] >= minimum_purchase_amount:
                    played_more_than_or_at_least_50 = True

        if played_ten_naira is True and played_more_than_or_at_least_50 is False:
            raise CustomValidationError("You can not play 10 naira without playing at least 50 naira ticket")

        if total_amount < AfricaLottoConstants.get_kenya_30_lotto_minmum_game_play_amount():
            raise CustomValidationError("Your total ticket amount should not be less than 200")

        # generate game id.

        combine_ticket_minimum_stake = AfricaLottoConstants.get_combine_ticket_minimum_stake()
        if total_amount < combine_ticket_minimum_stake:
            raise CustomValidationError(f"Total amount for combined ticket must be at least {combine_ticket_minimum_stake}")

        attrs["lottery_data"] = new_lottery_data
        attrs["total_amount"] = total_amount
        return attrs
    


class PlayKNowLottoGameSerializers(serializers.Serializer):
    lottery_data = GhanaLottoGamePlayChildTicketSerializers(many=True)

    def validate(self, attrs):
        lottery_data = attrs.get("lottery_data")
        total_amount = 0

        minimum_purchase_amount = AfricaLottoConstants.get_minimum_stake()

        new_lottery_data = []

        if len(lottery_data) > 10:
            raise CustomValidationError("You can only play a maximum of 10 games at a time")

        for data in lottery_data:
            lottery_type = data.get("lottery_type")
            lucky_number = data.get("lucky_number")
            bottom_ticket = data.get("bottom_ticket")
            purchase_amount = data.get("purchase_amount")
            double_chance = data.get("double_chance")

            purchase_amount_to_store = purchase_amount

            potential_winnings_to_store = None
            multiplier_to_store = None
            stake_per_line_to_store = 0
            winning_scenarios_to_store = None

            # validate that none of the lucky number is more than 60
            for i in lucky_number:
                if i > 60:
                    raise CustomValidationError("Lucky number must not be more than 60")

            if bottom_ticket:
                for i in bottom_ticket:
                    if i > 60:
                        raise CustomValidationError("Bottom ticket must not be more than 60")

            if lottery_type not in [AfricaLotteryType.PERM2, AfricaLotteryType.PERM2, AfricaLotteryType.PERM2, AfricaLotteryType.PERM2]:

                if purchase_amount < minimum_purchase_amount:
                    raise CustomValidationError(f"Purchase amount must be at least {minimum_purchase_amount}")
            else:
                per_minimum_purchase_amount = AfricaLottoConstants.get_perm_minimum_stake()
                if purchase_amount < per_minimum_purchase_amount:
                    raise CustomValidationError(f"Purchase amount must be at least {per_minimum_purchase_amount}")

            if purchase_amount > 50000:
                raise CustomValidationError("Purchase amount must not exceed 50,000")

            total_amount += purchase_amount

            

            if double_chance is True:
                total_amount += purchase_amount
                purchase_amount_to_store += purchase_amount

            len_of_lucky_number = len(lucky_number)

            if AfricaLotteryType.NAP2 == lottery_type:
                if len_of_lucky_number != 2:
                    raise CustomValidationError("Lucky number must be 2 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(2, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                print("get_calculation_details", get_calculation_details)

                print(
                    f"""
                        get_calculation_details: {type(get_calculation_details)}\n\n
                        """
                )

                potential_winnings_to_store = get_calculation_details["potential_winnings"] / 2
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP3 == lottery_type:
                if len_of_lucky_number != 3:
                    raise CustomValidationError("Lucky number must be 3 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(3, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"] / 2
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP4 == lottery_type:
                if len_of_lucky_number != 4:
                    raise CustomValidationError("Lucky number must be 4 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(4, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"]
                multiplier_to_store = get_calculation_details["multiplier"]

            if AfricaLotteryType.NAP5 == lottery_type:
                if len_of_lucky_number != 5:
                    raise CustomValidationError("Lucky number must be 5 digits")

                try:
                    get_calculation_details = calculate_nap_ticket(5, purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["potential_winnings"] / 2
                multiplier_to_store = get_calculation_details["multiplier"]

            if "PERM" in lottery_type:

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount

                perm_calculator = PermCalculator()
                try:
                    get_calculation_details = perm_calculator.calculate_ticket(
                        perm_type=lottery_type, selected_numbers=lucky_number, stake_per_line=purchase_amount
                    )
                except ValueError as e:
                    raise CustomValidationError(str(e))

                potential_winnings_to_store = get_calculation_details["max_potential_win"] / 2
                multiplier_to_store = get_calculation_details["multiplier"]
                purchase_amount_to_store = get_calculation_details["total_cost"]

                total_amount += get_calculation_details["total_cost"]


                stake_per_line_to_store = get_calculation_details["stake_per_line"]

                if double_chance is True:
                    total_amount += get_calculation_details["total_cost"]
                    stake_per_line_to_store += get_calculation_details["stake_per_line"]
                    purchase_amount_to_store += get_calculation_details["total_cost"]

            if AfricaLotteryType.BANKER == lottery_type:
                if len_of_lucky_number != 1:
                    raise CustomValidationError("Lucky number must be 1 digits")

                try:
                    get_calculation_details = calculate_banker_ball(purchase_amount)
                except ValueError as e:
                    raise CustomValidationError(str(e))

                print("get_calculation_details", get_calculation_details, "\n\n\n")

                potential_winnings_to_store = get_calculation_details["potential_winnings"] / 2
                multiplier_to_store = get_calculation_details["multiplier"]

                total_amount -= purchase_amount
                if double_chance is True:
                    total_amount -= purchase_amount

                total_amount += get_calculation_details["total_stake"]

                purchase_amount_to_store = get_calculation_details["total_stake"]

                if double_chance is True:
                    total_amount += get_calculation_details["total_stake"] 
                    purchase_amount_to_store += get_calculation_details["total_stake"]


            if AfricaLotteryType.AGAINST == lottery_type:
                # bottom_ticket = data.get("bottom_ticket")

                if isinstance(bottom_ticket, list):
                    splitted_bottom_ticket = bottom_ticket
                else:
                    splitted_bottom_ticket = bottom_ticket.split(",")

                if isinstance(lucky_number, list):
                    splitted_lucky_number = lucky_number
                else:
                    splitted_lucky_number = lucky_number.split(",")

                if bottom_ticket is None:
                    raise CustomValidationError("Bottom ticket is required for against")

                # check if any exist in any of the list
                if any(x in splitted_lucky_number for x in splitted_bottom_ticket):
                    raise CustomValidationError("Top and bottom numbers must not be the same")

                against_calculator = AgainstMarketCalculator()

                try:
                    ticket = against_calculator.calculate_ticket(
                        top_numbers=lucky_number, bottom_numbers=bottom_ticket, stake_per_line=purchase_amount
                    )

                except ValueError as e:
                    raise CustomValidationError(str(e))

                print("ticket, ticket", ticket, "\n\n\n")

                total_amount -= purchase_amount

                if double_chance is True:
                    total_amount -= purchase_amount

                total_amount += ticket["total_stake"]


                purchase_amount_to_store = ticket["total_stake"]

                if double_chance is True:
                    total_amount += ticket["total_stake"]
                    purchase_amount_to_store += ticket["total_stake"]

                potential_winnings_to_store = 0

            new_lottery_data.append(
                {
                    "lucky_number": lucky_number,
                    "bottom_ticket": bottom_ticket,
                    "lottery_type": lottery_type,
                    "purchase_amount": purchase_amount_to_store,
                    "potential_winnings": potential_winnings_to_store,
                    "multiplier": multiplier_to_store,
                    "stake_per_line": stake_per_line_to_store,
                    "winning_scenarios": winning_scenarios_to_store,
                    "double_chance": double_chance
                }
            )

        # VALIDATE AMOUNT
        played_ten_naira = False
        played_more_than_or_at_least_50 = False
        for data in new_lottery_data:
            lottery_type = data["lottery_type"]
            if lottery_type in [AfricaLotteryType.NAP2, AfricaLotteryType.NAP3, AfricaLotteryType.NAP4, AfricaLotteryType.NAP5]:
                if data["purchase_amount"] == 10:
                    played_ten_naira = True

                if data["purchase_amount"] >= minimum_purchase_amount:
                    played_more_than_or_at_least_50 = True

        if played_ten_naira is True and played_more_than_or_at_least_50 is False:
            raise CustomValidationError("You can not play 10 naira without playing at least 50 naira ticket")

        if total_amount < AfricaLottoConstants.get_k_now_lotto_minmum_game_play_amount():
            raise CustomValidationError("Your total ticket amount should not be less than 300")

        # generate game id.

        combine_ticket_minimum_stake = AfricaLottoConstants.get_combine_ticket_minimum_stake()
        if total_amount < combine_ticket_minimum_stake:
            raise CustomValidationError(f"Total amount for combined ticket must be at least {combine_ticket_minimum_stake}")

        attrs["lottery_data"] = new_lottery_data
        attrs["total_amount"] = total_amount
        return attrs


class NAPSerializer(serializers.Serializer):
    nap_type = serializers.IntegerField(min_value=2, max_value=5)
    purchase_amount = serializers.FloatField(min_value=5, max_value=50000)


class PERMSerializer(serializers.Serializer):
    perm_type = serializers.RegexField(regex=r"^PERM[2-5]$")
    selected_numbers = serializers.ListField(child=serializers.IntegerField(min_value=1, max_value=90), min_length=3, max_length=20)
    stake_per_line = serializers.FloatField(min_value=5, max_value=50000)

    def validate_selected_numbers(self, value):
        if len(set(value)) != len(value):
            raise CustomValidationError("Duplicate numbers are not allowed")
        return sorted(value)


class BankerBallSerializer(serializers.Serializer):
    single_stake = serializers.FloatField(min_value=5, max_value=50000)


class AgainstMarketSerializer(serializers.Serializer):
    top_numbers = serializers.ListField(child=serializers.IntegerField(min_value=1, max_value=90), min_length=1, max_length=10)
    bottom_numbers = serializers.ListField(child=serializers.IntegerField(min_value=1, max_value=90), min_length=1, max_length=10)
    stake_per_line = serializers.FloatField(min_value=5, max_value=50000)

    def validate_top_numbers(self, value):
        if len(set(value)) != len(value):
            raise CustomValidationError("Duplicate numbers are not allowed")
        return sorted(value)

    def validate_bottom_numbers(self, value):
        if len(set(value)) != len(value):
            raise CustomValidationError("Duplicate numbers are not allowed")
        return sorted(value)


class AgentDownLineCommissionGivingSerializer(serializers.Serializer):
    agent_id = serializers.IntegerField()
    commission = serializers.FloatField()
    commission_type = serializers.ChoiceField(choices=["percentage", "fixed"])
    commission_value = serializers.FloatField()
    start_date = serializers.DateField()
    end_date = serializers.DateField()


class AfricaLottoGameHistorySerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = AfricaLotto
        fields = "__all__"

    def to_representation(self, obj):
        serialized_data = super(AfricaLottoGameHistorySerializer, self).to_representation(obj)

        data = []

        batch = AfricaLottoBatch.objects.filter(id=serialized_data["batch"], draw_status=True).last()

        if batch:
            game_status = "lost"
            winning_status = False

            # check africa lotto winnings table
            if PosLotteryWinners().check_if_win_is_claimed_by_agent_phone_number(
                agent_phone=serialized_data["agent_phone_number"],
                game_id=serialized_data["game_play_id"],
            ):
                game_status = "claimed"
                winning_status = True

            data.append(
                {
                    "game_play_id": serialized_data["game_play_id"],
                    "status": game_status,
                    "stake_amount": serialized_data["purchase_amount"],
                    "number_of_ticket": serialized_data["number_of_ticket"],
                    "date": serialized_data["created_at"],
                    "won": winning_status,
                    "lost": False,
                }
            )
        else:
            data.append(
                {
                    "game_play_id": serialized_data["game_play_id"],
                    "status": "accepted",
                    "stake_amount": serialized_data["purchase_amount"],
                    "number_of_ticket": serialized_data["number_of_ticket"],
                    "date": serialized_data["created_at"],
                    "won": False,
                    "lost": False,
                }
            )

        return data[0]


class AfricaLottoDrawNumbersSerializers(serializers.ModelSerializer):
    class Meta:
        model = AfricaLottoDrawNumbers
        fields = "__all__"


class ManuallyDrawGhanaGamesSerializer(serializers.Serializer):
    batch_id = serializers.CharField()
    draw_number_db_id = serializers.IntegerField()
    game_type = serializers.ChoiceField(AfricaLottoGameType.choices)


class FetchDrawNumberUsingBatchNumberSerializer(serializers.Serializer):
    batch_number = serializers.CharField()



class TempPublishFireBallSocketSerializer(serializers.Serializer):
    fire_balls = serializers.CharField()



class TempUpdateKNowDrawNumbersSocketNotificationSerializer(serializers.Serializer):
    draw_number = serializers.CharField()
    machine_number = serializers.CharField()
    batch_number = serializers.CharField()


    
class TempUpdateKNowNextDrawSocketNotificationViewSerializer(serializers.Serializer):
    next_draw_time = serializers.DateTimeField()
    



class CheckAfricaLottoWinningNumberSerializer(serializers.Serializer):
    user_ticket_number = serializers.CharField()
    winning_numbers = serializers.CharField()
    lottery_type = serializers.ChoiceField(choices=AfricaLotteryType.choices, required=True)
    stake_per_line = serializers.FloatField()