from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

def send_fireball_notification(message="New Fireball event happened!"):
    """
    Send fireball notification to all connected clients
    
    Args:
        message (str): Message to send to clients
    """

    print(f"Sending fireball notification: {message}")
    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        "notifications_winwise_socket_notification",  # Must match self.room_group_name in consumer
        {
            "type": "fireball_notification",  # Must match the consumer method name
            "message": message,
        }
    )



def live_k_now_draw_number_socket_update(batch_number, data: dict):
    """
    Send fireball notification to all connected clients
    
    Args:
        message (str): Message to send to clients
    """
    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        "notifications_winwise_socket_notification",
        {
            "type": "live_draw_update",
            "message": data,
        }
    )


def update_next_k_now_draw(date_time_str):
    """
    Send fireball notification to all connected clients
    
    Args:
        message (str): Message to send to clients
    """
    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        "notifications_winwise_socket_notification",
        {
            "type": "soc_next_k_now_draw_time",
            "message": date_time_str,
        }
    )