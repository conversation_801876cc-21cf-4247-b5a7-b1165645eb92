etc/systemd/system - daphne.service




[Unit]
Description=Daphne Server
After=network.target

[Service]
Type=simple
KillMode=control-group
RemainAfterExit=yes
Restart=on-failure
WorkingDirectory=/home/<USER>/LibertyLotto/
ExecStart=/bin/sh -c '/home/<USER>/LibertyLotto/venv/bin/daphne  -b ************** -p 8001 liberty_lotto.asgi:application'
[Install]
WantedBy=multi-user.target




# Reload systemd daemon
sudo systemctl daemon-reload

# Enable the service
sudo systemctl enable daphne

# Start the service
sudo systemctl start daphne

# Check status
sudo systemctl status daphne


# View logs in real-time
sudo journalctl -u daphne -f


Make sure port 8001 is open in your firewall:

sudo ufw allow 8001

sudo systemctl restart daphne


etc/nginx/sites-available

location /ws/ {
    proxy_pass http://daphne_server;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Host $server_name;
    proxy_redirect off;
}


pip install drf-yasg --upgrade        
pip install djangorestframework --upgrade 

pip install channels-redis redis