import json
import logging
import uuid
from datetime import datetime, time, timedelta

import pytz
import requests
from celery import shared_task
from decouple import config
from django.conf import settings
from django.db.models import Sum

from africa_lotto.models import (
    AfricaLotteryType,
    AfricaLotto,
    AfricaLottoBatch,
    AfricaLottoChannel,
    AfricaLottoConstants,
    AfricaLottoDrawData,
    AfricaLottoDrawDataWinners,
    AfricaLottoDrawNumbers,
    AfricaLottoGameType,
    GhanaDrawNumberFetchedDataLog,
)
from africa_lotto.socket_utils import live_k_now_draw_number_socket_update
from main.helpers.open_ai_helper import OpenAiHelper
from main.models import UserProfile
from pos_app.models import (
    Agent,
    AgentWallet,
    AgentWalletTransaction,
    GamesDailyActivities,
    PosLotteryWinners,
)
from pos_app.pos_helpers import get_week_info, liberty_pay_vfd_account_enquiry
from wallet_system.models import Wallet

logger = logging.getLogger(__name__)


def should_batch_run(active_kenya_game_batch):
    """
    Checks if the active batch should be running based on the time difference
    between current time and next_draw_time.

    Args:
        game_type: The type of game to check

    Returns:
        tuple: (should_run: bool, batch: AfricaLottoBatch or None, message: str)
    """
    from datetime import datetime, timedelta

    import pytz
    from django.conf import settings

    active_kenya_game_batch: AfricaLottoBatch = active_kenya_game_batch

    # Use Kenya timezone
    timezone = pytz.timezone(settings.TIME_ZONE)
    current_datetime = datetime.now(tz=timezone)

    try:
        # If next_draw_time is stored as a string
        if isinstance(active_kenya_game_batch.next_draw_time, str):
            draw_time_obj = datetime.strptime(active_kenya_game_batch.next_draw_time, "%I:%M %p").time()
            draw_datetime = datetime.combine(current_datetime.date(), draw_time_obj)
            draw_datetime = timezone.localize(draw_datetime)
        else:

            draw_datetime = active_kenya_game_batch.next_draw_time
            if draw_datetime.tzinfo is None:
                draw_datetime = timezone.localize(draw_datetime)
    except (ValueError, AttributeError) as e:
        return False, active_kenya_game_batch, f"Error parsing next_draw_time: {e}"

    today_draw = draw_datetime
    tomorrow_draw = draw_datetime + timedelta(days=1)

    time_diff_today = (today_draw - current_datetime).total_seconds() / 60
    time_diff_tomorrow = (tomorrow_draw - current_datetime).total_seconds() / 60

    if time_diff_today >= -2:
        draw_datetime = today_draw
        time_difference = abs(time_diff_today)
        is_tomorrow = False
    else:
        draw_datetime = tomorrow_draw
        time_difference = abs(time_diff_tomorrow)
        is_tomorrow = True

    day_indicator = " (tomorrow)" if is_tomorrow else " (today)"

    if is_tomorrow is True:
        return False, active_kenya_game_batch, "Tommorrows game"

    if time_difference <= 60:
        return (
            True,
            active_kenya_game_batch,
            f"Batch should run{day_indicator} (time difference: {time_difference:.1f} minutes)",
        )
    elif time_difference > 60:
        return (
            False,
            active_kenya_game_batch,
            f"Batch should not run yet{day_indicator} (time difference: {time_difference:.1f} minutes)",
        )


@shared_task
def celery_africa_lotto_kenya_draw(
    game_type=AfricaLottoGameType.KENYA_LOTTO, create_new_batch=True, batch_db_id=None, draw_number_db_id=None
):

    timezone = pytz.timezone(settings.TIME_ZONE)
    current_datetime = datetime.now(tz=timezone)
    yesterday_datetime = current_datetime - timedelta(days=1)

    if batch_db_id is None:
        try:
            active_kenya_game_bacth = AfricaLottoBatch.objects.get(
                draw_status=False, batch_status=True, game_type=game_type
            )
        except AfricaLottoBatch.DoesNotExist:
            return "BATCH DOES NOT EXIST 1"

        # check if it's supposed to be running the batch at this time
        _status, _b, _message = should_batch_run(active_kenya_game_bacth)
        if _status is False:
            return f"_status: {_status} -----_b: {_b} ---- _message: {_message}"

        # check there's a batch with game type that exists with the same draw time
        if (
            len(
                AfricaLottoBatch.objects.filter(
                    game_type=active_kenya_game_bacth.game_type,
                    next_draw_time=active_kenya_game_bacth.next_draw_time,
                    created_at__date=active_kenya_game_bacth.created_at.date(),
                )
            )
            > 1
        ):
            return "THERE'S A BATCH THAT EXIST WITH THE SAME GAME NAME AND DRAW TIME"

    else:
        try:
            active_kenya_game_bacth = AfricaLottoBatch.objects.get(id=batch_db_id)
        except AfricaLottoBatch.DoesNotExist:
            return "BATCH DOES NOT EXIST 2"

    # if game_type == AfricaLottoGameType.KENYA_LOTTO:
    #     pass
    # else:
    #     active_kenya_game_bacth.draw_status = True
    #     active_kenya_game_bacth.batch_status = False
    #     active_kenya_game_bacth.save()

    active_kenya_game_bacth.draw_status = True
    active_kenya_game_bacth.batch_status = False
    active_kenya_game_bacth.save()

    if create_new_batch:
        AfricaLottoBatch.get_active_batch(game_type=game_type)

    batch_games = AfricaLotto.objects.filter(batch=active_kenya_game_bacth)

    # check if this batch is supposed to run today and decide if to allow the draw to use rtp
    should_use_rtp = True
    if active_kenya_game_bacth.created_at.date() != current_datetime.date():
        if active_kenya_game_bacth.game_type in [AfricaLottoGameType.K_NOW, AfricaLottoGameType.KENYA_30_LOTTO]:
            should_use_rtp = False
        elif active_kenya_game_bacth.game_type == AfricaLottoGameType.KENYA_LOTTO:
            if active_kenya_game_bacth.batch_name == "Barnabas":
                if active_kenya_game_bacth.created_at.date() != yesterday_datetime.date():
                    should_use_rtp = False
            else:
                should_use_rtp = False

    """
        {
            "nap": [
                [
                    53,
                    15
                ],
                **********.375711,
                364,
                503
            ]
        },


        {
            "perm-3": [
                [
                    32,
                    45,
                    30
                ],
                **********.375718,
                52,
                929
            ]
        },


        {
            "against": [
                [
                    [
                        67,
                        7,
                        43,
                        80,
                        18
                    ],
                    [
                        33,
                        38,
                        39,
                        10,
                        19,
                        20,
                        21
                    ]
                ],
                **********.375721,
                26,
                551
            ]
        },


        {
            "banker": [
                [
                    52
                ],
                **********.375724,
                993,
                591
            ]
        },
    """

    structured_tickets = []
    if game_type == AfricaLottoGameType.KENYA_30_LOTTO:
        kenya_running_balance = AfricaLottoConstants.objects.last().kenya30_running_balance
        first_draw_time = time(8, 15)
    elif game_type == AfricaLottoGameType.K_NOW:
        kenya_running_balance = AfricaLottoConstants.objects.last().k_now_running_balance
        first_draw_time = time(0, 0)
    else:
        kenya_running_balance = AfricaLottoConstants.objects.last().kenya_running_balance
        first_draw_time = time(9, 0)

    _timezone = pytz.timezone(settings.TIME_ZONE)
    current_datetime = datetime.now(tz=_timezone)
    current_time = current_datetime.time()

    current_minutes = current_time.hour * 60 + current_time.minute
    first_draw_minutes = first_draw_time.hour * 60 + first_draw_time.minute

    time_difference = current_minutes - first_draw_minutes

    if 0 <= time_difference <= 2:
        try:
            GamesDailyActivities.update_running_balance(game_type=game_type, running_balance=kenya_running_balance)
        except:
            pass

    # total_rtp = 0
    # total_rtp__fallback = 0

    total_rtp = batch_games.aggregate(Sum("effective_rtp")).get("effective_rtp__sum") or 0
    total_rtp__fallback = batch_games.aggregate(Sum("rtp")).get("rtp__sum") or 0

    double_chance_picks_list = []

    for game in batch_games:

        # total_rtp += game.effective_rtp
        # total_rtp__fallback += game.rtp

        if game.double_chance is True:
            double_chance_picks = game.lucky_number.split(",")
            double_chance_picks = [int(pick) for pick in double_chance_picks]
            double_chance_picks_list.extend(double_chance_picks)

            if game.bottom_ticket:
                bottom_ticket = game.bottom_ticket.split(",")
                bottom_ticket = [int(pick) for pick in bottom_ticket]
                double_chance_picks_list.extend(bottom_ticket)

        user_picks = game.lucky_number.split(",")
        user_picks = [int(pick) for pick in user_picks]

        stake_per_line = game.stake_per_line
        purchase_amount = game.purchase_amount

        if game.double_chance is True:
            try:
                stake_per_line = stake_per_line / 2
            except ZeroDivisionError:
                stake_per_line = 0

            purchase_amount = purchase_amount / 2

        if game.lottery_type in [
            AfricaLotteryType.NAP2,
            AfricaLotteryType.NAP3,
            AfricaLotteryType.NAP4,
            AfricaLotteryType.NAP5,
        ]:

            structured_tickets.append(
                {
                    "nap": [user_picks, f"{game.game_play_id}&{game.id}", stake_per_line, purchase_amount],
                }
            )

        if game.lottery_type == AfricaLotteryType.PERM2:
            structured_tickets.append(
                {
                    "perm-2": [user_picks, f"{game.game_play_id}&{game.id}", stake_per_line, purchase_amount],
                }
            )
        if game.lottery_type == AfricaLotteryType.PERM3:
            structured_tickets.append(
                {
                    "perm-3": [user_picks, f"{game.game_play_id}&{game.id}", stake_per_line, purchase_amount],
                }
            )

        if game.lottery_type == AfricaLotteryType.PERM4:
            structured_tickets.append(
                {
                    "perm-4": [user_picks, f"{game.game_play_id}&{game.id}", stake_per_line, purchase_amount],
                }
            )

        if game.lottery_type == AfricaLotteryType.PERM5:
            structured_tickets.append(
                {
                    "perm-5": [user_picks, f"{game.game_play_id}&{game.id}", stake_per_line, purchase_amount],
                }
            )

        if game.lottery_type == AfricaLotteryType.AGAINST:
            if game.bottom_ticket is None:
                continue

            bottom_ticket = game.bottom_ticket.split(",")
            bottom_ticket = [int(pick) for pick in bottom_ticket]

            structured_tickets.append(
                {
                    "against": [
                        [user_picks, bottom_ticket],
                        f"{game.game_play_id}&{game.id}",
                        stake_per_line,
                        purchase_amount,
                    ],
                }
            )

        if game.lottery_type == AfricaLotteryType.BANKER:
            structured_tickets.append(
                {
                    "banker": [user_picks, f"{game.game_play_id}&{game.id}", stake_per_line, purchase_amount],
                }
            )

    topup = 0

    # raw_rtp = total_rtp or total_rtp__fallback
    raw_rtp = total_rtp__fallback

    topup = AfricaLottoConstants.top_up_kenya_rtp(raw_rtp)
    if game_type == AfricaLottoGameType.KENYA_30_LOTTO:
        topup = AfricaLottoConstants.top_up_kenya30_rtp(raw_rtp)
    elif game_type == AfricaLottoGameType.K_NOW:
        topup = AfricaLottoConstants.top_up_k_now_rtp(raw_rtp)

    total_rtp = raw_rtp + topup + kenya_running_balance

    # Get the boost amount based on game type
    if game_type == AfricaLottoGameType.KENYA_30_LOTTO:
        boost_amount = topup
        running_balance = kenya_running_balance
    else:
        boost_amount = topup
        running_balance = kenya_running_balance

    if should_use_rtp is False:
        total_rtp = 0
        raw_rtp = 0
        running_balance = 0
        boost_amount = 0
        topup = 0

    draw_data_instance = AfricaLottoDrawData.objects.create(
        batch=active_kenya_game_bacth.batch_uuid,
        game_type=game_type,
        game_plays=structured_tickets,
        rtp=total_rtp,
        raw_rtp=raw_rtp,
        running_balance=running_balance,
        boost_amount=boost_amount,
        topup_amount=topup,
        double_chance_picks_list=double_chance_picks_list,
        re_running_previous_day_draw=True,
    )

    # if game_type == AfricaLottoGameType.KENYA_30_LOTTO:
    #     return

    draw_response = kenya_draw_simulation(
        total_rtp, structured_tickets, game_type=game_type, double_chance_picks=double_chance_picks_list
    )

    draw_data_instance.draw_response = draw_response
    draw_data_instance.save()

    if isinstance(draw_response, dict):
        todays_date = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        day_of_week = todays_date.strftime("%A")

        # draw_date = f"{day_of_week} {todays_date.strftime('%Y-%m-%d %H:%M:%S')}"

        # draw_date = f"{day_of_week} {todays_date.strftime('%Y-%m-%d %I:%M:%S %p')}"

        draw_date = f"{day_of_week} {todays_date.strftime('%Y-%m-%d')} {active_kenya_game_bacth.next_draw_time}"

        strings_combo = ",".join([str(number) for number in draw_response["best_combo"]])

        double_chance_picks_best_combo = draw_response.get("double_chance", {}).get("best_combo", [])
        double_chance_picks = ",".join([str(number) for number in double_chance_picks_best_combo])

        double_chance_winners = draw_response.get("double_chance", {}).get("winners", [])

        if draw_number_db_id is None:

            # try:
            #     dt = datetime.strptime(draw_date, "%A %Y-%m-%d %I:%M:%S %p")
            # except:
            #     try:
            #         dt = datetime.strptime(draw_date, "%A %Y-%m-%d %I:%M %p")
            #     except:
            #         dt = None

            # if dt is not None:

            #     draw_time_string_result = dt.strftime("%A %Y-%m-%d %H:%M")

            #     if AfricaLottoDrawNumbers.objects.filter(draw_date__icontains=draw_time_string_result).exists():
            #         return "DRAW NUMBER ALREADY EXISTS -- " + draw_time_string_result

            AfricaLottoDrawNumbers.objects.create(
                game_type=game_type,
                draw_date=draw_date,
                draw_name=active_kenya_game_bacth.batch_name,
                draw_number=strings_combo,
                batch_date=active_kenya_game_bacth.created_at,
                batch_id=active_kenya_game_bacth.batch_uuid,
                machine_number=double_chance_picks,
                batch_number=active_kenya_game_bacth.batch_number,
            )
        else:
            AfricaLottoDrawNumbers.objects.filter(id=draw_number_db_id).update(
                draw_date=draw_date,
                draw_name=active_kenya_game_bacth.batch_name,
                draw_number=strings_combo,
                batch_date=active_kenya_game_bacth.created_at,
                batch_id=active_kenya_game_bacth.batch_uuid,
                machine_number=double_chance_picks,
            )

        winners_data = process_winner_data(draw_response["winners"], draw_data_model_instance=draw_data_instance)

        winners_data = process_winner_data(double_chance_winners, draw_data_model_instance=draw_data_instance)

        if game_type == AfricaLottoGameType.K_NOW:
            k_now_data = {
                "draw_number": strings_combo,
                "machine_number": double_chance_picks,
                "batch_number": active_kenya_game_bacth.batch_number,
            }

            live_k_now_draw_number_socket_update(active_kenya_game_bacth.batch_number, k_now_data)

    else:
        winners_data = {}

    try:
        double_chance_winnings = draw_response.get("double_chance", {}).get("total_winnings", 0)
    except:
        double_chance_winnings = 0

    if game_type == AfricaLottoGameType.KENYA_30_LOTTO:
        try:
            AfricaLottoConstants.objects.update(
                kenya30_running_balance=total_rtp - (draw_response["total_winnings"] + double_chance_winnings)
            )
        except Exception as e:
            logger.error(str(e))
    elif game_type == AfricaLottoGameType.K_NOW:
        try:
            AfricaLottoConstants.objects.update(
                k_now_running_balance=total_rtp - (draw_response["total_winnings"] + double_chance_winnings)
            )
        except Exception as e:
            logger.error(str(e))

    else:
        try:
            AfricaLottoConstants.objects.update(
                kenya_running_balance=total_rtp - (draw_response["total_winnings"] + double_chance_winnings)
            )
        except Exception as e:
            logger.error(str(e))

    return winners_data


@shared_task
def celery_filter_africa_lotto_game_winners(winning_number, game_plays):
    urls = f"{config('AFRICA_LOTTO_DRAW_SERVER_IP')}/filter_wins"

    data = {
        "combo": winning_number,
        "tickets": game_plays,
    }

    response = requests.post(urls, json=data)

    try:
        return response.json()
    except Exception:
        return response.text


def kenya_draw_simulation(
    rtp, game_plays, game_type=AfricaLottoGameType.KENYA_LOTTO, double_chance=True, double_chance_picks: list = []
):

    urls = f"{config('AFRICA_LOTTO_DRAW_SERVER_IP')}/simulate"

    data = {
        "rtp": rtp,
        "double_chance": double_chance,
        "double_chance_picks": double_chance_picks,
        "lottery_type": "normal",
        "tickets": game_plays,
    }

    if game_type in [AfricaLottoGameType.KENYA_30_LOTTO, AfricaLottoGameType.K_NOW]:
        data["lottery_type"] = "k_30"

    response = requests.post(urls, json=data)

    """
        SAMPLE RESPONSE

        {
            "best_combo": [
                27,
                7,
                10,
                86,
                87
            ],
            "number_of_winners": 72,
            "total_winnings": 9521280,
            "winners": []
        }

    """

    try:
        return response.json()
    except Exception as e:
        print(str(e))


def filter_africa_lotto_winnders_data(draw_db_id, batch_id, game_type=AfricaLottoGameType.GHANA_LOTTO, add_topup=True):

    todays_ghana_draw_number = AfricaLottoDrawNumbers.objects.get(id=draw_db_id)

    active_kenya_game_bacth = AfricaLottoBatch.objects.get(id=batch_id)

    active_kenya_game_bacth.draw_status = True
    active_kenya_game_bacth.batch_status = False
    active_kenya_game_bacth.save()

    batch_games = AfricaLotto.objects.filter(
        batch=active_kenya_game_bacth, game_type=game_type, exempted_from_draw=False, double_chance=False
    )

    draw_numbers = str(todays_ghana_draw_number.draw_number).split(",")
    draw_numbers = [int(number) for number in draw_numbers]

    machine_numbers = []
    if todays_ghana_draw_number.machine_number is not None:
        machine_numbers = str(todays_ghana_draw_number.machine_number).split(",")
        machine_numbers = [int(number) for number in machine_numbers]

    double_batch_games = AfricaLotto.objects.filter(
        batch=active_kenya_game_bacth, game_type=game_type, exempted_from_draw=False, double_chance=True
    )

    structured_tickets = []

    for game in batch_games:

        user_picks = game.lucky_number.split(",")
        user_picks = [int(pick) for pick in user_picks]

        if game.lottery_type in [
            AfricaLotteryType.NAP2,
            AfricaLotteryType.NAP3,
            AfricaLotteryType.NAP4,
            AfricaLotteryType.NAP5,
        ]:
            structured_tickets.append(
                {
                    "nap": [user_picks, f"{game.game_play_id}&{game.id}", game.stake_per_line, game.purchase_amount],
                }
            )

        if game.lottery_type == AfricaLotteryType.PERM2:
            structured_tickets.append(
                {
                    "perm-2": [
                        user_picks,
                        f"{game.game_play_id}&{game.id}",
                        game.stake_per_line,
                        game.purchase_amount,
                    ],
                }
            )
        if game.lottery_type == AfricaLotteryType.PERM3:
            structured_tickets.append(
                {
                    "perm-3": [
                        user_picks,
                        f"{game.game_play_id}&{game.id}",
                        game.stake_per_line,
                        game.purchase_amount,
                    ],
                }
            )

        if game.lottery_type == AfricaLotteryType.PERM4:
            structured_tickets.append(
                {
                    "perm-4": [
                        user_picks,
                        f"{game.game_play_id}&{game.id}",
                        game.stake_per_line,
                        game.purchase_amount,
                    ],
                }
            )

        if game.lottery_type == AfricaLotteryType.PERM5:
            structured_tickets.append(
                {
                    "perm-5": [
                        user_picks,
                        f"{game.game_play_id}&{game.id}",
                        game.stake_per_line,
                        game.purchase_amount,
                    ],
                }
            )

        if game.lottery_type == AfricaLotteryType.AGAINST:
            if game.bottom_ticket is None:
                continue

            bottom_ticket = game.bottom_ticket.split(",")
            bottom_ticket = [int(pick) for pick in bottom_ticket]

            structured_tickets.append(
                {
                    "against": [
                        [user_picks, bottom_ticket],
                        f"{game.game_play_id}&{game.id}",
                        game.stake_per_line,
                        game.purchase_amount,
                    ],
                }
            )

        if game.lottery_type == AfricaLotteryType.BANKER:
            structured_tickets.append(
                {
                    "banker": [
                        user_picks,
                        f"{game.game_play_id}&{game.id}",
                        game.stake_per_line,
                        game.purchase_amount,
                    ],
                }
            )

    print("STRUCTURED_TICKETS>>>>>>>>>", structured_tickets, "\n\n\n\n")
    # For Ghana Lotto, set running balance, boost amount, and topup amount to 0
    draw_data_instance = AfricaLottoDrawData.objects.create(
        batch=active_kenya_game_bacth.batch_uuid,
        game_type=game_type,
        game_plays=structured_tickets,
        running_balance=0,
        boost_amount=0,
        topup_amount=0,
    )

    process_africa_lotto_winners(
        draw_numbers, structured_tickets, draw_data_instance, batchdb_id=batch_id, add_topup=add_topup
    )

    if len(double_batch_games) > 0:

        structured_tickets = []

        for game in double_batch_games:

            user_picks = game.lucky_number.split(",")
            user_picks = [int(pick) for pick in user_picks]

            if game.lottery_type in [
                AfricaLotteryType.NAP2,
                AfricaLotteryType.NAP3,
                AfricaLotteryType.NAP4,
                AfricaLotteryType.NAP5,
            ]:
                structured_tickets.append(
                    {
                        "nap": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            if game.lottery_type == AfricaLotteryType.PERM2:
                structured_tickets.append(
                    {
                        "perm-2": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )
            if game.lottery_type == AfricaLotteryType.PERM3:
                structured_tickets.append(
                    {
                        "perm-3": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            if game.lottery_type == AfricaLotteryType.PERM4:
                structured_tickets.append(
                    {
                        "perm-4": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            if game.lottery_type == AfricaLotteryType.PERM5:
                structured_tickets.append(
                    {
                        "perm-5": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            if game.lottery_type == AfricaLotteryType.AGAINST:
                if game.bottom_ticket is None:
                    continue

                bottom_ticket = game.bottom_ticket.split(",")
                bottom_ticket = [int(pick) for pick in bottom_ticket]

                structured_tickets.append(
                    {
                        "against": [
                            [user_picks, bottom_ticket],
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            if game.lottery_type == AfricaLotteryType.BANKER:
                structured_tickets.append(
                    {
                        "banker": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            draw_data_instance = AfricaLottoDrawData.objects.create(
                batch=active_kenya_game_bacth.batch_uuid,
                game_type=game_type,
                game_plays=structured_tickets,
                running_balance=0,
                boost_amount=0,
                topup_amount=0,
            )

        process_africa_lotto_winners(
            machine_numbers,
            structured_tickets,
            draw_data_instance,
            batchdb_id=batch_id,
            is_double_chance=True,
            add_topup=add_topup,
        )


def process_africa_lotto_winners(
    draw_numbers,
    structured_tickets,
    draw_data_instance,
    batchdb_id=None,
    game_type=None,
    is_double_chance=False,
    add_topup=True,
):
    """
    Process winners for Africa Lotto game and save results.

    Args:
        draw_numbers: The drawn lottery numbers
        structured_tickets: Structured ticket data
        draw_data_instance: Instance to save filtered winners to
    """
    # Get filtered winners
    filter_winners = celery_filter_africa_lotto_game_winners(draw_numbers, structured_tickets)
    print("filter_winners", filter_winners, "\n\n")

    # Save filtered winners to draw data instance
    try:
        draw_data_instance.filtered_winners = filter_winners
        draw_data_instance.save()
    except:  # noqa
        pass

    # if game_type == AfricaLottoGameType.KENYA_LOTTO:
    #     return

    # Process winners if we have valid data
    if not isinstance(filter_winners, dict):
        print("Invalid filter_winners format: not a dictionary")
        return

    winners = filter_winners.get("winners", [])
    if not isinstance(winners, list):
        print("Invalid winners format: not a list")
        return

    process_winner_data(
        winners, batchdb_id=batchdb_id, game_type=game_type, is_double_chance=is_double_chance, add_topup=add_topup
    )

    return winners


def process_winner_data(
    winners, batchdb_id=None, game_type=None, draw_data_model_instance=None, is_double_chance=False, add_topup=True
):
    """Process the winner data and create winner records."""

    game_ids = []
    total_winnings = 0
    for first_level in winners:
        if not isinstance(first_level, list):
            continue

        for second_level in first_level:
            if not isinstance(second_level, list):
                continue

            amount_won = second_level[-1]  # The amount won is the last item in the list

            # if game_type == AfricaLottoGameType.KENYA_30_LOTTO:
            #     amount_won = amount_won / 2

            for item in second_level:
                print("NOT A DICT", item)
                if not isinstance(item, dict):
                    continue

                # try:
                #     create_winner_record(item, amount_won)
                # except Exception as e:
                #     print(f"Error processing winner: {e}")

                if is_double_chance is True:
                    amount_won = amount_won / 2

                create_winner_record(item, amount_won, add_topup=add_topup)
                print("WINNER", item, amount_won)
                total_winnings += amount_won

                keys = list(item.keys())
                winning_item = item[keys[0]]
                if not winning_item:
                    continue

                game_id = winning_item[1]
                game_parts = game_id.split("&")
                if len(game_parts) < 2:
                    continue

                game_play_id = game_parts[0]
                game_ids.append(game_play_id)

    if draw_data_model_instance is not None:
        if isinstance(draw_data_model_instance, AfricaLottoDrawData):
            aggregated_pos_lottery_winners = PosLotteryWinners.objects.filter(game_id__in=game_ids).aggregate(
                Sum("amount_won")
            )
            aggregated_pos_lottery_winners = aggregated_pos_lottery_winners.get("amount_won__sum") or 0

            winning_data_report = {
                "total_winnings": total_winnings,
                "aggregated_pos_lottery_winnings": aggregated_pos_lottery_winners,
            }

            if draw_data_model_instance.winning_data_report:
                old_winning = (
                    f"{draw_data_model_instance.winning_data_report}\n\n{draw_data_model_instance.winning_data_report}"
                )
                draw_data_model_instance.winning_data_report = old_winning
                draw_data_model_instance.save()
            else:
                draw_data_model_instance.winning_data_report = json.dumps(winning_data_report)
                draw_data_model_instance.save()

    try:
        batch_instance = AfricaLottoBatch.objects.get(id=batchdb_id)
    except AfricaLottoBatch.DoesNotExist:
        return

    if batch_instance.game_type == AfricaLottoGameType.GHANA_LOTTO:
        total_purchase_amount = (
            AfricaLotto.objects.filter(batch__id=batchdb_id)
            .aggregate(Sum("purchase_amount"))
            .get("purchase_amount__sum")
            or 0
        )

        rto = total_purchase_amount - total_winnings
        if rto > 0:
            Wallet.fund_wallet(wallet_type="GHANA_RTO_WALLET", amount=rto, game_type="GHANA_LOTTO")

        # move total winnings to rtp wallet
        if total_winnings > 0:

            vfd_enquiries = liberty_pay_vfd_account_enquiry(source="GHANA_RTP_WALLET")
            if isinstance(vfd_enquiries, dict):
                vfd_enquiries = vfd_enquiries.get("available_balance", 0)
                amount = vfd_enquiries

                if total_winnings > amount:
                    total_winnings = amount

            Wallet.fund_wallet(wallet_type="MOVEABLE_GHANA_RTP_WALLET", amount=total_winnings, game_type="GHANA_LOTTO")


def create_winner_record(item, amount_won, add_topup=True):
    """Create a winner record from the winner item data."""
    keys = list(item.keys())
    if not keys:
        return

    winning_item = item[keys[0]]
    if not winning_item:
        return

    game_id = winning_item[1]
    game_parts = game_id.split("&")
    if len(game_parts) < 2:
        print(f"Invalid game_id format: {game_id}")
        return

    game_play_id = game_parts[0]
    instance_id = game_parts[-1]

    # Get ticket instance
    try:
        game_ticket_instance = AfricaLotto.objects.get(game_play_id=game_play_id, id=instance_id, won=False)
    except AfricaLotto.DoesNotExist:
        print(f"Ticket not found: game_play_id={game_play_id}, id={instance_id}")
        return

    # Mark ticket as won
    game_ticket_instance.won = True

    # Prepare winner data
    winners_payload = {
        "user_phone_number": game_ticket_instance.user_phone_number,
        "user_name": game_ticket_instance.user_name,
        "agent_phone_number": game_ticket_instance.agent_phone_number,
        "agent_name": game_ticket_instance.agent_name,
        "batch": game_ticket_instance.batch.batch_uuid,
        "amount_won": amount_won,
        "game_play_id": game_id,
        "game_pin": game_ticket_instance.game_pin,
        "lucky_number": game_ticket_instance.lucky_number,
        "bottom_ticket": game_ticket_instance.bottom_ticket,
        "channel": game_ticket_instance.channel,
        "lottery_type": game_ticket_instance.lottery_type,
        "game_type": game_ticket_instance.game_type,
    }

    # Create winner record
    AfricaLottoDrawDataWinners.create_winner(**winners_payload)
    game_ticket_instance.save()

    # Handle POS agent channel
    if game_ticket_instance.channel == AfricaLottoChannel.POS_AGENT:
        handle_pos_agent_winners(game_ticket_instance, amount_won, add_topup=add_topup)


def handle_pos_agent_winners(game_ticket_instance, amount_won, add_topup=True):
    """Handle POS agent channel winners."""
    try:
        agent_profile_instance = Agent.objects.get(phone=game_ticket_instance.agent_phone_number)
        user_profile = UserProfile.objects.get(phone_number=game_ticket_instance.agent_phone_number)

        if add_topup == True:
            existing_instance = PosLotteryWinners.objects.filter(
                agent=agent_profile_instance,
                player=user_profile,
                game_id=game_ticket_instance.game_play_id,
                lottery_type=game_ticket_instance.game_type,
                pin=game_ticket_instance.game_pin,
                is_win_claimed=False,
            ).first()
            if existing_instance:

                transaction_reference = f"{game_ticket_instance.game_play_id}-{uuid.uuid4()}"

                agent_wallet = AgentWallet.objects.get(agent=agent_profile_instance)

                AgentWalletTransaction.create_record(
                    transaction_reference=transaction_reference,
                    unique_transaction_reference=transaction_reference,
                    agent_wallet=agent_wallet,
                    transaction_type="CREDIT",
                    game_type=existing_instance.lottery_type,
                    game_play_id=existing_instance.game_id,
                    status="SUCCESSFUL",
                    phone_number=agent_profile_instance.phone,
                    transaction_from="WINNINGS",
                    agent_phone_number=agent_profile_instance.phone,
                    agent_email=agent_profile_instance.email,
                    type_of_agent=(
                        "LOTTO_AGENT" if agent_profile_instance.agent_type == "LOTTO_AGENT" else "OTHER_AGENTS"
                    ),
                    agent_name=agent_profile_instance.full_name,
                    amount=amount_won,
                )

                existing_instance.amount_won += amount_won
                existing_instance.save()

                from_lotto_agent = False
                if existing_instance.agent.agent_type == "LOTTO_AGENT":
                    from_lotto_agent = True

                GamesDailyActivities.create_record(
                    game_type=existing_instance.lottery_type,
                    winnings=amount_won,
                    from_lotto_agent=from_lotto_agent,
                    game_id=existing_instance.game_id,
                )

                return

        # Create a new winner record

        PosLotteryWinners().create_winners(
            agent=agent_profile_instance,
            player=user_profile,
            game_id=game_ticket_instance.game_play_id,
            amount_won=amount_won,
            win_flavour="WHITE",
            lottery_type=game_ticket_instance.game_type,
            pin=game_ticket_instance.game_pin,
        )
    except (Agent.DoesNotExist, UserProfile.DoesNotExist) as e:
        print(f"Error handling POS agent winners: {e}")


# @shared_task()
def get_ghana_winnings_from_590_mobile():
    from africa_lotto.models import (
        AfricaLottoBatch,
        AfricaLottoDrawNumbers,
        AfricaLottoGameType,
    )

    today = datetime.today()
    this_date = str(today.date())
    this_day = today.day

    # Check if Draw Data is created

    try:
        url = "https://www.590mobile.com.gh/gameplay.php"
        payload = json.dumps({"action": "getActiveResults"})
        headers = {"Content-Type": "application/json"}
        result = requests.request("GET", url, headers=headers, data=payload)
        print("Ghana Lotto Results: ", result.text)
        if result.status_code == 200:
            response = result.json()
            yesterday = today - timedelta(days=1)
            draw_list = response["draws"]
            for draw in draw_list:
                draw_name = draw.get("drawType")
                draw_date = draw.get("resultDate")
                winnings = draw.get("winningNumbers").get("original")

                # Get previous day created batch
                lotto_batch = AfricaLottoBatch.objects.filter(
                    game_type=AfricaLottoGameType.GHANA_LOTTO,
                    draw_status=False,
                    batch_status=False,
                    batch_name__iexact=draw_name,
                ).last()
                if not lotto_batch:
                    break
                else:
                    try:
                        AfricaLottoDrawNumbers.objects.get(draw_name=lotto_batch.batch_name, game_batch=lotto_batch)
                        break
                    except AfricaLottoDrawNumbers.DoesNotExist:
                        pass
                    except AfricaLottoDrawNumbers.MultipleObjectsReturned:
                        break

                    # # save log from website data and open ai response
                    # week_range = get_week_info(lotto_batch.created_at)

                    # open_ai_prompt = f"""is this ghana draw number {winnings} for {lotto_batch.batch_name} for this week {week_range}
                    # response: {{
                    #     "is_this_week_and_it_correct": bool(if the draw number is for this week),
                    #     "is_this_draw": boolif the draw number is for this week),
                    # }}

                    # NOTE: must return data in json, using the above response sample
                    # """

                    # fetch_data_model_instance = GhanaDrawNumberFetchedDataLog.objects.create(
                    #     fetched_data_log=draw_list,
                    #     open_ai_prompt = open_ai_prompt
                    # )

                    # open_ai_response_data = OpenAiHelper.prompt_open_ai(open_ai_prompt)

                    # fetch_data_model_instance.open_ai_response

                    # msg = open_ai_response_data.get("message")

                    # if not isinstance(msg, dict):
                    #     return

                    # msg_msg = msg.get("is_this_week_and_it_correct", False)
                    # if not isinstance(msg_msg, bool):
                    #     return

                    # if msg_msg == False:
                    #     return

                    lotto, _ = AfricaLottoDrawNumbers.objects.get_or_create(
                        game_type="GHANA_LOTTO", draw_date=this_date, game_batch=lotto_batch
                    )

                    lotto.draw_date = this_date
                    lotto.draw_number = winnings
                    lotto.draw_name = lotto_batch.batch_name
                    lotto.game_batch = lotto_batch

                    lotto.manually_filtered_winnings = True
                    lotto.save()

                    # Filter Winners
                    filter_africa_lotto_winnders_data(
                        draw_db_id=lotto.id,
                        batch_id=lotto_batch.id,
                        game_type=AfricaLottoGameType.GHANA_LOTTO,
                        add_topup=True,
                    )
                    break
        else:
            print("Unable to fetch winnings")
            pass
    except requests.exceptions.RequestException as err:
        print("Error occurred while fetching winnings: ", err)
        pass

    return True
