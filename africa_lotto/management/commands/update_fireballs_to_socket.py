
from africa_lotto.socket_utils import send_fireball_notification
from main.helpers.redis_storage import RedisStorage
from django.core.management.base import BaseCommand
from decouple import config
import random

class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        print(f"Sending fireball notification FIRST ATTEMPT")

        # save the result in redis
        redis_db = RedisStorage(redis_key="redis_saved_fireballs")

        fireballs = redis_db.get_data()

        print(str(config("ENVIRONMENT")))

        print("fireballs", fireballs)

        if fireballs is None:
            # if not str(config("ENVIRONMENT")).startswith("pro"):
            combos = ["3,6,7", "90,1,4", "2,48,44", "44,20,15"]

            # randomly_selected = random.choices(combos)
            randomly_selected = random.choices(combos)[0]
            randomly_selected = randomly_selected.split(",")
            send_fireball_notification(random.choices(randomly_selected))

        else:
            fireballs = fireballs.decode("utf-8")
            if str(fireballs).lower() == "none":
                
                redis_db.delete_data()
                
                combos = ["3,6,7", "90,1,4", "2,48,44", "44,20,15"]
                randomly_selected = random.choices(combos)[0]
                randomly_selected = randomly_selected.split(",")
                send_fireball_notification(random.choices(combos))
            else:
                fireballs = fireballs.split(",")
                send_fireball_notification(fireballs)

        self.stdout.write(self.style.SUCCESS(f"Sending fireball notification"))

