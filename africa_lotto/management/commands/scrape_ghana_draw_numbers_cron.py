from django.core.management.base import BaseCommand

from africa_lotto.helpers import scrape_ghana_draw_numbers
from datetime import datetime

from africa_lotto.models import AfricaLottoDrawNumbers, AfricaLottoGameType



class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        today = datetime.now().date()
        if AfricaLottoDrawNumbers.objects.filter(created_at__date = today, game_type=AfricaLottoGameType.GHANA_LOTTO).exists():
            pass
        else:
            scrape_ghana_draw_numbers()