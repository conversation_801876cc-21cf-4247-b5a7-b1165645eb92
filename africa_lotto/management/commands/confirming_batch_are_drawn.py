from django.core.management.base import BaseCommand
from django.utils import timezone

from africa_lotto.models import AfricaLottoBatch, AfricaLottoDrawNumbers
from africa_lotto.tasks import celery_africa_lotto_kenya_draw


class Command(BaseCommand):

    def handle(self, *args, **kwargs):

        current_date = timezone.now().date()

        batches = AfricaLottoBatch.objects.filter(
            created_at__date=current_date,
            batch_status=False,
            draw_status=True,
            game_type__in=["KENYA_LOTTO", "KENYA_30_LOTTO", "K_NOW"],
        ).order_by("-id")

        for batch in batches:

            if AfricaLottoDrawNumbers.objects.filter(batch_id=batch.batch_uuid).exists():
                continue

            res = celery_africa_lotto_kenya_draw(
                game_type=batch.game_type, batch_db_id=batch.id, create_new_batch=False
            )

            print(f"Draw for batch {batch.id} with game type {batch.game_type} has been processed: {res}")
