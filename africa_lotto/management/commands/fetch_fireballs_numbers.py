from django.core.management.base import BaseCommand

from africa_lotto.models import AfricaLottoDrawNumbers, AfricaLottoGameType
from africa_lotto.socket_utils import send_fireball_notification
from collections import Counter

from main.helpers.redis_storage import RedisStorage



class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        k_now_draw_number_queryset = AfricaLottoDrawNumbers.objects.filter(
            game_type=AfricaLottoGameType.K_NOW
        ).distinct("batch_id").order_by("batch_id", "-id")[:40]

        draw_numbers_list = k_now_draw_number_queryset.values_list('draw_number', flat=True)

        all_numbers = []
        for draw_numbers in draw_numbers_list:
            if draw_numbers:
                all_numbers.extend(draw_numbers.split(','))


        # print("all_numbers", all_numbers, "\n\n\n")
        
        all_numbers = [num.strip() for num in all_numbers if num.strip().isdigit()]
        # print("All numbers:", all_numbers)
        counter = Counter(all_numbers)

        top_3 = counter.most_common(3)

        # print("Top 3 numbers:", top_3)

        if len(top_3) > 0:
            result = ",".join(item[0] for item in top_3)
        else:
            result = None


        # send_fireball_notification(result)

        # save the result in redis
        redis_db = RedisStorage(redis_key="redis_saved_fireballs")
        redis_db.set_data(str(result))

        self.stdout.write(self.style.SUCCESS('Successfully sent fireball notification'))
        