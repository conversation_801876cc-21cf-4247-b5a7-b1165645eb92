from django.core.management.base import BaseCommand
from datetime import datetime
import pytz
from django.conf import settings

from account.authentication import KenyaLottoDrawPermission

class Command(BaseCommand):
    help = "Check if draw time has closed and create a new batch"

    def handle(self, *args, **kwargs):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_time = datetime.now(tz=timezone).strftime("%I:%M %p")
        permission = KenyaLottoDrawPermission()
        permission.is_draw_closed(current_time)
        self.stdout.write(self.style.SUCCESS("Checked draw time and updated batch if necessary."))


