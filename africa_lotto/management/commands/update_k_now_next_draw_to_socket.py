from django.core.management.base import BaseCommand
from africa_lotto.models import AfricaLottoBatch, AfricaLottoGameType
from africa_lotto.socket_utils import update_next_k_now_draw
from africa_lotto.tasks import celery_africa_lotto_kenya_draw
from datetime import timedelta

class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        active_k_now_batch = AfricaLottoBatch.objects.filter(game_type = AfricaLottoGameType.K_NOW, batch_status = True)
        if active_k_now_batch:
            batch = active_k_now_batch.first()

            new_datetime = batch.created_at + timedelta(minutes=5)
            # Format the datetime as requested
            formatted_datetime = new_datetime.strftime("%Y-%m-%dT%H:%M:%SZ")

            update_next_k_now_draw(str(formatted_datetime))
