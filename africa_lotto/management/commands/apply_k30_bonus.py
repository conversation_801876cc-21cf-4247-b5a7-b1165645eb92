from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import models
from africa_lotto.models import BonusCalendarEntry, AfricaLottoGameType, AfricaLottoConstants
import logging
import random

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Check for due bonuses and apply them to K30 running balance"

    def handle(self, *args, **kwargs):
        """
        Check for all active non-delivered bonuses for today and apply them
        to the K30 running balance.
        """
        current_date = timezone.now().date()

        # Get all pending bonuses for K30 for today
        pending_bonuses = BonusCalendarEntry.objects.filter(
            date=current_date,
            game_type=AfricaLottoGameType.KENYA_30_LOTTO,
            delivered=False
        )

        if not pending_bonuses.exists():
            self.stdout.write(
                self.style.WARNING(
                    f"No pending bonuses found for K30 on {current_date}"
                )
            )
            return

        total_bonus_applied = 0
        bonuses_applied = 0
        bonuses_skipped = 0

        # Check if current time is 20:00 (8 PM) - force delivery
        current_hour = timezone.now().hour
        force_delivery = current_hour == 15

        # Apply each pending bonus with random delivery (4-to-1 chance, 20% probability)
        # Unless it's 8 PM, then force delivery
        for bonus in pending_bonuses:
            should_deliver = False

            if force_delivery:
                should_deliver = True
                delivery_reason = "forced delivery at 8 PM"
            else:
                # Random delivery decision: 1 in 5 chance (20% probability)
                delivery_chance = random.randint(1, 4)
                should_deliver = delivery_chance == 1
                delivery_reason = "randomly selected for delivery"

            if should_deliver:
                try:
                    # Add bonus amount to K30 running balance
                    AfricaLottoConstants.objects.all().update(
                        kenya30_running_balance=models.F("kenya30_running_balance") + bonus.amount
                    )

                    # Mark bonus as delivered
                    bonus.delivered = True
                    bonus.delivered_at = timezone.now()
                    bonus.save()

                    total_bonus_applied += bonus.amount
                    bonuses_applied += 1

                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Applied bonus of {bonus.amount} for {bonus.date} ({delivery_reason})"
                        )
                    )

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Error applying bonus {bonus.id}: {str(e)}"
                        )
                    )
                    logger.error(f"Error applying bonus {bonus.id}: {str(e)}")
            else:
                # Bonus not selected for delivery this time
                bonuses_skipped += 1
                self.stdout.write(
                    self.style.WARNING(
                        f"Bonus of {bonus.amount} for {bonus.date} not selected for delivery (random chance)"
                    )
                )

        # Summary message
        total_bonuses_found = bonuses_applied + bonuses_skipped
        delivery_mode = "FORCED DELIVERY (8 PM)" if force_delivery else "RANDOM DELIVERY"

        if bonuses_applied > 0:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Summary for {current_date} [{delivery_mode}]: Applied {bonuses_applied} bonuses totaling {total_bonus_applied}, "
                    f"skipped {bonuses_skipped} bonuses (out of {total_bonuses_found} total pending bonuses)"
                )
            )
        elif bonuses_skipped > 0:
            self.stdout.write(
                self.style.WARNING(
                    f"Summary for {current_date} [{delivery_mode}]: No bonuses applied, {bonuses_skipped} bonuses skipped due to random selection"
                )
            )
        else:
            self.stdout.write(
                self.style.WARNING(
                    f"No bonuses were processed on {current_date} [{delivery_mode}]"
                )
            )