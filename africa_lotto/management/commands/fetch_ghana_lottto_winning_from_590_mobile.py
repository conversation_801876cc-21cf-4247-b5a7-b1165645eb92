from django.core.management import BaseCommand

from africa_lotto.tasks import get_ghana_winnings_from_590_mobile


class Command(BaseCommand):
    help = 'Fetch Ghana winnings from 590 Mobile'

    def handle(self, *args, **options):
        self.stdout.write("Running get_ghana_winnings_from_590_mobile()")
        get_ghana_winnings_from_590_mobile()
        self.stdout.write(self.style.SUCCESS("Done."))
