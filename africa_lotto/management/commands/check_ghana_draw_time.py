from django.core.management.base import BaseCommand
from datetime import datetime
import pytz
from django.conf import settings

from account.authentication import GhanaLottoDrawPermissionPermission
from africa_lotto.models import AfricaLottoConstants

class Command(BaseCommand):
    help = "Check if draw time has closed and create a new batch"

    def handle(self, *args, **kwargs):
        permission = GhanaLottoDrawPermissionPermission()

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        current_time_str = TODAY.strftime("%I:%M %p")  # Ensures 12-hour format with AM/PM

        africa_lotto_constant = AfricaLottoConstants.objects.last()
        if africa_lotto_constant is None:
            africa_lotto_constant = AfricaLottoConstants.objects.create(minimum_stake=50)

        draw_end_time_str = str(africa_lotto_constant.draw_end_time)  # Stored as integer or HH:MM
        draw_end_period = africa_lotto_constant.draw_end_time_period.upper()

         # Convert draw_end_time_str to 12-hour format with AM/PM
        if ":" not in draw_end_time_str:
            draw_end_time_str = f"{int(draw_end_time_str)}:00"

        draw_end_time_formatted = f"{draw_end_time_str} {draw_end_period}"
        permission.is_draw_closed(current_time_str, draw_end_time_formatted)
        self.stdout.write(self.style.SUCCESS("Checked draw time and updated batch if necessary."))


