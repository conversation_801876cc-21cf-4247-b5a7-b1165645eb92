from django.core.management.base import BaseCommand
from africa_lotto.helpers import live_scrape_ghana_draw_numbers, scrape_ghana_draw_numbers
from africa_lotto.models import AfricaLottoDrawNumbers
from datetime import datetime
from decouple import config


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        url = "https://www.590mobile.com.gh/results"
        if config("ENVIRONMENT") == "dev":
            lotto = scrape_ghana_draw_numbers(url)
        elif config("ENVIRONMENT") == "staging" or config("ENVIRONMENT") == "prod":
            print(config("ENVIRONMENT"))
            lotto = live_scrape_ghana_draw_numbers(url)
        """
        SAMPLE RESPONSE

        [
            {
                "Draw ID":"178596",
                "Draw Name":"National Weekly",
                "Date":"2025-03-01",
                "Numbers":"1,18,55,61,34"
            },
            {
                "Draw ID":"178595",
                "Draw Name":"Saturday Noon Rush",
                "Date":"2025-03-01",
                "Numbers":"18,84,16,58,28"
            },
        ]
        """

        print("lotto", lotto, "\n\n")

        if isinstance(lotto, list):
            for game in lotto:
                if AfricaLottoDrawNumbers.objects.filter(draw_date=game["Date"]).exists():
                    continue

                if "rush" in game["Draw Name"].lower():
                    continue
                

                AfricaLottoDrawNumbers.objects.create(
                    draw_date=game["Date"],
                    draw_name=game["Draw Name"],
                    draw_number=str(game["Numbers"]).replace("No.", "")
                )
                

