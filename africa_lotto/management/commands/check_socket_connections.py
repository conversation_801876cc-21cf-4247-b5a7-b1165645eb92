# management/commands/check_connections.py
from django.core.management.base import BaseCommand
import subprocess
import os

class Command(BaseCommand):
    help = 'Check system resource usage related to WebSocket connections'

    def handle(self, *args, **options):
        # Get process ID
        pid = os.getpid()
        self.stdout.write(f"Checking connections for process {pid}")
        
        # Check open files
        open_files = subprocess.check_output(f"lsof -p {pid} | wc -l", shell=True).decode().strip()
        self.stdout.write(f"Open files: {open_files}")
        
        # Check socket connections
        sockets = subprocess.check_output(f"lsof -p {pid} | grep -c ESTABLISHED", shell=True).decode().strip()
        self.stdout.write(f"Active socket connections: {sockets}")
        
        # Check system limits
        file_limit = subprocess.check_output("ulimit -n", shell=True).decode().strip()
        self.stdout.write(f"File descriptor limit: {file_limit}")