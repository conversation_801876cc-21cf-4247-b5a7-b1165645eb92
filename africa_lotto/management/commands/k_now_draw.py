from django.core.management.base import BaseCommand
from africa_lotto.models import AfricaLottoGameType
from africa_lotto.tasks import celery_africa_lotto_kenya_draw
from main.models import DrawLog

class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        DrawLog.create_draw_log(frequency_minutes=5, draw_name="K_NOW")
        celery_africa_lotto_kenya_draw(game_type = AfricaLottoGameType.K_NOW)

        self.stdout.write(self.style.SUCCESS(f"Running main draw for"))