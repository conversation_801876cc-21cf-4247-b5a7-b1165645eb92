from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta, date

from africa_lotto.models import AfricaLottoBatch, AfricaLottoGameType
from africa_lotto.tasks import celery_africa_lotto_kenya_draw
from django.db.models import Q


class Command(BaseCommand):

    def handle(self, *args, **kwargs):

        today = timezone.now().date()
        yesterday = today - timedelta(days=1)

        pending_draws = AfricaLottoBatch.objects.filter(Q(created_at__date__gte = yesterday), Q(batch_status = False), Q(draw_status = False), ~Q(game_type = "GHANA_LOTTO"))

        for instance in pending_draws:
            celery_africa_lotto_kenya_draw(game_type = instance.game_type, batch_db_id = instance.id, create_new_batch = False)