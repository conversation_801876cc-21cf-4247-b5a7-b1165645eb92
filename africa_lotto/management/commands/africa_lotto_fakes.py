import random
from datetime import datetime, timedelta

from django.contrib.auth.hashers import make_password
from django.core.management.base import BaseCommand

from account.models import User
from africa_lotto.helpers import (
    get_valid_until_date_for_kenya_30_games,
    scrape_ghana_draw_numbers,
)
from africa_lotto.models import (
    AfricaLotteryType,
    AfricaLotto,
    AfricaLottoBatch,
    AfricaLottoDrawNumbers,
)
from africa_lotto.socket_utils import live_k_now_draw_number_socket_update
from africa_lotto.tasks import (
    celery_africa_lotto_kenya_draw,
    celery_filter_africa_lotto_game_winners,
)
from main.helpers.open_ai_helper import OpenAiHelper
from pos_app.pos_helpers import get_week_info


def create_fake_africa_lotto_ticket():
    """
    Create fake Africa Lotto tickets for testing purposes.
    """

    game_types = ["KENYA_LOTTO", "KENYA_30_LOTTO", "K_NOW"]

    for game in game_types:
        # create 5 tickets for each game type
        for i in range(5):
            batch_name = None

            if game == "KENYA_LOTTO":
                batch_name = random.choices(["Tokyo", "Ultimate", "Angelina Special", "Supreme Max", "Barnabas"])
                batch_name = batch_name[0]

            batch = AfricaLottoBatch.objects.create(game_type=game, batch_name=batch_name, batch_status=False)

            # create 5 random numbers
            ticket = random.sample(range(1, 91), 5)
            ticket = ",".join([str(num) for num in ticket])

            machine_number = random.sample(range(1, 91), 5)
            machine_number = ",".join([str(num) for num in machine_number])

            AfricaLottoDrawNumbers.objects.create(
                draw_number=ticket,
                game_type=game,
                machine_number=machine_number,
                batch_id=batch.batch_uuid,
            )


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):

        draw_number = AfricaLottoDrawNumbers.objects.get(id=20538)

        week_range = get_week_info(draw_number.created_at)

        open_ai_prompt = f"""is this ghana draw number {draw_number.draw_number} for {draw_number.draw_name} for this week {week_range}
        response: {{
            "is_this_week_and_it_correct": bool(if the draw number is for this week),
            "is_this_draw": boolif the draw number is for this week),
        }}
        
        NOTE: must return data in json, using the above response sample
        """

        print(open_ai_prompt)

        print("\n")

        open_ai_response_data = OpenAiHelper.prompt_open_ai(open_ai_prompt)

        print(open_ai_response_data)
