from django.core.management.base import BaseCommand
from africa_lotto.models import AfricaLottoGameType
from africa_lotto.tasks import celery_africa_lotto_kenya_draw
from datetime import datetime
import pytz
from django.conf import settings


class Command(BaseCommand):
    help = "Check if main draw time has reached and run the draw"
    
    def handle(self, *args, **kwargs):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_time = datetime.now(tz=timezone).strftime("%I:%M %p")
        draw_times = ["08:15 AM","08:45 AM", "09:15 AM", "09:45 AM", "10:15 AM", "10:45 AM",
                      "11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", 
                      "01:15 PM", "01:45 PM", "02:15 PM", "02:45 PM", 
                      "03:15 PM", "03:45 PM", "04:15 PM", "04:45 PM",
                      "05:15 PM", "05:45 PM", "06:15 PM", "06:45 PM",
                      "07:15 PM", "07:45 PM", "08:15 PM", "08:45 PM", 
                      "09:15 PM", "09:45 PM"]
        
        if current_time in draw_times:
            # Run the main draw logic here
            celery_africa_lotto_kenya_draw(game_type = AfricaLottoGameType.KENYA_30_LOTTO)
            self.stdout.write(self.style.SUCCESS(f"Running main draw for {current_time}"))
        else:
            self.stdout.write(self.style.WARNING("Not a valid draw time."))