# Create your models here.
import random
import uuid
from datetime import datetime, time

import pytz
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.postgres.fields import ArrayField
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models import Q, Sum, UniqueConstraint
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from africa_lotto.helpers import (
    base62_encode,
    get_valid_until_date,
    get_valid_until_date_for_k_now_games,
    get_valid_until_date_for_kenya_30_games,
    get_valid_until_date_for_kenya_games,
)
from pos_app.models import (
    Agent,
    AgentConstantVariables,
    AgentWallet,
    GamesDailyActivities,
    GeneralRetailLottoGames,
    LottoVerticalLeadWallet,
    RetailWalletTransactions,
    SupervisorWallet,
)
from pos_app.pos_helpers import PosAgentHelper
from retail_metrics.models import ReturnToPlayerAndReturnToOwnerAnalytics
from wallet_system.models import Wallet


class AfricaLottoGameType(models.TextChoices):
    GHANA_LOTTO = "GHANA_LOTTO", _("GHANA_LOTTO")
    NIGERIA_LOTTO = "NIGERIA_LOTTO", _("NIGERIA_LOTTO")
    KENYA_LOTTO = "KENYA_LOTTO", _("KENYA_LOTTO")
    KENYA_30_LOTTO = "KENYA_30_LOTTO", _("KENYA_30_LOTTO")
    SALARY_FOR_LIFE = "SALARY_FOR_LIFE", _("SALARY_FOR_LIFE")
    INSTANT_CASH = "INSTANT_CASH", _("INSTANT_CASH")
    BANKER = "BANKER", _("BANKER")
    K_NOW = "K_NOW", _("K_NOW")


class AfricaLottoGames(models.Model):
    name = models.CharField(max_length=100, choices=AfricaLottoGameType.choices)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "AFRICAN LOTTO GAMES"
        verbose_name_plural = "AFRICAN LOTTO GAMES"


class AfricaLottoConstants(models.Model):
    agent_commission_percentage = models.FloatField(default=0.3)
    rto_percentage = models.FloatField(default=0.3)
    rtp_percentage = models.FloatField(default=0.2)
    k_now_rto_percentage = models.FloatField(default=0.3)
    k_now_rtp_percentage = models.FloatField(default=0.2)
    minimum_stake = models.FloatField(default=5)
    per_minimum_stake = models.FloatField(default=5)
    combine_ticket_minimum_stake = models.FloatField(default=100)
    perm_maximum_balls = models.IntegerField(default=20)
    banker_maximum_balls = models.IntegerField(default=1)
    against_top_maximum_balls = models.IntegerField(default=5)
    against_bottom_maximum_balls = models.IntegerField(default=10)
    draw_end_time = models.CharField(max_length=50, default="7:30", help_text="Time in hours minutes. e.g 7:30")
    weekend_draw_end_time = models.CharField(
        max_length=50, default="5:00", help_text="Time in hours minutes. e.g 7:30"
    )
    draw_end_time_period = models.CharField(max_length=50, default="AM", help_text="AM or PM")
    draw_start_time = models.CharField(max_length=50, default=8, help_text="Time in hours")
    draw_start_time_period = models.CharField(max_length=50, default="AM", help_text="AM or PM")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    kenya_running_balance = models.FloatField(default=0)
    kenya_booster_balance = models.FloatField(default=0)
    kenya_booster_percent = models.CharField(
        default="1,2",
        max_length=50,
        help_text="Percentage increase in RTP at every draw",
    )
    kenya_topup_balance = models.FloatField(default=0)
    kenya_topup_amount = models.CharField(
        default="1000,10000, 100",
        max_length=50,
        help_text="Fixed addition to RTP at every draw",
    )
    kenya30_running_balance = models.FloatField(default=0)
    kenya30_booster_balance = models.FloatField(default=0)
    kenya30_booster_percent = models.CharField(
        default="1,2",
        max_length=50,
        help_text="Percentage increase in RTP at every draw",
    )
    kenya30_topup_balance = models.FloatField(default=0)
    kenya30_topup_amount = models.CharField(
        default="1000,10000, 100",
        max_length=50,
        help_text="Fixed addition to RTP at every draw",
    )
    k_now_running_balance = models.FloatField(default=0)
    k_now_booster_balance = models.FloatField(default=0)
    k_now_booster_percent = models.CharField(
        default="1,2",
        max_length=50,
        help_text="Percentage increase in RTP at every draw",
    )
    k_now_topup_balance = models.FloatField(default=0)
    k_now_topup_amount = models.CharField(
        default="1000,10000, 100",
        max_length=50,
        help_text="Fixed addition to RTP at every draw",
    )
    k_now_lotto_minmum_game_play_amount = models.FloatField(default=300)
    kenya_30_lotto_minmum_game_play_amount = models.FloatField(default=200)
    active_games = models.ManyToManyField(AfricaLottoGames)
    k_now_commission_percentage = models.FloatField(default=0.2)

    class Meta:
        verbose_name = "AFRICAN LOTTO CONSTANTS"
        verbose_name_plural = "AFRICAN LOTTO CONSTANTS"

    @classmethod
    def get_minimum_stake(cls):
        return cls.objects.first().minimum_stake if cls.objects.exists() else 5

    @classmethod
    def get_perm_minimum_stake(cls):
        return cls.objects.first().per_minimum_stake if cls.objects.exists() else 5

    @classmethod
    def get_agent_commission_percentage(cls):
        return cls.objects.first().agent_commission_percentage if cls.objects.exists() else 0.3

    @classmethod
    def get_rto_percentage(cls):
        return cls.objects.first().rto_percentage if cls.objects.exists() else 0.3

    @classmethod
    def get_rtp_percentage(cls):
        return cls.objects.first().rtp_percentage if cls.objects.exists() else 0.2

    @classmethod
    def get_k_now_rto_percentage(cls):
        return cls.objects.first().k_now_rto_percentage if cls.objects.exists() else 0.3

    @classmethod
    def get_k_now_rtp_percentage(cls):
        return cls.objects.first().k_now_rtp_percentage if cls.objects.exists() else 0.2

    @classmethod
    def get_combine_ticket_minimum_stake(cls):
        return cls.objects.first().combine_ticket_minimum_stake if cls.objects.exists() else 100

    @classmethod
    def top_up_kenya_rtp(cls, amount):
        topup = 0

        if amount <= 0:
            return topup

        x, y, z = cls.objects.all().last().kenya_topup_amount.split(",")

        topup = random.choice(range(int(x), int(y), int(z)))

        if cls.objects.all().last().kenya_topup_balance < topup:
            return 0

        AfricaLottoConstants.objects.all().update(kenya_topup_balance=models.F("kenya_topup_balance") - topup)

        return topup

    @classmethod
    def top_up_kenya30_rtp(cls, amount):
        topup = 0

        if amount <= 0:
            return topup

        x, y, z = cls.objects.all().last().kenya30_topup_amount.split(",")

        topup = random.choice(range(int(x), int(y), int(z)))

        if cls.objects.all().last().kenya30_topup_balance < topup:
            return 0

        AfricaLottoConstants.objects.all().update(kenya30_topup_balance=models.F("kenya30_topup_balance") - topup)

        return topup

    @classmethod
    def top_up_k_now_rtp(cls, amount):
        topup = 0

        if amount <= 0:
            return topup

        x, y, z = cls.objects.all().last().k_now_topup_amount.split(",")

        topup = random.choice(range(int(x), int(y), int(z)))

        if cls.objects.all().last().k_now_topup_balance < topup:
            return 0

        AfricaLottoConstants.objects.all().update(k_now_topup_balance=models.F("k_now_topup_balance") - topup)

        return topup

    @classmethod
    def get_kenya_30_lotto_minmum_game_play_amount(cls):
        return cls.objects.last().kenya_30_lotto_minmum_game_play_amount if cls.objects.last() else 200

    @classmethod
    def get_k_now_lotto_minmum_game_play_amount(cls):
        return cls.objects.last().k_now_lotto_minmum_game_play_amount if cls.objects.last() else 300

    @classmethod
    def active_africa_lotto_games(cls):
        if cls.objects.exists():
            r = list(cls.objects.first().active_games.all().values_list("name"))
            r = [i[0] for i in r]
            # print(r)
            return r
        else:
            return []

    @classmethod
    def get_k_now_commission_percentage(cls):
        return cls.objects.first().k_now_commission_percentage if cls.objects.exists() else 0.3


class AfricaLotteryType(models.TextChoices):
    NAP2 = "NAP2", _("NAP2")
    NAP3 = "NAP3", _("NAP3")
    NAP4 = "NAP4", _("NAP4")
    NAP5 = "NAP5", _("NAP5")
    PERM2 = "PERM2", _("PERM2")
    PERM3 = "PERM3", _("PERM3")
    PERM4 = "PERM4", _("PERM4")
    PERM5 = "PERM5", _("PERM5")
    BANKER = "1BANKER", _("1BANKER")
    AGAINST = "AGAINST", _("AGAINST")


class AfricaLottoChannel(models.TextChoices):
    USSD = "USSD", _("USSD")
    WEB = "WEB", _("WEB")
    POS_AGENT = "POS_AGENT", _("POS_AGENT")


# class BatchNameChoices(models.TextChoices):
#     SUNDAY = 'sunday', 'Sunday Aseda'
#     MONDAY = 'monday', 'Monday Special'
#     TUESDAY = 'tuesday', 'Lucky Tuesday'
#     WEDNESDAY = 'wednesday', 'Midweek'
#     THURSDAY = 'thursday', 'Fortune Thursday'
#     FRIDAY = 'friday', 'Friday Bonanza'
#     SATURDAY = 'saturday', 'National Weekly'


def generate_batch_uuid_func():
    formatted_month = datetime.now().strftime("%b%Y")  # Make sure to import datetime
    return f"{formatted_month}-{uuid.uuid4()}"


class AfricaLottoBatch(models.Model):

    formatted_month = datetime.strftime(datetime.now(), "%b")

    batch_uuid = models.CharField(max_length=150, default=generate_batch_uuid_func)
    game_type = models.CharField(max_length=50, choices=AfricaLottoGameType.choices)
    batch_name = models.CharField(max_length=100, blank=True, null=True)
    batch_number = models.CharField(max_length=100, blank=True, null=True, unique=True)
    total_revenue = models.FloatField(default=0)
    batch_status = models.BooleanField(default=True)
    draw_status = models.BooleanField(default=False)
    draw_date = models.DateTimeField(blank=True, null=True)
    next_draw_time = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):

        day_of_week = self.created_at.strftime("%A")
        formatted_date = f"{day_of_week} {self.created_at.strftime('%Y-%m-%d %I:%M:%S %p')}"

        return f"{self.game_type} - {self.batch_uuid} - {formatted_date}"

    def save(self, *args, **kwargs):

        if self.next_draw_time:

            timezone = pytz.timezone(settings.TIME_ZONE)

            current_time = datetime.now(tz=timezone)
            # if self.created_at:
            #     current_time = self.created_at
            # rent_time.date

            date = current_time.date()

            existing_batch = AfricaLottoBatch.objects.filter(
                game_type=self.game_type, next_draw_time=self.next_draw_time, created_at__date=date
            ).filter(~Q(pk=self.pk))

            if existing_batch.exists():
                raise ValidationError(
                    f"A batch for {self.game_type} with draw date {self.next_draw_time} already exists. date: {date}!"
                )

        return super(AfricaLottoBatch, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "AFRICAN LOTTO BATCH"
        verbose_name_plural = "AFRICAN LOTTO BATCHES"

        constraints = [
            UniqueConstraint(
                fields=["game_type"],
                condition=Q(batch_status=True),
                name="unique_active_batch",
            )
        ]

    @classmethod
    def update_batch_status(cls, batch_id, status):
        try:
            batch = cls.objects.get(id=batch_id)
            batch.batch_status = status
            batch.save()
            return True
        except cls.DoesNotExist:
            return False

    @classmethod
    def get_batch_number(cls, game_type):
        pre_fix = f"{str(game_type)[0]}{str(game_type)[-1]}"
        random_numbers = [random.randint(1, 9) for _ in range(8)]
        random_numbers = "".join(str(i) for i in random_numbers)
        batch_number = f"{pre_fix}{random_numbers}"

        try:
            cls.objects.get(batch_uuid=batch_number)
            return cls.get_batch_number(game_type)
        except cls.DoesNotExist:
            return batch_number

    @classmethod
    def get_active_batch(cls, game_type):

        try:
            active_batch = cls.objects.get(game_type=game_type, batch_status=True)
            if game_type != AfricaLottoGameType.GHANA_LOTTO:
                return active_batch

            TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
            day_of_week = TODAY.strftime("%A")

            if str(day_of_week).lower() == "sunday":
                name_of_new_batch = "sunday aseda"

            elif str(day_of_week).lower() == "monday":
                name_of_new_batch = "monday special"

            elif str(day_of_week).lower() == "tuesday":
                name_of_new_batch = "lucky tuesday"

            elif str(day_of_week).lower() == "wednesday":
                name_of_new_batch = "midweek"
            elif str(day_of_week).lower() == "thursday":
                name_of_new_batch = "fortune thursday"
            elif str(day_of_week).lower() == "friday":
                name_of_new_batch = "friday bonanza"

            elif str(day_of_week).lower() == "saturday":
                name_of_new_batch = "national weekly"
            else:
                name_of_new_batch = None

            if active_batch.batch_name != name_of_new_batch:
                active_batch.batch_status = False
                active_batch.save()

                batch_number = cls.get_batch_number(game_type)

                next_draw_date = get_valid_until_date().strftime("%I:%M %p")

                # check if there's already a batch that has this details created the same day
                try:
                    active_batch = cls.objects.get(
                        game_type=game_type,
                        batch_name=name_of_new_batch,
                        batch_number=batch_number,
                        next_draw_time=next_draw_date,
                        created_at__date=TODAY.date(),
                    )
                    active_batch.batch_status = True
                    active_batch.save()

                    return active_batch

                except cls.DoesNotExist:
                    return cls.objects.create(
                        game_type=game_type,
                        batch_name=name_of_new_batch,
                        batch_number=batch_number,
                        next_draw_time=next_draw_date,
                    )

            return active_batch

        except cls.DoesNotExist:
            if game_type == AfricaLottoGameType.GHANA_LOTTO:
                TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
                day_of_week = TODAY.strftime("%A")

                if str(day_of_week).lower() == "sunday":
                    name_of_new_batch = "sunday aseda"

                elif str(day_of_week).lower() == "monday":
                    name_of_new_batch = "monday special"

                elif str(day_of_week).lower() == "tuesday":
                    name_of_new_batch = "lucky tuesday"

                elif str(day_of_week).lower() == "wednesday":
                    name_of_new_batch = "midweek"
                elif str(day_of_week).lower() == "thursday":
                    name_of_new_batch = "fortune thursday"
                elif str(day_of_week).lower() == "friday":
                    name_of_new_batch = "friday bonanza"

                elif str(day_of_week).lower() == "saturday":
                    name_of_new_batch = "national weekly"
                else:
                    name_of_new_batch = None

                batch_number = cls.get_batch_number(game_type)
                next_draw_date = get_valid_until_date().strftime("%I:%M %p")
                try:
                    active_batch = cls.objects.get(
                        game_type=game_type,
                        batch_name=name_of_new_batch,
                        next_draw_time=next_draw_date,
                        created_at__date=TODAY.date(),
                    )
                    active_batch.batch_status = True
                    active_batch.save()
                    return active_batch

                except:
                    return cls.objects.create(
                        game_type=game_type,
                        batch_name=name_of_new_batch,
                        batch_number=batch_number,
                        next_draw_time=next_draw_date,
                    )

            elif game_type == AfricaLottoGameType.KENYA_LOTTO:

                timezone = pytz.timezone(settings.TIME_ZONE)
                current_time = datetime.now(tz=timezone).strftime("%I:%M %p")

                TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

                batch_number = cls.get_batch_number(game_type)
                next_draw_date = get_valid_until_date_for_kenya_games().strftime("%I:%M %p")

                # if next_draw_date in ["09:01 AM", "09:00 AM"]:
                #     # name_of_new_batch = "Ultimate"
                #     name_of_new_batch = "Barnabas"
                #     # name_of_new_batch = "Tokyo"
                # elif next_draw_date in ["12:01 PM", "12:00 PM"]:
                #     name_of_new_batch = "Tokyo"
                #     # name_of_new_batch = "Ultimate"
                #     # valid_until = "03:00 PM"
                # elif next_draw_date in ["03:01 PM", "03:00 PM"]:
                #     # name_of_new_batch = "Angelina Special"
                #     name_of_new_batch = "Ultimate"
                #     # valid_until = "06:00 PM"
                # elif next_draw_date in ["06:01 PM", "06:00 PM"]:
                #     # name_of_new_batch = "Barnabas"
                #     # name_of_new_batch = "Supreme Max"
                #     name_of_new_batch = "Angelina Special"
                #     # valid_until = "09:00 PM"
                # elif next_draw_date in ["09:01 PM", "09:00 PM"]:
                #     name_of_new_batch = "Supreme Max"
                #     # name_of_new_batch = "Barnabas"
                #     # valid_until = "09:01 AM"
                # else:
                #     name_of_new_batch = None

                next_draw_date = str(next_draw_date).strip()
                if next_draw_date == str("09:01 AM").strip() or next_draw_date == str("09:00 AM").strip():
                    name_of_new_batch = "Barnabas"
                elif next_draw_date == str("12:01 PM").strip() or next_draw_date == str("12:00 PM").strip():
                    name_of_new_batch = "Tokyo"
                elif next_draw_date == str("03:01 PM").strip() or next_draw_date == str("03:00 PM").strip():
                    name_of_new_batch = "Ultimate"
                elif next_draw_date == str("06:01 PM").strip() or next_draw_date == str("06:00 PM").strip():
                    name_of_new_batch = "Angelina Special"
                elif next_draw_date == str("09:00 PM").strip() or next_draw_date == str("09:00 PM").strip():
                    name_of_new_batch = "Supreme Max"
                else:
                    name_of_new_batch = None

                try:
                    active_batch = cls.objects.get(
                        game_type=game_type, next_draw_time=next_draw_date, created_at__date=TODAY.date()
                    )
                    active_batch.batch_status = True
                    active_batch.save()

                    return active_batch
                except cls.DoesNotExist:
                    return cls.objects.create(
                        game_type=game_type,
                        batch_name=name_of_new_batch,
                        batch_number=batch_number,
                        next_draw_time=next_draw_date,
                    )
            else:
                batch_number = cls.get_batch_number(game_type)
                if game_type == AfricaLottoGameType.KENYA_30_LOTTO:
                    next_draw_date = get_valid_until_date_for_kenya_30_games().strftime("%I:%M %p")
                else:
                    next_draw_date = get_valid_until_date_for_k_now_games().strftime("%I:%M %p")

                try:
                    active_batch = cls.objects.create(game_type=game_type, next_draw_time=next_draw_date)
                    active_batch.batch_status = True
                    active_batch.save()

                    return active_batch
                except cls.DoesNotExist:
                    try:
                        active_batch = cls.objects.get(
                            game_type=game_type, next_draw_time=next_draw_date, created_at__date=TODAY.date()
                        )
                        active_batch.batch_status = True
                        active_batch.save()
                        return active_batch

                    except:
                        return cls.objects.create(
                            game_type=game_type, batch_number=batch_number, next_draw_time=next_draw_date
                        )


class AfricaLotto(models.Model):
    DRAWN_FOR_CHOICES = [
        ("GLOBAL", "GLOBAL"),
        ("LOCAL", "LOCAL"),
    ]

    user_phone_number = models.CharField(max_length=20, blank=True, null=True)
    user_name = models.CharField(max_length=100, blank=True, null=True)
    agent_phone_number = models.CharField(max_length=20, blank=True, null=True)
    agent_name = models.CharField(max_length=100, blank=True, null=True)
    batch = models.ForeignKey(AfricaLottoBatch, on_delete=models.CASCADE)
    game_play_id = models.CharField(max_length=100)
    game_pin = models.CharField(max_length=100)
    lucky_number = models.CharField(max_length=100)
    bottom_ticket = models.CharField(max_length=100, blank=True, null=True)
    stake_per_line = models.FloatField(default=0)
    purchase_amount = models.FloatField(default=0)
    total_game_stake_amount = models.FloatField(default=0)
    potential_winnings = models.FloatField(default=0)
    multiplier = models.CharField(max_length=50, blank=True, null=True)
    winning_scenarios = models.TextField(blank=True, null=True)
    rtp = models.FloatField(default=0)
    rto = models.FloatField(default=0)
    effective_rtp = models.FloatField(default=0)
    commission_percentage = models.FloatField(default=0)
    commission = models.FloatField(default=0)
    pool = models.FloatField(default=0)
    channel = models.CharField(max_length=50, choices=AfricaLottoChannel.choices)
    lottery_type = models.CharField(max_length=50, choices=AfricaLotteryType.choices)
    game_type = models.CharField(max_length=50, choices=AfricaLottoGameType.choices)
    paid = models.BooleanField(default=False)
    number_of_ticket = models.IntegerField(default=1)
    won = models.BooleanField(default=False)
    exempted_from_draw = models.BooleanField(default=False)
    drawn_for = models.CharField(max_length=25, choices=DRAWN_FOR_CHOICES, default="GLOBAL")
    seeder_status = models.CharField(
        max_length=100,
        choices=(("COMPLETE", "COMPLETE"), ("PROCESSING", "PROCESSING"), ("PENDING", "PENDING")),
        default="PENDING",
        help_text="Current status of the seeder for this ticket.",
    )
    double_chance = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AFRICAN LOTTO"
        verbose_name_plural = "AFRICAN LOTTO"

    @classmethod
    def generate_game_id(cls, prefix, count=8):
        game_id = f"{prefix}-{base62_encode(uuid.uuid4().int >> 64)[:count]}"

        if cls.objects.filter(game_play_id=game_id).exists():
            cls.generate_game_id(prefix)

        return game_id

    @classmethod
    def get_game_pin(cls):
        # random 4 digit number
        return random.randint(1000, 999999)

    @classmethod
    def create_ghana_lotto_ticket(cls, agent_instance: Agent, **kwargs):
        from main.api.api_lottery_helpers import generate_game_play_id

        """
        Create a Ghana Lotto ticket with associated agent wallet transaction.

        This method handles the complete flow of creating a Ghana Lotto ticket:
        1. Generates a unique game ID
        2. Charges the agent's wallet
        3. Creates lottery tickets in the active batch
        4. Processes multiple lottery numbers and scenarios

        Args:
            agent_instance (Agent): The agent creating the ticket
            **kwargs: Additional parameters including:
                - total_amount (float): Total amount to charge
                - phone_number (str): Customer phone number
                - lottery_data (list[dict]): List of lottery ticket data containing:
                    - lucky_number (list[int]): Main lottery numbers
                    - bottom_ticket (list[int], optional): Secondary lottery numbers
                    - stake_per_line (float): Stake amount per line
                    - purchase_amount (float): Total purchase amount
                    - total_stake (float): Total stake amount
                    - potential_winnings (float): Potential winning amount
                    - multiplier (int): Winning multiplier
                    - winning_scenarios (dict): Possible winning scenarios
                    - lottery_type (str): Type of lottery

        Returns:
            tuple[bool, str, dict | None]: Tuple containing:
                - bool: Success status
                - str: Status message
                - dict | None: Game data if successful, None if failed

        Raises:
            No exceptions are raised; all errors are returned in the tuple
        """

        # print(kwargs)

        # charge agent wallet and play game
        try:
            with transaction.atomic():

                game_play_id = generate_game_play_id()

                pos_agent_helper = PosAgentHelper(
                    agent_instance=agent_instance,
                    amount=kwargs.get("total_amount"),
                    pin="1",
                )

                charge_response = pos_agent_helper.handle_agent_charge_and_lottery_play(
                    lottery_qs=[1],
                    return_after_successfull_charge=True,
                    _game_play_id=game_play_id,
                    lottery_type="GHANA_LOTTO",
                )

                if charge_response != "success":
                    if not isinstance(charge_response, dict):
                        print("charge_response", charge_response)
                        raise Exception("An error occured while charging agent wallet")

                    if charge_response.get("message") != "success":
                        raise Exception(charge_response.get("message"))

                # get lottery batch
                active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.GHANA_LOTTO)

                game_pin = cls.get_game_pin()

                next_draw_time = active_batch.next_draw_time
                if next_draw_time is None:
                    next_draw_time = get_valid_until_date().strftime("%I:%M %p")

                sales_date = datetime.now().date()
                kwargs["game_pin"] = game_pin
                kwargs["game_play_id"] = game_play_id
                kwargs["GAME_TYPE"] = active_batch.game_type
                kwargs["sales_date"] = sales_date.strftime("%Y-%m-%d")
                kwargs["valid_until_date"] = next_draw_time
                kwargs["draw_time"] = next_draw_time
                kwargs["agent_id"] = f"winwise-{agent_instance.id}"
                kwargs["batch_name"] = active_batch.batch_name
                kwargs["batch_number"] = active_batch.batch_number

                lottery_tickets = kwargs["lottery_data"]

                # agent_commission_percentage = AfricaLottoConstants.get_agent_commission_percentage()

                for lottery_ticket in lottery_tickets:
                    ticket = lottery_ticket.get("lucky_number")
                    ticket = ",".join(str(i) for i in ticket)

                    bottom_ticket = None
                    if lottery_ticket.get("bottom_ticket"):
                        bottom_ticket = lottery_ticket.get("bottom_ticket")
                        bottom_ticket = ",".join(str(i) for i in bottom_ticket)

                    # agent_commission = 0

                    # amount_paid = amount_paid - agent_commission

                    ticket_instance = cls.objects.create(
                        user_phone_number=kwargs.get("phone_number"),
                        agent_phone_number=agent_instance.phone,
                        agent_name=agent_instance.full_name,
                        batch=active_batch,
                        game_play_id=game_play_id,
                        game_pin=game_pin,
                        lucky_number=ticket,
                        bottom_ticket=bottom_ticket,
                        stake_per_line=lottery_ticket.get("stake_per_line", 0),
                        purchase_amount=lottery_ticket.get("purchase_amount"),
                        total_game_stake_amount=lottery_ticket.get("total_stake", 0),
                        potential_winnings=lottery_ticket.get("potential_winnings"),
                        multiplier=lottery_ticket.get("multiplier", 0),
                        winning_scenarios=lottery_ticket.get("winning_scenarios"),
                        lottery_type=lottery_ticket.get("lottery_type"),
                        channel=AfricaLottoChannel.POS_AGENT,
                        game_type=AfricaLottoGameType.GHANA_LOTTO,
                        paid=True,
                        double_chance=lottery_ticket.get("double_chance", False),
                    )

                    if agent_instance.agent_type in ["LOTTO_AGENT", "AGENT"]:
                        AgentWallet.reward_commission(
                            agent_id=agent_instance.id,
                            game_play_amount=lottery_ticket.get("purchase_amount"),
                            commission_type="COMMISSION_ON_GAME_PLAY",
                            game_type="GHANA_LOTTO",
                            rto_amount=0,
                        )

                    Wallet.fund_wallet(
                        wallet_type="GHANA_RTP_WALLET",
                        amount=lottery_ticket.get("purchase_amount"),
                        game_type="GHANA_LOTTO",
                    )

                    try:
                        RetailWalletTransactions.create_debit_record_for_game_play(
                            amount=lottery_ticket.get("purchase_amount"),
                            wallet_value=lottery_ticket.get("purchase_amount"),
                            rto_value=0,
                            rtp_value=0,
                            game_type=AfricaLottoGameType.GHANA_LOTTO,
                        )
                    except:
                        pass

                    GeneralRetailLottoGames.create_record(
                        agent_phone_number=agent_instance.phone,
                        agent_name=agent_instance.full_name,
                        agent_email=agent_instance.email,
                        batch_uuid=active_batch.batch_uuid,
                        game_play_id=game_play_id,
                        game_pin=game_pin,
                        lucky_number=ticket,
                        purchase_amount=ticket_instance.purchase_amount,
                        lotto_db_id=ticket_instance.id,
                        paid=ticket_instance.paid,
                        number_of_ticket=ticket_instance.number_of_ticket,
                        multiplier=ticket_instance.multiplier,
                        double_chance=ticket_instance.double_chance,
                        bottom_ticket=ticket_instance.bottom_ticket,
                        rtp=ticket_instance.rtp,
                        rto=ticket_instance.rto,
                        commission_percentage=ticket_instance.commission_percentage,
                        pool=ticket_instance.pool,
                        type_of_agent=agent_instance.agent_type,
                        ticket_type=ticket_instance.lottery_type,
                        lotto_game_type=ticket_instance.game_type,
                        potential_winnings=ticket_instance.potential_winnings,
                    )

                return True, "success", kwargs

        except Exception as e:
            # status, message, game data
            return False, str(e), None
        # return cls.objects.create(**kwargs)

    @classmethod
    def create_kenya_lotto_ticket(cls, agent_instance: Agent, **kwargs):

        from africa_lotto.tasks import celery_africa_lotto_kenya_draw

        """
        Create a KENYA Lotto ticket with associated agent wallet transaction.

        This method handles the complete flow of creating a Ghana Lotto ticket:
        1. Generates a unique game ID
        2. Charges the agent's wallet
        3. Creates lottery tickets in the active batch
        4. Processes multiple lottery numbers and scenarios

        Args:
            agent_instance (Agent): The agent creating the ticket
            **kwargs: Additional parameters including:
                - total_amount (float): Total amount to charge
                - phone_number (str): Customer phone number
                - lottery_data (list[dict]): List of lottery ticket data containing:
                    - lucky_number (list[int]): Main lottery numbers
                    - bottom_ticket (list[int], optional): Secondary lottery numbers
                    - stake_per_line (float): Stake amount per line
                    - purchase_amount (float): Total purchase amount
                    - total_stake (float): Total stake amount
                    - potential_winnings (float): Potential winning amount
                    - multiplier (int): Winning multiplier
                    - winning_scenarios (dict): Possible winning scenarios
                    - lottery_type (str): Type of lottery

        Returns:
            tuple[bool, str, dict | None]: Tuple containing:
                - bool: Success status
                - str: Status message
                - dict | None: Game data if successful, None if failed

        Raises:
            No exceptions are raised; all errors are returned in the tuple
        """

        # print(kwargs)

        # charge agent wallet and play game

        with transaction.atomic():
            from main.api.api_lottery_helpers import generate_game_play_id

            game_play_id = generate_game_play_id()

            pos_agent_helper = PosAgentHelper(
                agent_instance=agent_instance,
                amount=kwargs.get("total_amount"),
                pin="1",
            )

            charge_response = pos_agent_helper.handle_agent_charge_and_lottery_play(
                lottery_qs=[1],
                return_after_successfull_charge=True,
                _game_play_id=game_play_id,
                lottery_type="KENYA_LOTTO",
            )

            print("charge_response", charge_response)

            if charge_response != "success":
                if not isinstance(charge_response, dict):
                    print("charge_response", charge_response)
                    raise Exception("An error occured while charging agent wallet")

                if charge_response.get("message") != "success":
                    raise Exception(charge_response.get("message"))

            sales_date = datetime.now().date()
            current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

            current_time_cutoff = current_time.time()
            cutoff_time = time(21, 00)

            if current_time_cutoff <= cutoff_time:
                # get lottery batch
                active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_LOTTO)

                # check the draw time for the batch and make sure this game play is not exactly at the time or the draw time should have drawn
                batch_draw_date = f"{sales_date.strftime('%Y-%m-%d')} {active_batch.next_draw_time}"
                batch_draw_date = datetime.strptime(batch_draw_date, "%Y-%m-%d %I:%M %p")

                batch_draw_date = pytz.timezone(settings.TIME_ZONE).localize(batch_draw_date)
                current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
                if current_time >= batch_draw_date:
                    active_batch.batch_status = False
                    active_batch.save()

                    celery_africa_lotto_kenya_draw(batch_db_id=active_batch.id)

                    active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_LOTTO)
            else:
                active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_LOTTO)

                if active_batch.next_draw_time.strip() != "09:01 AM":

                    active_batch.batch_status = False
                    active_batch.save()

                    # Get a new active batch
                    active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_LOTTO)

            print("active_batch", active_batch)

            game_pin = cls.get_game_pin()

            # valid_until = get_valid_until_date()

            next_draw_time = active_batch.next_draw_time
            if next_draw_time is None:
                next_draw_time = get_valid_until_date_for_kenya_games().strftime("%I:%M %p")

            kwargs["game_pin"] = game_pin
            kwargs["game_play_id"] = game_play_id
            kwargs["GAME_TYPE"] = active_batch.game_type
            kwargs["sales_date"] = sales_date.strftime("%Y-%m-%d")
            kwargs["valid_until_date"] = next_draw_time
            kwargs["draw_time"] = next_draw_time
            kwargs["agent_id"] = f"winwise-{agent_instance.id}"
            kwargs["batch_name"] = active_batch.batch_name
            kwargs["batch_number"] = active_batch.batch_number

            agent_commission_percentage = AfricaLottoConstants.get_agent_commission_percentage()
            agent_commission = 0

            lottery_tickets = kwargs["lottery_data"]
            for lottery_ticket in lottery_tickets:
                ticket = lottery_ticket.get("lucky_number")
                ticket = ",".join(str(i) for i in ticket)

                bottom_ticket = None
                if lottery_ticket.get("bottom_ticket"):
                    bottom_ticket = lottery_ticket.get("bottom_ticket")
                    bottom_ticket = ",".join(str(i) for i in bottom_ticket)

                amount_paid = lottery_ticket.get("purchase_amount")
                if agent_instance.agent_type == "LOTTO_AGENT":

                    agent_commission = round(amount_paid * agent_commission_percentage, 2)

                    # amount_paid = amount_paid - agent_commission

                    rto = AfricaLottoConstants.get_rto_percentage()
                    rtp = AfricaLottoConstants.get_rtp_percentage()

                    rto_amount = amount_paid * rto
                    rtp_amount = amount_paid * rtp

                    if agent_instance.business_development_agent is not None:
                        business_development_agent_commission_percentage = (
                            AgentConstantVariables.get_company_commission_percentage_on_business_development_agent()
                        )
                        business_development_agent_commission = round(
                            rto_amount * business_development_agent_commission_percentage, 2
                        )
                        rto_amount = rto_amount - business_development_agent_commission

                    AgentWallet.reward_commission(
                        agent_id=agent_instance.id,
                        game_play_amount=amount_paid,
                        commission_type="COMMISSION_ON_GAME_PLAY",
                        game_type=AfricaLottoGameType.KENYA_LOTTO,
                        rto_amount=rto_amount,
                    )

                else:

                    rto = AfricaLottoConstants.get_rto_percentage()
                    rtp = AfricaLottoConstants.get_rtp_percentage()

                    rto_amount = amount_paid * rto
                    rtp_amount = amount_paid * rtp

                    if agent_instance.business_development_agent is not None:
                        business_development_agent_commission_percentage = (
                            AgentConstantVariables.get_company_commission_percentage_on_business_development_agent()
                        )
                        business_development_agent_commission = round(
                            rto_amount * business_development_agent_commission_percentage, 2
                        )
                        rto_amount = rto_amount - business_development_agent_commission

                # RtoWallet().update_rto_wallet_amount(
                #     amount=rto_amount,
                #     phone_number=rto_amount,
                #     wallet_type="DEFAULT",
                # )

                vertical_lead_commission = 0
                supervisor_commission = 0

                ## calculate veritical lead and supervisor commission
                if agent_instance is not None:
                    if agent_instance.supervisor is not None:
                        supervisor_commission = round(
                            rto_amount * AgentConstantVariables.get_supervisor_commission_percentage(), 2
                        )

                        SupervisorWallet.fund_commission_wallet(
                            supervisor_phone_number=agent_instance.supervisor.phone,
                            amount=supervisor_commission,
                            transaction_type="CREDIT",
                            lottery_type=AfricaLottoGameType.KENYA_LOTTO,
                        )

                        if agent_instance.supervisor.vertical_lead is not None:
                            vertical_lead_commission = round(
                                rto_amount * AgentConstantVariables.get_vertical_lead_commission_percentage(), 2
                            )

                            LottoVerticalLeadWallet.fund_commission_wallet(
                                vertical_lead_phone_number=agent_instance.supervisor.vertical_lead.phone,
                                amount=vertical_lead_commission,
                                transaction_type="CREDIT",
                                lottery_type=AfricaLottoGameType.KENYA_LOTTO,
                            )

                rto_amount = rto_amount - (vertical_lead_commission + supervisor_commission)

                Wallet.fund_wallet(amount=rto_amount, wallet_type="RTO_WALLET", game_type="KENYA_LOTTO")

                Wallet.fund_wallet(amount=rtp_amount, wallet_type="RETAIL_RTP_WALLET", game_type="KENYA_LOTTO")

                _from_lotto_agent = True if agent_instance.terminal_id is not None else False

                GamesDailyActivities.create_record(
                    game_type=AfricaLottoGameType.KENYA_LOTTO,
                    rtp=rtp_amount,
                    rto=rto_amount,
                    from_lotto_agent=_from_lotto_agent,
                )

                try:
                    RetailWalletTransactions.create_debit_record_for_game_play(
                        amount=amount_paid,
                        wallet_value=rtp_amount,
                        rto_value=rto_amount,
                        rtp_value=rtp_amount,
                        game_type=AfricaLottoGameType.KENYA_LOTTO,
                    )
                except:
                    pass

                ticket_instance = cls.objects.create(
                    user_phone_number=kwargs.get("phone_number"),
                    agent_phone_number=agent_instance.phone,
                    agent_name=agent_instance.full_name,
                    batch=active_batch,
                    game_play_id=game_play_id,
                    game_pin=game_pin,
                    lucky_number=ticket,
                    bottom_ticket=bottom_ticket,
                    stake_per_line=lottery_ticket.get("stake_per_line", 0),
                    purchase_amount=lottery_ticket.get("purchase_amount"),
                    total_game_stake_amount=lottery_ticket.get("total_stake", 0),
                    potential_winnings=lottery_ticket.get("potential_winnings"),
                    multiplier=lottery_ticket.get("multiplier", 0),
                    winning_scenarios=lottery_ticket.get("winning_scenarios"),
                    lottery_type=lottery_ticket.get("lottery_type"),
                    channel=AfricaLottoChannel.POS_AGENT,
                    game_type=AfricaLottoGameType.KENYA_LOTTO,
                    paid=True,
                    rtp=rtp_amount,
                    rto=rto_amount,
                    commission_percentage=agent_commission_percentage,
                    commission=agent_commission,
                    double_chance=lottery_ticket.get("double_chance", False),
                )

                try:
                    ReturnToPlayerAndReturnToOwnerAnalytics.add_or_create_record(
                        game_type="KENYA_LOTTO", rtp=rtp_amount, rto=rto_amount
                    )
                except:
                    pass

                GeneralRetailLottoGames.create_record(
                    agent_phone_number=agent_instance.phone,
                    agent_name=agent_instance.full_name,
                    agent_email=agent_instance.email,
                    batch_uuid=active_batch.batch_uuid,
                    game_play_id=game_play_id,
                    game_pin=game_pin,
                    lucky_number=ticket,
                    purchase_amount=ticket_instance.purchase_amount,
                    lotto_db_id=ticket_instance.id,
                    paid=ticket_instance.paid,
                    number_of_ticket=ticket_instance.number_of_ticket,
                    multiplier=ticket_instance.multiplier,
                    double_chance=ticket_instance.double_chance,
                    bottom_ticket=ticket_instance.bottom_ticket,
                    rtp=ticket_instance.rtp,
                    rto=ticket_instance.rto,
                    commission_percentage=ticket_instance.commission_percentage,
                    pool=ticket_instance.pool,
                    type_of_agent=agent_instance.agent_type,
                    ticket_type=ticket_instance.lottery_type,
                    lotto_game_type=ticket_instance.game_type,
                    potential_winnings=ticket_instance.potential_winnings,
                )

            return True, "success", kwargs

    @classmethod
    def create_kenya_30_lotto_ticket(cls, agent_instance: Agent, **kwargs):
        """
        Create a KENYA Lotto ticket with associated agent wallet transaction.

        This method handles the complete flow of creating a Ghana Lotto ticket:
        1. Generates a unique game ID
        2. Charges the agent's wallet
        3. Creates lottery tickets in the active batch
        4. Processes multiple lottery numbers and scenarios

        Args:
            agent_instance (Agent): The agent creating the ticket
            **kwargs: Additional parameters including:
                - total_amount (float): Total amount to charge
                - phone_number (str): Customer phone number
                - lottery_data (list[dict]): List of lottery ticket data containing:
                    - lucky_number (list[int]): Main lottery numbers
                    - bottom_ticket (list[int], optional): Secondary lottery numbers
                    - stake_per_line (float): Stake amount per line
                    - purchase_amount (float): Total purchase amount
                    - total_stake (float): Total stake amount
                    - potential_winnings (float): Potential winning amount
                    - multiplier (int): Winning multiplier
                    - winning_scenarios (dict): Possible winning scenarios
                    - lottery_type (str): Type of lottery

        Returns:
            tuple[bool, str, dict | None]: Tuple containing:
                - bool: Success status
                - str: Status message
                - dict | None: Game data if successful, None if failed

        Raises:
            No exceptions are raised; all errors are returned in the tuple
        """

        # print(kwargs)

        # charge agent wallet and play game

        with transaction.atomic():
            from main.api.api_lottery_helpers import generate_game_play_id

            game_play_id = generate_game_play_id()

            pos_agent_helper = PosAgentHelper(
                agent_instance=agent_instance,
                amount=kwargs.get("total_amount"),
                pin="1",
            )

            charge_response = pos_agent_helper.handle_agent_charge_and_lottery_play(
                lottery_qs=[1],
                return_after_successfull_charge=True,
                _game_play_id=game_play_id,
                lottery_type="KENYA_30_LOTTO",
            )

            if charge_response != "success":
                if not isinstance(charge_response, dict):
                    print("charge_response", charge_response)
                    raise Exception("An error occured while charging agent wallet")

                if charge_response.get("message") != "success":
                    raise Exception(charge_response.get("message"))

            sales_date = datetime.now().date()
            current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

            current_time_cutoff = current_time.time()
            cutoff_time = time(21, 45)

            if current_time_cutoff <= cutoff_time:
                # get lottery batch
                active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_30_LOTTO)

                # check the draw time for the batch and make sure this game play is not exactly at the time or the draw time should have drawn
                batch_draw_date = f"{sales_date.strftime('%Y-%m-%d')} {active_batch.next_draw_time}"
                print("batch_draw_date", batch_draw_date, "\n\n\n")
                batch_draw_date = datetime.strptime(batch_draw_date, "%Y-%m-%d %I:%M %p")

                batch_draw_date = pytz.timezone(settings.TIME_ZONE).localize(batch_draw_date)
                current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
                if current_time >= batch_draw_date:
                    active_batch.batch_status = False
                    active_batch.save()

                    active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_30_LOTTO)
            else:
                active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_30_LOTTO)

                # Check if next draw time is 8:15 AM
                if active_batch.next_draw_time.strip() != "08:15 AM":

                    active_batch.batch_status = False
                    active_batch.save()

                    # Get a new active batch
                    active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_30_LOTTO)

            game_pin = cls.get_game_pin()

            print("active_batch", active_batch, "\n\n")

            next_draw_time = active_batch.next_draw_time
            if next_draw_time is None:
                next_draw_time = get_valid_until_date_for_kenya_30_games().strftime("%I:%M %p")

            kwargs["game_pin"] = game_pin
            kwargs["game_play_id"] = game_play_id
            kwargs["GAME_TYPE"] = active_batch.game_type
            kwargs["sales_date"] = sales_date.strftime("%Y-%m-%d")
            kwargs["valid_until_date"] = next_draw_time
            kwargs["draw_time"] = next_draw_time
            kwargs["agent_id"] = f"winwise-{agent_instance.id}"
            kwargs["batch_name"] = active_batch.batch_name
            kwargs["batch_number"] = active_batch.batch_number

            agent_commission_percentage = AfricaLottoConstants.get_agent_commission_percentage()
            agent_commission = 0

            lottery_tickets = kwargs["lottery_data"]
            for lottery_ticket in lottery_tickets:
                ticket = lottery_ticket.get("lucky_number")
                ticket = ",".join(str(i) for i in ticket)

                bottom_ticket = None
                if lottery_ticket.get("bottom_ticket"):
                    bottom_ticket = lottery_ticket.get("bottom_ticket")
                    bottom_ticket = ",".join(str(i) for i in bottom_ticket)

                amount_paid = lottery_ticket.get("purchase_amount")
                if agent_instance.agent_type == "LOTTO_AGENT":

                    agent_commission = round(amount_paid * agent_commission_percentage, 2)

                    # amount_paid = amount_paid - agent_commission

                    rto = AfricaLottoConstants.get_rto_percentage()
                    rtp = AfricaLottoConstants.get_rtp_percentage()

                    rto_amount = amount_paid * rto
                    rtp_amount = amount_paid * rtp

                    if agent_instance.business_development_agent is not None:
                        business_development_agent_commission_percentage = (
                            AgentConstantVariables.get_company_commission_percentage_on_business_development_agent()
                        )
                        business_development_agent_commission = round(
                            rto_amount * business_development_agent_commission_percentage, 2
                        )
                        rto_amount = rto_amount - business_development_agent_commission

                    AgentWallet.reward_commission(
                        agent_id=agent_instance.id,
                        game_play_amount=amount_paid,
                        commission_type="COMMISSION_ON_GAME_PLAY",
                        game_type=AfricaLottoGameType.KENYA_30_LOTTO,
                        rto_amount=rto_amount,
                    )

                else:

                    rto = AfricaLottoConstants.get_rto_percentage()
                    rtp = AfricaLottoConstants.get_rtp_percentage()

                    rto_amount = amount_paid * rto
                    rtp_amount = amount_paid * rtp

                    if agent_instance.business_development_agent is not None:
                        business_development_agent_commission_percentage = (
                            AgentConstantVariables.get_company_commission_percentage_on_business_development_agent()
                        )
                        business_development_agent_commission = round(
                            rto_amount * business_development_agent_commission_percentage, 2
                        )
                        rto_amount = rto_amount - business_development_agent_commission

                # RtoWallet().update_rto_wallet_amount(
                #     amount=rto_amount,
                #     phone_number=rto_amount,
                #     wallet_type="DEFAULT",
                # )

                vertical_lead_commission = 0
                supervisor_commission = 0

                ## calculate veritical lead and supervisor commission
                if agent_instance is not None:
                    if agent_instance.supervisor is not None:
                        supervisor_commission = round(
                            rto_amount * AgentConstantVariables.get_supervisor_commission_percentage(), 2
                        )

                        SupervisorWallet.fund_commission_wallet(
                            supervisor_phone_number=agent_instance.supervisor.phone,
                            amount=supervisor_commission,
                            transaction_type="CREDIT",
                            lottery_type=AfricaLottoGameType.KENYA_30_LOTTO,
                        )

                        if agent_instance.supervisor.vertical_lead is not None:
                            vertical_lead_commission = round(
                                rto_amount * AgentConstantVariables.get_vertical_lead_commission_percentage(), 2
                            )

                            LottoVerticalLeadWallet.fund_commission_wallet(
                                vertical_lead_phone_number=agent_instance.supervisor.vertical_lead.phone,
                                amount=vertical_lead_commission,
                                transaction_type="CREDIT",
                                lottery_type=AfricaLottoGameType.KENYA_30_LOTTO,
                            )

                rto_amount = rto_amount - (vertical_lead_commission + supervisor_commission)

                Wallet.fund_wallet(amount=rto_amount, wallet_type="RTO_WALLET", game_type="KENYA_LOTTO")

                Wallet.fund_wallet(amount=rtp_amount, wallet_type="RETAIL_RTP_WALLET", game_type="KENYA_LOTTO")

                _from_lotto_agent = True if agent_instance.terminal_id is not None else False

                GamesDailyActivities.create_record(
                    game_type=AfricaLottoGameType.KENYA_30_LOTTO,
                    rtp=rtp_amount,
                    rto=rto_amount,
                    from_lotto_agent=_from_lotto_agent,
                )

                try:
                    RetailWalletTransactions.create_debit_record_for_game_play(
                        amount=amount_paid,
                        wallet_value=rtp_amount,
                        rto_value=rto_amount,
                        rtp_value=rtp_amount,
                        game_type=AfricaLottoGameType.KENYA_30_LOTTO,
                    )
                except:
                    pass

                ticket_instance = cls.objects.create(
                    user_phone_number=kwargs.get("phone_number"),
                    agent_phone_number=agent_instance.phone,
                    agent_name=agent_instance.full_name,
                    batch=active_batch,
                    game_play_id=game_play_id,
                    game_pin=game_pin,
                    lucky_number=ticket,
                    bottom_ticket=bottom_ticket,
                    stake_per_line=lottery_ticket.get("stake_per_line", 0),
                    purchase_amount=lottery_ticket.get("purchase_amount"),
                    total_game_stake_amount=lottery_ticket.get("total_stake", 0),
                    potential_winnings=lottery_ticket.get("potential_winnings"),
                    multiplier=lottery_ticket.get("multiplier", 0),
                    winning_scenarios=lottery_ticket.get("winning_scenarios"),
                    lottery_type=lottery_ticket.get("lottery_type"),
                    channel=AfricaLottoChannel.POS_AGENT,
                    game_type=AfricaLottoGameType.KENYA_30_LOTTO,
                    paid=True,
                    rtp=rtp_amount,
                    rto=rto_amount,
                    commission_percentage=agent_commission_percentage,
                    commission=agent_commission,
                    double_chance=lottery_ticket.get("double_chance", False),
                )

                try:
                    ReturnToPlayerAndReturnToOwnerAnalytics.add_or_create_record(
                        game_type="KENYA_30_LOTTO", rtp=rtp_amount, rto=rto_amount
                    )
                except:
                    pass

                GeneralRetailLottoGames.create_record(
                    agent_phone_number=agent_instance.phone,
                    agent_name=agent_instance.full_name,
                    agent_email=agent_instance.email,
                    batch_uuid=active_batch.batch_uuid,
                    game_play_id=game_play_id,
                    game_pin=game_pin,
                    lucky_number=ticket,
                    purchase_amount=ticket_instance.purchase_amount,
                    lotto_db_id=ticket_instance.id,
                    paid=ticket_instance.paid,
                    number_of_ticket=ticket_instance.number_of_ticket,
                    multiplier=ticket_instance.multiplier,
                    double_chance=ticket_instance.double_chance,
                    bottom_ticket=ticket_instance.bottom_ticket,
                    rtp=ticket_instance.rtp,
                    rto=ticket_instance.rto,
                    commission_percentage=ticket_instance.commission_percentage,
                    pool=ticket_instance.pool,
                    type_of_agent=agent_instance.agent_type,
                    ticket_type=ticket_instance.lottery_type,
                    lotto_game_type=ticket_instance.game_type,
                    potential_winnings=ticket_instance.potential_winnings,
                )

            return True, "success", kwargs

    @classmethod
    def create_k_now_lotto_ticket(cls, agent_instance: Agent, **kwargs):

        from africa_lotto.serializers import AfricaLottoDrawNumbersSerializers

        """
        Create a KENYA Lotto ticket with associated agent wallet transaction.

        This method handles the complete flow of creating a Ghana Lotto ticket:
        1. Generates a unique game ID
        2. Charges the agent's wallet
        3. Creates lottery tickets in the active batch
        4. Processes multiple lottery numbers and scenarios

        Args:
            agent_instance (Agent): The agent creating the ticket
            **kwargs: Additional parameters including:
                - total_amount (float): Total amount to charge
                - phone_number (str): Customer phone number
                - lottery_data (list[dict]): List of lottery ticket data containing:
                    - lucky_number (list[int]): Main lottery numbers
                    - bottom_ticket (list[int], optional): Secondary lottery numbers
                    - stake_per_line (float): Stake amount per line
                    - purchase_amount (float): Total purchase amount
                    - total_stake (float): Total stake amount
                    - potential_winnings (float): Potential winning amount
                    - multiplier (int): Winning multiplier
                    - winning_scenarios (dict): Possible winning scenarios
                    - lottery_type (str): Type of lottery

        Returns:
            tuple[bool, str, dict | None]: Tuple containing:
                - bool: Success status
                - str: Status message
                - dict | None: Game data if successful, None if failed

        Raises:
            No exceptions are raised; all errors are returned in the tuple
        """

        # print(kwargs)

        # charge agent wallet and play game

        with transaction.atomic():
            from main.api.api_lottery_helpers import generate_game_play_id

            game_play_id = generate_game_play_id()

            pos_agent_helper = PosAgentHelper(
                agent_instance=agent_instance,
                amount=kwargs.get("total_amount"),
                pin="1",
            )

            charge_response = pos_agent_helper.handle_agent_charge_and_lottery_play(
                lottery_qs=[1],
                return_after_successfull_charge=True,
                _game_play_id=game_play_id,
                lottery_type="K_NOW",
            )

            if charge_response != "success":
                if not isinstance(charge_response, dict):
                    print("charge_response", charge_response)
                    raise Exception("An error occured while charging agent wallet")

                if charge_response.get("message") != "success":
                    raise Exception(charge_response.get("message"))

            sales_date = datetime.now().date()

            # get lottery batch
            active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.K_NOW)

            # check the draw time for the batch and make sure this game play is not exactly at the time or the draw time should have drawn
            batch_draw_date = f"{sales_date.strftime('%Y-%m-%d')} {active_batch.next_draw_time}"
            batch_draw_date = datetime.strptime(batch_draw_date, "%Y-%m-%d %I:%M %p")
            current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
            batch_draw_date = pytz.timezone(settings.TIME_ZONE).localize(batch_draw_date)

            current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

            if current_time >= batch_draw_date:
                active_batch.batch_status = False
                active_batch.save()

                active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.K_NOW)

            game_pin = cls.get_game_pin()

            next_draw_time = active_batch.next_draw_time
            if next_draw_time is None:
                next_draw_time = get_valid_until_date_for_k_now_games().strftime("%I:%M %p")

            kwargs["game_pin"] = game_pin
            kwargs["game_play_id"] = game_play_id
            kwargs["GAME_TYPE"] = active_batch.game_type
            kwargs["sales_date"] = sales_date.strftime("%Y-%m-%d")
            kwargs["valid_until_date"] = next_draw_time
            kwargs["draw_time"] = next_draw_time
            kwargs["agent_id"] = f"winwise-{agent_instance.id}"
            kwargs["batch_name"] = active_batch.batch_name
            kwargs["batch_number"] = active_batch.batch_number

            previous_qs = (
                AfricaLottoDrawNumbers.objects.filter(game_type=AfricaLottoGameType.KENYA_LOTTO)
                .distinct("batch_id")
                .order_by("batch_id", "-id")
            )
            previous_qs = previous_qs[:3]
            serialized_previous_data = AfricaLottoDrawNumbersSerializers(previous_qs, many=True).data
            kwargs["previous_draw_data"] = serialized_previous_data

            agent_commission_percentage = AfricaLottoConstants.get_agent_commission_percentage()
            agent_commission = 0

            lottery_tickets = kwargs["lottery_data"]
            for lottery_ticket in lottery_tickets:
                ticket = lottery_ticket.get("lucky_number")
                ticket = ",".join(str(i) for i in ticket)

                bottom_ticket = None
                if lottery_ticket.get("bottom_ticket"):
                    bottom_ticket = lottery_ticket.get("bottom_ticket")
                    bottom_ticket = ",".join(str(i) for i in bottom_ticket)

                amount_paid = lottery_ticket.get("purchase_amount")
                if agent_instance.agent_type == "LOTTO_AGENT":

                    agent_commission = round(amount_paid * agent_commission_percentage, 2)

                    # amount_paid = amount_paid - agent_commission

                    rto = AfricaLottoConstants.get_k_now_rto_percentage()
                    rtp = AfricaLottoConstants.get_k_now_rtp_percentage()

                    rto_amount = amount_paid * rto
                    rtp_amount = amount_paid * rtp

                    if agent_instance.business_development_agent is not None:
                        business_development_agent_commission_percentage = (
                            AgentConstantVariables.get_company_commission_percentage_on_business_development_agent()
                        )
                        business_development_agent_commission = round(
                            rto_amount * business_development_agent_commission_percentage, 2
                        )
                        rto_amount = rto_amount - business_development_agent_commission

                    AgentWallet.reward_commission(
                        agent_id=agent_instance.id,
                        game_play_amount=amount_paid,
                        commission_type="COMMISSION_ON_GAME_PLAY",
                        game_type=AfricaLottoGameType.K_NOW,
                        rto_amount=rto_amount,
                    )

                else:

                    rto = AfricaLottoConstants.get_k_now_rto_percentage()
                    rtp = AfricaLottoConstants.get_k_now_rtp_percentage()

                    rto_amount = amount_paid * rto
                    rtp_amount = amount_paid * rtp

                    if agent_instance.business_development_agent is not None:
                        business_development_agent_commission_percentage = (
                            AgentConstantVariables.get_company_commission_percentage_on_business_development_agent()
                        )
                        business_development_agent_commission = round(
                            rto_amount * business_development_agent_commission_percentage, 2
                        )
                        rto_amount = rto_amount - business_development_agent_commission

                # RtoWallet().update_rto_wallet_amount(
                #     amount=rto_amount,
                #     phone_number=rto_amount,
                #     wallet_type="DEFAULT",
                # )

                vertical_lead_commission = 0
                supervisor_commission = 0

                ## calculate veritical lead and supervisor commission
                if agent_instance is not None:
                    if agent_instance.supervisor is not None:
                        supervisor_commission = round(
                            rto_amount * AgentConstantVariables.get_supervisor_commission_percentage(), 2
                        )

                        SupervisorWallet.fund_commission_wallet(
                            supervisor_phone_number=agent_instance.supervisor.phone,
                            amount=supervisor_commission,
                            transaction_type="CREDIT",
                            lottery_type=AfricaLottoGameType.K_NOW,
                        )

                        if agent_instance.supervisor.vertical_lead is not None:
                            vertical_lead_commission = round(
                                rto_amount * AgentConstantVariables.get_vertical_lead_commission_percentage(), 2
                            )

                            LottoVerticalLeadWallet.fund_commission_wallet(
                                vertical_lead_phone_number=agent_instance.supervisor.vertical_lead.phone,
                                amount=vertical_lead_commission,
                                transaction_type="CREDIT",
                                lottery_type=AfricaLottoGameType.K_NOW,
                            )

                rto_amount = rto_amount - (vertical_lead_commission + supervisor_commission)

                Wallet.fund_wallet(amount=rto_amount, wallet_type="RTO_WALLET", game_type="KENYA_LOTTO")

                Wallet.fund_wallet(amount=rtp_amount, wallet_type="RETAIL_RTP_WALLET", game_type="KENYA_LOTTO")

                _from_lotto_agent = True if agent_instance.terminal_id is not None else False

                GamesDailyActivities.create_record(
                    game_type=AfricaLottoGameType.K_NOW,
                    rtp=rtp_amount,
                    rto=rto_amount,
                    from_lotto_agent=_from_lotto_agent,
                )

                try:
                    RetailWalletTransactions.create_debit_record_for_game_play(
                        amount=amount_paid,
                        wallet_value=rtp_amount,
                        rto_value=rto_amount,
                        rtp_value=rtp_amount,
                        game_type=AfricaLottoGameType.KENYA_30_LOTTO,
                    )
                except:
                    pass

                ticket_instance = cls.objects.create(
                    user_phone_number=kwargs.get("phone_number"),
                    agent_phone_number=agent_instance.phone,
                    agent_name=agent_instance.full_name,
                    batch=active_batch,
                    game_play_id=game_play_id,
                    game_pin=game_pin,
                    lucky_number=ticket,
                    bottom_ticket=bottom_ticket,
                    stake_per_line=lottery_ticket.get("stake_per_line", 0),
                    purchase_amount=lottery_ticket.get("purchase_amount"),
                    total_game_stake_amount=lottery_ticket.get("total_stake", 0),
                    potential_winnings=lottery_ticket.get("potential_winnings"),
                    multiplier=lottery_ticket.get("multiplier", 0),
                    winning_scenarios=lottery_ticket.get("winning_scenarios"),
                    lottery_type=lottery_ticket.get("lottery_type"),
                    channel=AfricaLottoChannel.POS_AGENT,
                    game_type=AfricaLottoGameType.K_NOW,
                    paid=True,
                    rtp=rtp_amount,
                    rto=rto_amount,
                    commission_percentage=agent_commission_percentage,
                    commission=agent_commission,
                    double_chance=lottery_ticket.get("double_chance", False),
                )

                try:
                    ReturnToPlayerAndReturnToOwnerAnalytics.add_or_create_record(
                        game_type="K_NOW", rtp=rtp_amount, rto=rto_amount
                    )
                except:
                    pass

                GeneralRetailLottoGames.create_record(
                    agent_phone_number=agent_instance.phone,
                    agent_name=agent_instance.full_name,
                    agent_email=agent_instance.email,
                    batch_uuid=active_batch.batch_uuid,
                    game_play_id=game_play_id,
                    game_pin=game_pin,
                    lucky_number=ticket,
                    purchase_amount=ticket_instance.purchase_amount,
                    lotto_db_id=ticket_instance.id,
                    paid=ticket_instance.paid,
                    number_of_ticket=ticket_instance.number_of_ticket,
                    multiplier=ticket_instance.multiplier,
                    double_chance=ticket_instance.double_chance,
                    bottom_ticket=ticket_instance.bottom_ticket,
                    rtp=ticket_instance.rtp,
                    rto=ticket_instance.rto,
                    commission_percentage=ticket_instance.commission_percentage,
                    pool=ticket_instance.pool,
                    type_of_agent=agent_instance.agent_type,
                    ticket_type=ticket_instance.lottery_type,
                    lotto_game_type=ticket_instance.game_type,
                    potential_winnings=ticket_instance.potential_winnings,
                )

            return True, "success", kwargs

    @classmethod
    def get_game_details(cls, game_id):
        queryset_data = cls.objects.filter(game_play_id=game_id)
        if not queryset_data.exists():
            return {}

        instance = queryset_data.first()

        total_stake = queryset_data.aggregate(Sum("purchase_amount"))["purchase_amount__sum"] or 0
        total_winning = queryset_data.aggregate(Sum("potential_winnings"))["potential_winnings__sum"] or 0

        tickets_details = []

        data = {
            "status": "Accepted",
            "agent_id": instance.agent_phone_number,
            "ticket_owner": instance.agent_phone_number,
            "game_id": instance.game_play_id,
            "pin": instance.game_pin,
            "game_type": instance.game_type,
            "date": str(instance.created_at),
            "stake_per_pick": instance.stake_per_line if instance.stake_per_line > 0 else instance.purchase_amount,
            "total_stake": total_stake,
            "potential_winning": total_winning,
            "total_ticket": len(queryset_data),
            "batch_number": instance.batch.batch_number,
            "system_pick": [],
        }

        for ticket in queryset_data:
            if ticket.batch.draw_status is False:
                int_ticket = [int(i) for i in ticket.lucky_number.split(",")]
                tickets_details.append(
                    {
                        "ticket": int_ticket,
                        "status": "pending draw",
                    }
                )
            else:
                int_ticket = [int(i) for i in ticket.lucky_number.split(",")]
                ticket_status = "lost"

                if AfricaLottoDrawDataWinners.objects.filter(
                    game_play_id__contains=instance.game_play_id, lucky_number=ticket.lucky_number
                ).exists():
                    ticket_status = "won"

                tickets_details.append(
                    {
                        "ticket": int_ticket,
                        "status": ticket_status,
                    }
                )

        data["tickets"] = tickets_details

        return data


class AfricaLottoDrawNumbers(models.Model):
    game_type = models.CharField(
        max_length=50, choices=AfricaLottoGameType.choices, default=AfricaLottoGameType.GHANA_LOTTO
    )
    draw_date = models.CharField(max_length=100, blank=True, null=True)
    draw_name = models.CharField(max_length=100, blank=True, null=True)
    draw_number = models.CharField(max_length=100)
    machine_number = models.CharField(max_length=100, blank=True, null=True)
    batch_date = models.CharField(max_length=100, blank=True, null=True)
    batch_id = models.CharField(max_length=100, blank=True, null=True, unique=True)
    game_batch = models.ForeignKey(AfricaLottoBatch, on_delete=models.PROTECT, blank=True, null=True)
    batch_drawn = models.BooleanField(default=False)
    manually_filtered_winnings = models.BooleanField(default=False)
    batch_number = models.CharField(max_length=200, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AFRICA LOTTO DRAW NUMBER"
        verbose_name_plural = "AFRICA LOTTO DRAW NUMBERS"


class AfricaLottoDrawData(models.Model):
    game_type = models.CharField(max_length=50, choices=AfricaLottoGameType.choices)
    rtp = models.FloatField(default=0)
    raw_rtp = models.FloatField(default=0)
    running_balance = models.FloatField(default=0)
    boost_amount = models.FloatField(default=0)
    topup_amount = models.FloatField(default=0)
    game_plays = models.TextField()
    double_chance_picks_list = ArrayField(models.IntegerField(), null=True, blank=True)
    batch = models.CharField(max_length=300)
    draw_response = models.TextField(blank=True, null=True)
    filtered_winners = models.TextField(blank=True, null=True)
    winning_data_report = models.TextField(blank=True, null=True)
    manually_processed_winners = models.BooleanField(default=False)
    re_running_previous_day_draw = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AFRICAN LOTTO DRAW DATA"
        verbose_name_plural = "AFRICAN LOTTO DRAW DATAS"


class AfricaLottoDrawDataWinners(models.Model):
    user_phone_number = models.CharField(max_length=20, blank=True, null=True)
    user_name = models.CharField(max_length=100, blank=True, null=True)
    agent_phone_number = models.CharField(max_length=20, blank=True, null=True)
    agent_name = models.CharField(max_length=100, blank=True, null=True)
    batch = models.CharField(max_length=300)
    amount_won = models.FloatField(default=0.0)
    game_play_id = models.CharField(max_length=100)
    game_pin = models.CharField(max_length=100)
    lucky_number = models.CharField(max_length=100)
    bottom_ticket = models.CharField(max_length=100, blank=True, null=True)
    channel = models.CharField(max_length=50, choices=AfricaLottoChannel.choices)
    lottery_type = models.CharField(max_length=50, choices=AfricaLotteryType.choices)
    game_type = models.CharField(max_length=50, choices=AfricaLottoGameType.choices)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AFRICAN LOTTO DRAW WINNER"
        verbose_name_plural = "AFRICAN LOTTO DRAW WINNERS"

    @classmethod
    def create_winner(cls, **kwargs):
        return cls.objects.create(**kwargs)


class BonusFrequencyChoices(models.TextChoices):
    """Frequency choices for bonus calendar templates."""

    DAILY = "DAILY", _("Daily")
    WEEKLY = "WEEKLY", _("Weekly")
    MONTHLY = "MONTHLY", _("Monthly")


class BonusCalendarEntry(models.Model):
    """
    Model to store individual bonus calendar entries for specific dates and game types.

    This model represents a single bonus entry that can be applied to a specific
    game type on a specific date. When a draw is running, the system will check
    for any undelivered bonuses for that date and game type, and randomly add
    the bonus amount to the running balance.
    """

    date = models.DateField(help_text="The date when this bonus should be applied")
    game_type = models.CharField(
        max_length=50, choices=AfricaLottoGameType.choices, help_text="The game type this bonus applies to"
    )
    amount = models.FloatField(help_text="The bonus amount to be added to the running balance")
    delivered = models.BooleanField(default=False, help_text="Whether this bonus has been delivered/applied")
    delivered_at = models.DateTimeField(blank=True, null=True, help_text="When this bonus was delivered")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "BONUS CALENDAR ENTRY"
        verbose_name_plural = "BONUS CALENDAR ENTRIES"
        unique_together = ["date", "game_type"]
        ordering = ["-date", "game_type"]

    def __str__(self):
        status = "Delivered" if self.delivered else "Pending"
        return f"{self.date} - {self.game_type} - {self.amount} ({status})"

    @classmethod
    def get_pending_bonuses_for_date_and_game(cls, date, game_type):
        """
        Get all pending bonuses for a specific date and game type.

        Args:
            date: The date to check for bonuses
            game_type: The game type to check for bonuses

        Returns:
            QuerySet of pending BonusCalendarEntry objects
        """
        return cls.objects.filter(date=date, game_type=game_type, delivered=False)

    @classmethod
    def apply_bonus_to_running_balance(cls, date, game_type):
        """
        Apply any pending bonuses for the given date and game type to the running balance.

        This method will:
        1. Find all pending bonuses for the date and game type
        2. Randomly select one bonus to apply (if multiple exist)
        3. Add the bonus amount to the appropriate running balance
        4. Mark the bonus as delivered

        Args:
            date: The date to check for bonuses
            game_type: The game type to apply bonuses for

        Returns:
            float: The bonus amount applied, or 0 if no bonus was applied
        """
        pending_bonuses = cls.get_pending_bonuses_for_date_and_game(date, game_type)

        if not pending_bonuses.exists():
            return 0

        # Randomly select one bonus if multiple exist
        import random

        selected_bonus = random.choice(list(pending_bonuses))

        # Update the appropriate running balance based on game type
        constants = AfricaLottoConstants.objects.first()
        if constants:
            if game_type == AfricaLottoGameType.KENYA_LOTTO:
                AfricaLottoConstants.objects.all().update(
                    kenya_running_balance=models.F("kenya_running_balance") + selected_bonus.amount
                )
            elif game_type == AfricaLottoGameType.KENYA_30_LOTTO:
                AfricaLottoConstants.objects.all().update(
                    kenya30_running_balance=models.F("kenya30_running_balance") + selected_bonus.amount
                )
            elif game_type == AfricaLottoGameType.K_NOW:
                AfricaLottoConstants.objects.all().update(
                    k_now_running_balance=models.F("k_now_running_balance") + selected_bonus.amount
                )

        # Mark the bonus as delivered
        selected_bonus.delivered = True
        selected_bonus.delivered_at = timezone.now()
        selected_bonus.save()

        return selected_bonus.amount

    def mark_as_delivered(self):
        """Mark this bonus entry as delivered."""
        self.delivered = True
        self.delivered_at = timezone.now()
        self.save()


class BonusCalendarTemplate(models.Model):
    """
    Model to create bulk bonus calendar entries based on templates.

    This model allows administrators to define templates for creating
    multiple bonus entries across a date range with specified frequency.
    """

    name = models.CharField(max_length=200, help_text="Descriptive name for this bonus template")
    game_type = models.CharField(
        max_length=50, choices=AfricaLottoGameType.choices, help_text="The game type this template applies to"
    )
    start_date = models.DateField(help_text="Start date for creating bonus entries")
    end_date = models.DateField(help_text="End date for creating bonus entries")
    amount = models.FloatField(help_text="The bonus amount for each entry")
    frequency = models.CharField(
        max_length=20,
        choices=BonusFrequencyChoices.choices,
        default=BonusFrequencyChoices.DAILY,
        help_text="How often to create bonus entries",
    )
    is_active = models.BooleanField(default=True, help_text="Whether this template is active")
    entries_created = models.BooleanField(
        default=False, help_text="Whether bonus entries have been created from this template"
    )
    randomize = models.BooleanField(default=False, help_text="Whether the bonus amount should be random")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "BONUS CALENDAR TEMPLATE"
        verbose_name_plural = "BONUS CALENDAR TEMPLATES"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.name} - {self.game_type} ({self.start_date} to {self.end_date})"

    def clean(self):
        """Validate that end_date is after start_date."""
        from django.core.exceptions import ValidationError

        if self.end_date and self.start_date and self.end_date < self.start_date:
            raise ValidationError("End date must be after start date.")

    def generate_dates(self):
        """
        Generate a list of dates based on the template's frequency.

        Returns:
            list: List of date objects
        """
        dates = []
        current_date = self.start_date

        while current_date <= self.end_date:
            dates.append(current_date)

            if self.frequency == BonusFrequencyChoices.DAILY:
                current_date += timezone.timedelta(days=1)
            elif self.frequency == BonusFrequencyChoices.WEEKLY:
                current_date += timezone.timedelta(weeks=1)
            elif self.frequency == BonusFrequencyChoices.MONTHLY:
                current_date += relativedelta(months=1)
            else:
                break  # Unknown frequency, stop to prevent infinite loop

        return dates

    def create_bonus_entries(self, overwrite_existing=False):
        """
        Create BonusCalendarEntry objects based on this template.

        Args:
            overwrite_existing (bool): Whether to overwrite existing entries

        Returns:
            tuple: (created_count, skipped_count, error_message)
        """
        if self.entries_created and not overwrite_existing:
            return 0, 0, "Entries already created for this template"

        dates = self.generate_dates()
        created_count = 0
        skipped_count = 0
        _amount = [self.amount, 0 if self.randomize else self.amount, 0 if self.randomize else self.amount]
        print([self.amount, 0 if self.randomize else self.amount])
        print(self.randomize)

        for date in dates:
            amount = random.choice(_amount)
            print(f"AMOUNT :::: {amount}")
            try:
                entry, created = BonusCalendarEntry.objects.get_or_create(
                    date=date, game_type=self.game_type, defaults={"amount": amount, "delivered": False}
                )

                if created:
                    created_count += 1
                else:
                    if overwrite_existing and not entry.delivered:
                        entry.amount = amount
                        entry.save()
                        created_count += 1
                    else:
                        skipped_count += 1

            except Exception as e:
                return created_count, skipped_count, f"Error creating entry for {date}: {str(e)}"

        # Mark template as having entries created
        self.entries_created = True
        self.save()

        return created_count, skipped_count, None


class GhanaDrawNumberFetchedDataLog(models.Model):
    fetched_data_log = models.TextField(blank=True, null=True)
    open_ai_prompt = models.TextField(blank=True, null=True)
    open_ai_response = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GHANA DRAW NUMBER FETCHED DATA LOG"
        verbose_name_plural = "GHANA DRAW NUMBER FETCHED DATA LOGS"
