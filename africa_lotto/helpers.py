import string
from datetime import datetime, time, timedelta
from itertools import combinations, product

import pytz
import requests
from bs4 import BeautifulSoup
from django.conf import settings


def calculate_nap_ticket(nap_type, purchase_amount):
    """
    Calculate NAP ticket details including validation, winnings multiplier and potential winnings.

    Args:
        nap_type (int): The NAP type (2-5)
        purchase_amount (float): Amount customer wants to stake

    Returns:
        dict: Ticket details including validation status, multiplier and potential winnings

    Raises:
        ValueError: If purchase amount or NAP type is invalid
    """

    from africa_lotto.models import AfricaLottoConstants

    # NAP multipliers mapping
    nap_multipliers = {2: 240, 3: 2100, 4: 6000, 5: 44000}

    # Validate NAP type
    if nap_type not in nap_multipliers:
        raise ValueError(f"Invalid NAP type. Must be one of {list(nap_multipliers.keys())}")

    minimum_purchase_amount = AfricaLottoConstants.get_minimum_stake()

    # Validate purchase amount
    if not minimum_purchase_amount <= purchase_amount <= 50000:
        raise ValueError("Purchase amount must be between ₦5 and ₦50,000")

    # Get multiplier for this NAP type
    multiplier = nap_multipliers[nap_type]

    # Calculate potential winnings
    potential_winnings = purchase_amount * multiplier

    return {
        "nap_type": f"NAP{nap_type}",
        "purchase_amount": purchase_amount,
        "multiplier": multiplier,
        "potential_winnings": potential_winnings,
        "numbers_to_select": nap_type,
        "total_numbers": 90,
    }


class PermCalculator:
    """Calculator for PERM lottery games"""

    # Predefined number of lines for PERM2 to PERM5
    PERM2_LINES = {
        3: 3,
        4: 6,
        5: 10,
        6: 15,
        7: 21,
        8: 28,
        9: 36,
        10: 45,
        11: 55,
        12: 66,
        13: 78,
        14: 91,
        15: 105,
        16: 120,
        17: 136,
        18: 153,
        19: 171,
        20: 190,
    }

    PERM3_LINES = {
        4: 4,
        5: 10,
        6: 20,
        7: 35,
        8: 56,
        9: 84,
        10: 120,
        11: 165,
        12: 220,
        13: 286,
        14: 364,
        15: 455,
        16: 560,
        17: 680,
        18: 816,
        19: 969,
        20: 1140,
    }

    PERM4_LINES = {
        5: 5,
        6: 15,
        7: 35,
        8: 70,
        9: 126,
        10: 210,
        11: 330,
        12: 495,
        13: 715,
        14: 1001,
        15: 1365,
        16: 1820,
        17: 2380,
        18: 3060,
        19: 3876,
        20: 4845,
    }

    PERM5_LINES = {
        5: 1,
        6: 6,
        7: 21,
        8: 56,
        9: 126,
        10: 252,
        11: 462,
        12: 792,
        13: 1287,
        14: 2002,
        15: 3003,
        16: 4368,
        17: 6188,
        18: 8568,
        19: 11628,
        20: 15504,
    }

    # Winning multipliers (example values - adjust as needed)
    MULTIPLIERS = {"PERM2": 240, "PERM3": 2100, "PERM4": 6000, "PERM5": 44000}

    def __init__(self):
        from africa_lotto.models import AfricaLottoConstants

        minimum_purchase_amount = AfricaLottoConstants.get_perm_minimum_stake()
        self.min_stake = minimum_purchase_amount  # Minimum stake amount in Naira
        self.max_stake = 50000  # Maximum stake amount in Naira

    def validate_input(self, perm_type, num_selected, stake_per_line):
        """Validate input parameters"""
        if perm_type not in ["PERM2", "PERM3", "PERM4", "PERM5"]:
            raise ValueError("Invalid PERM type. Must be 'PERM2', 'PERM3', 'PERM4', or 'PERM5'")

        # Get valid ranges for each PERM type
        valid_ranges = {
            "PERM2": self.PERM2_LINES.keys(),
            "PERM3": self.PERM3_LINES.keys(),
            "PERM4": self.PERM4_LINES.keys(),
            "PERM5": self.PERM5_LINES.keys(),
        }

        if num_selected not in valid_ranges[perm_type]:
            min_num = min(valid_ranges[perm_type])
            max_num = max(valid_ranges[perm_type])
            raise ValueError(f"For {perm_type}, number of selections must be between {min_num} and {max_num}")

        if not self.min_stake <= stake_per_line <= self.max_stake:
            raise ValueError(f"Stake per line must be between ₦{self.min_stake} and ₦{self.max_stake}")

    def get_winning_combinations(self, numbers, perm_type):
        """Generate all possible winning combinations"""
        r = int(perm_type[-1])  # Extract number from PERM type (e.g., 2 from PERM2)
        return list(combinations(sorted(numbers), r))

    def get_num_lines(self, perm_type, num_selected):
        """Get number of lines based on PERM type and number of selections"""
        perm_lines_map = {"PERM2": self.PERM2_LINES, "PERM3": self.PERM3_LINES, "PERM4": self.PERM4_LINES, "PERM5": self.PERM5_LINES}
        return perm_lines_map[perm_type][num_selected]

    def calculate_ticket(self, perm_type, selected_numbers, stake_per_line):
        """
        Calculate ticket details including total cost and potential winnings

        Args:
            perm_type (str): 'PERM2', 'PERM3', 'PERM4', or 'PERM5'
            selected_numbers (list): List of selected numbers
            stake_per_line (float): Stake amount per line

        Returns:
            dict: Ticket details including combinations and costs
        """
        num_selected = len(selected_numbers)
        self.validate_input(perm_type, num_selected, stake_per_line)

        # Get number of lines
        lines = self.get_num_lines(perm_type, num_selected)

        # Calculate costs
        total_cost = lines * stake_per_line
        multiplier = self.MULTIPLIERS[perm_type]
        max_potential_win = total_cost * multiplier

        # Generate combinations
        combinations_list = self.get_winning_combinations(selected_numbers, perm_type)

        return {
            "perm_type": perm_type,
            "selected_numbers": sorted(selected_numbers),
            "num_lines": lines,
            "stake_per_line": stake_per_line,
            "total_cost": total_cost,
            "multiplier": multiplier,
            "max_potential_win": max_potential_win,
            "combinations": combinations_list,
        }

    def format_ticket_details(self, ticket_details):
        """Format ticket details for display"""
        combinations_str = "\n".join(
            [f"Line {i+1} = {' and '.join(str(n) for n in combo)}" for i, combo in enumerate(ticket_details["combinations"])]
        )

        return (
            f"Game Type: {ticket_details['perm_type']}\n"
            f"Selected Numbers: {', '.join(map(str, ticket_details['selected_numbers']))}\n"
            f"Number of Lines: {ticket_details['num_lines']}\n"
            f"Stake per Line: ₦{ticket_details['stake_per_line']:,.2f}\n"
            f"Total Ticket Cost: ₦{ticket_details['total_cost']:,.2f}\n"
            f"Multiplier: {ticket_details['multiplier']}x\n"
            f"Maximum Potential Win: ₦{ticket_details['max_potential_win']:,.2f}\n\n"
            f"Winning Combinations:\n{combinations_str}"
        )

    def check_winnings(self, ticket_details, drawn_numbers):
        """
        Check how many winning combinations match the drawn numbers

        Args:
            ticket_details (dict): Ticket details from calculate_ticket()
            drawn_numbers (list): List of numbers drawn

        Returns:
            dict: Details of winning lines and amount won
        """
        winning_combos = []
        perm_size = int(ticket_details["perm_type"][-1])
        drawn_combos = set(combinations(sorted(drawn_numbers), perm_size))

        for combo in ticket_details["combinations"]:
            if combo in drawn_combos:
                winning_combos.append(combo)

        num_winning_lines = len(winning_combos)
        total_winnings = num_winning_lines * ticket_details["stake_per_line"] * ticket_details["multiplier"]

        return {"winning_combinations": winning_combos, "num_winning_lines": num_winning_lines, "total_winnings": total_winnings}


def calculate_banker_ball(single_stake):
    """
    Calculate 1Banker Ball ticket details including total stake and potential winnings.

    Args:
        single_stake (float): Single stake amount in Naira

    Returns:
        dict: Ticket details including validation status, total stake and potential winnings

    Raises:
        ValueError: If stake amount is invalid
    """

    from africa_lotto.models import AfricaLottoConstants

    # Constants
    MIN_STAKE = AfricaLottoConstants.get_minimum_stake()
    MAX_STAKE = 50000
    NUMBERS_TO_PLAY = 89  # Total numbers to play against banker number
    WINNING_MULTIPLIER = 960

    # Validate stake amount
    if not MIN_STAKE <= single_stake <= MAX_STAKE:
        raise ValueError(f"Single stake amount must be between ₦{MIN_STAKE} and ₦{MAX_STAKE}")

    # Calculate total stake
    total_stake = single_stake * NUMBERS_TO_PLAY

    # Calculate potential winnings
    potential_winnings = single_stake * WINNING_MULTIPLIER

    return {
        "game_type": "1Banker Ball",
        "single_stake": single_stake,
        "numbers_to_play": NUMBERS_TO_PLAY,
        "total_stake": total_stake,
        "multiplier": WINNING_MULTIPLIER,
        "potential_winnings": potential_winnings,
    }


class AgainstMarketCalculator:
    """Calculator for Against Market lottery game"""

    def __init__(self):
        from africa_lotto.models import AfricaLottoConstants

        self.min_stake = AfricaLottoConstants.get_minimum_stake()  # Minimum stake amount in Naira
        self.max_stake = 50000  # Maximum stake amount in Naira
        self.prize_multiplier = 240

        # Predefined winning lines mapping
        self.winning_lines_map = {1: 1, 2: 2, 3: 3, 4: 4, 6: 6}  # Maximum possible winning lines

    def validate_numbers(self, top_numbers, bottom_numbers):
        """Validate the selected numbers"""
        if not top_numbers or not bottom_numbers:
            raise ValueError("Both top and bottom numbers must be provided")

        if len(set(top_numbers)) != len(top_numbers):
            raise ValueError("Duplicate numbers not allowed in top selection")

        if len(set(bottom_numbers)) != len(bottom_numbers):
            raise ValueError("Duplicate numbers not allowed in bottom selection")

    def calculate_total_lines(self, top_numbers, bottom_numbers):
        """Calculate total number of lines based on selections"""
        return len(top_numbers) * len(bottom_numbers)

    def generate_combinations(self, top_numbers, bottom_numbers):
        """Generate all possible combinations of top and bottom numbers"""
        return list(product(top_numbers, bottom_numbers))

    def calculate_ticket(self, top_numbers, bottom_numbers, stake_per_line):
        """
        Calculate ticket details including total stake and potential winnings

        Args:
            top_numbers (list): List of selected top numbers
            bottom_numbers (list): List of selected bottom numbers
            stake_per_line (float): Stake amount per line

        Returns:
            dict: Ticket details including combinations and costs
        """
        # Validate inputs
        self.validate_numbers(top_numbers, bottom_numbers)
        if not self.min_stake <= stake_per_line <= self.max_stake:
            raise ValueError(f"Stake per line must be between ₦{self.min_stake} and ₦{self.max_stake}")

        # Calculate total lines and stake
        total_lines = self.calculate_total_lines(top_numbers, bottom_numbers)
        total_stake = total_lines * stake_per_line

        # Generate all possible combinations
        combinations = self.generate_combinations(top_numbers, bottom_numbers)

        # Calculate potential winnings for different winning line scenarios
        winning_scenarios = {lines: lines * stake_per_line * self.prize_multiplier for lines in self.winning_lines_map.values()}

        return {
            "game_type": "Against Market",
            "top_numbers": sorted(top_numbers),
            "bottom_numbers": sorted(bottom_numbers),
            "total_lines": total_lines,
            "stake_per_line": stake_per_line,
            "total_stake": total_stake,
            "prize_multiplier": self.prize_multiplier,
            "combinations": combinations,
            "winning_scenarios": winning_scenarios,
        }

    def format_ticket_details(self, ticket_details):
        """Format ticket details for display"""
        combinations_str = "\n".join([f"Line {i+1}: Top {top} * Bottom {bottom}" for i, (top, bottom) in enumerate(ticket_details["combinations"])])

        winning_scenarios_str = "\n".join([f"{lines} line(s): ₦{amount:,.2f}" for lines, amount in ticket_details["winning_scenarios"].items()])

        return (
            f"Game Type: {ticket_details['game_type']}\n"
            f"Top Numbers: {', '.join(map(str, ticket_details['top_numbers']))}\n"
            f"Bottom Numbers: {', '.join(map(str, ticket_details['bottom_numbers']))}\n"
            f"Total Lines: {ticket_details['total_lines']}\n"
            f"Stake per Line: ₦{ticket_details['stake_per_line']:,.2f}\n"
            f"Total Stake Amount: ₦{ticket_details['total_stake']:,.2f}\n"
            f"Prize Multiplier: {ticket_details['prize_multiplier']}x\n\n"
            f"Potential Winnings Scenarios:\n{winning_scenarios_str}\n\n"
            f"All Combinations:\n{combinations_str}"
        )


BASE62_ALPHABET = string.digits + string.ascii_letters


def base62_encode(num):
    base = len(BASE62_ALPHABET)
    encoded = ""
    while num:
        num, rem = divmod(num, base)
        encoded = BASE62_ALPHABET[rem] + encoded
    return encoded or "0"


def get_valid_until_date():
    """
    Determines the valid until date based on current time:
    - If current time is after 8 PM, valid until = next day 8 PM
    - If current time is before 8 PM, valid until = current day 8 PM
    Returns:
        datetime: The calculated valid until datetime
    """
    current_datetime = datetime.now()
    target_time = time(20, 0)  # 8 PM
    # Create datetime for 8 PM today
    valid_until = datetime.combine(current_datetime.date(), target_time)
    # If current time is past 8 PM, set valid until to 8 PM tomorrow
    if current_datetime.time() >= target_time:
        valid_until += timedelta(days=1)

    return valid_until


def get_valid_until_date_for_kenya_games():
    """
    Determines the valid until date based on current time ranges in Kenya timezone.
    Maps time ranges to specific valid_until times.

    Returns:
        datetime: The calculated valid until datetime
    """
    # Use Kenya timezone
    timezone = pytz.timezone(settings.TIME_ZONE)
    current_datetime = datetime.now(tz=timezone)
    current_hour = current_datetime.hour

    # Define time ranges and their valid_until times
    # Format: (start_hour, end_hour, valid_until_hour, valid_until_minute, next_day)
    time_ranges = [
        (9, 12, 12, 0, False),  # 9 AM to 12 PM → valid until 12 PM
        (12, 15, 15, 0, False),  # 12 PM to 3 PM → valid until 3 PM
        (15, 18, 18, 0, False),  # 3 PM to 6 PM → valid until 6 PM
        (18, 21, 21, 0, False),  # 6 PM to 9 PM → valid until 9 PM
        (21, 24, 9, 1, True),  # 9 PM to midnight → valid until 9:01 AM next day
        (0, 9, 9, 1, True),  # Midnight to 9 AM → valid until 9:01 AM
    ]

    # Find the matching time range for current time
    for start_hour, end_hour, valid_hour, valid_minute, next_day in time_ranges:
        if start_hour <= current_hour < end_hour:
            # Create the valid until datetime
            valid_until = datetime.combine(current_datetime.date() + timedelta(days=1 if next_day else 0), time(valid_hour, valid_minute))

            return valid_until

    # Default fallback - should not reach here if time ranges cover all 24 hours
    target_time = time(20, 0)  # 8 PM
    valid_until = datetime.combine(current_datetime.date(), target_time)
    if current_datetime.time() >= target_time:
        valid_until += timedelta(days=1)

    return valid_until




def get_valid_until_date_for_kenya_30_games():
    """
    Determines the next valid_until datetime based on draw times in the Kenya timezone.

    Returns:
        datetime: The next draw time as a timezone-aware datetime object.
    """
    # Use Kenya timezone
    timezone = pytz.timezone(settings.TIME_ZONE)
    current_datetime = datetime.now(tz=timezone)

    # Define draw times
    draw_times = [
        "08:15 AM", "08:45 AM", "09:15 AM", "09:45 AM", "10:15 AM", "10:45 AM",
        "11:15 AM", "11:45 AM", "12:15 PM", "12:45 PM", "01:15 PM", "01:45 PM",
        "02:15 PM", "02:45 PM", "03:15 PM", "03:45 PM", "04:15 PM", "04:45 PM",
        "05:15 PM", "05:45 PM", "06:15 PM", "06:45 PM", "07:15 PM", "07:45 PM",
        "08:15 PM", "08:45 PM", "09:15 PM", "09:45 PM"
    ]

    # Convert draw times to datetime objects for today
    today = current_datetime.date()
    draw_datetimes = []
    for time_str in draw_times:
        draw_time = datetime.strptime(time_str, "%I:%M %p").time()
        draw_datetime = datetime.combine(today, draw_time)
        draw_datetimes.append(timezone.localize(draw_datetime))

    # Find the next draw time
    for draw_datetime in draw_datetimes:
        if current_datetime < draw_datetime:
            return draw_datetime

    # If no draw time is left today, return the first draw time for tomorrow
    first_draw_time = datetime.strptime(draw_times[0], "%I:%M %p").time()
    tomorrow_datetime = datetime.combine(today + timedelta(days=1), first_draw_time)
    return timezone.localize(tomorrow_datetime)



def get_valid_until_date_for_k_now_games():
    """
    Determines the next valid_until datetime based on draw times every 5 minutes
    throughout the day in the Kenya timezone.
    
    Returns:
        datetime: The next draw time as a timezone-aware datetime object.
    """
    # Use Kenya timezone
    timezone = pytz.timezone(settings.TIME_ZONE)
    current_datetime = datetime.now(tz=timezone)
    
    # Calculate minutes since midnight
    current_time = current_datetime.time()
    minutes_since_midnight = current_time.hour * 60 + current_time.minute
    
    # Calculate next 5-minute mark
    next_5min_mark = ((minutes_since_midnight // 5) + 1) * 5
    
    # Calculate hours and minutes for the next draw time
    next_hour = next_5min_mark // 60 % 24  # Wrap around at 24 hours
    next_minute = next_5min_mark % 60
    
    # Create the next draw datetime
    today = current_datetime.date()
    next_day = today
    
    # If we've wrapped around to the next day
    if next_hour * 60 + next_minute <= minutes_since_midnight:
        next_day = today + timedelta(days=1)
    
    next_draw_time = time(hour=next_hour, minute=next_minute)
    next_draw_datetime = datetime.combine(next_day, next_draw_time)
    
    return timezone.localize(next_draw_datetime)


# def scrape_ghana_draw_numbers(url):
#     """
#     Scrape Ghana lotto draw numbers from the given URL.

#     Args:
#         url (str): The URL to scrape

#     Returns:
#         list: List of dictionaries containing draw numbers and dates
#     """

#     driver = webdriver.Chrome(options=chrome_options)
#     driver.get(url)
#     draw_results = []

#     try:
#         # Wait for the table's <tbody> to appear
#         WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, "//table[@id='example']/tbody")))

#         table = driver.find_element(By.ID, "example")
#         tbody = table.find_element(By.TAG_NAME, "tbody")
#         rows = tbody.find_elements(By.TAG_NAME, "tr")

#         for row in rows:
#             print("Processing row...", row)

#             try:
#                 cols = row.find_elements(By.TAG_NAME, "td")
#                 print("Processing columns...", cols, "\n\n\n")
#                 if len(cols) >= 4:  # Ensure all columns exist
#                     draw_dict = {
#                         "Draw ID": cols[0].text.strip(),
#                         "Draw Name": cols[1].text.strip(),
#                         "Date": cols[2].text.strip(),
#                         "Numbers": cols[3].text.strip(),
#                     }
#                     print(f"Draw Data: {draw_dict}")
#                     draw_results.append(draw_dict)
#             except NoSuchElementException:
#                 print("Row structure is not as expected.")

#     except TimeoutException:
#         print("Table not found or data not loaded within timeout.")

#     finally:
#         driver.quit()  # Close the browser


def scrape_ghana_draw_numbers():
    from africa_lotto.models import AfricaLottoDrawNumbers, AfricaLottoGameType

    """
    Scrape Ghana lotto draw numbers from the given URL.

    Args:
        url (str): The URL to scrape

    Returns:
        list: List of dictionaries containing draw numbers and dates
    """

    # Perform the request
    url = "https://www.ghanayello.com/lottery/results/history"
    # headers = {
    #     "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    #     "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
    #     "Accept-Language": "en-US,en;q=0.9",
    #     "Referer": "https://www.ghanayello.com/",
    # }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.9",
        "Referer": "https://www.ghanayello.com/",
        "Connection": "keep-alive",
        "DNT": "1",  # Do Not Track Request Header
        "Upgrade-Insecure-Requests": "1",
    }

    response = requests.get(url, headers=headers)
    response.raise_for_status()  # This will raise an exception for 4XX/5XX responses
    soup = BeautifulSoup(response.content, "html.parser")

    # Find all table rows with itemListElement attribute
    lottery_rows = soup.find_all("tr", {"itemprop": "itemListElement"})

    lottery_results = []
    for row in lottery_rows:
        result = {}

        # print(row, "\n\n")

        # Extract draw date
        date_td = row.find("td", {"title": "Draw Date"})
        if date_td:
            result["draw_date"] = date_td.text.strip()

        game_td = row.find("td", {"title": lambda value: value and "Ghana Lotto Game" in value})
        # print(game_td)
        if game_td:
            result["game_of_draw_game"] = game_td.get("title", "").strip()

        winning_td = row.find("td", {"itemprop": "description", "title": lambda value: value and "Winning Numbers" in value})
        if winning_td:
            # Option 1: Get the title attribute which has the numbers in format "Winning Numbers 32-72-89-52-17"
            winning_numbers_str = winning_td.get("title", "").replace("Winning Numbers ", "")
            result["winning_numbers"] = winning_numbers_str

            # Option 2: Extract each number from the divs if you want them separately
            number_divs = winning_td.find_all("div", class_=lambda c: c and c.startswith("lotto_no_r"))
            winning_numbers = [div.text.strip() for div in number_divs]
            result["winning_numbers_list"] = winning_numbers

        lottery_results.append(result)

    if len(lottery_results) > 0:
        for result in lottery_results:
            draw_date = result.get("draw_date")
            game_of_draw_game = result.get("game_of_draw_game")
            winning_numbers = result.get("winning_numbers")

            if winning_numbers == None:  # noqa
                continue

            winning_numbers = winning_numbers.replace("-", ",")

            if "sunday aseda" in game_of_draw_game.lower():
                if AfricaLottoDrawNumbers.objects.filter(draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO).exists():
                    continue

                AfricaLottoDrawNumbers.objects.create(
                    draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO, draw_number=winning_numbers, draw_name="Sunday Aseda"
                )

            elif "lucky tuesday" in game_of_draw_game.lower():
                if AfricaLottoDrawNumbers.objects.filter(draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO).exists():
                    continue

                AfricaLottoDrawNumbers.objects.create(
                    draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO, draw_number=winning_numbers, draw_name="Lucky Tuesday"
                )

            elif "midweek" in game_of_draw_game.lower():
                if AfricaLottoDrawNumbers.objects.filter(draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO).exists():
                    continue

                AfricaLottoDrawNumbers.objects.create(
                    draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO, draw_number=winning_numbers, draw_name="Midweek"
                )

            elif "fortune thursday" in game_of_draw_game.lower():
                if AfricaLottoDrawNumbers.objects.filter(draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO).exists():
                    continue

                AfricaLottoDrawNumbers.objects.create(
                    draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO, draw_number=winning_numbers, draw_name="Fortune Thursday"
                )

            elif "friday bonanza" in game_of_draw_game.lower():
                if AfricaLottoDrawNumbers.objects.filter(draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO).exists():
                    continue

                AfricaLottoDrawNumbers.objects.create(
                    draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO, draw_number=winning_numbers, draw_name="Friday Bonanza"
                )

            elif "national weekly" in game_of_draw_game.lower():
                if AfricaLottoDrawNumbers.objects.filter(draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO).exists():
                    continue

                AfricaLottoDrawNumbers.objects.create(
                    draw_date=draw_date, game_type=AfricaLottoGameType.GHANA_LOTTO, draw_number=winning_numbers, draw_name="National Weekly"
                )




import math
from itertools import combinations

def check_lotto_win(user_numbers, winning_numbers, play_type, stake_per_line=10.0, total_lines=None):
    """
    Check lotto winnings for Nigerian lotto games (like Baba Ijebu)

    Args:
        user_numbers (list): User's selected numbers
        winning_numbers (list): The winning numbers drawn
        play_type (str): Type of play - 'perm2', 'perm3', 'nap2', 'nap3', 'nap4', 'nap5', 'against'
        stake_per_line (float): Amount staked per line/combination
        total_lines (int, optional): Total number of lines/combinations played. If None, it will be
                                     calculated based on play_type and user_numbers.

    Returns:
        dict: Results containing matches, winnings, and details
    """

    # Updated winning multipliers for different play types based on the latest information
    multipliers = {
        'perm2': 240,
        'perm3': 2100,
        'nap2': 240,     # Updated as per new information
        'nap3': 2100,    # Updated as per new information
        'nap4': 6000,    # Updated as per new information
        'nap5': 44000,   # Updated as per new information
        'against': 5     # Assuming this remains the same as not specified otherwise
    }

    # Predefined lines for PERM games (as provided in previous context)
    perm_lines_data = {
        "PERM2": {
            3: 3, 4: 6, 5: 10, 6: 15, 7: 21, 8: 28, 9: 36, 10: 45,
            11: 55, 12: 66, 13: 78, 14: 91, 15: 105, 16: 120, 17: 136,
            18: 153, 19: 171, 20: 190,
        },
        "PERM3": {
            4: 4, 5: 10, 6: 20, 7: 35, 8: 56, 9: 84, 10: 120,
            11: 165, 12: 220, 13: 286, 14: 364, 15: 455, 16: 560,
            17: 680, 18: 816, 19: 969, 20: 1140,
        },
        "PERM4": {
            5: 5, 6: 15, 7: 35, 8: 70, 9: 126, 10: 210, 11: 330,
            12: 495, 13: 715, 14: 1001, 15: 1365, 16: 1820,
            17: 2380, 18: 3060, 19: 3876, 20: 4845,
        },
        "PERM5": {
            5: 1, 6: 6, 7: 21, 8: 56, 9: 126, 10: 252, 11: 462,
            12: 792, 13: 1287, 14: 2002, 15: 3003, 16: 4368,
            17: 6188, 18: 8568, 19: 11628, 20: 15504,
        },
    }

    def get_combinations(numbers, r):
        """Generate all combinations of r numbers from the list"""
        return list(combinations(numbers, r))

    def check_perm_win(user_nums, win_nums, perm_size):
        """Check permutation wins (any order)"""
        user_combinations = get_combinations(user_nums, perm_size)

        matches = []
        # Find winning lines from user's combinations that match any 'perm_size' combination of winning numbers
        for user_combo in user_combinations:
            user_combo_set = set(user_combo)
            # Check against all combinations of 'perm_size' from winning numbers
            for win_combo in get_combinations(win_nums, perm_size):
                if user_combo_set == set(win_combo):
                    matches.append(user_combo)
                    break # Found a match for this user_combo, no need to check other winning combinations
        return matches

    def check_nap_win(user_nums, win_nums, nap_size):
        """Check NAP wins (exact positions)"""
        matches = []
        # For NAP, the user's selected numbers must match a sequence within the winning numbers.
        # The key here is that the 'nap_size' user numbers must be PRESENT in the winning numbers
        # AND those 'nap_size' user numbers must also appear together in the same order
        # as a subset of the winning numbers, starting from any position.

        # However, typical NAP definitions (like Baba Ijebu) often imply matching the first
        # 'nap_size' winning numbers, or matching a sequence of numbers from the draw.
        # Given the previous context and the typical understanding of NAPs, I will assume
        # that 'their numbers are drawn out' for NAP means that if the user picks X, Y, Z,
        # and X, Y, Z appear *in that order* among the winning numbers.

        # Let's adjust this to be more flexible, matching if the user's selected numbers (in order)
        # appear as a *consecutive subsequence* within the winning numbers.
        # If the interpretation is simply that the user's selected numbers must be among the winning
        # numbers (in any order, but a specific quantity), that's more like a PERM.

        # Based on "They win if their numbers are drawn out" for NAP, and contrasting with PERM
        # which focuses on combinations, NAP typically implies matching an ordered subset
        # of the winning numbers, or matching the first 'N' winning numbers.
        # I will revert to the stricter interpretation of `check_nap_win` where it implies
        # matching the first `nap_size` winning numbers, as it aligns with typical fixed-position
        # NAP games in some lotteries. If the user's numbers don't need to be in a specific position
        # among the winning numbers, but just present as a sequence, the logic would change.

        # Let's stick with the original `check_nap_win` logic as it implies fixed-position wins,
        # where the user's `nap_size` numbers must match the *first* `nap_size` winning numbers.
        # If the problem statement implies "if their numbers (in order) are present *anywhere*
        # in the winning numbers list", the logic for check_nap_win would need a significant change.
        # For now, matching the *first* `nap_size` numbers for NAPs.

        if len(user_nums) >= nap_size and len(win_nums) >= nap_size:
            user_sequence = tuple(user_nums[:nap_size])
            winning_sequence_prefix = tuple(win_nums[:nap_size])

            if user_sequence == winning_sequence_prefix:
                matches.append(user_sequence)
        return matches

    def check_against_win(user_nums, win_nums):
        """Check AGAINST wins (none of your numbers should appear)"""
        user_set = set(user_nums)
        winning_set = set(win_nums)

        # Win if none of your numbers appear in winning numbers
        if not user_set.intersection(winning_set):
            return [tuple(user_nums)]
        return []

    # Determine total_lines if not provided
    if total_lines is None:
        num_selected = len(user_numbers)
        play_type_upper = play_type.upper()
        if play_type_upper.startswith("PERM") and play_type_upper in perm_lines_data:
            if num_selected in perm_lines_data[play_type_upper]:
                total_lines = perm_lines_data[play_type_upper][num_selected]
            else:
                # Fallback to combinations calculation if not in predefined table
                if play_type_upper == "PERM2":
                    total_lines = math.comb(num_selected, 2)
                elif play_type_upper == "PERM3":
                    total_lines = math.comb(num_selected, 3)
                elif play_type_upper == "PERM4":
                    total_lines = math.comb(num_selected, 4)
                elif play_type_upper == "PERM5":
                    total_lines = math.comb(num_selected, 5)
                else:
                    total_lines = 0 # Should not happen for PERM types, but for safety
        elif play_type_upper.startswith("NAP") or play_type_upper == "AGAINST":
            total_lines = 1 # For NAP and Against, typically one "line" or selection is made for the stake
        else:
            total_lines = 0 # Default if play_type is not recognized or has no defined lines

    if total_lines is None: # Final check if still None
        total_lines = 0


    # Initialize results
    result = {
        'play_type': play_type,
        'user_numbers': user_numbers,
        'winning_numbers': winning_numbers,
        'matches': [],
        'total_winnings': 0.0,
        'stake_per_line': stake_per_line,
        'total_stake': stake_per_line * total_lines,
        'multiplier': multipliers.get(play_type, 0),
        'lines_won': 0
    }

    # Check wins based on play type
    if play_type == 'perm2':
        result['matches'] = check_perm_win(user_numbers, winning_numbers, 2)
    elif play_type == 'perm3':
        result['matches'] = check_perm_win(user_numbers, winning_numbers, 3)
    elif play_type == 'nap2':
        result['matches'] = check_nap_win(user_numbers, winning_numbers, 2)
    elif play_type == 'nap3':
        result['matches'] = check_nap_win(user_numbers, winning_numbers, 3)
    elif play_type == 'nap4':
        result['matches'] = check_nap_win(user_numbers, winning_numbers, 4)
    elif play_type == 'nap5':
        result['matches'] = check_nap_win(user_numbers, winning_numbers, 5)
    elif play_type == 'against':
        result['matches'] = check_against_win(user_numbers, winning_numbers)

    # Calculate winnings
    result['lines_won'] = len(result['matches'])
    if result['lines_won'] > 0:
        result['total_winnings'] = result['lines_won'] * stake_per_line * result['multiplier']

    # Calculate profit/loss
    result['net_profit'] = result['total_winnings'] - result['total_stake']

    return result


# Example usage
if __name__ == "__main__":
    # Your example data
    user_numbers = [9, 90, 45, 11, 60]
    winning_numbers = [9, 90, 45, 11, 60]  # Replace with actual winning numbers
    
    # Test different play types
    play_types = ['perm2', 'perm3', 'nap2', 'nap3', 'against']
    
    for play_type in play_types:
        result = check_lotto_win(
            user_numbers=user_numbers,
            winning_numbers=winning_numbers,
            play_type=play_type,
            stake_per_line=10.0,
            total_lines=10
        )
        