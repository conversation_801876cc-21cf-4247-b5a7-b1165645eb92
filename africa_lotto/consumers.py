import json
from channels.generic.websocket import AsyncWebsocketConsumer
import asyncio
import time

class FireballConsumer(AsyncWebsocketConsumer):
    async def connect(self):

        self.connection_timeout = 3600  # 1 hour in seconds
        self.last_activity = time.time()


        self.room_name = "fireball_notifications"
        self.room_group_name = f"notifications_{self.room_name}"
        print(f"Connecting to room: {self.room_group_name}")  # Debugging line
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        print(f"Added to group: {self.room_group_name}")  # Add logging to check
        
        await self.accept()

        # Schedule cleanup task
        self.cleanup_task = asyncio.create_task(self.cleanup_inactive())

    
    async def cleanup_inactive(self):
        while True:
            await asyncio.sleep(300)  # Check every 5 minutes
            if time.time() - self.last_activity > self.connection_timeout:
                # Connection has been inactive too long
                await self.close()
                break
        
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    # Receive message from WebSocket
    async def receive(self, text_data):
        self.last_activity = time.time()

        text_data_json = json.loads(text_data)
        message = text_data_json['message']
        
        # Send message to room group
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'fireball_notification',
                'message': message
            }
        )
    
    async def fireball_notification(self, event):
        print(f"Received fireball notification: {event}")  # Debugging line
        message = event['message']
        print(f"Broadcasting message to WebSocket: {message}")  # Add logging to check
        await self.send(text_data=json.dumps({
            'message': message
        }))







class LiveDrawConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.batch_id = self.scope['url_route']['kwargs']['batch_id']


        self.identifier = self.scope['url_route']['kwargs']['identifier']
    
        

        
        # Validate batch_id exists
        # if not await self.batch_exists():
        #     await self.close()
        #     return
        
        self.room_name = f"live_draw_{self.batch_id}"
        self.room_group_name = f"notifications_{self.room_name}"
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        await self.accept()

        print(f"Connecting to room: {self.room_group_name}")
        
    

    async def disconnect(self, close_code):
        if hasattr(self, 'cleanup_task'):
            self.cleanup_task.cancel()
            
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_type = text_data_json.get('type')
        
        if message_type == 'draw_update':
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'live_draw_update',
                    'draw_number': text_data_json.get('draw_number'),
                    'batch_id': self.batch_id
                }
            )
    
    async def live_draw_update(self, event):
        print("\n\n", event, "\n\n")
        await self.send(text_data=json.dumps({
            'type': 'draw_update',
            'draw_number': event["message"]['draw_number'],
            'machine_number': event["message"].get('machine_number', None),
            'batch_number': event["message"]['batch_number']
        }))



class GeneralSocketConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        
        self.room_name = "winwise_socket_notification"
        
        self.room_group_name = f"notifications_{self.room_name}"
        
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        await self.accept()

        print(f"Connecting to room: {self.room_group_name}")
        
    

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_type = text_data_json.get('type')
        
        if message_type == 'draw_update':
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'live_draw_update',
                    'draw_number': text_data_json.get('draw_number'),
                    'batch_id': self.batch_id
                }
            )
    
    async def live_draw_update(self, event):
        # print("\n\n", event, "\n\n")

        draw_number = event["message"].get('draw_number', [])
        machine_number = event["message"].get('machine_number', [])
        if isinstance(draw_number, str):
            draw_number = str(draw_number).split(",")
        
        if isinstance(machine_number, str):
            machine_number = str(machine_number).split(",")

        await self.send(text_data=json.dumps({
            "event_type": "live_draw_update",
            "data": {
                'type': 'draw_update',
                'draw_number': draw_number,
                'machine_number': machine_number,
                'batch_number': event["message"]['batch_number']
            }
        }))

    
    async def fireball_notification(self, event):
        print(f"Received fireball notification: {event}")  # Debugging line
        message = event['message']
        if isinstance(message, str):
            message = message.split(",")
            
        print(f"Broadcasting message to WebSocket: {message}")  # Add logging to check
        await self.send(text_data=json.dumps({
            "event_type": "fireball_notification",
            'data': message
        }))


    async def soc_next_k_now_draw_time(self, event):
        print(f"Received fireball notification: {event}")  # Debugging line
        message = event['message']
        print(f"Broadcasting message to WebSocket: {message}")  # Add logging to check
        await self.send(text_data=json.dumps({
            "event_type": "next_k_now_draw_time",
            'data': message
        }))