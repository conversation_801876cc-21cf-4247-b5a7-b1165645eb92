import json
from itertools import combinations, product

from django.contrib.admin.views.decorators import staff_member_required
from django.db.models import Q
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.throttling import UserRateThrottle
from rest_framework.views import APIView

from account.authentication import (
    GhanaLottoDrawPermissionPermission,
    IsAgentRemittanceDuePermission,
    IsAgentSuspendedPermission,
    IsBlackListedPermission,
    IsGhanaGameActivePermission,
    IsKenya30GameActivePermission,
    IsKenyaGameActivePermission,
    IsKNowGameActivePermission,
    Kenya30LottoDrawPermission,
    KenyaLottoDrawPermission,
)
from africa_lotto.helpers import check_lotto_win
from africa_lotto.models import (
    AfricaLotteryType,
    AfricaLotto,
    AfricaLottoBatch,
    AfricaLottoDrawData,
    AfricaLottoDrawNumbers,
    AfricaLottoGameType,
)
from africa_lotto.serializers import (
    AfricaLottoDrawNumbersSerializers,
    AgainstMarketSerializer,
    BankerBallSerializer,
    CheckAfricaLottoWinningNumberSerializer,
    FetchDrawNumberUsingBatchNumberSerializer,
    ManuallyDrawGhanaGamesSerializer,
    NAPSerializer,
    PERMSerializer,
    PlayGhanaLottoGameSerializers,
    PlayKenya30LottoGameSerializers,
    PlayKenyaLottoGameSerializers,
    PlayKNowLottoGameSerializers,
    TempPublishFireBallSocketSerializer,
    TempUpdateKNowDrawNumbersSocketNotificationSerializer,
    TempUpdateKNowNextDrawSocketNotificationViewSerializer,
)
from africa_lotto.socket_utils import (
    live_k_now_draw_number_socket_update,
    send_fireball_notification,
    update_next_k_now_draw,
)
from africa_lotto.tasks import (
    celery_africa_lotto_kenya_draw,
    filter_africa_lotto_winnders_data,
    process_africa_lotto_winners,
)
from main.ussd.helpers import Utility
from pos_app.models import Agent, LottoAgentRemittanceTable, RetailTicketRequestLogs

# Create your views here.


class PerUserGhanaLottoThrottle(UserRateThrottle):
    def allow_request(self, request, view):
        if request.user:
            if request.user.is_superuser:
                self.rate = "100/minute"  # Higher limit for superusers
            else:
                self.rate = "2/minute"  # Default limit for other users
        else:
            self.rate = "0/minute"  # No requests allowed for unauthenticated users

        return super().allow_request(request, view)


class PlayGhanaLottoGameView(APIView):
    serializer_class = PlayGhanaLottoGameSerializers

    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        GhanaLottoDrawPermissionPermission,
        IsGhanaGameActivePermission,
    ]

    # throttle_classes = [PerUserGhanaLottoThrottle]

    @method_decorator(csrf_exempt)
    def post(self, request):

        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        total_stake_amount = serializer.validated_data.get("total_amount", 0)
        # validate_remittance_amount
        response = LottoAgentRemittanceTable().valuate_amount_to_be_added(
            amount=total_stake_amount, agent=agent_instance
        )
        if response.get("is_amount_much"):
            response = {
                "message": f"Hi, kindly top up your lottowallet with this amount {Utility.currency_formatter(response.get('amount'))} to continue game play"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            game_play_status, game_play_msg, game_play_data = AfricaLotto.create_ghana_lotto_ticket(
                agent_instance, **serializer.validated_data
            )
        except Exception as e:
            return Response(data={"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        if not game_play_status:
            return Response(data={"message": game_play_msg}, status=status.HTTP_400_BAD_REQUEST)

        return Response(data={"message": game_play_msg, "data": game_play_data}, status=status.HTTP_201_CREATED)


class PlayKenyaLottoGameView(APIView):
    serializer_class = PlayKenyaLottoGameSerializers

    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        KenyaLottoDrawPermission,
        IsKenyaGameActivePermission,
    ]

    # throttle_classes = [PerUserGhanaLottoThrottle]

    @method_decorator(csrf_exempt)
    def post(self, request):

        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        total_stake_amount = serializer.validated_data.get("total_amount", 0)
        # validate_remittance_amount
        response = LottoAgentRemittanceTable().valuate_amount_to_be_added(
            amount=total_stake_amount, agent=agent_instance
        )
        if response.get("is_amount_much"):
            response = {
                "message": f"Hi, kindly top up your lottowallet with this amount {Utility.currency_formatter(response.get('amount'))} to continue game play"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            game_play_status, game_play_msg, game_play_data = AfricaLotto.create_kenya_lotto_ticket(
                agent_instance, **serializer.validated_data
            )
        except Exception as e:
            return Response(data={"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        if not game_play_status:
            return Response(data={"message": game_play_msg}, status=status.HTTP_400_BAD_REQUEST)

        return Response(data={"message": game_play_msg, "data": game_play_data}, status=status.HTTP_201_CREATED)


class PlayKenya30LottoGameView(APIView):
    serializer_class = PlayKenya30LottoGameSerializers

    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        Kenya30LottoDrawPermission,
        IsKenya30GameActivePermission,
    ]

    # throttle_classes = [PerUserGhanaLottoThrottle]

    @method_decorator(csrf_exempt)
    def post(self, request):

        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        total_stake_amount = serializer.validated_data.get("total_amount", 0)
        # validate_remittance_amount
        response = LottoAgentRemittanceTable().valuate_amount_to_be_added(
            amount=total_stake_amount, agent=agent_instance
        )
        if response.get("is_amount_much"):
            response = {
                "message": f"Hi, kindly top up your lottowallet with this amount {Utility.currency_formatter(response.get('amount'))} to continue game play"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            game_play_status, game_play_msg, game_play_data = AfricaLotto.create_kenya_30_lotto_ticket(
                agent_instance, **serializer.validated_data
            )
        except Exception as e:
            return Response(data={"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        if not game_play_status:
            return Response(data={"message": game_play_msg}, status=status.HTTP_400_BAD_REQUEST)

        return Response(data={"message": game_play_msg, "data": game_play_data}, status=status.HTTP_201_CREATED)


class PlayKNowLottoGameView(APIView):
    serializer_class = PlayKNowLottoGameSerializers

    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        IsKNowGameActivePermission,
    ]

    # throttle_classes = [PerUserGhanaLottoThrottle]

    @method_decorator(csrf_exempt)
    def post(self, request):

        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        print(request.data)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        RetailTicketRequestLogs.objects.create(agent=agent_instance, request_payload=json.dumps(request.data))

        try:
            game_play_status, game_play_msg, game_play_data = AfricaLotto.create_k_now_lotto_ticket(
                agent_instance, **serializer.validated_data
            )
        except Exception as e:
            return Response(data={"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        if not game_play_status:
            return Response(data={"message": game_play_msg}, status=status.HTTP_400_BAD_REQUEST)

        return Response(data={"message": game_play_msg, "data": game_play_data}, status=status.HTTP_201_CREATED)


class NAPCalculatorView(APIView):
    serializer_class = NAPSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        nap_type = serializer.validated_data["nap_type"]
        purchase_amount = serializer.validated_data["purchase_amount"]

        nap_multipliers = {2: 240, 3: 2100, 4: 6000, 5: 44000}
        multiplier = nap_multipliers[nap_type]
        potential_winnings = purchase_amount * multiplier

        return Response(
            {
                "nap_type": f"NAP{nap_type}",
                "purchase_amount": purchase_amount,
                "multiplier": multiplier,
                "potential_winnings": potential_winnings,
                "numbers_to_select": nap_type,
                "total_numbers": 90,
            }
        )


class PERMCalculatorView(APIView):
    serializer_class = PERMSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        perm_type = serializer.validated_data["perm_type"]
        selected_numbers = serializer.validated_data["selected_numbers"]
        stake_per_line = serializer.validated_data["stake_per_line"]

        perm_lines = {
            "PERM2": {
                3: 3,
                4: 6,
                5: 10,
                6: 15,
                7: 21,
                8: 28,
                9: 36,
                10: 45,
                11: 55,
                12: 66,
                13: 78,
                14: 91,
                15: 105,
                16: 120,
                17: 136,
                18: 153,
                19: 171,
                20: 190,
            },
            "PERM3": {
                4: 4,
                5: 10,
                6: 20,
                7: 35,
                8: 56,
                9: 84,
                10: 120,
                11: 165,
                12: 220,
                13: 286,
                14: 364,
                15: 455,
                16: 560,
                17: 680,
                18: 816,
                19: 969,
                20: 1140,
            },
            "PERM4": {
                5: 5,
                6: 15,
                7: 35,
                8: 70,
                9: 126,
                10: 210,
                11: 330,
                12: 495,
                13: 715,
                14: 1001,
                15: 1365,
                16: 1820,
                17: 2380,
                18: 3060,
                19: 3876,
                20: 4845,
            },
            "PERM5": {
                5: 1,
                6: 6,
                7: 21,
                8: 56,
                9: 126,
                10: 252,
                11: 462,
                12: 792,
                13: 1287,
                14: 2002,
                15: 3003,
                16: 4368,
                17: 6188,
                18: 8568,
                19: 11628,
                20: 15504,
            },
        }

        multipliers = {"PERM2": 240, "PERM3": 2100, "PERM4": 10000, "PERM5": 50000}

        num_selected = len(selected_numbers)
        if num_selected not in perm_lines[perm_type]:
            return Response(
                {"error": f"Invalid number of selections for {perm_type}"}, status=status.HTTP_400_BAD_REQUEST
            )

        lines = perm_lines[perm_type][num_selected]
        total_cost = lines * stake_per_line
        multiplier = multipliers[perm_type]
        max_potential_win = total_cost * multiplier

        r = int(perm_type[-1])
        combinations_list = list(combinations(selected_numbers, r))

        return Response(
            {
                "perm_type": perm_type,
                "selected_numbers": selected_numbers,
                "num_lines": lines,
                "stake_per_line": stake_per_line,
                "total_cost": total_cost,
                "multiplier": multiplier,
                "max_potential_win": max_potential_win,
                "combinations": combinations_list,
            }
        )


class BankerBallCalculatorView(APIView):
    serializer_class = BankerBallSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        single_stake = serializer.validated_data["single_stake"]

        NUMBERS_TO_PLAY = 89
        WINNING_MULTIPLIER = 960

        total_stake = single_stake * NUMBERS_TO_PLAY
        potential_winnings = single_stake * WINNING_MULTIPLIER

        return Response(
            {
                "game_type": "1Banker Ball",
                "single_stake": single_stake,
                "numbers_to_play": NUMBERS_TO_PLAY,
                "total_stake": total_stake,
                "multiplier": WINNING_MULTIPLIER,
                "potential_winnings": potential_winnings,
            }
        )


class AgainstMarketCalculatorView(APIView):
    serializer_class = AgainstMarketSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        top_numbers = serializer.validated_data["top_numbers"]
        bottom_numbers = serializer.validated_data["bottom_numbers"]
        stake_per_line = serializer.validated_data["stake_per_line"]

        PRIZE_MULTIPLIER = 240

        total_lines = len(top_numbers) * len(bottom_numbers)
        total_stake = total_lines * stake_per_line

        combinations_list = list(product(top_numbers, bottom_numbers))

        winning_scenarios = {str(lines): lines * stake_per_line * PRIZE_MULTIPLIER for lines in [1, 2, 3, 4, 6]}

        return Response(
            {
                "game_type": "Against Market",
                "top_numbers": top_numbers,
                "bottom_numbers": bottom_numbers,
                "total_lines": total_lines,
                "stake_per_line": stake_per_line,
                "total_stake": total_stake,
                "prize_multiplier": PRIZE_MULTIPLIER,
                "combinations": combinations_list,
                "winning_scenarios": winning_scenarios,
            }
        )


class FetchGhanaDrawNumbersApiView(APIView):
    def get(self, request):
        qs = AfricaLottoDrawNumbers.objects.filter(game_type=AfricaLottoGameType.GHANA_LOTTO).order_by("-id")[:100]
        serialized_data = AfricaLottoDrawNumbersSerializers(qs, many=True).data
        return Response(data=serialized_data, status=status.HTTP_200_OK)


class FetchKenyaDrawNumbersApiView(APIView):
    def get(self, request):
        qs = AfricaLottoDrawNumbers.objects.filter(game_type=AfricaLottoGameType.KENYA_LOTTO).order_by("-id")[:100]
        serialized_data = AfricaLottoDrawNumbersSerializers(qs, many=True).data
        return Response(data=serialized_data, status=status.HTTP_200_OK)


class FetchKNowDrawNumbersApiView(APIView):
    def get(self, request):
        qs = AfricaLottoDrawNumbers.objects.filter(game_type=AfricaLottoGameType.K_NOW).order_by("-id")[:100]
        serialized_data = AfricaLottoDrawNumbersSerializers(qs, many=True).data
        return Response(data=serialized_data, status=status.HTTP_200_OK)


class FetchKenya30DrawNumbersApiView(APIView):
    def get(self, request):
        qs = AfricaLottoDrawNumbers.objects.filter(game_type=AfricaLottoGameType.KENYA_30_LOTTO).order_by("-id")[:100]
        serialized_data = AfricaLottoDrawNumbersSerializers(qs, many=True).data
        # serialized_data = []
        return Response(data=serialized_data, status=status.HTTP_200_OK)


class FetchWebKenya30DrawNumbersApiView(APIView):
    def get(self, request):
        qs = AfricaLottoDrawNumbers.objects.filter(game_type=AfricaLottoGameType.KENYA_30_LOTTO).order_by("-id")[:100]
        serialized_data = AfricaLottoDrawNumbersSerializers(qs, many=True).data
        return Response(data=serialized_data, status=status.HTTP_200_OK)


@method_decorator(staff_member_required, name="dispatch")
class ManuallyDrawGhanaGamesApiView(APIView):
    serializer_class = ManuallyDrawGhanaGamesSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        batch_id = serializer.validated_data["batch_id"]
        draw_number_db_id = serializer.validated_data["draw_number_db_id"]

        try:
            batch_instance = AfricaLottoBatch.objects.get(
                batch_uuid=batch_id, game_type=AfricaLottoGameType.GHANA_LOTTO, draw_status=False
            )
        except AfricaLottoBatch.DoesNotExist:
            return Response(data={"message": "Batch not found"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            draw_instance = AfricaLottoDrawNumbers.objects.get(
                id=draw_number_db_id, game_type=AfricaLottoGameType.GHANA_LOTTO
            )
        except AfricaLottoDrawNumbers.DoesNotExist:
            return Response(data={"message": "Draw numbers not found"}, status=status.HTTP_400_BAD_REQUEST)

        data = filter_africa_lotto_winnders_data(draw_db_id=draw_instance.id, batch_id=batch_instance.id)

        return Response(data=data, status=status.HTTP_200_OK)


@method_decorator(staff_member_required, name="dispatch")
class ManuallyFilterWinningsView(APIView):
    serializer_class = ManuallyDrawGhanaGamesSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        batch_id = serializer.validated_data["batch_id"]
        draw_number_db_id = serializer.validated_data["draw_number_db_id"]

        try:
            batch_instance = AfricaLottoBatch.objects.get(batch_uuid=batch_id)
        except AfricaLottoBatch.DoesNotExist:
            return Response(data={"message": "Batch not found"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            draw_instance = AfricaLottoDrawNumbers.objects.get(id=draw_number_db_id)
        except AfricaLottoDrawNumbers.DoesNotExist:
            return Response(data={"message": "Draw numbers not found"}, status=status.HTTP_400_BAD_REQUEST)

        batch_games = AfricaLotto.objects.filter(batch=batch_instance)

        structured_tickets = []

        total_rtp = 0

        for game in batch_games:

            total_rtp += game.effective_rtp

            user_picks = game.lucky_number.split(",")
            user_picks = [int(pick) for pick in user_picks]

            if game.lottery_type in [
                AfricaLotteryType.NAP2,
                AfricaLotteryType.NAP3,
                AfricaLotteryType.NAP4,
                AfricaLotteryType.NAP5,
            ]:
                structured_tickets.append(
                    {
                        "nap": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            if game.lottery_type == AfricaLotteryType.PERM2:
                structured_tickets.append(
                    {
                        "perm-2": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )
            if game.lottery_type == AfricaLotteryType.PERM3:
                structured_tickets.append(
                    {
                        "perm-3": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            if game.lottery_type == AfricaLotteryType.PERM4:
                structured_tickets.append(
                    {
                        "perm-4": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            if game.lottery_type == AfricaLotteryType.PERM5:
                structured_tickets.append(
                    {
                        "perm-5": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            if game.lottery_type == AfricaLotteryType.AGAINST:
                if game.bottom_ticket is None:
                    continue

                bottom_ticket = game.bottom_ticket.split(",")
                bottom_ticket = [int(pick) for pick in bottom_ticket]

                structured_tickets.append(
                    {
                        "against": [
                            [user_picks, bottom_ticket],
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

            if game.lottery_type == AfricaLotteryType.BANKER:
                structured_tickets.append(
                    {
                        "banker": [
                            user_picks,
                            f"{game.game_play_id}&{game.id}",
                            game.stake_per_line,
                            game.purchase_amount,
                        ],
                    }
                )

        draw_data_instance = AfricaLottoDrawData.objects.create(
            batch=batch_instance.batch_uuid,
            game_type=AfricaLottoGameType.KENYA_LOTTO,
            game_plays=structured_tickets,
            rtp=total_rtp,
        )

        _draw_number = [int(n) for n in draw_instance.draw_number.split(",")]
        response = process_africa_lotto_winners(_draw_number, structured_tickets, draw_data_instance)

        return Response(data=response, status=status.HTTP_200_OK)


@method_decorator(staff_member_required, name="dispatch")
class ManuallyDrawKenya30GamesApiView(APIView):
    serializer_class = ManuallyDrawGhanaGamesSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        batch_id = serializer.validated_data["batch_id"]
        serializer.validated_data["draw_number_db_id"]
        game_type = serializer.validated_data["game_type"]

        if game_type == "GHANA_LOTTO":
            return Response(data={"message": "NOT ALLOWED"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            batch_instance = AfricaLottoBatch.objects.get(batch_uuid=batch_id, game_type=game_type, draw_status=False)
        except AfricaLottoBatch.DoesNotExist:
            return Response(data={"message": "Batch not found"}, status=status.HTTP_400_BAD_REQUEST)

        data = celery_africa_lotto_kenya_draw(
            game_type=game_type, batch_db_id=batch_instance.id, create_new_batch=False
        )

        return Response(data=data, status=status.HTTP_200_OK)


class FetchDrawNumberUsingBatchNumberApiView(APIView):
    permission_classes = []
    permission_classes = []

    serializer_class = FetchDrawNumberUsingBatchNumberSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        batch_number = serializer.validated_data.get("batch_number")

        try:
            batch = AfricaLottoBatch.objects.get(batch_number=batch_number)
        except AfricaLottoBatch.DoesNotExist:
            return Response(data={"message": "Invalid batch number"}, status=status.HTTP_400_BAD_REQUEST)

        qs = AfricaLottoDrawNumbers.objects.filter(batch_id=batch.batch_uuid).last()

        serialized_data = AfricaLottoDrawNumbersSerializers(instance=qs).data

        if qs is None:
            serialized_data["drawn"] = False
        else:
            serialized_data["drawn"] = True

        return Response(data=serialized_data, status=status.HTTP_200_OK)


class TempPublishFireBallSocketView(APIView):
    serializer_class = TempPublishFireBallSocketSerializer

    permission_classes = []
    permission_classes = []

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        fire_balls = serializer.validated_data["fire_balls"]

        if isinstance(fire_balls, str):
            fire_balls = fire_balls.split(",")

        send_fireball_notification(fire_balls)
        # live_k_now_draw_number_socket_update("KW78883455", data)
        return Response(data={"message": "okay"}, status=status.HTTP_200_OK)


class TempUpdateKNowDrawNumbersSocketNotificationView(APIView):
    serializer_class = TempUpdateKNowDrawNumbersSocketNotificationSerializer

    permission_classes = []
    permission_classes = []

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        draw_number = serializer.validated_data.get("draw_number")
        machine_number = serializer.validated_data.get("machine_number")
        batch_number = serializer.validated_data.get("batch_number")

        data = {"draw_number": draw_number, "machine_number": machine_number, "batch_number": batch_number}

        live_k_now_draw_number_socket_update(batch_number, data)

        return Response(data={"message": "okay"}, status=status.HTTP_200_OK)


class TempUpdateKNowNextDrawSocketNotificationView(APIView):
    serializer_class = TempUpdateKNowNextDrawSocketNotificationViewSerializer

    permission_classes = []
    permission_classes = []

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        next_draw_time = serializer.validated_data.get("next_draw_time")

        update_next_k_now_draw(str(next_draw_time))

        return Response(data={"message": "okay"}, status=status.HTTP_200_OK)


class CheckAfricaLottoWinningNumberApiView(APIView):
    permission_classes = []
    permission_classes = []

    serializer_class = CheckAfricaLottoWinningNumberSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_ticket_number = serializer.validated_data.get("user_ticket_number")
        user_ticket_number = [int(i) for i in str(user_ticket_number).split(",")]
        winning_numbers = serializer.validated_data.get("winning_numbers")
        winning_numbers = [int(i) for i in str(winning_numbers).split(",")]
        lottery_type = serializer.validated_data.get("lottery_type")
        stake_per_line = serializer.validated_data.get("stake_per_line")

        result = check_lotto_win(
            user_numbers=user_ticket_number,
            winning_numbers=winning_numbers,
            play_type=str(lottery_type).lower(),
            stake_per_line=stake_per_line,
        )

        return Response(data=result, status=status.HTTP_200_OK)


class FetchGameDraws(APIView):
    def get(self, request):
        query_params = request.GET.get("query_params")

        # Game types
        # GHANA_LOTTO
        # NIGERIA_LOTTO
        # KENYA_LOTTO
        # KENYA_30_LOTTO
        # K_NOW

        if query_params:
            try:
                latest_draw = AfricaLottoDrawNumbers.objects.filter(game_type=query_params).latest("created_at")
                latest_draw_data = AfricaLottoDrawNumbersSerializers(latest_draw).data
                data = {
                    "message": f"Draw fetch process for {query_params} - Completed",
                    "latest_draw": latest_draw_data,
                }

            except AfricaLottoDrawNumbers.DoesNotExist:
                data = {"message": "Draw fetch process - Failed", "latest_draw": []}

            return Response(data=data, status=status.HTTP_200_OK)

        else:
            data = {
                "message": "Invalid query parameter please enter any of the following - "
                "GHANA_LOTTO, NIGERIA_LOTTO, KENYA_LOTTO, KENYA_30_LOTTO, K_NOW",
                "latest_draw": [],
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)
