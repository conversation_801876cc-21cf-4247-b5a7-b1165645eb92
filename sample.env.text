SECRET_KEY=xxxx
ALLOWED_HOSTS=.localhost, 127.0.0.1, 0.0.0.0
DATABASE_NAME=devdb
DATABASE_USER=devuser
DATABASE_PASSWORD=changeme
DATABASE_HOST=lotto_db
DB_PORT=5432
WOVEN_BANK_CODE_SWITCH=xxx
DB_HOST=lotto_db

DEBUG=1
WINWISE_LOTTO_BACKEND_URL=xxxx
ACCOUNT_ACTIVATION_OTP_TEMPLATE=xxxx
AGENCY_BANKING_SUPER_TOKEN=xxxx
LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE_WITH_ACCOUNT=xxxxxx
AGENCY_BANKING_TOKEN=xxxx
LIBERTY_LOTTO_CHECK_WINNERS_LINK_LIST=xxxx
LIBERTY_LOTTO_WINNERS_LIST=xxxxxxx
POS_INSTANT_CASHOUT_TEST_FOR_POS_AGENT=xxxx
LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE=xxxxxx
LIBERTY_LOTTO_PAYMENT_RECEIPT_TEMPLATE=xxx
LIBERTY_LOTTO_DEMO_SMS_FOR_NON_WINNERS=xxxxxx
UVERIFY_TOKEN=xxxx
WATUPAY_PUBLIC_KEY=xxxx
WATUPAY_MERCHANT_REFERENCE=xxxx
DEFAULT_AGENT_ID=xxxx
DEV_TEST_USERS=xxxx
PABBLY_AUTH_USER=xxxx
PABBLY_AUTH_PASS=xxxx

PABBLY_AUTH_USERNAME=xxxx
PABBLY_AUTH_PASSWORD=xxxx

BITLY_TOKEN=xxxx

WOVEN_DEVELOPMENT_MODE=1
TEST_DESTINATION_BANK=xxxx
LIVE_DESTINATION_BANK=xxxx
WOVEN_API_KEY_TEST=xxxx
WOVEN_API_KEY_LIVE=xxxx

WHISPER_KEY=xxxx
LIBERTY_LOTTO_PAYMENT_COLLECTION=xxxx

# Augustine paystack details
PAYSTACK_BEARER=xxxx
PAYSTACK_PUBLIC_KEY=xx

# PAYSTACK_BEARER=
PAYSTACK_PUBLIC_KEY=xxxx

PAYSTACK_ENVIRONMENT=test
PAYSTACK_BS64=xxxx

# PAYSTACK_BEARER=xxxx
# PAYSTACK_BEARER=xxxx

WOVEN_PAYOUT_PIN=xxx

WOVEN_DISBURSEMENT_SOURCE_ACCOUNT=xxxx
WOVEN_DISBURSEMENT_PAYMENT_PIN=xxxx
WOVEN_DISBURSEMENT_API_SECRET=xxxx

LOAN_DISK_SEC_KEY=xxxx
LOAN_DISK_PUBLICK_KEY=xxxx
LOAN_DISK_BRANCH_ID=xxxx


LIBERTY_LOTTO_PAYMENT_COLLECTION=xxxx
LIBERTY_LOTTO_WINNERS_LIST=xxxx
LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE=xxxx
LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE_WITH_ACCOUNT=xxxx
WINWISE_LOTTO_COLLECTION_S4L_YCASH=xxxxxxx
WINWISE_LOTTO_COLLECTION_INSTANTCASH=xxxxxxxxxxx



PICKY_ASSIST_TOKEN=xxxx
WEMA_BANK_CODE=xxxx
CMB_BANK_CODE=xxxx
SPARKLE_BANK_CODE=xxxx

WOVEN_CALL_BACK=xx
LIBERTY_LOTTO_RESEND_ACCOUNT_COLLECT_SMS=xx

CLOUDINARY_CLOUD_NAME=xxxx
CLOUDINARY_API_KEY=xxxx
CLOUDINARY_API_SECRET=xxxx

TRANSACTION_OTP_TEMPLATE=xxxx

REFERRAL_SMS_TEMPLATE=xxxx

RawPayStack=xx

LOTTO_FRONTEND_LINK=xxxx

libertyassured = xxxx
libertypay = xxxx
libertycredi = xxxx

MAILGUN_API_KEY=xxxx

AGENCY_BANKING_BASE_URL=xxxx
AGENCY_BANKING_USEREMAIL=xxxx
AGENCY_BANKING_PASSWORD=xxxx

RED_BILLER_PRIVATE_KEY=xxxx
RED_BILLER_AUTH_BEARER=xx

CORAL_PAY_USERNAME=xxxx
CORAL_PAY_PASSWORD=xxxxx
CORAL_PAY_MERCHANT_ID=XXX


CORAL_PAY_AUTH_USERNAME=xxxx
CORAL_PAY_AUTH_PASSWORD=XXx

ENGAGE_API_KEY=xxxxxxxxxxxx
ENGAGE_SECRET_KEY=xxxxxxxxxxxx

CUTTLY_API_KEY=XXXX
LIBERTY_VAS_BASE_URL=xxxxxxxxxxxxxxxxxxx
LIBERTY_VAS_AUTH_USERNAME=xxxxxxxxxxxxxxxxx
LIBERTY_VAS_AUTH_PASSWORD=xxxxxxxxxxxxxxxxx
USER_BVN=xxxxxxxxxxx

CHAMPIONS_LEAGUE_SEASON_YEAR=xx
ENGLISH_LEAGUE_SEASON_YEAR=xx
EUROPA_LEAGUE_SEASON_YEAR=xx
LA_LIGA_SEASON_YEAR=xx
WORLD_CUP_SEASON_YEAR=xx
FOOTBALL_API_KEY=xx
WORLD_CUP_SEASON_YEAR=xx

TELEGRAM_BOT_TOKEN=xx
ACTIVATE_BOT=False

MAILGUN_WEBHOOK_SIGNING_KEY=xx

LIBERTYPAY_USER_AUTH=xxxxxxxxxxxxxxxxx
LIBERTYPAY_AGENTS_AUTH=xxxxxxxxxxxxxxxxx

PAYSTACK_CALLBACK_URL=xxxxxxxxxxxxxxxx
AGENCY_BANKING_TRANSACTION_PIN=xxxxxxxxxx
IP_WHITELIST=xxxxxxxxxxx
DOMAIN_WHITELIST=xxxxxxxxxxxx
WHITELISTING_TO_USE=xxxxxxxxxxxx

AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID=xxxxxxxxxxxxx

SELF_SERVICE_AUTH=xxxxxxxxxxxx

REMITTANCE_EMAIL_LIST=xxxxxxxxxxxxxxxx

BBC_PARTNER_ID=x
BBC_PARTNER_PASSWORD=x
BBC_SERVICE_ID=x
BBC_USSD_SERVICE_ACTIVATION_NUMBER=x
BBC_IP=x
BBC_ON_DEMAND_SERVICE_USERNAME=x
BBC_ON_DEMAND_SERVICE_PASSWORD=x
BBC_ON_DEMAND_SERVICE_ID=x
BBC_ON_DEMAND_SERVICE_PRODUCT_ID=x
BBC_SMS_SERVICE_USERNAME=x
BBC_SMS_SERVICE_PASSWORD=x


SCRATCH_CARD_EMAIL=
SCRATCH_CARD_PASSWORD=


EXTERNAL_DB_NAME=
EXTERNAL_DB_PASSWORD=
EXTERNAL_DB_USERNAME=
EXTERNAL_DB_HOST=

ZENITH_USER=
ZENITH_PROTECTOR=
ZENITH_SOURCE_ACCOUNT=


SECURED_D_IP_ADDRESS

234809RETAIL1=

CORE_BANKING_EMAIL=
CORE_BANKING_PASSWORD=

LIBERTY_USSD_BASE_URL=

BRANCH_LOCATION_CODES=

AIRTIME_VENDING_AK=
AIRTIME_VENDING_SERVER=

OPENAI_API_KEY=

COMMISSION_AGENCY_BANKING_USEREMAIL=
COMMISSION_AGENCY_BANKING_PASSWORD=

EXTERNAL_DB2_NAME=
EXTERNAL_DB2_USERNAME=
EXTERNAL_DB2_PASSWORD=
EXTERNAL_DB2_HOST=
DIGITALOCEAN_KEY=
GITHUB_TOKEN=

PERPLEXITY_API_TOKEN=

REDIS_HOST=localhost

ENVIRONMENT=dev
CORE_REDIS_HOST=localhost

NITROSWITCH_X_TOKEN=xx