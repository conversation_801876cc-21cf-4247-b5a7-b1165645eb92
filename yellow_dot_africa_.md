# YELLOW DOT AFRICA SERVICE



## Database Model
1. Create YellowDotAfricaDatasync Model
    <br />
    :: This model holds the request data (dump data) sent by them for<br />
    (1) Subscription <br >
    (2) Update (Modification)<br >
    (3) Deletion (Unsubscription)<br />

2. YellowDotAfricaSubscriber <br />
    :: This model will store unique phone number of each subscriber and the day they go into the system

3. YellowDotAfricaSmsMODatasync <br />
    :: This model stores datasync from sms mo endpoint

4. YelloDotAfricaDailySubscription <br />
    :: This model keeps record of daily activities of users, When the datasync comes in, this table keeps record of the activities. <br />
    fields: <br />
    1. phone_number
    2. service name
    3. subscription type (DAILY, ON_DEMAND)
    4. subscription status (FAILED, DEACTIVATED, ACTIVATION, RENEWAL)
    5. amount
    6. service_id
    7. product_id
    8. unique_reference (reference from the datasync + created date)
    9. transactionId

5. YelloDotAfricaSubscriptionData <br />
    :: This data holds record of user subscription, it can only be active, deactivated or one time <br />
    :: When user unsubscriber from a particular product, it come to update the product as deactivate<br />
    :: This is unique by user phone, product id and status as active



###### <br />

## ENDPOINT
1. crreate endpoint to accept datasync and store it in the model <br />
    :: Subscription and Modification
2. Endpoint to accept unsubscription datasync stores it in datasync model
3. Endpoint for smsmo datasync and store it in sms mo datasync
4. Create a function that send sms via yellow dot africa service



    