from datetime import date, datetime, timedelta
from decimal import Decimal

from django.db.models import Avg, Count, Q, Sum
from django.utils import timezone

from pos_app.models import Agent, LottoAgentDailySalesActivity
from retail_metrics.models import AgentMetricSummary


class MetricsCalculator:
    """Helper class to calculate dynamic metrics"""

    @staticmethod
    def get_date_range(period):
        """Get start and end dates for different periods"""
        now = timezone.now()
        today = now.date()

        if period == "today":
            return today, today
        elif period == "yesterday":
            yesterday = today - timedelta(days=1)
            return yesterday, yesterday
        elif period == "this_week":
            # Monday as start of week
            start = today - timedelta(days=today.weekday())
            return start, today
        elif period == "last_week":
            # Previous week Monday to Sunday
            start = today - timedelta(days=today.weekday() + 7)
            end = today - timedelta(days=today.weekday() + 1)
            return start, end
        elif period == "this_month":
            start = today.replace(day=1)
            return start, today
        elif period == "last_month":
            first_this_month = today.replace(day=1)
            last_month_end = first_this_month - timedelta(days=1)
            last_month_start = last_month_end.replace(day=1)
            return last_month_start, last_month_end
        elif period == "this_year":
            start = today.replace(month=1, day=1)
            return start, today

        return None, None

    @staticmethod
    def calculate_agent_metrics(period="today"):
        """Calculate agent-related metrics for a specific period"""
        start_date, end_date = MetricsCalculator.get_date_range(period)

        if not start_date or not end_date:
            return {}

        total_agents = Agent.objects.filter(wave="WAVE_TWO", created_date__date__range=[start_date, end_date])

        # Active agents (agents who were present in the period)
        active_agents = LottoAgentDailySalesActivity.objects.filter(
            created_at__date__range=[start_date, end_date],
        )

        # Inactive agents (agents who were not present)
        inactive_agents = (
            AgentMetricSummary.objects.filter(created_at__date__range=[start_date, end_date], is_present=False)
            .values("agent")
            .distinct()
            .count()
        )

        # Absent agents (agents with no activity record)
        agents_with_activity = (
            AgentMetricSummary.objects.filter(created_at__date__range=[start_date, end_date])
            .values("agent")
            .distinct()
        )

        absent_agents = total_agents - agents_with_activity.count()

        # Percentage active
        percentage_active = (active_agents / total_agents * 100) if total_agents > 0 else 0

        # Sales metrics
        period_activities = AgentMetricSummary.objects.filter(created_at__date__range=[start_date, end_date])

        total_sales = period_activities.aggregate(total=Sum("sales_amount"))["total"] or 0

        total_commissions = period_activities.aggregate(total=Sum("commission_amount"))["total"] or 0

        total_winnings = period_activities.aggregate(total=Sum("winnings_amount"))["total"] or 0

        # Average sales per agent
        avg_sales = period_activities.aggregate(avg=Avg("sales_amount"))["avg"] or 0

        # Top and bottom agents
        agent_totals = (
            period_activities.values("agent__name").annotate(total_sales=Sum("sales_amount")).order_by("-total_sales")
        )

        top_agent = agent_totals.first()["agent__name"] if agent_totals else "N/A"
        bottom_agent = agent_totals.last()["agent__name"] if agent_totals else "N/A"

        return {
            "Total Agents": str(total_agents),
            "Active Agents": str(active_agents),
            "Inactive Agents": str(inactive_agents),
            "Absent Agent": str(absent_agents),
            "Percentage Active": f"{percentage_active:.1f}%",
            "Agents Under Recovery": "0",  # You'll need to define this logic
            "Agents Achieving >75% Target": "0",  # You'll need to define this logic
            "Total Sales (Month)": f"₦ {total_sales:,.0f}",
            "Total Commissions": f"₦ {total_commissions:,.0f}",
            "Total Winnings": f"₦ {total_winnings:,.0f}",
            "Average Sales per Agent": f"₦ {avg_sales:,.0f}",
            "Top Agent (Month)": top_agent,
            "Bottom Agent (Month)": bottom_agent,
        }

    @staticmethod
    def update_metrics_for_period(period):
        """Update metrics for a specific time period"""
        metrics = MetricsCalculator.calculate_agent_metrics(period)

        for metric_name, value in metrics.items():
            AgentMetricSummary.set_metric_value(metric_name, period, value)

    @staticmethod
    def update_all_dynamic_metrics():
        """Update all dynamic metrics (today, yesterday, this_week, etc.)"""
        periods = ["today", "yesterday", "this_week", "last_week", "this_month", "last_month", "this_year"]

        for period in periods:
            MetricsCalculator.update_metrics_for_period(period)


from datetime import timedelta

from django.db.models import Avg, Count, Sum
from django.utils import timezone


class SalesBasedMetricsCalculator:
    """Calculator for metrics based on sales performance thresholds"""

    THRESHOLDS = {
        "daily": 10000,
        "weekly": 30000,
        "monthly": 120000,
        "yearly": 1440000,
        "all_time": 1440000,
    }

    @staticmethod
    def get_active_agents_by_sales(wave, start_date, end_date, period_type):
        """
        Get agents who meet sales threshold for being considered 'active'

        Args:
            wave: Wave filter (e.g., "WAVE_TWO")
            start_date: Start date for calculation
            end_date: End date for calculation
            period_type: 'daily', 'weekly', or 'monthly'

        Returns:
            dict: Contains count and list of active agents
        """

        # Get total agents in the wave created in the period
        total_agents = Agent.objects.filter(wave=wave, created_date__date__range=[start_date, end_date])

        # Calculate days in period
        days_in_period = (end_date - start_date).days + 1

        # Get threshold based on period type
        if period_type == "daily":
            threshold = SalesBasedMetricsCalculator.THRESHOLDS["daily"]
        elif period_type == "weekly":
            threshold = SalesBasedMetricsCalculator.THRESHOLDS["weekly"]
        elif period_type == "monthly":
            threshold = SalesBasedMetricsCalculator.THRESHOLDS["monthly"]
        elif period_type == "yearly":
            threshold = SalesBasedMetricsCalculator.THRESHOLDS["yearly"]
        else:
            threshold = SalesBasedMetricsCalculator.THRESHOLDS["all_time"]

        # Get sales data for agents in the period
        print(
            "Calculating active agents for wave:",
            wave,
            "from",
            start_date,
            "to",
            end_date,
            "with threshold:",
            threshold,
        )
        agent_sales = LottoAgentDailySalesActivity.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).annotate(total_sales=Sum("sales"), total_winnings=Sum("winnings"))

        active_agents = []
        active_agents_count = 0
        inactive_agents_count = 0

        print("agent_sales", agent_sales, "\n\n\n")

        for agent_data in agent_sales:
            print("agent_data", agent_data, "\n\n\n")
            total_sales = agent_data.total_sales or 0
            total_winnings = agent_data.total_winnings or 0

            # Calculate if agent meets threshold
            is_active = False

            if period_type == "daily":
                # For daily, total sales should be >= 10k
                is_active = total_sales >= threshold

            elif period_type == "weekly":
                # For weekly, average daily sales should be >= 30k
                avg_daily_sales = total_sales / 7
                is_active = avg_daily_sales >= threshold

            elif period_type == "monthly":
                # For monthly/yearly, average daily sales should be >= 120k
                avg_daily_sales = total_sales / days_in_period
                is_active = avg_daily_sales >= threshold

            if is_active:
                active_agents_count += 1
                active_agents.append(
                    {
                        "total_sales": total_sales,
                        "total_winnings": total_winnings,
                        "avg_daily_sales": total_sales / days_in_period,
                        "meets_threshold": True,
                    }
                )
            else:
                inactive_agents_count += 1
                active_agents.append(
                    {
                        "total_sales": total_sales,
                        "total_winnings": total_winnings,
                        "avg_daily_sales": total_sales / days_in_period,
                        "meets_threshold": True,
                    }
                )

        return {
            "count": active_agents_count,
            "in_active_agent": inactive_agents_count,
            "agents": active_agents,
            "threshold": threshold,
            "period_type": period_type,
        }

    @staticmethod
    def calculate_agent_metrics_by_sales(wave, period="today"):
        """Calculate all agent metrics based on sales performance"""
        start_date, end_date = SalesBasedMetricsCalculator.get_date_range(period)

        if not start_date or not end_date:
            return {}

        # Determine period type for threshold calculation
        if period in ["today", "yesterday"]:
            period_type = "daily"
        elif period in ["this_week", "last_week"]:
            period_type = "weekly"
        elif period in ["this_month", "last_month"]:
            period_type = "monthly"
        elif period == "this_year":
            period_type = "yearly"
        else:
            period_type = "all_time"

        print("Start Date:", start_date, "End Date:", end_date, "Period Type:", period_type, "\n\n\n")

        # Get total agents in wave
        total_agents = Agent.objects.filter(wave=wave, created_date__date__range=[start_date, end_date]).count()

        # Get active agents based on sales
        active_data = SalesBasedMetricsCalculator.get_active_agents_by_sales(wave, start_date, end_date, period_type)

        active_agents = active_data["count"]

        inactive_agents = active_data["in_active_agent"]

        absent_agents = total_agents - (active_agents + inactive_agents)
        if absent_agents < 0:
            absent_agents = 0

        # Percentage active
        percentage_active = (active_agents / total_agents * 100) if total_agents > 0 else 0

        # Sales totals
        sales_data = LottoAgentDailySalesActivity.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).aggregate(total_sales=Sum("sales"), total_winnings=Sum("winnings"))

        total_sales = sales_data["total_sales"] or 0
        total_commissions = 0
        total_winnings = sales_data["total_winnings"] or 0

        # Average sales per agent
        avg_sales = total_sales / total_agents if total_agents > 0 else 0

        # Top and bottom agents by sales
        top_agents = (
            LottoAgentDailySalesActivity.objects.filter(created_at__date__range=[start_date, end_date])
            .values("agent_phone_number")
            .annotate(total_sales=Sum("sales"))
            .order_by("-total_sales")
        )

        top_agent = top_agents.first()["agent_phone_number"] if top_agents else "N/A"
        bottom_agent = top_agents.last()["agent_phone_number"] if top_agents else "N/A"

        return {
            "Total Agents": str(total_agents),
            "Active Agents": str(active_agents),
            "Inactive Agents": str(inactive_agents),
            "Absent Agent": str(absent_agents),
            "Percentage Active": f"{percentage_active:.1f}%",
            "Total Sales (Month)": f"₦ {total_sales:,.0f}",
            "Total Commissions": f"₦ {total_commissions:,.0f}",
            "Total Winnings": f"₦ {total_winnings:,.0f}",
            "Average Sales per Agent": f"₦ {avg_sales:,.0f}",
            "Top Agent (Month)": top_agent,
            "Bottom Agent (Month)": bottom_agent,
            "Active Threshold": f"₦ {active_data['threshold']:,.0f}",
            "Period Type": period_type,
        }

    @staticmethod
    def get_date_range(period):
        """Get start and end dates for different periods"""
        now = timezone.now()
        today = now.date()

        if period == "today":
            return today, today
        elif period == "yesterday":
            yesterday = today - timedelta(days=1)
            return yesterday, yesterday
        elif period == "this_week":
            start = today - timedelta(days=today.weekday())
            return start, today
        elif period == "last_week":
            start = today - timedelta(days=today.weekday() + 7)
            end = today - timedelta(days=today.weekday() + 1)
            return start, end
        elif period == "this_month":
            start = today.replace(day=1)
            return start, today
        elif period == "last_month":
            first_this_month = today.replace(day=1)
            last_month_end = first_this_month - timedelta(days=1)
            last_month_start = last_month_end.replace(day=1)
            return last_month_start, last_month_end
        elif period == "this_year":

            current_year = datetime.now().year
            start = date(current_year, 1, 1)
            end = date(current_year, 12, 31)

            start = today.replace(month=1, day=1)
            return start, end

        return Agent.objects.last().created_date.date(), datetime.now().date()
