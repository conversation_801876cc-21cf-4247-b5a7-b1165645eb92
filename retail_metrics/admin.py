from datetime import datetime

import pytz
from django.conf import settings
from django.contrib import admin, messages
from django.db.models import Sum
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from pos_app.models import Agent, AgentWalletTransaction, LottoAgentRemittanceTable
from retail_metrics.models import (
    AgentMetrics,
    AgentMetricSummary,
    DailyFundingWalletActivity,
    GameAnalytics,
    LottoAgentAnalytic,
    LottoAgentDailyAnalytic,
    LottoAgentMonthlyAnalytic,
    LottoSupervisorAnalytic,
    LottoSupervisorDailyAnalytic,
    LottoSupervisorMonthlyAnalytic,
    LottoVerticalLeadAnalytic,
    LottoVerticalLeadDailyAnalytic,
    LottoVerticalLeadMonthlyAnalytic,
    PayoutAnalytics,
    RemittanceAnalytics,
    RetailMetricsConstant,
    RetailWallet,
    RetailWalletTransactions,
    RetrieveTerminalsData,
    ReturnToPlayerAndReturnToOwnerAnalytics,
    WalletFundingAnalytics,
)


# Register your models here.
class AgentMetricsResource(resources.ModelResource):
    class Meta:
        model = AgentMetrics


class RetrieveTerminalsDataResource(resources.ModelResource):
    class Meta:
        model = RetrieveTerminalsData


class GameAnalyticsResource(resources.ModelResource):
    class Meta:
        model = GameAnalytics


class ReturnToPlayerAndReturnToOwnerAnalyticsResource(resources.ModelResource):
    class Meta:
        model = ReturnToPlayerAndReturnToOwnerAnalytics


class WalletFundingAnalyticsResource(resources.ModelResource):
    class Meta:
        model = WalletFundingAnalytics


class PayoutAnalyticsResource(resources.ModelResource):
    class Meta:
        model = PayoutAnalytics


class RemittanceAnalyticsResource(resources.ModelResource):
    class Meta:
        model = RemittanceAnalytics


class RetailWalletTransactionsResource(resources.ModelResource):
    class Meta:
        model = RetailWalletTransactions


class RetailWalletResource(resources.ModelResource):
    class Meta:
        model = RetailWallet


class LottoVerticalLeadDailyAnalyticResource(resources.ModelResource):
    class Meta:
        model = LottoVerticalLeadDailyAnalytic


class LottoSupervisorDailyAnalyticResource(resources.ModelResource):
    class Meta:
        model = LottoSupervisorDailyAnalytic


class LottoSupervisorMonthlyAnalyticResource(resources.ModelResource):
    class Meta:
        model = LottoSupervisorMonthlyAnalytic


class RetailMetricsConstantResource(resources.ModelResource):
    class Meta:
        model = RetailMetricsConstant


class LottoAgentMonthlyAnalyticResource(resources.ModelResource):
    class Meta:
        model = LottoAgentMonthlyAnalytic


class LottoAgentDailyAnalyticResource(resources.ModelResource):
    class Meta:
        model = LottoAgentDailyAnalytic


class DailyFundingWalletActivityResource(resources.ModelResource):
    class Meta:
        model = DailyFundingWalletActivity


class LottoVerticalLeadMonthlyAnalyticResource(resources.ModelResource):
    class Meta:
        model = LottoVerticalLeadMonthlyAnalytic


class LottoVerticalLeadAnalyticResource(resources.ModelResource):
    class Meta:
        model = LottoVerticalLeadAnalytic


class LottoSupervisorAnalyticResource(resources.ModelResource):
    class Meta:
        model = LottoSupervisorAnalytic


class LottoAgentAnalyticResource(resources.ModelResource):
    class Meta:
        model = LottoAgentAnalytic


class AgentMetricSummaryResource(resources.ModelResource):
    class Meta:
        model = AgentMetricSummary


class AgentMetricsResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentMetricsResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = ["updaterecord"]

    @admin.action(description="UPDATE RECORDS")
    def updaterecord(modeladmin, request, queryset):
        for obj in queryset:
            agents = Agent.objects.all()
            obj.total_agents = len(agents)
            obj.total_merchants = len(agents.filter(agent_type="MERCHANT"))
            obj.total_lotto_agents = len(agents.filter(agent_type="LOTTO_AGENT"))
            obj.total_personal_accounts = len(agents.filter(agent_type="PERSONAL"))
            obj.total_liberty_retail = len(agents.filter(agent_type="LIBERTY_RETAIL"))
            obj.save()

            break

        messages.success(request, "UPDATED SUCCESSFULLY")


class RetrieveTerminalsDataResourceAdmin(ImportExportModelAdmin):
    resource_class = RetrieveTerminalsDataResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    search_fields = ["phone_number", "first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = ["updaterecord"]

    @admin.action(description="UPDATE RECORDS")
    def updaterecord(modeladmin, request, queryset):
        for obj in queryset:
            agents = Agent.objects.filter(terminal_id__isnull=False, terminal_retrieved=True)
            for agent in agents:
                try:
                    RetrieveTerminalsData.objects.create(
                        phone_number=agent.phone,
                        first_name=agent.first_name,
                        last_name=agent.last_name,
                        full_name=agent.full_name,
                        terminal_id=agent.terminal_id,
                    )
                except:
                    pass

            break

        messages.success(request, "UPDATED SUCCESSFULLY")


class GameAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = GameAnalyticsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at", "year", "month", "game_type"]

    # search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = ["updatethismonthrecord"]

    @admin.action(description="UPDATE THIS MONTH RECORDS")
    def updatethismonthrecord(modeladmin, request, queryset):
        for obj in queryset:
            TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

            this_month = TODAY.month
            this_year = TODAY.year

            query_set_for_winnings = AgentWalletTransaction.objects.filter(
                date_created__year=this_year, transaction_from="WINNINGS", game_type=obj.game_type
            ).filter(ate_created__month=this_month)
            query_set_for_game_play = AgentWalletTransaction.objects.filter(
                date_created__year=this_year, transaction_from="GAME_PLAY", game_type=obj.game_type
            ).filter(ate_created__month=this_month)

            this_month_winnings = query_set_for_winnings.aggregate(Sum("amount")).get("amount__sum")
            if this_month_winnings is None:
                this_month_winnings = 0

            this_month_sales = query_set_for_game_play.aggregate(Sum("amount")).get("amount__sum")
            if this_month_sales is None:
                this_month_sales = 0

            GameAnalytics.update_record(
                game_type=obj.game_type,
                sales_amount=this_month_sales,
                winning_amount=this_month_winnings,
                year=this_year,
                month=this_month,
            )

        messages.success(request, "UPDATED SUCCESSFULLY")


class ReturnToPlayerAndReturnToOwnerAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = ReturnToPlayerAndReturnToOwnerAnalyticsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at", "year", "month", "game_type"]

    # search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WalletFundingAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = WalletFundingAnalyticsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at", "year", "month"]

    # search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PayoutAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = PayoutAnalyticsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at", "year", "month"]

    # search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RemittanceAnalyticsResourceAdmin(ImportExportModelAdmin):
    resource_class = RemittanceAnalyticsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at", "year", "month"]

    # search_fields = ["phone_number","first_name", "last_name", "full_name", "terminal_id"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    ctions = ["updatethismonthrecord"]

    @admin.action(description="UPDATE THIS MONTH RECORDS")
    def updatethismonthrecord(modeladmin, request, queryset):
        for obj in queryset:
            TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

            this_month = TODAY.month
            this_year = TODAY.year

            queryset = LottoAgentRemittanceTable.objects.filter(created_at__year=this_year).filter(
                reated_at__month=this_month
            )
            amount_remitted = queryset.filter(remitted=True).aggregate(Sum("amount_paid")).get("amount_paid__sum")
            if amount_remitted is None:
                amount_remitted = 0

            amount_due = queryset.filter(remitted=False, due=True).aggregate(Sum("amount")).get("amount__sum")
            if amount_due is None:
                amount_due = 0

            obj.amount_remitted = amount_remitted
            obj.amount_due = amount_due
            obj.save()

            break


class RetailWalletTransactionsResourceAdmin(ImportExportModelAdmin):
    resource_class = RetailWalletTransactionsResource

    date_hierarchy = "created_at"

    list_filter = ["created_at", "transaction_type", "transaction_category"]

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]

        data.remove("wallet_value")

        return data


class RetailWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = RetailWalletResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
        "wallet_type",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoVerticalLeadDailyAnalyticResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoVerticalLeadDailyAnalyticResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    search_fields = [
        "first_name",
        "last_name",
        "full_name",
        "phone",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoSupervisorDailyAnalyticResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoSupervisorDailyAnalyticResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    search_fields = ["first_name", "last_name", "full_name", "phone"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoSupervisorMonthlyAnalyticResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoSupervisorMonthlyAnalyticResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    search_fields = ["first_name", "last_name", "full_name", "phone"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RetailMetricsConstantResourceAdmin(ImportExportModelAdmin):
    resource_class = RetailMetricsConstantResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoAgentDailyAnalyticResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoAgentDailyAnalyticResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    search_fields = ["first_name", "last_name", "full_name", "phone", "supervisor_phone", "vertical_lead_phone"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoAgentMonthlyAnalyticResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoAgentMonthlyAnalyticResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    search_fields = ["first_name", "last_name", "full_name", "phone", "supervisor_phone", "vertical_lead_phone"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DailyFundingWalletActivityResourceAdmin(ImportExportModelAdmin):
    resource_class = DailyFundingWalletActivityResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoVerticalLeadMonthlyAnalyticResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoVerticalLeadMonthlyAnalyticResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    search_fields = [
        "first_name",
        "last_name",
        "full_name",
        "phone",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoVerticalLeadAnalyticResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoVerticalLeadAnalyticResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    search_fields = [
        "first_name",
        "last_name",
        "full_name",
        "phone",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoSupervisorAnalyticResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoSupervisorAnalyticResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    search_fields = [
        "first_name",
        "last_name",
        "full_name",
        "phone",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoAgentAnalyticResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoAgentAnalyticResource

    date_hierarchy = "created_at"

    list_filter = [
        "created_at",
    ]

    search_fields = [
        "first_name",
        "last_name",
        "full_name",
        "phone",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentMetricSummaryResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentMetricSummaryResource

    date_hierarchy = "created_at"

    list_filter = ["created_at", "metric_name"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(AgentMetrics, AgentMetricsResourceAdmin)
admin.site.register(RetrieveTerminalsData, RetrieveTerminalsDataResourceAdmin)
admin.site.register(GameAnalytics, GameAnalyticsResourceAdmin)
admin.site.register(ReturnToPlayerAndReturnToOwnerAnalytics, ReturnToPlayerAndReturnToOwnerAnalyticsResourceAdmin)
admin.site.register(WalletFundingAnalytics, WalletFundingAnalyticsResourceAdmin)
admin.site.register(PayoutAnalytics, PayoutAnalyticsResourceAdmin)
admin.site.register(RemittanceAnalytics, RemittanceAnalyticsResourceAdmin)
admin.site.register(RetailWalletTransactions, RetailWalletTransactionsResourceAdmin)
admin.site.register(LottoVerticalLeadDailyAnalytic, LottoVerticalLeadDailyAnalyticResourceAdmin)
admin.site.register(LottoSupervisorDailyAnalytic, LottoSupervisorDailyAnalyticResourceAdmin)
admin.site.register(RetailMetricsConstant, RetailMetricsConstantResourceAdmin)
admin.site.register(LottoAgentDailyAnalytic, LottoAgentDailyAnalyticResourceAdmin)
admin.site.register(LottoAgentMonthlyAnalytic, LottoAgentMonthlyAnalyticResourceAdmin)
admin.site.register(LottoSupervisorMonthlyAnalytic, LottoSupervisorMonthlyAnalyticResourceAdmin)
admin.site.register(DailyFundingWalletActivity, DailyFundingWalletActivityResourceAdmin)
admin.site.register(LottoVerticalLeadMonthlyAnalytic, LottoVerticalLeadMonthlyAnalyticResourceAdmin)
admin.site.register(LottoVerticalLeadAnalytic, LottoVerticalLeadAnalyticResourceAdmin)
admin.site.register(LottoSupervisorAnalytic, LottoSupervisorAnalyticResourceAdmin)
admin.site.register(LottoAgentAnalytic, LottoAgentAnalyticResourceAdmin)
admin.site.register(AgentMetricSummary, AgentMetricSummaryResourceAdmin)
