from datetime import datetime, timedelta

# Create your models here.
import pytz
from django.conf import settings
from django.db import models, transaction
from django.db.models import Q


class RetailMetricsConstant(models.Model):
    agent_avg_game_sales_period_days = models.IntegerField(default=14)
    agent_target_sales_on_avg = models.IntegerField(default=8000)
    agent_daily_sales_target = models.IntegerField(default=20000)
    agent_weekly_sales_target = models.IntegerField(default=140000)
    agent_monthly_sales_target = models.IntegerField(default=600000)
    agent_sales_at_risk_threshold = models.IntegerField(default=4000)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RETAIL METRICS CONSTANT"
        verbose_name_plural = "RETAIL METRICS CONSTANTS"

    @classmethod
    def get_agent_avg_game_sales_period_days(cls):
        return cls.objects.last().agent_avg_game_sales_period_days if cls.objects.last() != None else 14

    @classmethod
    def get_agent_daily_sales_target(cls):
        return cls.objects.last().agent_daily_sales_target if cls.objects.last() != None else 20000

    @classmethod
    def get_agent_weekly_sales_target(cls):
        return cls.objects.last().agent_weekly_sales_target if cls.objects.last() != None else 140000

    @classmethod
    def get_agent_monthly_sales_target(cls):
        return cls.objects.last().agent_monthly_sales_target if cls.objects.last() != None else 600000

    @classmethod
    def get_agent_target_sales_on_avg(cls):
        return cls.objects.last().agent_target_sales_on_avg if cls.objects.last() != None else 8000

    @classmethod
    def get_agent_sales_at_risk_threshold(cls):
        return cls.objects.last().agent_sales_at_risk_threshold if cls.objects.last() != None else 4000


class AgentMetrics(models.Model):
    total_agents = models.IntegerField(default=0)
    total_merchants = models.IntegerField(default=0)
    total_lotto_agents = models.IntegerField(default=0)
    total_personal_accounts = models.IntegerField(default=0)
    total_liberty_retail = models.IntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AGENT COUNT METRICS TABLE"
        verbose_name_plural = "AGENT COUNT METRICS TABLE"


class RetrieveTerminalsData(models.Model):
    phone_number = models.CharField(max_length=300, unique=True)
    first_name = models.CharField(max_length=300)
    last_name = models.CharField(max_length=300)
    full_name = models.CharField(max_length=300, blank=True, null=True)
    terminal_id = models.CharField(max_length=300, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RETRIEVED TERMINAL DATA"
        verbose_name_plural = "RETRIEVED TERMINAL DATAS"


class GameAnalytics(models.Model):

    LOTTERY_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("QUIKA", "QUIKA"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("BANKER", "BANKER"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW"),
    ]

    game_type = models.CharField(max_length=300, choices=LOTTERY_TYPE_CHOICES)
    sales = models.FloatField(default=0)
    winnings = models.FloatField(default=0)
    year = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GAME ANALYTICS"
        verbose_name_plural = "GAME ANALYTICS"

    @classmethod
    def add_or_create_record(cls, game_type, sales_amount=0, winning_amount=0):
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        this_month = TODAY.month
        this_year = TODAY.year

        try:
            instance = cls.objects.get(year=this_year, month=this_month, game_type=game_type)
        except cls.DoesNotExist:
            instance = cls.objects.create(year=this_year, month=this_month, game_type=game_type)

        instance.sales += sales_amount
        instance.winnings += winning_amount
        instance.save()

    @classmethod
    def update_record(cls, game_type, sales_amount, winning_amount, year, month):

        try:
            instance = cls.objects.get(year=year, month=month, game_type=game_type)
        except cls.DoesNotExist:
            instance = cls.objects.create(year=year, month=month, game_type=game_type)

        instance.winnings = winning_amount
        instance.sales = sales_amount
        instance.save()

        return instance


class ReturnToPlayerAndReturnToOwnerAnalytics(models.Model):

    LOTTERY_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("QUIKA", "QUIKA"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("BANKER", "BANKER"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW"),
    ]

    game_type = models.CharField(max_length=300, choices=LOTTERY_TYPE_CHOICES)
    rtp = models.IntegerField(default=0)
    rto = models.IntegerField(default=0)
    year = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RETURN TO PLAYER & RETURN TO OWNER ANALYTICS"
        verbose_name_plural = "RETURN TO PLAYER & RETURN TO OWNER ANALYTICS"

    @classmethod
    def add_or_create_record(cls, game_type, rtp=0, rto=0):
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        this_month = TODAY.month
        this_year = TODAY.year

        try:
            instance = cls.objects.get(year=this_year, month=this_month, game_type=game_type)
        except cls.DoesNotExist:
            instance = cls.objects.create(year=this_year, month=this_month, game_type=game_type)

        instance.rtp += rtp
        instance.rto += rto
        instance.save()

    @classmethod
    def update_record(cls, game_type, rtp, rto, year, month):

        try:
            instance = cls.objects.get(year=year, month=month, game_type=game_type)
        except cls.DoesNotExist:
            instance = cls.objects.create(year=year, month=month, game_type=game_type)

        instance.rto = rto
        instance.rtp = rtp
        instance.save()

        return instance


class WalletFundingAnalytics(models.Model):
    amount = models.FloatField(default=0.0)
    year = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "WALLET FUNDING ANALYTICS"
        verbose_name_plural = "WALLET FUNDING ANALYTICS"

    @classmethod
    def add_or_create_record(cls, amount=0):
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        this_month = TODAY.month
        this_year = TODAY.year

        try:
            instance = cls.objects.get(
                year=this_year,
                month=this_month,
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year=this_year,
                month=this_month,
            )

        instance.amount += amount
        instance.save()

    @classmethod
    def update_record(cls, amount, year, month):

        try:
            instance = cls.objects.get(
                year=year,
                month=month,
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year=year,
                month=month,
            )

        instance.amount = amount
        instance.save()

        return instance


class PayoutAnalytics(models.Model):
    amount = models.FloatField(default=0.0)
    year = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "PAYOUT ANALYTICS"
        verbose_name_plural = "PAYOUT ANALYTICS"

    @classmethod
    def add_or_create_record(cls, amount=0):
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        this_month = TODAY.month
        this_year = TODAY.year

        try:
            instance = cls.objects.get(
                year=this_year,
                month=this_month,
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year=this_year,
                month=this_month,
            )

        instance.amount += amount
        instance.save()

    @classmethod
    def update_record(cls, amount, year, month):

        try:
            instance = cls.objects.get(
                year=year,
                month=month,
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year=year,
                month=month,
            )

        instance.amount = amount
        instance.save()

        return instance


class RemittanceAnalytics(models.Model):
    amount_remitted = models.FloatField(default=0.0)
    amount_due = models.FloatField(default=0.0)
    year = models.IntegerField(default=0)
    month = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "REMITTANCE ANALYTICS"
        verbose_name_plural = "REMITTANCE ANALYTICS"

    @classmethod
    def update_record(cls, month, year, amount_remitted, amount_due):

        try:
            instance = cls.objects.get(
                year=year,
                month=month,
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                year=year,
                month=month,
            )

        instance.amount_remitted = amount_remitted
        instance.amount_due = amount_due
        instance.save()

        return instance


class RetailWallet(models.Model):

    WALLET_SOURCE = [("PAYOUT", "PAYOUT"), ("COMMISSION", "COMMISSION"), ("GAME_PLAY", "GAME_PLAY"), ("RTO", "RTO")]

    balance = models.FloatField(default=0.0)
    previous_balance = models.FloatField(default=0.0)
    agency_banking_balance = models.FloatField(default=0.0)
    wallet_type = models.CharField(max_length=255, choices=WALLET_SOURCE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RETAIL WALLET"
        verbose_name_plural = "RETAIL WALLETS"


class RetailWalletTransactions(models.Model):
    TRANSACTION_TYPES = [
        ("CREDIT", "CREDIT"),
        ("DEBIT", "DEBIT"),
    ]

    TRANSACTION_CATEGORIES = [
        ("PAYOUT", "PAYOUT"),
        ("COMMISSION", "COMMISSION"),
        ("GAME_PLAY", "GAME_PLAY"),
        ("RTO", "RTO"),
    ]
    amount = models.FloatField(default=0)
    balance_before = models.FloatField(default=0)
    balance_after = models.FloatField(default=0)
    transaction_type = models.CharField(max_length=255, choices=TRANSACTION_TYPES)
    transaction_category = models.CharField(max_length=255, choices=TRANSACTION_CATEGORIES)
    transaction_ref = models.CharField(max_length=300, blank=True, null=True, unique=True)
    wallet_value = models.FloatField(default=0)
    rto_value = models.FloatField(default=0)
    rtp_value = models.FloatField(default=0)
    agency_banking_rtp_wallet_balance = models.FloatField(default=0)
    agency_banking_rto_wallet_balance = models.FloatField(default=0)
    agency_banking_commission_wallet_balance = models.FloatField(default=0)
    agency_banking_funding_wallet_balance = models.FloatField(default=0)
    date_won = models.DateTimeField(null=True, blank=True)
    game_type = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RETAIL WALLET TRANSACTION"
        verbose_name_plural = "RETAIL WALLET TRANSACTIONS"

    def __str__(self):
        return f"{self.transaction_type} - {self.transaction_category} - {self.amount}"

    @classmethod
    def create_debit_record_for_payout(cls, amount, transaction_ref, date_won=None):

        from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry

        agency_bankingwallet_value = 0

        try:
            vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_RTP_WALLET")
            if isinstance(vfd_enquiries, dict):
                agency_bankingwallet_value = vfd_enquiries.get("available_balance", 0)
        except Exception:
            pass

        try:
            with transaction.atomic():
                retail_wallet, _ = RetailWallet.objects.get_or_create(wallet_type="PAYOUT")

                previous_balance = retail_wallet.balance
                balance_after = previous_balance - amount

                retail_wallet.balance = balance_after
                retail_wallet.previous_balance = balance_after
                retail_wallet.agency_banking_balance = agency_bankingwallet_value
                retail_wallet.save()

                cls.objects.create(
                    amount=amount,
                    balance_before=previous_balance,
                    balance_after=balance_after,
                    transaction_type="DEBIT",
                    transaction_category="PAYOUT",
                    agency_banking_rtp_wallet_balance=agency_bankingwallet_value,
                    transaction_ref=transaction_ref,
                    date_won=date_won,
                )
        except Exception as e:
            pass

    @classmethod
    def create_credit_record_for_payout(cls, amount, transaction_ref):

        from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry

        agency_bankingwallet_value = 0

        try:
            vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_RTP_WALLET")
            if isinstance(vfd_enquiries, dict):
                agency_bankingwallet_value = vfd_enquiries.get("available_balance", 0)
        except Exception:
            pass

        try:
            with transaction.atomic():
                retail_wallet, _ = RetailWallet.objects.get_or_create(wallet_type="PAYOUT")

                previous_balance = retail_wallet.balance
                balance_after = previous_balance + amount

                retail_wallet.balance = balance_after
                retail_wallet.previous_balance = balance_after
                retail_wallet.agency_banking_balance = agency_bankingwallet_value
                retail_wallet.save()

                cls.objects.create(
                    amount=amount,
                    balance_before=previous_balance,
                    balance_after=balance_after,
                    transaction_type="CREDIT",
                    transaction_category="PAYOUT",
                    agency_banking_rtp_wallet_balance=agency_bankingwallet_value,
                    transaction_ref=transaction_ref,
                )
        except Exception as e:
            pass

    @classmethod
    def create_debit_record_for_commission(cls, amount, transaction_ref):

        from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry

        agency_bankingwallet_value = 0

        try:
            vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_COMMISSION_WALLET")
            if isinstance(vfd_enquiries, dict):
                agency_bankingwallet_value = vfd_enquiries.get("available_balance", 0)
        except Exception:
            pass

        try:
            with transaction.atomic():
                retail_wallet, _ = RetailWallet.objects.get_or_create(wallet_type="COMMISSION")
                previous_balance = retail_wallet.balance
                balance_after = previous_balance - amount

                retail_wallet.balance = balance_after
                retail_wallet.previous_balance = balance_after
                retail_wallet.agency_banking_balance = agency_bankingwallet_value
                retail_wallet.save()

                cls.objects.create(
                    amount=amount,
                    balance_before=previous_balance,
                    balance_after=balance_after,
                    transaction_type="DEBIT",
                    transaction_category="COMMISSION",
                    agency_banking_commission_wallet_balance=agency_bankingwallet_value,
                    transaction_ref=transaction_ref,
                )
        except:
            pass

    @classmethod
    def create_credit_record_for_commission(cls, amount, transaction_ref):

        from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry

        agency_bankingwallet_value = 0

        try:
            vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_COMMISSION_WALLET")
            if isinstance(vfd_enquiries, dict):
                agency_bankingwallet_value = vfd_enquiries.get("available_balance", 0)
        except Exception:
            pass

        try:
            with transaction.atomic():
                retail_wallet, _ = RetailWallet.objects.get_or_create(wallet_type="COMMISSION")
                previous_balance = retail_wallet.balance
                balance_after = previous_balance + amount

                retail_wallet.balance = balance_after
                retail_wallet.previous_balance = balance_after
                retail_wallet.agency_banking_balance = agency_bankingwallet_value
                retail_wallet.save()

                cls.objects.create(
                    amount=amount,
                    balance_before=previous_balance,
                    balance_after=balance_after,
                    transaction_type="CREDIT",
                    transaction_category="COMMISSION",
                    agency_banking_commission_wallet_balance=agency_bankingwallet_value,
                    transaction_ref=transaction_ref,
                )
        except:
            pass

    @classmethod
    def create_debit_record_for_game_play(
        cls, amount, wallet_value, rto_value, rtp_value, game_type=None, transaction_ref=None
    ):

        agency_bankingwallet_value = 0

        try:
            with transaction.atomic():

                retail_wallet, _ = RetailWallet.objects.get_or_create(wallet_type="GAME_PLAY")
                previous_balance = retail_wallet.balance
                balance_after = previous_balance - amount

                retail_wallet.balance = balance_after
                retail_wallet.previous_balance = balance_after
                retail_wallet.save()

                cls.objects.create(
                    amount=amount,
                    balance_before=previous_balance,
                    balance_after=balance_after,
                    transaction_type="DEBIT",
                    transaction_category="GAME_PLAY",
                    wallet_value=wallet_value,
                    rto_value=rto_value,
                    rtp_value=rtp_value,
                    game_type=game_type,
                    transaction_ref=transaction_ref,
                )
        except:
            pass

    @classmethod
    def create_credit_record_for_game_play(cls, amount, wallet_value, transaction_ref):

        from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry

        agency_bankingwallet_value = 0

        try:
            vfd_enquiries = vfd_enquiries = liberty_pay_vfd_account_enquiry()
            if isinstance(vfd_enquiries, dict):
                agency_bankingwallet_value = vfd_enquiries.get("available_balance", 0)
        except Exception:
            pass

        try:
            with transaction.atomic():

                retail_wallet, _ = RetailWallet.objects.get_or_create(wallet_type="GAME_PLAY")
                previous_balance = retail_wallet.balance
                balance_after = previous_balance + amount

                retail_wallet.balance = balance_after
                retail_wallet.previous_balance = balance_after
                retail_wallet.save()

                cls.objects.create(
                    amount=amount,
                    balance_before=previous_balance,
                    balance_after=balance_after,
                    transaction_type="CREDIT",
                    transaction_category="GAME_PLAY",
                    wallet_value=wallet_value,
                    agency_banking_funding_wallet_balance=agency_bankingwallet_value,
                    transaction_ref=transaction_ref,
                )

        except:
            pass

    @classmethod
    def create_debit_record_for_rto_wallet(cls, amount, transaction_ref):

        from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry

        agency_bankingwallet_value = 0

        try:
            vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RTO_WALLET")
            if isinstance(vfd_enquiries, dict):
                agency_bankingwallet_value = vfd_enquiries.get("available_balance", 0)
        except Exception:
            pass

        try:
            with transaction.atomic():
                retail_wallet, _ = RetailWallet.objects.get_or_create(wallet_type="RTO")
                previous_balance = retail_wallet.balance
                balance_after = previous_balance - amount

                retail_wallet.balance = balance_after
                retail_wallet.previous_balance = balance_after
                retail_wallet.agency_banking_balance = agency_bankingwallet_value
                retail_wallet.save()

                cls.objects.create(
                    amount=amount,
                    balance_before=previous_balance,
                    balance_after=balance_after,
                    transaction_type="DEBIT",
                    transaction_category="RTO",
                    agency_banking_commission_wallet_balance=agency_bankingwallet_value,
                    transaction_ref=transaction_ref,
                )
        except:
            pass

    @classmethod
    def create_credit_record_for_rto_wallet(cls, amount, transaction_ref):

        from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry

        agency_bankingwallet_value = 0

        try:
            vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RTO_WALLET")
            if isinstance(vfd_enquiries, dict):
                agency_bankingwallet_value = vfd_enquiries.get("available_balance", 0)
        except Exception:
            pass

        try:
            with transaction.atomic():
                retail_wallet, _ = RetailWallet.objects.get_or_create(wallet_type="RTO")
                previous_balance = retail_wallet.balance
                balance_after = previous_balance + amount

                retail_wallet.balance = balance_after
                retail_wallet.previous_balance = balance_after
                retail_wallet.agency_banking_balance = agency_bankingwallet_value
                retail_wallet.save()

                cls.objects.create(
                    amount=amount,
                    balance_before=previous_balance,
                    balance_after=balance_after,
                    transaction_type="CREDIT",
                    transaction_category="RTO",
                    agency_banking_commission_wallet_balance=agency_bankingwallet_value,
                    transaction_ref=transaction_ref,
                )
        except:
            pass


class LottoVerticalLeadDailyAnalytic(models.Model):
    first_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=300, blank=True, null=True)
    number_of_supervisors = models.IntegerField(default=0)
    number_of_active_supervisors = models.IntegerField(default=0)
    number_of_inactive_supervisors = models.IntegerField(default=0)
    number_of_agents = models.IntegerField(default=0)
    number_of_active_agents = models.IntegerField(default=0)
    number_of_inactive_agents = models.IntegerField(default=0)
    number_of_absent_agents = models.IntegerField(default=0)
    number_terminal_under_recovery = models.IntegerField(default=0)
    percentage_of_target_achieved = models.IntegerField(default=0)
    total_sales = models.IntegerField(default=0)
    commission_earned = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO VERTICAL LEAD DAILY ANALYTIC"
        verbose_name_plural = "LOTTO VERTICAL LEAD DAILY ANALYTICS"

    @classmethod
    def create_or_update_record(
        cls,
        phone,
        first_name,
        last_name,
        full_name,
        number_of_agents,
        number_of_active_agents,
        number_of_inactive_agents,
        number_of_absent_agents,
        number_of_terminal_under_recovery,
        percentage_of_target_achieved,
        total_sales,
        commission_earned,
    ):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        try:
            instance = cls.objects.get(phone=phone, created_at__date=current_datetime.date())
        except cls.DoesNotExist:
            instance = cls.objects.create(phone=phone, first_name=first_name, last_name=last_name, full_name=full_name)

        instance.total_sales = total_sales
        instance.commission_earned = commission_earned
        instance.percentage_of_target_achieved = percentage_of_target_achieved
        instance.number_of_agents = number_of_agents
        instance.number_of_active_agents = number_of_active_agents
        instance.number_of_inactive_agents = number_of_inactive_agents
        instance.number_of_absent_agents = number_of_absent_agents
        instance.save()

        return instance


class LottoVerticalLeadMonthlyAnalytic(models.Model):
    first_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=300, blank=True, null=True)
    number_of_supervisors = models.IntegerField(default=0)
    number_of_active_supervisors = models.IntegerField(default=0)
    number_of_inactive_supervisors = models.IntegerField(default=0)
    number_of_agents = models.IntegerField(default=0)
    number_of_active_agents = models.IntegerField(default=0)
    number_of_inactive_agents = models.IntegerField(default=0)
    number_of_absent_agents = models.IntegerField(default=0)
    number_of_terminal_under_recovery = models.IntegerField(default=0)
    percentage_of_target_achieved = models.IntegerField(default=0)
    total_sales = models.IntegerField(default=0)
    commission_earned = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO VERTICAL LEAD MONTHLY ANALYTIC"
        verbose_name_plural = "LOTTO VERTICAL LEAD MONTHLY ANALYTICS"


class LottoVerticalLeadAnalytic(models.Model):
    first_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=300, blank=True, null=True)
    number_of_supervisors = models.IntegerField(default=0)
    number_of_active_supervisors = models.IntegerField(default=0)
    number_of_inactive_supervisors = models.IntegerField(default=0)
    number_of_agents = models.IntegerField(default=0)
    number_of_active_agents = models.IntegerField(default=0)
    number_of_inactive_agents = models.IntegerField(default=0)
    number_of_absent_agents = models.IntegerField(default=0)
    number_of_terminal_under_recovery = models.IntegerField(default=0)
    percentage_of_target_achieved = models.IntegerField(default=0)
    total_sales = models.IntegerField(default=0)
    commission_earned = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO VERTICAL LEAD ANALYTIC"
        verbose_name_plural = "LOTTO VERTICAL LEAD ANALYTICS"


class LottoSupervisorDailyAnalytic(models.Model):
    first_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=300, blank=True, null=True)
    number_of_agents = models.IntegerField(default=0)
    number_of_active_agents = models.IntegerField(default=0)
    number_of_inactive_agents = models.IntegerField(default=0)
    number_of_absent_agents = models.IntegerField(default=0)
    number_of_terminal_under_recovery = models.IntegerField(default=0)
    percentage_of_target_achieved = models.IntegerField(default=0)
    total_sales = models.IntegerField(default=0)
    commission_earned = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO SUPERVISOR DAILY ANALYTIC"
        verbose_name_plural = "LOTTO SUPERVISOR DAILY ANALYTICS"

    @classmethod
    def create_or_update_record(
        cls,
        phone,
        first_name,
        last_name,
        full_name,
        number_of_agents,
        number_of_active_agents,
        number_of_inactive_agents,
        number_of_absent_agents,
        number_of_terminal_under_recovery,
        percentage_of_target_achieved,
        total_sales,
        commission_earned,
    ):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        try:
            instance = cls.objects.get(phone=phone, created_at__date=current_datetime.date())
        except cls.DoesNotExist:
            instance = cls.objects.create(phone=phone, first_name=first_name, last_name=last_name, full_name=full_name)

        instance.total_sales = total_sales
        instance.commission_earned = commission_earned
        instance.percentage_of_target_achieved = percentage_of_target_achieved
        instance.number_of_agents = number_of_agents
        instance.number_of_active_agents = number_of_active_agents
        instance.number_of_inactive_agents = number_of_inactive_agents
        instance.number_of_absent_agents = number_of_absent_agents
        instance.number_of_terminal_under_recovery = number_of_terminal_under_recovery
        instance.save()

        return instance


class LottoSupervisorMonthlyAnalytic(models.Model):
    first_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=300, blank=True, null=True)
    number_of_agents = models.IntegerField(default=0)
    number_of_active_agents = models.IntegerField(default=0)
    number_of_inactive_agents = models.IntegerField(default=0)
    number_of_absent_agents = models.IntegerField(default=0)
    number_of_terminal_under_recovery = models.IntegerField(default=0)
    percentage_of_target_achieved = models.IntegerField(default=0)
    total_sales = models.IntegerField(default=0)
    commission_earned = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO SUPERVISOR MONTHLY ANALYTIC"
        verbose_name_plural = "LOTTO SUPERVISOR MONTHLY ANALYTICS"

    @classmethod
    def create_or_update_record(
        cls,
        phone,
        first_name,
        last_name,
        full_name,
        number_of_agents,
        number_of_active_agents,
        number_of_inactive_agents,
        number_of_absent_agents,
        number_of_terminal_under_recovery,
        percentage_of_target_achieved,
        total_sales,
        commission_earned,
    ):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        try:
            instance = cls.objects.get(
                phone=phone, created_at__year=current_datetime.year, created_at__month=current_datetime.month
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(phone=phone, first_name=first_name, last_name=last_name, full_name=full_name)

        instance.total_sales = total_sales
        instance.commission_earned = commission_earned
        instance.percentage_of_target_achieved = percentage_of_target_achieved
        instance.number_of_agents = number_of_agents
        instance.number_of_active_agents = number_of_active_agents
        instance.number_of_inactive_agents = number_of_inactive_agents
        instance.number_of_absent_agents = number_of_absent_agents
        instance.number_of_terminal_under_recovery = number_of_terminal_under_recovery
        instance.save()

        return instance


class LottoSupervisorAnalytic(models.Model):
    first_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=300, blank=True, null=True)
    number_of_agents = models.IntegerField(default=0)
    number_of_active_agents = models.IntegerField(default=0)
    number_of_inactive_agents = models.IntegerField(default=0)
    number_of_absent_agents = models.IntegerField(default=0)
    number_of_terminal_under_recovery = models.IntegerField(default=0)
    percentage_of_target_achieved = models.IntegerField(default=0)
    total_sales = models.IntegerField(default=0)
    commission_earned = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO SUPERVISOR ANALYTIC"
        verbose_name_plural = "LOTTO SUPERVISOR ANALYTICS"

    @classmethod
    def create_or_update_record(
        cls,
        phone,
        first_name,
        last_name,
        full_name,
        number_of_agents,
        number_of_active_agents,
        number_of_inactive_agents,
        number_of_absent_agents,
        number_of_terminal_under_recovery,
        percentage_of_target_achieved,
        total_sales,
        commission_earned,
    ):

        try:
            instance = cls.objects.get(phone=phone)
        except cls.DoesNotExist:
            instance = cls.objects.create(phone=phone, first_name=first_name, last_name=last_name, full_name=full_name)

        instance.total_sales = total_sales
        instance.commission_earned = commission_earned
        instance.percentage_of_target_achieved = percentage_of_target_achieved
        instance.number_of_agents = number_of_agents
        instance.number_of_active_agents = number_of_active_agents
        instance.number_of_inactive_agents = number_of_inactive_agents
        instance.number_of_absent_agents = number_of_absent_agents
        instance.number_of_terminal_under_recovery = number_of_terminal_under_recovery
        instance.save()

        return instance


class LottoAgentDailyAnalytic(models.Model):

    STATUS = [
        ("ACTIVE", "ACTIVE"),
        ("DECLINING_AGENTS", "DECLINING_AGENTS"),
        ("AT_RISK", "AT_RISK"),
        ("INACTIVE", "INACTIVE"),
        ("ABSENT", "ABSENT"),
    ]

    first_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=300, blank=True, null=True)
    supervisor_first_name = models.CharField(max_length=255, blank=True, null=True)
    supervisor_last_name = models.CharField(max_length=255, blank=True, null=True)
    supervisor_full_name = models.CharField(max_length=255, blank=True, null=True)
    supervisor_phone = models.CharField(max_length=300, blank=True, null=True)
    vertical_lead_first_name = models.CharField(max_length=255, blank=True, null=True)
    vertical_lead_last_name = models.CharField(max_length=255, blank=True, null=True)
    vertical_lead_full_name = models.CharField(max_length=255, blank=True, null=True)
    vertical_lead_phone = models.CharField(max_length=300, blank=True, null=True)
    total_sales = models.IntegerField(default=0)
    commission_earned = models.IntegerField(default=0)
    percentage_of_target_achieved = models.IntegerField(default=0)
    status = models.CharField(max_length=300, choices=STATUS, default="INACTIVE")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO AGENT DAILY ANALYTIC"
        verbose_name_plural = "LOTTO AGENT DAILY ANALYTICS"

    @classmethod
    def create_or_update_record(
        cls,
        phone,
        first_name,
        last_name,
        full_name,
        supervisor_first_name,
        supervisor_last_name,
        supervisor_full_name,
        supervisor_phone,
        status,
        vertical_lead_first_name=None,
        vertical_lead_last_name=None,
        vertical_lead_full_name=None,
        vertical_lead_phone=None,
        total_sales=0,
        commission_earned=0,
        percentage_of_target_achieved=0,
    ):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        try:
            instance = cls.objects.get(phone=phone, created_at__date=current_datetime.date())
        except cls.DoesNotExist:
            instance = cls.objects.create(
                phone=phone,
                first_name=first_name,
                last_name=last_name,
                full_name=full_name,
                supervisor_first_name=supervisor_first_name,
                supervisor_last_name=supervisor_last_name,
                supervisor_full_name=supervisor_full_name,
                supervisor_phone=supervisor_phone,
                vertical_lead_first_name=vertical_lead_first_name,
                vertical_lead_last_name=vertical_lead_last_name,
                vertical_lead_full_name=vertical_lead_full_name,
                vertical_lead_phone=vertical_lead_phone,
            )

        instance.total_sales = total_sales
        instance.commission_earned = commission_earned
        instance.percentage_of_target_achieved = percentage_of_target_achieved
        instance.status = status
        instance.save()

        return instance


class LottoAgentMonthlyAnalytic(models.Model):

    STATUS = [
        ("ACTIVE", "ACTIVE"),
        ("DECLINING_AGENTS", "DECLINING_AGENTS"),
        ("AT_RISK", "AT_RISK"),
        ("INACTIVE", "INACTIVE"),
        ("ABSENT", "ABSENT"),
    ]

    first_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=300, blank=True, null=True)
    supervisor_first_name = models.CharField(max_length=255, blank=True, null=True)
    supervisor_last_name = models.CharField(max_length=255, blank=True, null=True)
    supervisor_full_name = models.CharField(max_length=255, blank=True, null=True)
    supervisor_phone = models.CharField(max_length=300, blank=True, null=True)
    vertical_lead_first_name = models.CharField(max_length=255, blank=True, null=True)
    vertical_lead_last_name = models.CharField(max_length=255, blank=True, null=True)
    vertical_lead_full_name = models.CharField(max_length=255, blank=True, null=True)
    vertical_lead_phone = models.CharField(max_length=300, blank=True, null=True)
    total_sales = models.IntegerField(default=0)
    commission_earned = models.IntegerField(default=0)
    percentage_of_target_achieved = models.IntegerField(default=0)
    status = models.CharField(max_length=300, choices=STATUS, default="INACTIVE")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO AGENT MONTHLY ANALYTIC"
        verbose_name_plural = "LOTTO AGENT MONTHLY ANALYTICS"

    @classmethod
    def create_or_update_record(
        cls,
        phone,
        first_name,
        last_name,
        full_name,
        supervisor_first_name,
        supervisor_last_name,
        supervisor_full_name,
        status,
        supervisor_phone,
        vertical_lead_first_name=None,
        vertical_lead_last_name=None,
        vertical_lead_full_name=None,
        vertical_lead_phone=None,
        total_sales=0,
        commission_earned=0,
        percentage_of_target_achieved=0,
    ):

        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        try:
            instance = cls.objects.get(
                phone=phone, created_at__year=current_datetime.year, created_at__month=current_datetime.month
            )
        except cls.DoesNotExist:
            instance = cls.objects.create(
                phone=phone,
                first_name=first_name,
                last_name=last_name,
                full_name=full_name,
                supervisor_first_name=supervisor_first_name,
                supervisor_phone=supervisor_phone,
                supervisor_last_name=supervisor_last_name,
                supervisor_full_name=supervisor_full_name,
                vertical_lead_first_name=vertical_lead_first_name,
                vertical_lead_last_name=vertical_lead_last_name,
                vertical_lead_full_name=vertical_lead_full_name,
                vertical_lead_phone=vertical_lead_phone,
            )

        instance.total_sales = total_sales
        instance.commission_earned = commission_earned
        instance.percentage_of_target_achieved = percentage_of_target_achieved
        instance.status = status
        instance.save()

        return instance


class LottoAgentAnalytic(models.Model):

    STATUS = [
        ("ACTIVE", "ACTIVE"),
        ("DECLINING_AGENTS", "DECLINING_AGENTS"),
        ("AT_RISK", "AT_RISK"),
        ("INACTIVE", "INACTIVE"),
        ("ABSENT", "ABSENT"),
    ]

    first_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255, blank=True, null=True)
    full_name = models.CharField(max_length=255, blank=True, null=True)
    phone = models.CharField(max_length=300, blank=True, null=True)
    supervisor_first_name = models.CharField(max_length=255, blank=True, null=True)
    supervisor_last_name = models.CharField(max_length=255, blank=True, null=True)
    supervisor_full_name = models.CharField(max_length=255, blank=True, null=True)
    supervisor_phone = models.CharField(max_length=300, blank=True, null=True)
    vertical_lead_first_name = models.CharField(max_length=255, blank=True, null=True)
    vertical_lead_last_name = models.CharField(max_length=255, blank=True, null=True)
    vertical_lead_full_name = models.CharField(max_length=255, blank=True, null=True)
    vertical_lead_phone = models.CharField(max_length=300, blank=True, null=True)
    total_sales = models.IntegerField(default=0)
    commission_earned = models.IntegerField(default=0)
    percentage_of_target_achieved = models.IntegerField(default=0)
    status = models.CharField(max_length=300, choices=STATUS, default="INACTIVE")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO AGENT ANALYTIC"
        verbose_name_plural = "LOTTO AGENT ANALYTICS"

    @classmethod
    def create_or_update_record(
        cls,
        phone,
        first_name,
        last_name,
        full_name,
        supervisor_first_name,
        supervisor_last_name,
        supervisor_full_name,
        status,
        supervisor_phone,
        vertical_lead_first_name=None,
        vertical_lead_last_name=None,
        vertical_lead_full_name=None,
        vertical_lead_phone=None,
        total_sales=0,
        commission_earned=0,
        percentage_of_target_achieved=0,
    ):

        try:
            instance = cls.objects.get(phone=phone)
        except cls.DoesNotExist:
            instance = cls.objects.create(
                phone=phone,
                first_name=first_name,
                last_name=last_name,
                full_name=full_name,
                supervisor_first_name=supervisor_first_name,
                supervisor_phone=supervisor_phone,
                supervisor_last_name=supervisor_last_name,
                supervisor_full_name=supervisor_full_name,
                vertical_lead_first_name=vertical_lead_first_name,
                vertical_lead_last_name=vertical_lead_last_name,
                vertical_lead_full_name=vertical_lead_full_name,
                vertical_lead_phone=vertical_lead_phone,
            )

        instance.total_sales = total_sales
        instance.commission_earned = commission_earned
        instance.percentage_of_target_achieved = percentage_of_target_achieved
        instance.status = status
        instance.save()

        return instance


class DailyFundingWalletActivity(models.Model):
    total_funding = models.FloatField(default=0.0)
    total_due_remittance = models.FloatField(default=0.0)
    total_pending_remittance = models.FloatField(default=0.0)
    total_rtp_deduction = models.FloatField(default=0.0)
    total_rto_deduction = models.FloatField(default=0.0)
    total_commission_deduction = models.FloatField(default=0.0)
    uncliamed_winning_count = models.IntegerField(default=0)
    uncliamed_winning_value = models.FloatField(default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "DAILY FUNDING WALLET ACTIVITY"
        verbose_name_plural = "DAILY FUNDING WALLET ACTIVITIES"

    @classmethod
    def create_or_update(
        cls,
        total_funding,
        total_due_remittance,
        total_pending_remittance,
        total_rtp_deduction,
        total_rto_deduction,
        total_commission_deduction,
        uncliamed_winning_count,
        uncliamed_winning_value,
    ):

        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        try:
            instance = cls.objects.get(created_at__date=current_datetime.date())
        except cls.DoesNotExist:
            instance = cls.objects.create(total_funding=0)

        instance.total_funding = total_funding
        instance.total_due_remittance = total_due_remittance
        instance.total_pending_remittance = total_pending_remittance
        instance.total_rtp_deduction = total_rtp_deduction
        instance.total_rto_deduction = total_rto_deduction
        instance.total_commission_deduction = total_commission_deduction
        instance.uncliamed_winning_count = uncliamed_winning_count
        instance.uncliamed_winning_value = uncliamed_winning_value
        instance.save()

    @classmethod
    def get_instance(cls):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        try:
            instance = cls.objects.get(created_at__date=current_datetime.date())
        except cls.DoesNotExist:
            instance = cls.objects.create(total_funding=0)

        return instance


class AgentMetricSummary(models.Model):

    metric_name = models.CharField(max_length=100, unique=True)
    all_time = models.CharField(max_length=200, null=True, blank=True)
    today = models.CharField(max_length=200, null=True, blank=True)
    yesterday = models.CharField(max_length=200, null=True, blank=True)
    this_week = models.CharField(max_length=200, null=True, blank=True)
    last_week = models.CharField(max_length=200, null=True, blank=True)
    this_month = models.CharField(max_length=200, null=True, blank=True)
    last_month = models.CharField(max_length=200, null=True, blank=True)
    this_year = models.CharField(max_length=200, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AGENT METRIC SUMMARY"
        verbose_name_plural = "AGENT METRIC SUMMARYS"
        
        
    @classmethod
    def get_metric_value(cls, metric_name, time_period):
        """Helper method to get a specific metric value"""
        try:
            metric = cls.objects.get(metric_name=metric_name)
            return getattr(metric, time_period, None)
        except cls.DoesNotExist:
            return None
    
    @classmethod
    def set_metric_value(cls, metric_name, time_period, value):
        """Helper method to set a specific metric value"""
        metric, created = cls.objects.get_or_create(metric_name=metric_name)
        setattr(metric, time_period, str(value) if value is not None else None)
        metric.save()
        return metric
    
    @classmethod
    def get_all_metrics_for_period(cls, time_period):
        """Get all metrics for a specific time period"""
        metrics = {}
        for metric in cls.objects.all():
            value = getattr(metric, time_period, None)
            if value:
                metrics[metric.metric_name] = value
        return metrics
    
    @classmethod
    def initialize_default_metrics(cls):
        """Initialize the table with default metric rows"""
        default_metrics = [
            'Total Agents',
            'Active Agents', 
            'Inactive Agents',
            'Absent Agent',
            'Percentage Active',
            'Agents Under Recovery',
            'Agents Achieving >75% Target',
            'Total Sales (Month)',
            'Total Commissions',
            'Total Winnings',
            'Average Sales per Agent',
            'Top Agent (Month)',
            'Bottom Agent (Month)'
        ]
        
        for metric in default_metrics:
            cls.objects.get_or_create(metric_name=metric)
