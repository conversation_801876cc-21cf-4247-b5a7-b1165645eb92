from django.core.management.base import BaseCommand
from django.db.models import Sum
from pos_app.models import AgentWalletTransaction
from retail_metrics.models import GameAnalytics


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        years = [2023, 2024, 2025]

        for exsiting_year in years:
            for i in range(1, 13):
                LOTTERY_TYPE_CHOICES = ["SALARY_FOR_LIFE",
                    "INSTANT_CASHOUT", "WYSE_CASH", "SOCCER_CASH", "VIRTUAL_SOCCER",
                    "QUIKA", "GHANA_LOTTO", "KENYA_LOTTO", "BANKER", "KENYA_30_LOTTO",
                    "K_NOW"]
                
                for game_type in LOTTERY_TYPE_CHOICES:
                    queryset = AgentWalletTransaction.objects.filter(
                        game_type = game_type,
                        date_created__year = exsiting_year,
                    ).filter(date_created__month = i)

                    winnings = queryset.filter(transaction_from = "WINNINGS").aggregate(Sum("amount")).get("amount__sum")
                    if winnings is None:
                        winnings = 0

                    sales = queryset.filter(transaction_from = "GAME_PLAY").aggregate(Sum("amount")).get("amount__sum")
                    if sales is None:
                        sales = 0

                    
                    GameAnalytics.update_record(
                        game_type = game_type,
                        sales_amount = sales,
                        winning_amount = winnings,
                        year = exsiting_year,
                        month = i
                    )

                    
