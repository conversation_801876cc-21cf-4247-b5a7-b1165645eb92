from django.core.management.base import BaseCommand
from django.db.models import Sum

from africa_lotto.models import AfricaLotto
from main.models import LotteryModel, LottoTicket
from retail_metrics.models import ReturnToPlayerAndReturnToOwnerAnalytics




class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        years = [2023, 2024, 2025]
        for exsiting_year in years:
            for i in range(1, 13):
                LOTTERY_TYPE_CHOICES = ["SALARY_FOR_LIFE",
                    "INSTANT_CASHOUT", "WYSE_CASH", "SOCCER_CASH", "VIRTUAL_SOCCER",
                    "QUIKA", "GHANA_LOTTO", "KENYA_LOTTO", "BANKER", "KENYA_30_LOTTO",
                    "K_NOW"]
                
                for game_type in LOTTERY_TYPE_CHOICES:
                    if game_type in ["K_NOW", "KENYA_LOTTO", "KENYA_30_LOTTO"]:
                        queryset = AfricaLotto.objects.filter(game_type = game_type, created_at__year = exsiting_year).filter(created_at__month = i)
                        rtp = queryset.aggregate(Sum("rtp")).get("rtp__sum")
                        if rtp is None:
                            rtp = 0

                        rto = queryset.aggregate(Sum("rto")).get("rto__sum")
                        if rto is None:
                            rto = 0

                        ReturnToPlayerAndReturnToOwnerAnalytics.update_record(
                            game_type = game_type,
                            rtp = rtp,
                            rto = rto,
                            year = exsiting_year,
                            month = i
                        )

                        continue

                    
                    if game_type == "WYSE_CASH":
                        queryset = LotteryModel.objects.filter(lottery_type = game_type, date__year = exsiting_year).filter(date__month = i)
                        rtp = queryset.aggregate(Sum("rtp")).get("rtp__sum")
                        if rtp is None:
                            rtp = 0

                        rto = queryset.aggregate(Sum("rto")).get("rto__sum")
                        if rto is None:
                            rto = 0

                        ReturnToPlayerAndReturnToOwnerAnalytics.update_record(
                            game_type = game_type,
                            rtp = rtp,
                            rto = rto,
                            year = exsiting_year,
                            month = i
                        )

                        continue

                    else:
                        queryset = LottoTicket.objects.filter(lottery_type = game_type, date__year = exsiting_year).filter(date__month = i)
                        rtp = queryset.aggregate(Sum("rtp")).get("rtp__sum")
                        if rtp is None:
                            rtp = 0

                        rto = queryset.aggregate(Sum("rto")).get("rto__sum")
                        if rto is None:
                            rto = 0

                        ReturnToPlayerAndReturnToOwnerAnalytics.update_record(
                            game_type = game_type,
                            rtp = rtp,
                            rto = rto,
                            year = exsiting_year,
                            month = i
                        )

                        continue


                        