from datetime import datetime, timedelta

import pytz
from django.conf import settings
from django.core.management.base import BaseCommand
from django.db.models import Sum

from main.models import PayoutTransactionTable
from pos_app.models import (
    AgentFundingTable,
    LottoAgentRemittanceTable,
    LottoAgentSalesActivity,
)
from retail_metrics.models import AgentMetricSummary, DailyFundingWalletActivity


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # timezone = pytz.timezone(settings.TIME_ZONE)
        # _current_datetime = datetime.now(tz=timezone)
        # current_datetime = _current_datetime - timedelta(days =1)
        # yesterday = current_datetime - timedelta(days =1)

        # print("CURRENT DATE", current_datetime.date())
        # print("YESTERDAY DATA", yesterday.date())

        # remittance_queryset = LottoAgentRemittanceTable.objects.filter(created_at__date__range = [yesterday.date(), current_datetime.date()], remitted = False)
        # due_remittance = remittance_queryset.filter(due = True).aggregate(Sum("amount")).get("amount__sum") or 0
        # pending_remittance = remittance_queryset.filter(due = False).aggregate(Sum("amount")).get("amount__sum") or 0

        # payout_transaction_queryset = PayoutTransactionTable.objects.filter(date_added__date = current_datetime.date(), source_response_payload__icontains = "completed successfully")

        # total_rtp_deduction = payout_transaction_queryset.filter(source_wallet = "AGENT_FUNDING_WALLET", recipient_wallet__in = ["RETAIL_RTP_WALLET", "GHANA_RTP_WALLET"]).aggregate(Sum("amount")).get("amount__sum") or 0
        # total_rto_deduction = payout_transaction_queryset.filter(source_wallet = "AGENT_FUNDING_WALLET", recipient_wallet = "RTO_WALLET").aggregate(Sum("amount")).get("amount__sum") or 0
        # total_commission_deduction = payout_transaction_queryset.filter(source_wallet = "AGENT_FUNDING_WALLET", recipient_wallet = "RETAIL_COMMISSION_WALLET").aggregate(Sum("amount")).get("amount__sum") or 0

        # total_funding = AgentFundingTable.objects.filter(created_at__date = current_datetime.date()).aggregate(Sum("amount")).get("amount__sum") or 0

        # try:
        #     instance = DailyFundingWalletActivity.objects.get(created_at__date = current_datetime.date())
        # except DailyFundingWalletActivity.DoesNotExist:
        #     _instance = DailyFundingWalletActivity.objects.create(total_funding = 0)
        #     _instance.created_at = current_datetime
        #     _instance.save()
        # else:
        #     instance.total_funding = round(total_funding, 2)
        #     instance.total_due_remittance = round(due_remittance, 2)
        #     instance.total_pending_remittance = round(pending_remittance, 2)
        #     instance.total_rtp_deduction = round(total_rtp_deduction, 2)
        #     instance.total_rto_deduction = round(total_rto_deduction, 2)
        #     instance.total_commission_deduction = round(total_commission_deduction, 2)
        #     instance.save()

        LottoAgentSalesActivity.create_agent_sales_activity()
