from django.core.management.base import BaseCommand

from retail_metrics.models import AgentMetricSummary
from retail_metrics.retails_helper import SalesBasedMetricsCalculator


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):

        periods = ["all_time", "today", "yesterday", "this_week", "last_week", "this_month", "last_month", "this_year"]

        for period in periods:
            metrics = SalesBasedMetricsCalculator.calculate_agent_metrics_by_sales(wave="WAVE_TWO", period=period)

            # Update the metrics table
            for metric_name, value in metrics.items():
                AgentMetricSummary.set_metric_value(metric_name, period, value)
