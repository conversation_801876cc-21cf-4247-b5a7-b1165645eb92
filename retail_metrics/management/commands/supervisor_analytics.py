import calendar
from datetime import datetime, timedelta

import pytz
from django.conf import settings
from django.core.management.base import BaseCommand
from django.db.models import Sum

from africa_lotto.models import AfricaLottoConstants
from pos_app.models import (
    Agent,
    AgentConstantVariables,
    AgentWalletTransaction,
    LottoVerticalLead,
    Supervisor,
)
from retail_metrics.models import (
    LottoAgentAnalytic,
    LottoAgentDailyAnalytic,
    LottoAgentMonthlyAnalytic,
    LottoSupervisorAnalytic,
    LottoSupervisorDailyAnalytic,
    LottoSupervisorMonthlyAnalytic,
    LottoVerticalLeadAnalytic,
    LottoVerticalLeadDailyAnalytic,
    LottoVerticalLeadMonthlyAnalytic,
    RetailMetricsConstant,
)


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        supervisor_qs = Supervisor.objects.filter(is_active=True)

        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        # get all agents under this supervisor
        for supervisor_instance in supervisor_qs:
            agents = Agent.objects.filter(supervisor=supervisor_instance)
            if len(agents) < 1:
                continue

            for agent in agents:

                start_date = current_datetime - timedelta(
                    days=RetailMetricsConstant.get_agent_avg_game_sales_period_days()
                )

                start_of_month = current_datetime.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                last_day = calendar.monthrange(current_datetime.year, current_datetime.month)[1]
                end_of_month = current_datetime.replace(
                    day=last_day, hour=23, minute=59, second=59, microsecond=999999
                )

                transaction_history_all_time = AgentWalletTransaction.objects.filter(
                    agent_wallet__agent__supervisor=supervisor_instance
                )

                transaction_history_this_month = transaction_history_all_time.filter(
                    date_created__range=[start_of_month, end_of_month]
                )

                game_plays_this_month = transaction_history_this_month.filter(transaction_from="GAME_PLAY")

                aggregated_draw_games_sales_this_month = (
                    game_plays_this_month.filter(
                        game_type__in=[
                            "SALARY_FOR_LIFE",
                            "WYSE_CASH",
                            "GHANA_LOTTO",
                            "KENYA_LOTTO",
                            "BANKER",
                            "KENYA_30_LOTTO",
                        ]
                    )
                    .aggregate(Sum("amount"))
                    .get("amount__sum")
                    or 0
                )
                aggregated_instant_games_sales_this_month = (
                    game_plays_this_month.filter(game_type__in=["INSTANT_CASHOUT", "QUIKA"])
                    .aggregate(Sum("amount"))
                    .get("amount__sum")
                    or 0
                )
                aggregated_k_now_games_sales_this_month = (
                    game_plays_this_month.filter(game_type__in=["K_NOW"]).aggregate(Sum("amount")).get("amount__sum")
                    or 0
                )

                total_sales_for_this_month = (
                    aggregated_draw_games_sales_this_month
                    + aggregated_instant_games_sales_this_month
                    + aggregated_k_now_games_sales_this_month
                )

                commission_earned_for_draw_games_this_month = 0
                if aggregated_draw_games_sales_this_month > 1:
                    commission_earned_for_draw_games_this_month = (
                        aggregated_draw_games_sales_this_month
                        * AgentConstantVariables.get_draw_game_commission_percentage()
                    )

                commission_earned_for_k_now_games_this_month = 0
                if aggregated_k_now_games_sales_this_month > 1:
                    commission_earned_for_k_now_games_this_month = (
                        aggregated_k_now_games_sales_this_month
                        * AfricaLottoConstants.get_k_now_commission_percentage()
                    )

                commission_earned_for_instant_games_this_month = 0
                if aggregated_instant_games_sales_this_month > 1:
                    commission_earned_for_instant_games_this_month = (
                        aggregated_instant_games_sales_this_month
                        * AgentConstantVariables.get_instant_game_commission_percentage()
                    )

                total_sales_this_month = (
                    aggregated_draw_games_sales_this_month
                    + aggregated_instant_games_sales_this_month
                    + aggregated_k_now_games_sales_this_month
                )
                avg_sales_this_month = round(total_sales_this_month / 30, 2)

                transaction_history_for_past_days = transaction_history_this_month.filter(
                    agent_wallet__agent__supervisor=supervisor_instance,
                    date_created__range=[start_date, current_datetime],
                )

                transaction_history_today = transaction_history_this_month.filter(
                    agent_wallet__agent__supervisor=supervisor_instance, date_created__date=current_datetime.date()
                )

                ############### TODAY

                game_plays_today = transaction_history_today.filter(transaction_from="GAME_PLAY")

                aggregated_draw_games_sales_for_today = (
                    game_plays_today.filter(
                        game_type__in=[
                            "SALARY_FOR_LIFE",
                            "WYSE_CASH",
                            "GHANA_LOTTO",
                            "KENYA_LOTTO",
                            "BANKER",
                            "KENYA_30_LOTTO",
                        ]
                    )
                    .aggregate(Sum("amount"))
                    .get("amount__sum")
                    or 0
                )
                aggregated_instant_games_sales_for_today = (
                    game_plays_today.filter(game_type__in=["INSTANT_CASHOUT", "QUIKA"])
                    .aggregate(Sum("amount"))
                    .get("amount__sum")
                    or 0
                )
                aggregated_k_now_games_sales_for_today = (
                    game_plays_today.filter(game_type__in=["K_NOW"]).aggregate(Sum("amount")).get("amount__sum") or 0
                )

                total_sales_today = (
                    aggregated_draw_games_sales_for_today
                    + aggregated_instant_games_sales_for_today
                    + aggregated_k_now_games_sales_for_today
                )

                commission_earned_for_draw_games_today = 0
                if aggregated_draw_games_sales_for_today > 1:
                    commission_earned_for_draw_games_today = (
                        aggregated_draw_games_sales_for_today
                        * AgentConstantVariables.get_draw_game_commission_percentage()
                    )

                commission_earned_for_k_now_games_today = 0
                if aggregated_k_now_games_sales_for_today > 1:
                    commission_earned_for_k_now_games_today = (
                        aggregated_k_now_games_sales_for_today * AfricaLottoConstants.get_k_now_commission_percentage()
                    )

                commission_earned_for_instant_games_today = 0
                if aggregated_instant_games_sales_for_today > 1:
                    commission_earned_for_instant_games_today = (
                        aggregated_instant_games_sales_for_today
                        * AgentConstantVariables.get_instant_game_commission_percentage()
                    )

                ############### PAST DAYS
                game_plays_past_weeks = transaction_history_for_past_days.filter(transaction_from="GAME_PLAY")
                aggregated_draw_games_sales_for_past_days = (
                    game_plays_past_weeks.filter(
                        game_type__in=[
                            "SALARY_FOR_LIFE",
                            "WYSE_CASH",
                            "GHANA_LOTTO",
                            "KENYA_LOTTO",
                            "BANKER",
                            "KENYA_30_LOTTO",
                        ]
                    )
                    .aggregate(Sum("amount"))
                    .get("amount__sum")
                    or 0
                )
                aggregated_instant_games_sales_for_past_days = (
                    game_plays_past_weeks.filter(game_type__in=["INSTANT_CASHOUT", "QUIKA"])
                    .aggregate(Sum("amount"))
                    .get("amount__sum")
                    or 0
                )
                aggregated_k_now_games_sales_for_past_days = (
                    game_plays_past_weeks.filter(game_type__in=["K_NOW"]).aggregate(Sum("amount")).get("amount__sum")
                    or 0
                )

                commission_earned_for_draw_games_past_days = 0
                if aggregated_draw_games_sales_for_past_days > 1:
                    commission_earned_for_draw_games_past_days = (
                        aggregated_draw_games_sales_for_past_days
                        * AgentConstantVariables.get_draw_game_commission_percentage()
                    )

                commission_earned_for_k_now_games_past_days = 0
                if aggregated_k_now_games_sales_for_past_days > 1:
                    commission_earned_for_k_now_games_past_days = (
                        aggregated_k_now_games_sales_for_past_days
                        * AfricaLottoConstants.get_k_now_commission_percentage()
                    )

                commission_earned_for_instant_games_past_days = 0
                if aggregated_instant_games_sales_for_past_days > 1:
                    commission_earned_for_instant_games_past_days = (
                        aggregated_instant_games_sales_for_past_days
                        * AgentConstantVariables.get_instant_game_commission_percentage()
                    )

                total_sales_for_past_days = (
                    aggregated_draw_games_sales_for_past_days
                    + aggregated_instant_games_sales_for_past_days
                    + aggregated_k_now_games_sales_for_past_days
                )

                avg_sales_for_past_days = round(
                    total_sales_for_past_days / RetailMetricsConstant.get_agent_avg_game_sales_period_days(), 2
                )

                agent_activity_status = "INACTIVE"

                avg_sales_target = RetailMetricsConstant.get_agent_target_sales_on_avg()
                at_risk_threshold = RetailMetricsConstant.get_agent_sales_at_risk_threshold()

                if avg_sales_for_past_days > avg_sales_target:
                    agent_activity_status = "ACTIVE"
                elif (avg_sales_this_month > avg_sales_target) and (avg_sales_for_past_days < avg_sales_target):
                    agent_activity_status = "DECLINING_AGENTS"

                elif (avg_sales_for_past_days <= at_risk_threshold) and (avg_sales_for_past_days < avg_sales_target):
                    agent_activity_status = "AT_RISK"

                elif (avg_sales_for_past_days <= 1000) and (avg_sales_for_past_days < at_risk_threshold):
                    agent_activity_status = "INACTIVE"

                elif avg_sales_for_past_days < 1000:
                    agent_activity_status = "ABSENT"

                vertical_lead_first_name = None
                vertical_lead_last_name = None
                vertical_lead_full_name = None
                vertical_lead_phone = None

                if supervisor_instance.vertical_lead is not None:
                    vertical_lead_first_name = supervisor_instance.vertical_lead.first_name
                    vertical_lead_last_name = supervisor_instance.vertical_lead.last_name
                    vertical_lead_full_name = supervisor_instance.vertical_lead.full_name
                    vertical_lead_phone = supervisor_instance.vertical_lead.phone

                LottoAgentDailyAnalytic.create_or_update_record(
                    phone=agent.phone,
                    first_name=agent.first_name,
                    last_name=agent.last_name,
                    full_name=agent.full_name,
                    supervisor_first_name=agent.supervisor.first_name,
                    supervisor_last_name=agent.supervisor.last_name,
                    supervisor_full_name=agent.supervisor.full_name,
                    supervisor_phone=agent.supervisor.phone,
                    status=agent_activity_status,
                    vertical_lead_first_name=vertical_lead_first_name,
                    vertical_lead_last_name=vertical_lead_last_name,
                    vertical_lead_full_name=vertical_lead_full_name,
                    vertical_lead_phone=vertical_lead_phone,
                    total_sales=total_sales_today,
                    commission_earned=commission_earned_for_draw_games_today
                    + commission_earned_for_k_now_games_today
                    + commission_earned_for_instant_games_today,
                    percentage_of_target_achieved=round(
                        total_sales_today / RetailMetricsConstant.get_agent_daily_sales_target()
                    )
                    * 100,
                )

                LottoAgentMonthlyAnalytic.create_or_update_record(
                    phone=agent.phone,
                    first_name=agent.first_name,
                    last_name=agent.last_name,
                    full_name=agent.full_name,
                    supervisor_first_name=agent.supervisor.first_name,
                    supervisor_last_name=agent.supervisor.last_name,
                    supervisor_full_name=agent.supervisor.full_name,
                    supervisor_phone=agent.supervisor.phone,
                    status=agent_activity_status,
                    vertical_lead_first_name=vertical_lead_first_name,
                    vertical_lead_last_name=vertical_lead_last_name,
                    vertical_lead_full_name=vertical_lead_full_name,
                    vertical_lead_phone=vertical_lead_phone,
                    total_sales=total_sales_for_this_month,
                    commission_earned=commission_earned_for_draw_games_this_month
                    + commission_earned_for_k_now_games_this_month
                    + commission_earned_for_instant_games_this_month,
                    percentage_of_target_achieved=round(
                        total_sales_for_this_month / RetailMetricsConstant.get_agent_monthly_sales_target()
                    )
                    * 100,
                )

                # ALL TIME DATA

                all_time_game_plays = transaction_history_all_time.filter(transaction_from="GAME_PLAY")

                aggregated_draw_games_sales_all_time = (
                    all_time_game_plays.filter(
                        game_type__in=[
                            "SALARY_FOR_LIFE",
                            "WYSE_CASH",
                            "GHANA_LOTTO",
                            "KENYA_LOTTO",
                            "BANKER",
                            "KENYA_30_LOTTO",
                        ]
                    )
                    .aggregate(Sum("amount"))
                    .get("amount__sum")
                    or 0
                )
                aggregated_instant_games_sales_all_time = (
                    all_time_game_plays.filter(game_type__in=["INSTANT_CASHOUT", "QUIKA"])
                    .aggregate(Sum("amount"))
                    .get("amount__sum")
                    or 0
                )
                aggregated_k_now_games_sales_all_time = (
                    all_time_game_plays.filter(game_type__in=["K_NOW"]).aggregate(Sum("amount")).get("amount__sum")
                    or 0
                )

                total_sales_for_all_time = (
                    aggregated_draw_games_sales_all_time
                    + aggregated_instant_games_sales_all_time
                    + aggregated_k_now_games_sales_all_time
                )

                commission_earned_for_draw_games_all_time = 0
                if aggregated_draw_games_sales_all_time > 1:
                    commission_earned_for_draw_games_all_time = (
                        aggregated_draw_games_sales_all_time
                        * AgentConstantVariables.get_draw_game_commission_percentage()
                    )

                commission_earned_for_k_now_games_all_time = 0
                if aggregated_k_now_games_sales_all_time > 1:
                    commission_earned_for_k_now_games_all_time = (
                        aggregated_k_now_games_sales_all_time * AfricaLottoConstants.get_k_now_commission_percentage()
                    )

                commission_earned_for_instant_games_all_time = 0
                if aggregated_instant_games_sales_all_time > 1:
                    commission_earned_for_instant_games_all_time = (
                        aggregated_instant_games_sales_all_time
                        * AgentConstantVariables.get_instant_game_commission_percentage()
                    )

                LottoAgentAnalytic.create_or_update_record(
                    phone=agent.phone,
                    first_name=agent.first_name,
                    last_name=agent.last_name,
                    full_name=agent.full_name,
                    supervisor_first_name=agent.supervisor.first_name,
                    supervisor_last_name=agent.supervisor.last_name,
                    supervisor_full_name=agent.supervisor.full_name,
                    supervisor_phone=agent.supervisor.phone,
                    status=agent_activity_status,
                    vertical_lead_first_name=vertical_lead_first_name,
                    vertical_lead_last_name=vertical_lead_last_name,
                    vertical_lead_full_name=vertical_lead_full_name,
                    vertical_lead_phone=vertical_lead_phone,
                    total_sales=total_sales_for_all_time,
                    commission_earned=commission_earned_for_draw_games_all_time
                    + commission_earned_for_k_now_games_all_time
                    + commission_earned_for_instant_games_all_time,
                    percentage_of_target_achieved=0,
                )

            # supervisor metrics
            daily_metrics_qs = LottoAgentDailyAnalytic.objects.filter(
                created_at__date=current_datetime.date(), supervisor_phone=supervisor_instance.phone
            )
            count_of_agents_created_today = len(agents.filter(created_date__date=current_datetime.date()))
            number_of_active_agents_today = len(daily_metrics_qs.filter(status="ACTIVE"))
            number_of_inactive_agents_today = len(daily_metrics_qs.filter(status="INACTIVE"))
            number_of_absent_agents_today = len(daily_metrics_qs.filter(status="ABSENT"))
            number_of_terminal_under_recovery = 0

            total_sales = daily_metrics_qs.aggregate(Sum("total_sales")).get("total_sales__sum") or 0

            percentage_of_target_achieved = (
                round(total_sales / (RetailMetricsConstant.get_agent_monthly_sales_target() * len(daily_metrics_qs)))
                * 100
            )

            LottoSupervisorDailyAnalytic.create_or_update_record(
                phone=supervisor_instance.phone,
                first_name=supervisor_instance.first_name,
                last_name=supervisor_instance.last_name,
                full_name=supervisor_instance.full_name,
                number_of_agents=count_of_agents_created_today,
                number_of_active_agents=number_of_active_agents_today,
                number_of_inactive_agents=number_of_inactive_agents_today,
                number_of_absent_agents=number_of_absent_agents_today,
                number_of_terminal_under_recovery=number_of_terminal_under_recovery,
                percentage_of_target_achieved=percentage_of_target_achieved,
                total_sales=total_sales,
                commission_earned=0,
            )

            ########## THIS MONTH
            daily_metrics_qs = LottoAgentMonthlyAnalytic.objects.filter(
                created_at__year=current_datetime.year, supervisor_phone=supervisor_instance.phone
            ).filter(created_at__month=current_datetime.month)
            count_of_agents_created_today = len(
                agents.filter(created_date__year=current_datetime.year).filter(
                    created_date__month=current_datetime.month
                )
            )
            number_of_active_agents_today = len(daily_metrics_qs.filter(status="ACTIVE"))
            number_of_inactive_agents_today = len(daily_metrics_qs.filter(status="INACTIVE"))
            number_of_absent_agents_today = len(daily_metrics_qs.filter(status="ABSENT"))
            number_of_terminal_under_recovery = 0

            total_sales = daily_metrics_qs.aggregate(Sum("total_sales")).get("total_sales__sum") or 0

            percentage_of_target_achieved = (
                round(total_sales / (RetailMetricsConstant.get_agent_monthly_sales_target() * len(daily_metrics_qs)))
                * 100
            )

            LottoSupervisorMonthlyAnalytic.create_or_update_record(
                phone=supervisor_instance.phone,
                first_name=supervisor_instance.first_name,
                last_name=supervisor_instance.last_name,
                full_name=supervisor_instance.full_name,
                number_of_agents=count_of_agents_created_today,
                number_of_active_agents=number_of_active_agents_today,
                number_of_inactive_agents=number_of_inactive_agents_today,
                number_of_absent_agents=number_of_absent_agents_today,
                number_of_terminal_under_recovery=number_of_terminal_under_recovery,
                percentage_of_target_achieved=percentage_of_target_achieved,
                total_sales=total_sales,
                commission_earned=0,
            )
            
            
            ########## ALL TIME
            all_metrics_qs = LottoAgentAnalytic.objects.filter(
                supervisor_phone=supervisor_instance.phone
            )
            count_of_agents_created_all_time = len(agents)
            
            number_of_active_agents_all_time = len(all_metrics_qs.filter(status="ACTIVE"))
            number_of_inactive_agents_all = len(all_metrics_qs.filter(status="INACTIVE"))
            number_of_absent_agents_today = len(all_metrics_qs.filter(status="ABSENT"))
            number_of_terminal_under_recovery = 0

            total_sales = all_metrics_qs.aggregate(Sum("total_sales")).get("total_sales__sum") or 0

            percentage_of_target_achieved = 0

            LottoSupervisorAnalytic.create_or_update_record(
                phone=supervisor_instance.phone,
                first_name=supervisor_instance.first_name,
                last_name=supervisor_instance.last_name,
                full_name=supervisor_instance.full_name,
                number_of_agents=count_of_agents_created_all_time,
                number_of_active_agents=number_of_active_agents_all_time,
                number_of_inactive_agents=number_of_inactive_agents_all,
                number_of_absent_agents=number_of_absent_agents_today,
                number_of_terminal_under_recovery=number_of_terminal_under_recovery,
                percentage_of_target_achieved=percentage_of_target_achieved,
                total_sales=total_sales,
                commission_earned=0,
            )




        vertical_leads = LottoVerticalLead.objects.all()
        
        for team_lead in vertical_leads:
            supervisor_phone_numbers = list(Supervisor.objects.filter(vertical_lead=team_lead).values_list('phone', flat=True))
            
            timezone = pytz.timezone(settings.TIME_ZONE)
            current_datetime = datetime.now(tz=timezone)
            
            number_of_supervisors = len(supervisor_phone_numbers)
            number_of_active_supervisors = 0
            number_of_inactive_supervisors = 0
            number_of_agents = 0
            number_of_active_agents = 0
            number_of_inactive_agents = 0
            number_of_absent_agents = 0
            number_terminal_under_recovery = 0
            total_sales = 0
            
            
            for supervisor_phone_number in supervisor_phone_numbers:
                daily_metrics_qs = LottoSupervisorDailyAnalytic.objects.filter(
                    created_at__date=current_datetime.date(), phone=supervisor_phone_number
                ).last()
                
                if daily_metrics_qs is None:
                    continue
                
                number_of_agents += daily_metrics_qs.number_of_agents
                
                active_agent_percentage = (daily_metrics_qs.number_of_active_agents / daily_metrics_qs.number_of_agents) * 100 
                if active_agent_percentage >= 80:
                    number_of_active_supervisors += 1
                else:
                    number_of_inactive_supervisors += 1
                    
                
                number_of_active_agents += daily_metrics_qs.number_of_active_agents
                number_of_inactive_agents += daily_metrics_qs.number_of_inactive_agents
                number_of_absent_agents += daily_metrics_qs.number_of_absent_agents
                number_terminal_under_recovery += daily_metrics_qs.number_of_terminal_under_recovery
                
                total_sales += daily_metrics_qs.total_sales
                
            
            
            
            try:
                daily_instance = LottoVerticalLeadDailyAnalytic.objects.get(
                    created_at__date=current_datetime.date(), phone=team_lead.phone
                )
            except LottoVerticalLeadDailyAnalytic.DoesNotExist:
                daily_instance = LottoVerticalLeadDailyAnalytic.objects.create(phone=team_lead.phone)
            
            
            daily_instance.first_name = team_lead.first_name
            daily_instance.last_name = team_lead.last_name
            daily_instance.full_name = team_lead.full_name
            daily_instance.number_of_supervisors = number_of_supervisors
            daily_instance.number_of_active_supervisors = number_of_active_supervisors
            daily_instance.number_of_inactive_supervisors = number_of_inactive_supervisors
            daily_instance.number_of_agents = number_of_agents
            daily_instance.number_of_active_agents = number_of_active_agents
            daily_instance.number_of_inactive_agents = number_of_inactive_agents
            daily_instance.number_of_absent_agents = number_of_absent_agents
            daily_instance.number_terminal_under_recovery = number_terminal_under_recovery
            daily_instance.total_sales = total_sales
            daily_instance.save()
            
            
            
            # monthly metrics
            number_of_active_supervisors = 0
            number_of_inactive_supervisors = 0
            number_of_agents = 0
            number_of_active_agents = 0
            number_of_inactive_agents = 0
            number_of_absent_agents = 0
            number_terminal_under_recovery = 0
            total_sales = 0
            
            for supervisor_phone_number in supervisor_phone_numbers:
                monthly_metrics_qs = LottoSupervisorMonthlyAnalytic.objects.filter(
                    created_at__year=current_datetime.year, phone=supervisor_phone_number
                ).filter(created_at__month=current_datetime.month).last()
                
                if monthly_metrics_qs is None:
                    continue
                
                number_of_agents += monthly_metrics_qs.number_of_agents
                
                active_agent_percentage = (monthly_metrics_qs.number_of_active_agents / monthly_metrics_qs.number_of_agents) * 100 
                if active_agent_percentage >= 80:
                    number_of_active_supervisors += 1
                else:
                    number_of_inactive_supervisors += 1
                    
                
                number_of_active_agents += monthly_metrics_qs.number_of_active_agents
                number_of_inactive_agents += monthly_metrics_qs.number_of_inactive_agents
                number_of_absent_agents += monthly_metrics_qs.number_of_absent_agents
                number_terminal_under_recovery += monthly_metrics_qs.number_of_terminal_under_recovery
                
                total_sales += monthly_metrics_qs.total_sales
            
            
            try:
                monthly_instance = LottoVerticalLeadMonthlyAnalytic.objects.get(
                    created_at__year=current_datetime.year, created_at__month=current_datetime.month, phone=team_lead.phone
                )
            except LottoVerticalLeadMonthlyAnalytic.DoesNotExist:
                monthly_instance = LottoVerticalLeadMonthlyAnalytic.objects.create(phone=team_lead.phone)
                
            
            monthly_instance.first_name = team_lead.first_name
            monthly_instance.last_name = team_lead.last_name
            monthly_instance.full_name = team_lead.full_name
            monthly_instance.number_of_supervisors = number_of_supervisors
            monthly_instance.number_of_active_supervisors = number_of_active_supervisors
            monthly_instance.number_of_inactive_supervisors = number_of_inactive_supervisors
            monthly_instance.number_of_agents = number_of_agents
            monthly_instance.number_of_active_agents = number_of_active_agents
            monthly_instance.number_of_inactive_agents = number_of_inactive_agents
            monthly_instance.number_of_absent_agents = number_of_absent_agents
            monthly_instance.number_of_terminal_under_recovery = number_terminal_under_recovery
            monthly_instance.total_sales = total_sales
            monthly_instance.save()
            
            
            # all time metrics
            number_of_active_supervisors = 0
            number_of_inactive_supervisors = 0
            number_of_agents = 0
            number_of_active_agents = 0
            number_of_inactive_agents = 0
            number_of_absent_agents = 0
            number_terminal_under_recovery = 0
            total_sales = 0
            
            for supervisor_phone_number in supervisor_phone_numbers:
                all_time_metrics_qs = LottoSupervisorAnalytic.objects.filter(
                    phone=supervisor_phone_number
                ).last()
                
                if all_time_metrics_qs is None:
                    continue
                
                number_of_agents += all_time_metrics_qs.number_of_agents
                
                active_agent_percentage = (all_time_metrics_qs.number_of_active_agents / all_time_metrics_qs.number_of_agents) * 100 
                if active_agent_percentage >= 80:
                    number_of_active_supervisors += 1
                else:
                    number_of_inactive_supervisors += 1
                    
                
                number_of_active_agents += all_time_metrics_qs.number_of_active_agents
                number_of_inactive_agents += all_time_metrics_qs.number_of_inactive_agents
                number_of_absent_agents += all_time_metrics_qs.number_of_absent_agents
                number_terminal_under_recovery += all_time_metrics_qs.number_of_terminal_under_recovery
                
                total_sales += all_time_metrics_qs.total_sales
            
            try:
                all_time_instance = LottoVerticalLeadAnalytic.objects.get(
                    phone=team_lead.phone
                )
            except LottoVerticalLeadAnalytic.DoesNotExist:
                all_time_instance = LottoVerticalLeadAnalytic.objects.create(phone=team_lead.phone)
            
            
            all_time_instance.first_name = team_lead.first_name
            all_time_instance.last_name = team_lead.last_name
            all_time_instance.full_name = team_lead.full_name
            all_time_instance.number_of_supervisors = number_of_supervisors
            all_time_instance.number_of_active_supervisors = number_of_active_supervisors
            all_time_instance.number_of_inactive_supervisors = number_of_inactive_supervisors
            all_time_instance.number_of_agents = number_of_agents
            all_time_instance.number_of_active_agents = number_of_active_agents
            all_time_instance.number_of_inactive_agents = number_of_inactive_agents
            all_time_instance.number_of_absent_agents = number_of_absent_agents
            all_time_instance.number_of_terminal_under_recovery = number_terminal_under_recovery
            all_time_instance.total_sales = total_sales
            all_time_instance.save()
            
            
            
            
                
            
            
            
                
                
                
                
            
            
            
            
        










