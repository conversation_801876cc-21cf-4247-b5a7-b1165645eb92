from django.core.management.base import BaseCommand
from django.db.models import Sum
from pos_app.models import AgentFundingTable, AgentWalletTransaction
from retail_metrics.models import GameAnalytics, WalletFundingAnalytics


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        years = [2023, 2024, 2025]

        for exsiting_year in years:
            for i in range(1, 13):
                
                queryset = AgentFundingTable.objects.filter(
                    created_at__year = exsiting_year,
                ).filter(created_at__month = i)


                amount = queryset.aggregate(Sum("amount")).get("amount__sum")
                if amount is None:
                    amount = 0

                
                WalletFundingAnalytics.update_record(
                    amount = amount,
                    year = exsiting_year,
                    month = i
                )

                    
