from django.core.management.base import BaseCommand
import pytz
from django.conf import settings
from datetime import datetime, timedelta

from main.models import PayoutTransactionTable
from pos_app.models import AgentFundingTable, LottoAgentRemittanceTable, PosLotteryWinners
from django.db.models import Sum

from retail_metrics.models import DailyFundingWalletActivity





class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)
        yesterday = current_datetime - timedelta(days =1)

        remittance_queryset = LottoAgentRemittanceTable.objects.filter(created_at__date__range = [yesterday.date(), current_datetime.date()], remitted = False)
        due_remittance = remittance_queryset.filter(due = True).aggregate(Sum("amount")).get("amount__sum") or 0
        pending_remittance = remittance_queryset.filter(due = False).aggregate(Sum("amount")).get("amount__sum") or 0

        payout_transaction_queryset = PayoutTransactionTable.objects.filter(date_added__date = current_datetime.date(), source_response_payload__icontains = "completed successfully")

        total_rtp_deduction = payout_transaction_queryset.filter(source_wallet = "AGENT_FUNDING_WALLET", recipient_wallet__in = ["RETAIL_RTP_WALLET", "GHANA_RTP_WALLET"]).aggregate(Sum("amount")).get("amount__sum") or 0
        total_rto_deduction = payout_transaction_queryset.filter(source_wallet = "AGENT_FUNDING_WALLET", recipient_wallet = "RTO_WALLET").aggregate(Sum("amount")).get("amount__sum") or 0
        total_commission_deduction = payout_transaction_queryset.filter(source_wallet = "AGENT_FUNDING_WALLET", recipient_wallet = "RETAIL_COMMISSION_WALLET").aggregate(Sum("amount")).get("amount__sum") or 0


        total_funding = AgentFundingTable.objects.filter(created_at__date = current_datetime.date()).aggregate(Sum("amount")).get("amount__sum") or 0

        unclaimed_ticket_qs = PosLotteryWinners.objects.filter(expired = False, payout_successful = False)
        unclaimed_ticket_count = len(unclaimed_ticket_qs)
        unclaimed_ticket_value = unclaimed_ticket_qs.aggregate(Sum("amount_won")).get("amount_won__sum") or 0


        DailyFundingWalletActivity.create_or_update(
            total_funding = round(total_funding, 2),
            total_due_remittance = round(due_remittance, 2),
            total_pending_remittance = round(pending_remittance, 2),
            total_rtp_deduction = round(total_rtp_deduction, 2),
            total_rto_deduction = round(total_rto_deduction,2),
            total_commission_deduction = round(total_commission_deduction, 2),
            uncliamed_winning_count = unclaimed_ticket_count,
            uncliamed_winning_value = round(unclaimed_ticket_value, 2)
        )

        
