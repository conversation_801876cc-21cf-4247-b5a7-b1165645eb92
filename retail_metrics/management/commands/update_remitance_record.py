from django.core.management.base import BaseCommand
from django.db.models import Sum
from pos_app.models import AgentFundingTable, AgentWalletTransaction, LottoAgentRemittanceTable
from retail_metrics.models import GameAnalytics, RemittanceAnalytics, WalletFundingAnalytics


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        years = [2023, 2024, 2025]

        for exsiting_year in years:
            for i in range(1, 13):

                
                
                queryset = LottoAgentRemittanceTable.objects.filter(created_at__year = exsiting_year).filter(created_at__month = i)
                amount_remitted = queryset.filter(remitted = True).aggregate(Sum("amount_paid")).get("amount_paid__sum")
                if amount_remitted is None:
                    amount_remitted = 0


                amount_due = queryset.filter(remitted = False, due = True).aggregate(Sum("amount")).get("amount__sum")
                if amount_due is None:
                    amount_due = 0

                
                RemittanceAnalytics.update_record(
                    amount_remitted = amount_remitted,
                    amount_due = amount_due,
                    year = exsiting_year,
                    month = i
                )

                    
