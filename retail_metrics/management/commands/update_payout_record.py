from django.core.management.base import BaseCommand
from django.db.models import Sum
from main.models import PayoutTransactionTable
from pos_app.models import AgentFundingTable, AgentWalletTransaction
from retail_metrics.models import GameAnalytics, PayoutAnalytics, WalletFundingAnalytics


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        years = [2023, 2024, 2025]

        for exsiting_year in years:
            for i in range(1, 13):
                
                queryset = PayoutTransactionTable.objects.filter(
                    date_added__year = exsiting_year, channel = "BUDDY",
                    recipient_wallet = "USER_WALLET",
                    type_of_transaction = "PAYOUT",
                    disbursed = True
                ).filter(date_added__month = i)


                amount = queryset.aggregate(Sum("amount")).get("amount__sum")
                if amount is None:
                    amount = 0

                
                PayoutAnalytics.update_record(
                    amount = amount,
                    year = exsiting_year,
                    month = i
                )

                    
