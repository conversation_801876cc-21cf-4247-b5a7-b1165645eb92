import uuid
from datetime import datetime

from cryptography.fernet import Ferne<PERSON>
from django.conf import settings
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from main.api.api_lottery_helpers import create_lottery, generate_lucky_number
from main.api.serializer import DataSetSerializer
from main.helpers.helper_functions import ByteHelper
from main.helpers.woven_manager import WovenHelper
from main.models import CreditCard, LotteryBatch, LotteryModel, LottoTicket, UserProfile
from main.serializers import CreditCardSerializer
from main.ussd.bankdb import filter_bank
from pos_app.custom_serializer import CustomLottoSerializer
from pos_app.models import AgentPayoutBeneficiary, AgentWallet
from pos_app.pos_helpers import (
    CustomPaginator,
    PosLottoHelper,
    merge_and_sort_game_history,
    remove_duplicate_game_history,
    serialize_WYSE_CASH_game_history,
)
from pos_app.pos_permission import AgentCanLogin
from pos_app.serializers import (
    AgentPayoutSerializer,
    MobileGameHistorySerializer,
    SecondGameHistorySerializer,
)
from wallet_app.helpers.payment_gateway import PaymentGateway
from wallet_app.models import PaystackTransaction, WithdrawalTable

from .mobile_lotto_helper import MobileAgentHelper, fetch_paid_lottery
from .serializers import (
    AgentWalletSerializer,
    MobileAgentPayoutSerializer,
    MobileLottoPaymentSerializer,
    MobileLottoWalletCardFundingSerializer,
)


class MobileAgentDetails(APIView):
    permission_classes = [AgentCanLogin]

    def get(self, request):
        UserProfile.objects.get_or_create(phone_number=request.user.phone, channel="MOBILE")

        query_set = AgentWallet.objects.select_related("agent").filter(agent=request.user)

        serilizer = AgentWalletSerializer(query_set, many=True)

        return Response(serilizer.data, status=status.HTTP_200_OK)


class MobileLott0View(APIView):
    permission_classes = [AgentCanLogin]

    def post(self, request):
        _lotto_types = LottoTicket.LOTTO_TYPE

        list_of_lotto_types = []

        for _, value in _lotto_types:
            list_of_lotto_types.append(value)

        # check if the lotto type is valid
        req_lotto_type = str(request.GET.get("lotto_type")).upper()

        if req_lotto_type not in list_of_lotto_types:
            data = {
                "message": "Invalid lotto type",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        data = dict(request.data)

        serializer = CustomLottoSerializer(data=data)
        serializer.is_valid(raise_exception=True)

        user, created = UserProfile.objects.get_or_create(phone_number=request.user.phone, channel="MOBILE")

        play_lotto_reponse = MobileAgentHelper.play_lotto(user, serializer.data, req_lotto_type)

        if play_lotto_reponse.get("status") == "error":
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        return Response(data=play_lotto_reponse, status=status.HTTP_200_OK)

    def delete(self, request):
        id = request.GET.get("id")

        get_lottery = LottoTicket.objects.filter(id=int(id)).last()

        if get_lottery is None:
            data = {"message": "Ticket not found"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        game_id = get_lottery.game_play_id

        number_of_ticker = get_lottery.number_of_ticket - 1
        expected_amount = get_lottery.expected_amount - get_lottery.stake_amount

        LottoTicket.objects.filter(game_play_id=game_id).update(number_of_ticket=number_of_ticker, expected_amount=expected_amount)

        get_lottery.delete()

        data = {"message": "Ticket deleted"}

        return Response(data=data, status=status.HTTP_200_OK)


class MobileLottoPaymentView(APIView):
    permission_classes = [AgentCanLogin]

    def post(self, request):
        serializer = MobileLottoPaymentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        from_main_wallet = serializer.data.get("from_main_wallet")
        paystack = serializer.data.get("paystack")
        amount = serializer.data.get("amount")
        game_play_id = serializer.data.get("game_play_id")

        if from_main_wallet is True:
            agent_wallet = AgentWallet.objects.filter(agent=request.user).last()

            if agent_wallet is None:
                data = {"message": "No wallet found"}
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            if agent_wallet.game_play_bal < amount:
                # if user has insufficient funds in his play wallet
                # we will check if user has sufficient funds in withdrawable_available_balance
                # if yes, we will play the game from his withdrawable_available_balance balance
                # if not, we will return error message

                if agent_wallet.withdrawable_available_balance < amount:
                    # combine game_available_balance and withdrawable_available_balance

                    play_amount = agent_wallet.game_play_bal + agent_wallet.withdrawable_available_balance

                    if play_amount < amount:
                        data = {"message": "Insufficient funds"}
                        return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                    # spread the payment across lottery
                    # we'll use the game play id to detect the type of lottery

                    # user profile instance
                    user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

                    lottery_model = LotteryModel.objects.filter(
                        game_play_id=game_play_id, user_profile=user_profile, expected_amount=amount, paid=False
                    )

                    try:
                        if not lottery_model.exists():
                            raise LotteryModel.DoesNotExist

                        else:
                            # spread the payment across lottery
                            lottery_model.update(paid=True, paid_date=datetime.now())

                            lottery_model = LotteryModel.objects.filter(game_play_id=game_play_id, user_profile=user_profile, expected_amount=amount)

                            # update user wallet balance
                            play_amount -= amount
                            agent_wallet.game_play_bal = 0
                            agent_wallet.withdrawable_available_bal = play_amount
                            agent_wallet.save()

                            _data = {
                                "status": "Accepted",
                                "game_id": game_play_id,
                                "game_type": lottery_model.last().lottery_type,
                                "stake_per_pick": 100,
                                "total_stake": lottery_model.last().expected_amount,
                                "total_ticket": lottery_model.count(),
                            }

                            # get game bands
                            lottery_bands = [i.band for i in lottery_model]

                            empty_band_dict = {i: set([]) for i in lottery_bands}

                            for key in lottery_bands:
                                for data in lottery_model:
                                    if data.band == key:
                                        empty_band_dict[key].add(data.unique_id)

                            lottery_data = []

                            for key in empty_band_dict:
                                lottery_data.append(
                                    {
                                        "ticket": empty_band_dict[key],
                                    }
                                )

                            _data["tickets"] = lottery_data

                            return Response(data=_data, status=status.HTTP_200_OK)

                    except LotteryModel.DoesNotExist:
                        # if LotteryModel.DoesNotExist
                        # let;s check LottoTicket table

                        lotto_ticket = LottoTicket.objects.filter(
                            game_play_id=game_play_id, paid=False, user_profile=user_profile, expected_amount=float(amount)
                        )

                        if not lotto_ticket.exists():
                            data = {"message": "No ticket found"}
                            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                        _lottery = lotto_ticket.last()

                        lotto_ticket.update(paid=True)

                        # update user wallet balance
                        play_amount -= amount
                        agent_wallet.game_play_bal = 0
                        agent_wallet.withdrawable_available_bal = play_amount
                        agent_wallet.save()

                        # check if lottery type is INSTANT_CASHOUT, if yes, we draw the
                        # INSTANT_CASHOUT and give back the result

                        if _lottery.lottery_type == "INSTANT_CASHOUT":
                            # get lottery batch
                            get_current_batch = LotteryBatch.objects.filter(is_active=True, lottery_type=_lottery.lottery_type).last()

                            get_current_batch.instant_cash_draw()

                            _data = {
                                "status": "Accepted",
                                "game_id": _lottery.game_play_id,
                                "game_type": _lottery.lottery_type,
                                "stake_per_pick": 100,
                                "total_stake": _lottery.expected_amount,
                                "total_ticket": _lottery.number_of_ticket,
                            }

                            _lottery_data = []

                            for key in lotto_ticket:
                                if key.ticket in get_current_batch.lottery_winner_ticket:
                                    _lottery_data.append({"ticket": list(key.ticket), "won": True})

                                    # update user withdrawable_available_bal
                                    agent_wallet = AgentWallet.objects.filter(agent=request.user).last()
                                    agent_wallet.withdrawable_available_bal += 400
                                    agent_wallet.save()

                                else:
                                    _lottery_data.append({"ticket": list(key.ticket), "won": False})

                            _data["tickets"] = _lottery_data

                            return Response(data=_data, status=status.HTTP_200_OK)

                        elif _lottery.lottery_type == "SALARY_FOR_LIFE":
                            lotto_ticket.update(paid=True)

                            _data = {
                                "status": "Accepted",
                                "game_id": _lottery.game_play_id,
                                "game_type": _lottery.lottery_type,
                                "stake_per_pick": 100,
                                "total_stake": _lottery.expected_amount,
                                "total_ticket": _lottery.number_of_ticket,
                            }

                            _lottery_data = []

                            for key in lotto_ticket:
                                _lottery_data.append({"ticket": list(key.ticket)})

                            _data["tickets"] = _lottery_data

                            return Response(data=_data, status=status.HTTP_200_OK)

            else:
                # user profile instance
                user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

                # lottery_model = LotteryModel.objects.filter(game_play_id=game_play_id, user_profile = user_profile, expected_amount = float(amount))

                lottery_model = LotteryModel.objects.filter(
                    game_play_id=game_play_id, user_profile=user_profile, expected_amount=float(amount), paid=False
                )

                try:
                    if not lottery_model.exists():
                        raise LotteryModel.DoesNotExist

                    else:
                        # spread the payment across lottery
                        lottery_model.update(paid=True, paid_date=datetime.now())

                        lottery_model = LotteryModel.objects.filter(
                            game_play_id=game_play_id, user_profile=user_profile, expected_amount=float(amount)
                        )

                        _data = {
                            "status": "Accepted",
                            "game_id": game_play_id,
                            "game_type": lottery_model.last().lottery_type,
                            "stake_per_pick": 100,
                            "total_stake": lottery_model.last().expected_amount,
                            "total_ticket": lottery_model.count(),
                        }

                        # get game bands
                        lottery_bands = [i.band for i in lottery_model]

                        empty_band_dict = {i: set([]) for i in lottery_bands}

                        for key in lottery_bands:
                            for data in lottery_model:
                                if data.band == key:
                                    empty_band_dict[key].add(data.unique_id)

                        lottery_data = []

                        for key in empty_band_dict:
                            lottery_data.append(
                                {
                                    "ticket": empty_band_dict[key],
                                }
                            )

                        _data["tickets"] = lottery_data

                        return Response(data=_data, status=status.HTTP_200_OK)

                except LotteryModel.DoesNotExist:
                    # if LotteryModel.DoesNotExist
                    # let;s check LottoTicket table

                    lotto_ticket = LottoTicket.objects.filter(
                        game_play_id=game_play_id, paid=False, user_profile=user_profile, expected_amount=float(amount)
                    )

                    if not lotto_ticket.exists():
                        data = {"message": "No ticket found"}
                        return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                    _lottery = lotto_ticket.last()

                    lotto_ticket.update(paid=True)

                    # update user wallet balance
                    agent_wallet.game_play_bal -= amount
                    agent_wallet.save()

                    lotto_ticket = LottoTicket.objects.filter(game_play_id=game_play_id, user_profile=user_profile, expected_amount=float(amount))
                    """
                        The reason why this new queryset is made is because if we call the first initialized "lotto_ticket"
                        it'll try to make a new query to the database, and it'll return an empty queryset becuase "paid" has been changed from "False"
                        to "True"
                    """

                    # check if lottery type is INSTANT_CASHOUT, if yes, we draw the
                    # INSTANT_CASHOUT and give back the result

                    if _lottery.lottery_type == "INSTANT_CASHOUT":
                        # get lottery batch
                        get_current_batch = LotteryBatch.objects.filter(is_active=True, lottery_type=_lottery.lottery_type).last()

                        get_current_batch.instant_cash_draw()

                        _data = {
                            "status": "Accepted",
                            "game_id": _lottery.game_play_id,
                            "game_type": _lottery.lottery_type,
                            "stake_per_pick": 100,
                            "total_stake": _lottery.expected_amount,
                            "total_ticket": _lottery.number_of_ticket,
                        }

                        _lottery_data = []

                        for key in lotto_ticket:
                            if key.ticket in get_current_batch.lottery_winner_ticket:
                                _lottery_data.append({"ticket": list(key.ticket), "won": True})

                                # update user withdrawable_available_bal
                                agent_wallet = AgentWallet.objects.filter(agent=request.user).last()
                                agent_wallet.withdrawable_available_bal += 400
                                agent_wallet.save()

                            else:
                                _lottery_data.append({"ticket": list(key.ticket), "won": False})

                        _data["tickets"] = _lottery_data

                        return Response(data=_data, status=status.HTTP_200_OK)

                    elif _lottery.lottery_type == "SALARY_FOR_LIFE":
                        lotto_ticket.update(paid=True)

                        # update user wallet balance
                        agent_wallet.game_play_bal -= amount
                        agent_wallet.save()

                        _data = {
                            "status": "Accepted",
                            "game_id": _lottery.game_play_id,
                            "game_type": _lottery.lottery_type,
                            "stake_per_pick": 100,
                            "total_stake": _lottery.expected_amount,
                            "total_ticket": _lottery.number_of_ticket,
                        }

                        lotto_ticket = LottoTicket.objects.filter(game_play_id=game_play_id, user_profile=user_profile, expected_amount=float(amount))

                        _lottery_data = []

                        for key in lotto_ticket:
                            _lottery_data.append({"ticket": list(key.ticket)})

                        _data["tickets"] = _lottery_data

                        return Response(data=_data, status=status.HTTP_200_OK)

        elif paystack is True:
            # user profile instance
            user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

            # lottery_model = LotteryModel.objects.filter(game_play_id=game_play_id, paid = False)

            lottery_model = LotteryModel.objects.filter(
                game_play_id=game_play_id, user_profile=user_profile, expected_amount=float(amount), paid=False
            )

            try:
                if not lottery_model.exists():
                    raise LotteryModel.DoesNotExist

                else:
                    # paystack payload
                    transaction_ref = f"LTT-PAY{uuid.uuid4()}-{lottery_model.last().id}"

                    paystack_payload = {
                        "email": request.user.email,
                        "amount": float(amount) * 100,
                        "currency": "NGN",
                        "reference": transaction_ref,
                    }

                    # create transaction

                    PaystackTransaction.objects.create(
                        user=user_profile,
                        amount=float(amount),
                        reference=transaction_ref,
                        created_at=timezone.now(),
                        paid_at=timezone.now(),
                        channel="MOBILE",
                        raw_data=paystack_payload,
                        payment_reason="MOBILE_LOTTERY_PAYMENT",
                    )

                    # initiate payment
                    paystack_api = PaymentGateway().paystack_link_request(**paystack_payload)

                    if paystack_api.get("access_code") is None or paystack_api.get("authorization_url") == "":
                        data = {"message": "Paystack payment failed"}
                        return Response(data=data, status=status.HTTP_200_OK)

                    paystack_api["public_key"] = settings.PAYSTACK_PUBLIC_KEY

                    return Response(data=paystack_api, status=status.HTTP_200_OK)

            except LotteryModel.DoesNotExist:
                lotto_ticket = LottoTicket.objects.filter(
                    game_play_id=game_play_id, paid=False, user_profile=user_profile, expected_amount=float(amount)
                )

                if not lotto_ticket.exists():
                    data = {"message": "No ticket found"}
                    return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

                # paystack payload
                transaction_ref = f"LTT-PAY{uuid.uuid4()}-{lotto_ticket.last().id}"

                paystack_payload = {
                    "email": request.user.email,
                    "amount": float(amount) * 100,
                    "currency": "NGN",
                    "reference": transaction_ref,
                }

                # create transaction
                PaystackTransaction.objects.create(
                    user=user_profile,
                    amount=float(amount),
                    reference=transaction_ref,
                    created_at=timezone.now(),
                    paid_at=timezone.now(),
                    channel="MOBILE",
                    raw_data=paystack_payload,
                    payment_reason="MOBILE_LOTTERY_PAYMENT",
                )

                # initiate payment
                paystack_api = PaymentGateway().paystack_link_request(**paystack_payload)

                if paystack_api.get("access_code") is None or paystack_api.get("authorization_url") == "":
                    data = {"message": "Paystack payment failed"}
                    return Response(data=data, status=status.HTTP_200_OK)

                paystack_api["public_key"] = settings.PAYSTACK_PUBLIC_KEY

                return Response(data=paystack_api, status=status.HTTP_200_OK)

        return Response(serializer.data, status=status.HTTP_200_OK)


class FetchPaidMobileLottery(APIView):
    """
    Fetch paid mobile lottery
    """

    permission_classes = [AgentCanLogin]

    def get(self, request):
        """
        Fetch paid mobile lottery
        """
        game_play_id = request.GET.get("game_play_id")

        data = fetch_paid_lottery(game_play_id, request.user)

        if data.get("status") == "error":
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        return Response(data=data, status=status.HTTP_200_OK)


class AgentCreditCardView(APIView):
    """
    Agent credit card view
    """

    permission_classes = [AgentCanLogin]

    serializer_class = CreditCardSerializer

    def get(self, request):
        """
        Agent credit card view
        """
        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        user_credit_cards = CreditCard.objects.filter(user=user_profile)

        serializer = self.serializer_class(user_credit_cards, many=True)

        return Response(data=serializer.data, status=status.HTTP_200_OK)

    def delete(self, request):
        """
        Delete credit card
        """
        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        card_id = request.GET.get("card_id")

        card = CreditCard.objects.filter(user=user_profile, id=card_id)

        if not card.exists():
            data = {"message": "Card not found"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        card.delete()

        data = {"message": "Card deleted successfully"}
        return Response(data=data, status=status.HTTP_200_OK)


class MobileLottoWalletCardFunding(APIView):
    """
    Mobile lotto wallet funding
    """

    permission_classes = [AgentCanLogin]

    serializer_class = MobileLottoWalletCardFundingSerializer

    def post(self, request):
        """
        Mobile lotto wallet funding
        """
        serializer = self.serializer_class(data=request.data)

        serializer.is_valid(raise_exception=True)

        amount = serializer.validated_data.get("amount")
        card_id = serializer.validated_data.get("card_id")
        use_saved_card = serializer.validated_data.get("use_saved_card")

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        if not use_saved_card:
            # paystack payload
            transaction_ref = f"LTT-FUND-{uuid.uuid4()}-{request.user.id}"

            paystack_payload = {
                "email": request.user.email,
                "amount": float(amount) * 100,
                "currency": "NGN",
                "reference": transaction_ref,
            }

            # create transaction
            PaystackTransaction.objects.create(
                user=user_profile,
                amount=float(amount),
                reference=transaction_ref,
                created_at=timezone.now(),
                paid_at=timezone.now(),
                channel="MOBILE",
                raw_data=paystack_payload,
                payment_reason="MOBILE_AGENT_FUNDING",
            )

            # initiate payment
            paystack_api = PaymentGateway().paystack_link_request(**paystack_payload)

            if paystack_api.get("access_code") is None or paystack_api.get("authorization_url") == "":
                data = {"message": "Paystack payment failed"}
                return Response(data=data, status=status.HTTP_200_OK)

            paystack_api["public_key"] = settings.PAYSTACK_PUBLIC_KEY

            return Response(data=paystack_api, status=status.HTTP_200_OK)

        else:
            card = CreditCard.objects.filter(user=user_profile, id=card_id)

            if not card.exists():
                data = {"message": "Card not found"}
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            card_instance = card.last()

            if card_instance.reusable is False:
                data = {"message": "Card is not reusable"}
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            paystack_fernet_string = settings.PAYSTACK_BS64
            fernet = Fernet(bytes(paystack_fernet_string, encoding="utf-8"))

            decrypted_auth_code = fernet.decrypt(ByteHelper.convert_string_to_byte(card_instance.authorization_code)).decode()

            # paystack payload
            transaction_ref = f"LTT-FUND{uuid.uuid4()}-{request.user.id}"

            payload = {
                "amount": float(amount) * 100,
                "reference": transaction_ref,
                "email": card_instance.card_email,
                "authorization_code": decrypted_auth_code,
            }

            # create transaction
            paystack_trans_instance = PaystackTransaction.objects.create(
                user=user_profile,
                amount=float(amount),
                reference=transaction_ref,
                created_at=timezone.now(),
                paid_at=timezone.now(),
                channel="MOBILE",
                raw_data=payload,
                payment_reason="MOBILE_AGENT_FUNDING",
            )

            # initiate payment
            charge_card_response = PaymentGateway().charge_reusable_card(**payload)

            _status = charge_card_response.get("status")

            if _status is True:
                data = charge_card_response.get("data")
                gateway_res = data.get("gateway_response")

                if gateway_res == "Approved":
                    agent_wallet = AgentWallet.objects.filter(agent=request.user).last()

                    transaction_date = data["transaction_date"]

                    agent_wallet.game_play_bal += float(amount)
                    agent_wallet.save()

                    paystack_trans_instance.is_verified = True
                    paystack_trans_instance.status = "SUCCESSFUL"
                    paystack_trans_instance.raw_data = charge_card_response
                    paystack_trans_instance.save()

                    res = {
                        "status": True,
                        "message": "transaction was successful",
                        "results": {
                            "amount": amount,
                            "transaction_date": transaction_date,
                            "card_type": card_instance.card_type,
                        },
                    }

                    return Response(data=res, status=status.HTTP_200_OK)

                else:
                    res = {
                        "status": False,
                        "message": "TRANSACTION PENDING VERIFICATION",
                    }
                    return Response(data=res, status=status.HTTP_200_OK)

            else:
                result = {
                    "status": False,
                    "message": charge_card_response.get("message", None),
                }
                return Response(data=result, status=status.HTTP_400_BAD_REQUEST)


class MobileWhyseCashLottery(APIView):
    permission_classes = [AgentCanLogin]

    def get(self, request):
        generate_lottery_nums = generate_lucky_number(request.user, from_mobile_agent=True)

        return Response(generate_lottery_nums, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = DataSetSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # chekc if there's active batch running
        get_current_batch = LotteryBatch.objects.filter(is_active=True, lottery_type="WYSE_CASH").last()

        if get_current_batch is None:
            data = {"status": "error", "message": "No active batch found"}

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        ten = serializer.validated_data.get("ten")
        fifty = serializer.validated_data.get("fifty")
        two_fifty = serializer.validated_data.get("two_fifty")
        five_hundred = serializer.validated_data.get("five_hundred")

        all_data = [ten, fifty, two_fifty, five_hundred]

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        lottery = create_lottery(user_profile, all_data, channel="MOBILE")

        _serialize_WYSE_CASH = PosLottoHelper.serilaize_WYSE_CASH_data(lottery, request.user.phone, request.user)

        return Response(data=_serialize_WYSE_CASH, status=status.HTTP_200_OK)


class MobileGameHistory(APIView):
    permission_classes = [AgentCanLogin]

    def get(self, request):
        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        page = request.GET.get("page", 1)

        _loto_game_history = LottoTicket.objects.filter(user_profile=user_profile).order_by("-id")

        # paginator = Paginator(_loto_game_history, 30)

        paginate_all_lottery_ticket = CustomPaginator.paginate(request, _loto_game_history, page)

        lotto_serialized_data = MobileGameHistorySerializer(paginate_all_lottery_ticket, many=True)

        # remove duplicates
        _lotto_dara = remove_duplicate_game_history(lotto_serialized_data.data)

        _WYSE_CASH_game_history = LotteryModel.objects.filter(user_profile=user_profile).order_by("-id")
        # paginate_all_WYSE_CASH_games = Paginator.paginate(request=request, queryset=_WYSE_CASH_game_history, page=page)
        paginate_all_WYSE_CASH_games = CustomPaginator.paginate(request, _WYSE_CASH_game_history, page)

        WYSE_CASH_serialized_data = SecondGameHistorySerializer(paginate_all_WYSE_CASH_games, many=True)

        whyse_cahs_data = serialize_WYSE_CASH_game_history(WYSE_CASH_serialized_data.data)

        data = merge_and_sort_game_history(_lotto_dara, whyse_cahs_data)

        return Response(data, status=status.HTTP_200_OK)


class MobileAgentPayout(APIView):
    permission_classes = [AgentCanLogin]

    def post(self, request):
        serilaizer = AgentPayoutSerializer(data=request.data)
        serilaizer.is_valid(raise_exception=True)

        amount = serilaizer.validated_data.get("amount")
        account_number = serilaizer.validated_data.get("account_number")
        bank_code = serilaizer.validated_data.get("bank_code")
        narration = serilaizer.validated_data.get("narration")
        save_beneficiary = serilaizer.validated_data.get("save_beneficiary")

        # verify agent transaction pin

        # agent wallet
        agent_wallet = AgentWallet.objects.filter(agent=request.user).last()

        if agent_wallet is None:
            data = {"message": "You have no withdrawal wallet"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        _wallet_to_charge = agent_wallet.withdrawable_available_bal

        if amount > _wallet_to_charge:
            data = {"message": "Insufficient balance in your withdrawable wallet"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if amount < 500:
            data = {"message": "Minimum withdrawal amount is 500"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # VERIFY BANK DETAILS
        paystack = PaymentGateway()
        verify_bank_response = paystack.fetch_account_name(account_number=account_number, bank_code=bank_code)

        if isinstance(verify_bank_response, dict):
            print(verify_bank_response)

            if verify_bank_response.get("status") is False:
                data = {"message": "Invalid bank details"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            name = verify_bank_response.get("data").get("account_name")
            reference = "withdraw-{}".format(uuid.uuid4())

            _filter_bank_details = filter_bank(cbn_code=bank_code)

            if _filter_bank_details is None:
                data = {"message": "Invalid bank details"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            _bank_code = _filter_bank_details.get("cbn_code")

            # disbursement payload
            _narration = "disbursement"

            if narration is not None and narration != "":
                _narration = narration

            woven_payload = {
                "beneficiary_account_name": name,
                "beneficiary_nuban": account_number,
                "beneficiary_bank_code": _bank_code,
                "bank_code_scheme": "NIP",
                "currency_code": "NGN",
                "narration": _narration,
                "callback_url": "",
                "reference": reference,
                "amount": amount,
            }

            _bank_name = _filter_bank_details.get("name")

            _withdraw_table_instance = WithdrawalTable.objects.create(
                source="WOVEN",
                amount=amount,
                phone=request.user.phone,
                payout_trans_ref=reference,
                name=name,
                source_response_payload=woven_payload,
                bank_name=_bank_name,
                bank_code=_bank_code,
                channel="POS",
                agent_wallet_type="COMMISSION",
            )

            woven_payload["source_account"] = settings.WOVEN_DISBURSEMENT_SOURCE_ACCOUNT
            woven_payload["PIN"] = settings.WOVEN_DISBURSEMENT_PAYMENT_PIN

            # update agent wallet
            agent_wallet.withdrawable_available_bal -= amount
            agent_wallet.transaction_from = "WALLET"
            agent_wallet.transaction_type = "WIHTDRAWAL"
            agent_wallet.save()

            # initiate payout
            _woven_helper = WovenHelper()

            payout_response = _woven_helper.initaite_payout_to_winners(**woven_payload)

            _withdraw_table_instance.source_response_payload = payout_response
            _withdraw_table_instance.save()

            if save_beneficiary is True:
                AgentPayoutBeneficiary.objects.create(
                    agent=request.user,
                    beneficiary_name=name,
                    beneficiary_account_number=account_number,
                    beneficiary_bank=_filter_bank_details.get("name"),
                    beneficiary_bank_code=_filter_bank_details.get("cbn_code"),
                    beneficiary_bank_logo=_filter_bank_details.get("logo"),
                )

            data = {
                "status": "success",
                "message": "Withdrawal initiated",
                "transaction_ref": reference,
                "agent_id": request.user.user_uuid,
                "account_name": name,
                "bank_name": _bank_name,
                "amount": amount,
                "date": datetime.now(),
            }
            return Response(data, status=status.HTTP_200_OK)

        else:
            data = {"message": "Sorry we could not process your request"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class MobileAgentPayoutBeneficiary(APIView):
    permission_classes = [AgentCanLogin]

    def get(self, request):
        user = request.user
        beneficiary = AgentPayoutBeneficiary.objects.filter(agent=user, is_active=True)
        serializer = MobileAgentPayoutSerializer(beneficiary, many=True)

        return Response(data=serializer.data, status=status.HTTP_200_OK)
