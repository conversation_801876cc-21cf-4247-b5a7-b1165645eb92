from rest_framework import serializers

from main.models import (
    LotteryBatch,
    LotteryModel,
    LotteryWinnersTable,
    WovenAccountDetail,
)
from pos_app.models import Agent, AgentPayoutBeneficiary, AgentWallet


class MobileAgentDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Agent
        fields = "__all__"


class AgentWalletSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = AgentWallet
        fields = ["agent", "withdrawable_available_bal", "game_play_bal"]

    def to_representation(self, obj):
        serialized_data = super(AgentWalletSerializer, self).to_representation(obj)
        user_details = Agent.objects.filter(id=obj.agent.id).last()
        serialized_data.update(MobileAgentDetailsSerializer(user_details).data)

        # get user bank account details
        get_bank_obj = WovenAccountDetail.objects.filter(phone_number=obj.agent.phone, is_active=True).last()
        if get_bank_obj:
            serialized_data["bank_details"] = {
                "account_number": get_bank_obj.vnuban,
                "account_name": get_bank_obj.acct_name,
                "bank_name": get_bank_obj.bank_name,
            }
        else:
            serialized_data["bank_details"] = {
                "account_number": "",
                "account_name": "",
                "bank_name": "",
            }
        return serialized_data


class MobileLottoPaymentSerializer(serializers.Serializer):
    from_main_wallet = serializers.BooleanField(default=False)
    paystack = serializers.BooleanField(default=False)
    amount = serializers.FloatField(required=True)
    game_play_id = serializers.CharField()

    def validate(self, data):
        if data["from_main_wallet"] and data["paystack"]:
            raise serializers.ValidationError("You can't select both options")

        if not data["from_main_wallet"] and not data["paystack"]:
            raise serializers.ValidationError("You must select one option")

        return data


class MobileLottoWalletCardFundingSerializer(serializers.Serializer):
    amount = serializers.FloatField(required=True)
    card_id = serializers.CharField(required=False, allow_null=True)
    use_saved_card = serializers.BooleanField(default=False)


class MobileAgentPayoutSerializer(serializers.ModelSerializer):
    class Meta:
        model = AgentPayoutBeneficiary
        fields = "__all__"


class SecondMobileGameHistorySerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LotteryModel
        fields = "__all__"

    def to_representation(self, obj):
        serialized_data = super(SecondMobileGameHistorySerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        is_batch_active = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().is_active

        if is_batch_active:
            data.append(
                {
                    "game_play_id": serialized_data["game_play_id"],
                    "status": "accepted",
                    "stake_amount": serialized_data["stake_amount"],
                    "number_of_ticket": "",
                    "unique_id": serialized_data["unique_id"],
                    "date": serialized_data["date"],
                    "game_type": str(serialized_data["lottery_type"]).replace("_", " ").title(),
                    "pontential_win": serialized_data["band"],
                    "paid": serialized_data["paid"],
                    "won": False,
                    "lost": False,
                }
            )
        else:
            _is_lottery_winner = LotteryWinnersTable.objects.filter(unnique_id=serialized_data["unique_id"]).last()

            if _is_lottery_winner:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "accepted",
                        "stake_amount": serialized_data["stake_amount"],
                        "number_of_ticket": "",
                        "unique_id": serialized_data["unique_id"],
                        "date": serialized_data["date"],
                        "pontential_win": serialized_data["band"],
                        "paid": serialized_data["paid"],
                        "won": True,
                        "lost": False,
                    }
                )
            else:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "accepted",
                        "stake_amount": serialized_data["stake_amount"],
                        "number_of_ticket": "",
                        "unique_id": serialized_data["unique_id"],
                        "date": serialized_data["date"],
                        "pontential_win": serialized_data["band"],
                        "paid": serialized_data["paid"],
                        "won": False,
                        "lost": True,
                    }
                )

        return data[0]
