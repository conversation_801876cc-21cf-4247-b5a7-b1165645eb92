import uuid
from datetime import datetime
from time import sleep

from main.api.api_lottery_helpers import generate_game_play_id
from main.models import LotteryBatch, LotteryModel, LottoTicket
from pos_app.pos_helpers import PosLottoHelper
from pos_app.utils import serialize_ticket


class MobileAgentHelper:
    @staticmethod
    def play_lotto(user_instance, serialized_data, lotto_type):
        """
        PLAY LOTTO

        """

        get_game_play_id = generate_game_play_id()

        identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        amount = serialized_data.get("amount")
        phone = serialized_data.get("phone_number")
        lottery = serialized_data.get("lottery")

        get_current_batch = LotteryBatch.objects.filter(is_active=True, lottery_type=lotto_type).last()

        if get_current_batch is None:
            return {"status": "error", "message": "No active batch found"}

        if lotto_type == "SALARY_FOR_LIFE":
            number_of_ticket = len(lottery)
            expected_amount = int(number_of_ticket) * int(amount)

            data = {
                "status": "Accepted",
                "game_id": get_game_play_id,
                "game_type": lotto_type,
                "stake_per_pick": 100,
                "total_stake": number_of_ticket * 100,
                "total_ticket": number_of_ticket,
                "tickets": [],
            }

            for lottery_number in lottery:
                create_obj = LottoTicket.objects.create(
                    user_profile=user_instance,
                    batch=get_current_batch,
                    phone=phone,
                    stake_amount=int(amount),
                    expected_amount=int(expected_amount),
                    is_agent=True,
                    number_of_ticket=number_of_ticket,
                    channel="MOBILE",
                    game_play_id=get_game_play_id,
                    lottery_type=lotto_type,
                    ticket=serialize_ticket(lottery_number.get("ticket")),
                )

                data["tickets"].append({"id": create_obj.id, "ticket": lottery_number.get("ticket")})

            return data

        elif lotto_type == "INSTANT_CASHOUT":
            number_of_ticket = len(lottery)
            expected_amount = int(number_of_ticket) * int(amount)

            data = {
                "status": "Accepted",
                "game_id": get_game_play_id,
                "game_type": lotto_type,
                "stake_per_pick": 100,
                "total_stake": number_of_ticket * 100,
                "total_ticket": number_of_ticket,
                "tickets": [],
            }

            for lottery_number in lottery:
                create_obj = LottoTicket.objects.create(
                    user_profile=user_instance,
                    batch=get_current_batch,
                    phone=phone,
                    stake_amount=int(amount),
                    expected_amount=int(expected_amount),
                    is_agent=True,
                    number_of_ticket=number_of_ticket,
                    channel="MOBILE",
                    game_play_id=get_game_play_id,
                    lottery_type=lotto_type,
                    ticket=serialize_ticket(lottery_number.get("ticket")),
                    identity_id=identity_id,
                )

                data["tickets"].append({"id": create_obj.id, "ticket": lottery_number.get("ticket")})

            return data


def fetch_paid_lottery(game_play_id, agent_instance, run_time=5):
    lotto_ticket = LottoTicket.objects.filter(game_play_id=game_play_id, paid=True)

    run_time_db = {game_play_id: run_time}

    print("run_time", run_time)

    if run_time <= 0:
        return {"status": "error", "message": "No ticket found"}

    try:
        if not lotto_ticket:
            raise LottoTicket.DoesNotExist

        else:
            lotto_ticket_instance = lotto_ticket.last()

            data = {
                "status": "Accepted",
                "game_id": lotto_ticket_instance.game_play_id,
                "game_type": lotto_ticket_instance.lottery_type,
                "stake_per_pick": 100,
                "total_stake": lotto_ticket.count() * 100,
                "total_ticket": lotto_ticket.count(),
                "tickets": [],
            }

            for ticket in lotto_ticket:
                data["tickets"].append({"id": ticket.id, "ticket": ticket.ticket})

            return data

    except LottoTicket.DoesNotExist:
        _lottery_model_qs = LotteryModel.objects.filter(game_play_id=game_play_id, paid=True)

        if not _lottery_model_qs:
            run_time_db[game_play_id] -= 1
            sleep(6)
            return fetch_paid_lottery(game_play_id, agent_instance, run_time - 1)

        created_lottery = {
            "100": [],
            "200": [],
            "500": [],
            "1000": [],
        }

        for lottery in _lottery_model_qs:
            created_lottery[str(lottery.stake_amount).replace(".0", "")].append(
                {
                    "stake_amount": lottery.stake_amount,
                    "lucky_number": lottery.lucky_number,
                    "consent": True,
                    "band": lottery.band,
                    "id": lottery.id,
                    "get_game_play_id": lottery.game_play_id,
                }
            )

        data = PosLottoHelper.serilaize_WYSE_CASH_data(created_lottery, agent_instance.phone, agent_instance)

        return data

    except Exception as e:
        print(e)
