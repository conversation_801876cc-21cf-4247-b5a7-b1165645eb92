from django.urls import path

from .views import (
    AgentCreditCardView,
    FetchPaidMobileLottery,
    MobileAgentDetails,
    MobileAgentPayout,
    MobileAgentPayoutBeneficiary,
    MobileGameHistory,
    MobileLott0View,
    MobileLottoPaymentView,
    MobileLottoWalletCardFunding,
    MobileWhyseCashLottery,
)

urlpatterns = [
    path("agent_details/", MobileAgentDetails.as_view()),
    path("lottery/", MobileLott0View.as_view()),
    path("lottery_payment/", MobileLottoPaymentView.as_view()),
    path("get_paid_lottery/", FetchPaidMobileLottery.as_view()),
    path("user_card_list/", AgentCreditCardView.as_view()),
    path("fund_wallet_via_paystack/", MobileLottoWalletCardFunding.as_view()),
    path("WYSE_CASH/", MobileWhyseCashLottery.as_view()),
    path("game_history/", MobileGameHistory.as_view()),
    path("payout/", MobileAgentPayout.as_view()),
    path("beneficiary/", MobileAgentPayoutBeneficiary.as_view()),
]
