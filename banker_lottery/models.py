import uuid
from datetime import datetime

from django.db import models

from main.api.api_lottery_helpers import generate_game_play_id, generate_pin
from main.models import BankerConstant, LottoTicket


class SureBanker(models.Model):  # on hold; using lotto ticket for ticket registration
    @classmethod
    def current_batch(cls):
        from main.models import LotteryBatch, LotteryGlobalJackPot

        _batch = LotteryBatch.objects.filter(is_active=True, lottery_type="BANKER").last()
        if _batch:
            return _batch
        else:
            global_jackpot = LotteryGlobalJackPot.get_jackpot_instance()
            return LotteryBatch.objects.create(lottery_type="BANKER", global_jackpot=global_jackpot)

    class Meta:
        verbose_name = "SURE BANKER"
        verbose_name_plural = "SURE BANKER"

    @classmethod
    def create_payment_helper_object_and_event(cls, user_profile, total_lottery_stake_amount, game_play_id, channel) -> None:
        from wyse_ussd.models import UssdLotteryPayment

        UssdLotteryPayment.objects.create(
            user=user_profile,
            amount=total_lottery_stake_amount,
            game_play_id=game_play_id,
            channel=channel,
            lottery_type="BANKER",
        )
        engage_event_payload = {
            "event": "BANKER",
            "properties": {
                "game_id": game_play_id,
            },
        }

        from main.tasks import lottery_play_engange_event

        lottery_play_engange_event.delay(
            user_id=user_profile.id,
            is_user_profile_id=True,
            **engage_event_payload,
        )

    @classmethod
    def ticket_registration_and_payment(cls, games, user_profile, agent_profile, phone, channel, pin=None, play_wallet=False):
        for game in games["games"]:
            game_lines_selected = game["lines"]
            game_play_id = generate_game_play_id()
            ticket_pin = generate_pin()
            identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"
            line_count = len(game_lines_selected)
            total_lottery_stake_amount = game["stake_amount"]

            # split stake amount according to the count of game selected
            stake_amount = total_lottery_stake_amount / line_count

            # update potential winning by admin multiplier using the heights multiplier provided
            const_multipliers = BankerConstant.get_multiplier()
            potential_winning = stake_amount * max(const_multipliers)

            for line_index, line in enumerate(game_lines_selected):
                ticket = ",".join(map(str, map(int, line["ticket"])))

                ticket_ins = LottoTicket.objects.create(
                    user_profile_id=user_profile.id,
                    agent_profile=agent_profile,
                    batch=cls.current_batch(),
                    phone=phone,
                    stake_amount=stake_amount,
                    expected_amount=stake_amount,
                    potential_winning=potential_winning,
                    channel=channel,
                    game_play_id=game_play_id,
                    lottery_type="BANKER",
                    number_of_ticket=line_count,
                    ticket=ticket,
                    pin=ticket_pin,
                    identity_id=identity_id,
                )
                # game_play_ids_generated.append(game_play_id)
                game_lines_selected[line_index]["id"] = ticket_ins.id

            cls.create_payment_helper_object_and_event(user_profile, total_lottery_stake_amount, game_play_id, channel)

            # update response data sent to frontend after game registration and payment

            game["ticket_owner"] = phone
            game["game_id"] = game_play_id
            game["game_type"] = "BANKER"
            game["stake_per_pick"] = stake_amount
            game["total_stake"] = total_lottery_stake_amount
            game["potential_winning"] = potential_winning
            game["total_ticket"] = line_count
            game["pin"] = ticket_pin
            game["status"] = "Accepted"

            # Agent payment method
            if channel == "POS_AGENT":
                from pos_app.pos_helpers import PosAgentHelper

                lottery_ticket_qs = LottoTicket.objects.filter(
                    game_play_id=game_play_id,
                    paid=False,
                    agent_profile=agent_profile,
                    is_duplicate=False,
                )

                pos_agent_helper = PosAgentHelper(
                    agent_instance=agent_profile,
                    amount=total_lottery_stake_amount,
                    pin=pin,
                )

                response = pos_agent_helper.handle_agent_charge_and_lottery_play(
                    lottery_ticket_qs, instant_cashout_game=False, return_after_successfull_charge=True, _game_play_id=game_play_id
                )

                if isinstance(response, dict):
                    if response.get("status") != "success":
                        game["status"] = "Error"
                        game["paid"] = False
                        return {
                            "status": "Error",
                            "paid": False,
                            "message": response.get("message"),
                        }
                    else:
                        print("len of lottery_ticket_qs before after"), len(lottery_ticket_qs)

                else:
                    if response == "success":
                        for ticket in lottery_ticket_qs:
                            ticket.paid = True
                            ticket.amount_paid = stake_amount
                            try:
                                ticket.save()
                            except Exception:
                                try:
                                    LottoTicket.objects.filter(id=ticket.id).update(paid=True, amount_paid=stake_amount)
                                except Exception:
                                    pass

                        game["status"] = "Accepted"
                        game["paid"] = True
                        continue
                    else:
                        return {
                            "status": "Error",
                            "paid": False,
                            "message": response.get("message"),
                        }

            # WEB payment method
            elif channel == "WEB":
                from web_app.helper.payment import WebPayment

                # print("@ - web Payment")
                payment = WebPayment(player=user_profile, amount=total_lottery_stake_amount, game_play_id=game_play_id)
                if play_wallet is True:
                    payment.play_wallet(transfrom="BANKER_GAME_PLAY")
                elif play_wallet is False:
                    payment.referral_wallet()

                game["status"] = "Accepted"
                game["paid"] = True

                continue

        if channel == "POS_AGENT":
            final_response = {"status": "Accepted", "agent_id": agent_profile.id, "games": games["games"]}

        else:
            final_response = {"status": "Accepted", "games": games["games"]}
        return final_response
