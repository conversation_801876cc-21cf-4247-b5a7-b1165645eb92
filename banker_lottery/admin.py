# from django.contrib import admin
# from import_export.admin import ImportExportModelAdmin
# from .resource import *
#
#
# class BankerConstantResourceAdmin(ImportExportModelAdmin):
#     resource_class = BankerConstant
#
#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]
#
#
# class SureBankerResourceAdmin(ImportExportModelAdmin):
#     resource_class = SureBanker
#
#     def get_list_display(self, request):
#         data = [field.name for field in self.model._meta.concrete_fields]
#         # data.remove("")
#         return data
#
#
# admin.site.register(BankerConstant, BankerConstantResourceAdmin)
# admin.site.register(SureBanker, SureBankerResourceAdmin)
