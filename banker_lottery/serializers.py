from rest_framework import serializers
from rest_framework.exceptions import APIException
from main.models import BankerConstant
from overide_print import print

from rest_framework import serializers, status

class CustomValidationError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "Bad request"

    def __init__(self, detail=None, status_code=None):
        self.detail = {"message": detail}
        if status_code is not None:
            self.status_code = status_code



class BankerPosSerializer(serializers.Serializer):
    # pin = serializers.CharField(max_length=4, min_length=4)
    phone_number = serializers.CharField(max_length=13, min_length=11, allow_null=True, allow_blank=True)
    games = serializers.ListField(child=serializers.JSONField())

    def validate(self, data):
        games = data["games"]
        banker_minimum_stake = BankerConstant.get_banker_minimum_stake()
        for game in games:
            print(type(game["stake_amount"]))
            if type(game["stake_amount"]) is not int:
                raise serializers.ValidationError({"stake_amount": "please enter a positive number"})

            if game["stake_amount"] < banker_minimum_stake:
                raise serializers.ValidationError({"stake_amount": f"please enter a amount greater than or equals {banker_minimum_stake}"})

            # print(len(game["lines"][0]))
            # if len(game["lines"]) != 5:
            #     raise serializers.ValidationError({"lines": "please enter 5 valid numbers"})

            game_lines_selected = game["lines"]
            for line_index, line in enumerate(game_lines_selected):
                ticket = ",".join(map(str, map(int, line["ticket"])))
                if len(line["ticket"]) != 5:
                    raise CustomValidationError("please enter 5 valid numbers")
                
                if len(line["ticket"]) != len(set(line["ticket"])):
                    raise CustomValidationError("please enter unique numbers")
                



        return data


class BankerWebSerializer(serializers.Serializer):
    from_referral_wallet = serializers.BooleanField()
    paystack = serializers.BooleanField()
    from_play_wallet = serializers.BooleanField()
    games = serializers.ListField(child=serializers.JSONField())

    def validate(self, data):
        games = data["games"]
        for game in games:
            # print(type(game["stake_amount"]))
            if type(game["stake_amount"]) is not int:
                raise serializers.ValidationError({"stake_amount": "please enter a positive number"})

            # print(len(game["lines"][0]))
            # if len(game["lines"]) != 5:
            #     raise serializers.ValidationError({"lines": "please enter 5 valid numbers"})

        return data
