from datetime import datetime

from django.db.models import Q
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from account.authentication import (
    IsAgentRemittanceDuePermission,
    IsAgentSuspendedPermission,
    IsBlackListedPermission,
    PhoneNumberVerifedPermission,
)
from banker_lottery.helpers.total_stake import get_total_stake_amount
from banker_lottery.models import SureBanker
from banker_lottery.serializers import BankerPosSerializer, BankerWebSerializer
from main.models import LotteryBatch, LotteryModel, UserProfile
from main.ussd.helpers import Utility
from pos_app.models import Agent, LottoAgentRemittanceTable
from pos_app.pos_helpers import (
    CustomPaginator,
    LandingPageResultPaginator,
    salary_for_life_lotto_data_sort_by_date,
)
from pos_app.serializers import LotteryBatchSerializer
from referral_system.models import ReferralWallet
from wallet_app.models import UserWallet
from web_app.ip_whitelisting import ip_whitelist_middleware
from web_app.serializers import BankerResultSerializer, PosBankerResultSerializer


class POSBankerApiView(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def post(self, request, instance=None):
        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        serializer = BankerPosSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # pin = serializer.data["pin"]
        data = serializer.data
        total_stake_amount = get_total_stake_amount(data=data)

        # validate_remittance_amount
        response = LottoAgentRemittanceTable().valuate_amount_to_be_added(amount=total_stake_amount, agent=agent_instance)
        if response.get("is_amount_much"):
            response = {
                "message": f"Hi, kindly top up your lottowallet with this amount {Utility.currency_formatter(response.get('amount'))} to continue game play"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        phone = serializer.data["phone_number"]

        # ------------------------------ VALIDATE AGENT LIBERTY PAY TRANSACTION PIN ------------------------------ #

        # pos_agent_helper = PosAgentHelper(agent_instance=agent_instance, amount=0)

        # if pos_agent_helper.verify_agent_transaction_pin(pin) is False:
        #     data = {
        #         "message": "Invalid transaction pin",
        #     }

        #     return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # ------------------------------ END VALIDATE AGENT LIBERTY PAY TRANSACTION PIN ------------------------------ #

        if phone == "" or phone is None:
            phone = agent_instance.phone

        serializer.data["phone_number"] = agent_instance.phone

        phone_no = LotteryModel.format_number_from_back_add_234(phone)

        # if AgentConstantVariables().get_phone_number_is_required() is True:

        # if agent_instance.phone == phone_no:

        #     response = {"message": "You cannot use your own phone number"}
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # if phone is None or phone == "":
        #     phone = _agent_instance.phone

        # serializer.data["phone_number"] = phone

        # get or create user profile
        user_instance = UserProfile.create_user_profile_if_none_exist(phone_no=phone_no, channel="POS")

        ticket_reg_and_payment = SureBanker.ticket_registration_and_payment(
            games=data, user_profile=user_instance, agent_profile=agent_instance, phone=phone_no, channel="POS_AGENT", pin=None
        )

        if ticket_reg_and_payment.get("status") == "Error":
            return Response(data={"message": ticket_reg_and_payment.get("message")}, status=status.HTTP_400_BAD_REQUEST)

        # print("Ticket registration and payment --------------------------->", ticket_reg_and_payment)
        return Response(ticket_reg_and_payment, status=status.HTTP_200_OK)


class WEBBankerApiView(APIView):
    authentication_classes = [
        TokenAuthentication,
    ]
    permission_classes = [
        IsAuthenticated,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
    ]

    @method_decorator(csrf_exempt)
    @method_decorator(ip_whitelist_middleware())
    def post(self, request, instance=None):
        player = UserProfile.objects.filter(Q(email=request.user.email) | Q(phone_number=request.user.phone)).last()

        if player is None:
            return Response(
                data={"message": "User profile does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if player.has_sudo_phone_number is True:
            data = {
                "message": "Please update your phone number to continue",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        serializer = BankerWebSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        from_referral_wallet = serializer.data["from_referral_wallet"]
        serializer.data["paystack"]
        from_play_wallet = serializer.data["from_play_wallet"]
        data = serializer.data

        total_stake_amount = get_total_stake_amount(data=data)

        # print("TOTAL STAKE AMOUNT ------------------------>", total_stake_amount)

        if from_referral_wallet is True:  # Pay using referral wallet
            lottery_wallet = ReferralWallet.objects.filter(user=player).last()

            if lottery_wallet is None:
                return Response(
                    data={"message": "Referral wallet not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            if lottery_wallet.can_use_wallet is False:
                data = {"message": "You cannot use referral wallet yet"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if lottery_wallet.available_balance < total_stake_amount:
                data = {"message": "Insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if from_play_wallet is True:  # pay using play wallet
            main_wallet = UserWallet.objects.filter(user=player, wallet_tag="WEB").last()
            if main_wallet is None:
                UserWallet.objects.create(user=player, wallet_tag="WEB")
                data = {"message": "insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if main_wallet.game_available_balance < total_stake_amount:
                data = {"message": "insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        ticket_reg_and_payment = SureBanker.ticket_registration_and_payment(
            games=data, user_profile=player, agent_profile=None, phone=player.phone_number, channel="WEB", play_wallet=from_play_wallet
        )
        return Response(ticket_reg_and_payment, status=status.HTTP_200_OK)


class WebBankerGameResult(APIView):
    # @method_decorator(csrf_exempt)
    # @method_decorator(ip_whitelist_middleware())
    def get(self, request):
        page = request.GET.get("page", 1)
        salary_live_qs = (
            LotteryBatch.objects.filter(lottery_type="BANKER")
            .exclude(draw_date__isnull=True)
            .exclude(lottery_winner_ticket_number__isnull=True)
            .exclude(lottery_winner_ticket_number__exact="")
            .values_list("draw_date__date", flat=True)
            .distinct()
            .order_by("-draw_date__date")
        )
        salary_live_ticket = LandingPageResultPaginator.paginate(request, salary_live_qs, page)
        serializer = BankerResultSerializer(salary_live_ticket, many=True)
        data = {
            "data_type": "BANKER",
            "data": serializer.data,
            "total_page": salary_live_ticket.paginator.num_pages,
            "page_count": len(serializer.data),
            "total_data_count": salary_live_ticket.paginator.count,
        }
        return Response(data, status=status.HTTP_200_OK)


class PosBankerGameResult(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        page = request.GET.get("page", 1)

        filter_date = request.GET.get("date", None)

        agent = Agent.objects.filter(email=request.user.email).last()
        if agent is None:
            agent = Agent.objects.filter(phone=request.user.phone).last()

            if agent is None:
                data = {"message": "Agent not found"}
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        if filter_date is not None:
            try:
                filter_date = datetime.strptime(filter_date, "%Y-%m-%d").date()

            except Exception:
                data = {"message": "Invalid date format"}
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            lottery_batch_qs = LotteryBatch.objects.filter(
                is_active=False,
                created_date__date=filter_date,
                lottery_type="BANKER",
            ).order_by("-id")

            paginate_all_salary_for_life_games = CustomPaginator.paginate(request, lottery_batch_qs, page)

            instant_cash_serializer = LotteryBatchSerializer(paginate_all_salary_for_life_games, many=True)

            data = salary_for_life_lotto_data_sort_by_date(instant_cash_serializer)

            return Response(data=data, status=status.HTTP_200_OK)

        # LottoTicket().get_agent_sal_4_life_lottery_batch_db_ids(agent.id)

        lottery_batch_qs = LotteryBatch.objects.filter(Q(is_active=False), lottery_type="BANKER").order_by("-id")

        paginate_all_banker_games = CustomPaginator.paginate(request, lottery_batch_qs, page)

        lottery_batch_serialize_data = LotteryBatchSerializer(paginate_all_banker_games, many=True)

        data = salary_for_life_lotto_data_sort_by_date(lottery_batch_serialize_data)

        data = data[0]

        # new_data = []

        # for item in data:
        #     date = item["date"]
        #     values = item["value"]
        #     ticket_number = values["ticket_number"]

        #     if len(ticket_number) > 0:
        #         values["ticket_number"] = ticket_number[0]

        #     new_data.append({"date": date, "values": values})

        return Response(data=data, status=status.HTTP_200_OK)


class NewPosBankerGameResult(APIView):
    permission_classes = []
    permission_classes = []

    def get(self, request):
        page = request.GET.get("page", 1)
        salary_live_qs = (
            LotteryBatch.objects.filter(lottery_type="BANKER")
            .exclude(draw_date__isnull=True)
            .exclude(lottery_winner_ticket_number__isnull=True)
            .exclude(lottery_winner_ticket_number__exact="")
            .values_list("draw_date__date", flat=True)
            .distinct()
            .order_by("-draw_date__date")
        )
        salary_live_ticket = LandingPageResultPaginator.paginate(request, salary_live_qs, page)
        serializer = PosBankerResultSerializer(salary_live_ticket, many=True)
        data = serializer.data
        try:
            data = data[0]
        except Exception:
            data = data
        return Response(data, status=status.HTTP_200_OK)
