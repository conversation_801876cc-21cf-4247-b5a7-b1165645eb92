from django.contrib.auth import get_user_model
from django.db.models.signals import post_save
from django.dispatch import receiver

from banker_lottery.models import SureBanker

User = get_user_model()


@receiver(post_save, sender=SureBanker)
def update_wallet_status(sender, instance, created, **kwargs):
    if created:
        # # update potential winning by admin multiplier using the heights multiplier provided
        # const_multipliers = BankerConstant.get_multiplier()
        # instance.potential_winning = instance.stake_amount * max(const_multipliers)
        # instance.save()
        pass

    else:
        pass
