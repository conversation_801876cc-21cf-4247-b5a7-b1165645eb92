from django.db import models


# Create your models here.
class Games(models.Model):
    LOTTERY_TYPE = (
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("Q<PERSON><PERSON>", "<PERSON>UIKA"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("BANKER", "BANKER"),
        ("WYSE_CASH", "WYSE_CASH"),
    )
    name = models.Char<PERSON><PERSON>(max_length=300, choices=LOTTERY_TYPE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GAME"
        verbose_name_plural = "GAMES"

    def __str__(self):
        return self.name


INSTANT_CASHOUT_DEFUALT_PRICE = {
    "POS/MOBILE": {
        1: {
            "ticket_price": 200,
            "potential_winning": 11250,
            "line_number": 1,
            "total_amount": 200,
        },
        2: {
            "ticket_price": 500,
            "potential_winning": 15200,
            "line_number": 2,
            "total_amount": 500,
        },
        3: {
            "ticket_price": 750,
            "potential_winning": 15750,
            "line_number": 3,
            "total_amount": 750,
        },
        4: {
            "ticket_price": 800,
            "potential_winning": 18000,
            "line_number": 4,
            "total_amount": 800,
        },
        5: {
            "ticket_price": 1250,
            "potential_winning": 18750,
            "line_number": 5,
            "total_amount": 1250,
        },
        6: {
            "ticket_price": 1300,
            "potential_winning": 25000,
            "line_number": 6,
            "total_amount": 1300,
        },
        7: {
            "ticket_price": 1400,
            "potential_winning": 27000,
            "line_number": 7,
            "total_amount": 1400,
        },
    },
    "WEB": {
        1: {
            "ticket_price": 200,
            "potential_winning": 11250,
            "line_number": 1,
            "illusion_price": 100,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 400,
        },
        2: {
            "ticket_price": 500,
            "potential_winning": 15200,
            "line_number": 2,
            "illusion_price": 100,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 700,
        },
        3: {
            "ticket_price": 750,
            "potential_winning": 15750,
            "line_number": 3,
            "illusion_price": 100,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 950,
        },
        4: {
            "ticket_price": 800,
            "potential_winning": 18000,
            "line_number": 4,
            "illusion_price": 400,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1300,
        },
        5: {
            "ticket_price": 1250,
            "potential_winning": 18750,
            "line_number": 5,
            "illusion_price": 200,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1550,
        },
        6: {
            "ticket_price": 1300,
            "potential_winning": 25000,
            "line_number": 6,
            "illusion_price": 200,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1600,
        },
        7: {
            "ticket_price": 1400,
            "potential_winning": 27000,
            "line_number": 7,
            "illusion_price": 300,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1800,
        },
    },
    "USSD": {
        1: {
            "ticket_price": 200,
            "potential_winning": 11250,
            "line_number": 1,
            "illusion_price": 100,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 400,
        },
        2: {
            "ticket_price": 500,
            "potential_winning": 15200,
            "line_number": 2,
            "illusion_price": 100,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 700,
        },
        3: {
            "ticket_price": 750,
            "potential_winning": 15750,
            "line_number": 3,
            "illusion_price": 100,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 950,
        },
        4: {
            "ticket_price": 800,
            "potential_winning": 18000,
            "line_number": 4,
            "illusion_price": 400,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1300,
        },
        5: {
            "ticket_price": 1250,
            "potential_winning": 18750,
            "line_number": 5,
            "illusion_price": 200,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1550,
        },
        6: {
            "ticket_price": 1300,
            "potential_winning": 25000,
            "line_number": 6,
            "illusion_price": 200,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1600,
        },
        7: {
            "ticket_price": 1400,
            "potential_winning": 27000,
            "line_number": 7,
            "illusion_price": 300,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1800,
        },
    },
}


SALARY_FOR_LIFE_DEFUALT_PRICE = {
    "POS/MOBILE": {
        1: {
            "ticket_price": 150,
            "potential_winning": 5000,
            "line_number": 1,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 150,
        },
        2: {
            "ticket_price": 350,
            "potential_winning": 15000,
            "line_number": 2,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 350,
        },
        3: {
            "ticket_price": 700,
            "potential_winning": 50000,
            "line_number": 3,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 700,
        },
        4: {
            "ticket_price": 1400,
            "potential_winning": 150000,
            "line_number": 4,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 1400,
        },
        5: {
            "ticket_price": 2030,
            "potential_winning": 250000,
            "line_number": 5,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 2030,
        },
        6: {
            "ticket_price": 2800,
            "potential_winning": 500000,
            "line_number": 6,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 2800,
        },
        7: {
            "ticket_price": 3300,
            "potential_winning": 750000,
            "line_number": 7,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 3300,
        },
        8: {
            "ticket_price": 3700,
            "potential_winning": 900000,
            "line_number": 8,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 3700,
        },
        9: {
            "ticket_price": 4200,
            "potential_winning": 1250000,
            "line_number": 9,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 4200,
        },
        10: {
            "ticket_price": 5800,
            "potential_winning": 1500000,
            "line_number": 10,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 5800,
        },
    },
    "WEB": {
        1: {
            "ticket_price": 150,
            "potential_winning": 5000,
            "line_number": 1,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 250,
        },
        2: {
            "ticket_price": 350,
            "potential_winning": 15000,
            "line_number": 2,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 450,
        },
        3: {
            "ticket_price": 700,
            "potential_winning": 50000,
            "line_number": 3,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 800,
        },
        4: {
            "ticket_price": 1400,
            "potential_winning": 150000,
            "line_number": 4,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1500,
        },
        5: {
            "ticket_price": 2030,
            "potential_winning": 250000,
            "line_number": 5,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 2130,
        },
        6: {
            "ticket_price": 2800,
            "potential_winning": 500000,
            "line_number": 6,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 2900,
        },
        7: {
            "ticket_price": 3300,
            "potential_winning": 750000,
            "line_number": 7,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 3400,
        },
        8: {
            "ticket_price": 3700,
            "potential_winning": 900000,
            "line_number": 8,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 3800,
        },
        9: {
            "ticket_price": 4200,
            "potential_winning": 1250000,
            "line_number": 9,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 4300,
        },
        10: {
            "ticket_price": 5800,
            "potential_winning": 1500000,
            "line_number": 10,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 5900,
        },
    },
    "USSD": {
        1: {
            "ticket_price": 150,
            "potential_winning": 5000,
            "line_number": 1,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 250,
        },
        2: {
            "ticket_price": 350,
            "potential_winning": 15000,
            "line_number": 2,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 450,
        },
        3: {
            "ticket_price": 700,
            "potential_winning": 50000,
            "line_number": 3,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 800,
        },
        4: {
            "ticket_price": 1400,
            "potential_winning": 150000,
            "line_number": 4,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1500,
        },
        5: {
            "ticket_price": 2030,
            "potential_winning": 250000,
            "line_number": 5,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 2130,
        },
        6: {
            "ticket_price": 2800,
            "potential_winning": 500000,
            "line_number": 6,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 2900,
        },
        7: {
            "ticket_price": 3300,
            "potential_winning": 750000,
            "line_number": 7,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 3400,
        },
        8: {
            "ticket_price": 3700,
            "potential_winning": 900000,
            "line_number": 8,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 3800,
        },
        9: {
            "ticket_price": 4200,
            "potential_winning": 1250000,
            "line_number": 9,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 4300,
        },
        10: {
            "ticket_price": 5800,
            "potential_winning": 1500000,
            "line_number": 10,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 5900,
        },
    },
}


WYSE_CASH_DEFUALT_PRICE = {
    "POS/MOBILE": {
        10000: {
            1: {
                "stake_amount": 150 * 1,
                "total_winning_amount": 10000 * 1,
                "total_amount": 150 * 1,
            },
            2: {
                "stake_amount": 150 * 2,
                "total_winning_amount": 10000 * 2,
                "total_amount": 150 * 2,
            },
            3: {
                "stake_amount": 150 * 3,
                "total_winning_amount": 10000 * 3,
                "total_amount": 150 * 3,
            },
            4: {
                "stake_amount": 150 * 4,
                "total_winning_amount": 10000 * 4,
                "total_amount": 150 * 4,
            },
            5: {
                "stake_amount": 150 * 5,
                "total_winning_amount": 10000 * 5,
                "total_amount": 150 * 5,
            },
        },
        50000: {
            1: {
                "stake_amount": 250 * 1,
                "total_winning_amount": 50000 * 1,
                "total_amount": 250 * 1,
            },
            2: {
                "stake_amount": 250 * 2,
                "total_winning_amount": 50000 * 2,
                "total_amount": 250 * 2,
            },
            3: {
                "stake_amount": 250 * 3,
                "total_winning_amount": 50000 * 3,
                "total_amount": 250 * 3,
            },
            4: {
                "stake_amount": 250 * 4,
                "total_winning_amount": 50000 * 4,
                "total_amount": 250 * 4,
            },
            5: {
                "stake_amount": 250 * 5,
                "total_winning_amount": 50000 * 5,
                "total_amount": 250 * 5,
            },
        },
        100000: {
            1: {
                "stake_amount": 600 * 1,
                "total_winning_amount": 100000 * 1,
                "total_amount": 600 * 1,
            },
            2: {
                "stake_amount": 600 * 2,
                "total_winning_amount": 100000 * 2,
                "total_amount": 600 * 2,
            },
            3: {
                "stake_amount": 600 * 3,
                "total_winning_amount": 100000 * 3,
                "total_amount": 600 * 3,
            },
            4: {
                "stake_amount": 600 * 4,
                "total_winning_amount": 100000 * 4,
                "total_amount": 600 * 4,
            },
            5: {
                "stake_amount": 600 * 5,
                "total_winning_amount": 100000 * 5,
                "total_amount": 600 * 5,
            },
        },
        200000: {
            1: {
                "stake_amount": 1200 * 1,
                "total_winning_amount": 200000 * 1,
                "total_amount": 1200 * 1,
            },
            2: {
                "stake_amount": 1200 * 2,
                "total_winning_amount": 200000 * 2,
                "total_amount": 1200 * 2,
            },
            3: {
                "stake_amount": 1200 * 3,
                "total_winning_amount": 200000 * 3,
                "total_amount": 1200 * 3,
            },
            4: {
                "stake_amount": 1200 * 4,
                "total_winning_amount": 200000 * 4,
                "total_amount": 1200 * 4,
            },
            5: {
                "stake_amount": 1200 * 5,
                "total_winning_amount": 200000 * 5,
                "total_amount": 1200 * 5,
            },
        },
    },
    "WEB": {
        10000: {
            1: {
                "stake_amount": 150 * 1,
                "total_winning_amount": 10000 * 1,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (150 * 1) + 50 + 50,
            },
            2: {
                "stake_amount": 150 * 2,
                "total_winning_amount": 10000 * 2,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (150 * 2) + 50 + 50,
            },
            3: {
                "stake_amount": 150 * 3,
                "total_winning_amount": 10000 * 3,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (150 * 3) + 50 + 50,
            },
            4: {
                "stake_amount": 150 * 4,
                "total_winning_amount": 10000 * 4,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (150 * 4) + 50 + 50,
            },
            5: {
                "stake_amount": 150 * 5,
                "total_winning_amount": 10000 * 5,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (150 * 5) + 50 + 50,
            },
        },
        50000: {
            1: {
                "stake_amount": 250 * 1,
                "total_winning_amount": 50000 * 1,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (250 * 1) + 50 + 50,
            },
            2: {
                "stake_amount": 250 * 2,
                "total_winning_amount": 50000 * 2,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (250 * 2) + 50 + 50,
            },
            3: {
                "stake_amount": 250 * 3,
                "total_winning_amount": 50000 * 3,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (250 * 3) + 50 + 50,
            },
            4: {
                "stake_amount": 250 * 4,
                "total_winning_amount": 50000 * 4,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (250 * 4) + 50 + 50,
            },
            5: {
                "stake_amount": 250 * 5,
                "total_winning_amount": 50000 * 5,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (250 * 5) + 50 + 50,
            },
        },
        100000: {
            1: {
                "stake_amount": 600 * 1,
                "total_winning_amount": 100000 * 1,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (600 * 1) + 50 + 50,
            },
            2: {
                "stake_amount": 600 * 2,
                "total_winning_amount": 100000 * 2,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (600 * 2) + 50 + 50,
            },
            3: {
                "stake_amount": 600 * 3,
                "total_winning_amount": 100000 * 3,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (600 * 3) + 50 + 50,
            },
            4: {
                "stake_amount": 600 * 4,
                "total_winning_amount": 100000 * 4,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (600 * 4) + 50 + 50,
            },
            5: {
                "stake_amount": 600 * 5,
                "total_winning_amount": 100000 * 5,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (600 * 5) + 50 + 50,
            },
        },
        200000: {
            1: {
                "stake_amount": 1200 * 1,
                "total_winning_amount": 200000 * 1,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (1200 * 1) + 50 + 50,
            },
            2: {
                "stake_amount": 1200 * 2,
                "total_winning_amount": 200000 * 2,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (1200 * 2) + 50 + 50,
            },
            3: {
                "stake_amount": 1200 * 3,
                "total_winning_amount": 200000 * 3,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (1200 * 3) + 50 + 50,
            },
            4: {
                "stake_amount": 1200 * 4,
                "total_winning_amount": 200000 * 4,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (1200 * 4) + 50 + 50,
            },
            5: {
                "stake_amount": 1200 * 5,
                "total_winning_amount": 200000 * 5,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (1200 * 5) + 50 + 50,
            },
        },
    },
    "USSD": {
        10000: {
            1: {
                "stake_amount": 150 * 1,
                "total_winning_amount": 10000 * 1,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (150 * 1) + 50 + 50,
            },
            2: {
                "stake_amount": 150 * 2,
                "total_winning_amount": 10000 * 2,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (150 * 2) + 50 + 50,
            },
            3: {
                "stake_amount": 150 * 3,
                "total_winning_amount": 10000 * 3,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (150 * 3) + 50 + 50,
            },
            4: {
                "stake_amount": 150 * 4,
                "total_winning_amount": 10000 * 4,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (150 * 4) + 50 + 50,
            },
            5: {
                "stake_amount": 150 * 5,
                "total_winning_amount": 10000 * 5,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (150 * 5) + 50 + 50,
            },
        },
        50000: {
            1: {
                "stake_amount": 250 * 1,
                "total_winning_amount": 50000 * 1,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (250 * 1) + 50 + 50,
            },
            2: {
                "stake_amount": 250 * 2,
                "total_winning_amount": 50000 * 2,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (250 * 2) + 50 + 50,
            },
            3: {
                "stake_amount": 250 * 3,
                "total_winning_amount": 50000 * 3,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (250 * 3) + 50 + 50,
            },
            4: {
                "stake_amount": 250 * 4,
                "total_winning_amount": 50000 * 4,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (250 * 4) + 50 + 50,
            },
            5: {
                "stake_amount": 250 * 5,
                "total_winning_amount": 50000 * 5,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (250 * 5) + 50 + 50,
            },
        },
        100000: {
            1: {
                "stake_amount": 600 * 1,
                "total_winning_amount": 100000 * 1,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (600 * 1) + 50 + 50,
            },
            2: {
                "stake_amount": 600 * 2,
                "total_winning_amount": 100000 * 2,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (600 * 2) + 50 + 50,
            },
            3: {
                "stake_amount": 600 * 3,
                "total_winning_amount": 100000 * 3,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (600 * 3) + 50 + 50,
            },
            4: {
                "stake_amount": 600 * 4,
                "total_winning_amount": 100000 * 4,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (600 * 4) + 50 + 50,
            },
            5: {
                "stake_amount": 600 * 5,
                "total_winning_amount": 100000 * 5,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (600 * 5) + 50 + 50,
            },
        },
        200000: {
            1: {
                "stake_amount": 1200 * 1,
                "total_winning_amount": 200000 * 1,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (1200 * 1) + 50 + 50,
            },
            2: {
                "stake_amount": 1200 * 2,
                "total_winning_amount": 200000 * 2,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (1200 * 2) + 50 + 50,
            },
            3: {
                "stake_amount": 1200 * 3,
                "total_winning_amount": 200000 * 3,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (1200 * 3) + 50 + 50,
            },
            4: {
                "stake_amount": 1200 * 4,
                "total_winning_amount": 200000 * 4,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (1200 * 4) + 50 + 50,
            },
            5: {
                "stake_amount": 1200 * 5,
                "total_winning_amount": 200000 * 5,
                "woven_service_charge": 50,
                "africastalking_charge": 50,
                "total_amount": (1200 * 5) + 50 + 50,
            },
        },
    },
}


VIRTUAL_SOCCER_DEFAULT_PRICE = {
    "POS/MOBILE": {
        1: {
            "ticket_price": 200,
            "potential_winning": 11250,
            "line_number": 1,
            "illusion_price": 100,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 300,
        },
        2: {
            "ticket_price": 500,
            "potential_winning": 15200,
            "line_number": 2,
            "illusion_price": 100,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 600,
        },
        3: {
            "ticket_price": 800,
            "potential_winning": 18000,
            "line_number": 4,
            "illusion_price": 400,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 1200,
        },
    },
    "WEB": {
        1: {
            "ticket_price": 200,
            "potential_winning": 11250,
            "line_number": 1,
            "illusion_price": 100,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 400,
        },
        2: {
            "ticket_price": 500,
            "potential_winning": 15200,
            "line_number": 2,
            "illusion_price": 100,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 700,
        },
        3: {
            "ticket_price": 800,
            "potential_winning": 18000,
            "line_number": 4,
            "illusion_price": 400,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1300,
        },
    },
}

QUIKA_DEFAULT_PRICE = {
    "POS/MOBILE": {
        1: {
            "ticket_price": 200,
            "potential_winning": 11250,
            "line_number": 1,
            "ticket_line": 1,
            "illusion_price": 100,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 300,
        },
        2: {
            "ticket_price": 500,
            "potential_winning": 15200,
            "line_number": 2,
            "ticket_line": 1,
            "illusion_price": 100,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 600,
        },
        3: {
            "ticket_price": 800,
            "potential_winning": 18000,
            "line_number": 4,
            "ticket_line": 1,
            "illusion_price": 400,
            "woven_service_charge": 0,
            "africastalking_charge": 0,
            "total_amount": 1200,
        },
    },
    "WEB": {
        1: {
            "ticket_price": 200,
            "potential_winning": 11250,
            "line_number": 1,
            "ticket_line": 1,
            "illusion_price": 100,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 400,
        },
        2: {
            "ticket_price": 500,
            "potential_winning": 15200,
            "line_number": 2,
            "ticket_line": 1,
            "illusion_price": 100,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 700,
        },
        3: {
            "ticket_price": 800,
            "potential_winning": 18000,
            "line_number": 4,
            "ticket_line": 1,
            "illusion_price": 400,
            "woven_service_charge": 50,
            "africastalking_charge": 50,
            "total_amount": 1300,
        },
    },
}


class TicketPrice(models.Model):
    CHANNELS = (
        ("POS/MOBILE", "POS/MOBILE"),
        ("WEB", "WEB"),
        ("USSD", "USSD"),
    )
    game = models.ForeignKey(Games, on_delete=models.CASCADE)
    channel = models.CharField(max_length=300, choices=CHANNELS)
    price = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TICKET PRICE"
        verbose_name_plural = "TICKET PRICES"

    def save(self, *args, **kwargs):
        if not self.pk:
            if self.price is None or self.price == {}:
                if self.game.name == "INSTANT_CASHOUT":
                    self.price = INSTANT_CASHOUT_DEFUALT_PRICE[self.channel]
                elif self.game.name == "SALARY_FOR_LIFE":
                    self.price = SALARY_FOR_LIFE_DEFUALT_PRICE[self.channel]

                elif self.game.name == "WYSE_CASH":
                    self.price = WYSE_CASH_DEFUALT_PRICE[self.channel]

                elif self.game.name == "VIRTUAL_SOCCER":
                    self.price = VIRTUAL_SOCCER_DEFAULT_PRICE[self.channel]

                elif self.game.name == "QUIKA":
                    self.price = QUIKA_DEFAULT_PRICE[self.channel]

        return super(TicketPrice, self).save(*args, **kwargs)

    @classmethod
    def get_ticket_price(cls, game, channel):
        ticket_price = cls.objects.filter(game__name=game, channel=channel).first()
        if ticket_price:
            response = ticket_price.price
            new_data = {}

            print("response.items()", response.keys(), "\n\n\n\n\n")

            for key, value in response.items():
                if (key).isdigit():
                    new_data[int(key)] = value

                else:
                    new_data[key] = value

            # print(
            #     f"""
            # new_data: {new_data}
            # \n\n\n\n\n
            # """
            # )

            return new_data

        return None
