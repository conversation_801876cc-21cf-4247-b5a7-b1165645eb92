from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from ticket_price.models import Games, TicketPrice

# Register your models here.


class GamesResource(resources.ModelResource):
    class Meta:
        model = Games


class TicketPriceResource(resources.ModelResource):
    class Meta:
        model = TicketPrice


class GamesResourceAdmin(ImportExportModelAdmin):
    resource_class = GamesResource
    list_filter = (
        "name",
        "created_at",
    )
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TicketPriceResourceAdmin(ImportExportModelAdmin):
    resource_class = TicketPriceResource
    list_filter = (
        "channel",
        "created_at",
    )
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(Games, GamesResourceAdmin)
admin.site.register(TicketPrice, TicketPriceResourceAdmin)
