import json

import requests

url = "https://api.digitalocean.com/v2/droplets"


def create_digital_ocean_droplet(DIGITALOCEAN_KEY, GITHUB_TOKEN):
    payload = json.dumps(
        {
            "name": "LibertyLotto6",
            "region": "nyc1",
            "size": "c-60-intel",
            "image": "ubuntu-20-04-x64",
            "ssh_keys": [43244527],
            "backups": False,
            "ipv6": True,
            "monitoring": True,
            "user_data": f"""
    #cloud-config
    package_update: true
    package_upgrade: true

    # Install dependencies
    packages:
    - python3
    - python3-venv
    - python3-pip
    - git

    # Run commands after installation
    runcmd:
    # Clone the repository
    - git clone https://dtekluva:{GITHUB_TOKEN}@github.com/LibertytechX/LibertyLotto.git /home/<USER>

    # Navigate to the project directory and set up a virtual environment
    - mkdir /home/<USER>
    - cd /home/<USER>
    - python3 -m venv venv
    - source venv/bin/activate
    - pip install --upgrade pip
    - pip install -r requirements.txt

    # Configure Flask environment variables
    - echo "export FLASK_APP=flaskapp" >> /home/<USER>/venv/bin/activate
    - echo "export FLASK_ENV=production" >> /home/<USER>/venv/bin/activate

    # Create the systemd service for the Flask app
    - |
        cat <<EOF > /etc/systemd/system/flaskapp.service
        [Unit]
        Description=Flask Application Service
        After=network.target

        [Service]
        User=root
        WorkingDirectory=/home/<USER>
        ExecStart=python3 flask_app/s4l_draw.py
        Restart=always

        [Install]
        WantedBy=multi-user.target
        EOF

    # Reload systemd, enable, and start the Flask app service
    - systemctl daemon-reload
    - systemctl enable flaskapp.service
    - systemctl start flaskapp.service""",
        }
    )
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {DIGITALOCEAN_KEY}",
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    print(response.json())
    droplet_id = response.json()["droplet"]["id"]

    return droplet_id, response.json()


def check_droplet_ready(droplet_ip):
    import requests

    url = f"http://{droplet_ip}:3000/draw"

    payload = ""
    headers = {}

    try:
        response = requests.request("GET", url, headers=headers, data=payload, timeout=4)
        print(response.text)
    except Exception:
        return False

    if response.status_code == 405:
        return True
    else:
        return False


def fetch_draw_droplet_ip(DIGITALOCEAN_KEY, DROPLET_ID):
    import requests

    url = f"https://api.digitalocean.com/v2/droplets/{DROPLET_ID}"

    payload = {}
    headers = {
        "Authorization": f"Bearer {DIGITALOCEAN_KEY}",
    }

    response = requests.request("GET", url, headers=headers, data=payload)

    if response.status_code == 200:
        droplet = response.json().get("droplet", {})
        networks = droplet.get("networks", {})
        v4_ips = {net["type"]: net["ip_address"] for net in networks.get("v4", [])}
        return v4_ips
    elif response.status_code == 404:
        return {"error": "Droplet not found"}
    else:
        return {"error": f"Failed to fetch droplet details: {response.text}"}


def delete_droplet(DIGITALOCEAN_KEY, DROPLET_ID):
    import requests

    url = f"https://api.digitalocean.com/v2/droplets/{DROPLET_ID}"

    payload = {}
    headers = {
        "Authorization": f"Bearer {DIGITALOCEAN_KEY}",
    }

    response = requests.request("DELETE", url, headers=headers, data=payload)

    if response.status_code == 204:
        return True, response.json()
    else:
        return False, response.json()


# print(fetch_draw_droplet_ip(DIGITALOCEAN_KEY="***********************************************************************", DROPLET_ID="470635549"))
# print(create_digital_ocean_droplet(DIGITALOCEAN_KEY="***********************************************************************", GITHUB_TOKEN="****************************************"))
# print(check_droplet_ready("**************"))
