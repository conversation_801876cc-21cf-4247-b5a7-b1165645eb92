import datetime
import itertools
import multiprocessing
from collections import Counter
from pprint import pprint

from flask import Flask, request

app = Flask(__name__)

winnings_list = []
then = datetime.datetime.now()
contribution = 600000000
best_combo = []
best_combo_amount = 0
winners = []
combo_dict = {}


prices = {
    1: 5000.00 / 1 * 1,
    2: 15000.00 / 2 * 1,
    3: 50000.00 / 3 * 1,
    4: 150000.00 / 4 * 1,
    5: 250000.00 / 5 * 1,
    6: 500000.00 / 6 * 1,
    7: 750000.00 / 7 * 1,
    8: 900000.00 / 8 * 1,
    9: 1250000.00 / 9 * 1,
    10: 5000000.00 / 10 * 1,
}


plays = []


def search_number_occurences(numlist1: list, numlist2: list, number_of_match_limit=2) -> int:
    """COUNT COMMON NUMBERS INCLUDING MULTIPLE OCCURRENCES"""
    match_count = sum(numlist2[1].count(number) for number in numlist1)  # Count all occurrences of each number in the target list

    # if (16 in numlist2[1] and 17 in numlist2[1]) and (16 in numlist1 or 17 in numlist1):
    #     print(f"Match Count: {match_count}, Combo: {numlist1}, User Play: {numlist2}")
    return match_count if match_count > (number_of_match_limit - 1) else False


def filter_winnings(combo, plays, prices, jackpot_amount, print_=False):
    occurences = map(
        lambda user_play: search_number_occurences(combo, user_play, 2), plays
    )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

    play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
    over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)


    play_occurences_with_amount = map(
        lambda played: get_potential_winning(played, prices, jackpot_amount),
        over3ocurrences,
    )
    data = list(play_occurences_with_amount)

    if print_:
        print("Combo:", combo)
        pprint(data)
    return data


def get_potential_winning(data, prices, jackpot):
    """
    data[1][0] : number_of_lines
    data[0] : number_of_matches
    """

    base_price = prices[data[1][0]]
    sharing = {5: 1, 4: 1, 3: 0.6, 2: 0.6 * 0.5}

    if data[0] == 5:
        winning = jackpot

    else:
        winning = (base_price * sharing[data[0]]) if not sharing[data[0]] == 5 else jackpot

    response = [*data, int(winning)]

    return response


def play(target_set, winnings_list, plays):
    print(len(target_set))
    for index, combo in enumerate(target_set):
        winnings = filter_winnings(combo, plays, prices, 9999999999)

        check = index % 10000

        if check == 0 and index >= 10000:
            print(index)

        # print("Winnings>>", len(winnings))

        if len(winnings) == 0:
            t_amount = 0
            winnings_list.append([combo, t_amount])

        else:
            _, __, amount = zip(*winnings)
            t_amount = sum(amount)
            winnings_list.append([combo, t_amount])

        # if combo == (19, 20, 33, 37, 49):
        #     pprint.pprint(winnings)

def count_tickets(tickets):
    all_numbers = list(range(1,50))

    for ticket in tickets:
        all_numbers.extend(set(ticket[1]))

    number_counts = Counter(all_numbers)
    most_common = number_counts.most_common()[:10]
    least_common = number_counts.most_common()[-10:]
    mode_common = number_counts.most_common()[30:35]

    elements, counts = zip(*(mode_common+most_common+least_common))
    print("LEAST COMMON:::", least_common)

    return elements, counts


def find_closest_value(lst, target):
    lst.sort(key=lambda x: x[1])

    closest_combo = []
    closest_amount = -1
    target = target  #* 0.001 # Always give just 10% of winnings for now.
    print("Target:", target)

    for combo, amount in lst:
        if amount > closest_amount and target > amount and target > 0:
            closest_combo = combo
            closest_amount = amount

        if amount > target and target > 0:
            break
    else:
        print("No closest amount found")

    print("Combo:", combo, "Amount:", amount)
    if closest_amount == -1:
        closest_combo, closest_amount = lst[0][0], lst[0][1]

    print("Closest:", closest_combo, "Amount:", closest_amount)
    return closest_amount, closest_combo


def run_draw(plays, rtp, prices, jackpot_amount):
    then = datetime.datetime.now()
    step = 9000
    jobs = []

    least_tickets, _ = count_tickets(plays)

    random_combo = list(itertools.combinations(least_tickets, 5))
    manager = multiprocessing.Manager()
    winnings_list = manager.list()


    for i in range(0, 2000000, step):
        proccess = multiprocessing.Process(target=play, args=(random_combo[i : i + step], winnings_list, plays))
        jobs.append(proccess)
        proccess.start()

    for proc in jobs:
        proc.join()

    now = datetime.datetime.now()
    print((now - then).total_seconds())

    # winnings = list(winnings_list)
    # pprint.pprint(winnings)
    winnings = list(winnings_list)
    winning_combo = find_closest_value(winnings, rtp)

    # for val in winnings:

    #     print(val)

    print(winning_combo)
    print(rtp)

    if abs(winning_combo[0] - rtp) > rtp and winning_combo[0] > 0:

        return dict(
            best_match=0,
            best_match_combo=[],
            best_match_with_jkpt=[],
            best_match_with_jkpt_combo=[],
            best_match_witho_jkpt=[],
            best_match_witho_jkpt_combo=dict(
                            failing_match=winning_combo,
                            message="Closest amount is too far from target")
        )

    print(
        dict(
            best_match=winning_combo,
            best_match_combo=winning_combo[1],
            best_match_with_jkpt=[],
            best_match_with_jkpt_combo=[],
            best_match_witho_jkpt=[],
            best_match_witho_jkpt_combo=[],
        )
    )

    winning_combo_percent = 0
    try:
        winning_combo_percent = ((winning_combo[0] / rtp) * 100) if winning_combo[0] > 0 else 0
    except:
        pass

    response = dict(
        best_match= winning_combo_percent,
        best_match_combo=winning_combo[1],
        best_match_with_jkpt=[],
        best_match_with_jkpt_combo=[],
        best_match_witho_jkpt=[],
        best_match_witho_jkpt_combo=[],
    )

    return response


@app.route("/draw", methods=["POST"])
def run_s4l_draw():
    if request.method == "POST":
        data = request.json
        plays = data.get("plays")
        rtp = data.get("rtp")
        data.get("jackpot_amount")

        winnings = run_draw(plays=plays, rtp=rtp, prices=prices, jackpot_amount=9012100)

        if rtp < 1:
            rtp = 0

        print("WINNINGS:", winnings)
        pprint(filter_winnings(winnings["best_match_combo"], plays, prices, 0, True))

        return winnings

    else:
        return {}


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=3000, debug=True)
