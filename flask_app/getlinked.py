import os
import uuid
import time
import json
import random
import logging
import base64
import boto3
from flask import Flask, request,jsonify
import requests
from io import BytesIO
from concurrent.futures import Process<PERSON>oolExecutor
from PIL import Image
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('verification_service.log')
    ]
)
logger = logging.getLogger(__name__)
app = Flask(__name__)


def verify_faces(photo_id_url, profile_photo_url):
    """
    Verify faces in two images using Amazon Rekognition.

    Args:
        photo_id_url: URL to the ID document image
        profile_photo_url: URL to the profile photo image

    """
    # Environment variables
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    AWS_ACCESS_KEY_ID='********************'
    AWS_SECRET_ACCESS_KEY='RSu/MGU5RHAXC64a14/YeLU1z8PjzWLzdEfKNIkK'
    AWS_REGION = os.getenv('AWS_REGION', 'us-east-1')
    FACE_SIMILARITY_THRESHOLD = float(os.getenv('FACE_SIMILARITY_THRESHOLD', '55.0'))
    # Initialize result dictionary
    result = {
        'success': False,
        'document_type': 'Unknown',
        'similarity': 0,
        'error': None
    }

    try:
        # Log the start of verification process
        logger.info(f"Starting verification process for ID photo and profile photo")

        # Download and process ID image
        id_response = requests.get(photo_id_url, timeout=10)
        id_response.raise_for_status()
        id_image = Image.open(BytesIO(id_response.content))

        if id_image.mode == 'RGBA':
            id_image = id_image.convert('RGB')

        id_buffer = BytesIO()
        id_image.save(id_buffer, format="JPEG")
        base64_image = base64.b64encode(id_buffer.getvalue()).decode('utf-8')

        # Check document type using OpenAI
        if OPENAI_API_KEY:
            logger.info("Identifying document type with OpenAI")
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {OPENAI_API_KEY}"
            }

            # Construct the request payload
            payload = {
                "model": "gpt-4o-mini",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": """
                                        What document type of Nigerian ID is this? These are the valid options: (National ID, Driver’s License, Voter’s Card, International Passport, or Unknown).\n
                                        Carefully analyze the uploaded image and consider the following details to determine the ID type:\n

                                        1. National ID: Look for features like 'NIMC' logos, cardholder name, National Identification Number (NIN), and the issuing authority clearly labeled as 'National Identity Management Commission'.\n

                                        2. Driver’s License: Check for 'Federal Road Safety Commission' (FRSC) logos, driver's license number, expiration date, and other relevant details such as 'class of vehicle' and license holder's photograph.\n

                                        3. Voter’s Card: Verify the presence of 'Independent National Electoral Commission (INEC)' branding, a Voter’s Identification Number (VIN), polling unit number, and voter’s details such as name and address.\n

                                        4. International Passport: Look for a green cover (regular), blue cover (official), or red cover (diplomatic). Check for 'Federal Republic of Nigeria', the coat of arms, passport number, and holder's biographical details (name, date of birth, nationality, etc.).\n

                                        If the ID does not match any of the above categories or lacks sufficient clarity, classify it as 'Unknown'. \n

                                        Your response should ONLY be in a valid JSON format with this key: document_type. Ensure your response is only the json and no additional text or prefix and you MUST NOT wrap it within JSON md markers such as this ('```json).\n

                                        """
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 300
            }


            response = requests.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=15
            )

            if response.status_code == 200:
                response_data = response.json()
                content = response_data.get("choices", [{}])[0].get("message", {}).get("content", {})

                try:
                    content_json = json.loads(content)
                    document_type = content_json.get("document_type", "Unknown")
                    result['document_type'] = document_type
                    logger.info(f"Document type identified: {document_type}")
                except Exception as e:
                    logger.error(f"Error parsing OpenAI response: {str(e)}")
                    # Keep the default "Unknown"
                    pass
            else:
                logger.error(f"OpenAI API error: {response.status_code} - {response.text}")

        # Compare faces if document type is valid
        if result['document_type'] != "Unknown":
            logger.info("Document type is valid, proceeding with face comparison")

            # Download and process profile image
            profile_response = requests.get(profile_photo_url, timeout=10)
            profile_response.raise_for_status()
            profile_image = Image.open(BytesIO(profile_response.content))

            if profile_image.mode == 'RGBA':
                profile_image = profile_image.convert('RGB')

            # Prepare images for AWS Rekognition
            id_bytes_io = BytesIO()
            id_image.save(id_bytes_io, format='JPEG')
            id_bytes = id_bytes_io.getvalue()

            profile_bytes_io = BytesIO()
            profile_image.save(profile_bytes_io, format='JPEG')
            profile_bytes = profile_bytes_io.getvalue()

            # Use AWS Rekognition to compare faces
            if AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY:
                logger.info("Comparing faces with AWS Rekognition")
                rekognition_client = boto3.client(
                    "rekognition",
                    aws_access_key_id=AWS_ACCESS_KEY_ID,
                    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
                    region_name=AWS_REGION
                )

                rekognition_response = rekognition_client.compare_faces(
                    SourceImage={"Bytes": id_bytes},
                    TargetImage={"Bytes": profile_bytes},
                    SimilarityThreshold=FACE_SIMILARITY_THRESHOLD
                )

                # Extract similarity score
                matches = rekognition_response.get('FaceMatches', [])
                if matches:
                    similarity = matches[0]['Similarity']
                    result['similarity'] = similarity
                    result['success'] = similarity >= FACE_SIMILARITY_THRESHOLD
                    logger.info(f"Face comparison result: similarity={similarity}, success={result['success']}")
                else:
                    logger.info("No face matches found")
            else:
                logger.warning("AWS credentials not provided, skipping face comparison")
        else:
            logger.info("Document type is Unknown, skipping face comparison")

    except Exception as e:
        logger.error(f"Error in verification process: {str(e)}")
        result['error'] = str(e)

    # Return the final result
    return jsonify(result)


def bulk_verify_faces(bulk_data):
    """
    Verify faces in multiple images using Amazon Rekognition.

    Args:
        bulk_data: List of dictionaries containing photo_id_url and profile_photo_url
    """
    # Process each verification request in parallel
    with ProcessPoolExecutor() as executor:
        results = list(executor.map(verify_faces, bulk_data))

    return results


@app.route('/api/verify', methods=['POST'])
def verify_identity():
    """
    Verify identity by comparing a photo ID with a profile photo.

    Expects a JSON payload with:
    - photo_id_url: URL to the ID document image
    - profile_photo_url: URL to the profile photo image

    Returns:
    - JSON with verification results
    """
    # Get the request data from Flask request

    data = request.get_json()

    # Verify faces in the images
    return bulk_verify_faces(bulk_data=data)
if __name__ == '__main__':
    app.run(debug=True)