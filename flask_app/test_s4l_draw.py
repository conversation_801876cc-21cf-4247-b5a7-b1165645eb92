import unittest
from flask_app.s4l_draw import search_number_occurences, filter_winnings, play, prices  # Import prices from s4l_draw.py

class TestS4LDraw(unittest.TestCase):

    def test_search_number_occurences(self):
        # Test with multiple occurrences
        numlist1 = [8, 11, 12, 13, 17]
        numlist2 = [10, [17, 17, 17, 16, 16]]
        result = search_number_occurences(numlist1, numlist2, number_of_match_limit=1)
        self.assertEqual(result, 3, "Should count all occurrences of 17")

        # Test with no matches
        numlist1 = [1, 2, 3]
        numlist2 = [10, [4, 5, 6]]
        result = search_number_occurences(numlist1, numlist2, number_of_match_limit=1)
        self.assertFalse(result, "Should return False when no matches")

    def test_filter_winnings(self):
        # Test with a valid combination and multiple plays
        combo = [8, 11, 12, 13, 17]
        plays = [
            [10, [17, 17, 17, 16, 16]],  # 3 matches
            [5, [8, 11, 12, 13, 17]],    # 5 matches
            [3, [1, 2, 3, 4, 5]],        # 0 matches
            [7, [8, 11, 12, 13, 14]],    # 4 matches
        ]
        jackpot_amount = 1000000

        result = filter_winnings(combo, plays, prices, jackpot_amount)

        # Check the number of winners
        self.assertEqual(len(result), 3, "Should have 3 winners")

        # Check the winnings for each winner
        expected_winnings = [
            [3, [10, [17, 17, 17, 16, 16]], int(prices[10] * 0.6)],  # 3 matches
            [5, [5, [8, 11, 12, 13, 17]], jackpot_amount],           # 5 matches (jackpot)
            [4, [7, [8, 11, 12, 13, 14]], int(prices[7])],           # 4 matches
        ]

        for expected, actual in zip(expected_winnings, result):
            self.assertEqual(expected[0], actual[0], "Match count should be correct")
            self.assertEqual(expected[1], actual[1], "Play data should match")
            self.assertEqual(expected[2], actual[2], "Winning amount should be correct")

    def test_play_function(self):
        # Test play function with a small target set
        target_set = [(8, 11, 12, 13, 17), (1, 2, 3, 4, 5)]
        winnings_list = []
        plays = [[10, [17, 17, 17, 16, 16]]]
        play(target_set, winnings_list, plays)
        self.assertGreater(len(winnings_list), 0, "Winnings list should be populated")

    def test_search_number_occurences_edge_cases(self):
        # Test with empty lists
        numlist1 = []
        numlist2 = [10, []]
        result = search_number_occurences(numlist1, numlist2, number_of_match_limit=1)
        self.assertFalse(result, "Should return False for empty lists")

        # Test with no common numbers
        numlist1 = [1, 2, 3]
        numlist2 = [10, [4, 5, 6]]
        result = search_number_occurences(numlist1, numlist2, number_of_match_limit=1)
        self.assertFalse(result, "Should return False when no common numbers")

        # Test with all numbers matching
        numlist1 = [1, 2, 3]
        numlist2 = [10, [1, 2, 3]]
        result = search_number_occurences(numlist1, numlist2, number_of_match_limit=1)
        self.assertEqual(result, 3, "Should return the total count of matches")

    def test_filter_winnings_edge_cases(self):
        # Test with no plays
        combo = [8, 11, 12, 13, 17]
        plays = []
        jackpot_amount = 1000000
        result = filter_winnings(combo, plays, prices, jackpot_amount)  # Use imported prices
        self.assertEqual(result, [], "Should return an empty list when no plays")

        # Test with no matches
        combo = [8, 11, 12, 13, 17]
        plays = [[10, [1, 2, 3, 4, 5]]]
        result = filter_winnings(combo, plays, prices, jackpot_amount)  # Use imported prices
        self.assertEqual(result, [], "Should return an empty list when no matches")

    def test_play_function_edge_cases(self):
        # Test with an empty target set
        target_set = []
        winnings_list = []
        plays = [[10, [17, 17, 17, 16, 16]]]
        play(target_set, winnings_list, plays)
        self.assertEqual(len(winnings_list), 0, "Winnings list should remain empty for an empty target set")

        # Test with no plays
        target_set = [(8, 11, 12, 13, 17), (1, 2, 3, 4, 5)]
        winnings_list = []
        plays = []
        play(target_set, winnings_list, plays)
        self.assertEqual(len(winnings_list), len(target_set), "Winnings list should have entries for each target set combination")

if __name__ == "__main__":
    unittest.main()