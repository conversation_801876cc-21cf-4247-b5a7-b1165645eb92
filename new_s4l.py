# import datetime
# import itertools
# import random
# import pprint
# import multiprocessing
# from flask import Flask
# import json

# app = Flask(__name__)

# prices = {
#             1: 5000.00 / 1 * 1,
#             2: 15000.00 / 2 * 1,
#             3: 50000.00 / 3 * 1,
#             4: 150000.00 / 4 * 1,
#             5: 250000.00 / 5 * 1,
#             6: 500000.00 / 6 * 1,
#             7: 750000.00 / 7 * 1,
#             8: 900000.00  / 8 * 1,
#             9: 1250000.00 / 9 * 1,
#             10:5000000.00 /10 * 1,
#         }


# plays = [
# [1,[12,45,17,31,47]],
# [1,[3,12,8,36,38]],
# [1,[22,31,47,25,13]],
# [1,[12,26,17,49,31]],
# [1,[41,21,49,39,26]],
# [1,[31,2,27,29,8]],
# [1,[36,10,31,16,23]],
# [1,[2,34,21,7,41]],
# [1,[27,49,15,8,26]],
# [1,[49,46,36,16,8]],
# [1,[48,36,28,7,41]],
# [1,[49,16,42,6,11]],
# [1,[6,44,23,48,7]],
# [1,[32,14,49,33,37]],
# [1,[47,33,4,5,3]],
# [1,[46,12,6,2,36]],
# [1,[27,5,42,20,12]],
# [1,[47,33,19,5,40]],
# [1,[17,23,21,9,43]],
# [1,[7,35,44,39,16]],
# [1,[13,48,31,49,43]],
# [1,[22,12,3,14,17]],
# [1,[28,27,29,41,4]],
# [1,[19,9,49,6,24]],
# [1,[1,23,17,31,2]],
# [1,[36,40,12,38,28]],
# [1,[21,24,36,32,17]],
# [1,[16,36,27,42,30]],
# [1,[8,33,34,2,16]],
# [1,[25,36,42,15,9]],
# [1,[21,17,45,48,44]],
# [1,[7,18,33,11,22]],
# [1,[27,15,21,37,19]],
# [1,[28,11,6,7,4]],
# [1,[17,29,31,7,35]],
# [1,[35,34,36,10,15]],
# [1,[41,21,49,37,2]],
# [1,[7,24,49,6,12]],
# [1,[42,40,7,36,32]],
# [1,[15,24,22,44,32]],
# [1,[12,27,23,35,26]],
# [1,[24,14,42,11,19]],
# [1,[31,21,36,4,5]],
# [1,[8,35,21,18,23]],
# [1,[18,44,9,20,35]],
# [1,[11,9,4,35,38]],
# [1,[24,32,13,25,44]],
# [1,[18,25,23,8,5]],
# [1,[36,37,10,16,3]],
# [1,[49,12,39,45,16]],
# [1,[2,31,17,45,38]],
# [1,[17,36,33,12,24]],
# [1,[35,5,22,37,20]],
# [1,[45,26,15,18,43]],
# [1,[3,39,5,27,48]],
# [1,[3,24,41,35,4]],
# [1,[3,23,27,11,44]],
# [1,[18,47,30,36,3]],
# [1,[13,28,43,15,27]],
# [1,[31,32,21,8,5]],
# [1,[39,13,36,14,31]],
# [1,[20,35,12,33,48]],
# [1,[21,29,9,47,45]],
# [1,[30,19,12,15,39]],
# [1,[49,5,39,31,20]],
# [1,[21,5,8,2,48]],
# [1,[29,23,18,38,5]],
# [1,[9,30,21,14,17]],
# [1,[10,22,45,19,23]],
# [1,[3,28,19,9,23]],
# [1,[48,29,44,21,22]],
# [1,[42,20,10,2,5]],
# [1,[39,18,1,20,14]],
# [1,[17,41,14,10,49]],
# [1,[48,23,13,31,16]],
# [1,[17,7,13,40,24]],
# [1,[43,41,9,35,48]],
# [1,[1,11,28,19,6]],
# [1,[17,24,8,29,27]],
# [1,[42,31,9,15,48]],
# [1,[16,24,19,48,36]],
# [1,[35,42,10,46,1]],
# [1,[49,26,42,13,39]],
# [1,[29,36,20,4,5]],
# [1,[8,35,14,3,42]],
# [1,[31,47,6,20,38]],
# [1,[37,20,4,28,42]],
# [1,[36,15,45,32,19]],
# [1,[39,32,16,38,13]],
# [1,[44,9,25,27,42]],
# [1,[15,32,39,12,22]],
# [1,[33,49,16,20,35]],
# [1,[40,11,27,31,14]],
# [1,[19,20,35,5,30]],
# [1,[1,20,7,23,45]],
# [1,[31,35,5,42,26]],
# [1,[37,18,32,46,7]],
# [1,[13,40,33,7,25]],
# [1,[12,36,10,15,24]],
# [1,[22,19,42,29,5]],
# [1,[34,20,38,42,6]],
# [1,[22,31,7,46,41]],
# [1,[44,4,16,9,45]],
# [1,[48,36,46,12,1]],
# [1,[10,24,40,36,5]],
# [1,[19,18,27,21,41]],
# [1,[18,11,6,23,40]],
# [1,[25,18,31,32,46]],
# [1,[3,39,19,9,45]],
# [1,[43,2,8,26,3]],
# [1,[1,7,9,42,2]],
# [1,[48,17,10,32,44]],
# [1,[24,6,30,15,27]],
# [1,[31,15,43,21,45]],
# [1,[39,47,9,49,21]],
# [1,[4,42,27,6,39]],
# [1,[16,47,46,1,25]],
# [1,[44,41,4,26,25]],
# [1,[5,19,20,37,35]],
# [1,[39,18,31,13,36]],
# [1,[42,31,17,44,41]],
# [1,[36,46,42,6,35]],
# [1,[16,36,1,11,37]],
# [1,[8,48,20,44,5]],
# [1,[43,13,44,35,46]],
# [1,[22,11,36,31,37]],
# [1,[24,45,39,9,46]],
# [1,[45,23,41,31,42]],
# [1,[8,24,13,34,1]],
# [1,[42,31,5,12,9]],
# [1,[11,41,43,15,3]],
# [1,[27,11,31,35,39]],
# [1,[46,19,10,33,37]],
# [1,[39,49,17,47,45]],
# [1,[5,41,2,21,18]],
# [1,[17,18,43,3,1]],
# [1,[3,9,30,44,37]],
# [1,[16,47,28,35,36]],
# [1,[5,18,11,31,36]],
# [1,[27,28,10,7,23]],
# [1,[44,36,26,1,24]],
# [1,[49,4,19,35,31]],
# [1,[16,26,32,29,20]],
# [1,[42,18,25,8,32]],
# [1,[41,44,27,26,7]],
# [1,[46,47,8,2,1]],
# [1,[21,7,22,35,19]],
# [1,[48,19,37,7,20]],
# [1,[21,31,39,6,27]],
# [1,[42,18,46,15,23]],
# [1,[45,38,46,28,40]],
# [1,[33,43,13,2,10]],
# [1,[21,11,44,19,27]],
# [1,[38,9,40,39,47]],
# [1,[36,1,25,20,28]],
# [1,[49,43,27,21,19]],
# [1,[12,4,23,37,41]],
# [1,[16,12,47,20,19]],
# [1,[18,11,10,34,15]],
# [1,[14,6,45,16,28]],
# [1,[30,32,22,4,23]],
# [1,[46,41,30,44,13]],
# [1,[17,13,46,20,29]],
# [1,[1,23,9,13,30]],
# [1,[40,35,43,36,19]],
# [1,[23,38,28,45,4]],
# [1,[18,21,1,34,26]],
# [1,[36,25,29,28,47]],
# [1,[2,11,29,43,17]],
# [1,[48,46,41,12,30]],
# [1,[38,12,30,11,22]],
# [1,[37,1,17,49,8]],
# [1,[27,11,18,37,49]],
# [1,[8,18,13,16,34]],
# [1,[24,39,11,8,36]],
# [1,[14,37,7,6,8]],
# [1,[48,42,19,29,36]],
# [1,[32,2,8,12,24]],
# [1,[14,23,2,33,10]],
# [1,[6,41,14,33,4]],
# [1,[12,1,5,18,11]],
# [1,[2,44,3,14,13]],
# [1,[1,34,46,35,47]],
# [1,[46,23,35,48,9]],
# [1,[33,17,37,12,47]],
# [1,[2,7,33,43,40]],
# [1,[22,14,9,34,31]],
# [1,[36,19,20,15,16]],
# [1,[7,10,37,32,15]],
# [1,[42,33,29,2,10]],
# [1,[47,21,48,25,33]],
# [1,[27,15,29,37,2]],
# [1,[42,30,44,38,22]],
# [1,[19,9,44,10,8]],
# [1,[43,33,28,32,39]],
# [1,[37,33,8,34,6]],
# [1,[17,13,22,33,44]],
# [1,[15,29,31,44,45]],
# [1,[16,45,7,47,30]],
# [1,[14,40,45,21,31]],
# [1,[20,47,1,35,46]],
# [1,[38,6,13,10,17]],
# [1,[37,21,19,28,29]],
# [1,[12,4,33,37,45]],
# [1,[49,34,41,33,11]],
# [1,[30,45,11,37,7]],
# [1,[3,15,32,27,6]],
# [1,[11,15,28,39,19]],
# [1,[41,42,26,29,14]],
# [1,[47,32,3,46,6]],
# [1,[24,32,46,8,38]],
# [1,[35,29,37,40,27]],
# [1,[17,2,16,27,48]],
# [1,[38,20,31,30,15]],
# [1,[19,40,23,32,39]],
# [1,[40,31,19,27,18]],
# [1,[28,34,1,40,13]],
# [1,[3,38,40,5,9]],
# [1,[6,19,42,41,12]],
# [1,[25,27,4,11,21]],
# [1,[18,10,27,49,16]],
# [1,[16,4,10,20,24]],
# [1,[18,41,42,35,1]],
# [1,[33,8,4,15,11]],
# [1,[12,47,36,5,45]],
# [1,[47,3,45,46,25]],
# [1,[42,30,12,48,3]],
# [1,[36,33,29,8,6]],
# [1,[6,10,35,25,4]],
# [1,[12,25,16,14,30]],
# [1,[21,7,19,26,33]],
# [1,[26,47,42,25,4]],
# [1,[1,24,12,30,22]],
# [1,[22,7,26,5,2]],
# [1,[20,10,4,18,40]],
# [1,[44,17,8,47,43]],
# [1,[27,34,8,23,21]],
# [1,[47,31,11,33,45]],
# [1,[28,23,6,20,48]],
# [1,[38,42,33,37,43]],
# [1,[31,37,15,34,26]],
# [1,[4,15,31,48,7]],
# [1,[28,36,5,8,21]],
# [1,[22,16,14,19,24]],
# [1,[18,45,3,9,41]],
# [1,[20,16,49,19,43]],
# [1,[1,43,7,18,40]],
# [1,[41,10,49,29,8]],
# [1,[27,49,18,19,8]],
# [1,[38,25,45,13,35]],
# [1,[31,15,6,26,12]],
# [1,[45,49,8,44,14]],
# [1,[31,39,16,46,21]],
# [1,[20,44,41,36,34]],
# [1,[31,28,13,21,30]],
# [1,[25,2,33,12,9]],
# [1,[23,48,34,7,2]],
# [1,[29,9,6,7,16]],
# [1,[40,18,14,43,10]],
# [1,[46,45,26,47,44]],
# [1,[27,10,13,44,38]],
# [1,[49,9,11,18,36]],
# [1,[2,16,48,41,29]],
# [1,[33,6,18,41,36]],
# [1,[28,9,24,2,40]],
# [1,[34,9,1,30,45]],
# [1,[12,29,7,14,30]],
# [1,[6,45,8,9,19]],
# [1,[12,38,23,10,3]],
# [1,[13,38,48,20,49]],
# [1,[11,39,42,21,47]],
# [1,[23,21,47,33,22]],
# [1,[8,39,17,21,26]],
# [1,[7,37,27,22,1]],
# [1,[19,27,44,36,30]],
# [1,[28,43,18,4,27]],
# [1,[48,3,42,18,11]],
# [1,[9,4,49,46,34]],
# [1,[43,17,7,30,3]],
# [1,[37,10,6,28,20]],
# [1,[11,23,40,33,31]],
# [1,[24,8,12,41,33]],
# [1,[21,25,16,31,6]],
# [1,[6,32,30,31,24]],
# [1,[29,41,17,18,46]],
# [1,[25,49,27,41,10]],
# [1,[38,25,41,16,17]],
# [1,[35,17,13,3,43]],
# [1,[6,9,4,8,49]],
# [1,[46,5,28,7,8]],
# [1,[45,25,20,24,37]],
# [1,[49,17,38,41,24]],
# [1,[8,46,44,3,11]],
# [1,[37,36,46,30,9]],
# [1,[33,5,21,43,28]],
# [1,[48,24,1,31,21]],
# [1,[44,8,35,12,16]],
# [1,[16,38,27,44,19]],
# [1,[48,41,15,14,17]],
# [1,[1,17,36,7,22]],
# [1,[8,22,30,46,12]],
# [1,[20,19,9,12,21]],
# [1,[45,12,29,14,47]],
# [1,[10,12,42,27,18]],
# [1,[15,38,7,6,27]],
# [1,[11,48,16,20,46]],
# [1,[9,3,38,11,17]],
# [1,[8,20,17,41,7]],
# [1,[45,48,11,10,19]],
# [1,[20,4,18,2,43]],
# [1,[40,39,10,31,30]],
# [1,[1,11,23,30,29]],
# [1,[49,2,30,47,41]],
# [1,[13,4,18,41,16]],
# [1,[6,30,9,27,4]],
# [1,[7,4,42,25,10]],
# [1,[10,37,31,26,41]],
# [1,[27,4,14,38,28]],
# [1,[33,37,13,16,40]],
# [1,[41,14,29,46,22]],
# [1,[33,39,6,28,2]],
# [1,[36,28,21,40,2]],
# [1,[27,16,24,38,20]],
# [1,[12,4,47,34,8]],
# [1,[30,5,39,3,42]],
# [1,[39,42,37,1,4]],
# [1,[32,37,9,47,42]],
# [1,[45,19,30,15,49]],
# [1,[37,7,9,25,20]],
# [1,[23,2,19,33,39]],
# [1,[42,47,38,30,11]],
# [1,[9,31,28,22,29]],
# [1,[20,14,19,17,32]],
# [1,[42,26,37,19,41]],
# [1,[48,24,42,28,15]],
# [1,[40,5,15,32,28]],
# [1,[13,32,34,40,12]],
# [1,[24,15,46,17,8]],
# [1,[38,48,14,3,21]],
# [1,[24,45,37,3,14]],
# [1,[5,6,45,15,19]],
# [1,[12,3,37,41,26]],
# [1,[40,10,11,15,27]],
# [1,[26,15,47,8,40]],
# [1,[3,48,35,22,12]],
# [1,[4,33,17,40,27]],
# [1,[21,43,2,25,26]],
# [1,[28,29,7,30,13]],
# [1,[3,10,43,20,33]],
# [1,[27,15,24,32,1]],
# [1,[32,22,12,45,31]],
# [1,[47,44,39,33,45]],
# [1,[46,18,28,5,13]],
# [1,[39,21,9,16,7]],
# [1,[5,43,48,7,18]],
# [1,[38,29,25,34,28]],
# [1,[37,39,20,49,2]],
# [1,[9,45,7,6,22]],
# [1,[43,16,42,5,41]],
# [1,[5,39,9,24,44]],
# [1,[29,14,17,30,9]],
# [1,[15,25,46,14,16]],
# [1,[17,28,16,47,30]],
# [1,[43,24,45,46,22]],
# [1,[17,18,26,37,12]],
# [1,[47,5,6,34,29]],
# [1,[22,12,20,11,28]],
# [1,[21,8,42,46,41]],
# [1,[31,2,15,29,35]],
# [1,[46,40,14,18,42]],
# [1,[20,47,1,24,25]],
# [1,[25,29,48,47,12]],
# [1,[46,8,6,3,28]],
# [1,[29,31,26,30,12]],
# [1,[37,45,33,48,15]],
# [1,[3,14,45,31,5]],
# [1,[14,24,48,47,7]],
# [1,[45,30,34,13,31]],
# [1,[20,15,43,2,28]],
# [1,[18,15,35,41,33]],
# [1,[17,47,45,10,30]],
# [1,[18,5,20,32,27]],
# [1,[43,20,32,41,49]],
# [1,[48,36,18,45,20]],
# [1,[46,20,48,16,8]],
# [1,[2,27,36,23,29]],
# [1,[24,39,42,6,20]],
# [1,[24,41,11,43,5]],
# [1,[8,23,27,17,16]],
# [1,[41,30,33,39,11]],
# [1,[6,1,30,21,2]],
# [1,[49,10,27,35,47]],
# [1,[39,40,1,3,7]],
# [1,[15,29,10,17,20]],
# [1,[2,46,9,12,15]],
# [1,[11,33,40,25,1]],
# [1,[13,26,46,39,32]],
# [1,[47,5,30,27,8]],
# [1,[3,43,6,10,49]],
# [1,[45,1,43,8,12]],
# [1,[8,24,35,39,18]],
# [1,[33,49,34,24,20]],
# [1,[31,7,19,48,1]],
# [1,[36,46,28,17,6]],
# [1,[39,24,14,15,2]],
# [1,[26,35,40,17,15]],
# [1,[37,5,29,42,9]],
# [1,[35,18,46,2,33]],
# [1,[1,34,18,31,26]],
# [1,[35,21,1,32,48]],
# [1,[11,29,20,14,45]],
# [1,[2,24,13,14,20]],
# [1,[35,36,3,14,30]],
# [1,[23,46,5,8,2]],
# [1,[6,16,18,42,49]],
# [1,[18,30,7,33,44]],
# [1,[46,3,9,32,29]],
# [1,[27,34,48,19,32]],
# [1,[34,21,25,44,47]],
# [1,[30,25,24,32,45]],
# [1,[9,48,19,8,14]],
# [1,[10,22,14,45,35]],
# [1,[3,49,42,23,39]],
# [1,[34,4,6,36,2]],
# [1,[45,32,9,40,6]],
# [1,[24,6,28,35,2]],
# [1,[34,19,35,42,30]],
# [1,[42,47,35,38,2]],
# [1,[28,8,26,21,11]],
# [1,[45,10,15,26,34]],
# [1,[7,38,21,9,28]],
# [1,[1,48,30,29,38]],
# [1,[28,14,45,24,39]],
# [1,[16,7,21,1,27]],
# [1,[21,48,30,20,47]],
# [1,[45,25,11,43,14]],
# [1,[21,24,3,45,5]],
# [1,[3,29,43,38,4]],
# [1,[24,46,32,8,19]],
# [1,[23,44,20,16,3]],
# [1,[9,24,15,28,8]],
# [1,[2,25,33,22,27]],
# [1,[49,17,33,5,29]],
# [1,[36,11,37,30,1]],
# [1,[44,34,18,42,39]],
# [1,[6,47,28,48,10]],
# [1,[38,45,16,42,36]],
# [1,[2,24,47,6,45]],
# [1,[9,4,20,5,45]],
# [1,[2,4,26,24,8]],
# [1,[29,43,12,33,23]],
# [1,[24,26,49,27,35]],
# [1,[45,20,25,8,40]],
# [1,[6,20,49,21,27]],
# [1,[41,47,37,49,26]],
# [1,[40,32,14,15,47]],
# [1,[19,14,40,5,6]],
# [1,[49,29,1,33,30]],
# [1,[44,22,10,13,49]],
# [1,[10,30,2,34,16]],
# [1,[24,2,22,12,23]],
# [1,[11,46,33,23,40]],
# [1,[47,31,25,11,39]],
# [1,[2,48,24,35,40]],
# [1,[28,38,39,40,26]],
# [1,[19,16,48,28,36]],
# [1,[20,33,8,30,9]],
# [1,[33,12,26,19,17]],
# [1,[28,16,6,38,35]],
# [1,[2,33,9,18,28]],
# [1,[17,42,46,39,6]],
# [1,[19,10,27,6,5]],
# [1,[11,39,31,38,46]],
# [1,[24,5,11,4,30]],
# [1,[42,23,20,49,21]],
# [1,[10,8,5,4,22]],
# [1,[38,6,27,30,22]],
# [1,[10,46,20,38,16]],
# [1,[49,32,31,46,47]],
# [1,[38,37,19,31,29]],
# [1,[46,5,3,33,29]],
# [1,[43,5,8,21,7]],
# [1,[25,43,31,17,46]],
# [1,[17,7,11,34,5]],
# [1,[25,20,18,9,17]],
# [1,[40,22,43,38,5]],
# [1,[17,22,15,18,27]],
# [1,[35,41,26,43,3]],
# [1,[37,28,16,48,26]],
# [1,[31,37,5,26,21]],
# [1,[6,7,14,34,24]],
# [1,[37,17,43,29,34]],
# [1,[19,7,39,32,25]],
# [1,[19,45,34,47,32]],
# [1,[16,7,23,28,26]],
# [1,[28,6,9,34,3]],
# [1,[20,7,38,16,24]],
# [1,[27,40,49,25,36]],
# [1,[5,20,42,4,36]],
# [1,[16,24,35,32,48]],
# [1,[22,32,36,3,7]],
# [1,[17,3,32,23,45]],
# [1,[2,43,3,47,14]],
# [1,[7,40,8,34,17]],
# [1,[1,36,14,16,34]],
# [1,[2,36,4,31,11]],
# [1,[10,7,42,15,33]],
# [1,[43,11,10,46,28]],
# [1,[4,17,10,9,16]],
# [1,[40,47,5,26,39]],
# [1,[32,1,30,48,19]],
# [1,[7,39,26,34,49]],
# [1,[25,33,14,40,18]],
# [1,[7,32,44,11,25]],
# [1,[31,20,6,36,33]],
# [1,[29,30,14,11,4]],
# [1,[49,19,33,9,16]],
# [1,[18,3,43,27,7]],
# [1,[33,6,1,47,26]],
# [1,[13,25,17,24,27]],
# [1,[46,7,17,44,34]],
# [1,[22,7,48,39,46]],
# [1,[30,12,5,43,39]],
# [1,[22,26,38,4,33]],
# [1,[15,38,31,42,3]],
# [1,[26,8,39,4,49]],
# [1,[36,18,27,44,2]],
# [1,[23,3,10,16,44]],
# [1,[45,39,5,33,18]],
# [1,[7,37,10,42,41]],
# [1,[28,10,44,42,13]],
# [1,[43,1,16,2,20]],
# [1,[20,10,3,17,43]],
# [1,[11,15,18,9,27]],
# [1,[46,31,47,29,9]],
# [1,[3,30,33,48,10]],
# [1,[45,47,27,10,2]],
# [1,[2,15,22,21,12]],
# [1,[37,30,1,25,34]],
# [1,[31,28,10,12,7]],
# [1,[7,45,12,40,25]],
# [1,[19,38,18,21,17]],
# [1,[43,3,39,9,19]],
# [1,[27,32,24,21,3]],
# [1,[38,4,22,7,10]],
# [1,[16,23,44,45,20]],
# [1,[1,19,6,8,16]],
# [1,[42,36,4,12,19]],
# [1,[40,48,35,24,12]],
# [1,[31,13,32,30,42]],
# [1,[17,31,28,42,45]],
# [1,[2,12,33,15,5]],
# [1,[4,48,31,18,11]],
# [1,[15,21,22,18,38]],
# [1,[9,16,46,24,5]],
# [1,[17,34,6,11,8]],
# [1,[21,28,18,25,39]],
# [1,[20,35,18,10,5]],
# [1,[40,49,14,43,10]],
# [1,[40,21,42,32,15]],
# [1,[2,5,25,8,38]],
# [1,[10,13,16,32,41]],
# [1,[46,28,1,19,12]],
# [1,[40,11,6,17,23]],
# [1,[22,3,35,5,23]],
# [1,[22,33,41,16,46]],
# [1,[8,15,22,29,44]],
# [1,[13,22,47,35,44]],
# [1,[6,19,27,46,9]],
# [1,[34,24,16,45,26]],
# [1,[30,40,18,41,1]],
# [1,[46,26,11,17,36]],
# [1,[19,15,18,36,9]],
# [1,[25,23,14,29,16]],
# [1,[10,37,3,2,5]],
# [1,[39,9,30,47,11]],
# [1,[3,20,43,38,8]],
# [1,[21,48,13,39,26]],
# [1,[1,24,20,41,48]],
# [1,[27,7,22,20,23]],
# [1,[15,34,31,37,42]],
# [1,[8,37,5,1,7]],
# [1,[35,9,15,18,41]],
# [1,[41,5,17,43,22]],
# [1,[13,37,34,38,19]],
# [1,[46,18,42,29,31]],
# [1,[28,26,13,43,24]],
# [1,[42,1,14,10,35]],
# [1,[45,24,23,46,28]],
# [1,[4,22,28,1,7]],
# [1,[47,15,38,9,18]],
# [1,[16,21,32,14,40]],
# [1,[22,14,11,35,41]],
# [1,[46,49,2,3,14]],
# [1,[29,10,33,5,23]],
# [1,[41,5,37,40,4]],
# [1,[44,14,8,22,26]],
# [1,[9,49,22,25,21]],
# [1,[1,40,25,35,19]],
# [1,[41,17,18,29,27]],
# [1,[21,9,33,48,44]],
# [1,[1,39,46,42,27]],
# [1,[46,9,12,31,2]],
# [1,[22,29,49,16,36]],
# [1,[45,32,49,29,47]],
# [1,[49,44,25,31,45]],
# [1,[17,28,43,31,49]],
# [1,[33,46,44,24,17]],
# [1,[43,45,21,46,48]],
# [1,[44,30,16,7,8]],
# [1,[1,45,27,31,32]],
# [1,[33,29,36,16,17]],
# [1,[10,16,40,23,25]],
# [1,[32,21,22,42,26]],
# [1,[41,43,42,30,48]],
# [1,[2,28,24,3,39]],
# [1,[3,5,33,28,29]],
# [1,[5,36,39,23,3]],
# [1,[23,48,5,20,30]],
# [1,[31,27,46,40,19]],
# [1,[49,29,6,16,32]],
# [1,[28,9,10,24,22]],
# [1,[11,17,9,40,31]],
# [1,[35,41,24,13,27]],
# [1,[36,5,29,33,37]],
# [1,[39,44,4,11,24]],
# [1,[28,11,3,5,36]],
# [1,[15,2,20,31,18]],
# [1,[16,45,27,30,4]],
# [1,[43,33,49,2,44]],
# [1,[26,31,5,24,33]],
# [1,[26,19,35,45,49]],
# [1,[13,49,48,26,38]],
# [1,[19,9,40,14,49]],
# [1,[49,33,9,10,27]],
# [1,[48,29,32,25,5]],
# [1,[4,43,9,45,19]],
# [1,[23,46,41,20,6]],
# [1,[20,26,21,22,25]],
# [1,[7,44,40,42,31]],
# [1,[34,6,17,18,26]],
# [1,[48,49,11,28,33]],
# [1,[2,44,11,48,28]],
# [1,[44,7,43,28,4]],
# [1,[10,49,42,3,38]],
# [1,[44,8,6,22,19]],
# [1,[14,35,24,40,45]],
# [1,[21,27,39,40,3]],
# [1,[14,34,18,31,2]],
# [1,[49,17,21,41,38]],
# [1,[9,30,15,16,21]],
# [1,[8,4,10,1,12]],
# [1,[27,4,28,47,16]],
# [1,[27,7,31,34,37]],
# [1,[24,23,43,19,29]],
# [1,[8,17,48,20,33]],
# [1,[36,45,34,32,43]],
# [1,[24,18,5,15,22]],
# [1,[48,13,36,16,45]],
# [1,[29,27,34,1,46]],
# [1,[43,20,2,7,49]],
# [1,[13,37,27,22,25]],
# [1,[42,8,43,5,31]],
# [1,[16,17,37,8,9]],
# [1,[13,39,14,4,21]],
# [1,[46,28,11,29,49]],
# [1,[2,19,14,39,33]],
# [1,[24,16,44,20,17]],
# [1,[36,44,2,14,6]],
# [1,[29,24,8,42,36]],
# [1,[37,34,19,22,23]],
# [1,[36,10,15,38,21]],
# [1,[8,48,35,41,6]],
# [1,[7,17,12,37,16]],
# [1,[1,24,47,10,16]],
# [1,[32,24,2,39,17]],
# [1,[10,41,13,32,42]],
# [1,[39,25,44,36,5]],
# [1,[1,3,41,42,9]],
# [1,[37,41,40,29,21]],
# [1,[24,31,14,6,12]],
# [1,[16,27,10,30,18]],
# [1,[21,40,20,5,39]],
# [1,[48,32,36,6,35]],
# [1,[18,12,5,33,43]],
# [1,[47,34,33,25,42]],
# [1,[33,23,49,44,1]],
# [1,[3,5,40,25,33]],
# [1,[44,45,43,36,48]],
# [1,[48,3,39,25,49]],
# [1,[11,12,36,28,42]],
# [1,[2,40,26,15,19]],
# [1,[20,34,36,45,25]],
# [1,[37,16,10,3,29]],
# [1,[3,40,45,24,36]],
# [1,[25,40,43,32,37]],
# [1,[29,34,12,49,36]],
# [1,[10,42,29,40,38]],
# [1,[12,7,47,23,1]],
# [1,[16,17,4,41,25]],
# [1,[45,24,12,37,8]],
# [1,[47,2,23,44,17]],
# [1,[2,31,21,5,33]],
# [1,[31,4,38,33,34]],
# [1,[23,9,18,37,20]],
# [1,[43,4,47,28,19]],
# [1,[24,17,22,40,33]],
# [1,[9,29,14,3,44]],
# [1,[15,28,1,38,49]],
# [1,[25,49,17,37,5]],
# [1,[21,33,10,47,46]],
# [1,[44,37,36,43,26]],
# [1,[14,17,40,22,27]],
# [1,[3,19,9,13,41]],
# [1,[48,29,21,11,38]],
# [1,[13,39,47,27,30]],
# [1,[14,26,1,23,37]],
# [1,[38,48,35,39,28]],
# [1,[20,40,31,35,25]],
# [1,[20,2,10,21,19]],
# [1,[8,17,47,28,38]],
# [1,[19,2,5,24,44]],
# [1,[22,34,4,29,14]],
# [1,[3,21,47,2,19]],
# [1,[6,44,48,41,2]],
# [1,[3,45,20,35,18]],
# [1,[35,45,2,20,41]],
# [1,[41,19,32,35,27]],
# [1,[8,41,27,25,21]],
# [1,[23,1,48,42,19]],
# [1,[47,44,49,42,20]],
# [1,[36,27,49,38,45]],
# [1,[5,26,41,30,2]],
# [1,[7,36,38,24,14]],
# [1,[38,8,23,49,6]],
# [1,[44,26,24,8,2]],
# [1,[9,42,44,34,2]],
# [1,[44,2,15,1,3]],
# [1,[21,4,36,17,16]],
# [1,[41,10,44,1,49]],
# [1,[23,47,35,13,46]],
# [1,[42,3,38,33,49]],
# [1,[2,49,26,13,41]],
# [1,[37,32,31,4,48]],
# [1,[43,40,21,9,17]],
# [1,[1,42,8,22,28]],
# [1,[22,5,18,40,48]],
# [1,[35,37,11,9,28]],
# [1,[22,45,11,1,9]],
# [1,[38,8,19,41,49]],
# [1,[13,38,10,36,41]],
# [1,[42,49,43,17,11]],
# [1,[38,24,10,30,34]],
# [1,[3,28,38,27,18]],
# [1,[39,14,11,31,34]],
# [1,[4,22,14,1,37]],
# [1,[46,4,45,33,14]],
# [1,[38,45,34,3,6]],
# [1,[46,8,13,14,45]],
# [1,[7,31,42,49,21]],
# [1,[26,45,30,7,24]],
# [1,[17,18,42,37,8]],
# [1,[34,4,22,20,39]],
# [1,[28,16,15,44,23]],
# [1,[15,5,42,3,32]],
# [1,[49,15,16,9,40]],
# [1,[10,42,2,11,32]],
# [1,[37,23,28,18,45]],
# [1,[21,1,7,43,37]],
# [1,[20,44,27,3,9]],
# [1,[3,22,30,26,16]],
# [1,[32,45,25,6,8]],
# [1,[22,25,45,40,29]],
# [1,[37,33,14,38,6]],
# [1,[44,38,23,32,8]],
# [1,[28,29,25,21,20]],
# [1,[28,39,23,44,33]],
# [1,[17,28,36,9,42]],
# [1,[5,10,43,16,18]],
# [1,[34,20,6,15,9]],
# [1,[23,24,40,15,26]],
# [1,[41,31,43,8,28]],
# [1,[1,28,48,34,7]],
# [1,[10,19,48,34,20]],
# [1,[7,4,17,15,12]],
# [1,[22,2,15,19,26]],
# [1,[29,24,23,34,18]],
# [1,[27,49,16,19,21]],
# [1,[29,16,48,26,4]],
# [1,[30,10,22,41,15]],
# [1,[49,25,3,31,27]],
# [1,[37,5,48,33,31]],
# [1,[20,16,25,15,24]],
# [1,[9,5,3,11,4]],
# [1,[45,12,13,6,22]],
# [1,[45,34,24,33,46]],
# [1,[2,33,34,21,11]],
# [1,[42,26,20,19,43]],
# [1,[43,41,45,28,13]],
# [1,[1,4,35,22,29]],
# [1,[41,32,18,13,31]],
# [1,[13,38,10,26,23]],
# [1,[1,7,24,37,20]],
# [1,[24,9,5,29,39]],
# [1,[30,45,23,19,47]],
# [1,[4,8,48,21,9]],
# [1,[32,40,4,35,8]],
# [1,[19,26,9,49,34]],
# [1,[5,34,15,29,31]],
# [1,[21,28,38,7,25]],
# [1,[19,31,10,16,9]],
# [1,[11,29,46,30,23]],
# [1,[8,28,46,36,41]],
# [1,[30,19,45,6,3]],
# [1,[49,19,33,43,45]],
# [1,[40,23,21,28,7]],
# [1,[17,42,3,33,37]],
# [1,[39,1,15,30,37]],
# [1,[33,2,31,7,41]],
# [1,[15,43,41,10,2]],
# [1,[24,38,16,7,34]],
# [1,[11,39,33,23,44]],
# [1,[25,2,40,27,7]],
# [1,[18,13,43,33,37]],
# [1,[24,12,16,38,34]],
# [1,[13,36,32,5,19]],
# [1,[13,14,10,9,22]],
# [1,[9,22,2,39,42]],
# [1,[10,47,46,37,23]],
# [1,[6,14,26,2,35]],
# [1,[48,2,21,43,35]],
# [1,[5,31,3,32,20]],
# [1,[8,39,29,38,48]],
# [1,[46,5,7,39,18]],
# [1,[22,44,49,4,29]],
# [1,[29,2,31,7,19]],
# [1,[12,37,3,24,10]],
# [1,[10,37,40,27,12]],
# [1,[30,16,36,45,37]],
# [1,[13,17,44,47,15]],
# [1,[7,39,14,22,41]],
# [1,[6,40,8,47,30]],
# [1,[37,45,31,30,23]],
# [1,[16,19,15,18,32]],
# [1,[42,10,29,37,45]],
# [1,[1,40,43,30,46]],
# [1,[27,7,10,24,12]],
# [1,[28,13,31,8,17]],
# [1,[45,25,33,36,38]],
# [1,[14,48,23,4,1]],
# [1,[1,16,6,7,15]],
# [1,[6,15,30,2,49]],
# [1,[19,37,23,21,20]],
# [1,[28,31,26,24,44]],
# [1,[3,37,11,9,2]],
# [1,[27,21,46,18,48]],
# [1,[10,24,5,2,40]],
# [1,[38,11,32,5,35]],
# [1,[21,29,47,12,31]],
# [1,[5,37,30,22,45]],
# [1,[18,11,14,16,28]],
# [1,[13,10,30,18,8]],
# [1,[2,40,17,41,4]],
# [1,[21,45,41,19,15]],
# [1,[28,48,17,15,47]],
# [1,[29,1,5,7,21]],
# [1,[8,19,7,32,35]],
# [1,[2,24,44,17,9]],
# [1,[37,5,15,40,39]],
# [1,[23,9,28,46,22]],
# [1,[4,23,8,28,17]],
# [1,[34,9,3,20,22]],
# [1,[18,48,34,44,23]],
# [1,[21,29,38,14,27]],
# [1,[20,23,48,33,1]],
# [1,[42,6,35,37,31]],
# [1,[8,13,28,3,1]],
# [1,[24,5,23,31,38]],
# [1,[46,17,37,48,7]],
# [1,[17,6,41,23,20]],
# [1,[4,31,20,46,8]],
# [1,[30,38,49,33,43]],
# [1,[16,13,39,46,42]],
# [1,[5,32,6,44,16]],
# [1,[32,9,6,15,12]],
# [1,[42,34,32,4,10]],
# [1,[29,41,9,5,8]],
# [1,[28,47,26,45,39]],
# [1,[40,35,38,37,43]],
# [1,[41,18,2,35,25]],
# [1,[28,17,6,33,35]],
# [1,[5,42,28,12,44]],
# [1,[8,1,20,33,4]],
# [1,[17,16,47,8,25]],
# [1,[12,49,41,25,31]],
# [1,[4,15,28,14,36]],
# [1,[28,24,43,12,21]],
# [1,[31,15,41,42,25]],
# [1,[27,5,15,21,19]],
# [1,[24,33,19,48,34]],
# [1,[28,6,7,33,24]],
# [1,[21,13,8,24,38]],
# [1,[7,6,27,24,3]],
# [1,[21,27,39,18,12]],
# [1,[24,26,30,48,28]],
# [1,[10,17,37,2,7]],
# [1,[39,2,25,21,29]],
# [1,[49,21,1,43,41]],
# [1,[22,13,45,29,3]],
# [1,[17,49,10,38,44]],
# [1,[1,24,27,30,21]],
# [1,[31,7,16,37,23]],
# [1,[3,6,12,45,46]],
# [1,[4,13,27,28,24]],
# [1,[7,30,21,39,13]],
# [1,[5,12,10,36,37]],
# [1,[25,24,4,10,11]],
# [1,[32,39,10,35,4]],
# [1,[47,25,39,33,26]],
# [1,[11,4,37,10,5]],
# [1,[24,7,8,26,43]],
# [1,[35,47,2,33,24]],
# [1,[26,21,48,43,15]],
# [1,[13,27,38,8,20]],
# [1,[48,43,13,38,14]],
# [1,[8,10,33,27,47]],
# [1,[45,29,25,39,42]],
# [1,[7,35,12,21,34]],
# [1,[8,19,24,37,13]],
# [1,[34,11,41,7,10]],
# [1,[13,44,43,5,47]],
# [1,[43,7,6,33,11]],
# [1,[28,26,30,40,37]],
# [1,[24,13,6,32,5]],
# [1,[49,37,33,5,14]],
# [1,[35,44,6,47,42]],
# [1,[46,7,10,34,37]],
# [1,[4,20,46,45,16]],
# [1,[2,44,14,7,22]],
# [1,[23,19,7,6,10]],
# [1,[7,6,27,42,29]],
# [1,[38,13,5,14,43]],
# [1,[47,20,46,35,17]],
# [1,[29,37,38,39,19]],
# [1,[36,28,40,21,10]],
# [1,[27,32,12,21,2]],
# [1,[17,32,20,7,10]],
# [1,[8,25,46,21,3]],
# [1,[44,38,29,15,18]],
# [1,[7,48,22,40,8]],
# [1,[40,48,32,37,28]],
# [1,[5,42,20,21,39]],
# [1,[46,1,28,44,31]],
# [1,[46,19,15,36,2]],
# [1,[5,16,4,43,7]],
# [1,[48,43,18,23,10]],
# [1,[29,23,17,4,19]],
# [1,[2,43,48,8,20]],
# [1,[10,19,40,9,2]],
# [1,[28,31,44,4,13]],
# [1,[11,18,36,33,39]],
# [1,[45,43,33,9,37]],
# [1,[34,11,6,48,28]],
# [1,[22,44,41,16,7]],
# [1,[45,39,18,41,25]],
# [1,[32,9,19,5,13]],
# [1,[46,1,16,3,23]],
# [1,[20,8,31,7,12]],
# [1,[27,39,31,32,23]],
# [1,[16,43,44,36,17]],
# [1,[13,28,26,24,4]],
# [1,[27,12,47,37,6]],
# [1,[20,16,43,33,18]],
# [1,[2,44,26,29,5]],
# [1,[28,4,36,1,22]],
# [1,[2,32,28,7,41]],
# [1,[36,29,4,9,7]],
# [1,[22,40,42,43,35]],
# [1,[5,23,17,37,8]],
# [1,[33,32,26,31,44]],
# [1,[17,8,38,21,18]],
# [1,[6,47,27,1,21]],
# [1,[25,23,39,38,46]],
# [1,[15,10,9,29,18]],
# [1,[11,24,5,21,41]],
# [1,[9,7,18,45,41]],
# [1,[17,4,38,27,46]],
# [1,[44,4,30,19,9]],
# [1,[28,45,30,39,37]],
# [1,[2,45,22,36,26]],
# [1,[18,21,44,28,25]],
# [1,[23,48,44,10,24]],
# [1,[17,49,42,10,7]],
# [1,[2,33,37,27,29]],
# [1,[27,5,34,13,24]],
# [1,[46,15,3,40,39]],
# [1,[1,17,26,6,20]],
# [1,[41,14,27,9,8]],
# [1,[24,34,22,13,44]],
# [1,[21,39,20,15,28]],
# [1,[22,29,48,40,44]],
# [1,[1,16,49,3,31]],
# [1,[43,36,48,38,1]],
# [1,[44,36,20,6,27]],
# [1,[3,45,38,17,26]],
# [1,[5,17,38,39,49]],
# [1,[21,7,6,2,23]],
# [1,[37,48,12,46,17]],
# [1,[15,48,11,4,16]],
# [1,[33,36,46,11,25]],
# [1,[26,4,45,16,27]],
# [1,[20,43,37,36,23]],
# [1,[14,29,31,49,3]],
# [1,[11,10,42,16,36]],
# [1,[35,47,26,8,2]],
# [1,[5,39,45,7,48]],
# [1,[7,49,2,20,32]],
# [1,[40,44,3,41,49]],
# [1,[24,16,43,33,31]],
# [1,[29,18,6,46,21]],
# [1,[31,39,47,1,16]],
# [1,[13,8,14,5,30]],
# [1,[48,25,34,13,10]],
# [1,[13,49,6,37,23]],
# [1,[37,29,20,47,34]],
# [1,[13,5,7,45,29]],
# [1,[26,14,44,15,48]],
# [1,[21,39,18,24,25]],
# [1,[17,11,22,7,23]],
# [1,[47,26,37,43,25]],
# [1,[8,48,24,33,21]],
# [1,[6,23,32,8,14]],
# [1,[15,47,25,36,48]],
# [1,[13,49,8,38,9]],
# [1,[35,29,20,49,16]],
# [1,[48,41,40,27,30]],
# [1,[33,44,7,34,26]],
# [1,[5,34,47,14,3]],
# [1,[45,46,10,19,6]],
# [1,[28,8,44,18,46]],
# [1,[14,31,39,40,16]],
# [1,[12,26,13,35,45]],
# [1,[17,10,34,8,13]],
# [1,[49,42,43,23,12]],
# [1,[44,27,5,37,34]],
# [1,[6,25,32,36,7]],
# [1,[43,6,21,49,20]],
# [1,[39,34,36,40,33]],
# [1,[24,45,14,41,11]],
# [1,[2,17,10,30,27]],
# [1,[28,21,9,30,16]],
# [1,[40,25,20,24,30]],
# [1,[15,34,39,8,3]],
# [1,[19,15,17,11,30]],
# [1,[32,20,37,31,45]],
# [1,[49,47,39,7,46]],
# [1,[19,7,2,46,3]],
# [1,[22,2,33,36,12]],
# [1,[23,25,3,33,37]],
# [1,[38,29,40,24,43]],
# [1,[39,27,47,44,6]],
# [1,[41,33,5,25,29]],
# [1,[10,30,12,33,6]],
# [1,[8,36,30,2,7]],
# [1,[31,20,48,32,24]],
# [1,[15,43,47,44,8]],
# [1,[46,8,37,15,2]],
# [1,[22,18,11,1,21]],
# [1,[1,8,5,27,40]],
# [1,[32,41,40,23,35]],
# [1,[21,9,20,10,12]],
# [1,[9,31,26,32,6]],
# [1,[45,34,32,29,43]],
# [1,[36,4,8,14,10]],
# [1,[39,25,26,9,47]],
# [1,[27,17,15,46,38]],
# [1,[13,30,16,31,41]],
# [1,[9,41,18,42,39]],
# [1,[20,8,23,7,26]],
# [1,[26,16,25,27,31]],
# [1,[11,49,9,31,41]],
# [1,[19,36,17,1,16]],
# [1,[41,29,19,32,14]],
# [1,[36,34,25,24,42]],
# [1,[43,46,15,2,4]],
# [1,[23,34,1,6,48]],
# [1,[16,40,46,21,12]],
# [1,[24,19,45,13,2]],
# [1,[27,48,10,36,41]],
# [1,[3,46,42,32,7]],
# [1,[3,27,23,26,34]],
# [1,[49,3,9,31,12]],
# [1,[28,21,4,38,8]],
# [1,[36,38,28,35,27]],
# [1,[12,14,6,2,8]],
# [1,[14,19,3,17,8]],
# [1,[39,28,7,34,11]],
# [1,[41,19,18,48,30]],
# [1,[47,26,5,40,32]],
# [1,[33,18,46,22,11]],
# [1,[30,22,23,36,27]],
# [1,[14,6,5,25,8]],
# [1,[5,47,21,28,39]],
# [1,[11,22,49,40,44]],
# [1,[23,19,4,37,32]],
# [1,[39,1,42,11,4]],
# [1,[36,39,24,21,3]],
# [1,[9,14,13,35,28]],
# [1,[21,9,34,14,36]],
# [1,[21,7,45,39,30]],
# [1,[31,9,26,11,12]],
# [1,[27,18,44,41,34]],
# [1,[23,1,16,17,36]],
# [1,[16,49,4,38,42]],
# [1,[41,10,43,44,2]],
# [1,[23,7,40,24,17]],
# [1,[2,43,18,5,20]],
# [1,[21,18,49,24,41]],
# [1,[9,23,26,48,24]],
# [1,[20,49,28,11,29]],
# [1,[39,10,14,8,25]],
# [1,[44,41,19,22,9]],
# [1,[30,18,4,6,11]],
# [1,[4,12,26,44,7]],
# [1,[27,10,14,15,48]],
# [1,[25,38,7,44,23]],
# [1,[26,33,4,43,46]],
# [1,[40,43,18,20,1]],
# [1,[7,26,41,46,8]],
# [1,[3,48,43,1,14]],
# [1,[18,43,22,39,13]],
# [1,[48,9,49,36,1]],
# [1,[5,12,48,22,26]],
# [1,[3,32,14,13,31]],
# [1,[33,12,46,13,41]],
# [1,[42,10,47,36,2]],
# [1,[14,12,47,44,10]],
# [1,[18,48,40,47,44]],
# [1,[20,37,8,41,38]],
# [1,[15,33,41,48,43]],
# [1,[23,8,39,44,18]],
# [1,[40,20,23,36,15]],
# [1,[6,1,33,7,16]],
# [1,[23,15,42,2,3]],
# [1,[23,36,15,43,11]],
# [1,[30,48,9,2,4]],
# [1,[1,15,19,29,22]],
# [1,[16,7,45,31,9]],
# [1,[40,37,26,28,17]],
# [1,[45,11,47,31,48]],
# [1,[47,20,39,13,9]],
# [1,[13,34,9,16,45]],
# [1,[46,21,10,17,48]],
# [1,[9,25,22,8,31]],
# [1,[3,28,7,41,45]],
# [1,[27,33,40,18,31]],
# [1,[5,8,49,21,11]],
# [1,[5,22,25,33,32]],
# [1,[37,9,30,32,3]],
# [1,[48,27,9,37,30]],
# [1,[4,17,47,6,5]],
# [1,[45,24,49,12,26]],
# [1,[8,7,2,11,40]],
# [1,[36,13,14,42,16]],
# [1,[45,22,30,49,36]],
# [1,[10,16,49,33,18]],
# [1,[16,27,38,20,11]],
# [1,[36,17,27,40,14]],
# [1,[44,11,6,37,33]],
# [1,[43,16,36,44,15]],
# [1,[7,2,46,31,9]],
# [1,[43,5,10,41,6]],
# [1,[26,47,46,22,5]],
# [1,[12,26,32,16,15]],
# [1,[41,46,17,9,19]],
# [1,[3,44,33,10,1]],
# [1,[29,2,40,25,5]],
# [1,[36,9,46,39,22]],
# [1,[45,27,15,33,41]],
# [1,[46,33,41,39,30]],
# [1,[36,31,6,43,10]],
# [1,[4,40,36,41,11]],
# [1,[20,8,25,26,39]],
# [1,[7,47,15,27,39]],
# [1,[43,25,7,45,26]],
# [1,[29,21,7,4,25]],
# [1,[33,35,21,44,28]],
# [1,[7,6,12,21,48]],
# [1,[36,49,14,40,2]],
# [1,[21,46,28,22,44]],
# [1,[6,49,23,10,15]],
# [1,[17,25,32,24,7]],
# [1,[25,39,30,8,15]],
# [1,[18,12,4,25,32]],
# [1,[29,18,36,1,30]],
# [1,[2,41,39,49,5]],
# [1,[21,11,12,2,8]],
# [1,[38,26,36,16,14]],
# [1,[45,33,13,14,46]],
# [1,[32,26,31,10,25]],
# [1,[14,24,15,44,32]],
# [1,[14,42,32,33,36]],
# [1,[8,39,25,36,16]],
# [1,[26,7,9,34,30]],
# [1,[3,36,35,33,15]],
# [1,[44,1,3,8,49]],
# [1,[34,20,26,13,16]],
# [1,[34,22,1,45,29]],
# [1,[42,33,36,34,10]],
# [1,[31,28,38,15,12]],
# [1,[31,12,13,44,46]],
# [1,[40,5,29,18,38]],
# [1,[24,12,30,33,49]],
# [1,[37,21,19,7,36]],
# [1,[17,26,45,39,29]],
# [1,[8,2,39,27,31]],
# [1,[49,7,42,46,37]],
# [1,[42,40,2,35,14]],
# [1,[27,11,13,33,2]],
# [1,[23,8,10,29,20]],
# [1,[6,36,42,16,26]],
# [1,[48,26,13,24,42]],
# [1,[44,21,17,43,23]],
# [1,[46,30,35,20,24]],
# [1,[43,22,41,28,20]],
# [1,[23,16,20,2,31]],
# [1,[7,46,19,12,35]],
# [1,[29,11,31,46,43]],
# [1,[14,45,33,47,4]],
# [1,[7,36,41,35,28]],
# [1,[3,39,26,46,35]],
# [1,[14,39,44,26,36]],
# [1,[41,31,17,23,35]],
# [1,[19,36,14,7,24]],
# [1,[42,33,49,40,10]],
# [1,[21,12,18,19,2]],
# [1,[14,47,2,10,9]],
# [1,[3,11,5,33,31]],
# [1,[8,17,19,4,29]],
# [1,[16,3,24,7,39]],
# [1,[36,10,28,22,8]],
# [1,[10,23,14,9,39]],
# [1,[12,10,22,26,21]],
# [1,[45,46,7,8,12]],
# [1,[7,37,35,38,1]],
# [1,[10,7,24,13,4]],
# [1,[8,45,31,13,33]],
# [1,[48,31,42,9,45]],
# [1,[11,35,33,20,12]],
# [1,[27,25,26,41,7]],
# [1,[6,17,38,19,47]],
# [1,[12,21,16,27,10]],
# [1,[18,24,34,49,16]],
# [1,[25,8,3,19,49]],
# [1,[4,26,33,17,30]],
# [1,[17,27,29,18,36]],
# [1,[6,13,8,21,1]],
# [1,[27,45,11,8,46]],
# [1,[41,21,20,3,22]],
# [1,[36,24,39,20,2]],
# [1,[44,46,12,22,37]],
# [1,[39,44,32,49,31]],
# [1,[48,32,15,24,35]],
# [1,[16,42,11,43,24]],
# [1,[24,18,44,33,20]],
# [1,[34,21,16,7,3]],
# [1,[39,8,45,30,26]],
# [1,[31,48,17,41,2]],
# [1,[47,6,10,33,24]],
# [1,[18,34,32,22,8]],
# [1,[38,46,29,33,15]],
# [1,[39,5,20,26,6]],
# [1,[36,18,39,17,10]],
# [1,[7,10,23,17,12]],
# [1,[40,41,2,21,26]],
# [1,[37,46,17,38,34]],
# [1,[3,21,46,40,23]],
# [1,[13,23,4,44,35]],
# [1,[21,36,11,46,29]],
# [1,[36,8,14,44,12]],
# [1,[25,32,10,19,12]],
# [1,[46,37,2,35,36]],
# [1,[12,41,33,28,4]],
# [1,[46,32,18,8,45]],
# [1,[15,41,44,22,29]],
# [1,[1,36,19,13,17]],
# [1,[40,9,34,21,35]],
# [1,[25,33,43,17,2]],
# [1,[35,5,9,36,40]],
# [1,[21,20,33,17,31]],
# [1,[31,44,40,2,24]],
# [1,[20,13,1,25,37]],
# [1,[27,35,5,8,25]],
# [1,[31,34,24,40,48]],
# [1,[38,2,25,28,1]],
# [1,[12,35,31,7,8]],
# [1,[32,10,16,13,40]],
# [1,[36,18,49,37,43]],
# [1,[1,37,47,26,3]],
# [1,[20,48,49,45,19]],
# [1,[43,6,12,44,2]],
# [1,[43,1,34,48,12]],
# [1,[10,32,37,41,42]],
# [1,[1,42,9,46,8]],
# [1,[38,43,41,13,3]],
# [1,[32,36,15,10,34]],
# [1,[8,14,27,39,4]],
# [1,[49,27,22,12,36]],
# [1,[8,27,41,9,33]],
# [1,[43,40,30,20,16]],
# [1,[40,20,39,4,22]],
# [1,[5,36,22,10,24]],
# [1,[32,36,40,14,45]],
# [1,[15,19,18,25,39]],
# [1,[43,9,30,26,27]],
# [1,[38,46,31,48,25]],
# [1,[15,41,34,14,39]],
# [1,[49,22,46,41,1]],
# [1,[20,44,33,34,48]],
# [1,[46,31,28,8,12]],
# [1,[24,21,38,23,48]],
# [1,[24,44,5,22,17]],
# [1,[32,41,3,40,16]],
# [1,[37,33,36,6,16]],
# [1,[9,15,21,42,19]],
# [1,[36,13,31,8,38]],
# [1,[3,35,6,10,1]],
# [1,[2,4,1,37,32]],
# [1,[22,44,40,6,42]],
# [1,[43,45,22,3,26]],
# [1,[49,1,15,48,32]],
# [1,[27,19,22,23,18]],
# [1,[17,20,33,4,13]],
# [1,[41,32,26,47,20]],
# [1,[6,5,40,14,9]],
# [1,[6,48,25,32,1]],
# [1,[9,24,12,2,4]],
# [1,[49,4,32,45,12]],
# [1,[42,38,37,44,8]],
# [1,[13,25,45,49,36]],
# [1,[45,37,25,21,41]],
# [1,[27,10,25,45,46]],
# [1,[23,45,39,3,5]],
# [1,[41,17,36,32,30]],
# [1,[26,38,43,9,5]],
# [1,[23,14,26,49,41]],
# [1,[16,24,11,7,36]],
# [1,[35,32,17,30,21]],
# [1,[38,45,29,15,39]],
# [1,[3,9,31,11,23]],
# [1,[7,26,14,30,44]],
# [1,[13,3,17,26,7]],
# [1,[42,12,32,21,45]],
# [1,[30,37,17,8,38]],
# [1,[15,14,22,31,46]],
# [1,[23,13,9,15,25]],
# [1,[36,47,17,7,43]],
# [1,[6,25,3,27,44]],
# [1,[43,37,38,12,24]],
# [1,[5,39,12,42,28]],
# [1,[31,20,46,17,16]],
# [1,[13,45,9,25,20]],
# [1,[34,1,29,36,15]],
# [1,[34,24,44,7,39]],
# [1,[26,21,11,18,41]],
# [1,[22,11,7,28,9]],
# [1,[33,9,28,21,4]],
# [1,[7,18,20,46,33]],
# [1,[31,10,16,36,34]],
# [1,[13,33,32,20,42]],
# [1,[42,48,14,2,5]],
# [1,[29,18,41,5,27]],
# [1,[49,37,17,10,8]],
# [1,[1,44,24,26,16]],
# [1,[42,30,21,28,2]],
# [1,[41,33,4,21,46]],
# [1,[32,17,8,43,27]],
# [1,[19,17,40,2,29]],
# [1,[31,49,32,40,27]],
# [1,[31,24,14,2,36]],
# [1,[24,8,20,28,41]],
# [1,[17,11,38,47,15]],
# [1,[36,35,40,18,33]],
# [1,[37,24,46,11,40]],
# [1,[26,28,16,23,43]],
# [1,[11,6,5,31,14]],
# [1,[17,31,36,47,28]],
# [1,[40,31,42,46,27]],
# [1,[38,45,11,13,2]],
# [1,[16,48,31,44,5]],
# [1,[48,6,27,4,21]],
# [1,[35,11,1,23,16]],
# [1,[24,30,33,23,19]],
# [1,[20,36,32,9,27]],
# [1,[27,31,9,19,5]],
# [1,[15,24,35,38,28]],
# [1,[26,33,18,9,6]],
# [1,[24,12,1,25,26]],
# [1,[10,1,14,46,28]],
# [1,[30,15,25,33,29]],
# [1,[13,11,24,7,34]],
# [1,[45,37,4,38,26]],
# [1,[27,10,12,30,31]],
# [1,[45,4,44,31,6]],
# [1,[36,8,6,30,16]],
# [1,[14,28,33,48,30]],
# [1,[49,17,45,19,31]],
# [1,[40,10,47,38,46]],
# [1,[13,5,16,8,46]],
# [1,[1,2,25,37,41]],
# [1,[32,21,17,10,14]],
# [1,[47,2,17,19,30]],
# [1,[45,39,9,43,8]],
# [1,[2,18,37,46,28]],
# [1,[28,16,43,34,48]],
# [1,[21,43,45,8,6]],
# [1,[46,48,44,17,34]],
# [1,[32,1,13,4,9]],
# [1,[45,30,19,14,46]],
# [1,[22,4,12,39,8]],
# [1,[22,47,44,39,37]],
# [1,[37,29,30,9,27]],
# [1,[40,9,32,38,24]],
# [1,[7,10,18,35,48]],
# [1,[2,6,13,18,21]],
# [1,[42,38,31,2,16]],
# [1,[4,19,28,27,22]],
# [1,[33,37,30,45,35]],
# [1,[15,21,33,4,26]],
# [1,[6,20,25,32,22]],
# [1,[20,34,1,25,7]],
# [1,[20,37,5,41,31]],
# [1,[32,15,35,4,16]],
# [1,[37,18,22,47,17]],
# [1,[21,8,22,20,41]],
# [1,[39,4,31,24,44]],
# [1,[11,26,43,38,25]],
# [1,[32,16,21,49,42]],
# [1,[49,7,29,28,30]],
# [1,[26,21,37,12,18]],
# [1,[26,37,14,8,12]],
# [1,[1,14,33,4,16]],
# [1,[38,23,22,16,19]],
# [1,[28,37,27,10,2]],
# [1,[1,2,38,18,41]],
# [1,[46,19,7,29,2]],
# [1,[44,5,34,41,16]],
# [1,[3,27,25,30,4]],
# [1,[10,6,16,43,7]],
# [1,[20,38,1,2,14]],
# [1,[31,45,25,6,15]],
# [1,[32,44,38,39,12]],
# [1,[48,49,8,10,15]],
# [1,[42,10,4,38,2]],
# [1,[27,38,40,42,26]],
# [1,[7,16,46,11,47]],
# [1,[22,9,42,3,18]],
# [1,[7,13,14,47,41]],
# [1,[1,30,32,20,18]],
# [1,[23,16,31,41,45]],
# [1,[46,7,3,43,28]],
# [1,[43,26,17,1,4]],
# [1,[38,35,14,6,27]],
# [1,[27,3,21,36,42]],
# [1,[31,1,23,21,2]],
# [1,[40,41,12,43,34]],
# [1,[47,18,38,4,29]],
# [1,[3,7,23,17,48]],
# [1,[15,26,28,39,2]],
# [1,[38,17,15,13,35]],
# [1,[46,47,6,17,23]],
# [1,[37,30,42,9,8]],
# [1,[19,38,37,16,34]],
# [1,[5,32,10,11,39]],
# [1,[43,29,13,4,14]],
# [1,[31,25,1,14,42]],
# [1,[44,19,40,48,43]],
# [1,[9,8,5,26,39]],
# [1,[28,18,37,38,32]],
# [1,[31,5,32,24,41]],
# [1,[30,16,35,1,12]],
# [1,[33,15,26,48,38]],
# [1,[34,29,41,10,27]],
# [1,[10,29,33,6,7]],
# [1,[34,14,38,5,3]],
# [1,[19,36,39,35,44]],
# [1,[48,49,46,18,15]],
# [1,[32,40,44,34,24]],
# [1,[31,43,25,48,20]],
# [1,[2,42,15,38,18]],
# [1,[17,15,44,32,30]],
# [1,[15,5,29,8,34]],
# [1,[21,3,49,27,5]],
# [1,[41,49,38,44,13]],
# [1,[28,43,31,14,12]],
# [1,[17,49,43,20,25]],
# [1,[40,25,13,30,20]],
# [1,[37,49,39,27,38]],
# [1,[42,15,17,36,5]],
# [1,[26,18,10,46,16]],
# [1,[43,20,39,33,7]],
# [1,[10,49,20,31,33]],
# [1,[17,37,21,23,30]],
# [1,[11,13,48,14,15]],
# [1,[9,45,14,37,46]],
# [1,[24,41,25,38,28]],
# [1,[37,47,3,6,33]],
# [1,[16,30,17,43,41]],
# [1,[12,15,17,10,11]],
# [1,[3,39,49,9,17]],
# [1,[40,31,15,12,10]],
# [1,[5,29,4,33,14]],
# [1,[19,33,34,46,36]],
# [1,[4,21,34,23,11]],
# [1,[1,30,10,34,21]],
# [1,[36,22,12,49,24]],
# [1,[28,38,13,8,1]],
# [1,[42,37,41,43,7]],
# [1,[37,35,39,12,15]],
# [1,[17,15,47,34,31]],
# [1,[47,24,3,8,20]],
# [1,[49,5,33,30,17]],
# [1,[30,31,7,26,12]],
# [1,[20,21,36,29,31]],
# [1,[27,7,41,38,25]],
# [1,[26,32,45,1,29]],
# [1,[35,45,21,40,18]],
# [1,[22,8,34,32,41]],
# [1,[4,7,45,31,48]],
# [1,[24,41,17,7,16]],
# [1,[37,22,47,40,10]],
# [1,[20,4,16,47,40]],
# [1,[24,3,35,9,40]],
# [1,[10,1,38,22,20]],
# [1,[42,49,35,24,40]],
# [1,[45,34,38,24,48]],
# [1,[3,1,30,16,42]],
# [1,[47,48,44,8,29]],
# [1,[13,21,27,45,26]],
# [1,[9,11,23,5,2]],
# [1,[8,43,32,48,2]],
# [1,[24,19,11,37,33]],
# [1,[22,12,11,15,41]],
# [1,[42,34,28,7,44]],
# [1,[32,7,6,48,23]],
# [1,[11,26,47,34,1]],
# [1,[21,47,8,17,33]],
# [1,[44,38,16,23,15]],
# [1,[6,43,39,31,20]],
# [1,[34,43,9,45,25]],
# [1,[32,14,46,6,35]],
# [1,[34,9,7,35,10]],
# [1,[13,29,2,45,10]],
# [1,[19,34,14,36,9]],
# [1,[39,46,31,29,7]],
# [1,[12,2,8,30,21]],
# [1,[12,29,41,33,4]],
# [1,[48,34,24,29,16]],
# [1,[22,4,44,1,25]],
# [1,[19,33,39,23,40]],
# [1,[11,25,10,26,22]],
# [1,[5,40,42,16,39]],
# [1,[29,49,41,24,47]],
# [1,[15,31,12,7,34]],
# [1,[38,43,27,47,32]],
# [1,[48,8,21,45,46]],
# [1,[48,32,26,9,38]],
# [1,[34,33,7,46,49]],
# [1,[2,38,4,37,49]],
# [1,[5,32,42,44,6]],
# [1,[10,20,33,4,13]],
# [1,[2,20,28,5,32]],
# [1,[36,38,17,39,33]],
# [1,[32,35,36,37,1]],
# [1,[48,20,22,16,46]],
# [1,[23,19,7,43,2]],
# [1,[38,35,30,8,31]],
# [1,[42,38,31,34,10]],
# [1,[15,45,24,40,3]],
# [1,[20,2,31,46,28]],
# [1,[37,48,9,8,45]],
# [1,[5,48,3,40,10]],
# [1,[3,9,5,44,20]],
# [1,[2,18,27,10,24]],
# [1,[35,17,40,14,25]],
# [1,[6,17,19,9,39]],
# [1,[40,8,44,9,42]],
# [1,[42,28,12,45,3]],
# [1,[13,46,22,27,31]],
# [1,[37,7,13,24,9]],
# [1,[41,47,48,49,5]],
# [1,[41,9,31,10,12]],
# [1,[37,35,43,28,3]],
# [1,[39,27,42,10,15]],
# [1,[33,4,22,47,25]],
# [1,[1,18,38,13,12]],
# [1,[45,27,8,30,15]],
# [1,[22,13,35,39,5]],
# [1,[10,31,11,19,32]],
# [1,[6,17,8,34,37]],
# [1,[4,18,25,37,8]],
# [1,[38,26,37,8,9]],
# [1,[29,7,33,49,6]],
# [1,[15,34,39,45,18]],
# [1,[29,22,26,18,4]],
# [1,[32,16,40,39,33]],
# [1,[44,39,13,48,41]],
# [1,[36,25,43,6,34]],
# [1,[23,47,30,20,35]],
# [1,[15,47,25,17,41]],
# [1,[49,16,38,46,17]],
# [1,[34,49,33,16,23]],
# [1,[13,21,25,36,44]],
# [1,[9,5,25,11,33]],
# [1,[32,6,12,33,38]],
# [1,[35,33,21,22,46]],
# [1,[44,32,10,11,13]],
# [1,[30,7,31,16,47]],
# [1,[34,35,45,13,36]],
# [1,[5,49,48,1,15]],
# [1,[46,49,7,39,33]],
# [1,[14,5,45,1,24]],
# [1,[32,5,37,34,23]],
# [1,[43,29,38,32,10]],
# [1,[27,19,12,43,39]],
# [1,[45,43,16,34,25]],
# [1,[33,26,23,32,22]],
# [1,[21,48,27,46,32]],
# [1,[12,19,45,23,38]],
# [1,[37,43,29,47,17]],
# [1,[15,44,47,20,27]],
# [1,[18,44,27,4,31]],
# [1,[44,7,30,27,29]],
# [1,[15,25,29,30,34]],
# [1,[21,12,32,28,37]],
# [1,[18,10,2,49,44]],
# [1,[34,41,25,16,28]],
# [1,[22,32,42,45,24]],
# [1,[27,35,40,31,37]],
# [1,[10,39,37,22,45]],
# [1,[28,11,45,2,30]],
# [1,[49,29,4,20,40]],
# [1,[29,32,44,42,9]],
# [1,[32,31,35,11,29]],
# [1,[40,28,43,8,41]],
# [1,[21,40,36,22,31]],
# [1,[27,35,14,37,17]],
# [1,[47,43,21,30,39]],
# [1,[33,13,42,8,7]],
# [1,[23,43,5,34,25]],
# [1,[17,24,3,29,20]],
# [1,[22,32,42,21,44]],
# [1,[36,40,26,43,6]],
# [1,[41,7,49,45,3]],
# [1,[47,27,24,40,28]],
# [1,[44,6,48,30,39]],
# [1,[28,9,30,16,24]],
# [1,[1,13,24,22,10]],
# [1,[22,23,25,44,26]],
# [1,[17,5,47,38,34]],
# [1,[22,16,43,2,4]],
# [1,[34,45,3,6,32]],
# [1,[49,6,5,10,46]],
# [1,[25,44,23,7,24]],
# [1,[7,49,21,46,38]],
# [1,[36,1,34,21,2]],
# [1,[39,37,19,21,33]],
# [1,[17,11,21,2,15]],
# [1,[4,22,12,40,37]],
# [1,[12,8,40,15,49]],
# [1,[36,44,5,17,37]],
# [1,[43,1,31,38,45]],
# [1,[38,31,39,35,44]],
# [1,[11,4,24,25,29]],
# [1,[24,35,19,10,20]],
# [1,[3,45,21,18,44]],
# [1,[36,6,2,27,4]],
# [1,[8,16,19,37,24]],
# [1,[6,36,33,11,8]],
# [1,[8,1,33,41,36]],
# [1,[8,25,1,47,46]],
# [1,[41,17,33,15,21]],
# [1,[31,16,29,49,41]],
# [1,[37,16,24,9,14]],
# [1,[1,15,45,2,38]],
# [1,[25,3,11,37,47]],
# [1,[44,28,5,40,41]],
# [1,[27,33,29,32,26]],
# [1,[44,37,13,16,19]],
# [1,[16,33,2,29,9]],
# [1,[18,36,10,30,1]],
# [1,[42,48,35,28,7]],
# [1,[2,41,37,12,24]],
# [1,[14,49,29,33,2]],
# [1,[37,30,42,21,18]],
# [1,[26,15,10,8,38]],
# [1,[46,42,43,49,39]],
# [1,[10,27,48,9,38]],
# [1,[6,5,37,8,31]],
# [1,[23,46,28,7,17]],
# [1,[30,23,20,45,5]],
# [1,[38,20,49,6,36]],
# [1,[34,26,22,5,36]],
# [1,[31,5,13,49,2]],
# [1,[44,6,22,38,36]],
# [1,[38,4,5,44,47]],
# [1,[38,17,45,18,36]],
# [1,[26,13,46,1,38]],
# [1,[42,46,44,30,9]],
# [1,[46,2,32,3,35]],
# [1,[34,2,12,8,44]],
# [1,[46,43,42,1,41]],
# [1,[37,17,6,20,14]],
# [1,[2,8,6,7,5]],
# [1,[19,38,44,30,31]],
# [1,[37,17,35,9,38]],
# [1,[43,26,13,32,38]],
# [1,[18,14,3,48,10]],
# [1,[25,47,27,49,29]],
# [1,[10,20,11,44,13]],
# [1,[41,9,47,45,49]],
# [1,[30,9,8,13,11]],
# [1,[49,34,38,37,46]],
# [1,[37,12,45,42,16]],
# [1,[17,45,44,42,27]],
# [1,[23,35,20,45,9]],
# [1,[9,1,40,23,14]],
# [1,[49,36,42,22,35]],
# [1,[45,25,24,14,48]],
# [1,[30,3,37,25,2]],
# [1,[11,44,27,24,8]],
# [1,[26,32,33,44,40]],
# [1,[23,31,6,42,2]],
# [1,[18,9,48,46,11]],
# [1,[10,26,36,9,3]],
# [1,[12,13,3,47,31]],
# [1,[1,34,14,25,26]],
# [1,[30,48,40,24,1]],
# [1,[17,13,40,24,19]],
# [1,[19,39,34,13,16]],
# [1,[45,31,22,17,39]],
# [1,[30,23,13,3,4]],
# [1,[7,35,44,11,5]],
# [1,[13,29,2,20,23]],
# [1,[49,42,37,43,36]],
# [1,[40,1,47,19,26]],
# [1,[26,40,34,13,1]],
# [1,[16,46,12,48,44]],
# [1,[17,1,23,25,7]],
# [1,[32,45,48,27,15]],
# [1,[25,31,29,22,36]],
# [1,[5,10,27,34,21]],
# [1,[44,3,13,7,17]],
# [1,[2,38,44,26,22]],
# [1,[4,16,37,46,23]],
# [1,[10,14,39,47,25]],
# [1,[37,33,28,4,17]],
# [1,[45,1,39,11,3]],
# [1,[46,7,8,9,29]],
# [1,[4,20,19,17,46]],
# [1,[20,47,2,34,18]],
# [1,[31,9,17,25,18]],
# [1,[35,42,24,3,25]],
# [1,[34,9,35,16,31]],
# [1,[33,13,21,6,11]],
# [1,[2,49,28,16,42]],
# [1,[25,21,3,41,29]],
# [1,[36,15,17,49,4]],
# [1,[19,42,9,24,13]],
# [1,[48,47,45,38,27]],
# [1,[8,32,37,21,17]],
# [1,[16,46,4,32,20]],
# [1,[15,23,49,11,9]],
# [1,[22,6,38,9,47]],
# [1,[47,22,32,27,16]],
# [1,[19,24,11,47,23]],
# [1,[25,8,45,41,43]],
# [1,[20,23,3,36,35]],
# [1,[20,39,18,26,22]],
# [1,[46,39,25,47,45]],
# [1,[43,28,15,26,31]],
# [1,[10,44,6,2,25]],
# [1,[13,16,34,42,32]],
# [1,[18,8,44,25,17]],
# [1,[33,37,5,28,14]],
# [1,[24,47,39,10,6]],
# [1,[25,31,7,35,28]],
# [1,[43,14,5,36,2]],
# [1,[44,48,27,47,3]],
# [1,[24,26,20,8,43]],
# [1,[3,1,38,31,42]],
# [1,[18,9,6,3,27]],
# [1,[22,15,27,19,29]],
# [1,[21,5,9,6,1]],
# [1,[1,12,27,4,22]],
# [1,[38,20,39,3,27]],
# [1,[26,25,37,14,6]],
# [1,[32,8,45,3,5]],
# [1,[44,6,11,18,2]],
# [1,[1,37,24,44,35]],
# [1,[47,22,33,23,3]],
# [1,[8,12,27,9,29]],
# [1,[49,19,44,28,3]],
# [1,[16,34,49,26,27]],
# [1,[49,25,21,37,45]],
# [1,[33,17,8,31,14]],
# [1,[22,16,5,49,14]],
# [1,[49,41,42,21,18]],
# [1,[15,33,12,26,48]],
# [1,[33,8,20,32,19]],
# [1,[38,32,4,13,29]],
# [1,[32,13,12,25,26]],
# [1,[8,16,18,29,31]],
# [1,[23,6,47,36,7]],
# [1,[20,3,33,37,30]],
# [1,[24,21,34,47,14]],
# [1,[35,30,44,48,34]],
# [1,[29,33,31,36,38]],
# [1,[33,32,19,2,12]],
# [1,[13,23,30,14,2]],
# [1,[34,9,30,16,39]],
# [1,[28,22,43,20,46]],
# [1,[34,38,7,41,23]],
# [1,[37,14,26,49,9]],
# [1,[13,24,20,2,12]],
# [1,[8,32,5,28,11]],
# [1,[8,20,39,48,19]],
# [1,[18,48,3,33,24]],
# [1,[31,30,37,4,40]],
# [1,[37,21,31,2,40]],
# [1,[43,37,10,25,6]],
# [1,[22,21,7,20,3]],
# [1,[3,16,26,23,39]],
# [1,[35,7,30,48,17]],
# [1,[26,5,1,42,18]],
# [1,[7,37,30,46,9]],
# [1,[16,21,15,36,4]],
# [1,[44,9,21,49,47]],
# [1,[12,35,4,23,5]],
# [1,[8,33,39,36,41]],
# [1,[24,43,15,2,27]],
# [1,[5,11,24,38,46]],
# [1,[27,48,6,34,7]],
# [1,[46,27,7,1,31]],
# [1,[34,35,38,15,23]],
# [1,[42,11,34,21,44]],
# [1,[33,19,37,23,46]],
# [1,[47,42,18,11,17]],
# [1,[24,3,38,4,30]],
# [1,[30,49,22,15,27]],
# [1,[18,1,33,4,14]],
# [1,[17,9,18,21,38]],
# [1,[2,30,12,9,29]],
# [1,[8,30,10,32,23]],
# [1,[31,9,46,14,39]],
# [1,[18,14,34,13,11]],
# [1,[15,5,10,20,33]],
# [1,[47,44,8,30,33]],
# [1,[33,37,32,41,12]],
# [1,[43,10,44,41,32]],
# [1,[28,45,18,22,19]],
# [1,[19,39,30,37,33]],
# [1,[19,6,48,14,40]],
# [1,[26,40,20,27,3]],
# [1,[40,42,49,36,32]],
# [1,[48,49,16,18,29]],
# [1,[7,49,11,9,6]],
# [1,[23,28,18,10,20]],
# [1,[10,2,30,18,9]],
# [1,[32,20,34,21,44]],
# [1,[16,43,8,14,41]],
# [1,[21,22,32,6,31]],
# [1,[2,16,24,38,36]],
# [1,[13,47,29,3,4]],
# [1,[20,27,42,19,21]],
# [1,[41,3,32,22,37]],
# [1,[12,38,44,41,42]],
# [1,[17,6,30,35,45]],
# [1,[9,27,31,1,20]],
# [1,[44,27,32,42,23]],
# [1,[31,23,7,37,26]],
# [1,[34,7,44,15,38]],
# [1,[23,41,5,36,31]],
# [1,[16,4,45,13,12]],
# [1,[26,44,25,24,12]],
# [1,[38,39,27,45,29]],
# [1,[28,35,41,34,16]],
# [1,[33,21,5,12,36]],
# [1,[10,35,4,3,1]],
# [1,[15,41,16,44,11]],
# [1,[26,48,32,42,20]],
# [1,[27,19,9,24,45]],
# [1,[34,18,46,39,40]],
# [1,[29,32,24,38,27]],
# [1,[25,41,40,10,28]],
# [1,[20,33,12,36,27]],
# [1,[1,30,13,9,28]],
# [1,[4,42,7,33,18]],
# [1,[42,13,29,49,28]],
# [1,[11,16,33,30,40]],
# [1,[14,27,28,20,49]],
# [1,[19,33,29,45,48]],
# [1,[6,28,37,9,45]],
# [1,[4,46,16,30,23]],
# [1,[48,15,6,9,1]],
# [1,[49,19,27,26,9]],
# [1,[16,40,48,3,18]],
# [1,[18,29,6,16,38]],
# [1,[44,36,14,33,6]],
# [1,[14,8,35,10,30]],
# [1,[31,20,19,26,15]],
# [1,[26,28,16,31,24]],
# [1,[45,40,42,34,36]],
# [1,[47,38,43,13,48]],
# [1,[34,49,32,33,13]],
# [1,[41,37,18,47,5]],
# [1,[4,26,18,48,12]],
# [1,[13,30,11,22,12]],
# [1,[44,23,31,48,11]],
# [1,[2,48,39,4,27]],
# [1,[35,16,15,20,14]],
# [1,[41,7,49,19,24]],
# [1,[40,12,49,16,33]],
# [1,[12,26,25,37,47]],
# [1,[46,4,36,33,1]],
# [1,[6,18,25,48,27]],
# [1,[22,1,42,11,27]],
# [1,[49,38,44,46,48]],
# [1,[7,40,20,14,29]],
# [1,[8,7,22,20,33]],
# [1,[48,9,26,6,40]],
# [1,[34,37,25,6,9]],
# [1,[27,39,9,21,37]],
# [1,[34,1,39,31,15]],
# [1,[7,41,44,33,9]],
# [1,[49,45,47,33,17]],
# [1,[4,32,22,49,43]],
# [1,[43,20,1,4,49]],
# [1,[38,45,31,16,17]],
# [1,[11,48,34,45,26]],
# [1,[32,28,4,11,26]],
# [1,[22,2,28,9,42]],
# [1,[44,13,6,5,24]],
# [1,[15,34,49,40,22]],
# [1,[2,3,21,31,45]],
# [1,[30,8,29,20,34]],
# [1,[3,11,12,29,43]],
# [1,[26,17,19,13,16]],
# [1,[36,18,38,44,7]],
# [1,[41,8,23,2,18]],
# [1,[14,48,38,30,20]],
# [1,[22,19,48,43,15]],
# [1,[48,35,1,43,17]],
# [1,[17,32,11,6,12]],
# [1,[39,30,3,47,42]],
# [1,[46,10,37,40,9]],
# [1,[25,18,23,27,31]],
# [1,[49,38,28,31,35]],
# [1,[35,11,2,15,13]],
# [1,[43,15,41,19,28]],
# [1,[15,32,9,17,37]],
# [1,[3,48,29,45,10]],
# [1,[26,30,49,29,13]],
# [1,[4,15,2,39,26]],
# [1,[18,28,23,45,2]],
# [1,[14,31,2,41,8]],
# [1,[8,10,1,18,2]],
# [1,[31,23,40,33,22]],
# [1,[26,42,27,33,13]],
# [1,[26,34,24,17,9]],
# [1,[46,18,40,32,10]],
# [1,[9,5,41,46,17]],
# [1,[6,15,45,17,10]],
# [1,[29,15,36,11,19]],
# [1,[29,45,9,17,3]],
# [1,[33,11,2,4,14]],
# [1,[30,45,21,22,49]],
# [1,[38,1,20,22,5]],
# [1,[30,8,49,38,15]],
# [1,[41,44,24,36,13]],
# [1,[42,38,28,27,21]],
# [1,[15,48,26,20,41]],
# [1,[35,46,31,43,36]],
# [1,[2,35,47,16,46]],
# [1,[29,14,37,7,46]],
# [1,[4,41,36,49,30]],
# [1,[49,44,46,27,45]],
# [1,[48,9,17,14,36]],
# [1,[10,32,25,36,6]],
# [1,[49,33,14,42,28]],
# [1,[36,5,10,2,17]],
# [1,[30,46,29,47,33]],
# [1,[39,18,31,17,10]],
# [1,[11,36,33,19,9]],
# [1,[28,37,38,26,21]],
# [1,[48,42,31,6,28]],
# [1,[3,22,40,19,23]],
# [1,[3,15,5,26,18]],
# [1,[16,20,31,27,39]],
# [1,[34,43,39,24,31]],
# [1,[29,31,9,4,25]],
# [1,[6,38,22,7,28]],
# [1,[35,22,29,8,20]],
# [1,[38,1,40,37,28]],
# [1,[19,7,49,1,30]],
# [1,[21,6,29,36,7]],
# [1,[14,41,25,48,38]],
# [1,[12,6,40,25,48]],
# [1,[24,15,31,39,2]],
# [1,[16,33,11,38,24]],
# [1,[11,12,29,20,33]],
# [1,[1,24,41,49,39]],
# [1,[49,35,34,10,5]],
# [1,[10,12,17,23,3]],
# [1,[29,13,20,15,43]],
# [1,[31,36,4,11,17]],
# [1,[46,23,37,39,33]],
# [1,[11,7,47,36,12]],
# [1,[7,49,9,16,29]],
# [1,[2,45,7,21,28]],
# [1,[1,9,11,22,47]],
# [1,[10,40,35,19,27]],
# [1,[38,18,9,31,22]],
# [1,[18,5,31,46,25]],
# [1,[20,6,5,18,13]],
# [1,[11,7,24,1,13]],
# [1,[26,30,2,34,1]],
# [1,[12,45,36,19,31]],
# [1,[8,38,25,32,33]],
# [1,[46,5,33,47,15]],
# [1,[1,35,3,31,22]],
# [1,[46,5,27,23,11]],
# [1,[20,15,35,47,49]],
# [1,[25,47,22,6,43]],
# [1,[36,38,16,19,14]],
# [1,[18,9,1,45,27]],
# [1,[32,11,19,3,41]],
# [1,[25,31,19,47,2]],
# [1,[15,33,48,35,37]],
# [1,[27,46,22,12,11]],
# [1,[24,32,19,48,5]],
# [1,[49,48,42,39,35]],
# [1,[12,6,5,44,15]],
# [1,[10,35,47,26,33]],
# [1,[2,33,4,38,6]],
# [1,[7,49,1,21,25]],
# [1,[5,38,25,22,7]],
# [1,[49,22,48,42,24]],
# [1,[47,49,14,17,3]],
# [1,[47,43,8,6,48]],
# [1,[29,35,38,6,47]],
# [1,[42,46,36,2,27]],
# [1,[45,27,15,4,16]],
# [1,[16,10,23,33,47]],
# [1,[31,39,37,1,3]],
# [1,[48,31,25,7,2]],
# [1,[18,44,4,6,19]],
# [1,[20,29,11,35,2]],
# [1,[10,38,12,42,30]],
# [1,[29,9,49,21,11]],
# [1,[24,19,15,38,27]],
# [1,[29,28,43,48,47]],
# [1,[8,39,23,30,7]],
# [1,[26,35,7,29,20]],
# [1,[47,6,30,37,8]],
# [1,[1,30,10,6,38]],
# [1,[1,48,15,44,4]],
# [1,[14,15,32,5,37]],
# [1,[4,7,2,45,17]],
# [1,[16,4,34,13,26]],
# [1,[24,7,19,31,23]],
# [1,[23,34,44,49,1]],
# [1,[45,29,23,41,35]],
# [1,[17,25,26,27,24]],
# [1,[28,45,11,15,39]],
# [1,[2,16,30,33,5]],
# [1,[37,24,41,45,11]],
# [1,[47,9,5,15,49]],
# [1,[32,20,40,12,28]],
# [1,[3,18,9,46,48]],
# [1,[5,24,18,1,35]],
# [1,[44,40,28,11,26]],
# [1,[27,39,49,16,24]],
# [1,[25,16,29,41,23]],
# [1,[48,40,16,31,26]],
# [1,[17,4,29,43,21]],
# [1,[18,15,22,10,16]],
# [1,[12,22,27,13,46]],
# [1,[48,34,1,8,14]],
# [1,[25,38,31,2,29]],
# [1,[23,6,19,22,37]],
# [1,[29,14,31,16,39]],
# [1,[38,17,11,7,48]],
# [1,[2,15,23,34,38]],
# [1,[1,33,46,24,43]],
# [1,[40,14,7,31,13]],
# [1,[14,6,23,15,35]],
# [1,[21,48,47,15,29]],
# [1,[2,7,46,15,33]],
# [1,[39,15,1,11,35]],
# [1,[28,20,6,43,2]],
# [1,[36,45,3,11,30]],
# [1,[45,2,10,41,13]],
# [1,[12,5,45,9,31]],
# [1,[33,15,39,30,36]],
# [1,[48,11,9,44,22]],
# [1,[19,25,42,48,8]],
# [1,[26,37,7,21,9]],
# [1,[24,34,9,7,27]],
# [1,[35,22,39,23,4]],
# [1,[30,24,12,4,28]],
# [1,[13,32,49,44,40]],
# [1,[25,1,3,6,48]],
# [1,[6,26,42,24,40]],
# [1,[38,5,22,43,24]],
# [1,[47,7,34,24,39]],
# [1,[33,48,37,25,17]],
# [1,[7,49,48,6,9]],
# [1,[41,22,37,25,20]],
# [1,[32,22,13,11,37]],
# [1,[5,29,28,3,46]],
# [1,[30,33,19,49,27]],
# [1,[44,25,49,27,41]],
# [1,[47,5,37,12,38]],
# [1,[42,9,35,31,11]],
# [1,[13,33,29,18,43]],
# [1,[46,28,8,11,42]],
# [1,[43,21,27,47,7]],
# [1,[16,26,8,11,1]],
# [1,[18,14,29,43,39]],
# [1,[32,22,11,23,7]],
# [1,[27,14,19,30,11]],
# [1,[21,29,9,8,42]],
# [1,[31,13,6,14,25]],
# [1,[21,16,47,6,44]],
# [1,[42,15,36,4,10]],
# [1,[13,14,35,48,12]],
# [1,[19,25,10,42,26]],
# [1,[19,26,25,23,36]],
# [1,[44,19,36,31,26]],
# [1,[15,45,36,31,38]],
# [1,[30,5,12,16,14]],
# [1,[44,13,41,14,15]],
# [1,[7,2,22,44,6]],
# [1,[17,10,4,44,32]],
# [1,[37,3,35,24,38]],
# [1,[24,38,4,15,29]],
# [1,[43,33,9,5,21]],
# [1,[36,15,13,22,33]],
# [1,[29,4,47,15,46]],
# [1,[42,1,19,33,9]],
# [1,[21,3,42,18,25]],
# [1,[27,43,36,12,7]],
# [1,[47,24,25,44,48]],
# [1,[13,16,10,11,41]],
# [1,[12,13,41,25,22]],
# [1,[8,47,6,25,41]],
# [1,[43,5,4,23,20]],
# [1,[6,31,44,48,47]],
# [1,[49,48,31,47,19]],
# [1,[6,24,40,7,31]],
# [1,[38,18,39,4,49]],
# [1,[46,15,44,3,24]],
# [1,[9,26,45,28,39]],
# [1,[41,29,31,20,18]],
# [1,[13,2,44,43,25]],
# [1,[4,38,3,18,25]],
# [1,[31,5,36,12,10]],
# [1,[11,6,25,2,34]],
# [1,[36,41,35,37,8]],
# [1,[33,39,1,44,31]],
# [1,[44,45,46,43,36]],
# [1,[39,26,14,6,13]],
# [1,[33,49,1,21,12]],
# [1,[3,38,18,5,37]],
# [1,[25,38,22,37,19]],
# [1,[25,4,24,2,37]],
# [1,[1,14,15,40,22]],
# [1,[12,46,4,49,33]],
# [1,[12,41,31,28,26]],
# [1,[18,20,5,46,28]],
# [1,[10,37,49,29,43]],
# [1,[11,24,22,14,45]],
# [1,[35,8,49,3,46]],
# [1,[46,21,9,17,15]],
# [1,[48,42,43,10,16]],
# [1,[44,37,25,22,42]],
# [1,[28,26,35,14,36]],
# [1,[24,21,40,41,22]],
# [1,[36,3,48,17,25]],
# [1,[13,29,30,36,48]],
# [1,[34,11,7,43,28]],
# [1,[28,32,27,42,40]],
# [1,[20,30,24,6,3]],
# [1,[40,35,38,13,49]],
# [1,[16,2,24,26,49]],
# [1,[12,49,27,13,14]],
# [1,[40,6,48,18,41]],
# [1,[40,32,19,15,49]],
# [1,[43,33,42,30,9]],
# [1,[15,41,21,17,27]],
# [1,[48,23,6,46,49]],
# [1,[40,32,35,5,49]],
# [1,[40,21,38,20,24]],
# [1,[42,23,32,38,15]],
# [1,[14,29,34,20,6]],
# [1,[3,36,19,28,32]],
# [1,[31,42,3,23,11]],
# [1,[28,44,23,36,40]],
# [1,[38,46,25,5,26]],
# [1,[5,2,4,10,11]],
# [1,[4,46,43,23,36]],
# [1,[28,40,22,25,26]],
# [1,[19,10,28,9,5]],
# [1,[43,17,49,26,44]],
# [1,[21,36,4,40,15]],
# [1,[24,8,29,21,4]],
# [1,[41,42,44,39,14]],
# [1,[20,21,41,12,22]],
# [1,[6,24,32,35,37]],
# [1,[27,49,14,38,46]],
# [1,[44,24,47,1,14]],
# [1,[30,37,1,48,3]],
# [1,[22,21,15,3,29]],
# [1,[13,16,42,8,14]],
# [1,[8,10,47,5,30]],
# [1,[9,48,46,2,34]],
# [1,[8,32,19,3,34]],
# [1,[7,41,11,5,10]],
# [1,[22,10,43,36,49]],
# [1,[16,10,28,34,32]],
# [1,[37,9,36,24,12]],
# [1,[34,37,2,42,1]],
# [1,[29,35,5,37,47]],
# [1,[39,8,35,21,37]],
# [1,[15,3,43,37,1]],
# [1,[48,47,20,1,29]],
# [1,[45,2,20,30,4]],
# [1,[36,48,14,26,27]],
# [1,[38,40,7,2,5]],
# [1,[5,3,37,11,4]],
# [1,[36,32,9,10,15]],
# [1,[29,48,44,10,34]],
# [1,[48,7,11,43,49]],
# [1,[34,45,6,17,46]],
# [1,[41,37,36,34,29]],
# [1,[25,24,15,31,27]],
# [1,[16,34,38,36,5]],
# [1,[30,13,49,38,32]],
# [1,[37,36,47,1,42]],
# [1,[1,21,33,12,41]],
# [1,[19,14,44,31,43]],
# [1,[48,17,25,20,43]],
# [1,[47,14,27,28,46]],
# [1,[47,27,22,15,26]],
# [1,[9,22,12,24,13]],
# [1,[1,35,3,21,9]],
# [1,[7,46,43,42,18]],
# [1,[39,23,38,31,32]],
# [1,[13,45,3,48,8]],
# [1,[39,10,19,20,6]],
# [1,[25,48,45,19,15]],
# [1,[33,14,10,46,8]],
# [1,[48,13,5,46,35]],
# [1,[1,7,12,18,46]],
# [1,[4,17,15,5,40]],
# [1,[40,31,39,42,41]],
# [1,[25,37,17,12,36]],
# [1,[15,43,16,20,46]],
# [1,[34,47,9,30,33]],
# [1,[30,1,4,44,19]],
# [1,[1,44,34,49,43]],
# [1,[12,29,44,14,47]],
# [1,[24,3,5,14,6]],
# [1,[43,32,44,3,10]],
# [1,[16,25,41,26,29]],
# [1,[16,29,37,14,18]],
# [1,[3,1,17,48,28]],
# [1,[8,38,19,28,27]],
# [1,[2,25,19,45,47]],
# [1,[10,9,1,20,5]],
# [1,[17,19,49,25,42]],
# [1,[9,30,28,41,44]],
# [1,[35,12,31,29,5]],
# [1,[1,39,40,11,41]],
# [1,[31,34,44,13,23]],
# [1,[7,35,1,34,25]],
# [1,[4,20,22,39,18]],
# [1,[41,28,45,47,3]],
# [1,[11,12,23,27,48]],
# [1,[29,27,24,3,7]],
# [1,[17,16,49,28,42]],
# [1,[7,10,36,42,30]],
# [1,[36,3,13,49,42]],
# [1,[28,47,38,49,11]],
# [1,[16,34,26,32,18]],
# [1,[2,15,4,28,45]],
# [1,[1,46,10,38,4]],
# [1,[15,42,8,18,33]],
# [1,[25,34,13,4,29]],
# [1,[41,29,10,31,38]],
# [1,[14,1,8,32,28]],
# [1,[23,8,27,28,46]],
# [1,[1,42,39,16,47]],
# [1,[48,38,19,37,47]],
# [1,[19,17,11,22,2]],
# [1,[48,14,7,3,49]],
# [1,[48,37,11,27,43]],
# [1,[48,13,47,19,3]],
# [1,[3,18,36,27,49]],
# [1,[6,35,8,1,33]],
# [1,[30,42,7,24,39]],
# [1,[30,32,49,38,46]],
# [1,[24,34,33,25,35]],
# [1,[18,15,26,19,14]],
# [1,[43,27,8,42,7]],
# [1,[13,27,23,22,47]],
# [1,[45,49,13,7,47]],
# [1,[13,24,11,48,49]],
# [1,[46,30,11,26,27]],
# [1,[44,13,17,49,14]],
# [1,[1,46,21,40,32]],
# [1,[32,33,25,31,15]],
# [1,[21,6,32,33,49]],
# [1,[6,4,3,43,5]],
# [1,[3,14,49,23,25]],
# [1,[19,6,5,47,48]],
# [1,[21,47,20,45,49]],
# [1,[27,13,43,47,37]],
# [1,[16,38,36,8,1]],
# [1,[44,11,40,4,9]],
# [1,[33,28,35,21,49]],
# [1,[7,32,37,2,10]],
# [1,[43,34,15,13,3]],
# [1,[35,14,18,15,27]],
# [1,[21,41,24,26,45]],
# [1,[2,37,20,40,42]],
# [1,[42,3,39,16,45]],
# [1,[14,23,31,10,17]],
# [1,[28,19,47,20,38]],
# [1,[25,43,14,37,16]],
# [1,[12,16,25,30,8]],
# [1,[7,25,32,4,18]],
# [1,[4,11,12,44,2]],
# [1,[18,46,19,11,35]],
# [1,[30,31,20,15,21]],
# [1,[11,2,40,22,3]],
# [1,[39,11,5,28,36]],
# [1,[15,27,26,42,29]],
# [1,[11,49,42,6,27]],
# [1,[35,31,16,40,47]],
# [1,[24,23,3,30,31]],
# [1,[46,11,49,42,48]],
# [1,[21,46,39,47,25]],
# [1,[29,30,12,19,5]],
# [1,[39,41,12,19,28]],
# [1,[19,33,26,40,39]],
# [1,[20,34,10,22,37]],
# [1,[29,21,39,15,40]],
# [1,[36,33,45,6,39]],
# [1,[2,28,18,46,7]],
# [1,[47,14,28,19,8]],
# [1,[32,5,42,15,22]],
# [1,[32,39,3,17,9]],
# [1,[24,4,15,44,6]],
# [1,[4,38,30,16,10]],
# [1,[9,46,3,11,36]],
# [1,[19,44,24,1,38]],
# [1,[29,28,36,23,13]],
# [1,[7,5,35,4,42]],
# [1,[40,48,32,27,3]],
# [1,[28,5,23,15,17]],
# [1,[25,18,8,41,4]],
# [1,[37,31,16,27,12]],
# [1,[49,36,48,42,29]],
# [1,[33,27,42,32,35]],
# [1,[22,32,44,6,16]],
# [1,[39,18,19,22,44]],
# [1,[32,12,45,23,29]],
# [1,[17,31,23,2,26]],
# [1,[47,27,34,18,7]],
# [1,[39,3,20,22,4]],
# [1,[3,15,17,14,43]],
# [1,[26,37,30,8,28]],
# [1,[45,40,31,7,15]],
# [1,[31,2,32,29,7]],
# [1,[3,34,9,8,12]],
# [1,[39,34,13,19,27]],
# [1,[44,20,2,19,42]],
# [1,[5,39,28,25,41]],
# [1,[38,16,5,19,44]],
# [1,[34,30,25,44,31]],
# [1,[17,44,47,33,29]],
# [1,[13,18,42,20,40]],
# [1,[8,25,15,45,34]],
# [1,[41,36,43,2,25]],
# [1,[44,45,36,6,21]],
# [1,[42,30,33,46,35]],
# [1,[35,20,13,30,17]],
# [1,[3,44,43,34,14]],
# [1,[10,1,47,30,16]],
# [1,[36,28,16,25,29]],
# [1,[39,43,8,21,19]],
# [1,[21,8,34,2,36]],
# [1,[8,28,35,49,7]],
# [1,[40,25,7,22,28]],
# [1,[9,20,36,32,46]],
# [1,[20,24,37,22,46]],
# [1,[12,18,34,14,5]],
# [1,[35,44,29,10,39]],
# [1,[17,12,3,49,42]],
# [1,[19,40,35,25,12]],
# [1,[42,20,45,13,49]],
# [1,[24,21,10,46,35]],
# [1,[15,27,28,7,16]],
# [1,[12,20,5,49,37]],
# [1,[25,7,21,39,16]],
# [1,[18,10,39,44,36]],
# [1,[42,9,27,3,37]],
# [1,[30,10,38,31,24]],
# [1,[9,17,35,36,14]],
# [1,[7,2,16,8,37]],
# [1,[43,29,28,6,12]],
# [1,[14,3,45,44,12]],
# [1,[20,42,40,2,3]],
# [1,[42,19,11,9,36]],
# [1,[11,25,28,36,49]],
# [1,[28,49,23,36,47]],
# [1,[26,27,35,7,41]],
# [1,[17,44,25,27,28]],
# [1,[28,45,16,35,34]],
# [1,[5,25,36,20,12]],
# [1,[37,10,23,19,22]],
# [1,[12,22,39,10,24]],
# [1,[45,42,24,20,8]],
# [1,[13,36,4,44,18]],
# [1,[39,26,12,45,28]],
# [1,[47,49,39,4,38]],
# [1,[31,19,10,17,23]],
# [1,[41,25,19,21,36]],
# [1,[6,20,27,14,8]],
# [1,[14,37,46,4,11]],
# [1,[46,40,43,31,9]],
# [1,[45,15,49,19,2]],
# [1,[38,32,19,15,6]],
# [1,[38,39,46,34,31]],
# [1,[26,27,21,32,15]],
# [1,[18,39,13,17,6]],
# [1,[40,29,1,44,13]],
# [1,[27,32,1,30,3]],
# [1,[29,7,20,33,10]],
# [1,[33,9,1,29,18]],
# [1,[20,25,29,24,8]],
# [1,[46,34,18,2,27]],
# [1,[27,10,16,44,18]],
# [1,[20,22,25,16,41]],
# [1,[2,11,23,44,37]],
# [1,[20,26,8,38,42]],
# [1,[39,46,21,42,6]],
# [1,[6,40,28,26,21]],
# [1,[39,37,14,17,27]],
# [1,[25,14,48,40,37]],
# [1,[12,25,48,19,16]],
# [1,[23,44,14,42,11]],
# [1,[45,41,17,6,9]],
# [1,[28,10,14,24,43]],
# [1,[36,48,26,35,31]],
# [1,[15,24,43,10,26]],
# [1,[9,11,42,6,14]],
# [1,[5,46,19,34,13]],
# [1,[44,27,42,46,31]],
# [1,[30,41,36,9,19]],
# [1,[29,12,47,28,3]],
# [1,[16,27,38,6,23]],
# [1,[32,9,25,27,43]],
# [1,[15,25,40,33,7]],
# [1,[8,33,25,14,49]],
# [1,[4,17,16,22,12]],
# [1,[37,44,20,25,23]],
# [1,[17,22,23,44,19]],
# [1,[38,10,14,49,4]],
# [1,[31,38,11,20,37]],
# [1,[27,42,9,32,13]],
# [1,[38,14,19,11,26]],
# [1,[8,38,44,25,24]],
# [1,[20,37,1,15,4]],
# [1,[9,43,10,44,47]],
# [1,[27,34,30,42,17]],
# [1,[3,27,35,33,42]],
# [1,[1,6,15,9,49]],
# [1,[35,12,19,13,27]],
# [1,[8,23,13,1,40]],
# [1,[17,2,42,5,10]],
# [1,[41,1,18,19,11]],
# [1,[45,46,5,8,7]],
# [1,[9,20,21,23,37]],
# [1,[10,33,28,47,25]],
# [1,[45,22,30,14,46]],
# [1,[28,35,24,11,48]],
# [1,[8,22,46,6,43]],
# [1,[14,27,32,23,48]],
# [1,[42,2,40,13,21]],
# [1,[39,11,8,21,49]],
# [1,[12,35,11,23,27]],
# [1,[32,9,34,19,35]],
# [1,[39,2,19,23,22]],
# [1,[26,19,11,10,8]],
# [1,[12,18,4,6,1]],
# [1,[34,30,22,17,43]],
# [1,[17,4,21,3,43]],
# [1,[29,40,13,42,30]],
# [1,[4,35,28,36,32]],
# [1,[39,37,42,35,29]],
# [1,[49,30,22,46,29]],
# [1,[38,23,48,31,4]],
# [1,[2,38,19,30,32]],
# [1,[4,41,35,16,1]],
# [1,[20,18,41,48,28]],
# [1,[36,18,39,30,45]],
# [1,[14,11,1,49,25]],
# [1,[32,16,19,48,20]],
# [1,[24,25,7,47,48]],
# [1,[40,26,32,20,12]],
# [1,[40,27,15,49,9]],
# [1,[41,22,15,26,12]],
# [1,[12,4,34,19,2]],
# [1,[8,30,36,24,5]],
# [1,[8,45,47,20,29]],
# [1,[34,30,21,5,25]],
# [1,[33,30,21,49,28]],
# [1,[4,31,21,27,32]],
# [1,[15,4,36,42,12]],
# [1,[17,39,28,4,36]],
# [1,[27,36,8,25,38]],
# [1,[18,34,30,17,14]],
# [1,[44,8,21,24,35]],
# [1,[24,3,37,48,10]],
# [1,[7,47,2,31,8]],
# [1,[28,4,3,7,13]],
# [1,[6,46,19,45,25]],
# [1,[12,33,42,26,6]],
# [1,[31,19,4,37,40]],
# [1,[24,2,41,8,44]],
# [1,[29,32,27,41,16]],
# [1,[16,44,20,29,34]],
# [1,[45,2,27,44,15]],
# [1,[8,23,47,29,49]],
# [1,[16,45,18,23,36]],
# [1,[49,18,30,11,27]],
# [1,[41,21,11,1,13]],
# [1,[31,7,20,27,10]],
# [1,[27,5,28,22,4]],
# [1,[40,21,26,33,3]],
# [1,[8,48,10,1,33]],
# [1,[46,18,1,48,37]],
# [1,[6,35,15,45,38]],
# [1,[24,20,23,19,6]],
# [1,[37,34,4,9,38]],
# [1,[39,20,6,33,5]],
# [1,[13,35,2,41,34]],
# [1,[9,41,23,1,43]],
# [1,[22,2,32,33,11]],
# [1,[4,48,36,21,46]],
# [1,[31,18,9,36,19]],
# [1,[11,31,30,41,6]],
# [1,[37,14,28,18,23]],
# [1,[36,46,7,24,45]],
# [1,[18,47,45,34,31]],
# [1,[38,41,13,33,19]],
# [1,[48,6,19,46,21]],
# [1,[41,36,10,43,23]],
# [1,[16,22,41,43,26]],
# [1,[28,14,31,29,24]],
# [1,[35,30,18,47,23]],
# [1,[4,20,32,17,1]],
# [1,[9,20,27,7,33]],
# [1,[49,23,9,30,37]],
# [1,[4,44,24,18,22]],
# [1,[26,44,45,34,8]],
# [1,[49,28,32,27,31]],
# [1,[7,34,8,38,18]],
# [1,[43,25,40,26,15]],
# [1,[10,32,2,26,8]],
# [1,[12,21,25,36,49]],
# [1,[28,41,44,26,32]],
# [1,[18,33,36,47,35]],
# [1,[34,45,3,48,49]],
# [1,[18,28,22,21,20]],
# [1,[40,47,48,26,49]],
# [1,[41,20,19,34,49]],
# [1,[5,38,37,31,12]],
# [1,[33,23,20,49,34]],
# [1,[5,44,35,28,1]],
# [1,[40,20,28,7,14]],
# [1,[36,8,14,31,49]],
# [1,[5,36,41,33,1]],
# [1,[5,1,27,18,2]],
# [1,[33,35,49,40,2]],
# [1,[34,49,31,36,43]],
# [1,[30,36,1,37,25]],
# [1,[49,8,47,44,20]],
# [1,[16,47,26,23,25]],
# [1,[6,20,13,11,23]],
# [1,[1,34,26,27,18]],
# [1,[27,5,32,9,35]],
# [1,[22,27,33,48,25]],
# [1,[42,16,40,11,9]],
# [1,[33,28,24,11,46]],
# [1,[45,31,35,34,38]],
# [1,[45,21,17,22,27]],
# [1,[2,33,44,34,1]],
# [1,[24,20,2,3,49]],
# [1,[30,17,16,14,1]],
# [1,[41,34,18,47,25]],
# [1,[26,30,25,45,11]],
# [1,[22,19,29,13,30]],
# [1,[38,49,37,5,33]],
# [1,[35,24,29,1,28]],
# [1,[26,42,25,29,7]],
# [1,[34,43,4,41,29]],
# [1,[3,49,9,17,35]],
# [1,[19,27,31,33,7]],
# [1,[9,7,45,29,43]],
# [1,[21,34,6,32,30]],
# [1,[10,27,13,35,16]],
# [1,[3,45,15,17,46]],
# [1,[27,35,5,43,8]],
# [1,[6,34,42,20,19]],
# [1,[11,46,13,47,27]],
# [1,[16,44,17,4,49]],
# [1,[35,39,25,20,33]],
# [1,[11,18,46,14,8]],
# [1,[8,26,40,39,4]],
# [1,[2,27,26,24,48]],
# [1,[31,49,42,47,18]],
# [1,[38,10,3,36,47]],
# [1,[27,14,46,9,25]],
# [1,[5,37,46,44,19]],
# [1,[45,30,44,47,20]],
# [1,[25,39,42,46,37]],
# [1,[37,33,8,42,22]],
# [1,[29,48,26,8,23]],
# [1,[6,47,29,40,15]],
# [1,[25,7,8,21,9]],
# [1,[5,21,20,47,22]],
# [1,[47,45,15,10,2]],
# [1,[10,42,9,7,43]],
# [1,[22,24,19,42,8]],
# [1,[2,43,3,37,41]],
# [1,[8,37,12,17,14]],
# [1,[14,48,7,2,25]],
# [1,[37,8,45,5,22]],
# [1,[3,17,11,36,25]],
# [1,[16,44,13,39,23]],
# [1,[39,29,11,47,37]],
# [1,[29,13,40,22,43]],
# [1,[23,38,1,39,6]],
# [1,[7,34,12,43,3]],
# [1,[8,3,15,7,23]],
# [1,[10,13,14,25,48]],
# [1,[43,46,3,39,24]],
# [1,[42,26,2,38,35]],
# [1,[4,32,48,38,21]],
# [1,[26,31,30,35,5]],
# [1,[34,5,23,18,47]],
# [1,[14,44,24,7,19]],
# [1,[26,30,2,10,48]],
# [1,[33,36,40,30,35]],
# [1,[14,8,34,35,22]],
# [1,[13,44,32,16,40]],
# [1,[27,47,40,46,16]],
# [1,[32,33,3,31,14]],
# [1,[35,15,8,30,41]],
# [1,[21,10,46,41,19]],
# [1,[21,23,41,14,48]],
# [1,[30,9,7,44,40]],
# [1,[47,37,2,45,34]],
# [1,[21,44,38,10,12]],
# [1,[12,29,22,24,38]],
# [1,[40,31,20,37,6]],
# [1,[37,10,38,2,36]],
# [1,[2,27,34,9,4]],
# [1,[38,12,19,35,47]],
# [1,[12,32,5,25,23]],
# [1,[36,13,3,1,24]],
# [1,[47,24,36,22,12]],
# [1,[22,25,35,8,19]],
# [1,[14,1,41,8,37]],
# [1,[4,33,41,28,17]],
# [1,[44,7,27,19,18]],
# [1,[35,33,25,17,8]],
# [1,[39,41,17,37,23]],
# [1,[3,32,12,15,40]],
# [1,[48,8,38,20,9]],
# [1,[15,18,24,16,20]],
# [1,[46,41,2,38,8]],
# [1,[42,48,6,28,10]],
# [1,[30,23,21,42,36]],
# [1,[49,28,38,25,10]],
# [1,[43,45,24,30,33]],
# [1,[10,47,46,15,48]],
# [1,[13,3,22,19,42]],
# [1,[37,42,15,1,14]],
# [1,[16,5,3,43,19]],
# [1,[41,49,40,26,4]],
# [1,[24,15,43,49,20]],
# [1,[19,42,22,31,9]],
# [1,[42,7,30,33,13]],
# [1,[13,4,20,15,8]],
# [1,[28,48,12,14,22]],
# [1,[49,5,35,32,10]],
# [1,[24,33,8,39,10]],
# [1,[2,1,15,28,19]],
# [1,[3,29,33,25,1]],
# [1,[46,32,5,44,30]],
# [1,[25,31,48,4,19]],
# [1,[12,49,31,1,38]],
# [1,[2,47,29,12,40]],
# [1,[29,30,37,9,48]],
# [1,[33,9,45,42,27]],
# [1,[25,9,23,13,33]],
# [1,[33,14,26,28,48]],
# [1,[49,42,46,36,3]],
# [1,[15,22,3,30,19]],
# [1,[48,17,27,22,21]],
# [1,[6,48,41,10,43]],
# [1,[48,18,32,39,6]],
# [1,[46,9,39,21,40]],
# [1,[30,22,28,31,40]],
# [1,[40,23,11,12,3]],
# [1,[29,4,10,17,16]],
# [1,[40,44,14,6,35]],
# [1,[22,39,30,20,10]],
# [1,[28,13,47,27,3]],
# [1,[18,24,5,32,43]],
# [1,[16,19,24,36,34]],
# [1,[3,9,13,37,20]],
# [1,[1,14,42,12,40]],
# [1,[13,19,27,3,41]],
# [1,[21,42,4,18,10]],
# [1,[17,12,22,2,43]],
# [1,[1,47,6,3,2]],
# [1,[28,15,25,45,44]],
# [1,[23,12,30,20,47]],
# [1,[36,41,24,22,39]],
# [1,[9,5,26,22,39]],
# [1,[7,26,38,36,25]],
# [1,[49,31,4,19,18]],
# [1,[36,22,26,2,34]],
# [1,[39,13,35,1,10]],
# [1,[11,17,12,6,23]],
# [1,[28,10,15,39,1]],
# [1,[43,38,34,24,18]],
# [1,[4,18,31,14,27]],
# [1,[35,38,31,40,27]],
# [1,[5,35,40,3,14]],
# [1,[13,45,22,27,1]],
# [1,[30,37,8,23,49]],
# [1,[46,26,14,48,43]],
# [1,[16,35,5,47,31]],
# [1,[16,11,22,8,37]],
# [1,[2,32,10,40,25]],
# [1,[43,39,8,34,35]],
# [1,[30,20,22,28,23]],
# [1,[12,42,35,4,29]],
# [1,[22,11,48,25,32]],
# [1,[11,44,43,5,20]],
# [1,[24,35,37,23,16]],
# [1,[48,46,49,4,9]],
# [1,[20,39,2,30,38]],
# [1,[35,31,27,8,43]],
# [1,[31,26,9,20,48]],
# [1,[35,18,44,45,6]],
# [1,[6,35,30,12,32]],
# [1,[6,41,38,14,21]],
# [1,[29,18,14,27,40]],
# [1,[5,34,21,10,8]],
# [1,[49,33,19,45,32]],
# [1,[36,23,49,16,17]],
# [1,[13,9,37,24,27]],
# [1,[43,10,18,36,28]],
# [1,[25,31,12,26,11]],
# [1,[14,41,18,7,44]],
# [1,[34,40,27,44,3]],
# [1,[8,17,1,12,30]],
# [1,[29,41,15,27,42]],
# [1,[30,4,14,6,27]],
# [1,[11,45,31,23,13]],
# [1,[25,21,2,26,49]],
# [1,[1,14,41,30,15]],
# [1,[16,13,39,15,40]],
# [1,[44,42,8,25,41]],
# [1,[43,3,14,32,33]],
# [1,[19,37,43,6,20]],
# [1,[2,44,16,39,23]],
# [1,[6,46,28,48,45]],
# [1,[7,18,19,46,14]],
# [1,[36,45,34,5,32]],
# [1,[22,33,7,14,27]],
# [1,[14,12,4,38,10]],
# [1,[30,2,25,35,1]],
# [1,[23,32,12,8,40]],
# [1,[2,27,33,6,12]],
# [1,[14,42,7,20,12]],
# [1,[21,29,2,44,39]],
# [1,[2,18,46,48,27]],
# [1,[49,31,4,29,33]],
# [1,[49,38,6,22,16]],
# [1,[33,2,10,9,7]],
# [1,[35,5,49,45,29]],
# [1,[31,27,11,19,4]],
# [1,[48,16,22,20,14]],
# [1,[25,21,42,12,31]],
# [1,[2,35,31,42,18]],
# [1,[39,49,44,31,28]],
# [1,[47,16,2,40,3]],
# [1,[7,27,39,3,49]],
# [1,[17,21,12,41,48]],
# [1,[33,7,6,24,34]],
# [1,[45,4,34,13,15]],
# [1,[15,36,31,8,12]],
# [1,[45,7,47,4,40]],
# [1,[38,3,10,23,47]],
# [1,[11,26,12,30,21]],
# [1,[31,10,45,29,40]],
# [1,[42,23,12,28,8]],
# [1,[31,13,19,29,42]],
# [1,[2,12,34,5,47]],
# [1,[37,23,25,32,17]],
# [1,[25,48,7,44,23]],
# [1,[22,33,39,17,23]],
# [1,[40,29,13,49,42]],
# [1,[31,28,15,36,17]],
# [1,[12,19,8,27,46]],
# [1,[6,32,25,21,23]],
# [1,[3,32,11,23,48]],
# [1,[11,13,23,22,40]],
# [1,[12,11,36,23,24]],
# [1,[6,14,3,37,41]],
# [1,[7,42,41,23,39]],
# [1,[9,32,47,49,2]],
# [1,[23,2,15,25,12]],
# [1,[42,38,22,8,29]],
# [1,[13,49,36,20,38]],
# [1,[8,12,37,40,28]],
# [1,[42,23,30,48,32]],
# [1,[34,14,43,7,45]],
# [1,[8,21,2,16,49]],
# [1,[24,47,39,6,1]],
# [1,[25,14,40,38,31]],
# [1,[22,9,46,13,36]],
# [1,[40,35,7,10,28]],
# [1,[28,31,42,27,22]],
# [1,[45,1,6,37,10]],
# [1,[18,46,32,37,33]],
# [1,[21,38,43,35,19]],
# [1,[37,12,10,20,2]],
# [1,[35,27,41,44,42]],
# [1,[2,29,16,24,14]],
# [1,[39,38,19,17,25]],
# [1,[21,26,5,2,10]],
# [1,[33,37,25,34,27]],
# [1,[39,1,45,18,8]],
# [1,[7,48,17,11,37]],
# [1,[21,48,9,2,40]],
# [1,[2,28,46,29,43]],
# [1,[43,31,35,44,30]],
# [1,[6,41,4,3,24]],
# [1,[39,16,36,47,44]],
# [1,[28,42,4,8,31]],
# [1,[44,27,2,42,3]],
# [1,[7,8,47,49,35]],
# [1,[49,33,47,10,9]],
# [1,[45,48,26,40,16]],
# [1,[4,9,45,17,26]],
# [1,[26,25,41,22,20]],
# [1,[35,7,17,46,12]],
# [1,[29,32,14,2,22]],
# [1,[45,19,15,21,34]],
# [1,[49,47,38,30,36]],
# [1,[23,9,44,27,25]],
# [1,[24,7,1,4,46]],
# [1,[7,30,33,46,44]],
# [1,[5,29,27,22,38]],
# [1,[32,47,43,33,37]],
# [1,[29,13,32,22,44]],
# [1,[38,37,19,34,8]],
# [1,[42,1,27,20,39]],
# [1,[10,35,3,9,20]],
# [1,[20,11,45,33,44]],
# [1,[15,36,29,39,34]],
# [1,[19,20,43,22,46]],
# [1,[29,27,24,7,21]],
# [1,[32,10,38,49,42]],
# [1,[28,19,10,43,20]],
# [1,[39,2,40,29,14]],
# [1,[38,37,44,11,34]],
# [1,[44,14,11,34,9]],
# [1,[38,20,35,19,7]],
# [1,[46,13,5,32,37]],
# [1,[42,26,44,48,19]],
# [1,[11,18,49,27,12]],
# [1,[5,27,32,22,24]],
# [1,[30,4,28,12,26]],
# [1,[16,3,47,49,44]],
# [1,[39,21,11,5,12]],
# [1,[1,41,4,26,9]],
# [1,[43,19,24,4,30]],
# [1,[49,7,6,21,12]],
# [1,[43,27,24,39,21]],
# [1,[2,46,34,12,36]],
# [1,[23,37,24,19,46]],
# [1,[40,19,9,38,32]],
# [1,[42,28,30,2,41]],
# [1,[28,38,39,34,3]],
# [1,[2,35,22,3,43]],
# [1,[23,16,39,19,12]],
# [1,[36,26,7,40,37]],
# [1,[47,24,43,1,2]],
# [1,[32,23,36,7,19]],
# [1,[34,43,17,38,24]],
# [1,[27,10,19,18,7]],
# [1,[18,31,21,2,13]],
# [1,[18,24,45,21,30]],
# [1,[45,32,49,23,36]],
# [1,[29,1,49,12,25]],
# [1,[7,44,25,29,22]],
# [1,[27,26,49,3,31]],
# [1,[9,5,6,41,45]],
# [1,[31,49,29,21,46]],
# [1,[49,1,40,34,31]],
# [1,[45,13,47,3,23]],
# [1,[34,16,12,36,20]],
# [1,[15,16,34,8,35]],
# [1,[30,15,4,18,2]],
# [1,[46,14,21,41,43]],
# [1,[46,32,8,20,18]],
# [1,[9,40,24,30,26]],
# [1,[23,48,18,33,46]],
# [1,[24,32,28,13,14]],
# [1,[16,37,40,1,45]],
# [1,[26,25,1,34,42]],
# [1,[13,9,23,30,11]],
# [1,[5,36,27,16,9]],
# [1,[12,30,11,6,18]],
# [1,[11,39,37,14,10]],
# [1,[47,32,22,43,2]],
# [1,[31,16,3,48,9]],
# [1,[29,42,8,43,39]],
# [1,[9,32,33,42,35]],
# [1,[10,18,27,28,23]],
# [1,[33,35,5,30,28]],
# [1,[6,46,4,1,10]],
# [1,[19,4,15,2,21]],
# [1,[27,14,42,9,28]],
# [1,[34,9,11,17,24]],
# [1,[39,43,7,2,45]],
# [1,[32,47,10,43,41]],
# [1,[7,3,1,49,20]],
# [1,[47,16,1,8,49]],
# [1,[3,16,5,46,20]],
# [1,[44,33,43,8,30]],
# [1,[25,47,33,27,1]],
# [1,[1,17,48,43,44]],
# [1,[14,18,34,40,7]],
# [1,[45,4,19,6,33]],
# [1,[27,6,47,32,39]],
# [1,[24,47,18,39,21]],
# [1,[15,7,37,19,40]],
# [1,[39,33,5,46,26]],
# [1,[16,10,25,45,4]],
# [1,[46,27,16,28,49]],
# [1,[34,1,26,27,2]],
# [1,[3,44,43,4,27]],
# [1,[21,35,22,31,40]],
# [1,[31,49,33,36,14]],
# [1,[38,41,24,42,40]],
# [1,[37,27,24,21,43]],
# [1,[30,22,46,7,38]],
# [1,[40,16,19,11,24]],
# [1,[2,20,44,11,9]],
# [1,[17,14,16,5,42]],
# [1,[3,10,41,1,42]],
# [1,[29,33,30,21,39]],
# [1,[12,31,44,14,4]],
# [1,[26,37,45,40,14]],
# [1,[25,45,15,30,28]],
# [1,[20,23,21,31,15]],
# [1,[36,44,31,8,39]],
# [1,[19,16,26,47,34]],
# [1,[8,43,46,36,47]],
# [1,[26,6,28,21,19]],
# [1,[20,4,33,11,12]],
# [1,[21,33,43,23,30]],
# [1,[47,26,29,35,41]],
# [1,[41,38,32,15,19]],
# [1,[4,47,6,15,18]],
# [1,[37,8,17,5,11]],
# [1,[2,45,40,33,37]],
# [1,[5,46,18,48,30]],
# [1,[5,49,23,9,22]],
# [1,[7,2,40,19,23]],
# [1,[6,35,42,33,16]],
# [1,[41,23,43,22,30]],
# [1,[29,4,6,42,23]],
# [1,[12,43,8,27,5]],
# [1,[4,9,12,24,27]],
# [1,[3,24,8,45,35]],
# [1,[13,28,46,15,29]],
# [1,[16,22,48,31,39]],
# [1,[9,46,37,13,15]],
# [1,[36,5,18,21,17]],
# [1,[30,16,42,38,35]],
# [1,[14,21,42,11,45]],
# [1,[36,45,12,29,47]],
# [1,[3,28,21,27,19]],
# [1,[49,44,41,2,36]],
# [1,[21,11,45,35,29]],
# [1,[31,19,46,48,33]],
# [1,[1,16,47,12,32]],
# [1,[44,30,26,34,17]],
# [1,[1,16,6,15,24]],
# [1,[7,19,4,14,45]],
# [1,[33,8,5,30,39]],
# [1,[20,22,39,12,3]],
# [1,[43,6,37,23,32]],
# [1,[15,16,1,8,17]],
# [1,[7,5,44,16,36]],
# [1,[14,47,23,37,2]],
# [1,[3,10,44,40,41]],
# [1,[40,26,34,2,37]],
# [1,[19,33,42,23,20]],
# [1,[37,28,19,47,14]],
# [1,[38,16,14,2,45]],
# [1,[46,7,44,8,42]],
# [1,[2,14,35,41,3]],
# [1,[17,49,15,44,42]],
# [1,[14,18,32,23,44]],
# [1,[10,5,8,9,13]],
# [1,[26,36,46,3,29]],
# [1,[9,35,15,29,16]],
# [1,[14,48,34,28,1]],
# [1,[35,2,19,9,39]],
# [1,[6,14,37,38,4]],
# [1,[13,10,36,44,1]],
# [1,[14,33,23,10,25]],
# [1,[20,42,19,5,3]],
# [1,[36,23,32,18,4]],
# [1,[21,49,47,22,35]],
# [1,[9,44,40,26,20]],
# [1,[5,25,48,37,10]],
# [1,[46,45,9,12,18]],
# [1,[38,4,42,7,15]],
# [1,[18,40,7,1,37]],
# [1,[21,45,25,46,32]],
# [1,[43,24,5,45,27]],
# [1,[30,3,6,27,17]],
# [1,[47,41,44,12,20]],
# [1,[30,18,6,32,38]],
# [1,[3,21,32,9,14]],
# [1,[10,32,49,36,39]],
# [1,[33,24,34,18,37]],
# [1,[19,3,15,33,20]],
# [1,[23,11,9,38,27]],
# [1,[5,7,22,28,3]],
# [1,[48,22,45,23,36]],
# [1,[3,43,6,25,8]],
# [1,[49,20,43,28,13]],
# [1,[25,16,20,8,4]],
# [1,[1,36,41,22,46]],
# [1,[22,27,45,39,6]],
# [1,[35,18,2,12,5]],
# [1,[38,6,42,32,15]],
# [1,[13,24,14,8,38]],
# [1,[35,6,45,19,43]],
# [1,[30,18,8,12,15]],
# [1,[37,1,10,31,25]],
# [1,[27,48,31,5,44]],
# [1,[1,31,14,49,15]],
# [1,[40,22,14,44,43]],
# [1,[3,24,41,26,45]],
# [1,[37,19,49,29,23]],
# [1,[25,2,21,37,33]],
# [1,[17,28,4,5,33]],
# [1,[47,8,12,48,28]],
# [1,[33,49,6,11,22]],
# [1,[19,34,14,49,36]],
# [1,[31,49,24,6,37]],
# [1,[41,40,5,13,29]],
# [1,[48,18,34,28,4]],
# [1,[2,31,30,49,18]],
# [1,[24,1,11,36,46]],
# [1,[26,12,34,10,20]],
# [1,[6,1,48,16,26]],
# [1,[27,13,36,46,47]],
# [1,[16,10,22,35,4]],
# [1,[13,48,43,34,26]],
# [1,[24,10,7,17,3]],
# [1,[17,12,21,38,27]],
# [1,[48,12,23,30,46]],
# [1,[25,42,3,5,13]],
# [1,[16,23,40,4,29]],
# [1,[48,20,11,32,13]],
# [1,[1,35,47,6,36]],
# [1,[9,31,27,14,18]],
# [1,[15,44,20,5,22]],
# [1,[27,10,41,29,42]],
# [1,[15,24,10,25,31]],
# [1,[38,34,43,21,2]],
# [1,[30,17,18,1,38]],
# [1,[30,47,7,13,33]],
# [1,[48,44,6,11,35]],
# [1,[48,34,11,40,8]],
# [1,[30,22,36,26,13]],
# [1,[7,41,29,26,38]],
# [1,[23,30,38,4,1]],
# [1,[44,15,17,16,41]],
# [1,[34,39,49,26,32]],
# [1,[15,30,10,37,49]],
# [1,[19,38,39,27,12]],
# [1,[13,23,10,26,30]],
# [1,[22,38,8,48,20]],
# [1,[49,40,27,20,15]],
# [1,[1,38,15,5,23]],
# [1,[21,5,24,7,25]],
# [1,[28,15,47,35,25]],
# [1,[6,21,41,11,28]],
# [1,[43,40,18,20,47]],
# [1,[14,41,32,44,6]],
# [1,[49,36,45,15,8]],
# [1,[33,16,5,7,28]],
# [1,[8,25,48,29,6]],
# [1,[1,7,37,16,19]],
# [1,[7,32,3,41,43]],
# [1,[6,29,19,32,21]],
# [1,[1,9,31,15,18]],
# [1,[42,1,7,28,14]],
# [1,[31,7,39,20,46]],
# [1,[19,20,1,22,25]],
# [1,[7,34,15,38,8]],
# [1,[49,43,13,17,40]],
# [1,[7,12,45,22,2]],
# [1,[13,26,1,21,41]],
# [1,[3,45,2,36,1]],
# [1,[40,21,15,44,28]],
# [1,[33,23,32,25,45]],
# [1,[31,38,19,46,3]],
# [1,[25,27,39,9,48]],
# [1,[13,32,23,28,27]],
# [1,[44,19,42,25,36]],
# [1,[48,5,32,24,42]],
# [1,[6,24,16,10,7]],
# [1,[17,9,2,34,12]],
# [1,[25,33,14,34,22]],
# [1,[21,25,22,20,33]],
# [1,[36,31,6,10,32]],
# [1,[19,11,6,20,47]],
# [1,[19,33,4,49,38]],
# [1,[24,11,49,17,35]],
# [1,[15,22,9,6,17]],
# [1,[10,36,16,22,34]],
# [1,[42,25,49,3,1]],
# [1,[19,20,15,33,2]],
# [1,[7,18,36,12,13]],
# [1,[30,36,12,49,18]],
# [1,[19,24,46,21,17]],
# [1,[15,22,28,13,39]],
# [1,[45,3,44,35,27]],
# [1,[49,27,19,38,45]],
# [1,[31,49,11,28,43]],
# [1,[1,29,32,43,31]],
# [1,[17,33,42,45,23]],
# [1,[45,10,38,7,12]],
# [1,[36,9,27,35,49]],
# [1,[26,34,35,25,8]],
# [1,[11,17,16,14,30]],
# [1,[38,40,16,48,26]],
# [1,[37,8,19,41,27]],
# [1,[9,10,31,34,24]],
# [1,[38,21,6,30,3]],
# [1,[36,29,13,46,31]],
# [1,[18,29,15,38,4]],
# [1,[2,41,13,38,28]],
# [1,[13,5,28,27,10]],
# [1,[8,11,25,47,36]],
# [1,[17,12,30,41,21]],
# [1,[47,26,2,27,28]],
# [1,[4,43,45,8,34]],
# [1,[44,22,36,46,10]],
# [1,[36,8,23,38,29]],
# [1,[7,41,28,45,8]],
# [1,[47,5,24,25,45]],
# [1,[15,22,21,37,13]],
# [1,[45,5,47,14,30]],
# [1,[13,9,20,3,21]],
# [1,[38,8,43,41,17]],
# [1,[17,20,42,10,6]],
# [1,[33,37,48,10,3]],
# [1,[36,30,48,13,19]],
# [1,[28,11,43,41,44]],
# [1,[25,21,15,16,46]],
# [1,[31,45,42,18,32]],
# [1,[15,22,11,16,44]],
# [1,[26,19,3,39,11]],
# [1,[3,47,45,44,48]],
# [1,[25,8,2,37,42]],
# [1,[22,10,7,44,42]],
# [1,[48,30,22,35,46]],
# [1,[31,27,1,35,46]],
# [1,[29,20,49,5,21]],
# [1,[43,32,14,24,2]],
# [1,[12,34,20,6,18]],
# [1,[2,32,18,49,4]],
# [1,[37,10,5,42,16]],
# [1,[22,38,30,21,16]],
# [1,[34,6,27,14,7]],
# [1,[10,34,27,33,49]],
# [1,[24,20,43,28,46]],
# [1,[46,3,18,40,44]],
# [1,[26,16,19,32,44]],
# [1,[3,19,28,37,43]],
# [1,[46,11,41,22,10]],
# [1,[34,10,43,32,39]],
# [1,[11,21,15,25,37]],
# [1,[21,6,44,26,49]],
# [1,[23,21,31,43,32]],
# [1,[32,43,24,1,46]],
# [1,[48,6,18,4,19]],
# [1,[45,6,1,14,22]],
# [1,[45,15,30,47,33]],
# [1,[17,30,19,3,2]],
# [1,[23,49,13,24,1]],
# [1,[13,18,41,48,40]],
# [1,[15,32,35,4,13]],
# [1,[13,44,41,5,9]],
# [1,[45,25,17,4,12]],
# [1,[20,40,16,45,47]],
# [1,[4,12,33,6,7]],
# [1,[47,48,42,13,4]],
# [1,[48,28,31,19,38]],
# [1,[4,43,11,12,35]],
# [1,[8,22,39,5,12]],
# [1,[32,37,12,43,34]],
# [1,[42,28,32,33,13]],
# [1,[22,44,36,26,19]],
# [1,[41,33,21,31,29]],
# [1,[33,5,28,12,38]],
# [1,[7,28,15,30,29]],
# [1,[41,12,37,49,3]],
# [1,[22,20,3,33,24]],
# [1,[11,15,7,49,21]],
# [1,[5,36,9,32,12]],
# [1,[30,44,25,46,20]],
# [1,[30,21,9,34,37]],
# [1,[10,34,33,36,26]],
# [1,[23,7,35,31,29]],
# [1,[49,20,27,45,3]],
# [1,[47,26,46,48,40]],
# [1,[34,26,11,38,43]],
# [1,[35,13,41,25,23]],
# [1,[11,18,33,10,44]],
# [1,[27,13,35,14,9]],
# [1,[18,22,23,7,48]],
# [1,[10,19,8,14,43]],
# [1,[32,21,15,4,19]],
# [1,[26,14,31,18,32]],
# [1,[35,29,27,22,15]],
# [1,[38,31,8,5,44]],
# [1,[38,10,11,9,21]],
# [1,[13,49,3,15,10]],
# [1,[8,13,15,35,9]],
# [1,[2,35,33,27,44]],
# [1,[43,32,13,8,27]],
# [1,[29,9,30,12,42]],
# [1,[36,8,40,2,37]],
# [1,[43,16,6,40,5]],
# [1,[37,38,19,47,46]],
# [1,[35,13,34,27,48]],
# [1,[28,17,1,48,40]],
# [1,[28,48,42,47,26]],
# [1,[32,2,48,43,47]],
# [1,[6,19,25,20,28]],
# [1,[29,7,42,30,3]],
# [1,[30,43,29,16,1]],
# [1,[38,10,41,37,20]],
# [1,[44,33,39,34,42]],
# [1,[9,49,36,39,3]],
# [1,[8,33,13,48,25]],
# [1,[15,18,17,33,35]],
# [1,[8,16,19,26,38]],
# [1,[14,24,12,27,41]],
# [1,[22,11,13,48,5]],
# [1,[5,31,34,35,32]],
# [1,[41,42,34,2,44]],
# [1,[1,4,37,49,31]],
# [1,[5,17,8,20,27]],
# [1,[48,23,27,25,30]],
# [1,[19,40,46,23,22]],
# [1,[35,24,6,33,5]],
# [1,[48,4,24,38,43]],
# [1,[12,8,11,23,34]],
# [1,[7,41,38,17,25]],
# [1,[17,41,11,28,27]],
# [1,[17,4,18,16,39]],
# [1,[41,13,2,9,10]],
# [1,[26,46,49,40,39]],
# [1,[8,23,38,10,4]],
# [1,[35,26,36,21,44]],
# [1,[23,40,18,28,10]],
# [1,[16,14,26,49,30]],
# [1,[38,18,26,25,22]],
# [1,[32,42,8,4,1]],
# [1,[3,49,31,47,7]],
# [1,[18,9,3,22,42]],
# [1,[26,11,9,42,8]],
# [1,[31,33,3,18,40]],
# [1,[27,1,28,11,39]],
# [1,[14,36,20,42,9]],
# [1,[39,14,5,26,2]],
# [1,[18,38,28,45,20]],
# [1,[30,16,43,45,27]],
# [1,[23,39,5,25,49]],
# [1,[16,20,18,41,4]],
# [1,[47,2,17,39,6]],
# [1,[31,44,7,8,18]],
# [1,[41,22,17,46,28]],
# [1,[35,20,16,19,34]],
# [1,[41,12,48,47,28]],
# [1,[20,30,29,42,37]],
# [1,[20,26,2,44,1]],
# [1,[1,34,27,11,8]],
# [1,[30,38,47,48,40]],
# [1,[45,42,5,23,38]],
# [1,[11,27,16,5,13]],
# [1,[45,40,8,26,20]],
# [1,[40,32,41,19,11]],
# [1,[35,34,5,41,49]],
# [1,[16,13,1,23,19]],
# [1,[11,4,24,30,12]],
# [1,[10,16,21,7,35]],
# [1,[37,15,21,11,20]],
# [1,[27,41,17,2,24]],
# [1,[6,42,18,32,33]],
# [1,[10,16,4,30,33]],
# [1,[42,32,21,39,26]],
# [1,[32,46,24,10,43]],
# [1,[15,10,19,37,11]],
# [1,[11,29,23,46,36]],
# [1,[10,22,20,38,18]],
# [1,[17,12,19,34,36]],
# [1,[34,22,30,12,37]],
# [1,[34,6,25,11,46]],
# [1,[40,42,31,36,32]],
# [1,[9,31,27,1,2]],
# [1,[2,14,13,49,10]],
# [1,[8,3,41,12,6]],
# [1,[41,10,34,3,14]],
# [1,[14,35,44,5,39]],
# [1,[38,10,2,1,41]],
# [1,[48,26,30,31,13]],
# [1,[6,45,39,24,2]],
# [1,[31,15,9,25,35]],
# [1,[25,5,9,24,39]],
# [1,[15,25,30,14,17]],
# [1,[47,5,8,25,36]],
# [1,[33,31,34,38,1]],
# [1,[37,25,40,23,26]],
# [1,[46,43,45,22,44]],
# [1,[49,40,34,9,44]],
# [1,[10,14,34,7,27]],
# [1,[39,23,49,12,18]],
# [1,[29,30,25,9,5]],
# [1,[22,36,40,15,4]],
# [1,[9,43,5,49,12]],
# [1,[7,37,27,11,32]],
# [1,[17,1,40,47,20]],
# [1,[48,24,35,16,41]],
# [1,[24,43,22,37,28]],
# [1,[16,12,10,22,5]],
# [1,[22,10,24,45,42]],
# [1,[20,2,21,31,44]],
# [1,[31,33,46,16,25]],
# [1,[45,22,16,18,44]],
# [1,[27,41,37,9,47]],
# [1,[29,7,49,44,30]],
# [1,[17,49,12,7,44]],
# [1,[19,25,37,28,32]],
# [1,[19,34,46,22,26]],
# [1,[29,23,45,18,26]],
# [1,[22,28,17,31,29]],
# [1,[45,36,46,7,18]],
# [1,[39,5,49,20,14]],
# [1,[48,26,46,3,30]],
# [1,[8,38,44,46,30]],
# [1,[18,40,48,21,20]],
# [1,[4,3,46,13,36]],
# [1,[34,45,28,8,6]],
# [1,[30,3,2,37,29]],
# [1,[21,37,42,15,41]],
# [1,[9,49,29,31,48]],
# [1,[20,7,15,45,18]],
# [1,[1,25,45,21,11]],
# [1,[11,38,40,15,47]],
# [1,[37,8,19,33,11]],
# [1,[21,30,38,45,28]],
# [1,[45,18,17,28,42]],
# [1,[41,6,48,15,38]],
# [1,[17,38,49,3,8]],
# [1,[26,45,29,37,17]],
# [1,[10,2,12,37,11]],
# [1,[6,2,9,14,36]],
# [1,[4,40,38,20,23]],
# [1,[18,40,15,21,28]],
# [1,[15,3,17,27,49]],
# [1,[34,5,9,10,11]],
# [1,[16,10,32,13,26]],
# [1,[3,11,37,22,39]],
# [1,[16,31,40,24,29]],
# [1,[40,18,12,41,36]],
# [1,[46,1,15,19,7]],
# [1,[5,12,21,31,14]],
# [1,[38,40,47,19,26]],
# [1,[22,35,23,6,24]],
# [1,[40,2,44,12,34]],
# [1,[27,10,37,36,1]],
# [1,[47,18,44,26,17]],
# [1,[30,14,12,29,2]],
# [1,[40,33,45,41,34]],
# [1,[28,1,4,38,49]],
# [1,[10,7,47,12,2]],
# [1,[26,15,44,48,49]],
# [1,[27,1,9,13,36]],
# [1,[5,19,30,47,31]],
# [1,[38,42,40,1,14]],
# [1,[18,48,12,46,11]],
# [1,[43,32,14,10,37]],
# [1,[32,13,3,47,48]],
# [1,[11,4,16,39,22]],
# [1,[49,16,38,6,36]],
# [1,[29,6,47,43,18]],
# [1,[21,37,39,10,45]],
# [1,[2,15,8,4,31]],
# [1,[23,40,9,5,2]],
# [1,[29,18,27,17,20]],
# [1,[20,32,42,14,36]],
# [1,[10,4,19,32,29]],
# [1,[42,22,5,46,43]],
# [1,[18,46,35,19,11]],
# [1,[42,14,36,10,28]],
# [1,[2,49,6,24,4]],
# [1,[1,13,30,25,45]],
# [1,[31,12,1,32,28]],
# [1,[32,35,44,8,38]],
# [1,[25,11,46,35,12]],
# [1,[45,42,1,2,16]],
# [1,[12,36,46,10,13]],
# [1,[9,20,18,34,4]],
# [1,[29,42,19,2,49]],
# [1,[19,1,4,37,45]],
# [1,[7,28,43,46,6]],
# [1,[16,5,21,28,39]],
# [1,[43,34,28,31,26]],
# [1,[49,27,28,46,3]],
# [1,[3,13,36,43,2]],
# [1,[7,1,8,30,4]],
# [1,[34,46,22,35,16]],
# [1,[22,39,45,19,30]],
# [1,[39,4,46,16,27]],
# [1,[2,30,33,11,27]],
# [1,[45,34,32,36,42]],
# [1,[31,17,47,1,16]],
# [1,[33,11,3,29,16]],
# [1,[15,47,7,33,16]],
# [1,[1,31,33,17,5]],
# [1,[29,27,43,4,6]],
# [1,[19,27,37,49,4]],
# [1,[36,46,14,6,40]],
# [1,[19,10,8,5,32]],
# [1,[44,13,39,29,19]],
# [1,[9,33,29,26,1]],
# [1,[23,6,4,19,38]],
# [1,[12,2,4,26,11]],
# [1,[34,42,30,44,6]],
# [1,[16,8,25,18,33]],
# [1,[7,17,9,21,48]],
# [1,[12,14,43,45,15]],
# [1,[41,6,7,16,29]],
# [1,[25,3,10,40,16]],
# [1,[3,22,19,44,18]],
# [1,[18,15,22,36,10]],
# [1,[24,2,19,12,30]],
# [1,[24,8,6,32,41]],
# [1,[34,25,15,4,35]],
# [1,[37,34,8,39,46]],
# [1,[41,20,30,33,36]],
# [1,[22,23,31,33,2]],
# [1,[22,24,19,38,49]],
# [1,[10,5,23,33,31]],
# [1,[31,10,21,16,35]],
# [1,[19,33,47,1,21]],
# [1,[1,13,29,33,46]],
# [1,[30,32,35,36,25]],
# [1,[19,24,34,3,20]],
# [1,[21,38,47,26,14]],
# [1,[3,1,44,47,38]],
# [1,[30,34,8,21,26]],
# [1,[18,19,27,36,33]],
# [1,[7,41,29,4,12]],
# [1,[3,37,10,5,15]],
# [1,[18,49,12,41,21]],
# [1,[21,8,27,25,43]],
# [1,[28,45,31,6,25]],
# [1,[29,39,5,42,36]],
# [1,[46,32,44,22,41]],
# [1,[14,17,21,48,5]],
# [1,[38,46,30,14,7]],
# [1,[44,19,7,16,45]],
# [1,[43,16,5,22,44]],
# [1,[18,29,4,43,8]],
# [1,[38,41,46,14,12]],
# [1,[31,11,48,42,47]],
# [1,[47,40,29,25,23]],
# [1,[10,13,4,18,6]],
# [1,[27,24,15,20,23]],
# [1,[25,35,41,39,31]],
# [1,[24,25,36,34,22]],
# [1,[23,35,1,18,41]],
# [1,[49,31,15,27,33]],
# [1,[48,12,23,30,35]],
# [1,[41,14,27,38,43]],
# [1,[41,28,48,18,42]],
# [1,[3,23,42,45,49]],
# [1,[18,15,17,47,41]],
# [1,[28,48,11,40,4]],
# [1,[16,36,33,4,15]],
# [1,[14,13,26,42,45]],
# [1,[17,46,9,4,48]],
# [1,[16,43,23,7,28]],
# [1,[7,46,30,16,26]],
# [1,[21,46,31,36,8]],
# [1,[42,18,2,33,14]],
# [1,[38,46,15,43,35]],
# [1,[8,43,28,25,38]],
# [1,[36,17,35,33,7]],
# [1,[20,35,36,29,31]],
# [1,[39,23,8,34,1]],
# [1,[6,32,45,33,41]],
# [1,[46,4,15,31,26]],
# [1,[1,11,6,35,7]],
# [1,[38,39,22,33,10]],
# [1,[35,24,32,33,45]],
# [1,[25,46,43,21,14]],
# [1,[22,33,31,44,45]],
# [1,[41,2,40,25,44]],
# [1,[3,46,6,8,17]],
# [1,[25,7,40,26,6]],
# [1,[45,18,16,10,31]],
# [1,[17,39,16,49,43]],
# [1,[3,26,15,45,13]],
# [1,[31,5,11,12,26]],
# [1,[34,12,15,17,18]],
# [1,[14,13,27,40,16]],
# [1,[14,20,31,3,39]],
# [1,[44,23,29,40,11]],
# [1,[33,7,34,17,31]],
# [1,[6,20,22,16,33]],
# [1,[36,49,30,4,23]],
# [1,[37,43,24,11,38]],
# [1,[3,47,14,29,27]],
# [1,[44,4,15,10,25]],
# [1,[7,3,18,2,17]],
# [1,[8,6,34,2,14]],
# [1,[9,28,27,44,32]],
# [1,[20,5,24,45,6]],
# [1,[32,4,18,14,28]],
# [1,[6,10,42,11,19]],
# [1,[18,27,13,31,16]],
# [1,[17,43,1,21,27]],
# [1,[19,35,16,14,27]],
# [1,[17,45,5,36,10]],

# ]


# winnings_dict = []


# def search_number_occurences(numlist1: list, numlist2: list, number_of_match_limit=3) -> int:
#     """COUNT COMMON NUMBERS"""
#     match_count = 0

#     for number in numlist1:

#         if number in numlist2[1]:

#             match_count += 1

#     return match_count if match_count > (number_of_match_limit - 1) else False


# def filter_winnings(combo, plays, prices, jackpot_amount):

#     const_obj = 2
#     occurences = map(
#         lambda user_play: search_number_occurences(combo, user_play, 3), plays
#     )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

#     play_occurences = zip(
#         occurences, plays
#     )  # Match number of occurences to number of matches found in selected combination
#     over3ocurrences = list(
#         filter(lambda x: x[0], play_occurences)
#     )  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

#     play_occurences_with_amount = map(
#         lambda played: get_potential_winning(played, prices, jackpot_amount),
#         over3ocurrences,
#     )
#     data = list(play_occurences_with_amount)

#     return data


# def get_potential_winning(data, prices, jackpot):
#     """
#     data[1][0] : number_of_lines
#     data[0] : number_of_matches
#     """

#     base_price = prices[data[1][0]]
#     sharing = {5: 1, 4: 1, 3: 0.6, 2: 0.6*0.5}

#     if data[0] == 5:
#         winning = jackpot

#     else:
#         winning = (
#             (base_price * sharing[data[0]])
#             if not sharing[data[0]] == 5
#             else jackpot
#         )

#     response = [*data, int(winning)]

#     return response

# then = datetime.datetime.now()
# contribution = 600000000
# best_combo = []
# best_combo_amount = 0
# winners = []
# combo_dict = {}

# def play(target_set, winnings_dict, plays):

#     print(len(target_set))
#     for index, combo in enumerate(target_set):

#         winnings = filter_winnings(combo, plays, prices, 9999999999)

#         check = index%10000

#         if check == 0 and index >= 10000:
#             print(index)

#         print(len(winnings))

#         if len(winnings) == 0:

#             t_amount = 0
#             winnings_dict[combo] = t_amount

#         else:
#             _, __, amount = zip(*winnings)
#             t_amount = sum(amount)
#             winnings_dict[str(combo)] = t_amount

#             # if t_amount > 1000000:
#             #     print(combo, ":", len(winnings), "Amount->", t_amount)

#         if combo == (19, 20, 33, 37, 49):
#             print(combo, ":", len(winnings))
#             pprint.pprint(winnings)


# def run_draw(plays, rtp, prices, jackpot_amount):
#     then = datetime.datetime.now()
#     step = 90000
#     jobs = []
#     random_combo = list(itertools.combinations(range(1, 20), 5))
#     manager = multiprocessing.Manager()
#     winnings_dict = manager.dict()

#     for i in range(0, 2000000, step):

#         proccess = multiprocessing.Process(target=play, args=(random_combo[i:i+step], winnings_dict, plays))
#         jobs.append(proccess)
#         proccess.start()

#     for proc in jobs:
#         proc.join()

#     pprint.pprint(dict(winnings_dict))

#     now = datetime.datetime.now()
#     print((now - then).total_seconds())

#     return dict(winnings_dict)


# @app.route('/')
# def hello_world():
#     winnings = run_draw(plays=plays, rtp=30000, prices=prices, jackpot_amount=9012100)
#     return json.dumps(winnings)

# if __name__=="__main__":

#     winnings = run_draw(plays=plays, rtp=30000, prices=prices, jackpot_amount=9012100)
#     print(winnings)


# # def find_closest_value(lst, target):
# #     lst.sort(key=lambda x:x[1])

# #     closest_combo = []
# #     closest_amount = -1

# #     for combo, amount in lst:

# #         if amount > closest_amount and target > amount:
# #             closest_combo = combo
# #             closest_amount = amount

# #     return closest_amount, closest_combo


# # vals = [[(41, 44, 47, 48, 49),300000],
# #         [(41, 44, 47, 48, 49),200000],
# #         [(41, 44, 47, 48, 49),260000],
# #         [(41, 44, 47, 48, 49),28000],
# #         [(41, 44, 47, 48, 49),280000],
# #         [(41, 44, 47, 48, 49),280000],
# #         [(41, 44, 47, 48, 49),360000],
# #         [(41, 44, 47, 48, 49),200000],
# #         [(41, 44, 47, 48, 49),0]
# #         ]
# # x = find_closest_value(vals, 1000)

# # print(x)


x = [
    [2, [1, [21, 16, 30, 40, 14]], 1500],
    [3, [1, [28, 16, 41, 30, 20]], 3000],
    [2, [1, [30, 26, 16, 49, 19]], 1500],
    [2, [1, [43, 40, 37, 16, 24]], 1500],
    [2, [1, [30, 27, 6, 48, 46]], 1500],
    [2, [1, [28, 41, 6, 48, 4]], 1500],
    [2, [1, [24, 32, 2, 16, 27]], 1500],
    [2, [1, [31, 16, 18, 43, 28]], 1500],
    [3, [1, [6, 24, 28, 23, 15]], 3000],
    [3, [1, [30, 16, 9, 6, 3]], 3000],
    [2, [1, [12, 36, 28, 46, 6]], 1500],
    [2, [1, [18, 24, 45, 6, 20]], 1500],
    [2, [1, [21, 34, 30, 16, 11]], 1500],
    [2, [1, [6, 28, 45, 11, 12]], 1500],
    [2, [1, [26, 16, 41, 19, 24]], 1500],
    [2, [1, [25, 30, 19, 5, 24]], 1500],
    [2, [1, [16, 14, 8, 29, 30]], 1500],
    [2, [1, [20, 12, 30, 38, 6]], 1500],
    [2, [1, [24, 37, 6, 21, 32]], 1500],
    [2, [1, [28, 16, 7, 11, 40]], 1500],
    [2, [1, [22, 30, 40, 48, 24]], 1500],
    [2, [1, [31, 21, 20, 6, 16]], 1500],
    [2, [1, [30, 25, 14, 28, 48]], 1500],
    [3, [1, [44, 19, 16, 24, 6]], 3000],
    [2, [1, [43, 24, 16, 39, 19]], 1500],
    [3, [1, [38, 28, 16, 24, 35]], 3000],
    [2, [1, [14, 41, 24, 5, 16]], 1500],
    [2, [1, [25, 43, 30, 29, 24]], 1500],
    [2, [1, [14, 30, 6, 1, 23]], 1500],
    [2, [1, [28, 2, 26, 30, 42]], 1500],
    [2, [1, [30, 7, 8, 19, 28]], 1500],
    [2, [1, [30, 9, 36, 31, 28]], 1500],
    [2, [1, [28, 40, 16, 14, 10]], 1500],
    [3, [1, [31, 28, 24, 16, 1]], 3000],
    [2, [1, [46, 30, 24, 12, 7]], 1500],
    [2, [1, [30, 19, 8, 49, 16]], 1500],
    [3, [1, [39, 28, 21, 16, 24]], 3000],
    [2, [1, [20, 3, 16, 28, 41]], 1500],
    [2, [1, [12, 11, 33, 16, 6]], 1500],
    [2, [1, [39, 36, 16, 37, 28]], 1500],
    [2, [1, [6, 2, 39, 28, 4]], 1500],
    [2, [1, [24, 22, 16, 15, 41]], 1500],
    [3, [1, [35, 38, 24, 6, 16]], 3000],
    [2, [1, [28, 3, 5, 18, 30]], 1500],
    [2, [1, [6, 23, 10, 34, 28]], 1500],
    [2, [1, [28, 33, 20, 5, 16]], 1500],
    [2, [1, [5, 28, 6, 8, 44]], 1500],
    [3, [1, [14, 49, 24, 16, 28]], 3000],
    [4, [1, [28, 16, 30, 2, 24]], 5000],
    [2, [1, [30, 16, 18, 9, 10]], 1500],
    [2, [1, [16, 17, 45, 39, 24]], 1500],
    [2, [1, [4, 24, 15, 13, 6]], 1500],
    [2, [1, [28, 22, 45, 10, 30]], 1500],
    [3, [1, [34, 29, 28, 24, 30]], 3000],
    [2, [1, [19, 24, 35, 45, 16]], 1500],
    [2, [1, [30, 16, 10, 36, 29]], 1500],
    [2, [1, [21, 6, 34, 20, 24]], 1500],
    [2, [1, [24, 30, 2, 9, 20]], 1500],
    [2, [1, [28, 25, 6, 27, 41]], 1500],
    [2, [1, [28, 41, 40, 24, 8]], 1500],
    [2, [1, [6, 16, 34, 17, 43]], 1500],
    [2, [1, [15, 44, 33, 6, 24]], 1500],
    [2, [1, [28, 19, 21, 46, 6]], 1500],
    [2, [1, [28, 25, 10, 24, 31]], 1500],
    [2, [1, [38, 27, 24, 28, 4]], 1500],
    [2, [1, [6, 23, 18, 12, 16]], 1500],
    [2, [1, [30, 8, 28, 13, 17]], 1500],
    [2, [1, [20, 30, 25, 24, 29]], 1500],
    [3, [1, [20, 8, 24, 16, 28]], 3000],
    [2, [1, [16, 24, 48, 3, 19]], 1500],
    [2, [1, [48, 28, 30, 1, 47]], 1500],
    [2, [1, [24, 13, 30, 47, 35]], 1500],
    [2, [1, [27, 43, 31, 24, 6]], 1500],
    [2, [1, [1, 18, 16, 40, 28]], 1500],
    [2, [1, [2, 28, 45, 13, 24]], 1500],
    [2, [1, [2, 34, 16, 30, 41]], 1500],
    [2, [1, [24, 16, 33, 27, 39]], 1500],
    [4, [1, [16, 21, 28, 24, 6]], 5000],
    [2, [1, [40, 30, 28, 44, 19]], 1500],
    [3, [1, [28, 6, 19, 16, 39]], 3000],
    [3, [1, [36, 5, 16, 6, 30]], 3000],
    [2, [1, [30, 27, 15, 24, 2]], 1500],
    [2, [1, [40, 2, 30, 24, 11]], 1500],
    [2, [1, [7, 44, 49, 6, 16]], 1500],
    [2, [1, [30, 10, 31, 43, 28]], 1500],
    [2, [1, [48, 11, 40, 16, 24]], 1500],
    [2, [1, [15, 16, 11, 42, 24]], 1500],
    [2, [1, [22, 16, 36, 28, 42]], 1500],
    [2, [1, [17, 16, 25, 21, 6]], 1500],
    [2, [1, [17, 47, 28, 41, 24]], 1500],
    [2, [1, [16, 23, 24, 44, 17]], 1500],
    [2, [1, [12, 6, 4, 48, 24]], 1500],
    [2, [1, [14, 23, 28, 19, 30]], 1500],
    [2, [1, [28, 34, 38, 45, 16]], 1500],
]
y = 0

for i in x:
    y += i[2]
    print(i)


print(y)
