# Create your views here.
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from staff_commission.models import CommissionReceiverWalletTransaction


class CommissionInflowNotificationApiView(APIView):
    def post(self, request):

        return Response(
            {"message": "Inflow notification created successfully"},
            status=status.HTTP_201_CREATED,
        )
        amount = request.data.get("amount")
        transaction_reference = request.data.get("transaction_reference")

        if amount is None or transaction_reference is None:
            return Response(
                {"message": "amount and transaction_reference are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        CommissionReceiverWalletTransaction.inflow_notification(
            amount=amount,
            transaction_reference=transaction_reference,
        )

        return Response(
            {"message": "Inflow notification created successfully"},
            status=status.HTTP_201_CREATED,
        )
