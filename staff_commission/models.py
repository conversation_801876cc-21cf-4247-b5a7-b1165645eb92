import uuid

from django.db import models


class InflowNotification(models.Model):
    amount = models.FloatField(default=0.0)
    transaction_reference = models.CharField(max_length=300, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "INFLOW NOTIFICATION"
        verbose_name_plural = "INFLOW NOTIFICATIONS"


# Create your models here.
class CommissionReceiver(models.Model):
    full_name = models.CharField(max_length=300)
    phone_number = models.CharField(max_length=250)
    commission_percentage = models.FloatField(default=0.0)
    still_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "COMMISSION RECEIVER"
        verbose_name_plural = "COMMISSION RECEIVERS"


class CommissionReceiverWallet(models.Model):
    full_name = models.CharField(max_length=300)
    phone_number = models.CharField(max_length=250)
    account_number = models.CharField(max_length=300, blank=True, null=True)
    balance = models.FloatField(default=0.0)
    previous_balance = models.FloatField(default=0.0)
    amount_disbursed = models.FloatField(default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "COMMISSION RECEIVER WALLET"
        verbose_name_plural = "COMMISSION RECEIVER WALLETS"


class CommissionReceiverWalletTransaction(models.Model):
    SOURCE = [
        ("INFLOW", "INFLOW"),
        ("WITHDRAWAL", "WITHDRAWAL"),
        ("PAYOUT_REVERSAL", "PAYOUT_REVERSAL"),
    ]

    TYPE_OF_TRANSACTIONS = [
        ("CREDIT", "CREDIT"),
        ("DEBIT", "DEBIT"),
    ]
    full_name = models.CharField(max_length=300)
    phone_number = models.CharField(max_length=250)
    funding_amount = models.FloatField(default=0.0)
    amount = models.FloatField(default=0.0)
    balance = models.FloatField(default=0.0)
    previous_amount = models.FloatField(default=0.0)
    source = models.CharField(max_length=250, choices=SOURCE)
    type_of_transactions = models.CharField(max_length=250, choices=TYPE_OF_TRANSACTIONS)
    transaction_reference = models.CharField(max_length=300, unique=True)
    settled = models.BooleanField(default=False)
    payout_response_data = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "COMMISSION RECEIVER WALLET TRANSACTION"
        verbose_name_plural = "COMMISSION RECEIVER WALLET TRANSACTIONS"

    @classmethod
    def create_commission_reward_transaction(cls, funding_amount, reward_commission, receiver_instance: CommissionReceiver):
        try:
            user_wallet = CommissionReceiverWallet.objects.get(phone_number=receiver_instance.phone_number)
        except CommissionReceiverWallet.DoesNotExist:
            user_wallet = CommissionReceiverWallet.objects.create(full_name=receiver_instance.full_name, phone_number=receiver_instance.phone_number)

        previous_balance = user_wallet.balance
        user_wallet.balance += reward_commission
        user_wallet.previous_balance = previous_balance
        user_wallet.save()

        transaction_reference = str(uuid.uuid4())
        cls.objects.create(
            full_name=receiver_instance.full_name,
            phone_number=receiver_instance.phone_number,
            funding_amount=funding_amount,
            amount=reward_commission,
            balance=user_wallet.balance,
            previous_amount=previous_balance,
            source="INFLOW",
            type_of_transactions="CREDIT",
            transaction_reference=transaction_reference,
        )

        return transaction_reference

    @classmethod
    def inflow_notification(cls, amount, transaction_reference):
        try:
            InflowNotification.objects.create(amount=amount, transaction_reference=transaction_reference)
        except Exception as e:
            print(f"Error: {e}")
            return False

        active_commission_receivers = CommissionReceiver.objects.filter(still_active=True)
        if not active_commission_receivers.exists():
            return False

        for receiver in active_commission_receivers:
            funding_amount = amount
            reward_commission = round((receiver.commission_percentage / 100) * amount, 2)
            cls.create_commission_reward_transaction(funding_amount, reward_commission, receiver)
