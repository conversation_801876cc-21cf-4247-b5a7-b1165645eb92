from django.contrib import admin

# Register your models here.
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from staff_commission.models import (
    CommissionReceiver,
    CommissionReceiverWallet,
    CommissionReceiverWalletTransaction,
    InflowNotification,
)


class InflowNotificationResource(resources.ModelResource):
    class Meta:
        model = InflowNotification


class CommissionReceiverResource(resources.ModelResource):
    class Meta:
        model = CommissionReceiver


class CommissionReceiverWalletResource(resources.ModelResource):
    class Meta:
        model = CommissionReceiverWallet


class CommissionReceiverWalletTransactionResource(resources.ModelResource):
    class Meta:
        model = CommissionReceiverWalletTransaction


class InflowNotificationResourceAdmin(ImportExportModelAdmin):
    resource_class = InflowNotificationResource

    search_fields = ["transaction_reference"]
    list_filter = ("created_at",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CommissionReceiverResourceAdmin(ImportExportModelAdmin):
    resource_class = CommissionReceiverResource

    search_fields = ["full_name", "phone_number"]
    list_filter = ("created_at", "still_active")
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CommissionReceiverWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = CommissionReceiverWalletResource

    search_fields = ["full_name", "phone_number"]
    list_filter = ["created_at"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CommissionReceiverWalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = CommissionReceiverWalletTransactionResource

    search_fields = ["full_name", "phone_number"]
    list_filter = ["created_at"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(InflowNotification, InflowNotificationResourceAdmin)
admin.site.register(CommissionReceiver, CommissionReceiverResourceAdmin)
admin.site.register(CommissionReceiverWallet, CommissionReceiverWalletResourceAdmin)
admin.site.register(CommissionReceiverWalletTransaction, CommissionReceiverWalletTransactionResourceAdmin)
