from dataclasses import dataclass
from datetime import timedelta

import redis
import requests
from decouple import config


@dataclass
class LibertyPayHelper:
    def agent_login(self):
        # ---------- REDIS STORAGE ------------ #
        redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
        agency_user_token = redis_db.get("commission_agent_login_token")
        if agency_user_token is None:
            email = config("COMMISSION_AGENCY_BANKING_USEREMAIL")
            password = config("COMMISSION_AGENCY_BANKING_PASSWORD")

            url = f"{config('AGENCY_BANKING_BASE_URL')}/user/login/create/"

            payload = {
                "email": email,
                "password": password,
                "device_type": "MOBILE",
            }

            headers = {
                "Content-Type": "application/json",
            }

            try:
                response = requests.post(url, json=payload, headers=headers)
                res = response.json()
                token = res["access"]
                redis_db.set("commission_agent_login_token", token, ex=timedelta(days=1))

                return res

            except Exception:
                return self.agent_login()

        else:
            res = {"access": agency_user_token}
            return res

    def fetch_buddy_account(self, phone):
        url = f"{config('AGENCY_BANKING_BASE_URL')}/send/send_money_paybuddy/{phone}/"

        token = (self.agent_login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"commission_agent_login_token")

            return self.fetch_buddy_account(phone)

        """
        ERROR RESPONSE:
        {'status': 'error', 'message': 'Buddy Does Not Exist', 'invite_user': 'https://backend.libertypayng.com/invite_new_user/*************/'}


        CORRECT RESPONSE:
        {'message': 'buddy found', 'data': {'full_name': 'JOSEPH OGBU', 'phone_number': '*************'}}

        """

        try:
            return response.json()
        
        except Exception:
            return response.text

    def reward_commission(self, phone, reference, funding_amount, commission_amount):
        url = f"{config('AGENCY_BANKING_BASE_URL')}/accounts/give_commision_from_me/"

        token = (self.agent_login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        print("headers", headers, "\n\n")

        payload = {
            "transaction_ref": reference,
            "recipient": phone,
            "amount": funding_amount,
            "profit": commission_amount,
            "transaction_type": "WINWISE_LOTTO_COMMISSIONS",
            "transaction_reason": "WINWISE LOTTO DISBURSEMENT COMMISSION",
        }

        response = requests.post(url, headers=headers, json=payload)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"commission_agent_login_token")

            return self.reward_commission(phone, reference, funding_amount, commission_amount)

        try:
            return response.json()
        except Exception:
            return response.text

    def liberty_pay_vfd_account_enquiry(self, account_number=None):
        """
        Enquire VFD account details
        """

        token = (self.agent_login())["access"]

        if account_number is None:
            url = f"{config('AGENCY_BANKING_BASE_URL')}/accounts/get_other_accounts/"
        else:
            url = f"{config('AGENCY_BANKING_BASE_URL')}/accounts/get_other_accounts/?account_num={account_number}/"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"commission_agent_login_token")

            return self.liberty_pay_vfd_account_enquiry(account_number)
        
        

        """
        SAMPLE RESPONSES:
        {'user_id': 00001926888000000000000, 'account_provider': 'VFD', 'account_number': '111111111111111111111', 'account_name': 'Okoro Samson',
        'bank_name': 'VFD Microfinance Bank',
        'bank_code': '999999', 'is_test': False, 'is_active': True, 'available_balance': 15
        }

        """
        try:
            return response.json()
        except Exception:
            return {}
