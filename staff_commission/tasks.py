import time
import uuid

from celery import shared_task

from staff_commission.helpers.agency_banking_helper import LibertyPayHelper
from staff_commission.models import (
    CommissionReceiverWallet,
    CommissionReceiverWalletTransaction,
)


@shared_task
def settle_commissiom():
    queryset = CommissionReceiverWalletTransaction.objects.filter(settled=False).exclude(source__in=["WITHDRAWAL", "PAYOUT_REVERSAL"], settled=False)

    for instance in queryset:
        reference = f"{time.time()}-{uuid.uuid4()}"

        trans_instance = CommissionReceiverWalletTransaction.objects.create(
            full_name=instance.full_name,
            phone_number=instance.phone_number,
            funding_amount=instance.funding_amount,
            amount=round(instance.amount, 2),
            balance=round(instance.balance, 2) - round(instance.amount, 2),
            previous_amount=round(instance.balance, 2),
            source="WITHDRAWAL",
            type_of_transactions="DEBIT",
            transaction_reference=reference,
        )

        instance.settled = True
        instance.save()

        def format_phone_number(phone_number):
            formatted_num = phone_number[-10:]
            if formatted_num[0] == "0":
                return None
            else:
                return "234" + formatted_num

        phone_number = format_phone_number(instance.phone_number)

        commission_reward_response = LibertyPayHelper().reward_commission(phone_number, reference, instance.funding_amount, round(instance.amount, 2))

        trans_instance.payout_response_data = commission_reward_response
        trans_instance.save()

        if isinstance(commission_reward_response, dict):
            errors = commission_reward_response.get("errors")
            if errors is not None:
                if "sufficient" in errors:
                    instance.settled = False
                    instance.save()

                    reference = f"{time.time()}-{uuid.uuid4()}"

                    CommissionReceiverWalletTransaction.objects.create(
                        full_name=instance.full_name,
                        phone_number=instance.phone_number,
                        funding_amount=instance.funding_amount,
                        amount=round(instance.amount, 2),
                        balance=round(instance.balance, 2) + round(instance.amount, 2),
                        previous_amount=round(instance.balance, 2),
                        source="PAYOUT_REVERSAL",
                        type_of_transactions="CREDIT",
                        transaction_reference=reference,
                    )

                    break

                else:
                    wallet = CommissionReceiverWallet.objects.get(phone_number=instance.phone_number)
                    previoud_balance = wallet.balance
                    wallet.balance -= round(instance.amount, 2)
                    wallet.previous_balance = previoud_balance
                    wallet.amount_disbursed += round(instance.amount, 2)
                    wallet.save()

        else:
            break
