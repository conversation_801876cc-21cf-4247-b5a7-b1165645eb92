import requests
from celery import shared_task

from liberty_lotto import settings
from sms_campaign.helpers.smart_sms import send_sms
from sms_campaign.models import Agents<PERSON>hone<PERSON><PERSON>ber, MessageDrip, MessageDripContent
from wyse_ussd.models import random_with_N_digits


# Schedule your task(s) here.
@shared_task
def update_contacts():
    libertypay_url = "https://backend.libertypayng.com/agency/get_all_phone_numbers"
    headers = {"Authorization": f"Bearer {settings.LIBERTYPAY_AGENTS_AUTH}"}
    payload = {}

    response = requests.request("GET", libertypay_url, headers=headers, data=payload)
    if response.status_code == 200:
        data = response.json()

        if data.get("status") == "success" and data.get("message") == "phone numbers retrieved successfully":
            contacts_queryset = AgentsPhoneNumber.objects.filter(name="LIBERTYPAY_AGENTS")
            if contacts_queryset.exists():
                old_contact_list = contacts_queryset.last().contacts.split(",")
                fetched_contact_list = [contact.get("phone_number") for contact in data.get("data")]
                new_contact_list = [phone for phone in fetched_contact_list if phone not in old_contact_list]

                if len(new_contact_list) >= 1:
                    MessageDrip.objects.create(
                        name=f"LIBERTYPAY_AGENTS_DRIP_{random_with_N_digits()[-6:]}",
                        contacts=f"{','.join(new_contact_list)},2349069090865",
                        interval=2,
                        duration=30,
                    )
                    contacts_queryset.update(contacts=",".join(fetched_contact_list))

                    print("\nMESSAGE DRIP    =====>     CONTACTS UPDATED SUCCESSFULLY\n")
                else:
                    print("\nMESSAGE DRIP    =====>     NO AVAILABLE CONTACTS\n")
            else:
                print("\nMESSAGE DRIP    =====>     NO AVAILABLE AGENTS\n")
        else:
            print("\nMESSAGE DRIP    =====>     UNABLE TO FETCH CONTACTS\n")


@shared_task
def send_dnd_drip(content, contacts, batch, sender):
    # Reduce chance(s) of spamming

    if contacts:
        smartsms_response = send_sms(message_content=content, recipients=contacts, batch_uuid=batch, sender=sender, routing=4)
        response = smartsms_response

        if response.get("success"):
            print("\nMESSAGE DRIP    =====>     SENT MESSAGE DRIP CONTENT\n")
        else:
            print("\nMESSAGE DRIP    =====>     MESSAGE DRIP CONTENT NOT SENT\n")
    else:
        print("\nMESSAGE DRIP    =====>     NO DND CONTACTS AVAILABLE\n")


@shared_task
def send_drip():
    # Get all active drip(s)
    drip_queryset = MessageDrip.objects.filter(is_active=True)
    if drip_queryset.exists():
        for drip in drip_queryset:
            if not drip.is_completed:
                contacts = drip.contacts
                dnd_contacts = drip.dnd_contacts
                sender = drip.sender
                batch = drip.batch_id

                # Get message drip content(s)
                message_queryset = MessageDripContent.objects.filter(batch_id=batch, is_sent=False)
                if message_queryset.exists():
                    message = message_queryset.last()

                    smartsms_response = send_sms(message_content=message.content, recipients=contacts, batch_uuid=batch, sender=sender, routing=3)
                    response = smartsms_response

                    if response.get("success"):
                        message.is_sent = True
                        message.save()
                        print("\nMESSAGE DRIP    =====>     SENT MESSAGE DRIP CONTENT\n")

                        send_dnd_drip.apply_async(
                            kwargs={"content": message.content, "contacts": dnd_contacts, "batch": batch, "sender": sender},
                            countdown=180,
                        )
                        check_dnd_numbers = response.get("data").get("dnd_numbers")
                        if check_dnd_numbers:
                            dnd_numbers_list = check_dnd_numbers.split(",")
                            drip_contact_list = contacts.split(",")
                            non_dnd_numbers_list = [contact for contact in drip_contact_list if contact not in dnd_numbers_list]
                            drip.contacts = ",".join(non_dnd_numbers_list)
                            if dnd_contacts:
                                dnd_contacts = f"{dnd_contacts},{check_dnd_numbers}"
                            else:
                                dnd_contacts = ",".join(dnd_numbers_list)
                            drip.save()
                        else:
                            print("\nMESSAGE DRIP    =====>     NO DND NUMBERS IN SENT CONTACTS\n")
                    else:
                        print("\nMESSAGE DRIP    =====>     MESSAGE DRIP CONTENT NOT SENT\n")
                else:
                    print("\nMESSAGE DRIP    =====>     NO MESSAGE DRIP CONTENT\n")
            else:
                drip.is_active = False
                drip.save()
                print("\nMESSAGE DRIP    =====>     DRIP IS COMPLETED\n")
    else:
        print("\nMESSAGE DRIP    =====>     NO ACTIVE DRIPS\n")
