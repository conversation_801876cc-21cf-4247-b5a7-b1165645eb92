import json
import uuid
from datetime import datetime, timedelta

import pytz
from django.db import models

from liberty_lotto import settings


# Create your model(s) here.
class SmsMonitoring(models.Model):
    pass


class EmailCampaignManager(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    external_id = models.CharField(max_length=225, null=True, blank=True)
    recipient = models.EmailField(unique=False)
    subject = models.CharField(max_length=225, verbose_name="email subject", default="subject")
    clicked = models.BooleanField(default=False, verbose_name="link clicked")
    accepted = models.BooleanField(default=False, verbose_name="email accepted")
    rejected = models.BooleanField(default=False, verbose_name="email rejected")
    delivered = models.BooleanField(default=False, verbose_name="email delivered")
    failed = models.BooleanField(default=False, verbose_name="email failed")
    opened = models.BooleanField(default=False, verbose_name="email opened")
    unsubscribed = models.BooleanField(default=False, verbose_name="email unsubscribed")
    complained = models.BooleanField(default=False, verbose_name="email complained")
    accepted_data = models.TextField(null=True, blank=True)
    clicked_data = models.TextField(null=True, blank=True)
    rejected_data = models.TextField(null=True, blank=True)
    delivered_data = models.TextField(null=True, blank=True)
    failed_data = models.TextField(null=True, blank=True)
    opened_data = models.TextField(null=True, blank=True)
    unsubscribed_data = models.TextField(null=True, blank=True)
    complained_data = models.TextField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.recipient}: {self.subject}"

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "EMAIL CAMPAIGN MANAGER"
        verbose_name_plural = "EMAIL CAMPAIGN MANAGERS"

    @staticmethod
    def create_campaign_record(payload):
        data = json.dumps(payload)
        event = payload.get("event-data").get("event")
        recipient = payload.get("event-data").get("recipient")
        external_id = payload.get("event-data").get("message").get("headers").get("message-id")
        subject = payload.get("event-data").get("message").get("headers").get("subject")

        if subject is None:
            subject = "No Subject"

        check_campaign = EmailCampaignManager.objects.filter(recipient=recipient, external_id=external_id)
        if check_campaign.exists():
            campaign = check_campaign.first()

            if event == "delivered":
                campaign.delivered = True
                campaign.delivered_data = data

            if event == "complained":
                campaign.complained = True
                campaign.complained_data = data

            if event == "clicked":
                campaign.clicked = True
                campaign.clicked_data = data

            if event == "opened":
                campaign.opened = True
                campaign.opened_data = data

            if event == "unsubscribed":
                campaign.unsubscribed = True
                campaign.unsubscribed_data = data

            campaign.save()
            return True

        if event == "accepted":
            EmailCampaignManager.objects.create(external_id=external_id, recipient=recipient, accepted=True, subject=subject, accepted_data=data)
        if event == "delivered":
            EmailCampaignManager.objects.create(external_id=external_id, recipient=recipient, delivered=True, subject=subject, delivered_data=data)
        if event == "failed":
            EmailCampaignManager.objects.create(external_id=external_id, recipient=recipient, failed=True, subject=subject, failed_data=data)
        if event == "rejected":
            EmailCampaignManager.objects.create(external_id=external_id, recipient=recipient, rejected=True, subject=subject, rejected_data=data)
        return True


class MessageDrip(models.Model):
    batch_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    name = models.CharField(max_length=125)
    sender = models.CharField(max_length=125, default="LibertyPay")
    contacts = models.TextField(null=True, blank=True)
    dnd_contacts = models.TextField(null=True, blank=True)
    interval = models.IntegerField(default=1, verbose_name="drip interval")  # DAYS
    duration = models.IntegerField(verbose_name="drip duration")  # DAYS
    is_active = models.BooleanField(default=False)
    count = models.IntegerField(default=0, verbose_name="drip count")
    created_at = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.name

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "MESSAGE DRIP"
        verbose_name_plural = "MESSAGE DRIPS"

    @property
    def is_completed(self):
        today = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        time_threshold = self.created_at + timedelta(days=self.duration)
        if today > time_threshold:
            return True
        return False


class MessageDripContent(models.Model):
    message_drip = models.ForeignKey(MessageDrip, on_delete=models.CASCADE)
    batch_id = models.CharField(max_length=125, null=True, blank=True)
    name = models.CharField(max_length=125)
    content = models.TextField()
    is_sent = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.name

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "MESSAGE DRIP CONTENT"
        verbose_name_plural = "MESSAGE DRIP CONTENTS"


class AgentsPhoneNumber(models.Model):
    name = models.CharField(max_length=125)
    contacts = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.name

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "AGENTS PHONE NUMBER"
        verbose_name_plural = "AGENTS PHONE NUMBERS"


class MessageCampaignManager(models.Model):
    batch_id = models.CharField(max_length=125)
    smartsms_response = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.batch_id

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "MESSAGE CAMPAIGN MANAGER"
        verbose_name_plural = "MESSAGE CAMPAIGN MANAGERS"


class SmsChargeWallet(models.Model):
    PROVIDER = (("WHISPER_SMS", "WHISPER_SMS"),)

    provider = models.CharField(max_length=125, choices=PROVIDER)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    previous_balance = models.DecimalField(max_digits=10, decimal_places=2)
    amount_sent_to_provider = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.provider

    class Meta:
        verbose_name = "SMS CHARGE WALLET"
        verbose_name_plural = "SMS CHARGE WALLETS"

    @classmethod
    def add_charge(cls, provider, amount):
        charge_wallet = cls.objects.filter(provider=provider)

        if amount < 0:
            return False

        if charge_wallet.exists():
            instance = charge_wallet.last()
            previous_balance = instance.amount
            _amount = previous_balance + int(amount)

            charge_wallet.update(amount=_amount, previous_balance=previous_balance)
        else:
            cls.objects.create(provider=provider, amount=amount, previous_balance=0)
