from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from liberty_lotto.settings import MAILGUN_WEBHOOK_SIGNING_KEY
from sms_campaign.helpers.email_campaign_manager import mailgun_webhook
from sms_campaign.models import EmailCampaignManager


# Create your view(s) here.
class MailgunAPIView(APIView):
    def post(self, request):
        data = request.data
        token = data.get("signature").get("token")
        timestamp = data.get("signature").get("timestamp")
        signature = data.get("signature").get("signature")

        verify = mailgun_webhook(key=MAILGUN_WEBHOOK_SIGNING_KEY, token=token, timestamp=timestamp, signature=signature)
        if verify:
            EmailCampaignManager.create_campaign_record(payload=data)
            return Response(data={"message": "Okay"}, status=status.HTTP_200_OK)

        return Response(data={"message": "Sender could not be verified."}, status=status.HTTP_200_OK)
