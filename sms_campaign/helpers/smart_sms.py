import json

import requests

from sms_campaign.models import MessageCampaignManager


def send_sms(message_content, recipients, batch_uuid, sender, routing):
    """
    Routing "3" works for DND & non-DND numbers.
    Routing "4" works for DND numbers.
    """
    smartsms_url = "https://app.smartsmssolutions.com/io/api/client/v1/sms/"

    smartsms_payload = {
        "token": "2SGKJWzesn0Q8dGaJT8PTvHb0sicOa5La5HDRW8drzYw6X5SCR",
        "sender": sender,
        "to": recipients,
        "message": message_content,
        "type": "0",
        "routing": routing,
        "ref_id": batch_uuid,
    }
    files = []
    headers = {}
    response = requests.request("POST", smartsms_url, headers=headers, data=smartsms_payload, files=files)
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == 1000:
            MessageCampaignManager.objects.create(batch_id=batch_uuid, smartsms_response=json.dumps(data))
            return {"success": True, "data": data}
        elif data.get("code") == 1002:
            MessageCampaignManager.objects.create(batch_id=batch_uuid, smartsms_response=json.dumps(data))
            return {"success": False, "data": {}}
    return {"success": False, "data": {}}
