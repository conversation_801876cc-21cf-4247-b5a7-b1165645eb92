from django.db.models.signals import post_save
from django.dispatch import receiver

from sms_campaign.helpers.drip_content import sms_content
from sms_campaign.models import MessageDrip, MessageDripContent
from wyse_ussd.models import random_with_N_digits


# Create your signal(s)
@receiver(post_save, sender=MessageDrip)
def activate_message_drip(sender, instance, created, **kwargs):
    if created:
        instance.is_active = True
        instance.save()


@receiver(post_save, sender=MessageDrip)
def create_message_drip_content(sender, instance, created, **kwargs):
    if created:
        for content in sms_content:
            MessageDripContent.objects.create(message_drip=instance, name=f"PROMO_CAMPAIGN_{random_with_N_digits()[-6:]}", content=content)


@receiver(post_save, sender=MessageDripContent)
def add_content_batch_id(sender, instance, created, **kwargs):
    if created:
        message_drip_instance = instance.message_drip
        instance.batch_id = message_drip_instance.batch_id
        instance.save()


@receiver(post_save, sender=MessageDripContent)
def increment_drip_count(sender, instance, created, **kwargs):
    if instance.is_sent:
        message_drip_instance_queryset = MessageDrip.objects.filter(batch_id=instance.batch_id)
        if message_drip_instance_queryset.exists():
            message_drip_instance = message_drip_instance_queryset.first()
            message_drip_instance.count += 1
            message_drip_instance.save()
