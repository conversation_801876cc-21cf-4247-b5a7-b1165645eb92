from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from sms_campaign.models import (
    AgentsPhoneNumber,
    EmailCampaignManager,
    MessageCampaignManager,
    MessageDrip,
    MessageDripContent,
    SmsChargeWallet,
)


# Register your model(s) here.
class AgentsPhoneNumberManagerResource(resources.ModelResource):
    class Meta:
        model = AgentsPhoneNumber


class EmailCampaignManagerResource(resources.ModelResource):
    class Meta:
        model = EmailCampaignManager


class MessageDripManagerResource(resources.ModelResource):
    class Meta:
        model = MessageDrip


class MessageDripContentManagerResource(resources.ModelResource):
    class Meta:
        model = MessageDripContent


class MessageCampaignManagerResource(resources.ModelResource):
    class Meta:
        model = MessageCampaignManager


class SmsChargeWalletResource(resources.ModelResource):
    class Meta:
        model = SmsChargeWallet


class AgentsPhoneNumberManagerResourceAdmin(ImportExportModelAdmin):
    resource_class = AgentsPhoneNumberManagerResource
    search_fields = ["name"]
    list_filter = ("name",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class EmailCampaignManagerResourceAdmin(ImportExportModelAdmin):
    resource_class = EmailCampaignManagerResource
    search_fields = [
        "recipient",
        "subject",
    ]
    list_filter = ("clicked", "accepted", "rejected", "delivered", "failed", "opened", "unsubscribed", "complained")
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MessageDripManagerResourceAdmin(ImportExportModelAdmin):
    resource_class = EmailCampaignManagerResource
    search_fields = ["name", "batch_id"]
    list_filter = ("is_active",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MessageDripContentManagerResourceAdmin(ImportExportModelAdmin):
    resource_class = EmailCampaignManagerResource
    search_fields = ["name", "batch_id"]
    list_filter = ("is_sent",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MessageCampaignManagerResourceAdmin(ImportExportModelAdmin):
    resource_class = MessageCampaignManagerResource
    search_fields = [
        "batch_id",
    ]
    list_filter = ("batch_id",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SmsChargeWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = SmsChargeWalletResource
    # search_fields = ["batch_id",]
    list_filter = ("provider",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(AgentsPhoneNumber, AgentsPhoneNumberManagerResourceAdmin)
admin.site.register(EmailCampaignManager, EmailCampaignManagerResourceAdmin)
admin.site.register(MessageDrip, MessageDripManagerResourceAdmin)
admin.site.register(MessageDripContent, MessageDripContentManagerResourceAdmin)
admin.site.register(MessageCampaignManager, MessageCampaignManagerResourceAdmin)
admin.site.register(SmsChargeWallet, SmsChargeWalletResourceAdmin)
