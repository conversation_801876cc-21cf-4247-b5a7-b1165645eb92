from django.db import models


# Create your models here.
class BookMarkLinks(models.Model):
    title = models.CharField(max_length=255)
    link = models.URLField()
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "BOOKMARK LINK"
        verbose_name_plural = "BOOKMARK LINKS"

    def __str__(self):
        return self.title
