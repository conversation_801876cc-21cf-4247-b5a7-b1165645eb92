from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from resources_app.models import BookMarkLinks

# Register your models here.


class BookMarkLinksResource(resources.ModelResource):
    class Meta:
        model = BookMarkLinks


class BookMarkLinksResourceAdmin(ImportExportModelAdmin):
    resource_class = BookMarkLinksResource
    search_fields = ["title", "link"]

    list_filter = [
        "created_at",
    ]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(BookMarkLinks, BookMarkLinksResourceAdmin)
