exclude: |
  (?x)^(
    .*/apps\.py|
    .*/management/|
    .*/migrations/|
    .*/tests/|
    .*/settings/|
    .*/celery\.py|
    .*/
  )

repos:
- repo: https://github.com/psf/black
  rev: 25.1.0
  hooks:
    - id: black
      language_version: python3

- repo: https://github.com/pycqa/flake8
  rev: 7.1.2
  hooks:
    - id: flake8
      args: ["--max-line-length=150", "--ignore=E501"]

- repo: https://github.com/pycqa/isort
  rev: 6.0.0
  hooks:
    - id: isort
      args: ["--profile=black"]

- repo: https://github.com/myint/autoflake
  rev: v2.3.1
  hooks:
    - id: autoflake
      args: ["--in-place", "--remove-unused-variables", "--remove-all-unused-imports"]



