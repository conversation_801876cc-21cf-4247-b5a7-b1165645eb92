import hashlib
import json

import requests
from django.db.models import Q
from rest_framework import serializers
from rest_framework.reverse import reverse

from pos_app.models import Agent, AgentWallet
from scratch_cards.helpers.api import paystack_account_name
from scratch_cards.helpers.utils import (
    format_date,
    format_phone_number,
    index_formatter,
)
from scratch_cards.models import TICKET_EARNING, GameShowLotteryTicket, PayOut, ScratchCard


class CreateScratchCardSerializer(serializers.Serializer):
    index = serializers.CharField(required=True)
    serial_skew = serializers.CharField(required=True)
    serial_number = serializers.CharField(required=True)
    earning = serializers.ChoiceField(required=True, choices=TICKET_EARNING)
    earning_amount = serializers.FloatField(required=True, min_value=0)
    pin = serializers.CharField(required=True)
    game_id = serializers.CharField(required=True)
    pick = serializers.CharField(required=True)
    result = serializers.CharField(required=True)


class MultipleCreateScratchCardSerializer(serializers.Serializer):
    data = serializers.ListSerializer(child=CreateScratchCardSerializer())

    def validate(self, attrs):
        if len(attrs["data"]) < 1:
            raise serializers.ValidationError({"message": "data cannot be empty"})
        return attrs


class GetScratchCardAmountSerializer(serializers.Serializer):
    # game_id = serializers.CharField(required=True)
    serial_number = serializers.CharField(required=True)
    pin = serializers.CharField(required=True)

    def validate(self, attrs):
        # game_id = attrs.get("game_id")
        serial_number = attrs.get("serial_number")
        hashed_pin = f'password{attrs.get("pin")}'
        pin = hashlib.md5(hashed_pin.encode()).hexdigest()

        ticket_instance = ScratchCard.get_unique_scratch_cards(ticket_number=serial_number)
        if not ticket_instance:
            raise serializers.ValidationError({"message": "invalid scratch card"})
        # scratch_card_instance = ticket_instance.filter(game_id=game_id, pin=pin).first()
        scratch_card_instance = ticket_instance.filter(pin=pin).first()
        if not scratch_card_instance:
            raise serializers.ValidationError({"message": "invalid scratch card"})

        attrs["status"] = "success"
        attrs["amount"] = scratch_card_instance.earning_amount
        return attrs


class ScratchCardWithdrawalSerializer(serializers.Serializer):
    # game_id = serializers.CharField(required=True)
    serial_number = serializers.CharField(required=True)
    pin = serializers.CharField(required=True)
    account_number = serializers.CharField(required=True)
    bank_code = serializers.CharField(required=True)
    phone_number = serializers.CharField(required=True)

    def validate(self, attrs):
        # game_id = attrs.get("game_id")
        serial_number = attrs.get("serial_number")
        hashed_pin = f'password{attrs.get("pin")}'
        pin = hashlib.md5(hashed_pin.encode()).hexdigest()
        account_number = attrs.get("account_number")
        bank_code = attrs.get("bank_code")
        phone_number = attrs.get("phone_number")

        # clean phone number
        if len(phone_number) < 11:
            raise serializers.ValidationError({"message": "incomplete phone number"})

        # get the scratch card object
        ticket_instance = ScratchCard.get_unique_scratch_cards(ticket_number=serial_number)
        if not ticket_instance:
            raise serializers.ValidationError({"message": "invalid scratch card"})

        # check if the scratch card objects has a game id and pin match
        # scratch_card_instance = ticket_instance.filter(game_id=game_id, pin=pin, earning="WON").first()
        scratch_card_instance = ticket_instance.filter(pin=pin, earning_amount__gt=0).first()
        if not scratch_card_instance:
            raise serializers.ValidationError({"message": "ticket lost"})
        if not scratch_card_instance.paid or not scratch_card_instance.sold:
            raise serializers.ValidationError({"message": "invalid scratch card"})
        if scratch_card_instance.claimed:
            raise serializers.ValidationError({"message": "scratched claimed already"})

        # get the account name of the payout winner
        get_account_name = paystack_account_name(account_number=account_number, bank_code=bank_code)
        if not get_account_name.get("status"):
            raise serializers.ValidationError({"message": get_account_name.get("message")})

        transaction_reference = PayOut.create_transaction_reference(
            game_id=scratch_card_instance.game_id, phone_number=phone_number, account_number=account_number
        )
        if not transaction_reference:
            raise serializers.ValidationError({"message": "payout exist"})

        scratch_card_instance.claimant_bank_code = bank_code
        scratch_card_instance.claimant_account_number = account_number
        scratch_card_instance.claimant_account_name = get_account_name.get("account_name")
        scratch_card_instance.claimant_phone_number = phone_number
        scratch_card_instance.claimed = True
        scratch_card_instance.save()

        PayOut.create_transaction(
            account_name=get_account_name.get("account_name"),
            account_number=account_number,
            bank_code=bank_code,
            game_id=scratch_card_instance.game_id,
            phone_number=phone_number,
            transaction_reference=transaction_reference,
            ticket=scratch_card_instance,
        )

        attrs["status"] = "success"
        attrs["amount"] = scratch_card_instance.earning_amount
        attrs["transaction_reference"] = transaction_reference
        attrs["scratch_card"] = scratch_card_instance
        attrs["bank_code"] = bank_code
        attrs["account_number"] = account_number
        attrs["account_name"] = get_account_name.get("account_name")

        return attrs


class NewScratchCardWithdrawalSerializer(serializers.Serializer):
    # game_id = serializers.CharField(required=True)
    serial_number = serializers.CharField(required=True)
    pin = serializers.CharField(required=True)
    account_number = serializers.CharField(required=True)
    bank_code = serializers.CharField(required=True)
    phone_number = serializers.CharField(required=True)

    def validate(self, attrs):
        # game_id = attrs.get("game_id")
        serial_number = attrs.get("serial_number")
        pin = attrs.get("pin")
        account_number = attrs.get("account_number")
        bank_code = attrs.get("bank_code")
        phone_number = attrs.get("phone_number")
        is_game_show = False

        # clean phone number
        if len(phone_number) < 11:
            raise serializers.ValidationError({"message": "incomplete phone number"})

        # get the scratch card object
        ticket_instance = ScratchCard.get_unique_scratch_cards(ticket_number=serial_number, phone_number=phone_number)
        if not ticket_instance:
            ticket_instance = GameShowLotteryTicket.objects.filter(
                    index=serial_number, player_phone_number=phone_number, claimed=False
                )
            if not ticket_instance:
                raise serializers.ValidationError({"message": "invalid scratch card"})
            is_game_show = True

        # check if the scratch card objects has a game id and pin match
        # scratch_card_instance = ticket_instance.filter(game_id=game_id, pin=pin, earning="WON").first()
        scratch_card_instance = ticket_instance.filter(pin=pin, earning_amount__gt=0).first()
        if not scratch_card_instance:
            raise serializers.ValidationError({"message": "ticket lost"})
        if not scratch_card_instance.paid or not scratch_card_instance.sold:
            raise serializers.ValidationError({"message": "invalid scratch card"})
        if scratch_card_instance.claimed:
            raise serializers.ValidationError({"message": "scratched claimed already"})

        # get the account name of the payout winner
        get_account_name = paystack_account_name(account_number=account_number, bank_code=bank_code)
        if not get_account_name.get("status"):
            raise serializers.ValidationError({"message": get_account_name.get("message")})

        transaction_reference = PayOut.create_transaction_reference(
            game_id=scratch_card_instance.game_id, phone_number=phone_number, account_number=account_number
        )
        if not transaction_reference:
            raise serializers.ValidationError({"message": "payout exist"})

        scratch_card_instance.claimant_bank_code = bank_code
        scratch_card_instance.claimant_account_number = account_number
        scratch_card_instance.claimant_account_name = get_account_name.get("account_name")
        scratch_card_instance.claimant_phone_number = phone_number
        scratch_card_instance.claimed = True
        scratch_card_instance.save()

        PayOut.create_transaction(
            account_name=get_account_name.get("account_name"),
            account_number=account_number,
            bank_code=bank_code,
            game_id=scratch_card_instance.game_id,
            phone_number=phone_number,
            transaction_reference=transaction_reference,
            ticket=scratch_card_instance,
            is_game_show=is_game_show
        )

        attrs["status"] = "success"
        attrs["amount"] = scratch_card_instance.earning_amount
        attrs["transaction_reference"] = transaction_reference
        attrs["scratch_card"] = scratch_card_instance
        attrs["bank_code"] = bank_code
        attrs["account_number"] = account_number
        attrs["account_name"] = get_account_name.get("account_name")

        return attrs


class SellScratchCardSerializer(serializers.Serializer):
    serial_skew = serializers.CharField(required=True)
    serial_number = serializers.CharField(required=True)

    def validate(self, attrs):
        serial_skew = attrs.get("serial_skew")
        serial_number = attrs.get("serial_number")

        get_scratch_card_obj = ScratchCard.get_scratch_card(serial_skew=serial_skew, serial_number=serial_number)
        if not get_scratch_card_obj:
            raise serializers.ValidationError({"message": "invalid scratch card"})

        get_scratch_card_obj.sold = True
        get_scratch_card_obj.save()

        attrs["status"] = "success"
        return attrs


class PayScratchCardSerializer(serializers.Serializer):
    serial_skew = serializers.CharField(required=True)
    serial_number = serializers.CharField(required=True)

    def validate(self, attrs):
        serial_skew = attrs.get("serial_skew")
        serial_number = attrs.get("serial_number")

        get_scratch_card_obj = ScratchCard.get_scratch_card(serial_skew=serial_skew, serial_number=serial_number)
        if not get_scratch_card_obj:
            raise serializers.ValidationError({"message": "invalid scratch card"})

        if not get_scratch_card_obj.sold:
            raise serializers.ValidationError({"message": "scratch card has not been sold"})

        get_scratch_card_obj.paid = True
        get_scratch_card_obj.save()

        attrs["status"] = "success"
        return attrs


# class ScratchCardPayOutSerializer(serializers.Serializer):
#     account_name = serializers.CharField(required=True)
#     account_number = serializers.CharField(required=True)
#     phone_number = serializers.CharField(required=True)
#     bank_code = serializers.CharField(required=True)
#     serial_number = serializers.CharField(required=True)
#     game_id = serializers.CharField(required=True)

#     def validate(self, attrs):
#         account_name = attrs.get("account_name")
#         account_number = attrs.get("account_number")
#         bank_code = attrs.get("bank_code")
#         game_id = attrs.get("game_id")
#         phone_number = attrs.get("phone_number")
#         serial_number = attrs.get("serial_number")

#         if len(phone_number) < 11:
#             raise serializers.ValidationError({"message": "incomplete phone number"})

#         ticket_instance = ScratchCard.get_unique_scratch_cards(ticket_number=serial_number)
#         if not ticket_instance:
#             raise serializers.ValidationError({"message": "invalid scratch card"})
#         scratch_card_instance = ticket_instance.filter(game_id=game_id).first()
#         if not scratch_card_instance:
#             raise serializers.ValidationError({"message": "invalid scratch card"})

#         transaction_reference = PayOut.create_transaction_reference(game_id=game_id,
#                                             phone_number=phone_number,
#                                             account_number=account_number)
#         if not transaction_reference:
#             raise serializers.ValidationError({"message": "payout exist"})


#         payout_transaction = PayOut.create_transaction(
#                 account_name = account_name,
#                 account_number = account_number,
#                 bank_code = bank_code,
#                 game_id = game_id,
#                 phone_number = phone_number,
#                 transaction_reference = transaction_reference,
#                 ticket = scratch_card_instance
#             )

#         attrs["transaction_reference"] = transaction_reference
#         attrs["transaction"] = payout_transaction

#         return attrs


class RetryFailedWithdrawalSerializer(serializers.Serializer):
    transaction_reference = serializers.CharField(required=True)

    def validate(self, attrs):
        transaction_reference = attrs.get("transaction_reference")
        ticket_instance = PayOut.reference_exists(transaction_reference=transaction_reference)
        if not ticket_instance:
            raise serializers.ValidationError({"message": "invalid transaction_reference"})
        return attrs


class ScratchCardSerializer(serializers.ModelSerializer):
    class Meta:
        model = ScratchCard
        fields = "__all__"


class ValidateTicketSerializer(serializers.Serializer):
    player_phone_number = serializers.CharField(required=True)
    index_numbers = serializers.ListField(
        required=True,
        # child=serializers.CharField()
    )

    def validate(self, attrs):
        player_phone_number = attrs.get("player_phone_number")
        index_numbers = attrs.get("index_numbers")

        if not all(isinstance(i, str) for i in index_numbers):
            raise serializers.ValidationError("Index numbers should be a list of strings.")

        for idx in index_numbers:
            try:
                formatted_index = index_formatter(idx)
            except Exception as err:
                raise serializers.ValidationError(f"{err}: {idx}")

            card = ScratchCard.objects.filter(index=formatted_index, serial_skew="S").first()
            if not card:
                raise serializers.ValidationError(f"Ticket does not exist: {idx}")

            if card.sold or card.paid:
                raise serializers.ValidationError(f"Ticket already sold out: {idx}")

        try:
            player_phone_number = format_phone_number(player_phone_number)
        except Exception as err:
            raise serializers.ValidationError(str(err))

        attrs["len_of_index"] = len(index_numbers)
        return attrs


class BuyTicketSerializer(serializers.Serializer):
    player_phone_number = serializers.CharField(required=True)
    index_numbers = serializers.ListField(
        required=True,
        # child=serializers.CharField()
    )
    user_id = serializers.CharField(required=True)
    email = serializers.EmailField(required=True)
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)
    phone = serializers.CharField(required=True)
    pin = serializers.CharField(min_length=4, max_length=4, required=True)
    # amount = serializers.FloatField(required=True)
    address = serializers.CharField(required=True)
    user_uuid = serializers.CharField(required=True)

    def validate(self, attrs):
        request = self.context.get("request")
        email = attrs.get("email")
        first_name = attrs.get("first_name")
        last_name = attrs.get("last_name")
        phone = attrs.get("phone")
        player_phone_number = attrs.get("player_phone_number")
        index_numbers = attrs.get("index_numbers")
        user_id = attrs.get("user_id")
        address = attrs.get("address")
        user_uuid = attrs.get("user_uuid")

        ticket_objects = []
        if not all(isinstance(i, str) for i in index_numbers):
            raise serializers.ValidationError("Index numbers should be a list of strings.")

        for idx in index_numbers:
            try:
                formatted_index = index_formatter(idx)
            except Exception as err:
                raise serializers.ValidationError(f"{err}: {idx}")

            card = ScratchCard.objects.filter(index=formatted_index, serial_skew="S").first()
            if not card:
                raise serializers.ValidationError(f"Ticket does not exist: {idx}")

            if card.sold or card.paid:
                raise serializers.ValidationError(f"Ticket already sold out: {idx}")

            ticket_objects.append(card)

        try:
            player_phone_number = format_phone_number(player_phone_number)
        except Exception as err:
            raise serializers.ValidationError(str(err))

        try:
            agent = Agent.objects.get(user_id=user_id)
            agent_wallet = AgentWallet.objects.filter(agent=agent).last()
            if not agent_wallet:
                agent_wallet = AgentWallet.objects.create(
                    agent=agent,
                    agent_name=f"{agent.first_name} {agent.last_name}",
                    agent_phone_number=agent.phone,
                    agent_email=agent.email,
                )

            attrs["agent"] = agent
        except Agent.DoesNotExist:
            agent_record_url = reverse("create_agent_record", request=request)
            headers = {"Content-Type": "application/json"}
            payload = json.dumps(
                {
                    "address": address,
                    "user_id": user_id,
                    "phone": phone,
                    "email": email,
                    "first_name": first_name,
                    "last_name": last_name,
                    "user_uuid": user_uuid,
                }
            )
            try:
                response = requests.request("POST", agent_record_url, headers=headers, data=payload)
                if response.status_code == 201:
                    agent = Agent.objects.filter(Q(phone=phone) | Q(email=email) | Q(user_id=user_id)).first()
                    if not agent:
                        raise serializers.ValidationError("Agent created but not found, please try again")

                    agent_wallet = AgentWallet.objects.filter(agent=agent).last()
                    if not agent_wallet:
                        agent_wallet = AgentWallet.objects.create(
                            agent=agent,
                            agent_name=f"{agent.first_name} {agent.last_name}",
                            agent_phone_number=agent.phone,
                            agent_email=agent.email,
                        )

                    attrs["agent"] = agent

                else:
                    raise serializers.ValidationError("Agent does not exist and was unable to create agent.")

            except requests.exceptions.RequestException:
                raise serializers.ValidationError("Error creating agent")

            # first_name = attrs.get("first_name")
            # last_name = attrs.get("last_name")
            # email = attrs.get("email")
            # address = attrs.get("address")
            # user_uuid = attrs.get("user_uuid")
            # try:
            #     checks_to_create_agent(
            #         user_id=user_id,
            #         first_name=first_name,
            #         last_name=last_name,
            #         email=email,
            #         address=address
            #     )
            # except ValueError as err:
            #     raise serializers.ValidationError(err)

            # try:
            #     UserProfile.objects.create(
            #         phone_number=agent_phone,
            #         email=email,
            #         first_name=first_name,
            #         last_name=last_name,
            #         channel="MOBILE",
            #     )

            # except IntegrityError:
            #     pass

            # try:
            #     User.objects.create(
            #         email=email,
            #         phone=phone,
            #         password=make_password(user_uuid),
            #         first_name=first_name,
            #         last_name=last_name,
            #         channel="MOBILE",
            #         phone_is_verified=True,
            #     )

            # except IntegrityError:
            #     pass

            # payload = json.dumps(self.validated_data)
            # CreateAgentLogs.objects.create(phone=phone, payload=payload)

        attrs["len_of_index"] = len(index_numbers)
        attrs["player_phone_number"] = player_phone_number
        attrs["scratch_cards"] = ticket_objects

        return attrs


class WeeklyWinnerSerializer(serializers.Serializer):
    winner_phone = serializers.CharField()
    index_number = serializers.CharField()
    week_date = serializers.CharField()

    def validate(self, attrs):
        week_date = attrs.get("week_date")
        index_number = attrs.get("index_number")
        winner_phone = attrs.get("winner_phone")

        try:
            phone = format_phone_number(winner_phone)
        except Exception as err:
            raise serializers.ValidationError(err)

        try:
            valid_date = format_date(date_str=week_date)
        except Exception as err:
            raise serializers.ValidationError(str(err))

        if valid_date.weekday() != 4:  # 4 means Friday
            raise serializers.ValidationError("The week date must be a Friday.")

        # last_winner = WeeklyWinners.objects.all().order_by('-week_date').first()
        # if last_winner:

        #     date_diff = (valid_date - last_winner.week_date).days
        #     if date_diff < 7:
        #         raise serializers.ValidationError(f"The date must be at least 7 days after winning week -> {last_winner.week_date}.")
        #         # Ensuring date below a week from the last one isn't allowed

        #     today = datetime.date.today()
        #     min_allowed_date = last_winner.week_date + datetime.timedelta(days=7)

        #     if today < min_allowed_date:
        #         raise serializers.ValidationError(f"Today must be at least 7 days after the last winner's date -> ({last_winner.week_date}).")
        #         # Ensuring the date of initiating this call is at least 7days from the last one

        # ADD CHECK TO ENSURE PRESENT DATE IS NOT LESS THEN THE WEEK DATE PASSED
        try:
            ticket = ScratchCard.objects.get(index=index_number)
            if not ticket.sold:
                raise serializers.ValidationError("Index nnumber not sold out yet.")

            if ticket.player_phone_number != phone:
                raise serializers.ValidationError("Index number not for phone number supplied")

        except ScratchCard.DoesNotExist:
            raise serializers.ValidationError(f"Ticket with Index Number <{index_number}> Does Not Exist")

        attrs["ticket"] = ticket
        attrs["week_date"] = valid_date
        return attrs
    

class ScratchCardBriefSerializer(serializers.Serializer):
    serial_number = serializers.CharField()
    index = serializers.CharField()
    player_phone_number = serializers.CharField()

class WeeklyWinnersGroupedSerializer(serializers.Serializer):
    created_at = serializers.DateTimeField()
    tickets = ScratchCardBriefSerializer(many=True)
    

# Are the first 10000 tickets generated only for a week?
# Will the draw numbers also be attched to a week?
# Tickets should be mixed and sold such that you can buy week one winning ticket in week 5 and you can also buy week 3 ticket in week 2.
# what does the above mean?

# Matching System – A 5-digit match logic can be implemented to increase suspense 4/5 for weekly winner and 5/5 for game-show winner.


# For Joe
# Is commision going into the same wallet and using the same tranactions?Ask again tomorrow.

class BuyGoldenHourTicketSerializer(serializers.Serializer):
    """
    Serializer for purchasing Golden Hour tickets
    """
    player_phone_number = serializers.CharField(required=True, help_text="Player's phone number")
    tickets = serializers.ListField(
        required=True,
        child=serializers.CharField(),
        help_text="List of tickets in format: index-ticket_number (e.g., ['1-A1B2C3D4E5F', '2-K7M9P2Q8X1Y'])"
    )

    def validate_tickets(self, value):
        """Validate ticket format and availability"""
        if not isinstance(value, list):
            raise serializers.ValidationError("Tickets must be a list")

        if not value:
            raise serializers.ValidationError("Tickets list cannot be empty")

        if len(value) > 50:  # Reasonable limit
            raise serializers.ValidationError("Cannot purchase more than 50 tickets at once")

        validated_tickets = []

        for ticket in value:
            # Validate format: index-ticket_number
            if '-' not in ticket:
                raise serializers.ValidationError(f"Invalid ticket format: {ticket}. Expected format: index-ticket_number")

            try:
                index_str, ticket_number = ticket.split('-', 1)
                ticket_index = int(index_str)
            except ValueError:
                raise serializers.ValidationError(f"Invalid ticket format: {ticket}. Index must be a number")

            # Validate ticket number format (11 alphanumeric characters, uppercase letters and digits)
            if not ticket_number or len(ticket_number) != 11:
                raise serializers.ValidationError(f"Invalid ticket number: {ticket_number}. Must be 11 characters long")

            if not ticket_number.isalnum() or not ticket_number.isupper():
                raise serializers.ValidationError(f"Invalid ticket number: {ticket_number}. Must contain only uppercase letters and numbers")

            # Check if ticket already exists in database
            from scratch_cards.models import GoldenHourTicket
            if GoldenHourTicket.objects.filter(ticket_index=ticket_index).exists():
                raise serializers.ValidationError(f"Ticket with index {ticket_index} already exists")

            if GoldenHourTicket.objects.filter(ticket_number=ticket_number).exists():
                raise serializers.ValidationError(f"Ticket with number {ticket_number} already exists")

            validated_tickets.append({
                'ticket_index': ticket_index,
                'ticket_number': ticket_number,
                'original': ticket
            })

        return validated_tickets

    def validate_player_phone_number(self, value):
        """Validate and format phone number"""
        from scratch_cards.helpers.utils import format_phone_number

        try:
            formatted_phone = format_phone_number(value)
            return formatted_phone
        except Exception as err:
            raise serializers.ValidationError(str(err))