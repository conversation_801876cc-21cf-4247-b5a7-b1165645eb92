import json

import requests
from celery import shared_task
from django.conf import settings

USE_DEBUG_MODE = settings.DEBUG


@shared_task
def verify_scratch_card_payout():
    from scratch_cards.helpers.api import wema_login
    from scratch_cards.models import PayOut

    filtered_transaction = ["PENDING", "FAILED"]
    bearer_token = wema_login()
    access_token = bearer_token.get("access_token")
    if bearer_token:
        pending_transactions = PayOut.objects.filter(status__in=filtered_transaction)
        for transaction in pending_transactions:
            if not USE_DEBUG_MODE:
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                }
                url = f"https://banking.libertypayng.com/accounts/verify_transfer?search={transaction.transaction_reference}"

                response = requests.request("GET", url, headers=headers)
                payout_response = response.json()
                # print(payout_response, "VERIFY\n")

                if isinstance(payout_response, dict):
                    data = payout_response
                    transaction.response = data
                    try:
                        transaction_data = data.get("data", {}).get("transactions", {})[0]
                        if transaction_data.get("transaction_status") == "SUCCESSFUL":
                            transaction_status = "SUCCESSFUL"
                        elif transaction_data.get("transaction_status") == "PENDING" or transaction_data.get("transaction_status") == "IN_PROGRESS":
                            transaction_status = "PENDING"
                        else:
                            transaction_status = "FAILED"

                        transaction.status = transaction_status
                        transaction.save()
                    except IndexError:
                        pass

                else:
                    pass
            else:
                transaction.response = {
                    "status": "success",
                    "status_code": 200,
                    "data": {
                        "transactions": [
                            {
                                "id": "1d003fcc-ddf1-444e-bd76-8799befa430b",
                                "created_at": "2024-06-12T12:10:22.886183+01:00",
                                "updated_at": "2024-06-12T12:10:22.886225+01:00",
                                "beneficiary_account_number": "**********",
                                "beneficiary_account_name": "Augustine Chukwudi Jibunoh",
                                "reference": "BgnicIPV20240601ksxkf45f",
                                "amount": 200,
                                "fee": 0,
                                "amount_payable": 200,
                                "transaction_status": "SUCCESSFUL",
                                "session_id": "000017240601184056621863766097",
                                "bank_code": "000013",
                                "bank_name": "",
                                "source_account": "**********",
                                "service_provider": "WEMA_BANK",
                                "transaction_type": "DEBIT",
                                "narration": "confirm payments",
                                "is_verified": True,
                                "mode": "LIVE",
                                "company_reference": "00fb75cc-8627-4b62-8dfd-2b042bdb61b0",
                                "currency": "NGN",
                                "company": "Sparky Pythoneer Official",
                                "sub_company": "",
                            }
                        ]
                    },
                    "errors": "",
                }
                transaction.status = "SUCCESSFUL"
                transaction.save()
    else:
        pass
    return "TRANSACTION VERIFICATION"


@shared_task
def send_whisper_sms(message: str, user_phone: str):
    url = "https://whispersms.xyz/transactional/send"
    headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }
    payload = json.dumps(
        {
            "receiver": f"{user_phone}",
            "template": "4e08c548-54ba-453e-8cad-4876713a3f05",
            "place_holders": {"message": message},
        }
    )
    whisper_resp = requests.request("POST", url, headers=headers, data=payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response
