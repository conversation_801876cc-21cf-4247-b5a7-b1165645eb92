import random
import string
import time

from django.db import models, transaction
from django.utils import timezone

from pos_app.models import Agent

# Create your models here.
TICKET_EARNING = [
    ("LOST", "lost"),
    ("WON", "won"),
]

PAYOUT_STATUS = [
    ("PENDING", "pending"),
    ("SUCCESSFUL", "successful"),
    ("FAILED", "failed"),
]


class ScratchCard(models.Model):
    agent = models.ForeignKey(Agent, related_name="scratch_card_agents", on_delete=models.SET_NULL, null=True, blank=True)
    index = models.CharField(max_length=100, null=True, blank=True)
    serial_skew = models.CharField(max_length=100, null=True, blank=True)
    serial_number = models.CharField(max_length=100, null=True, blank=True)
    serial_number_int = models.IntegerField(default=0)
    earning = models.CharField(max_length=50, null=True, blank=True, default="LOST")
    earning_amount = models.FloatField(null=True, blank=True, default=0.0)
    pin = models.CharField(max_length=100, null=True, blank=True)
    game_id = models.CharField(max_length=150, null=True, blank=True)
    pick = models.TextField(null=True, blank=True)
    result = models.TextField(null=True, blank=True)
    paid = models.BooleanField(default=False)
    sold = models.BooleanField(default=False)
    ticket_amount_paid = models.FloatField(null=True, blank=True, default=0.0)
    claimant_account_name = models.CharField(max_length=255, null=True, blank=True)
    claimant_account_number = models.CharField(max_length=50, null=True, blank=True)
    claimant_bank_code = models.CharField(max_length=100, null=True, blank=True)
    claimant_phone_number = models.CharField(max_length=100, null=True, blank=True)
    player_phone_number = models.CharField(max_length=100, null=True, blank=True)
    claimed = models.BooleanField(default=False)
    winning_status = models.CharField(max_length=100, null=True, blank=True)
    drawn = models.BooleanField(default=False)
    week_drawn = models.DateField(null=True, blank=True)
    purchased_at = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return str(self.ticket_number)

    @property
    def ticket_number(self) -> str:
        return f"{self.serial_skew}{self.serial_number}"

    def save(self, *args, **kwargs):
        self.serial_number_int = self.serial_number
        super(ScratchCard, self).save(*args, **kwargs)

    class Meta:
        unique_together = ("serial_skew", "serial_number", "index", "game_id")
        verbose_name = "SCRATCH CARD"
        verbose_name_plural = "SCRATCH CARDS"

    @classmethod
    def create_scratch_card(cls, data):
        for item in data:
            index = item["index"]
            serial_skew = item["serial_skew"]
            serial_number = item["serial_number"]
            earning = item["earning"]
            earning_amount = item["earning_amount"]
            pin = item["pin"]
            game_id = item["game_id"]
            pick = item["pick"]
            result = item["result"]

            cls.objects.create(
                index=index,
                serial_skew=serial_skew,
                serial_number=serial_number,
                earning=earning,
                earning_amount=earning_amount,
                pin=pin,
                game_id=game_id,
                pick=pick,
                result=result,
            )

    @classmethod
    def get_unique_scratch_cards(cls, ticket_number=None, phone_number=None):
        """
        This method returns a unique scratch card queryset for the given ticket number
        combining the serial_skew and serial_number
        """
        serial_skew = "".join(filter(str.isalpha, ticket_number))
        serial_number = "".join(filter(str.isdigit, ticket_number))

        if phone_number is None:
            queryset = ScratchCard.objects.filter(serial_skew=serial_skew, serial_number=serial_number)
        else:
            queryset = ScratchCard.objects.filter(serial_skew=serial_skew, serial_number=serial_number, player_phone_number=phone_number)

        return queryset

    @classmethod
    def get_scratch_card(cls, serial_skew, serial_number):
        """
        This method returns the scratch card object for the given serial skew
        and serial number, returns None is not found
        """
        scratch_card = cls.objects.filter(serial_skew=serial_skew, serial_number=serial_number).first()
        return scratch_card

    @classmethod
    def separate_letters_and_numbers(cls, input_string):
        letters = "".join(char for char in input_string if char.isalpha())
        numbers = "".join(char for char in input_string if char.isdigit())
        return letters, numbers


class PayOut(models.Model):
    ticket = models.OneToOneField(ScratchCard, unique=True, on_delete=models.CASCADE, related_name="scratch_card_winning", null=True, blank=True)
    game_show_ticket = models.ForeignKey("GameShowLotteryTicket", on_delete=models.CASCADE, related_name="game_show_winning", null=True, blank=True)
    game_id = models.CharField(max_length=100, null=True, blank=True)
    amount = models.FloatField(null=True, blank=True, default=0.0)
    phone_number = models.CharField(max_length=100, null=True, blank=True)
    account_name = models.CharField(max_length=255, null=True, blank=True)
    account_number = models.CharField(max_length=50, null=True, blank=True)
    bank_code = models.CharField(max_length=100, null=True, blank=True)
    transaction_reference = models.CharField(max_length=200, null=True, blank=True, unique=True)
    request = models.TextField(null=True, blank=True)
    response = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=50, null=True, blank=True, choices=PAYOUT_STATUS, default="PENDING")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SCRATCH CARD PAYOUT"
        verbose_name_plural = "SCRATCH CARD PAYOUTS"

    @classmethod
    def reference_exists(cls, transaction_reference):
        reference_exists = cls.objects.filter(transaction_reference=transaction_reference).exists()
        return reference_exists

    @classmethod
    def create_transaction_reference(cls, game_id, phone_number, account_number) -> str:
        current_time_stamp = int(time.time())
        formed_reference = f"{game_id}{phone_number}{account_number}-{current_time_stamp}"
        get_reference = cls.reference_exists(transaction_reference=formed_reference)
        if not get_reference:
            reference = formed_reference
        else:
            reference = None

        return reference

    @classmethod
    def create_transaction(cls, account_name, account_number, bank_code, game_id, phone_number, transaction_reference, ticket, is_game_show=False):
        if not is_game_show:
            transaction = cls.objects.create(
                account_name=account_name,
                account_number=account_number,
                bank_code=bank_code,
                game_id=game_id,
                phone_number=phone_number,
                transaction_reference=transaction_reference,
                ticket=ticket,
                amount=ticket.earning_amount,
            )
            return transaction
        else:
            transaction = cls.objects.create(
                account_name=account_name,
                account_number=account_number,
                bank_code=bank_code,
                game_id=game_id,
                phone_number=phone_number,
                transaction_reference=transaction_reference,
                game_show_ticket=ticket,
                amount=ticket.earning_amount,
            )
            return transaction

    @classmethod
    def retry_transaction(cls, transaction_reference):
        from scratch_cards.helpers.api import send_scratch_card_payout

        TRANSACTION_STATUS = ["PENDING", "FAILED"]
        transaction_ins = cls.objects.filter(transaction_reference=transaction_reference, status__in=TRANSACTION_STATUS).first()
        if not transaction_ins:
            payout = {
                "status": "failed",
                "status_code": 400,
                "data": {"status": False, "message": "payout is already successful", "transaction": None},
                "errors": None,
            }
        else:
            with transaction.atomic():
                if "-" in transaction_reference:
                    current_time_stamp = int(time.time())
                    base_reference = transaction_reference.split("-")[0]
                    new_reference = f"{base_reference}-{current_time_stamp}"
                    transaction_ins.transaction_reference = new_reference
                    transaction_ins.save()
                    payout = send_scratch_card_payout(transaction_reference=new_reference)

                else:
                    current_time_stamp = int(time.time())
                    new_reference = f"{transaction_reference}-{current_time_stamp}"
                    transaction_ins.transaction_reference = new_reference
                    transaction_ins.save()
                    payout = send_scratch_card_payout(transaction_reference=new_reference)

        return payout


class ScratchCardAgents(models.Model):
    first_name = models.CharField(max_length=100, null=True, blank=True)
    last_name = models.CharField(max_length=100, null=True, blank=True)
    date_of_birth = models.DateField()
    nationality = models.CharField(max_length=100, null=True, blank=True)
    phone_Number = models.CharField(max_length=100, null=True, blank=True)
    whatsapp_phone_Number = models.CharField(max_length=100, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    house_number = models.CharField(max_length=100, null=True, blank=True)
    street = models.CharField(max_length=100, null=True, blank=True)
    landmark = models.CharField(max_length=100, null=True, blank=True)
    bus_stop = models.CharField(max_length=100, null=True, blank=True)
    lga = models.CharField(max_length=100, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    account_name = models.CharField(max_length=100, null=True, blank=True)
    account_number = models.CharField(max_length=100, null=True, blank=True)
    bank_name = models.CharField(max_length=100, null=True, blank=True)
    nok_name = models.CharField(max_length=100, null=True, blank=True)
    nok_phone_number = models.CharField(max_length=100, null=True, blank=True)
    nok_address = models.CharField(max_length=100, null=True, blank=True)
    nok_relationship = models.CharField(max_length=100, null=True, blank=True)
    nin = models.CharField(max_length=100, null=True, blank=True)
    sales_manager = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SCRATCH CARD AGENT"
        verbose_name_plural = "SCRATCH CARD AGENTS"


class WeeklyWinners(models.Model):
    week_date = models.DateField()  # Date representing the Friday of the week
    ticket = models.ForeignKey(ScratchCard, on_delete=models.CASCADE, related_name="weekly_winners")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Winner: {self.ticket.player_phone_number}, Date: {self.week_date}"

    class Meta:
        constraints = [models.UniqueConstraint(fields=["week_date", "ticket"], name="unique_weekly_winner")]


class CountAgentTickets(models.Model):
    agent = models.OneToOneField("pos_app.Agent", on_delete=models.CASCADE, unique=True)
    counts = models.IntegerField(default=0)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    # created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.agent.full_name} - {self.counts} tickets"

    class Meta:
        verbose_name = "Count Agent Tickets"
        verbose_name_plural = "Count Agent Tickets"


class Variables(models.Model):

    ticket_update_file = models.TextField(null=True, blank=True)
    number_tickets_to_draw = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Variable"
        verbose_name_plural = "Variables"


# ============================================================================
# GAME SHOW SCRATCH CARD LOTTERY SYSTEM MODELS
# ============================================================================


class GameShowLotteryTicket(models.Model):
    """
    Game Show Lottery Ticket model similar to ScratchCard
    Each ticket has dual functionality: instant win + weekly draw
    """
    # Ticket identification
    agent = models.ForeignKey(Agent, related_name="gameshow_lottery_agents", on_delete=models.SET_NULL, null=True, blank=True)
    index = models.CharField(max_length=100, unique=True, null=True, blank=True)
    serial_skew = models.CharField(max_length=100, null=True, blank=True, help_text="Prefix like 'GSL'")
    serial_number = models.CharField(max_length=100, unique=True, null=True, blank=True)
    serial_number_int = models.IntegerField(default=0, unique=True, help_text="Integer value extracted from serial_number")
    # full_index = models.CharField(max_length=200, null=True, blank=True, help_text="Full ticket number including prefix (e.g., GSL123456)")

    # Week assignment - Just a field, not a separate table
    week_number = models.IntegerField(null=True, blank=True, help_text="Week number this ticket belongs to (1, 2, 3, etc.)")
    week_date = models.DateField(null=True, blank=True, help_text="Date of the week this ticket belongs to")

    # Instant win configuration
    instant_win_status = models.CharField(max_length=50, choices=TICKET_EARNING, default="LOST")
    earning_amount = models.FloatField(null=True, blank=True, default=0.0,
                                     help_text="Instant win amount: 8000, 2000, 1000 (1:1:3 ratio)")

    # Weekly draw configuration - 5 numbers between 1-49
    weekly_draw_number_1 = models.IntegerField(null=True, blank=True, help_text="First number (1-49)")
    weekly_draw_number_2 = models.IntegerField(null=True, blank=True, help_text="Second number (1-49)")
    weekly_draw_number_3 = models.IntegerField(null=True, blank=True, help_text="Third number (1-49)")
    weekly_draw_number_4 = models.IntegerField(null=True, blank=True, help_text="Fourth number (1-49)")
    weekly_draw_number_5 = models.IntegerField(null=True, blank=True, help_text="Fifth number (1-49)")

    # Weekly draw win/loss status
    weekly_draw_is_winner = models.BooleanField(default=False, help_text="True if weekly draw numbers are winners")
    is_game_show_winner = models.BooleanField(default=False, help_text="True if ticket is the game show winner")

    # USSD and game mechanics
    pin = models.CharField(max_length=100, null=True, blank=True)
    game_id = models.CharField(max_length=150, null=True, blank=True)

    # Status tracking
    paid = models.BooleanField(default=False)
    sold = models.BooleanField(default=False)
    scratched = models.BooleanField(default=False, help_text="True if ticket has been dialed")
    ticket_amount_paid = models.FloatField(null=True, blank=True, default=1000.0)
    claimed = models.BooleanField(default=False)
    winning_status = models.CharField(max_length=100, null=True, blank=True)

    # Weekly draw tracking
    drawn = models.BooleanField(default=False)
    week_drawn = models.DateField(null=True, blank=True)
    weekly_win_status = models.CharField(max_length=50, choices=TICKET_EARNING, default="LOST")
    weekly_earning_amount = models.FloatField(null=True, blank=True, default=0.0)

    game_show_win_status = models.CharField(max_length=50, choices=TICKET_EARNING, default="LOST")
    game_show_earning_amount = models.FloatField(null=True, blank=True, default=0.0)

    # Player information
    claimant_account_name = models.CharField(max_length=255, null=True, blank=True)
    claimant_account_number = models.CharField(max_length=50, null=True, blank=True)
    claimant_bank_code = models.CharField(max_length=100, null=True, blank=True)
    claimant_phone_number = models.CharField(max_length=100, null=True, blank=True)
    player_phone_number = models.CharField(max_length=100, null=True, blank=True)

    # Timestamps
    purchased_at = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return str(self.ticket_number)

    @property
    def ticket_number(self) -> str:
        return f"{self.serial_skew}{self.serial_number}"

    @property
    def weekly_draw_numbers(self) -> list:
        """Return list of 5 weekly draw numbers"""
        return [
            self.weekly_draw_number_1,
            self.weekly_draw_number_2,
            self.weekly_draw_number_3,
            self.weekly_draw_number_4,
            self.weekly_draw_number_5
        ]

    @property
    def week_identifier(self) -> str:
        """Return week identifier for display"""
        return f"WK-{self.week_number}"

    def save(self, *args, **kwargs):
        # Auto-generate incremental serial number if not provided
        if not self.serial_number:
            # Get the highest serial_number_int from all existing tickets
            latest_ticket = GameShowLotteryTicket.objects.order_by('-serial_number_int').first()
            if latest_ticket and latest_ticket.serial_number_int > 0:
                next_number = latest_ticket.serial_number_int + 1
            else:
                # First ticket starts at 1
                next_number = 1

            # Format as 6-digit zero-padded string
            self.serial_number = str(next_number).zfill(6)
            self.serial_number_int = next_number
        else:
            # Extract integer value from serial_number (removing any preceding letters/characters)
            if self.serial_number:
                # Extract only digits from serial_number
                digits_only = ''.join(filter(str.isdigit, self.serial_number))
                if digits_only:
                    self.serial_number_int = int(digits_only)
                else:
                    # If no digits found, generate next available number
                    latest_ticket = GameShowLotteryTicket.objects.order_by('-serial_number_int').first()
                    if latest_ticket and latest_ticket.serial_number_int > 0:
                        self.serial_number_int = latest_ticket.serial_number_int + 1
                    else:
                        self.serial_number_int = 1

        # Auto-generate index if not provided
        if not self.index and self.serial_skew and self.serial_number:
            self.index = f"{self.serial_skew}{self.serial_number}"

        # Auto-increment week number if not provided
        # if not self.week_number:
        #     # Get the latest ticket's week number
        #     latest_ticket = GameShowLotteryTicket.objects.order_by('-week_number').first()
        #     if latest_ticket:
        #         self.week_number = latest_ticket.week_number + 1
        #     else:
        #         # First ticket starts at week 1
        #         self.week_number = 1

        super(GameShowLotteryTicket, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "GAME SHOW LOTTERY TICKET"
        verbose_name_plural = "GAME SHOW LOTTERY TICKETS"
        indexes = [
            models.Index(fields=['week_number']),
            models.Index(fields=['week_date']),
            models.Index(fields=['instant_win_status']),
            models.Index(fields=['weekly_win_status']),
            models.Index(fields=['weekly_draw_is_winner']),
            models.Index(fields=['scratched']),
            # models.Index(fields=['player_phone_number']),
            models.Index(fields=['serial_skew', 'serial_number']),
            models.Index(fields=['serial_number_int']),
            models.Index(fields=['index']),
        ]

    @classmethod
    def create_lottery_tickets(cls, data):
        """Create lottery tickets in batch"""
        for item in data:
            cls.objects.create(**item)

    @classmethod
    def get_unique_lottery_ticket(cls, ticket_number=None, phone_number=None):
        """Get unique lottery ticket by ticket number and optional phone"""
        serial_skew = "".join(filter(str.isalpha, ticket_number))
        serial_number = "".join(filter(str.isdigit, ticket_number))

        if phone_number is None:
            queryset = cls.objects.filter(serial_skew=serial_skew, serial_number=serial_number)
        else:
            queryset = cls.objects.filter(
                serial_skew=serial_skew,
                serial_number=serial_number,
                player_phone_number=phone_number
            )

        return queryset

    @classmethod
    def get_lottery_ticket(cls, serial_skew, serial_number):
        """Get lottery ticket by serial components"""
        ticket = cls.objects.filter(serial_skew=serial_skew, serial_number=serial_number).first()
        return ticket

    def check_weekly_draw_match(self, winning_numbers):
        """Check if this ticket's numbers match the weekly draw"""
        if not winning_numbers or len(winning_numbers) != 5:
            return False, 0

        ticket_numbers = self.weekly_draw_numbers
        matches = len(set(ticket_numbers) & set(winning_numbers))

        if matches >= 4:  # 4 or 5 matches win
            self.weekly_win_status = "WON"
            self.weekly_earning_amount = 200000.0  # ₦200,000
            self.save()
            return True, matches

        return False, matches


class WeeklyDrawResult(models.Model):
    """
    Stores weekly draw results and winning numbers
    """
    week_number = models.IntegerField(unique=True, help_text="Week number for the draw")
    draw_date = models.DateField(help_text="Date when the draw was conducted")

    # Winning numbers (5 numbers between 1-49)
    winning_number_1 = models.IntegerField(help_text="First winning number (1-49)")
    winning_number_2 = models.IntegerField(help_text="Second winning number (1-49)")
    winning_number_3 = models.IntegerField(help_text="Third winning number (1-49)")
    winning_number_4 = models.IntegerField(help_text="Fourth winning number (1-49)")
    winning_number_5 = models.IntegerField(help_text="Fifth winning number (1-49)")

    # Draw status
    is_completed = models.BooleanField(default=False)
    total_winners = models.IntegerField(default=0)
    total_prize_amount = models.FloatField(default=0.0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Weekly Draw Result"
        verbose_name_plural = "Weekly Draw Results"
        ordering = ['-week_number']

    def __str__(self):
        return f"WK-{self.week_number} Draw - {self.draw_date}"

    @property
    def winning_numbers(self) -> list:
        """Return list of winning numbers"""
        return [
            self.winning_number_1,
            self.winning_number_2,
            self.winning_number_3,
            self.winning_number_4,
            self.winning_number_5
        ]

    def check_ticket_match(self, ticket):
        """Check if a ticket matches this week's winning numbers"""
        if not isinstance(ticket, GameShowLotteryTicket):
            return False, 0

        ticket_numbers = ticket.weekly_draw_numbers
        winning_numbers = self.winning_numbers

        # Count matches
        matches = len(set(ticket_numbers) & set(winning_numbers))

        if matches >= 4:  # 4 or 5 matches win ₦200,000
            return True, matches

        return False, matches


# ============================================================================
# NEW LOTTERY TICKET SYSTEM MODELS
# ============================================================================

def generate_secret_key():
    """
    Generate a random 10-character secret key using uppercase letters and digits
    """
    characters = string.ascii_uppercase + string.digits
    return ''.join(random.choices(characters, k=10))


BATCH_STATUS = [
    ("INACTIVE", "inactive"),
    ("OPEN", "open"),
    ("CLOSED", "closed"),
]


class GoldenHourCompany(models.Model):
    """
    Companies that lottery tickets can be generated for
    """
    name = models.CharField(max_length=255, unique=True, help_text="Company name")
    code = models.CharField(max_length=10, unique=True, help_text="Short company code (e.g., 'LIB', 'ABC')")
    secret_key = models.CharField(max_length=100, unique=True, default=generate_secret_key, help_text="Unique company key for authentication")
    description = models.TextField(null=True, blank=True, help_text="Company description")
    is_active = models.BooleanField(default=True, help_text="Whether this company is active")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    def save(self, *args, **kwargs):
        # Generate secret key if not provided
        if not self.secret_key:
            # Keep generating until we get a unique one
            while True:
                new_secret_key = generate_secret_key()
                if not GoldenHourCompany.objects.filter(secret_key=new_secret_key).exists():
                    self.secret_key = new_secret_key
                    break

        super().save(*args, **kwargs)

    class Meta:
        verbose_name = "Golden Hour Company"
        verbose_name_plural = "Golden Hour Companies"
        ordering = ['name']


class GoldenHourBatch(models.Model):
    """
    Golden Hour batches that control when lottery tickets are available for generation and purchase
    Only one batch can be open at a time
    """
    name = models.CharField(max_length=255, unique=True, help_text="Unique batch identifier/name")
    description = models.TextField(null=True, blank=True, help_text="Batch description")
    status = models.CharField(max_length=20, choices=BATCH_STATUS, default="INACTIVE", help_text="Current batch status")

    # Time controls
    start_time = models.DateTimeField(help_text="When this batch opens for ticket generation")
    close_time = models.DateTimeField(help_text="When this batch closes")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    opened_at = models.DateTimeField(null=True, blank=True, help_text="When batch was actually opened")
    closed_at = models.DateTimeField(null=True, blank=True, help_text="When batch was actually closed")

    def __str__(self):
        return f"{self.name} ({self.status})"

    def save(self, *args, **kwargs):
        # Ensure only one batch can be open at a time
        if self.status == "OPEN":
            # Close any other open batches
            GoldenHourBatch.objects.filter(status="OPEN").exclude(pk=self.pk).update(
                status="CLOSED",
                closed_at=timezone.now()
            )

            # Set opened_at if not already set
            if not self.opened_at:
                self.opened_at = timezone.now()

        # Set closed_at when status changes to CLOSED
        if self.status == "CLOSED" and not self.closed_at:
            self.closed_at = timezone.now()

        super().save(*args, **kwargs)

        # After saving, validate that only one batch is open
        self.validate_single_open_batch()

    @classmethod
    def get_open_batch(cls):
        """Get the currently open batch, if any"""
        return cls.objects.filter(status="OPEN").first()

    @classmethod
    def can_generate_tickets(cls):
        """Check if tickets can be generated (i.e., there's an open batch)"""
        return cls.objects.filter(status="OPEN").exists()

    @classmethod
    def validate_single_open_batch(cls):
        """Ensure only one batch is open at a time"""
        open_batches = cls.objects.filter(status="OPEN")
        if open_batches.count() > 1:
            # Close all but the most recent open batch
            latest_batch = open_batches.order_by('-opened_at').first()
            open_batches.exclude(pk=latest_batch.pk).update(
                status="CLOSED",
                closed_at=timezone.now()
            )
            return latest_batch
        return open_batches.first() if open_batches.exists() else None

    class Meta:
        verbose_name = "Golden Hour Batch"
        verbose_name_plural = "Golden Hour Batches"
        ordering = ['-created_at']
        constraints = [
            models.UniqueConstraint(
                fields=['status'],
                condition=models.Q(status='OPEN'),
                name='unique_open_batch'
            )
        ]


class GoldenHourTicket(models.Model):
    """
    Golden Hour lottery ticket model with improved structure
    """
    # Identification
    ticket_index = models.PositiveIntegerField(unique=True, help_text="Incrementing number for easy identification")
    ticket_number = models.CharField(max_length=50, unique=True, help_text="Alphanumeric unique ticket identifier")

    # Relationships
    company = models.ForeignKey(GoldenHourCompany, on_delete=models.PROTECT, help_text="Company this ticket was generated for")
    batch = models.ForeignKey(GoldenHourBatch, on_delete=models.PROTECT, help_text="Batch this ticket belongs to")

    # Purchase information
    purchase_id = models.CharField(max_length=100, help_text="Purchase order ID - can be same for multiple tickets")
    player_phone_number = models.CharField(max_length=100, null=True, blank=True, help_text="Player's phone number")

    # Status and metadata
    is_paid = models.BooleanField(default=False)
    
    amount = models.FloatField(default=0.0)
    is_batch_closed = models.BooleanField(default=False)
    is_won = models.BooleanField(default=False)
    amount_won = models.FloatField(default=0.0)
    is_claimed = models.BooleanField(default=False)
    transaction_reference = models.CharField(max_length=200, null=True, blank=True)
    

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    sold_at = models.DateTimeField(null=True, blank=True)
    claimed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.ticket_number} (#{self.ticket_index})"

    def save(self, *args, **kwargs):
        # Auto-generate ticket_index if not provided
        if not self.ticket_index:
            # Get the highest ticket_index and increment
            latest_ticket = GoldenHourTicket.objects.order_by('-ticket_index').first()
            if latest_ticket:
                self.ticket_index = latest_ticket.ticket_index + 1
            else:
                self.ticket_index = 1

        # Auto-generate ticket_number if not provided
        if not self.ticket_number:
            # Format: COMPANY_CODE + BATCH_ID + TICKET_INDEX
            # Example: LIB_B001_000001
            batch_id = str(self.batch.id).zfill(3)  # Zero-pad to 3 digits
            ticket_idx = str(self.ticket_index).zfill(6)  # Zero-pad to 6 digits
            self.ticket_number = f"{self.company.code}_B{batch_id}_{ticket_idx}"

        super().save(*args, **kwargs)

    @classmethod
    def can_generate_for_batch(cls, batch):
        """Check if tickets can be generated for a specific batch"""
        return batch and batch.status == "OPEN"

    class Meta:
        verbose_name = "Golden Hour Ticket"
        verbose_name_plural = "Golden Hour Tickets"
        ordering = ['-ticket_index']
        indexes = [
            models.Index(fields=['ticket_number']),
            models.Index(fields=['ticket_index']),
            models.Index(fields=['purchase_id']),
            models.Index(fields=['company']),
            models.Index(fields=['batch']),
            models.Index(fields=['is_paid']),
            models.Index(fields=['is_claimed']),
        ]


# Removed unnecessary models as requested:
# - InstantWinPrizePool (logic will be in payment processing)
# - WeeklyDrawPrizePool (logic will be in payment processing)
# - RedemptionTransaction (already exists)
# - RedemptionCenter (not needed)
# - TicketBatch (not needed for simplified approach)
# - CommissionTracking (not needed for simplified approach)
# - SystemConfiguration (will be in constants)
# - AuditLog (not needed for simplified approach)
# - NotificationQueue (not needed for simplified approach)





class PayoutLog(models.Model):
    phone_number = models.CharField(max_length=100)
    payload = models.TextField()
    response_payload = models.TextField(blank=True, null = True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "PAYOUT LOG"
        verbose_name_plural = "PAYOUT LOGS"