"""
Custom authentication for Golden Hour Company API access
"""
from rest_framework.authentication import Base<PERSON>uthentication
from rest_framework.exceptions import AuthenticationFailed
from django.contrib.auth.models import AnonymousUser

from scratch_cards.models import GoldenHourCompany


class GoldenHourCompanyAuthentication(BaseAuthentication):
    """
    Custom authentication that validates company code and secret key
    
    Expected headers:
    - X-Company-Code: The company code (e.g., 'LIB', 'ABC')
    - X-Secret-Key: The company's secret key
    """
    
    def authenticate(self, request):
        # Try to get from query params first
        company_code = request.query_params.get('X-Company-Code') or request.query_params.get('x-company-code')
        secret_key = request.query_params.get('X-Secret-Key') or request.query_params.get('x-secret-key')

        # Fallback to headers if not in query params
        # if not company_code:
        #     company_code = request.META.get('HTTP_X_COMPANY_CODE')
        # if not secret_key:
        #     secret_key = request.META.get('HTTP_X_SECRET_KEY')

        if not company_code or not secret_key:
            return None  # No authentication attempted

        try:
            # Find company by code and validate secret key
            company = GoldenHourCompany.objects.get(
                code=company_code,
                secret_key=secret_key,
                is_active=True
            )
            
            # Return a tuple of (user, auth) where user can be None or AnonymousUser
            # and auth is the company object for later use
            return (AnonymousUser(), company)
            
        except GoldenHourCompany.DoesNotExist:
            raise AuthenticationFailed('Invalid company code or secret key')
    
    def authenticate_header(self, request):
        """
        Return a string to be used as the value of the `WWW-Authenticate`
        header in a `401 Unauthenticated` response, or `None` if the
        authentication scheme should return `403 Permission Denied` responses.
        """
        return 'GoldenHourCompany'


