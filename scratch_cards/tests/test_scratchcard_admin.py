import pytest
from django.contrib import admin
from django.contrib.admin.sites import AdminSite

from scratch_cards.models import (
    CountAgentTickets,
    ScratchCard, 
    WeeklyWinners,
    PayOut,
    ScratchCardAgents
)

class MockRequest:
    pass


@pytest.fixture
def mock_request():
    return MockRequest()

from scratch_cards.admin import (
    CountAgentTicketsAdmin,
    WeeklyWinnersAdmin,
    PayOutResourceAdmin,
    ScratchCardResourceAdmin,
    ScratchCardAgentsResourceAdmin
)

def test_admin_registrations():
    registered_models = list(admin.site._registry.keys())

    assert ScratchCard in registered_models
    assert PayOut in registered_models
    assert WeeklyWinners in registered_models
    assert ScratchCardAgents in registered_models


# @pytest.mark.parametrize(
#     "model_admin_class.model_class",
#     [
#         (WeeklyWinnersAdmin, WeeklyWinners),
#         (PayOutResourceAdmin, PayOut),
#         (ScratchCardResourceAdmin, ScratchCard),
#         (ScratchCardAgentsResourceAdmin, ScratchCardAgents),
#     ],
# )

def test_weekly_winners_model_admin():
    site = AdminSite()
    model_admin = WeeklyWinnersAdmin(WeeklyWinners, site)

    assert model_admin.search_fields == ["ticket__index", "week_date", "ticket__player_phone_number"]

    assert model_admin.list_display == ["week_date", "ticket", "created_at", "updated_at"]

    assert model_admin.date_hierarchy == "created_at"

def test_count_agents_ticket_model_admin():
    site = AdminSite()
    model_admin = CountAgentTicketsAdmin(CountAgentTickets, site)

    assert model_admin.search_fields == ["start_date", "end_date", "agent__first_name", "agent__last_name", "agent__phone_number"]

    assert model_admin.list_display == ["agent", "counts", "updated_at"]

    assert model_admin.ordering == ["-counts"]