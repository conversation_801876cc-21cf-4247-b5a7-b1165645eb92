# import pytest
# from django.test import TestCase
# from pos_app.models import Agent, AgentWallet, ChargeAgentTransaction
# from wallet_app.models import DebitCreditRecord, UserWallet
# from scratch_cards.helpers.utils import agents_commission_for_lotto_draw

# class AgentsCommissionForLottoDrawTestCase(TestCase):
#     def setUp(self):
#         self.agent = Agent.objects.create(
#             first_name="Test",
#             last_name="Agent",
#             phone="1234567890",
#             email="<EMAIL>",
#             user_id="1234",

#             # ...other required fields with dummy data...
#         )
#         self.funding_amount = 1000
#         self.commission_amount = 100

#     def test_agents_commission_for_lotto_draw(self):
#         wallet = AgentWallet.objects.create(agent=self.agent)
#         initial_wallet_balance = wallet.commission_bal
#         agents_commission_for_lotto_draw(self.agent, self.funding_amount, self.commission_amount)
        
#         # Check if ChargeAgentTransaction was created
#         self.assertTrue(ChargeAgentTransaction.objects.filter(agent=self.agent, amount=self.commission_amount).exists())
        
#         # Check if DebitCreditRecord was created for credit
#         self.assertTrue(DebitCreditRecord.objects.filter(
#             phone_number=self.agent.phone,
#             amount=float(self.commission_amount),
#             transaction_type="CREDIT"
#         ).exists())
        
#         # Check if UserWallet was funded
#         updated_wallet_balance = UserWallet.objects.get(user=self.agent).balance
#         self.assertEqual(updated_wallet_balance, initial_wallet_balance + float(self.commission_amount))
        
#         # Check if DebitCreditRecord was created for debit
#         self.assertTrue(DebitCreditRecord.objects.filter(
#             phone_number=self.agent.phone,
#             amount=float(self.commission_amount),
#             transaction_type="DEBIT"
#         ).exists())
        
#         # Check if UserWallet was deducted
#         final_wallet_balance = UserWallet.objects.get(user=self.agent).balance
#         self.assertEqual(final_wallet_balance, initial_wallet_balance)

#         # Check if the commission was sent to the agent
#         # ...additional checks for the commission transfer...

# # ...existing code...
