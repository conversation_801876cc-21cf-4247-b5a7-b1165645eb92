import random
import uuid
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone

from main.models import ConstantVariable
from scratch_cards.models import GameShowLotteryTicket


class Command(BaseCommand):
    help = "Generate GameShowLotteryTicket instances based on constant table values"

    def add_arguments(self, parser):
        parser.add_argument(
            'week_date',
            type=str,
            help='Week date for ticket generation (e.g., "2024-01-15", "2024-12-25"). Must not be in the past.',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force generation even if tickets already exist for the week date',
        )

    def handle(self, **options):
        try:
            # Fix any existing tickets with serial_number_int = 0
            self._fix_existing_tickets()

            # Get week_date argument and parse it
            week_date_str = options['week_date']

            # Parse the date string
            try:
                week_date = datetime.strptime(week_date_str, '%Y-%m-%d').date()
            except ValueError:
                raise CommandError('Invalid date format. Use "YYYY-MM-DD" format (e.g., "2024-01-15")')

            # Validate that the date is not in the past
            today = timezone.now().date()
            if week_date < today:
                raise CommandError(f'Week date {week_date} cannot be in the past. Today is {today}.')

            # Retrieve values from ConstantVariable model
            constant_vars = ConstantVariable.objects.last()
            if not constant_vars:
                raise CommandError('No ConstantVariable found. Please create one first.')

            # Get values from game lottery fields
            total_tickets = constant_vars.game_lottery_ticket_count
            ticket_cost = constant_vars.game_show_lottery_ticket_cost

            # Calculate available instant win amount (40% of total revenue)
            total_revenue = ticket_cost * total_tickets
            available_instant_win_amount = total_revenue * 0.40

            # Get prize amounts from constant table
            max_amount = constant_vars.game_lottery_instant_win_max_amount
            mid_amount = constant_vars.game_lottery_instant_win_mid_amount
            min_amount = constant_vars.game_lottery_instant_win_min_amount
            weekly_win_amount = constant_vars.game_lottery_weekly_win_amount
            gameshow_amount = constant_vars.game_lottery_weekly_game_show_amount

            self.stdout.write(
                self.style.SUCCESS(
                    f"Configuration loaded: {total_tickets} tickets, "
                    f"Ticket cost: ₦{ticket_cost:,.2f}, Available instant win amount: ₦{available_instant_win_amount:,.2f}, Week Date: {week_date}"
                )
            )

            # Check if tickets already exist for this week date
            existing_tickets = GameShowLotteryTicket.objects.filter(week_date=week_date).count()
            if existing_tickets > 0 and not options['force']:
                self.stdout.write(
                    self.style.WARNING(
                        f"Found {existing_tickets} existing tickets for week date {week_date}. "
                        f"Use --force to generate anyway."
                    )
                )
                return

            # Calculate winning tickets using formula-based distribution
            # Apply the 1:1:3 ratio formula dynamically based on available instant win amount
            # The formula maintains the ratio while maximizing the use of available funds

            # Calculate optimal distribution using 1:1:3 ratio
            # Let x be the base unit, then we have:
            # max_amount * x + mid_amount * x + min_amount * 3x = available_instant_win_amount
            # Solving for x: x = available_instant_win_amount / (max_amount + mid_amount + 3 * min_amount)

            ratio_denominator = max_amount + mid_amount + (3 * min_amount)
            base_unit = available_instant_win_amount / ratio_denominator

            # Calculate winners for each tier based on the ratio
            winners_8000 = max(1, int(base_unit))  # 1 part of the ratio
            winners_2000 = max(1, int(base_unit))  # 1 part of the ratio
            winners_1000 = max(1, int(base_unit * 3))  # 3 parts of the ratio

            # Verify we don't exceed available amount and adjust if necessary
            calculated_cost = (winners_8000 * max_amount +
                              winners_2000 * mid_amount +
                              winners_1000 * min_amount)

            # If calculated cost exceeds available amount, reduce proportionally
            if calculated_cost > available_instant_win_amount:
                adjustment_factor = available_instant_win_amount / calculated_cost
                winners_8000 = max(1, int(winners_8000 * adjustment_factor))
                winners_2000 = max(1, int(winners_2000 * adjustment_factor))
                winners_1000 = max(1, int(winners_1000 * adjustment_factor))

            # Calculate actual total cost and total winners
            actual_total_cost = (winners_8000 * max_amount +
                                winners_2000 * mid_amount +
                                winners_1000 * min_amount)
            total_winners = winners_8000 + winners_2000 + winners_1000

            self.stdout.write(
                self.style.SUCCESS(
                    f"Generating {total_tickets} tickets with {total_winners} winners "
                    f"(₦{max_amount:,.0f}: {winners_8000}, ₦{mid_amount:,.0f}: {winners_2000}, ₦{min_amount:,.0f}: {winners_1000})\n"
                    f"Total prize cost: ₦{actual_total_cost:,.2f} / Available: ₦{available_instant_win_amount:,.2f}"
                )
            )

            # Get the starting serial number from the last ticket
            latest_ticket = GameShowLotteryTicket.objects.order_by('-serial_number_int').first()
            if latest_ticket:
                start_serial_number = latest_ticket.serial_number_int + 1
            else:
                start_serial_number = 1

            # Generate tickets in batches
            batch_size = 100
            tickets_created = 0

            with transaction.atomic():
                for batch_start in range(0, total_tickets, batch_size):
                    batch_end = min(batch_start + batch_size, total_tickets)
                    batch_tickets = []

                    for i in range(batch_start, batch_end):
                        # Generate incremental serial number
                        current_serial_int = start_serial_number + i
                        serial_number = str(current_serial_int).zfill(6)  # 6-digit serial number
                        index = f"GSL{serial_number}"

                        # Determine instant win status and amount
                        instant_win_status = "LOST"
                        earning_amount = 0.0

                        # Assign winners based on calculated distribution
                        if i < winners_8000:
                            instant_win_status = "WON"
                            earning_amount = max_amount
                        elif i < winners_8000 + winners_2000:
                            instant_win_status = "WON"
                            earning_amount = mid_amount
                        elif i < winners_8000 + winners_2000 + winners_1000:
                            instant_win_status = "WON"
                            earning_amount = min_amount

                        # Generate 5 random numbers for weekly draw (1-49)
                        weekly_numbers = random.sample(range(1, 50), 5)

                        # Generate PIN and Game ID
                        pin = str(random.randint(1000, 9999))
                        game_id = str(uuid.uuid4())[:8].upper()

                        ticket_data = {
                            'index': index,
                            'serial_skew': 'GSL',
                            'serial_number': serial_number,
                            'serial_number_int': current_serial_int,  # Explicitly set the integer value
                            'week_date': week_date,
                            'instant_win_status': instant_win_status,
                            'earning_amount': earning_amount,
                            'weekly_draw_number_1': weekly_numbers[0],
                            'weekly_draw_number_2': weekly_numbers[1],
                            'weekly_draw_number_3': weekly_numbers[2],
                            'weekly_draw_number_4': weekly_numbers[3],
                            'weekly_draw_number_5': weekly_numbers[4],
                            'pin': pin,
                            'game_id': game_id,
                        }

                        batch_tickets.append(GameShowLotteryTicket(**ticket_data))

                    # Bulk create batch
                    GameShowLotteryTicket.objects.bulk_create(batch_tickets)
                    tickets_created += len(batch_tickets)

                    self.stdout.write(f"Created batch: {tickets_created}/{total_tickets} tickets")

            # Shuffle the winning tickets to randomize distribution
            self._randomize_winners(week_date)

            # Mark exactly 4 tickets as weekly draw winners with unique combinations
            self._mark_weekly_draw_winners(week_date, weekly_win_amount, gameshow_amount)

            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully generated {tickets_created} GameShow lottery tickets for week date {week_date}"
                )
            )

        except Exception as e:
            self.stderr.write(f"Error generating tickets: {str(e)}")
            raise

    def _fix_existing_tickets(self):
        """Fix any existing tickets with serial_number_int = 0"""
        problematic_tickets = GameShowLotteryTicket.objects.filter(serial_number_int=0)
        if problematic_tickets.exists():
            self.stdout.write(f"Found {problematic_tickets.count()} tickets with serial_number_int = 0. Fixing...")

            for i, ticket in enumerate(problematic_tickets.order_by('id'), 1):
                if ticket.serial_number:
                    # Extract digits from serial_number
                    digits_only = ''.join(filter(str.isdigit, ticket.serial_number))
                    if digits_only:
                        ticket.serial_number_int = int(digits_only)
                    else:
                        # If no digits, assign incremental number
                        ticket.serial_number_int = i
                else:
                    # If no serial_number, assign incremental number
                    ticket.serial_number_int = i

                ticket.save()

            self.stdout.write(f"Fixed {problematic_tickets.count()} tickets.")

    def _randomize_winners(self, week_date):
        """Randomize the distribution of winning tickets"""
        # Get all tickets for the week date
        all_tickets = list(GameShowLotteryTicket.objects.filter(week_date=week_date))

        # Get current winners
        winners = [t for t in all_tickets if t.instant_win_status == "WON"]

        # Shuffle the list to randomize positions
        random.shuffle(all_tickets)

        # Reassign winners randomly
        winner_index = 0
        for ticket in all_tickets:
            if winner_index < len(winners):
                # Assign winner data
                winner = winners[winner_index]
                ticket.instant_win_status = winner.instant_win_status
                ticket.earning_amount = winner.earning_amount
                winner_index += 1
            else:
                # Assign loser data
                ticket.instant_win_status = "LOST"
                ticket.earning_amount = 0.0

        # Bulk update
        GameShowLotteryTicket.objects.bulk_update(
            all_tickets,
            ['instant_win_status', 'earning_amount']
        )

        self.stdout.write("Randomized winner distribution")

    def _mark_weekly_draw_winners(self, week_date, weekly_win_amount, gameshow_amount):
        """
        Mark exactly 4 tickets as weekly draw winners with unique number combinations.
        From these 4 weekly winners, select 1 as the game show winner.
        This ensures every ticket generation batch has exactly 4 weekly winners per week.
        """
        # Get all tickets for the week date
        all_tickets = list(GameShowLotteryTicket.objects.filter(week_date=week_date))

        if len(all_tickets) < 4:
            self.stdout.write(
                self.style.WARNING(f"Not enough tickets to mark 4 weekly winners. Only {len(all_tickets)} tickets available.")
            )
            return

        # Track used number combinations to ensure uniqueness
        used_combinations = set()
        weekly_winners = []

        # First, collect all existing combinations to avoid duplicates
        for ticket in all_tickets:
            combination = (
                ticket.weekly_draw_number_1,
                ticket.weekly_draw_number_2,
                ticket.weekly_draw_number_3,
                ticket.weekly_draw_number_4,
                ticket.weekly_draw_number_5
            )
            used_combinations.add(combination)

        # Shuffle tickets to randomize selection
        random.shuffle(all_tickets)

        # Try to find 4 tickets with unique combinations from existing tickets
        for ticket in all_tickets:
            if len(weekly_winners) >= 4:
                break

            combination = (
                ticket.weekly_draw_number_1,
                ticket.weekly_draw_number_2,
                ticket.weekly_draw_number_3,
                ticket.weekly_draw_number_4,
                ticket.weekly_draw_number_5
            )

            # Check if this combination is unique among selected winners
            winner_combinations = {(
                w.weekly_draw_number_1,
                w.weekly_draw_number_2,
                w.weekly_draw_number_3,
                w.weekly_draw_number_4,
                w.weekly_draw_number_5
            ) for w in weekly_winners}

            if combination not in winner_combinations:
                weekly_winners.append(ticket)

        # If we couldn't find 4 unique combinations, regenerate numbers to guarantee uniqueness
        if len(weekly_winners) < 4:
            self.stdout.write("Regenerating numbers to ensure 4 completely unique combinations...")

            # Get remaining tickets that weren't selected
            remaining_tickets = [t for t in all_tickets if t not in weekly_winners]
            random.shuffle(remaining_tickets)

            # Track combinations used by selected winners
            winner_combinations = {(
                w.weekly_draw_number_1,
                w.weekly_draw_number_2,
                w.weekly_draw_number_3,
                w.weekly_draw_number_4,
                w.weekly_draw_number_5
            ) for w in weekly_winners}

            for ticket in remaining_tickets:
                if len(weekly_winners) >= 4:
                    break

                # Generate new unique combination that doesn't exist in any selected winner
                attempts = 0
                while attempts < 1000:  # Increased attempts for better guarantee
                    new_numbers = random.sample(range(1, 50), 5)
                    new_combination = tuple(sorted(new_numbers))  # Sort for consistent comparison

                    # Check if this combination is unique among selected winners
                    if new_combination not in winner_combinations:
                        # Update ticket with new numbers
                        ticket.weekly_draw_number_1 = new_numbers[0]
                        ticket.weekly_draw_number_2 = new_numbers[1]
                        ticket.weekly_draw_number_3 = new_numbers[2]
                        ticket.weekly_draw_number_4 = new_numbers[3]
                        ticket.weekly_draw_number_5 = new_numbers[4]

                        winner_combinations.add(new_combination)
                        weekly_winners.append(ticket)
                        break

                    attempts += 1

                if attempts >= 1000:
                    self.stdout.write(
                        self.style.WARNING(f"Could not generate unique combination after 1000 attempts for ticket {ticket.ticket_number}")
                    )

        # Final validation: Ensure we have exactly 4 winners with unique combinations
        if len(weekly_winners) < 4:
            self.stdout.write(
                self.style.ERROR(f"Failed to select 4 weekly winners. Only {len(weekly_winners)} winners selected.")
            )
            return

        # Validate that all 4 combinations are actually unique
        final_combinations = {(
            w.weekly_draw_number_1,
            w.weekly_draw_number_2,
            w.weekly_draw_number_3,
            w.weekly_draw_number_4,
            w.weekly_draw_number_5
        ) for w in weekly_winners}

        if len(final_combinations) != 4:
            self.stdout.write(
                self.style.ERROR(f"Duplicate combinations detected! Expected 4 unique combinations, got {len(final_combinations)}")
            )
            return

        self.stdout.write(
            self.style.SUCCESS(f"✅ Confirmed: All 4 weekly winners have unique number combinations")
        )

        # Mark the selected tickets as weekly draw winners
        for ticket in weekly_winners:
            ticket.weekly_draw_is_winner = True
            ticket.weekly_win_status = "WON"
            ticket.weekly_earning_amount = weekly_win_amount

        # Select one of the 4 weekly winners as the game show winner
        if weekly_winners:
            game_show_winner = random.choice(weekly_winners)
            game_show_winner.is_game_show_winner = True
            game_show_winner.game_show_win_status = "WON"
            game_show_winner.game_show_earning_amount = gameshow_amount
            # Keep weekly earning amount separate from game show amount

            self.stdout.write(
                self.style.SUCCESS(
                    f"Selected ticket {game_show_winner.ticket_number} as game show winner "
                    f"(Weekly: ₦{weekly_win_amount:,.2f} + Game Show: ₦{gameshow_amount:,.2f} = Total: ₦{weekly_win_amount + gameshow_amount:,.2f})"
                )
            )

        # Bulk update all tickets (both winners and non-winners)
        GameShowLotteryTicket.objects.bulk_update(
            all_tickets,
            ['weekly_draw_number_1', 'weekly_draw_number_2', 'weekly_draw_number_3',
             'weekly_draw_number_4', 'weekly_draw_number_5', 'weekly_draw_is_winner',
             'weekly_win_status', 'weekly_earning_amount', 'is_game_show_winner',
             'game_show_win_status', 'game_show_earning_amount']
        )

        # Calculate total prize amounts for reporting
        regular_winners = len(weekly_winners) - 1  # Exclude game show winner
        total_regular_prizes = regular_winners * weekly_win_amount
        game_show_total_prize = weekly_win_amount + gameshow_amount
        total_weekly_prizes = total_regular_prizes + game_show_total_prize

        self.stdout.write(
            self.style.SUCCESS(
                f"Marked {len(weekly_winners)} tickets as weekly draw winners:\n"
                f"  - {regular_winners} regular weekly winners: ₦{weekly_win_amount:,.2f} each\n"
                f"  - 1 game show winner: ₦{weekly_win_amount:,.2f} (weekly) + ₦{gameshow_amount:,.2f} (game show) = ₦{game_show_total_prize:,.2f}\n"
                f"  - Total weekly prizes: ₦{total_weekly_prizes:,.2f}"
            )
        )
