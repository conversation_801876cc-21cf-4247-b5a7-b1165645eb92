import pandas as pd
from django.core.management.base import BaseCommand

from scratch_cards.models import ScratchCard


class Command(BaseCommand):
    help = "Import data from Excel into the database"

    def add_arguments(self, parser):
        parser.add_argument("file_path", type=str, help="The path to the Excel file")

    def handle(self, *args, **kwargs):
        file_path = kwargs["file_path"]

        try:
            # Read the Excel file
            df = pd.read_excel(file_path, engine="openpyxl")

            # Iterate over each row in the DataFrame and save to the database
            for _, row in df.iterrows():
                try:
                    ScratchCard.objects.create(
                        serial_skew=row["serial_skew"],
                        serial_number=row["serial_number"],
                        serial_number_int=row["serial_number_int"],
                        earning=row["earning"],
                        earning_amount=row["earning_amount"],
                        pin=row["pin"],
                        game_id=row["game_id"],
                        pick=row["pick"],
                        result=row["result"],
                        index=row["index"],
                    )
                    print("CREATED.!!!")
                except Exception:
                    pass
            self.stdout.write(self.style.SUCCESS("Successfully imported data"))

        except Exception as e:
            self.stderr.write(f"Error: {str(e)}")
