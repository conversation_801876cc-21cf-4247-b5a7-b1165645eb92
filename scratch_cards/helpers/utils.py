from datetime import datetime

import requests
from django.conf import settings
from rest_framework.pagination import PageNumberPagination

from pos_app.models import (
    AgencyBankingLogs,
    AgencyBankingToken,
    Agent,
    AgentWallet,
    AgentWalletTransaction,
    ChargeAgentTransaction,
)

from decouple import config

import redis
from pos_app.pos_helpers import agent_login
from wallet_app.models import DebitCreditRecord, UserWallet


class PaginationClass(PageNumberPagination):
    page_size_query_param = "page_size"

    def get_paginated_response(self, data):
        resp = super().get_paginated_response(data)
        page_size = super().get_page_size(self.request)
        resp.data["page_size"] = page_size
        return resp


def index_formatter(index):
    try:
        number = int(index)
    except ValueError:
        raise ValueError("Index not a number")
    return str(number)


def format_phone_number(phone_number):
    phone_number = phone_number.strip()
    if not phone_number.isdigit():
        raise ValueError("Phone Number not digits")

    if phone_number.startswith("0") and len(phone_number) == 11:
        return "234" + phone_number[1:]

    elif phone_number.startswith("+234") and len(phone_number) == 14:
        return phone_number[1:]

    else:
        raise ValueError("Invalid phone number format. It should start with '0' or '+234' with 11 or 14 digits respectively.")


def get_serializer_key_error(errors_dict: dict):
    try:
        key = list(errors_dict)[0]
        error = errors_dict.get(key)
        return f"{error[0]}"
    except Exception:
        return ""


def format_date(date_str: str) -> datetime.date:
    """
    Accepts date format as either `Year-Month-Date` or `Date-Month-Year`
    If not either of them, returns an error
    """

    try:
        formatted_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        return formatted_date
    except ValueError:
        try:
            date_passed = datetime.strptime(date_str, "%d-%m-%Y").date()
            date_passed = date_passed.strftime("%Y-%m-%d")
            standard_date = datetime.strptime(date_passed, "%Y-%m-%d").date()
            # to allow uniformly returned date obj and not string
            return standard_date
        except ValueError:
            raise ValueError(f"Invalid date format: {date_str}. Must be in 'YYYY-MM-DD' or 'DD-MM-YYYY' format.")


def reverify_payment(reference, auth_token):
    url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/get_service_transaction?unique_reference={reference}"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {auth_token}",
    }

    response = requests.get(url, headers=headers)

    print(
        f"""

              ---------------------------
              agent reverify_payment response {response.text}

              """
    )

    try:
        return response.json()
    except Exception:
        return {"message": "failed"}


def charge_agent_wallet_for_lotto_tickets(agent_instance, amount, pin):
    call_log = AgencyBankingLogs.objects.create(agent=agent_instance)

    from wallet_app.models import FloatWallet

    charge_agent_trans_instance = ChargeAgentTransaction.objects.create(
        agent=agent_instance,
        amount=amount,
    )

    payload = {
        "service_name": "LOTTO_PLAY",
        "user_id": agent_instance.user_id,
        "unique_reference": charge_agent_trans_instance.transaction_ref,
        "narration": "LOTTO_PLAY",
        "total_amount": amount,
        "service_comm": amount,
        "agent_comm": 0,
        "transaction_pin": pin,
    }

    token  = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")
    call_log.level_description = "TOKEN"
    call_log.token = token
    call_log.payload = payload
    call_log.save()

    agent_wallet = AgentWallet.objects.filter(agent=agent_instance).last()

    agent_transaction_instance = AgentWalletTransaction.objects.create(
        transaction_reference=charge_agent_trans_instance.transaction_ref,
        agent_wallet=agent_wallet,
        amount=amount,
        transaction_type="DEBIT",
        rewarded_commission=charge_agent_trans_instance.commission_amount,
        transaction_from="LIBERTY_PAY_LOTTO_CHARGE",
        agent_name=agent_wallet.agent.first_name + " " + agent_wallet.agent.last_name,
        agent_phone_number=agent_wallet.agent.phone,
        agent_email=agent_wallet.agent.email,
        terminal_id=agent_wallet.agent.terminal_id,
        terminal_serial_number=agent_wallet.agent.terminal_serial_number,
        wave = agent_wallet.agent.get_wave()
    )

    # initiate transaction
    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/other_services_charge/"
    # url = f"http://127.0.0.1:9091/send/other_services_charge/"

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    try:
        charge_response = requests.post(url, headers=headers, json=payload)

        if charge_response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"agent_login_token")

            return charge_agent_wallet_for_lotto_tickets(agent_instance, amount, pin)


        call_log.level_description = "CALL CHARGE"
        call_log.response = charge_response
        call_log.save()

        # update float wallet
        FloatWallet().update_float_wallet()

        print(
            f"""

                ---------------------------
                agent charge_response response {charge_response.text}


                """
        )
    except requests.exceptions.RequestException as e:
        agent_transaction_instance.status = "FAILED"
        agent_transaction_instance.save()
        data = {
            "status": "failed",
            "message": e,
        }
        call_log.level_description = "CALL CHARGE EXCEPTION"
        call_log.response = str(e)
        call_log.save()
        return data

    try:
        reponse = charge_response.json()
        call_log.level_description = "CHARGE JSON"
        call_log.response = reponse
        call_log.save()
    except Exception:
        agent_transaction_instance.status = "FAILED"
        agent_transaction_instance.save()

        data = {
            "status": "failed",
            "message": charge_response.text,
        }
        call_log.level_description = "CHARGE TEXT"
        call_log.response = charge_response.text
        call_log.save()
        return data

    if reponse.get("error") is not None:
        # print("got to error")
        agent_transaction_instance.status = "FAILED"
        agent_transaction_instance.save()
        data = {
            "status": "failed",
            "message": reponse.get("message"),
        }
        if reponse.get("message") == "Incorrect Agent Pin":
            data["user_message"] = "Incorrect Agent Pin"
        if reponse.get("message") == "Agent does not have sufficient balance to make this transaction":
            data["user_message"] = "Insufficient Balance."

        call_log.level_description = "CHARGE ERROR"
        call_log.response = reponse.get("message")
        call_log.save()
        return data

    print(f"charge_response.status_code >>>>>>>>>> {type(charge_response.status_code)}, {charge_response.status_code}")
    if charge_response.status_code == 400 or charge_response.status_code == 500:
        agent_transaction_instance.status = "FAILED"
        agent_transaction_instance.save()

        data = {
            "status": "failed",
            "message": charge_response,
        }
        call_log.level_description = "CHARGE RESP"
        call_log.response = charge_response
        call_log.save()
        print("got to 400 or 500")

        return data

    charge_agent_trans_instance.payment_initiated = True
    charge_agent_trans_instance.save()

    # verify charge
    verify_response = reverify_payment(charge_agent_trans_instance.transaction_ref, token)
    if verify_response.get("status") == "SUCCESSFUL":
        agent_transaction_instance.status = "SUCCESSFUL"
        agent_transaction_instance.save()

        call_log.level_description = "VERIFY PAYMENT"
        call_log.response = verify_response
        call_log.save()
        return {"message": "success", "data": verify_response}

    else:
        agent_transaction_instance.status = "FAILED"
        agent_transaction_instance.save()

        call_log.level_description = "FAILED VERIFY PAYMENT"
        call_log.response = verify_response
        call_log.save()
        return {"message": "failed", "data": verify_response}


def agents_commission_for_lotto_draw(agent_instance: Agent, funding_amount: str, commission_amount: str):

    agent_trans_instance = ChargeAgentTransaction.objects.create(
        agent=agent_instance,
        amount=commission_amount,
    )

    credit_record = DebitCreditRecord.create_record(
        phone_number=agent_instance.phone,
        amount=float(commission_amount),
        channel="POS",
        reference=f"credit-{agent_trans_instance.transaction_ref}",
        transaction_type="CREDIT",
    )

    credit_wallet_payload = {
        "transaction_from": "SEEDS_PENNIES_DRAW_COMMISSION",
    }

    UserWallet.fund_wallet(
        user=agent_instance,
        amount=commission_amount,
        channel="POS",
        transaction_id=credit_record.reference,
        user_wallet_type="COMMISSION_WALLET",
        **credit_wallet_payload,
    )

    debit_record = DebitCreditRecord.create_record(
        phone_number=agent_instance.phone,
        amount=float(commission_amount),
        channel="POS",
        reference=f"debit-{agent_trans_instance.transaction_ref}",
        transaction_type="DEBIT",
    )

    debit_wallet_payload = {
        "transaction_from": "SEEDS_PENNIES_AGENT_DRAW_COMMISSION_WITHDRAWAL",
    }

    UserWallet.deduct_wallet(
        user=agent_instance,
        amount=commission_amount,
        channel="POS",
        transaction_id=debit_record.reference,
        user_wallet_type="COMMISSION_WALLET",
        **debit_wallet_payload,
    )

    token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/give_commision_from_me/"
    payload = {
        "transaction_ref": agent_trans_instance.transaction_ref,
        "recipient": agent_instance.phone,
        "amount": funding_amount,
        "profit": commission_amount,
        "transaction_type": "LOTTO_SCRATCH_CARD_AGENT_COMMISSION",
        "transaction_reason": "SEED AND PENNIES AGENT LOTTO DRAW COMMISSION",
    }

    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 401:
        redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
        redis_db.delete(f"agent_login_token")

        return agents_commission_for_lotto_draw(agent_instance, funding_amount, commission_amount)

    try:
        return response.json()
    except Exception:
        return response.text
