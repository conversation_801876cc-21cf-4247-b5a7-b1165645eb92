import csv
import datetime
import random
from io import StringIO

from django.contrib import admin, messages
from django.db.models import Count, Q
from django.utils import timezone
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from scratch_cards.models import (
    CountAgentTickets,
    GameShowLotteryTicket,
    GoldenHourBatch,
    GoldenHourCompany,
    GoldenHourTicket,
    PayOut,
    PayoutLog,
    ScratchCard,
    ScratchCardAgents,
    Variables,
    WeeklyDrawResult,
    WeeklyWinners,
)
from scratch_cards.tasks import send_whisper_sms
from main.models import ConstantVariable

# Register your models here.


# RESOURCES
class ScratchCardResource(resources.ModelResource):
    class Meta:
        model = ScratchCard


class PayOutResource(resources.ModelResource):
    class Meta:
        model = PayOut


class ScratchCardAgentsResource(resources.ModelResource):
    class Meta:
        model = ScratchCardAgents


class WeeklyWinnersResource(resources.ModelResource):
    class Meta:
        model = WeeklyWinners


class VariablesResource(resources.ModelResource):
    class Meta:
        model = Variables


# GAME SHOW LOTTERY RESOURCES
class GameShowLotteryTicketResource(resources.ModelResource):
    class Meta:
        model = GameShowLotteryTicket


class WeeklyDrawResultResource(resources.ModelResource):
    class Meta:
        model = WeeklyDrawResult


class PayoutLogResource(resources.ModelResource):
    class Meta:
        model = PayoutLog


#
# ADMINS


class PlayerPhoneNumberFilter(admin.SimpleListFilter):
    title = "Player Phone Number"
    parameter_name = "player_phone_number"

    def lookups(self, request, model_admin):
        return (
            ("yes", "Yes"),
            ("no", "No"),
        )

    def queryset(self, request, queryset):
        if self.value() == "yes":
            return queryset.filter(player_phone_number__isnull=False)
        elif self.value() == "no":
            return queryset
        return queryset


class ScratchCardResourceAdmin(ImportExportModelAdmin):
    resource_class = ScratchCardResource
    search_fields = [
        "serial_skew",
        "serial_number",
        "claimant_account_name",
        "claimant_account_number",
        "claimant_phone_number",
        "claimant_bank_code",
    ]
    list_filter = ("paid", "sold", "claimed", "serial_skew", "earning", PlayerPhoneNumberFilter)
    date_hierarchy = "date_created"
    ordering = ["-date_updated"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = (
        "makeavailable",
        "makeunavailable",
        "markassold",
        "pick_random_winner",
        "set_tickets_to_drawn",
        "set_specific_tickets_to_drawn",
        "update_tickets_from_file",
    )  # Necessary

    @admin.action(description="makeavailable")
    def makeavailable(modeladmin, request, queryset):
        for obj in queryset:
            obj.paid = True
            obj.sold = True
            obj.save()
            messages.success(request, "Successfully made available!")

    @admin.action(description="makeunavailable")
    def makeunavailable(modeladmin, request, queryset):
        for obj in queryset:
            obj.paid = False
            obj.sold = False
            obj.save()
            messages.success(request, "Successfully made unavailable!")

    @admin.action(description="markassold")
    def markassold(modeladmin, request, queryset):
        for obj in queryset:
            obj.sold = True
            obj.save()
            messages.success(request, "Successfully updated as sold!")

    # @admin.action(description="Start Lotto Draw")
    # def pick_random_winner(modeladmin, request, queryset):
    #     eligible_cards = ScratchCard.objects.filter(player_phone_number__isnull=False, sold=True, paid=True, serial_skew="S", drawn=False).exclude(
    #         id__in=WeeklyWinners.objects.values("ticket_id")
    #     )

    #     if eligible_cards.exists():
    #         winner_card = random.choice(eligible_cards)
    #         today_str = timezone.now().date().strftime("%Y-%m-%d")

    #         WeeklyWinners.objects.create(week_date=today_str, ticket=winner_card)
    #         # send sms here
    #         winner_message = f"Congratulations, you have been drawn as a winner in the Winwise Lotto Draw, Business Edition. Your winning number is: {winner_card.index}."
    #         send_whisper_sms.delay(message=winner_message, user_phone=winner_card.player_phone_number)
    #         messages.success(request, f"Scractch Card with Index: <{winner_card.index}> has been picked as a successful winner for the week.")
    #     else:
    #         modeladmin.message_user(request, "No eligible scratch cards found for the week.", level="warning")

    @admin.action(description="START LOTTO DRAW")
    def pick_random_winner(modeladmin, request, queryset):
        eligible_cards = ScratchCard.objects.filter(player_phone_number__isnull=False, sold=True, paid=True, serial_skew="S", drawn=False).exclude(
            id__in=WeeklyWinners.objects.values("ticket_id")
        )

        if not eligible_cards.exists():
            modeladmin.message_user(request, "No eligible scratch cards found for the week.", level="warning")
            return

        variables = Variables.objects.all().first()
        no_draw = variables.number_tickets_to_draw
        top_agents = eligible_cards.values("agent").annotate(count=Count("id")).order_by("-count")[:no_draw]

        top_agent_ids = [agent["agent"] for agent in top_agents]

        winners = []
        for agent_id in top_agent_ids:
            agent_cards = eligible_cards.filter(agent_id=agent_id)
            if agent_cards.exists():
                winners.append(random.choice(list(agent_cards)))

        if winners:
            today_str = timezone.now().date().strftime("%Y-%m-%d")

            for winner_card in winners:
                WeeklyWinners.objects.create(week_date=today_str, ticket=winner_card)

                winner_message = f"Congratulations! You have been drawn as a winner in the Winwise Lotto Draw, Business Edition. Your winning number is: {winner_card.index}."
                send_whisper_sms.delay(message=winner_message, user_phone=winner_card.player_phone_number)
                messages.success(request, f"Scratch Card with Index: <{winner_card.index}> has been picked as a successful winner for the week.")
        else:
            modeladmin.message_user(request, "No eligible winners were found for the selected agents.", level="warning")

    @admin.action(description="Set Tickets to Drawn")
    def set_tickets_to_drawn(modeladmin, request, queryset):
        eligible_cards = ScratchCard.objects.filter(player_phone_number__isnull=False, sold=True, paid=True, drawn=False)
        eligible_cards.update(drawn=True, week_drawn=timezone.now().date())
        messages.success(request, "Successfully set tickets to drawn!")

    @admin.action(description="SET SELECTED TICKETS TO DRAWN")
    def set_specific_tickets_to_drawn(modeladmin, request, queryset):
        # eligible_cards = ScratchCard.objects.filter(player_phone_number__isnull=False, sold=True, paid=True, drawn=False)
        queryset.update(drawn=True, week_drawn=timezone.now().date())
        messages.success(request, "Successfully set selected tickets to drawn!")

    @admin.action(description="UPDATE TICKETS FROM FILE")
    def update_tickets_from_file(modeladmin, request, queryset):
        variables = Variables.objects.all().first()
        if not variables or not variables.ticket_update_file:
            messages.error(request, "No ticket update file found.")
            return

        csv_file_content = variables.ticket_update_file
        csv_file = StringIO(csv_file_content)
        reader = csv.reader(csv_file)

        for row in reader:
            if len(row) < 2:
                continue  # skip row if it does not have at least 2 columns (improperly formatted)

            index = row[0].strip()
            earning = row[1].strip().lower()
            earning_amount = float(row[2].strip()) if len(row) > 2 and earning == "won" else 0.0

            try:
                scratch_card = ScratchCard.objects.get(index=index, serial_skew="S")
                if earning == "lost":
                    scratch_card.earning = "Lost"
                elif earning == "won":
                    scratch_card.earning = "Won"
                    scratch_card.earning_amount = earning_amount
                elif earning == "weekly draw":
                    scratch_card.earning = "Weekly Draw"
                else:
                    continue

                scratch_card.save()
                messages.success(request, f"Successfully updated ScratchCard with index {index}.")
            except ScratchCard.DoesNotExist:
                messages.warning(request, f"ScratchCard with index {index} not found. Skipping.")


class PayOutResourceAdmin(ImportExportModelAdmin):
    resource_class = PayOutResource
    search_fields = ["phone_number", "account_name", "account_number", "bank_code", "transaction_reference"]
    list_filter = ("status",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = ("retry_transaction",)  # Necessary

    @admin.action(description="Retry Selected Failed Transaction")
    def retry_transaction(modeladmin, request, queryset):
        for transaction in queryset:
            PayOut.retry_transaction(transaction_reference=transaction.transaction_reference)
            messages.success(request, "Successfully retried transaction!")


class ScratchCardAgentsResourceAdmin(ImportExportModelAdmin):
    resource_class = ScratchCardAgents
    search_fields = ["first_name", "last_name", "phone_number", "email"]
    date_hierachy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class VariablesResourceAdmin(ImportExportModelAdmin):
    resource_class = VariablesResource
    # search_fields = ["first_name", "last_name", "phone_number", "email"]
    date_hierachy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WeeklyWinnersAdmin(ImportExportModelAdmin):
    resource_class = WeeklyWinnersResource
    search_fields = ["ticket__index", "week_date", "ticket__player_phone_number"]
    list_filter = ("week_date",)
    list_display = ["week_date", "ticket", "created_at", "updated_at"]
    date_hierarchy = "created_at"


class CountAgentTicketsAdmin(admin.ModelAdmin):
    list_display = ["agent", "counts", "updated_at"]
    search_fields = ["start_date", "end_date", "agent__first_name", "agent__last_name", "agent__phone_number"]
    ordering = ["-counts"]

    def update_agent_ticket_counts(self):
        agents = ScratchCard.objects.filter(paid=True, sold=True, player_phone_number__isnull=False, agent__isnull=False, serial_skew="S")
        for agent in agents:
            ticket_count = ScratchCard.objects.filter(
                paid=True, sold=True, player_phone_number__isnull=False, agent=agent.agent, serial_skew="S"
            ).count()
            count_agent_ticket, created = CountAgentTickets.objects.get_or_create(agent=agent.agent)
            count_agent_ticket.counts = ticket_count
            count_agent_ticket.updated_at = timezone.now()
            count_agent_ticket.save()

    def get_search_results(self, request, queryset, search_term):
        self.update_agent_ticket_counts()
        start_date = request.GET.get("start_date", None)
        end_date = request.GET.get("end_date", None)

        if start_date and end_date:
            start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d")
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d") + datetime.timedelta(days=1)
            agent_ids = ScratchCard.objects.filter(
                paid=True,
                sold=True,
                player_phone_number__isnull=False,
                agent__isnull=False,
                serial_skew="S",
                purchased_at__range=(start_date, end_date),
            ).values_list("agent", flat=True)

            queryset = CountAgentTickets.objects.filter(agent__in=agent_ids)
            return queryset, False

        if search_term:
            terms = search_term.split(" ")
            if len(terms) == 2:
                try:
                    start_date = datetime.datetime.strptime(terms[0], "%Y-%m-%d")
                    end_date = datetime.datetime.strptime(terms[1], "%Y-%m-%d") + datetime.timedelta(days=1)
                    scratch_cards = (
                        ScratchCard.objects.filter(
                            paid=True,
                            sold=True,
                            player_phone_number__isnull=False,
                            agent__isnull=False,
                            serial_skew="S",
                            purchased_at__range=(start_date, end_date),
                        )
                        .values("agent")
                        .annotate(count=Count("id"))
                    )

                    for agent_data in scratch_cards:
                        agent_id = agent_data["agent"]
                        count = agent_data["count"]
                        CountAgentTickets.objects.update_or_create(agent_id=agent_id, defaults={"counts": count, "updated_at": timezone.now()})

                    agent_ids = [agent_data["agent"] for agent_data in scratch_cards]
                    total_count = sum([agent_data["count"] for agent_data in scratch_cards])
                    request.total_count = total_count
                    queryset = CountAgentTickets.objects.filter(agent__in=agent_ids)
                    return queryset, False
                    # ).values_list('agent', flat=True)

                    # queryset = CountAgentTickets.objects.filter(agent__in=agent_ids)
                except ValueError:
                    queryset = CountAgentTickets.objects.filter(
                        Q(agent__first_name__icontains=search_term)
                        | Q(agent__last_name__icontains=search_term)
                        | Q(agent__full_name__icontains=search_term)
                    )
                    total_count = sum([q.counts for q in queryset])
                    request.total_count = total_count
                    return queryset, False
            else:
                queryset = CountAgentTickets.objects.filter(
                    Q(agent__first_name__icontains=search_term)
                    | Q(agent__last_name__icontains=search_term)
                    | Q(agent__full_name__icontains=search_term)
                )
                total_count = sum([q.counts for q in queryset])
                request.total_count = total_count
                return queryset, False

        else:
            queryset = CountAgentTickets.objects.filter(
                Q(agent__first_name__icontains=search_term) | Q(agent__last_name__icontains=search_term) | Q(agent__full_name__icontains=search_term)
            )
            total_count = sum([q.counts for q in queryset])
            request.total_count = total_count
            return queryset, False

    def changelist_view(self, request, extra_context=None):
        response = super().changelist_view(request, extra_context)
        total_count = getattr(request, "total_count", 0)
        if total_count:
            messages.info(request, f"Total Counts: {total_count}")
        return response


class WeekDateFilter(admin.SimpleListFilter):
    title = 'Week Date'
    parameter_name = 'week_date'

    def lookups(self, request, model_admin):
        """Return a list of tuples for filtering options"""
        # Get all unique week_date values from the database
        week_dates = GameShowLotteryTicket.objects.values_list('week_date', flat=True).distinct().order_by('-week_date')

        # Filter out None values and create tuples for the filter
        lookups = []
        for week_date in week_dates:
            if week_date:
                # Format the date for display
                formatted_date = week_date.strftime('%Y-%m-%d (%A)')
                lookups.append((week_date.strftime('%Y-%m-%d'), formatted_date))

        return lookups

    def queryset(self, request, queryset):
        """Filter the queryset based on the selected week_date"""
        if self.value():
            return queryset.filter(week_date=self.value())
        return queryset


class GameShowLotteryTicketResourceAdmin(ImportExportModelAdmin):
    resource_class = GameShowLotteryTicketResource
    search_fields = [
        "index",  # Changed from "full_index" since that field was removed
        "serial_skew",
        "serial_number",
        "player_phone_number",
        "claimant_account_name",
        "claimant_account_number",
        "claimant_phone_number",
        "claimant_bank_code",
    ]
    list_filter = (
        WeekDateFilter,  # Custom week_date filter
        "sold",
        "scratched",
        "claimed",
        "instant_win_status",
        "weekly_draw_is_winner",
        "is_game_show_winner",
        "week_number",
        "agent"
    )
    date_hierarchy = "date_created"
    ordering = ["-date_updated"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = (
        "mark_as_sold",
        "mark_as_scratched",
        "mark_as_claimed",
        "set_instant_winners",
        "set_weekly_winners",
        "generate_weekly_draw",
    )

    @admin.action(description="Mark as sold")
    def mark_as_sold(modeladmin, request, queryset):
        for obj in queryset:
            obj.sold = True
            obj.save()
            messages.success(request, "Successfully marked as sold!")

    @admin.action(description="Mark as scratched")
    def mark_as_scratched(modeladmin, request, queryset):
        for obj in queryset:
            obj.scratched = True
            obj.save()
            messages.success(request, "Successfully marked as scratched!")

    @admin.action(description="Mark as claimed")
    def mark_as_claimed(modeladmin, request, queryset):
        for obj in queryset:
            obj.claimed = True
            obj.save()
            messages.success(request, "Successfully marked as claimed!")

    @admin.action(description="Set instant winners (1:1:3 ratio)")
    def set_instant_winners(modeladmin, request, queryset):
        losing_tickets = queryset.filter(instant_win_status="LOST")
        if losing_tickets.count() < 5:
            messages.warning(request, "Need at least 5 losing tickets to set winners.")
            return

        # Select 5 tickets for 1:1:3 ratio
        winners = list(losing_tickets[:5])
        prize_amounts = [8000, 2000, 1000, 1000, 1000]  # 1:1:3 ratio

        for i, ticket in enumerate(winners):
            ticket.instant_win_status = "WON"
            ticket.earning_amount = prize_amounts[i]
            ticket.save()

        messages.success(request, f"Successfully set {len(winners)} instant winners!")

    @admin.action(description="Set weekly draw winners (4 winners + 1 game show winner)")
    def set_weekly_winners(modeladmin, request, queryset):
        eligible_tickets = queryset.filter(
            sold=True,
            player_phone_number__isnull=False,
            weekly_draw_is_winner=False
        )

        if not eligible_tickets.exists():
            messages.warning(request, "No eligible tickets for weekly draw.")
            return

        if eligible_tickets.count() < 4:
            messages.warning(request, f"Need at least 4 eligible tickets. Only {eligible_tickets.count()} available.")
            return

        # Select exactly 4 winners
        winners = random.sample(list(eligible_tickets), 4)

        # Mark all 4 as weekly draw winners
        for winner in winners:
            winner.weekly_draw_is_winner = True
            winner.weekly_win_status = "WON"
            winner.weekly_earning_amount = 200000.0
            winner.save()

        # Select 1 of the 4 as game show winner
        game_show_winner = random.choice(winners)
        game_show_winner.is_game_show_winner = True
        # Game show winner gets both weekly + game show amount
        game_show_winner.weekly_earning_amount = 200000.0 + 1000000.0  # ₦1,200,000 total
        game_show_winner.save()

        messages.success(
            request,
            f"Successfully set 4 weekly draw winners! "
            f"Game show winner: {game_show_winner.ticket_number} (₦1,200,000)"
        )

    @admin.action(description="Generate weekly draw numbers")
    def generate_weekly_draw(modeladmin, request, queryset):
        for ticket in queryset:
            # Generate 5 random numbers between 1-49
            numbers = random.sample(range(1, 50), 5)
            ticket.weekly_draw_number_1 = numbers[0]
            ticket.weekly_draw_number_2 = numbers[1]
            ticket.weekly_draw_number_3 = numbers[2]
            ticket.weekly_draw_number_4 = numbers[3]
            ticket.weekly_draw_number_5 = numbers[4]
            ticket.save()

        messages.success(request, f"Successfully generated weekly draw numbers for {queryset.count()} tickets!")


class WeeklyDrawResultResourceAdmin(ImportExportModelAdmin):
    resource_class = WeeklyDrawResultResource
    search_fields = ["week_number", "draw_date"]
    list_filter = ("is_completed", "draw_date", "total_winners")
    date_hierarchy = "draw_date"
    ordering = ["-week_number"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = (
        "generate_winning_numbers",
        "conduct_draw",
        "mark_completed",
        "calculate_winners",
    )

    @admin.action(description="Generate winning numbers")
    def generate_winning_numbers(modeladmin, request, queryset):
        for draw in queryset:
            # Generate 5 random numbers between 1-49
            numbers = random.sample(range(1, 50), 5)
            draw.winning_number_1 = numbers[0]
            draw.winning_number_2 = numbers[1]
            draw.winning_number_3 = numbers[2]
            draw.winning_number_4 = numbers[3]
            draw.winning_number_5 = numbers[4]
            draw.save()

        messages.success(request, f"Successfully generated winning numbers for {queryset.count()} draws!")

    @admin.action(description="Conduct weekly draw")
    def conduct_draw(modeladmin, request, queryset):
        for draw_result in queryset:
            if draw_result.is_completed:
                continue

            # Generate winning numbers if not set
            if not all([draw_result.winning_number_1, draw_result.winning_number_2,
                       draw_result.winning_number_3, draw_result.winning_number_4,
                       draw_result.winning_number_5]):
                numbers = random.sample(range(1, 50), 5)
                draw_result.winning_number_1 = numbers[0]
                draw_result.winning_number_2 = numbers[1]
                draw_result.winning_number_3 = numbers[2]
                draw_result.winning_number_4 = numbers[3]
                draw_result.winning_number_5 = numbers[4]

            # Find matching tickets
            matching_tickets = GameShowLotteryTicket.objects.filter(
                week_number=draw_result.week_number,
                sold=True,
                player_phone_number__isnull=False
            )

            winners_count = 0
            total_prize = 0.0

            for ticket in matching_tickets:
                is_winner, matches = draw_result.check_ticket_match(ticket)
                if is_winner:
                    ticket.weekly_draw_is_winner = True
                    ticket.weekly_win_status = "WON"
                    ticket.weekly_earning_amount = 200000.0
                    ticket.save()
                    winners_count += 1
                    total_prize += 200000.0

            draw_result.total_winners = winners_count
            draw_result.total_prize_amount = total_prize
            draw_result.is_completed = True
            draw_result.save()

        messages.success(request, f"Successfully conducted draw for {queryset.count()} weeks!")

    @admin.action(description="Mark as completed")
    def mark_completed(modeladmin, request, queryset):
        for obj in queryset:
            obj.is_completed = True
            obj.save()
            messages.success(request, "Successfully marked as completed!")

    @admin.action(description="Calculate winners and prizes")
    def calculate_winners(modeladmin, request, queryset):
        for draw_result in queryset:
            matching_tickets = GameShowLotteryTicket.objects.filter(
                week_number=draw_result.week_number,
                weekly_draw_is_winner=True
            )

            winners_count = matching_tickets.count()
            total_prize = winners_count * 200000.0

            draw_result.total_winners = winners_count
            draw_result.total_prize_amount = total_prize
            draw_result.save()

        messages.success(request, f"Successfully calculated winners for {queryset.count()} draws!")


# ============================================================================
# NEW LOTTERY TICKET SYSTEM ADMIN CLASSES
# ============================================================================

class GoldenHourCompanyResource(resources.ModelResource):
    class Meta:
        model = GoldenHourCompany


class GoldenHourBatchResource(resources.ModelResource):
    class Meta:
        model = GoldenHourBatch


class GoldenHourTicketResource(resources.ModelResource):
    class Meta:
        model = GoldenHourTicket


class GoldenHourCompanyResourceAdmin(ImportExportModelAdmin):
    resource_class = GoldenHourCompanyResource
    search_fields = ["name", "code", "description", "secret_key"]
    list_filter = ("is_active", "created_at")
    list_display = ("name", "code", "secret_key", "description", "secret_key", "is_active", "created_at")
    readonly_fields = ("secret_key", "created_at", "updated_at")
    date_hierarchy = "created_at"
    ordering = ["name"]

    fieldsets = (
        ("Company Information", {
            "fields": ("name", "code", "description", "is_active")
        }),
        ("Authentication", {
            "fields": ("secret_key",),
            "description": "Auto-generated secret key for API authentication"
        }),
        ("Timestamps", {
            "fields": ("created_at", "updated_at"),
            "classes": ("collapse",)
        }),
    )


class GoldenHourBatchResourceAdmin(ImportExportModelAdmin):
    resource_class = GoldenHourBatchResource
    search_fields = ["name", "description"]
    list_filter = ("status", "created_at", "start_time", "close_time")
    list_display = ("name", "status", "start_time", "close_time", "opened_at", "closed_at", "created_at")
    date_hierarchy = "created_at"
    ordering = ["-created_at"]
    readonly_fields = ("opened_at", "closed_at")

    actions = ("open_batch", "close_batch", "select_golden_hour_winners_action")

    @admin.action(description="Open selected batches")
    def open_batch(self, request, queryset):
        for batch in queryset:
            if batch.status != "OPEN":
                batch.status = "OPEN"
                batch.save()
        self.message_user(request, f"Opened {queryset.count()} batch(es)")

    @admin.action(description="Close selected batches")
    def close_batch(self, request, queryset):
        for batch in queryset:
            if batch.status != "CLOSED":
                batch.status = "CLOSED"
                batch.save()
        self.message_user(request, f"Closed {queryset.count()} batch(es)")
        
    @admin.action(description="Select Golden Hour Winners")
    def select_golden_hour_winners_action(modeladmin, request, queryset):
        open_batches = GoldenHourBatch.objects.filter(status="OPEN")
        print(f"Open batches: {open_batches.count()}")
        if not open_batches.exists():
            modeladmin.message_user(request, "No open Golden Hour batches found.", level="warning")
            return

        # Get config
        config = ConstantVariable.objects.first()
        if not config:
            modeladmin.message_user(request, "ConstantVariable config not found.", level="error")
            return
        num_winners = getattr(config, "golden_hour_no_winners", 5)
        total_amount = getattr(config, "golden_hour_daily_winning_amount", 100000.0)

        for batch in open_batches:
            eligible_tickets = GoldenHourTicket.objects.filter(batch=batch, is_paid=True, is_claimed=False)
            print(f"Eligible tickets for batch {batch.name}: {eligible_tickets.count()}")
            ticket_count = eligible_tickets.count()
            if ticket_count == 0:
                modeladmin.message_user(request, f"No eligible tickets in batch {batch.name}.", level="warning")
                continue
            winners_count = min(num_winners, ticket_count)
            win_amount = total_amount / winners_count if winners_count else 0
            winners = random.sample(list(eligible_tickets), winners_count)
            for ticket in winners:
                ticket.is_won = True
                ticket.amount_won = win_amount
                ticket.save()
            # Close the batch
            batch.status = "CLOSED"
            batch.closed_at = timezone.now()
            batch.save()
            # Open a new batch (incremental name)
            new_batch_name = f"GoldenHour_{timezone.now().strftime('%Y%m%d_%H%M%S')}"
            GoldenHourBatch.objects.create(
                name=new_batch_name,
                status="OPEN",
                start_time=timezone.now(),
                close_time=timezone.now() + timezone.timedelta(days=1),
            )
        modeladmin.message_user(request, "Golden Hour winners selected, batches closed, and new batch opened.", level="success")


class GoldenHourTicketResourceAdmin(ImportExportModelAdmin):
    resource_class = GoldenHourTicketResource
    search_fields = ["ticket_number", "ticket_index", "purchase_id"]
    list_filter = ("company", "batch", "is_paid", "is_claimed", "created_at")
    list_display = ("ticket_number", "ticket_index", "company", "batch", "purchase_id", "is_paid", "is_claimed", "created_at")
    date_hierarchy = "created_at"
    ordering = ["-ticket_index"]
    readonly_fields = ("ticket_index", "ticket_number")

    def get_list_display(self, request):
        return self.list_display

    actions = ("mark_as_paid", "mark_as_claimed")

    @admin.action(description="Mark as paid")
    def mark_as_paid(self, request, queryset):
        queryset.update(is_paid=True, sold_at=timezone.now())
        self.message_user(request, f"Marked {queryset.count()} ticket(s) as paid")

    @admin.action(description="Mark as claimed")
    def mark_as_claimed(self, request, queryset):
        queryset.update(is_claimed=True, claimed_at=timezone.now())
        self.message_user(request, f"Marked {queryset.count()} ticket(s) as claimed")






class PayoutLogResourceAdmin(ImportExportModelAdmin):
    resource_class = PayoutLogResource
    search_fields = ["phone_number", ]
    list_filter = ["created_at"]


    def get_list_display(self, request):
        return self.list_display

admin.site.register(CountAgentTickets, CountAgentTicketsAdmin)
admin.site.register(WeeklyWinners, WeeklyWinnersAdmin)
admin.site.register(ScratchCardAgents, ScratchCardAgentsResourceAdmin)
admin.site.register(ScratchCard, ScratchCardResourceAdmin)
admin.site.register(PayOut, PayOutResourceAdmin)
admin.site.register(Variables, VariablesResourceAdmin)

# Register Game Show Lottery models
admin.site.register(GameShowLotteryTicket, GameShowLotteryTicketResourceAdmin)
admin.site.register(WeeklyDrawResult, WeeklyDrawResultResourceAdmin)

# Register new Golden Hour lottery ticket system models
admin.site.register(GoldenHourCompany, GoldenHourCompanyResourceAdmin)
admin.site.register(GoldenHourBatch, GoldenHourBatchResourceAdmin)
admin.site.register(GoldenHourTicket, GoldenHourTicketResourceAdmin)
admin.site.register(PayoutLog, PayoutLogResourceAdmin)