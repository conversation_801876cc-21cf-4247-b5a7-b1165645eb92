import random
import string
from django.db import IntegrityError
from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from django.utils.timezone import localtime

from awoof_app.paginator import CustomPagination
from main.models import ConstantVariable, UserProfile
from scratch_cards.authentication import GoldenHourCompanyAuthentication
from scratch_cards.helpers.api import get_transaction_status, send_scratch_card_payout
from scratch_cards.helpers.utils import (
    agents_commission_for_lotto_draw,
    charge_agent_wallet_for_lotto_tickets,
    get_serializer_key_error,
)
from scratch_cards.models import GoldenHourBatch, GoldenHourTicket, PayOut, ScratchCard, ScratchCardAgents, WeeklyWinners
from scratch_cards.serializers import (
    BuyGoldenHourTicketSerializer,
    BuyTicketSerializer,
    GetScratchCardAmountSerializer,
    MultipleCreateScratchCardSerializer,
    NewScratchCardWithdrawalSerializer,
    PayScratchCardSerializer,
    RetryFailedWithdrawalSerializer,
    ScratchCardWithdrawalSerializer,
    SellScratchCardSerializer,
    ValidateTicketSerializer,
    WeeklyWinnerSerializer,
)
from scratch_cards.tasks import send_whisper_sms
from wallet_app.helpers.payment_gateway import PaymentGateway
from wallet_app.models import PaystackTransaction

# Create your views here.


class CreateScratchCardsAPIView(APIView):
    def post(self, request):
        serializer = MultipleCreateScratchCardSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data.get("data")
        ScratchCard.create_scratch_card(data=data)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class GetScratchCardAmountAPIView(APIView):
    def post(self, request):
        serializer = GetScratchCardAmountSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.validated_data, status=status.HTTP_200_OK)


class ScratchCardWithdrawalAPIView(APIView):
    def post(self, request):
        serializer = ScratchCardWithdrawalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        transaction_reference = serializer.validated_data.get("transaction_reference")
        # scratch_card = serializer.validated_data.get("scratch_card")
        # bank_code = serializer.validated_data.get("bank_code")
        # account_number = serializer.validated_data.get("account_number")
        # account_name = serializer.validated_data.get("account_name")
        amount = serializer.validated_data.get("amount")

        payout = send_scratch_card_payout(transaction_reference=transaction_reference)
        if payout.get("status") == "failed":
            payout_response = {"status": "failed", "success": False, "message": "an error occurred try again", "amount": ""}
        else:
            if payout.get("status_code") == 200:
                payout_response = {"status": "success", "success": True, "message": "payout successful", "amount": amount}

            elif payout.get("status_code") == 400:
                payout_response = {"status": "failed", "success": False, "message": "payout failed, try again", "amount": ""}
            else:
                payout_response = {"status": "failed", "success": False, "message": "payout failed, try again", "amount": ""}

        return Response(payout_response, status=status.HTTP_200_OK)


class NewScratchCardWithdrawalAPIView(APIView):
    def post(self, request):
        serializer = NewScratchCardWithdrawalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        transaction_reference = serializer.validated_data.get("transaction_reference")
        amount = serializer.validated_data.get("amount")

        payout = send_scratch_card_payout(transaction_reference=transaction_reference)
        if payout.get("status") == "failed":
            payout_response = {"status": "failed", "success": False, "message": "an error occurred try again", "amount": ""}
        else:
            if payout.get("status_code") == 200:
                payout_response = {"status": "success", "success": True, "message": "payout successful", "amount": amount}

            elif payout.get("status_code") == 400:
                payout_response = {"status": "failed", "success": False, "message": "payout failed, try again", "amount": ""}
            else:
                payout_response = {"status": "failed", "success": False, "message": "payout failed, try again", "amount": ""}

        return Response(payout_response, status=status.HTTP_200_OK)


class SellScratchCardAPIView(APIView):
    def post(self, request):
        serializer = SellScratchCardSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.validated_data, status=status.HTTP_200_OK)


class PayScratchCardAPIView(APIView):
    def post(self, request):
        serializer = PayScratchCardSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.validated_data, status=status.HTTP_200_OK)


class RetryFailedWithdrawalAPIView(APIView):
    def post(self, request):
        serializer = RetryFailedWithdrawalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        transaction_reference = serializer.validated_data.get("transaction_reference")
        ticket_instance = PayOut.retry_transaction(transaction_reference=transaction_reference)
        return Response(ticket_instance, status=status.HTTP_200_OK)


class GetTransactionStatusAPIView(APIView):
    def post(self, request):
        serializer = RetryFailedWithdrawalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        transaction_reference = serializer.validated_data.get("transaction_reference")
        transaction_status = get_transaction_status(transaction_reference=transaction_reference)
        return Response(transaction_status, status=status.HTTP_200_OK)


class ScratchCardAgentFormView(APIView):
    def post(self, request):
        data = request.data
        print("data")
        print(data)
        ScratchCardAgents.objects.create(
            first_name=data["First Name"],
            last_name=data["Last Name"],
            date_of_birth=data["Date of Birth"],
            nationality=data["Nationality"],
            phone_Number=data["Phone Number"],
            whatsapp_phone_Number=data["WhatsApp Phone Number"],
            email=data["email"],
            house_number=data["House Number"],
            street=data["Street"],
            landmark=data["Landmark"],
            bus_stop=data["Bus Stop"],
            lga=data["LGA"],
            state=data["State"],
            account_name=data["Account Name"],
            account_number=data["Account Number"],
            bank_name=data["Bank Name"],
            nok_name=data["Next of Kin Name"],
            nok_phone_number=data["Next of Kin Phone Number"],
            nok_address=data["Next of Kin Address"],
            nok_relationship=data["Next of Kin Relationship"],
            nin=data["NIN"],
            sales_manager=data["Sales Manager"],
        )
        return Response(data=data, status=status.HTTP_201_CREATED)

    # {'First Name': 'Adebayo', 'Last Name': 'Craig', 'Date of Birth': '2024-06-06',
    # 'Nationality': 'Nigeria', 'Address': '256 Chapman Road STE 105-4', 'State': 'Lagos',
    # 'Country of Residence': 'Nigeria', 'Phone Number': '************', 'email': '<EMAIL>'}


# whatsapp phone number*,
# house no, street, landmark, bus stop, lga, state, nin*,
# account name, account number, bank name,
# next of kin: name, number, address, relationship


# class GetScratchCards(GenericViewSet):

#     pagination_class = PaginationClass

#     def get_queryset(self):
#         # refine for the agent making the request, that is only return tickets for an agent.
#         scratch_card = ScratchCard.objects.all()

#         skew = self.request.query_params.get('skew', None)
#         pick = self.request.query_params.get('pick', None)
#         result =  self.request.query_params.get('result', None)
#         paid = self.request.query_params.get('paid', None)
#         earning = self.request.query_params.get('earning', None)
#         earning_amount = self.request.query_params.get('earning_amount', None)
#         claimed = self.request.query_params.get("claimed", None)

#         if skew:
#             scratch_card = scratch_card.filter(serial_skew=skew)
#         if pick:
#             scratch_card = scratch_card.filter(pick=pick)
#         if result:
#             scratch_card = scratch_card.filter(result=result)
#         if paid:
#             scratch_card = scratch_card.filter(paid=paid)
#         if earning:
#             scratch_card = scratch_card.filter(earning=earning)
#         if earning_amount:
#             scratch_card = scratch_card.filter(earning_amount=earning_amount)
#         if claimed:
#             scratch_card = scratch_card.filter(claimed=claimed)

#         return scratch_card

#     def list(self, request):
#         pass


class ValidateTicket(APIView):
    def post(self, request):
        serializer = ValidateTicketSerializer(data=request.data)
        if serializer.is_valid():
            constant = ConstantVariable.objects.all().last()
            amount = constant.lotto_scratch_card_amount
            len_of_index = serializer.validated_data.get("len_of_index")

            sum_amount = amount * len_of_index
            data = {"status": "success", "amount": sum_amount, "number_of_tickets": len_of_index}
            return Response(data, status=status.HTTP_200_OK)
        else:
            data = {"status": "failed", "message": get_serializer_key_error(serializer.errors)}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class BuyTicket(APIView):
    def post(self, request):
        serializer = BuyTicketSerializer(data=request.data, context={"request": request})
        if serializer.is_valid():
            validated_data = serializer.validated_data
            tickets = validated_data.get("scratch_cards")
            player_phone_number = validated_data.get("player_phone_number")
            agent = validated_data.get("agent")
            len_of_index = validated_data.get("len_of_index")
            # amount = validated_data.get("amount")
            pin = validated_data.get("pin")
            constant = ConstantVariable.objects.all().last()
            amount = constant.lotto_scratch_card_amount
            commission_percent = constant.lotto_agent_commission_percent
            sum_amount = amount * len_of_index

            agent_helper_response = charge_agent_wallet_for_lotto_tickets(agent, sum_amount, pin)
            # agent_helper_response = {"message": "success"}
            if agent_helper_response.get("message") == "success":
                for ticket in tickets:
                    ticket.sold = True
                    ticket.paid = True
                    ticket.agent = agent
                    ticket.ticket_amount_paid = amount
                    ticket.player_phone_number = player_phone_number
                    ticket.purchased_at = timezone.now()
                    ticket.save()

                all_tickets = [t.index for t in tickets]
                message = f"You have successfully entered into the Winwise Business Edition Raffle to win up to 500k. Ticket number: `{all_tickets}`"
                send_whisper_sms.delay(message, player_phone_number)
                commission_amount = (commission_percent * sum_amount) / 100
                agents_commission_for_lotto_draw(agent, sum_amount, commission_amount)
                data = {"status": "success", "message": "Successfully assigned ticket"}
                return Response(data=data, status=status.HTTP_200_OK)
            else:
                if agent_helper_response.get("user_message") is not None:
                    data = {"status": "failed", "message": agent_helper_response.get("user_message")}
                else:
                    data = {"status": "failed", "message": "Unable to charge wallet."}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        else:
            data = {"status": "failed", "message": get_serializer_key_error(serializer.errors)}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class WeeklyWinnerView(APIView):
    def post(self, request):
        serializer = WeeklyWinnerSerializer(data=request.data)
        if serializer.is_valid():
            ticket = serializer.validated_data.get("ticket")
            week_date = serializer.validated_data.get("week_date")
            try:
                WeeklyWinners.objects.create(week_date=week_date, ticket=ticket)
                data = {"status": "success", "message": "Successfully saved weekly winner"}
                return Response(data, status=status.HTTP_201_CREATED)
            except IntegrityError:
                data = {"status": "failed", "message": "TIcket winner already exist for this date."}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        else:
            data = {"status": "failed", "message": get_serializer_key_error(serializer.errors)}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


# class WeeklyWinnersGroupedView(APIView):
#     pagination_class = CustomPagination()

#     def get(self, request):
#         # Get all WeeklyWinners ordered by created_at
#         winners = WeeklyWinners.objects.select_related('ticket').order_by('-created_at')

#         # Step 1: Group winners by exact `created_at` datetime (or date if you prefer)
#         grouped = {}
#         for winner in winners:
#             key = localtime(winner.created_at).strftime("%Y-%m-%d %H:%M:%S")  # convert datetime to string
#             grouped.setdefault(key, []).append({
#                 "serial_number": winner.ticket.serial_number,
#                 "index": winner.ticket.index,
#                 "player_phone_number": winner.ticket.player_phone_number,
#             })

#             grouped_list = [
#                 {
#                     "created_at": date_str,
#                     "tickets": tickets
#                 } for date_str, tickets in grouped.items()
#             ]

#         # Step 3: Apply pagination to the grouped list
#         paginator = self.pagination_class
#         page = paginator.paginate_queryset(grouped_list, request, view=self)
#         return paginator.get_paginated_response(page)


class WeeklyWinnersGroupedView(APIView):
    pagination_class = CustomPagination()

    def get(self, request):
        from collections import defaultdict
        winners = WeeklyWinners.objects.select_related('ticket').order_by('-created_at')

        # Step 2: Group by date and keep track of latest datetime for that date
        grouped = defaultdict(list)
        latest_times = {}

        for winner in winners:
            local_created_at = localtime(winner.created_at)
            date_key = local_created_at.date().isoformat()

            if date_key not in latest_times or local_created_at > latest_times[date_key]:
                latest_times[date_key] = local_created_at

            # Add only specific fields from the related ScratchCard
            ticket = winner.ticket
            grouped[date_key].append({
                "index": ticket.index,
                "serial_number": ticket.serial_number,
                "player_phone_number": ticket.player_phone_number,
            })

        grouped_list = [
            {
                "created_at": latest_times[date_key].strftime("%Y-%m-%d %H:%M:%S"),
                "tickets": grouped[date_key]
            }
            for date_key in sorted(grouped.keys(), reverse=True)
        ]

        paginator = self.pagination_class
        page = paginator.paginate_queryset(grouped_list, request, view=self)
        return paginator.get_paginated_response(page)


# ============================================================================
# GOLDEN HOUR API VIEWS
# ============================================================================




class GenerateTicketIndicesAPIView(APIView):
    """
    API endpoint to generate 10 available ticket indices for a company

    Headers required:
    - X-Company-Code: Company code (e.g., 'LIB', 'ABC')
    - X-Secret-Key: Company's secret key

    Returns 10 available ticket indices in format: index-ticket_number
    Example: 20-T234HRYUIIH
    """

    authentication_classes = [GoldenHourCompanyAuthentication]

    def get(self, request):
        try:
            # Get the authenticated company
            company = request.auth

            # Check if there's an open batch for ticket generation
            open_batch = GoldenHourBatch.get_open_batch()
            if not open_batch:
                return Response({
                    'success': False,
                    'message': 'No open batch available for ticket generation',
                    'error_code': 'NO_OPEN_BATCH'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Generate 10 available ticket indices
            generated_indices = []
            attempts = 0
            max_attempts = 1000  # Prevent infinite loop

            # Get the starting index
            current_index = self._generate_next_available_index()

            while len(generated_indices) < 10 and attempts < max_attempts:
                # Generate ticket number
                ticket_number = self._generate_ticket_number()

                # Check if this combination is available (not in database)
                if not self._is_combination_available(current_index, ticket_number):
                    attempts += 1
                    continue

                # Format as index-ticket_number
                formatted_ticket = f"{current_index}-{ticket_number}"
                generated_indices.append(formatted_ticket)

                # Increment index for next ticket
                current_index += 1
                attempts += 1

            if len(generated_indices) < 10:
                return Response({
                    'success': False,
                    'message': f'Could only generate {len(generated_indices)} available indices after {max_attempts} attempts',
                    'error_code': 'GENERATION_LIMIT_REACHED'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({
                'success': True,
                'message': 'Successfully generated 10 available ticket indices',
                'data': generated_indices,
                'company_code': company.code,
                'total_generated': len(generated_indices)
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'An error occurred: {str(e)}',
                'error_code': 'INTERNAL_ERROR'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _generate_next_available_index(self):
        """
        Generate the next available ticket index
        """
        # Get the highest existing ticket_index
        latest_ticket = GoldenHourTicket.objects.order_by('-ticket_index').first()
        if latest_ticket:
            return latest_ticket.ticket_index + 1
        else:
            return 1

    def _generate_ticket_number(self):
        """
        Generate a random alphanumeric ticket number
        Format: 11 random alphanumeric characters (uppercase letters and digits)
        Example: A1B2C3D4E5F, K7M9P2Q8X1Y, Z9Y8X7W6V5U
        """
        # Generate 11 random alphanumeric characters (letters and digits)
        characters = string.ascii_uppercase + string.digits
        random_chars = ''.join(random.choices(characters, k=11))
        return random_chars

    def _is_combination_available(self, ticket_index, ticket_number):
        """
        Check if the ticket_index and ticket_number combination is available
        (not already in the database)
        """
        # Check if ticket_index already exists
        if GoldenHourTicket.objects.filter(ticket_index=ticket_index).exists():
            return False

        # Check if ticket_number already exists
        if GoldenHourTicket.objects.filter(ticket_number=ticket_number).exists():
            return False

        return True


class BuyGoldenHourTicketAPIView(APIView):
    """
    API endpoint to purchase Golden Hour tickets

    Headers required:
    - X-Company-Code: Company code (e.g., 'LIB', 'ABC')
    - X-Secret-Key: Company's secret key

    Body:
    - player_phone_number: Player's phone number
    - tickets: List of tickets in format ["index-ticket_number", ...]
    """

    authentication_classes = [GoldenHourCompanyAuthentication]

    def post(self, request):
        try:
            # Get the authenticated company
            company = request.auth

            # Check if there's an open batch for ticket purchase
            open_batch = GoldenHourBatch.get_open_batch()
            if not open_batch:
                return Response({
                    'success': False,
                    'message': 'No open batch available for ticket purchase',
                    'error_code': 'NO_OPEN_BATCH'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validate request data
            serializer = BuyGoldenHourTicketSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({
                    'success': False,
                    'message': 'Validation failed',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            player_phone_number = validated_data['player_phone_number']
            tickets_data = validated_data['tickets']

            # Generate purchase ID for all tickets (first 10 characters of UUID)
            import uuid
            purchase_id = str(uuid.uuid4())[:10]
            transaction_ref = f"GHL-{uuid.uuid4()}"

            const_table = ConstantVariable.objects.last()
            created_tickets = []
            for ticket_data in tickets_data:
                ticket = GoldenHourTicket.objects.create(
                    ticket_index=ticket_data['ticket_index'],
                    ticket_number=ticket_data['ticket_number'],
                    company=company,
                    batch=open_batch,
                    amount=const_table.golden_hour_ticket_price,
                    purchase_id=purchase_id,
                    is_paid=False,  # Leave as false as requested
                    sold_at=timezone.now(),
                    player_phone_number=player_phone_number,
                    transaction_reference=transaction_ref
                )
                created_tickets.append(ticket)
            
           
            amount = len(created_tickets) * const_table.golden_hour_ticket_price
            user_profile, _ = UserProfile.objects.get_or_create(phone_number=player_phone_number)
            
            paystack_payload = {
                "email": user_profile.email if user_profile.email is not None else "<EMAIL>",
                "amount": float(amount) * 100,
                "currency": "NGN",
                "reference": transaction_ref,
            }
            
            PaystackTransaction.objects.create(
                user=user_profile,
                amount=float(amount),
                reference=transaction_ref,
                created_at=timezone.now(),
                paid_at=timezone.now(),
                channel="WEB",
                raw_data=paystack_payload,
                payment_reason="GOLDEN_HOUR_LOTTERY",
            )

            # initiate payment
            paystack_api = PaymentGateway().paystack_link_request(**paystack_payload)

            payment_link = paystack_api.get("authorization_url")

            return Response({
                'success': True,
                'message': f'Successfully purchased {len(created_tickets)} Golden Hour tickets',
                'data': {
                    'tickets_purchased': [f"{t.ticket_index}-{t.ticket_number}" for t in created_tickets],
                    'player_phone_number': player_phone_number,
                    'batch_name': open_batch.name
                },
                'purchase_id': purchase_id,
                'paystack_url': payment_link,
                'total_tickets': len(created_tickets),
                'company_code': company.code
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'An error occurred: {str(e)}',
                'error_code': 'INTERNAL_ERROR'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



class VerifyGoldenHourTicketPayment(APIView):
    """
    Verifies Paystack payment for Golden Hour lottery tickets using a purchase_id.
    """

    def get(self, request):
        purchase_id = request.query_params.get('purchase_id')

        if not purchase_id:
            return Response({
                'success': False,
                'message': 'Missing purchase_id query parameter'
            }, status=status.HTTP_400_BAD_REQUEST)

        tickets = GoldenHourTicket.objects.filter(purchase_id=purchase_id)

        if not tickets.exists():
            return Response({
                'success': False,
                'message': 'No tickets found for this purchase_id'
            }, status=status.HTTP_404_NOT_FOUND)
            
        transaction_ref = tickets.first().transaction_reference

        try:
            verification_data = PaymentGateway().paystack_verify_payment(transaction_ref)
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error verifying payment: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        if not isinstance(verification_data, dict):
            return Response({
                'success': False,
                'message': 'Invalid verification response from payment gateway'
            }, status=status.HTTP_400_BAD_REQUEST)

        data = verification_data.get("data", {})
        status_check = data.get("status", "").lower() == "success" or verification_data.get("message") == "Verification successful"

        if not status_check:
            return Response({
                'success': False,
                'message': 'Payment verification failed or is not successful',
                'gateway_response': verification_data
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            paystack_txn = PaystackTransaction.objects.get(reference=transaction_ref)
        except PaystackTransaction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Transaction record not found in system'
            }, status=status.HTTP_404_NOT_FOUND)

        if paystack_txn.payment_reason != "GOLDEN_HOUR_LOTTERY":
            return Response({
                'success': False,
                'message': 'Transaction reason does not match expected GOLDEN_HOUR_LOTTERY'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Update tickets and transaction
        tickets.update(is_paid=True, updated_at=timezone.now())
        paystack_txn.status = "SUCCESSFUL"
        paystack_txn.is_verified = True
        paystack_txn.save()

        total_amount = sum(ticket.amount for ticket in tickets)
        ticket_list = [
            {"ticket_index": t.ticket_index, "ticket_number": t.ticket_number}
            for t in tickets
        ]
        batch_name = tickets.first().batch.name

        return Response({
            'success': True,
            'message': 'Payment successfully verified and tickets updated',
            'data': {
                'total_amount': total_amount,
                'tickets': ticket_list,
                'batch_name': batch_name
            }
        }, status=status.HTTP_200_OK)