from django.urls import path

from scratch_cards import views

app_name = "scratch_cards"

scratch_card = [
    path("add_scratch_cards/", views.CreateScratchCardsAPIView.as_view()),
    path("get_scratch_card_amount/", views.GetScratchCardAmountAPIView.as_view(), name="get-scratch-card-amount"),
    path("scratch_card_withdrawal/", views.ScratchCardWithdrawalAPIView.as_view(), name="scratch-card-withdrawal"),
    path("new_scratch_card_withdrawal/", views.NewScratchCardWithdrawalAPIView.as_view(), name="new_scratch-card-withdrawal"),
    path("sell_scratch_card/", views.SellScratchCardAPIView.as_view()),
    path("pay_scratch_card/", views.PayScratchCardAPIView.as_view()),
    # path("retry_failed_withdrawal/", views.RetryFailedWithdrawalAPIView.as_view()),
    path("get_transaction_status/", views.GetTransactionStatusAPIView.as_view()),
    path("agent-form/", views.ScratchCardAgentFormView.as_view()),
    path("validate-ticket/", views.ValidateTicket.as_view(), name="validate_ticket"),
    path("buy-ticket/", views.BuyTicket.as_view(), name="buy_ticket"),
    path("weekly-winners/", views.WeeklyWinnerView.as_view(), name="weekly_winners"),
    path("scratch_cards_results", views.WeeklyWinnersGroupedView.as_view(), name="scratch_card_results")
]

golden_hour_api = [
    path("generate-ticket-indices/", views.GenerateTicketIndicesAPIView.as_view(), name='generate_ticket_indices'),
    path("buy-golden-hour-tickets/", views.BuyGoldenHourTicketAPIView.as_view(), name='buy_golden_hour_tickets'),
    path("verify-golden-hour-ticket", views.VerifyGoldenHourTicketPayment.as_view(), name="verify_golden_hour_ticket")
]

urlpatterns = [
    *scratch_card,
    *golden_hour_api,
]
