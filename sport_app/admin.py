from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from sport_app.models import (
    AgentSoccerCashWinner,
    ChampionsLeagueWeekHistory,
    CutoutTable,
    DecisioningResult,
    DualTeamFinalist,
    DualTeamFinalistPrediction,
    EnglishWeekHistory,
    EuropaLeagueWeekHistory,
    FootballHistory,
    FootballTable,
    FootballTeamShortCode,
    GoalScorer,
    GoalScorerPrediction,
    ImportantTeams,
    LaligaWeekHistory,
    OddPredictionType,
    PlayerGoal,
    PredictionTable,
    RunningBalance,
    SoccerCashWinner,
    SoccerOddPredictionTable,
    SoccerPredictionLink,
    TeamFinalist,
    TeamFinalistPrediction,
    UserPredictionSubscriptionTable,
    VirtualLeagues,
    VirtualMatches,
    VirtualTeams,
    WorldCupTeams,
    WorldCupWeekHistory,
)

# Register your models here.


# RESOURCES
class FootballTableResource(resources.ModelResource):
    class Meta:
        model = FootballTable


class FootballHistoryResource(resources.ModelResource):
    class Meta:
        model = FootballHistory


class EnglishWeekHistoryResource(resources.ModelResource):
    class Meta:
        model = EnglishWeekHistory


class LaligaWeekHistoryResource(resources.ModelResource):
    class Meta:
        model = LaligaWeekHistory


class ChampionsLeagueWeekHistoryResource(resources.ModelResource):
    class Meta:
        model = ChampionsLeagueWeekHistory


class EuropaLeagueWeekHistoryResource(resources.ModelResource):
    class Meta:
        model = EuropaLeagueWeekHistory


class WorldCupWeekHistoryResource(resources.ModelResource):
    class Meta:
        model = WorldCupWeekHistory


class FootballTeamShortCodeResource(resources.ModelResource):
    class Meta:
        model = FootballTeamShortCode


class SoccerCashWinnerResource(resources.ModelResource):
    class Meta:
        model = SoccerCashWinner


class DecisioningResultResource(resources.ModelResource):
    class Meta:
        model = DecisioningResult


class GoalScorerResource(resources.ModelResource):
    class Meta:
        model = GoalScorer


class GoalScorerPredictionResource(resources.ModelResource):
    class Meta:
        model = GoalScorerPrediction


class PlayerGoalResource(resources.ModelResource):
    class Meta:
        model = PlayerGoal


class TeamFinalistResource(resources.ModelResource):
    class Meta:
        model = TeamFinalist


class TeamFinalistPredictionResource(resources.ModelResource):
    class Meta:
        model = TeamFinalistPrediction


class DualTeamFinalistResource(resources.ModelResource):
    class Meta:
        model = DualTeamFinalist


class WorldCupTeamsResource(resources.ModelResource):
    class Meta:
        model = WorldCupTeams


class DualTeamFinalistPredictionResource(resources.ModelResource):
    class Meta:
        model = DualTeamFinalistPrediction


class CutoutTableResource(resources.ModelResource):
    class Meta:
        model = CutoutTable


class ImportantTeamsResource(resources.ModelResource):
    class Meta:
        model = ImportantTeams


class RunningBalanceResource(resources.ModelResource):
    class Meta:
        model = RunningBalance


class AgentSoccerCashWinnerResource(resources.ModelResource):
    class Meta:
        model = AgentSoccerCashWinner


class VirtualLeaguesResource(resources.ModelResource):
    class Meta:
        model = VirtualLeagues


class VirtualMatchesresource(resources.ModelResource):
    class Meta:
        model = VirtualMatches


class VirtualTeamsResource(resources.ModelResource):
    class Meta:
        model = VirtualTeams


class PredictionTableResource(resources.ModelResource):
    class Meta:
        model = PredictionTable


class OddPredictionTypeResource(resources.ModelResource):
    class Meta:
        model = OddPredictionType


class SoccerOddPredictionTableResource(resources.ModelResource):
    class Meta:
        model = SoccerOddPredictionTable

class UserPredictionSubscriptionTableResource(resources.ModelResource):
    class Meta:
        model = UserPredictionSubscriptionTable

class SoccerPredictionLinkResource(resources.ModelResource):
    class Meta:
        model = SoccerPredictionLink


#
# ADMINS


class FootballHistoryResourceAdmin(ImportExportModelAdmin):
    resource_class = FootballHistoryResource
    search_fields = [
        "fixture_id",
        "lotto_id",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class FootballTableResourceAdmin(ImportExportModelAdmin):
    resource_class = FootballTableResource
    search_fields = [
        "fixture_id",
        "league_type",
        "match_status",
        "fixture_date",
        "league_id",
        "home_team",
        "away_team",
        "home_id",
        "away_id",
        "home_team_code",
        "away_team_code",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class EnglishWeekHistoryResourceAdmin(ImportExportModelAdmin):
    resource_class = EnglishWeekHistoryResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LaligaWeekHistoryResourceAdmin(ImportExportModelAdmin):
    resource_class = LaligaWeekHistoryResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ChampionsLeagueWeekHistoryResourceAdmin(ImportExportModelAdmin):
    resource_class = ChampionsLeagueWeekHistoryResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class EuropaLeagueWeekHistoryResourceAdmin(ImportExportModelAdmin):
    resource_class = EuropaLeagueWeekHistoryResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WorldCupWeekHistoryResourceAdmin(ImportExportModelAdmin):
    resource_class = WorldCupWeekHistoryResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class FootballTeamShortCodeResourceAdmin(ImportExportModelAdmin):
    resource_class = FootballTeamShortCodeResource
    search_fields = [
        "team_id",
        "team_code",
        "team_name",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SoccerCashWinnerResourceAdmin(ImportExportModelAdmin):
    resource_class = SoccerCashWinnerResource
    # search_fields = []
    list_filter = [
        "date_won",
    ]
    date_hierarchy = "date_won"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DecisioningResultResourceAdmin(ImportExportModelAdmin):
    resource_class = DecisioningResultResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class GoalScorerResourceAdmin(ImportExportModelAdmin):
    resource_class = GoalScorerResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class GoalScorerPredictionResourceAdmin(ImportExportModelAdmin):
    resource_class = GoalScorerPredictionResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


# class GoalScorerPredictionResourceAdmin(ImportExportModelAdmin):
#     resource_class = GoalScorerPredictionResource

#     # search_fields = []
#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


class PlayerGoalResourceAdmin(ImportExportModelAdmin):
    resource_class = PlayerGoalResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TeamFinalistResourceAdmin(ImportExportModelAdmin):
    resource_class = TeamFinalistResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TeamFinalistPredictionResourceAdmin(ImportExportModelAdmin):
    resource_class = TeamFinalistPredictionResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DualTeamFinalistResourceAdmin(ImportExportModelAdmin):
    resource_class = DualTeamFinalistResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WorldCupTeamsResourceAdmin(ImportExportModelAdmin):
    resource_class = WorldCupTeamsResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DualTeamFinalistPredictionResourceAdmin(ImportExportModelAdmin):
    resource_class = DualTeamFinalistPredictionResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CutoutTableResourceAdmin(ImportExportModelAdmin):
    resource_class = CutoutTableResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ImportantTeamsResourceAdmin(ImportExportModelAdmin):
    resource_class = ImportantTeamsResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RunningBalanceResourceAdmin(ImportExportModelAdmin):
    resource_class = RunningBalanceResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AgentSoccerCashWinnerResourceeAdmin(ImportExportModelAdmin):
    resource_class = AgentSoccerCashWinnerResource

    # search_fields = []
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class VirtualLeaguesResourceAdmin(ImportExportModelAdmin):
    resource_class = VirtualLeaguesResource
    search_fields = ["name", "country"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class VirtualTeamsResourceAdmin(ImportExportModelAdmin):
    resource_class = VirtualTeamsResource
    search_fields = ["name", "league__name", "league__country", "code"]
    date_hierarchy = "created_at"
    list_filter = ["league"]
    raw_id_fields = [
        "league",
    ]
    autocomplete_fields = [
        "league",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class VirtualMatchesResourceeAdmin(ImportExportModelAdmin):
    resource_class = VirtualMatchesresource
    search_fields = [
        "game_play_id",
        "user_profile__phone_number",
        "user_profile__email",
        "league__name",
        "league__country",
    ]
    date_hierarchy = "created_at"
    raw_id_fields = ["league", "home_team", "away_team"]
    autocomplete_fields = ["league", "home_team", "away_team"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PredictionTableResourceAdmin(ImportExportModelAdmin):
    resource_class = PredictionTableResource
    search_fields = [
        "fixture_id",
        "league_type",
        "match_status",
        "fixture_date",
        "league_id",
        "home_team",
        "away_team",
        "home_id",
        "away_id",
        "home_team_code",
        "away_team_code",
    ]
    date_hierarchy = "fixture_date"
    list_filter = ["api_type", "is_predicted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OddPredictionTypeResourceAdmin(ImportExportModelAdmin):
    resource_class = OddPredictionTypeResource
    search_fields = [
        "subscription_name",
    ]
    date_hierarchy = "date_created"
    list_filter = ["duration_of_subscription_type", "is_active", "is_deleted"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SoccerOddPredictionTableResourceAdmin(ImportExportModelAdmin):
    resource_class = SoccerOddPredictionTableResource
    search_fields = [
        "phone_number",
        "subscription_type__subscription_name",
    ]
    date_hierarchy = "date_created"
    list_filter = ["is_paid", "is_active", "duration_of_subscription_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class UserPredictionSubscriptionTableResourceAdmin(ImportExportModelAdmin):
    resource_class =  UserPredictionSubscriptionTableResource
    search_fields = [
      "phone_number"

    ]
    date_hierarchy = "date_created"
    list_filter = ["is_subscribed", "duration_of_subscription_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]
    
class SoccerPredictionLinkResourceAdmin(ImportExportModelAdmin):
    resource_class =  SoccerPredictionLinkResource
    search_fields = [
      "link_id"

    ]
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(FootballTable, FootballTableResourceAdmin)
admin.site.register(FootballHistory, FootballHistoryResourceAdmin)
admin.site.register(EnglishWeekHistory, EnglishWeekHistoryResourceAdmin)
admin.site.register(LaligaWeekHistory, LaligaWeekHistoryResourceAdmin)
admin.site.register(ChampionsLeagueWeekHistory, ChampionsLeagueWeekHistoryResourceAdmin)
admin.site.register(EuropaLeagueWeekHistory, EuropaLeagueWeekHistoryResourceAdmin)
admin.site.register(WorldCupWeekHistory, WorldCupWeekHistoryResourceAdmin)
admin.site.register(FootballTeamShortCode, FootballTeamShortCodeResourceAdmin)
admin.site.register(SoccerCashWinner, SoccerCashWinnerResourceAdmin)
admin.site.register(DecisioningResult, DecisioningResultResourceAdmin)
admin.site.register(GoalScorer, GoalScorerResourceAdmin)
admin.site.register(GoalScorerPrediction, GoalScorerPredictionResourceAdmin)
admin.site.register(PlayerGoal, PlayerGoalResourceAdmin)
admin.site.register(TeamFinalist, TeamFinalistResourceAdmin)
admin.site.register(TeamFinalistPrediction, TeamFinalistPredictionResourceAdmin)
admin.site.register(DualTeamFinalist, DualTeamFinalistResourceAdmin)
admin.site.register(WorldCupTeams, WorldCupTeamsResourceAdmin)
admin.site.register(DualTeamFinalistPrediction, DualTeamFinalistPredictionResourceAdmin)
admin.site.register(CutoutTable, CutoutTableResourceAdmin)
admin.site.register(ImportantTeams, ImportantTeamsResourceAdmin)
admin.site.register(RunningBalance, RunningBalanceResourceAdmin)
admin.site.register(AgentSoccerCashWinner, AgentSoccerCashWinnerResourceeAdmin)
admin.site.register(VirtualLeagues, VirtualLeaguesResourceAdmin)
admin.site.register(VirtualTeams, VirtualTeamsResourceAdmin)
admin.site.register(VirtualMatches, VirtualMatchesResourceeAdmin)
admin.site.register(PredictionTable, PredictionTableResourceAdmin)
admin.site.register(OddPredictionType, OddPredictionTypeResourceAdmin)
admin.site.register(SoccerOddPredictionTable, SoccerOddPredictionTableResourceAdmin)
admin.site.register(UserPredictionSubscriptionTable, UserPredictionSubscriptionTableResourceAdmin)
admin.site.register(SoccerPredictionLink, SoccerPredictionLinkResourceAdmin)
