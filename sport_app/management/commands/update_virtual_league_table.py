from django.core.management.base import BaseCommand

from sport_app.models import VirtualLeagues


def get_data():
    pass

    # querystring = {"country": "England", "type": "league"}

    data = [
        {
            "league": "La Liga",
            "country": "Spain",
            "id": 140,
            "logo": "https://media-2.api-sports.io/football/leagues/140.png",
        },
        {
            "league": "Serie A",
            "country": "Italy",
            "id": 135,
            "logo": "https://media-1.api-sports.io/football/leagues/135.png",
        },
        {
            "league": "Bundesliga",
            "country": "Germany",
            "id": 78,
            "logo": "https://media-1.api-sports.io/football/leagues/78.png",
        },
        {
            "league": "Ligue 1",
            "country": "France",
            "id": 61,
            "logo": "https://media-1.api-sports.io/football/leagues/61.png",
        },
        {
            "league": "Premier League",
            "country": "England",
            "id": 39,
            "logo": "https://media-1.api-sports.io/football/leagues/39.png",
        },
    ]

    return data

    # response = requests.request("GET", url, headers=headers, params=querystring)
    # res = response
    # pyperclip.copy(str(res.json()))


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        data = get_data()

        data_queryset = []

        for league in data:
            data_queryset.append(
                VirtualLeagues(
                    rapid_api_league_id=league["id"],
                    name=league["league"],
                    logo=league["logo"],
                    country=league["country"],
                )
            )

        VirtualLeagues.objects.bulk_create(data_queryset)
