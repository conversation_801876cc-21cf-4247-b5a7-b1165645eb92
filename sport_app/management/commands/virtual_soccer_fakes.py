from django.core.management.base import BaseCommand

from sport_app.models import VirtualMatches


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **options):
        # qs = VirtualTeams.objects.filter(league__id=5)
        # result = match_virtual_teams(qs)

        # print(result)
        # r = VirtualMatches.update_match_as_lost("48K544i-vs")
        # print(r)

        # lotto_winners = LottoWinners.objects.filter(lotto_type="VIRTUAL_SOCCER").last()
        # print(lotto_winners.game_play_id)
        # VirtualMatches.update_match_as_won(
        #     game_play_id=lotto_winners.game_play_id, lotto_winner_instance=lotto_winners
        # )

        teams = VirtualMatches.objects.filter(game_play_id="61604PP-vs")

        prediction_result = VirtualMatches().generate_predictions(queryset=teams, type_of_winning="Won", no_of_winning=3)

        print(prediction_result)

        # import random

        # # Get original scores for both teams
        # team1_score = int(input("Enter team 1 score: "))
        # team2_score = int(input("Enter team 2 score: "))

        # # Calculate average score for both teams
        # avg_score = (team1_score + team2_score) / 2

        # # Generate close scores for both teams
        # team1_close_score = int(avg_score + random.randint(-3, 3))
        # team2_close_score = int(avg_score + random.randint(-3, 3))

        # # Ensure close scores are between 0 and 5
        # team1_close_score = min(max(team1_close_score, 0), 5)
        # team2_close_score = min(max(team2_close_score, 0), 5)

        # # Ensure close scores are not equal to each other
        # while team1_close_score == team2_close_score:
        #     team1_close_score = int(avg_score + random.randint(-3, 3))
        #     team2_close_score = int(avg_score + random.randint(-3, 3))
        #     team1_close_score = min(max(team1_close_score, 0), 5)
        #     team2_close_score = min(max(team2_close_score, 0), 5)

        # # Output the close scores
        # print("Team 1 close score:", team1_close_score)
        # print("Team 2 close score:", team2_close_score)
