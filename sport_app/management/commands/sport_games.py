from django.core.management.base import BaseCommand

from sport_app.models import FootballTable
from sport_app.tasks import (
    champions_league_week_match,
    english_league_week_match,
    europa_league_week_match,
    laliga_league_week_match,
    today_fixtures,
    world_cup_week_match,
)
from wyse_ussd.models import SoccerPrediction

# from sport_app.models import FootballTable
# from django.db.models import Count


def update_existing_completed_predictions():
    matches = FootballTable.objects.filter(game_completed=True)
    for match in matches:
        away_choice = match.away_full_time_score
        home_choice = match.home_full_time_score

        predictions = SoccerPrediction.objects.filter(paid=True, game_fixture_id=match.fixture_id)
        predictions.update(is_drawn=True)
        predictions.filter(
            away_choice=away_choice,
            home_choice=home_choice,
        ).update(won=True)

    print("UPDATED SUCCESSFULLY")


class Command(BaseCommand):
    help = "Test run script maually"

    def handle(self, *args, **kwargs):
        w_cup_matches = world_cup_week_match()

        print("WORLD CUP MATCHES", w_cup_matches, "\n")

        lal_week_matches = laliga_league_week_match()

        print("LALIGA LEAGE WEEK MATCHES", lal_week_matches, "\n")

        e_league_week_matches = europa_league_week_match()

        print("EUROPA LEAGUE WEEK MATCHES", e_league_week_matches, "\n")

        eng_league_week_matches = english_league_week_match()

        print("ENGLISH LEAGUE WEEK MATCHES", eng_league_week_matches, "\n")

        chm_league_week_matches = champions_league_week_match()

        print("CHAMPIONS LEAGUE WEEK MATCHES", chm_league_week_matches, "\n")

        today_fx = today_fixtures()

        print("TODAY FIXTURES", today_fx, "\n")
