import random

from main.api.api_lottery_helpers import generate_game_play_id
from main.models import UserProfile
from sport_app.models import (
    DualTeamFinalist,
    DualTeamFinalistPrediction,
    GoalScorer,
    GoalScorerPrediction,
    TeamFinalist,
    TeamFinalistPrediction,
)


def fake_goal_soccer_prediction():
    users = [user for user in UserProfile.objects.all()]
    goal_scorers = [goal_scorer for goal_scorer in GoalScorer.objects.all()]
    band_choices = [200, 500, 1000, 2000]
    # choices = random.randint(1, 3)

    for _ in range(0, 1000):
        user = random.choice(users)
        # football_table = FootballTable.objects.get(id=54)
        goal_scorer = random.choice(goal_scorers)
        band_choice = random.choice(band_choices)
        game_id = generate_game_play_id()

        print("BAND CHOICE:::::::", band_choice)

        if band_choice == 200:
            potential_winning = 1500
        elif band_choice == 500:
            potential_winning = 5000
        elif band_choice == 1000:
            potential_winning = 12000
        elif band_choice == 2000:
            potential_winning = 20000

        GoalScorerPrediction.objects.create(
            user_profile=user,
            phone=user.phone_number,
            game_id=game_id,
            goal=random.randint(0, 5),
            band_played=band_choice,
            stake_amount=band_choice,
            potential_winning=potential_winning,
            paid=True,
            goal_scorer=goal_scorer,
        )
    return "FAKE PREDICTION DATA CREATED SUCCESSFULLY"


def fake_dual_team_prediction():
    users = [user for user in UserProfile.objects.all()]
    dual_team_finalists = [dual_team_finalist for dual_team_finalist in DualTeamFinalist.objects.all()]
    band_choices = [200, 500, 1000, 2000]
    # choices = random.randint(1, 3)

    for _ in range(0, 1000):
        user = random.choice(users)
        # football_table = FootballTable.objects.get(id=54)
        dual_team_finalist = random.choice(dual_team_finalists)
        band_choice = random.choice(band_choices)
        game_id = generate_game_play_id()

        print("BAND CHOICE:::::::", band_choice)

        if band_choice == 200:
            potential_winning = 1500
        elif band_choice == 500:
            potential_winning = 5000
        elif band_choice == 1000:
            potential_winning = 12000
        elif band_choice == 2000:
            potential_winning = 20000

        DualTeamFinalistPrediction.objects.create(
            user_profile=user,
            phone=user.phone_number,
            game_id=game_id,
            band_played=band_choice,
            stake_amount=band_choice,
            potential_winning=potential_winning,
            paid=True,
            dual_team_finalist=dual_team_finalist,
        )
    return "FAKE PREDICTION DATA CREATED SUCCESSFULLY"


def fake_single_team_prediction():
    users = [user for user in UserProfile.objects.all()]
    team_final_lists = [team_final_list for team_final_list in TeamFinalist.objects.all()]
    band_choices = [200, 500, 1000, 2000]
    # choices = random.randint(1, 3)

    for _ in range(0, 1000):
        user = random.choice(users)
        # football_table = FootballTable.objects.get(id=54)
        team_final_list = random.choice(team_final_lists)
        band_choice = random.choice(band_choices)
        game_id = generate_game_play_id()

        print("BAND CHOICE:::::::", band_choice)

        if band_choice == 200:
            potential_winning = 1500
        elif band_choice == 500:
            potential_winning = 5000
        elif band_choice == 1000:
            potential_winning = 12000
        elif band_choice == 2000:
            potential_winning = 20000

        TeamFinalistPrediction.objects.create(
            user_profile=user,
            phone=user.phone_number,
            game_id=game_id,
            band_played=band_choice,
            stake_amount=band_choice,
            potential_winning=potential_winning,
            paid=True,
            team_final_list=team_final_list,
        )
    return "FAKE PREDICTION DATA CREATED SUCCESSFULLY"
