from rest_framework import serializers

from main.api.api_lottery_helpers import generate_game_play_id
from main.models import LotteryModel, UserProfile
from main.tasks import create_wema_collection_account

from sport_app.models import SUBSCRIPTION_TYPE, GoalScorer, OddPredictionType, PredictionTable, SoccerOddPredictionTable, UserPredictionSubscriptionTable, VirtualLeagues, WorldCupTeams
from ussd_web_app.tasks import celery_get_woven_account_or_create_woven_account
from wallet_app.models import UserWallet
from wyse_ussd.models import UssdLotteryPayment
from wyse_ussd.tasks import ussd_lottery_payment


class SoccerCashGameSelectSerializer(serializers.Serializer):
    play_type = serializers.CharField(required=True, max_length=15)
    game_play = serializers.ListField(child=serializers.JSONField())

    def validate(self, data):
        match_selections = data.get("game_play")
        if len(match_selections) <= 0:
            raise serializers.ValidationError(
                {
                    "game_play": "Ensure this value has at least one object e.g"
                    ' {"fixtures_id": "946832","stake_amount": 200,"predictions": '
                    '[{"home_choice": 1,"away_choice": 2,"freemium": true},'
                    '{"home_choice": 1,"away_choice": 2,"freemium": true}]}.'
                }
            )

        if data["play_type"] == "PERSONAL":
            if len(match_selections) != 3:
                raise serializers.ValidationError({"play_type": "Total score prediction must be equal to 3 on PERSONAL play type."})

        for match in match_selections:
            try:
                # predictions
                if len(match["predictions"]) <= 0:
                    raise serializers.ValidationError(
                        {"predictions": "Ensure this value has at least one object e.g" " {'home_choice':1, 'away_choice':2, 'freemium':false}."}
                    )

                for prediction in match["predictions"]:
                    try:
                        # print(prediction["home_choice"])
                        # print(prediction["away_choice"])
                        # print(prediction["freemium"])

                        if type(prediction["home_choice"]) is not int:
                            raise serializers.ValidationError({"home_choice": "A valid number is required."})

                        if type(prediction["away_choice"]) is not int:
                            raise serializers.ValidationError({"away_choice": "A valid number is required."})
                        # print(prediction["freemium"])
                        if type(prediction["freemium"]) is not bool:
                            raise serializers.ValidationError({"freemium": "Must be a valid boolean."})

                        # stake amount checks
                        if type(prediction["stake_amount"]) is not int:  # validate stake amount as int
                            raise serializers.ValidationError({"stake_amount": "A valid number is required."})
                        if prediction["stake_amount"] < 200:  # validate value is not less than 200
                            raise serializers.ValidationError({"stake_amount": "Ensure this value is greater than or equal to 200."})

                    except KeyError:
                        raise serializers.ValidationError({"predictions": "Invalid key"})

            except KeyError as key:
                print(key)
                raise serializers.ValidationError({"game_play": f"Invalid key {key}"})

        return data


class GoalScorerSerializer(serializers.ModelSerializer):
    class Meta:
        model = GoalScorer
        # exclude = ("agent", "loan", "date_created", "agent_trnx")
        fields = "__all__"


class PredictGoalScorerSerializer(serializers.Serializer):
    # phone_no = serializers.CharField(max_length=13, min_length=11, required=True)
    game_play = serializers.ListField(child=serializers.JSONField())

    def validate(self, data):
        player_selection = data.get("game_play")
        if len(player_selection) <= 0:
            raise serializers.ValidationError({"game_play": "Ensure this value has at least one object e.g ."})

        for selection in player_selection:
            try:
                if len(selection["predictions"]) <= 0:
                    raise serializers.ValidationError(
                        {"predictions": "Ensure this value has at least one object e.g " "{'home_choice':1, 'away_choice':2, 'freemium':false}."}
                    )

                if type(selection["player_id"]) is not int:  # validate stake amount as int
                    raise serializers.ValidationError({"player_id": "A valid number is required."})

                for prediction in selection["predictions"]:
                    try:
                        if type(prediction["goal"]) is not int:
                            raise serializers.ValidationError({"home_choice": "A valid number is required."})

                        # stake amount checks
                        if type(prediction["stake_amount"]) is not int:  # validate stake amount as int
                            raise serializers.ValidationError({"stake_amount": "A valid number is required."})
                        if prediction["stake_amount"] < 200:  # validate value is not less than 200
                            raise serializers.ValidationError({"stake_amount": "Ensure this value is greater than or equal to 200."})

                    except KeyError:
                        raise serializers.ValidationError({"predictions": "Invalid key"})

            except KeyError as key:
                print(key)
                raise serializers.ValidationError({"game_play": f"Invalid key {key}"})

        return data


class PredictTeamFinalistSerializer(serializers.Serializer):
    game_play = serializers.ListField(child=serializers.JSONField())

    def validate(self, data):
        player_selection = data.get("game_play")
        if len(player_selection) <= 0:
            raise serializers.ValidationError({"game_play": "Ensure this value has at least one object e.g ."})

        for selection in player_selection:
            try:
                if len(selection["predictions"]) <= 0:
                    raise serializers.ValidationError(
                        {"predictions": "Ensure this value has at least one object e.g " "{'home_choice':1, 'away_choice':2, 'freemium':false}."}
                    )

                if type(selection["team_id"]) is not int:  # validate stake amount as int
                    raise serializers.ValidationError({"player_id": "A valid number is required."})

                for prediction in selection["predictions"]:
                    try:
                        # stake amount checks
                        if type(prediction["stake_amount"]) is not int:  # validate stake amount as int
                            raise serializers.ValidationError({"stake_amount": "A valid number is required."})
                        if prediction["stake_amount"] < 200:  # validate value is not less than 200
                            raise serializers.ValidationError({"stake_amount": "Ensure this value is greater than or equal to 200."})

                    except KeyError:
                        raise serializers.ValidationError({"predictions": "Invalid key"})

            except KeyError as key:
                print(key)
                raise serializers.ValidationError({"game_play": f"Invalid key {key}"})

        return data


class PredictDualTeamFinalistSerializer(serializers.Serializer):
    game_play = serializers.ListField(child=serializers.JSONField())

    def validate(self, data):
        player_selection = data.get("game_play")
        if len(player_selection) <= 0:
            raise serializers.ValidationError({"game_play": "Ensure this value has at least one object e.g ."})

        for selection in player_selection:
            try:
                if len(selection["predictions"]) <= 0:
                    raise serializers.ValidationError(
                        {"predictions": "Ensure this value has at least one object e.g " "{'home_choice':1, 'away_choice':2, 'freemium':false}."}
                    )

                if type(selection["team_a_id"]) is not int:  # validate stake amount as int
                    raise serializers.ValidationError({"team_a_id": "A valid number is required."})
                if type(selection["team_b_id"]) is not int:  # validate stake amount as int
                    raise serializers.ValidationError({"team_b_id": "A valid number is required."})

                for prediction in selection["predictions"]:
                    try:
                        # stake amount checks
                        if type(prediction["stake_amount"]) is not int:  # validate stake amount as int
                            raise serializers.ValidationError({"stake_amount": "A valid number is required."})
                        if prediction["stake_amount"] < 200:  # validate value is not less than 200
                            raise serializers.ValidationError({"stake_amount": "Ensure this value is greater than or equal to 200."})

                    except KeyError:
                        raise serializers.ValidationError({"predictions": "Invalid key"})

            except KeyError as key:
                print(key)
                raise serializers.ValidationError({"game_play": f"Invalid key {key}"})

        return data


class WorldCupTeamSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorldCupTeams
        exclude = ("team_id",)
        # fields = ("")


class VirtualLeagueSerializer(serializers.ModelSerializer):
    class Meta:
        model = VirtualLeagues
        fields = "__all__"


class VirtualPredictionSerializer(serializers.Serializer):
    home_team_code = serializers.CharField()
    home_team_id = serializers.IntegerField()
    home_team_prediction = serializers.IntegerField()
    away_team_code = serializers.CharField()
    away_team_id = serializers.IntegerField()
    away_team_prediction = serializers.IntegerField()


class VirtualPredictionMasterSerializer(serializers.Serializer):
    amount = serializers.IntegerField()
    pin = serializers.CharField(required=False, allow_null=True)
    predictions = serializers.ListField(child=VirtualPredictionSerializer())

    def validate(self, data):
        predictions = data.get("predictions")
        if len(predictions) <= 4:
            raise serializers.ValidationError({"predictions": "Ensure this value has at least 5 objects."})

        # if len(predictions) * 100 != data.get("amount"):
        #     raise serializers.ValidationError(
        #         {
        #             "amount": "Ensure this value is equal to the number of predictions * 100."
        #         }
        #     )

        for prediction in predictions:
            try:
                if type(prediction["home_team_prediction"]) is not int:  # validate stake amount as int
                    raise serializers.ValidationError({"home_team_prediction": "A valid number is required."})
                if type(prediction["away_team_prediction"]) is not int:  # validate stake amount as int
                    raise serializers.ValidationError({"away_team_prediction": "A valid number is required."})
            except KeyError as key:
                raise serializers.ValidationError({"predictions": f"Invalid key {key}"})

        return data


class VirtualSoccerResultSearchViewSerializer(serializers.Serializer):
    game_play_id = serializers.CharField()


class GetPredictOddSerializer(serializers.ModelSerializer):
    class Meta:
        model = OddPredictionType
        fields = ["id", "subscription_name", "subscription_amount", "subscription_duration", "duration_of_subscription_type"]


class CreatePredictionOddSerializer(serializers.Serializer):
    subscription_name = serializers.CharField()
    subscription_amount = serializers.FloatField()
    duration_of_subscription_type = serializers.ChoiceField(required=True, choices=SUBSCRIPTION_TYPE)
    is_active = serializers.BooleanField()

    def validate(self, attrs):
        subscription_name = attrs.get("subscription_name")
        prediction_type = OddPredictionType.objects.filter(subscription_name=subscription_name, is_deleted=False).first()
        if prediction_type:
            raise serializers.ValidationError({"subscription_name": f"{subscription_name} already exist"})
        OddPredictionType.objects.create(
            subscription_name=subscription_name,
            subscription_amount=attrs.get("subscription_amount"),
            duration_of_subscription_type=attrs.get("duration_of_subscription_type"),
            is_active=attrs.get("is_active"),
        )
        return attrs


class EditPredictionOddSerializer(serializers.ModelSerializer):
    class Meta:
        model = OddPredictionType
        fields = ["id", "subscription_name", "subscription_amount", "subscription_duration", "duration_of_subscription_type"]

    def validate(self, attrs):
        if "subscription_name" in attrs.keys():
            subscription_name = attrs.get("subscription_name")
            prediction_type = OddPredictionType.objects.filter(subscription_name=subscription_name, is_deleted=False).first()
            if prediction_type:
                raise serializers.ValidationError({"subscription_name": f"{subscription_name} already exist"})

        return attrs


class PredictOddsSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    subscription_id = serializers.CharField()
    consent = serializers.BooleanField()
    amount = serializers.FloatField()

    def validate(self, attrs):
        if not attrs.get("phone_number").isnumeric():
            raise serializers.ValidationError({"phone_number": "phone_number must be numeric"})
        if len(attrs.get("phone_number")) < 11 or len(attrs.get("phone_number")) > 11:
            raise serializers.ValidationError({"phone_number": "Ensure this field has 11 characters."})
        phone_number = LotteryModel.format_number_from_back_add_234(attrs.get("phone_number"))

        SoccerOddPredictionTable.objects.filter(phone_number=phone_number, is_active=True).update(is_active=False)
        attrs["phone_number"] = phone_number
        subscription_id = attrs.get("subscription_id")
        consent = attrs.get("consent")
        get_subscription = OddPredictionType.objects.filter(id=subscription_id, is_active=True).first()
        if not get_subscription:
            raise serializers.ValidationError({"subscription_id": "subscription_id is invalid"})
        if not consent:
            raise serializers.ValidationError({"consent": "consent is must be True"})
        if get_subscription.subscription_amount != attrs.get("amount"):
            raise serializers.ValidationError(
                {"amount": f"{get_subscription.subscription_name} amount must be {get_subscription.subscription_amount}"}
            )

        user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
        if user_profile is None:
            user_profile = UserProfile.objects.create(phone_number=phone_number)

        user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="USSD").last()
        if user_wallet is None:
            user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="USSD")

        # check if user have wema virtual account
        if user_wallet.wema_account is None:
            create_wema_collection_account(phone_number=phone_number)

        game_pay_id = generate_game_play_id()
        amount = attrs.get("amount")
        get_user_data = UserPredictionSubscriptionTable.objects.filter(
            user_profile=user_profile,
            phone_number=phone_number
        ).first()
        if not get_user_data:
            UserPredictionSubscriptionTable.objects.create(
                user_profile=user_profile,
                phone_number=phone_number
            )
       
        prediction_subscription = SoccerOddPredictionTable.objects.create(
                user_profile=user_profile,
                phone_number=phone_number,
                subscription_type=get_subscription,
                amount=amount,
                consent=consent,
                game_pay_id=game_pay_id
            )

        
        UssdLotteryPayment.objects.create(
            user=user_profile,
            amount=amount,
            game_play_id=game_pay_id,
            channel="USSD",
            lottery_type="PREDICTION_SUBSCRIPTION",
        )

        ussd_play_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
        if ussd_play_wallet is None:
            ussd_play_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")
        if ussd_play_wallet.game_available_balance >= amount:
            ussd_lottery_payment.apply_async(
                queue="celery1",
                args=[
                    user_profile.phone_number,
                    int(amount),
                    user_profile.bank_code,
                    get_subscription.subscription_name,
                    "PREDICTION_SUBSCRIPTION",
                    game_pay_id,
                ],
            )
            prediction_subscription.is_paid = True
            prediction_subscription.save()
            
            attrs["status"] = "ACCEPTED"
            attrs["message"] = "Paid"
            attrs["game_id"] = game_pay_id
            attrs["paid"] = True
            attrs["amount"] = amount
            attrs["subscription_type"] = "PREDICTION_SUBSCRIPTION"

        else:
            if user_wallet.woven_account is None:
                celery_get_woven_account_or_create_woven_account(phone=user_profile.phone_number)
                payment_details = {}
            else:
                payment_details = {
                    "account_name": user_wallet.wema_account.acct_name if user_wallet.wema_account else None,
                    "account_number": user_wallet.wema_account.vnuban if user_wallet.wema_account else None,
                    "bank_name": user_wallet.wema_account.bank_name if user_wallet.wema_account else None,
                }

            attrs["status"] = "ACCEPTED"
            attrs["message"] = "Payment processing"
            attrs["game_id"] = game_pay_id
            attrs["paid"] = False
            attrs["amount"] = amount + 50
            attrs["subscription_type"] = "PREDICTION_SUBSCRIPTION"
            attrs["payment_details"] = payment_details

        return attrs


class PredictionSubscriptionPaymentSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=100)
    amount = serializers.IntegerField()

class WebSoccerPredictionSerializer(serializers.ModelSerializer):
    class Meta:
        model = PredictionTable
        fields = "__all__"