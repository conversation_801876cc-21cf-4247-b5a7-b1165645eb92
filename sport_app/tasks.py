from datetime import datetime, timedelta

import requests
from celery import shared_task
from django.conf import settings
from django.db.models import Q
from django.http import Http404, JsonResponse
from django.shortcuts import render
from rest_framework import status
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.views import APIView

from liberty_lotto.celery import celery
import random

from sport_app.helpers.helper_functions import generate_link_id


# Create your views here.


@shared_task
def today_fixtures():
    from sport_app.models import FootballTable

    today_date = datetime.now().date()
    all_data = []
    url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"
    headers = {
        "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
        "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
    }
    querystring = querystring = {"date": f"{today_date}"}
    print(today_date)

    response = requests.request("GET", url, headers=headers, params=querystring)
    resp = response.json()
    data = resp.get("response")
    for fixture in data:
        fixture_id = fixture["fixture"]["id"]
        shorts = fixture["fixture"]["status"]["short"]
        get_fixtures = FootballTable.objects.filter(fixture_id=fixture_id, is_drawn=False).last()
        if get_fixtures:
            if shorts == "FT" or shorts == "PEN":
                home_score = fixture["score"]["fulltime"]["home"]
                away_score = fixture["score"]["fulltime"]["away"]
                home_team = fixture["teams"]["home"]["name"]
                away_team = fixture["teams"]["away"]["name"]
                get_fixtures.home_full_time_score = home_score
                get_fixtures.away_full_time_score = away_score
                get_fixtures.match_status = shorts
                get_fixtures.game_completed = True
                get_fixtures.full_time_score = f"{home_score}-{away_score}"
                get_fixtures.full_time_score_text = f"FULLTIME RESULT-> {home_team}:{home_score} - {away_score}:{away_team}"
                get_fixtures.save()
                all_data.append(
                    {
                        "fixture_id": fixture_id,
                        "teams": f"{home_team} vs {away_team}",
                        "scores": f"{home_score} vs {away_score}",
                        "results": f"{home_team}:{home_score} - {away_score}:{away_team}",
                        "status": shorts,
                    }
                )
            else:
                home_score = fixture["score"]["fulltime"]["home"]
                away_score = fixture["score"]["fulltime"]["away"]
                home_team = fixture["teams"]["home"]["name"]
                away_team = fixture["teams"]["away"]["name"]
                get_fixtures.match_status = shorts
                get_fixtures.save()
                all_data.append(
                    {
                        "fixture_id": fixture_id,
                        "teams": f"{home_team} vs {away_team}",
                        "scores": f"{home_score} vs {away_score}",
                        "results": f"{home_team}:{home_score} - {away_score}:{away_team}",
                        "status": shorts,
                    }
                )

        else:
            pass

    print(all_data)
    return all_data


@shared_task
def champions_league_week_match():
    from sport_app.helpers.team_code import TeamShortCode
    from sport_app.models import (
        ChampionsLeagueWeekHistory,
        FootballTable,
        FootballTeamShortCode,
        ImportantTeams,
    )

    # url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"
    headers = {
        "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
        "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
    }
    id = "2"

    def get_week(n):
        today = datetime.now()
        week_start_date = today - timedelta(days=today.weekday())
        # week_end_date = week_start_date + timedelta(days=6)

        a_week = ChampionsLeagueWeekHistory.objects.filter(week_start_date=week_start_date).last()
        if a_week is None:
            # week = ChampionsLeagueWeekHistory.objects.create(week_start_date=week_start_date, week_end_date=week_end_date)
            return get_week(n)
        else:
            start_date = a_week.week_start_date
            end_date = a_week.week_end_date
            season = f"{settings.CHAMPIONS_LEAGUE_SEASON_YEAR}"

            url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"

            querystring = {
                "league": f"{id}",
                "season": f"{season}",
                "from": f"{start_date}",
                "to": f"{end_date}",
                "timezone": "Africa/Lagos",
                "status": "NS",
            }

            response = requests.request("GET", url, headers=headers, params=querystring)
            print(start_date)
            print(end_date)
            print("\nWEEK MATCH:::::", response.text, ":::::")
            resp = response.json()
            data = resp.get("response")

            print("ALL WEEK FIXTURES :::::::", data)
            if n == 0:
                return None
            if data is None:
                return None
                # return {"message": "No match Currently Available"}
            if len(data) < 1:
                print("No match this week")
                date_1 = start_date + timedelta(days=7)
                date_2 = end_date + timedelta(days=7)
                ChampionsLeagueWeekHistory.objects.create(week_start_date=date_1, week_end_date=date_2)
                print("CURRENT VALUE OF COUNT", n)
                get_week(n - 1)
            if len(data) >= 1:
                return data

    get_week_match = get_week(4)
    if get_week_match is None:
        return {"message": "No match Currently Available"}
    else:
        content = ImportantTeams.objects.all()
        content_list = [x.team_id for x in content]
        print("The list is: ", content_list)
        first_data = []
        second_data = []

        print("match available this week")
        # match fixtures
        fixture_list = []
        fixtures_data = []
        app = get_week_match
        for i in app:
            home_id = i["teams"]["home"]["id"]
            away_id = i["teams"]["away"]["id"]
            home_team = i["teams"]["home"]["name"]
            away_team = i["teams"]["away"]["name"]
            home_fix = {
                "fixture_id": i["fixture"]["id"],
                "home_team": home_team,
                "away_team": away_team,
                "home_id": home_id,
                "away_id": away_id,
                "home_logo": i["teams"]["home"]["logo"],
                "away_logo": i["teams"]["away"]["logo"],
                "fixture_date": i["fixture"]["date"],
                "league_type": "UEFA Champions League",
                "league_id": f"{id}",
            }
            if str(home_id) in content_list or str(away_id) in content_list:
                if home_fix not in first_data:
                    first_data.append(home_fix)
                else:
                    pass
            else:
                if home_fix not in second_data:
                    second_data.append(home_fix)
                else:
                    pass
        print(first_data, "\n\n")
        print(second_data, "\n\n")
        fixture_list = [*first_data, *second_data]
        for x in fixture_list:
            if x not in fixtures_data:
                fixtures_data.append(x)
                if FootballTable.objects.filter(fixture_id=x["fixture_id"]).exists():
                    print(x["fixture_id"], " already exists :::::")
                else:
                    home_id = x["home_id"]
                    away_id = x["away_id"]
                    home_team = x["home_team"]
                    away_team = x["away_team"]
                    home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()

                    if home_team_code is None:
                        TeamShortCode.short_code(id=home_id)

                    home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()

                    if home_team_code is None:
                        continue

                    away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()
                    if away_team_code is None:
                        TeamShortCode.short_code(id=away_id)

                    away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()

                    if away_team_code is None:
                        continue

                    FootballTable.objects.create(
                        fixture_id=x["fixture_id"],
                        home_team=home_team,
                        away_team=away_team,
                        home_id=home_id,
                        away_id=away_id,
                        home_team_code=home_team_code.team_code,
                        away_team_code=away_team_code.team_code,
                        home_logo=x["home_logo"],
                        away_logo=x["away_logo"],
                        fixture_date=x["fixture_date"],
                        league_type=x["league_type"],
                        league_id=x["league_id"],
                    )
            else:
                pass
        print(":::::::::::::::")
        print(":::::::::::::::")
        print(":::::::::::::::")
        print(":::::::::::::::")
        print("FILTERED WEEK FIXTURE ::::::", fixtures_data)
        return fixtures_data

        # end of match fixtures


@shared_task
def english_league_week_match():
    from sport_app.helpers.team_code import TeamShortCode
    from sport_app.models import (
        EnglishWeekHistory,
        FootballTable,
        FootballTeamShortCode,
        ImportantTeams,
    )

    # url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"
    # headers = {
    #     "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
    #     "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
    # }
    id = "39"

    def get_week(n):
        today = datetime.now()
        week_start_date = today - timedelta(days=today.weekday())
        # week_end_date = week_start_date + timedelta(days=6)

        url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"
        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }
        id = "39"

        a_week = EnglishWeekHistory.objects.filter(week_start_date=week_start_date).last()
        if a_week is None:
            # week = EnglishWeekHistory.objects.create(week_start_date=week_start_date, week_end_date=week_end_date)
            return get_week(n)
        else:
            start_date = a_week.week_start_date
            end_date = a_week.week_end_date
            season = f"{settings.ENGLISH_LEAGUE_SEASON_YEAR}"

            url = f"{url}/fixtures"

            querystring = {
                "league": f"{id}",
                "season": f"{season}",
                "from": f"{start_date}",
                "to": f"{end_date}",
                "timezone": "Africa/Lagos",
                "status": "NS",
            }

            response = requests.request("GET", url, headers=headers, params=querystring)

            print(start_date)
            print(end_date)
            print("\nWEEK MATCH:::::", response.text, ":::::")
            resp = response.json()
            data = resp.get("response")
            print("ALL WEEK FIXTURES :::::::", data)

            if n == 0:
                return None
            if data is None:
                return None
                # return {"message": "No match Currently Available"}
            if len(data) < 1:
                print("No match this week")
                date_1 = start_date + datetime.timedelta(days=7)
                date_2 = end_date + datetime.timedelta(days=7)
                EnglishWeekHistory.objects.create(week_start_date=date_1, week_end_date=date_2)
                print("\n\n\n\nCURRENT VALUE OF COUNT", n)
                get_week(n - 1)
            if len(data) >= 1:
                return data

    get_week_match = get_week(4)
    print("GET WEEK MATCH ::::::", get_week_match)
    if get_week_match is None:
        return {"message": "No match Currently Available"}
    else:
        content = ImportantTeams.objects.all()
        content_list = [x.team_id for x in content]
        print("The list is: ", content_list)
        first_data = []
        second_data = []

        print("match available this week")
        # match fixtures
        fixture_list = []
        fixtures_data = []
        app = get_week_match
        for i in app:
            home_id = i["teams"]["home"]["id"]
            away_id = i["teams"]["away"]["id"]
            home_team = i["teams"]["home"]["name"]
            away_team = i["teams"]["away"]["name"]
            home_fix = {
                "fixture_id": i["fixture"]["id"],
                "home_team": home_team,
                "away_team": away_team,
                "home_id": home_id,
                "away_id": away_id,
                "home_logo": i["teams"]["home"]["logo"],
                "away_logo": i["teams"]["away"]["logo"],
                "fixture_date": i["fixture"]["date"],
                "league_type": "Premier League",
                "league_id": f"{id}",
            }
            if str(home_id) in content_list or str(away_id) in content_list:
                if home_fix not in first_data:
                    first_data.append(home_fix)
                else:
                    pass
            else:
                if home_fix not in second_data:
                    second_data.append(home_fix)
                else:
                    pass
        print(first_data, "\n\n")
        print(second_data, "\n\n")
        fixture_list = [*first_data, *second_data]
        for x in fixture_list:
            if x not in fixtures_data:
                fixtures_data.append(x)
                if FootballTable.objects.filter(fixture_id=x["fixture_id"]).exists():
                    print(x["fixture_id"], " already exists :::::")
                else:
                    home_id = x["home_id"]
                    away_id = x["away_id"]
                    home_team = x["home_team"]
                    away_team = x["away_team"]
                    home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()

                    if home_team_code is None:
                        TeamShortCode.short_code(id=home_id)

                    home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()

                    if home_team_code is None:
                        continue

                    away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()
                    if away_team_code is None:
                        TeamShortCode.short_code(id=away_id)

                    away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()

                    if away_team_code is None:
                        continue

                    FootballTable.objects.create(
                        fixture_id=x["fixture_id"],
                        home_team=home_team,
                        away_team=away_team,
                        home_id=home_id,
                        away_id=away_id,
                        home_team_code=home_team_code.team_code,
                        away_team_code=away_team_code.team_code,
                        home_logo=x["home_logo"],
                        away_logo=x["away_logo"],
                        fixture_date=x["fixture_date"],
                        league_type=x["league_type"],
                        league_id=x["league_id"],
                    )
            else:
                pass
        print(":::::::::::::::")
        print(":::::::::::::::")
        print(":::::::::::::::")
        print(":::::::::::::::")
        print("FILTERED WEEK FIXTURE ::::::", fixtures_data)
        return fixtures_data

        # end of match fixtures


@shared_task
def europa_league_week_match():
    from sport_app.helpers.team_code import TeamShortCode
    from sport_app.models import (
        EuropaLeagueWeekHistory,
        FootballTable,
        FootballTeamShortCode,
        ImportantTeams,
    )

    # url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"
    headers = {
        "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
        "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
    }
    id = "3"

    def get_week(n):
        url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"

        today = datetime.now()
        week_start_date = today - timedelta(days=today.weekday())
        week_end_date = week_start_date + timedelta(days=6)

        a_week = EuropaLeagueWeekHistory.objects.filter(week_start_date=week_start_date).last()
        if a_week is None:
            EuropaLeagueWeekHistory.objects.create(week_start_date=week_start_date, week_end_date=week_end_date)
            return get_week(n)
        else:
            start_date = a_week.week_start_date
            end_date = a_week.week_end_date
            season = f"{settings.EUROPA_LEAGUE_SEASON_YEAR}"

            url = f"{url}/fixtures"
            querystring = {
                "league": f"{id}",
                "season": f"{season}",
                "from": f"{start_date}",
                "to": f"{end_date}",
                "timezone": "Africa/Lagos",
                "status": "NS",
            }

            response = requests.request("GET", url, headers=headers, params=querystring)

            print(start_date)
            print(end_date)
            print("\nWEEK MATCH:::::", response.text, ":::::")
            resp = response.json()
            data = resp.get("response")

            print("ALL WEEK FIXTURES :::::::", data)
            if n == 0:
                return None
            if data is None:
                return None
                # return {"message": "No match Currently Available"}
            if len(data) < 1:
                print("No match this week")
                date_1 = start_date + datetime.timedelta(days=7)
                date_2 = end_date + datetime.timedelta(days=7)
                EuropaLeagueWeekHistory.objects.create(week_start_date=date_1, week_end_date=date_2)
                print("CURRENT VALUE OF COUNT", n)
                get_week(n - 1)
            if len(data) >= 1:
                return data

    get_week_match = get_week(4)
    if get_week_match is None:
        return {"message": "No match Currently Available"}
    else:
        content = ImportantTeams.objects.all()
        content_list = [x.team_id for x in content]
        print("The list is: ", content_list)
        first_data = []
        second_data = []

        print("match available this week")
        # match fixtures
        fixture_list = []
        fixtures_data = []
        app = get_week_match
        for i in app:
            home_id = i["teams"]["home"]["id"]
            away_id = i["teams"]["away"]["id"]
            home_team = i["teams"]["home"]["name"]
            away_team = i["teams"]["away"]["name"]
            home_fix = {
                "fixture_id": i["fixture"]["id"],
                "home_team": home_team,
                "away_team": away_team,
                "home_id": home_id,
                "away_id": away_id,
                "home_logo": i["teams"]["home"]["logo"],
                "away_logo": i["teams"]["away"]["logo"],
                "fixture_date": i["fixture"]["date"],
                "league_type": "UEFA Europa League",
                "league_id": f"{id}",
            }
            if str(home_id) in content_list or str(away_id) in content_list:
                if home_fix not in first_data:
                    first_data.append(home_fix)
                else:
                    pass
            else:
                if home_fix not in second_data:
                    second_data.append(home_fix)
                else:
                    pass
        print(first_data, "\n\n")
        print(second_data, "\n\n")
        fixture_list = [*first_data, *second_data]
        for x in fixture_list:
            if x not in fixtures_data:
                fixtures_data.append(x)
                if FootballTable.objects.filter(fixture_id=x["fixture_id"]).exists():
                    print(x["fixture_id"], " already exists :::::")
                else:
                    home_id = x["home_id"]
                    away_id = x["away_id"]
                    home_team = x["home_team"]
                    away_team = x["away_team"]
                    home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()

                    if home_team_code is None:
                        TeamShortCode.short_code(id=home_id)

                    home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()

                    if home_team_code is None:
                        continue

                    away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()
                    if away_team_code is None:
                        TeamShortCode.short_code(id=away_id)

                    away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()

                    if away_team_code is None:
                        continue

                    FootballTable.objects.create(
                        fixture_id=x["fixture_id"],
                        home_team=home_team,
                        away_team=away_team,
                        home_id=home_id,
                        away_id=away_id,
                        home_team_code=home_team_code.team_code,
                        away_team_code=away_team_code.team_code,
                        home_logo=x["home_logo"],
                        away_logo=x["away_logo"],
                        fixture_date=x["fixture_date"],
                        league_type=x["league_type"],
                        league_id=x["league_id"],
                    )
            else:
                pass
        print(":::::::::::::::")
        print(":::::::::::::::")
        print(":::::::::::::::")
        print(":::::::::::::::")
        print("FILTERED WEEK FIXTURE ::::::", fixtures_data)
        return fixtures_data

        # end of match fixtures


@shared_task
def laliga_league_week_match():
    from sport_app.helpers.team_code import TeamShortCode
    from sport_app.models import (
        FootballTable,
        FootballTeamShortCode,
        ImportantTeams,
        LaligaWeekHistory,
    )

    def get_week(n):
        url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"
        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }
        id = "140"

        today = datetime.now()
        week_start_date = today - timedelta(days=today.weekday())
        # week_end_date = week_start_date + timedelta(days=6)

        a_week = LaligaWeekHistory.objects.filter(week_start_date=week_start_date).last()
        if a_week is None:
            # week = LaligaWeekHistory.objects.create(week_start_date=week_start_date, week_end_date=week_end_date)
            return get_week(n)
        else:
            start_date = a_week.week_start_date
            end_date = a_week.week_end_date
            season = f"{settings.LA_LIGA_SEASON_YEAR}"

            url = f"{url}/fixtures"

            querystring = {
                "league": f"{id}",
                "season": f"{season}",
                "from": f"{start_date}",
                "to": f"{end_date}",
                "timezone": "Africa/Lagos",
                "status": "NS",
            }

            response = requests.request("GET", url, headers=headers, params=querystring)

            print(start_date)
            print(end_date)
            print("\nWEEK MATCH:::::", response.text, ":::::")
            resp = response.json()
            data = resp.get("response")
            print("ALL WEEK FIXTURES :::::::", data)

            if n == 0:
                return None
            if data is None:
                return None
                # return {"message": "No match Currently Available"}
            if len(data) < 1:
                print("No match this week")
                date_1 = start_date + datetime.timedelta(days=7)
                date_2 = end_date + datetime.timedelta(days=7)
                LaligaWeekHistory.objects.create(week_start_date=date_1, week_end_date=date_2)
                print("CURRENT VALUE OF COUNT", n)
                get_week(n - 1)
            if len(data) >= 1:
                return data

    get_week_match = get_week(4)
    if get_week_match is None:
        return {"message": "No match Currently Available"}
    else:
        content = ImportantTeams.objects.all()
        content_list = [x.team_id for x in content]
        print("The list is: ", content_list)
        first_data = []
        second_data = []

        print("match available this week")
        # match fixtures
        fixture_list = []
        fixtures_data = []
        app = get_week_match
        for i in app:
            home_id = i["teams"]["home"]["id"]
            away_id = i["teams"]["away"]["id"]
            home_team = i["teams"]["home"]["name"]
            away_team = i["teams"]["away"]["name"]
            home_fix = {
                "fixture_id": i["fixture"]["id"],
                "home_team": home_team,
                "away_team": away_team,
                "home_id": home_id,
                "away_id": away_id,
                "home_logo": i["teams"]["home"]["logo"],
                "away_logo": i["teams"]["away"]["logo"],
                "fixture_date": i["fixture"]["date"],
                "league_type": "La Liga",
                "league_id": f"{id}",
            }
            if str(home_id) in content_list or str(away_id) in content_list:
                if home_fix not in first_data:
                    first_data.append(home_fix)
                else:
                    pass
            else:
                if home_fix not in second_data:
                    second_data.append(home_fix)
                else:
                    pass
        print(first_data, "\n\n")
        print(second_data, "\n\n")
        fixture_list = [*first_data, *second_data]
        for x in fixture_list:
            if x not in fixtures_data:
                fixtures_data.append(x)
                if FootballTable.objects.filter(fixture_id=x["fixture_id"]).exists():
                    print(x["fixture_id"], " already exists :::::")
                else:
                    home_id = x["home_id"]
                    away_id = x["away_id"]
                    home_team = x["home_team"]
                    away_team = x["away_team"]
                    home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()

                    if home_team_code is None:
                        TeamShortCode.short_code(id=home_id)

                    home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()

                    if home_team_code is None:
                        continue

                    away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()
                    if away_team_code is None:
                        TeamShortCode.short_code(id=away_id)

                    away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()

                    if away_team_code is None:
                        continue

                    FootballTable.objects.create(
                        fixture_id=x["fixture_id"],
                        home_team=home_team,
                        away_team=away_team,
                        home_id=home_id,
                        away_id=away_id,
                        home_team_code=home_team_code.team_code,
                        away_team_code=away_team_code.team_code,
                        home_logo=x["home_logo"],
                        away_logo=x["away_logo"],
                        fixture_date=x["fixture_date"],
                        league_type=x["league_type"],
                        league_id=x["league_id"],
                    )
            else:
                pass
        print(":::::::::::::::")
        print(":::::::::::::::")
        print(":::::::::::::::")
        print(":::::::::::::::")
        print("FILTERED WEEK FIXTURE ::::::", fixtures_data)
        return fixtures_data

        # end of match fixtures


@shared_task
def world_cup_week_match():
    from sport_app.helpers.team_code import TeamShortCode
    from sport_app.models import (
        FootballTable,
        FootballTeamShortCode,
        ImportantTeams,
        WorldCupWeekHistory,
    )

    # url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"
    # headers = {
    #     "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
    #     "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
    # }
    id = "1"

    def get_week(n):
        url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"
        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }
        id = "140"

        today = datetime.now()
        week_start_date = today - timedelta(days=today.weekday())
        week_end_date = week_start_date + timedelta(days=6)

        a_week = WorldCupWeekHistory.objects.filter(week_start_date=week_start_date).last()
        url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"
        if a_week is None:
            WorldCupWeekHistory.objects.create(week_start_date=week_start_date, week_end_date=week_end_date)
            return get_week(n)
        else:
            start_date = a_week.week_start_date
            end_date = a_week.week_end_date
            season = f"{settings.WORLD_CUP_SEASON_YEAR}"

            url = f"{url}/fixtures"

            querystring = {
                "league": f"{id}",
                "season": f"{season}",
                "from": f"{start_date}",
                "to": f"{end_date}",
                "timezone": "Africa/Lagos",
                "status": "NS",
            }

            response = requests.request("GET", url, headers=headers, params=querystring)

            print(start_date)
            print(end_date)
            print("\nWEEK MATCH:::::", response.text, ":::::")
            resp = response.json()
            data = resp.get("response")
            print("ALL WEEK FIXTURES :::::::", data)

            if n == 0:
                return None
            if data is None:
                return None
                # return {"message": "No match Currently Available"}
            if len(data) < 1:
                print("No match this week")
                date_1 = start_date + datetime.timedelta(days=7)
                date_2 = end_date + datetime.timedelta(days=7)
                WorldCupWeekHistory.objects.create(week_start_date=date_1, week_end_date=date_2)
                print("CURRENT VALUE OF COUNT", n)
                get_week(n - 1)
            if len(data) >= 1:
                return data

    get_week_match = get_week(4)
    if get_week_match is None:
        return {"message": "No match Currently Available"}
    else:
        content = ImportantTeams.objects.all()
        content_list = [x.team_id for x in content]
        print("The list is: ", content_list)
        first_data = []
        second_data = []

        print("match available this week")
        # match fixtures
        fixture_list = []
        fixtures_data = []
        app = get_week_match
        for i in app:
            home_id = i["teams"]["home"]["id"]
            away_id = i["teams"]["away"]["id"]
            home_team = i["teams"]["home"]["name"]
            away_team = i["teams"]["away"]["name"]
            home_fix = {
                "fixture_id": i["fixture"]["id"],
                "home_team": home_team,
                "away_team": away_team,
                "home_id": home_id,
                "away_id": away_id,
                "home_logo": i["teams"]["home"]["logo"],
                "away_logo": i["teams"]["away"]["logo"],
                "fixture_date": i["fixture"]["date"],
                "league_type": "World Cup",
                "league_id": f"{id}",
            }
            if str(home_id) in content_list or str(away_id) in content_list:
                if home_fix not in first_data:
                    first_data.append(home_fix)
                else:
                    pass
            else:
                if home_fix not in second_data:
                    second_data.append(home_fix)
                else:
                    pass
        print(first_data, "\n\n")
        print(second_data, "\n\n")
        fixture_list = [*first_data, *second_data]
        for x in fixture_list:
            if x not in fixtures_data:
                fixtures_data.append(x)
                if FootballTable.objects.filter(fixture_id=x["fixture_id"]).exists():
                    print(x["fixture_id"], " already exists :::::")
                else:
                    home_id = x["home_id"]
                    away_id = x["away_id"]
                    home_team = x["home_team"]
                    away_team = x["away_team"]
                    home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()

                    if home_team_code is None:
                        TeamShortCode.short_code(id=home_id)

                    home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()

                    if home_team_code is None:
                        continue

                    away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()
                    if away_team_code is None:
                        TeamShortCode.short_code(id=away_id)

                    away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()

                    if away_team_code is None:
                        continue

                    FootballTable.objects.create(
                        fixture_id=x["fixture_id"],
                        home_team=home_team,
                        away_team=away_team,
                        home_id=home_id,
                        away_id=away_id,
                        home_team_code=home_team_code.team_code,
                        away_team_code=away_team_code.team_code,
                        home_logo=x["home_logo"],
                        away_logo=x["away_logo"],
                        fixture_date=x["fixture_date"],
                        league_type=x["league_type"],
                        league_id=x["league_id"],
                    )
            else:
                pass
        print(":::::::::::::::")
        print(":::::::::::::::")
        print(":::::::::::::::")
        print(":::::::::::::::")
        print("FILTERED WEEK FIXTURE ::::::", fixtures_data)
        return fixtures_data

        # end of match fixtures


# WINWISE_GAMEDRAWN_SOCCER_WON
@shared_task
def send_sms_win_wise_game_draw_soccer_won(phone_number, won_amount, prediction, match, draw_date):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": "33d18a28-b6c4-4727-81e9-330deabdb607",
        "place_holders": {
            "Won Amount": f"{won_amount}",
            "prediction": f"{prediction}",
            "Match": f"{match}",
            "Draw_date": f"{draw_date}",
        },
    }
    print(whisper_payload)

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


# WINWISE_GAMEDRAWN_SOCCER_LOST
@shared_task
def send_sms_win_wise_game_draw_soccer_lost(phone_number, total_amount_won, no_of_Winners, match, draw_date):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": "967f4be6-a6bd-4891-bf23-f2a1785fc5db",
        "place_holders": {
            "Total Amount Won": f"{total_amount_won}",
            "no_of_Winners": f"{no_of_Winners}",
            "Match": f"{match}",
            "Draw_date": f"{draw_date}",
        },
    }
    print(whisper_payload)

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


@shared_task
def missed_today_fixtures():
    from sport_app.models import FootballTable

    today_date = datetime.now().date()
    missed_fixtures = FootballTable.objects.filter(Q(fixture_date__date__gte=today_date, match_status="NS"))
    for i in missed_fixtures:
        fix_id = i.fixture_id
        i.fixture_date
        i.league_type
        i.league_id
        print("FIXTURE ID", fix_id)

        all_data = []
        url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"
        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }
        querystring = querystring = {"id": f"{fix_id}"}

        response = requests.request("GET", url, headers=headers, params=querystring)
        resp = response.json()
        data = resp.get("response")[0]

        fixture_id = data["fixture"]["id"]
        shorts = data["fixture"]["status"]["short"]
        get_fixtures = FootballTable.objects.filter(fixture_id=fixture_id, is_drawn=False).last()
        if get_fixtures:
            if shorts == "FT" or shorts == "PEN":
                home_score = data["score"]["fulltime"]["home"]
                away_score = data["score"]["fulltime"]["away"]
                home_team = data["teams"]["home"]["name"]
                away_team = data["teams"]["away"]["name"]
                get_fixtures.home_full_time_score = home_score
                get_fixtures.away_full_time_score = away_score
                get_fixtures.match_status = shorts
                get_fixtures.game_completed = True
                get_fixtures.full_time_score = f"{home_score}-{away_score}"
                get_fixtures.full_time_score_text = f"FULLTIME RESULT-> {home_team}:{home_score} - {away_score}:{away_team}"
                get_fixtures.save()
                all_data.append(
                    {
                        "fixture_id": fixture_id,
                        "teams": f"{home_team} vs {away_team}",
                        "scores": f"{home_score} vs {away_score}",
                        "results": f"{home_team}:{home_score} - {away_score}:{away_team}",
                        "status": shorts,
                    }
                )
            else:
                home_score = data["score"]["fulltime"]["home"]
                away_score = data["score"]["fulltime"]["away"]
                home_team = data["teams"]["home"]["name"]
                away_team = data["teams"]["away"]["name"]
                get_fixtures.match_status = shorts
                get_fixtures.save()
                all_data.append(
                    {
                        "fixture_id": fixture_id,
                        "teams": f"{home_team} vs {away_team}",
                        "scores": f"{home_score} vs {away_score}",
                        "results": f"{home_team}:{home_score} - {away_score}:{away_team}",
                        "status": shorts,
                    }
                )
        else:
            pass
    # print(all_data)
    return all_data


@shared_task
def rapid_api_prediction():
    from sport_app.models import  PredictionTable
    from sport_app.models import SoccerPredictionLink

    first_date = datetime.now().date()
    second_date = first_date + timedelta(days=1)
    third_date = first_date + timedelta(days=2)

    all_matches = PredictionTable.objects.filter(api_type="RAPID_API", is_predicted=False)

    ### get random 10 matches for yesterday
    
    yesterday_all_predictions = all_matches.filter(fixture_date__date=first_date)
    yesterday_predicted_random = yesterday_all_predictions.values_list('id', flat=True)
    yesterday_random_predicted_id = random.sample(list(yesterday_predicted_random), min(len(yesterday_predicted_random), 10))
    yesterday_query_set = yesterday_all_predictions.filter(id__in=yesterday_random_predicted_id)

    ### get random 10 matches for today
    today_all_predictions = all_matches.filter(fixture_date__date=second_date)
    today_predicted_random = today_all_predictions.values_list('id', flat=True)
    today_random_predicted_id = random.sample(list(today_predicted_random), min(len(today_predicted_random), 10))
    today_query_set = today_all_predictions.filter(id__in=today_random_predicted_id)

    ### get random 10 matches for tomorrow
    tomorrow_all_predictions = all_matches.filter(fixture_date__date=third_date)
    tomorrow_predicted_random = tomorrow_all_predictions.values_list('id', flat=True)
    tomorrow_random_predicted_id = random.sample(list(tomorrow_predicted_random), min(len(tomorrow_predicted_random), 10))
    tomorrow_query_set = tomorrow_all_predictions.filter(id__in=tomorrow_random_predicted_id)

    yesterday_prediction = []
    today_prediction = []
    tomorrow_prediction = []

    predicted = []
    for fixture in yesterday_query_set:
        url = f"https://api-football-v1.p.rapidapi.com/v3/predictions?fixture={fixture.fixture_id}"
        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }
        response = requests.request("GET", url, headers=headers)
        resp = response.json()
        data = resp["response"][0]
        prediction = data.get("predictions").get("winner")

        predicted_winner = prediction.get("name")
        predicted_winner_id = prediction.get("id")
        predicted_advice = data.get("predictions").get("advice")
        predicted_win_or_draw = data.get("predictions").get("win_or_draw")
        if data.get("predictions").get("under_over"):
            predicted_under_or_over = data.get("predictions").get("under_over")
        else:
            predicted_under_or_over = None
        is_predicted = True
        correct_score = False if data.get("predictions").get("win_or_draw") else True
        predicted_home_score_odd = data.get("predictions").get("goals").get("home")
        predicted_away_score_odd = data.get("predictions").get("goals").get("away")

        ## update fixture prediction data
        fixture.predicted_winner = predicted_winner
        fixture.predicted_winner_id = predicted_winner_id
        fixture.predicted_advice = predicted_advice
        fixture.predicted_win_or_draw = predicted_win_or_draw
        predicted_under_or_over = predicted_under_or_over
        fixture.is_predicted = is_predicted
        fixture.correct_score = correct_score
        fixture.predicted_home_score_odd = predicted_home_score_odd
        fixture.predicted_away_score_odd = predicted_away_score_odd
        fixture.save()
        # predicted.append(data)

        home_team = fixture.home_team
        away_team = fixture.away_team
        league_name = fixture.league_type
        fixture_time = fixture.fixture_date.strftime("%m/%d/%Y, %H:%M:%S")
        fixture_name = f"{home_team} vs {away_team}"
        yesterday_prediction.append({
            "prediction":prediction,
            "predicted_winner":predicted_winner,
            "predicted_advice":predicted_advice,
            "predicted_win_or_draw": predicted_win_or_draw,
            "predicted_under_or_over": predicted_under_or_over,
            "is_predicted": is_predicted,
            "correct_score": correct_score,
            "predicted_home_score_odd": predicted_home_score_odd,
            "predicted_away_score_odd": predicted_away_score_odd,
            "home_team": home_team,
            "away_team": away_team,
            "league_name": league_name,
            "fixture_time": fixture_time,
            "fixture_name": fixture_name

        })

    for fixture in today_query_set:
        url = f"https://api-football-v1.p.rapidapi.com/v3/predictions?fixture={fixture.fixture_id}"
        
        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }
        response = requests.request("GET", url, headers=headers)
        resp = response.json()
        data = resp["response"][0]
        prediction = data.get("predictions").get("winner")

        predicted_winner = prediction.get("name")
        predicted_winner_id = prediction.get("id")
        predicted_advice = data.get("predictions").get("advice")
        predicted_win_or_draw = data.get("predictions").get("win_or_draw")
        if data.get("predictions").get("under_over"):
            predicted_under_or_over = data.get("predictions").get("under_over")
        else:
            predicted_under_or_over = None
        is_predicted = True
        correct_score = False if data.get("predictions").get("win_or_draw") else True
        predicted_home_score_odd = data.get("predictions").get("goals").get("home")
        predicted_away_score_odd = data.get("predictions").get("goals").get("away")

        ## update fixture prediction data
        fixture.predicted_winner = predicted_winner
        fixture.predicted_winner_id = predicted_winner_id
        fixture.predicted_advice = predicted_advice
        fixture.predicted_win_or_draw = predicted_win_or_draw
        predicted_under_or_over = predicted_under_or_over
        fixture.is_predicted = is_predicted
        fixture.correct_score = correct_score
        fixture.predicted_home_score_odd = predicted_home_score_odd
        fixture.predicted_away_score_odd = predicted_away_score_odd
        fixture.save()
        # predicted.append(data)

        home_team = fixture.home_team
        away_team = fixture.away_team
        league_name = fixture.league_type
        fixture_time = fixture.fixture_date.strftime("%m/%d/%Y, %H:%M:%S")
        fixture_name = f"{home_team} vs {away_team}"
        today_prediction.append({
            "prediction":prediction,
            "predicted_winner":predicted_winner,
            "predicted_advice":predicted_advice,
            "predicted_win_or_draw": predicted_win_or_draw,
            "predicted_under_or_over": predicted_under_or_over,
            "is_predicted": is_predicted,
            "correct_score": correct_score,
            "predicted_home_score_odd": predicted_home_score_odd,
            "predicted_away_score_odd": predicted_away_score_odd,
            "home_team": home_team,
            "away_team": away_team,
            "league_name": league_name,
            "fixture_time": fixture_time,
            "fixture_name": fixture_name

        })

    for fixture in tomorrow_query_set:
        url = f"https://api-football-v1.p.rapidapi.com/v3/predictions?fixture={fixture.fixture_id}"

        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }
        response = requests.request("GET", url, headers=headers)
        resp = response.json()
        data = resp["response"][0]
        prediction = data.get("predictions").get("winner")

        predicted_winner = prediction.get("name")
        predicted_winner_id = prediction.get("id")
        predicted_advice = data.get("predictions").get("advice")
        predicted_win_or_draw = data.get("predictions").get("win_or_draw")
        if data.get("predictions").get("under_over"):
            predicted_under_or_over = data.get("predictions").get("under_over")
        else:
            predicted_under_or_over = None
        is_predicted = True
        correct_score = False if data.get("predictions").get("win_or_draw") else True
        predicted_home_score_odd = data.get("predictions").get("goals").get("home")
        predicted_away_score_odd = data.get("predictions").get("goals").get("away")

        ## update fixture prediction data
        fixture.predicted_winner = predicted_winner
        fixture.predicted_winner_id = predicted_winner_id
        fixture.predicted_advice = predicted_advice
        fixture.predicted_win_or_draw = predicted_win_or_draw
        predicted_under_or_over = predicted_under_or_over
        fixture.is_predicted = is_predicted
        fixture.correct_score = correct_score
        fixture.predicted_home_score_odd = predicted_home_score_odd
        fixture.predicted_away_score_odd = predicted_away_score_odd
        fixture.save()
        # predicted.append(data)

        home_team = fixture.home_team
        away_team = fixture.away_team
        league_name = fixture.league_type
        fixture_time = fixture.fixture_date.strftime("%m/%d/%Y, %H:%M:%S")
        fixture_name = f"{home_team} vs {away_team}"
        tomorrow_prediction.append({
            "prediction":prediction,
            "predicted_winner":predicted_winner,
            "predicted_advice":predicted_advice,
            "predicted_win_or_draw": predicted_win_or_draw,
            "predicted_under_or_over": predicted_under_or_over,
            "is_predicted": is_predicted,
            "correct_score": correct_score,
            "predicted_home_score_odd": predicted_home_score_odd,
            "predicted_away_score_odd": predicted_away_score_odd,
            "home_team": home_team,
            "away_team": away_team,
            "league_name": league_name,
            "fixture_time": fixture_time,
            "fixture_name": fixture_name

        })
    
    link_id = generate_link_id()
    predicted_matches = {
            f"{first_date}": yesterday_prediction,
            f"{second_date}": today_prediction,
            f"{third_date}": tomorrow_prediction
        }
    link_ins = SoccerPredictionLink.objects.create(
        link_id=link_id,
        prediction_matches=predicted_matches,
        link_expiry_date=first_date + timedelta(days=3)

    )
    return f"ALL MATCHES PREDICTED"
        
    
@shared_task  
def create_rapid_prediction_matches():
    from sport_app.models import FootballTeamShortCode, PredictionTable

    url = "https://api-football-v1.p.rapidapi.com/v3/fixtures"

    from sport_app.helpers.team_code import TeamShortCode

    headers = {
        "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
        "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
    }
    first_date = datetime.now().date()
    second_date = first_date + timedelta(days=1)
    third_date = first_date + timedelta(days=2)
    
    first_querystring = {
        "date": f"{first_date}",
        "timezone": "Africa/Lagos",
        "status": "NS",
    }
    second_querystring = {
        "date": f"{second_date}",
        "timezone": "Africa/Lagos",
        "status": "NS",
    }
    third_querystring = {
        "date": f"{third_date}",
        "timezone": "Africa/Lagos",
        "status": "NS",
    }
    football_short_code = FootballTeamShortCode.objects.all()
    prediction_table = PredictionTable.objects.all()
    ## first date fixtures
    first_response = requests.request("GET", url, headers=headers, params=first_querystring)
    first_resp = first_response.json()
    first_data = first_resp.get("response")
    if len(first_data) >= 1:
        for i in first_data:
            fixture_id = i["fixture"]["id"]
            home_logo = i["teams"]["home"]["logo"]
            away_logo = i["teams"]["away"]["logo"]
            fixture_date = i["fixture"]["date"]
            league_type = i["league"]["name"]
            league_id = i["league"]["id"]
            home_id = i["teams"]["home"]["id"]
            away_id = i["teams"]["away"]["id"]
            home_team = i["teams"]["home"]["name"]
            away_team = i["teams"]["away"]["name"]
            home_team_code = football_short_code.filter(
                        team_id=home_id
                    ).last()
            away_team_code = football_short_code.filter(
                team_id=away_id
            ).last()
            if prediction_table.filter(fixture_id=fixture_id).exists():
                pass
            else:
                PredictionTable.objects.create(
                        fixture_id=fixture_id,
                        home_team=home_team,
                        away_team=away_team,
                        home_id=home_id,
                        away_id=away_id,
                        home_team_code=home_team_code.team_code if home_team_code else "",
                        away_team_code=away_team_code.team_code if away_team_code else "",
                        home_logo=home_logo,
                        away_logo=away_logo,
                        fixture_date=fixture_date,
                        league_type=league_type,
                        api_type="RAPID_API",
                        league_id=league_id,
                    )
                
    ## second date fixtures          
    second_response = requests.request("GET", url, headers=headers, params=second_querystring)
    second_resp = second_response.json()
    second_data = second_resp.get("response")
    if len(second_data) >= 1:
        for i in second_data:
            fixture_id = i["fixture"]["id"]
            home_logo = i["teams"]["home"]["logo"]
            away_logo = i["teams"]["away"]["logo"]
            fixture_date = i["fixture"]["date"]
            league_type = i["league"]["name"]
            league_id = i["league"]["id"]
            home_id = i["teams"]["home"]["id"]
            away_id = i["teams"]["away"]["id"]
            home_team = i["teams"]["home"]["name"]
            away_team = i["teams"]["away"]["name"]
            home_team_code = football_short_code.filter(
                        team_id=home_id
                    ).last()
            away_team_code = football_short_code.filter(
                team_id=away_id
            ).last()
            if prediction_table.filter(fixture_id=fixture_id).exists():
                pass
            else:
                PredictionTable.objects.create(
                        fixture_id=fixture_id,
                        home_team=home_team,
                        away_team=away_team,
                        home_id=home_id,
                        away_id=away_id,
                        home_team_code=home_team_code.team_code if home_team_code else "",
                        away_team_code=away_team_code.team_code if away_team_code else "",
                        home_logo=home_logo,
                        away_logo=away_logo,
                        fixture_date=fixture_date,
                        league_type=league_type,
                        api_type="RAPID_API",
                        league_id=league_id,
                    )
                
    ## third date fixtures          
    third_response = requests.request("GET", url, headers=headers, params=third_querystring)
    third_resp = third_response.json()
    third_data = third_resp.get("response")
    if len(third_data) >= 1:
        for i in third_data:
            fixture_id = i["fixture"]["id"]
            home_logo = i["teams"]["home"]["logo"]
            away_logo = i["teams"]["away"]["logo"]
            fixture_date = i["fixture"]["date"]
            league_type = i["league"]["name"]
            league_id = i["league"]["id"]
            home_id = i["teams"]["home"]["id"]
            away_id = i["teams"]["away"]["id"]
            home_team = i["teams"]["home"]["name"]
            away_team = i["teams"]["away"]["name"]
            home_team_code = football_short_code.filter(
                        team_id=home_id
                    ).last()
            away_team_code = football_short_code.filter(
                team_id=away_id
            ).last()
            if prediction_table.filter(fixture_id=fixture_id).exists():
                pass
            else:
                PredictionTable.objects.create(
                        fixture_id=fixture_id,
                        home_team=home_team,
                        away_team=away_team,
                        home_id=home_id,
                        away_id=away_id,
                        home_team_code=home_team_code.team_code if home_team_code else "",
                        away_team_code=away_team_code.team_code if away_team_code else "",
                        home_logo=home_logo,
                        away_logo=away_logo,
                        fixture_date=fixture_date,
                        league_type=league_type,
                        api_type="RAPID_API",
                        league_id=league_id,
                    )
    return "PREDICTION MATCHES RUN SUCCESSFULLY"      

@shared_task
def check_prediction_subscription_plan():
    from sport_app.models import SoccerOddPredictionTable
    get_all_subscription = SoccerOddPredictionTable.objects.filter(is_active=True)
    for subscription in get_all_subscription:
        if subscription.subscription_days_left > 0:
            current_subscription_days = subscription.subscription_days_left
            next_subscription_days = current_subscription_days - 1
            subscription.subscription_days_left -= 1
            if next_subscription_days < 1:
                subscription.is_active = False
            subscription.save()
        else:
            subscription.subscription_days_left = 0
            subscription.is_active = False
            subscription.save()

@shared_task
def clear_prediction_subscription_plan():
    from sport_app.models import SoccerOddPredictionTable
    get_all_subscription = SoccerOddPredictionTable.objects.filter(is_active=True)
    for subscription in get_all_subscription: 
        subscription.subscription_days_left = 0
        subscription.is_active = False
        subscription.save()