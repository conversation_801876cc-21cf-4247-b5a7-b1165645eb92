from sport_app.decisioning_engine.fund_wallet import fund_user_or_agent_wallet
from sport_app.decisioning_engine.team_win_draw import WinTeamGamePlay
from sport_app.models import DecisioningResult, SoccerCashWinner, TeamFinalistPrediction


class WinningTeamDecisioning:
    def run_game_disburse_amount(game_model, win_data):
        game = WinTeamGamePlay()
        game_winners = game.run(game_model, win_data)
        DecisioningResult.objects.create(result_payload=game_winners)
        # pprint(game_winners)

        for key, value in game_winners.items():
            if isinstance(value, dict):
                for winner_item in value.get("winners"):
                    phone_no = winner_item["player_phone"]

                    match_prediction_instance = TeamFinalistPrediction.objects.get(id=winner_item["player_id"])
                    amount_won = winner_item["earning"]
                    dual_team_finalist_id = winner_item["team_id"]
                    agent_id = winner_item["agent_id"]

                    SoccerCashWinner.objects.create(
                        phone_number=phone_no,
                        agent_id=int(agent_id),
                        game_play_id=match_prediction_instance.game_id,
                        player_id=winner_item["player_id"],
                        stake_amount=match_prediction_instance.stake_amount,
                        earning=amount_won,
                        channel_played_from=match_prediction_instance.channel,
                        game_fixture_id=f"WIN TEAM - {dual_team_finalist_id}",
                    )

                    fund_user_or_agent_wallet(agent_id, amount_won, phone_no)
                    # TODO send win sms to winner

    @classmethod
    def run_game(cls, game_model: TeamFinalistPrediction, win_data: dict):
        """This method runs the decisioning
        on save method when is_top_scorer is
        toggle true at the end of each match
        and also sends sms, disburse win
        amount to winners
        """

        try:
            # run game decisioining
            # paid game
            print(game_model)
            paid_game_players = TeamFinalistPrediction.objects.filter(team_final_list=game_model, paid=True)
            cls.run_game_disburse_amount(paid_game_players, win_data)

            # TODO send sms to winner that lost the game

        except ZeroDivisionError:
            pass
