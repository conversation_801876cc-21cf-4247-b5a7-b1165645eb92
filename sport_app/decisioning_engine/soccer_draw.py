from pprint import pprint


def soccer_cash_draw(load_players, players, win_data, running_balance):
    from sport_app.models import RunningBalance

    """
    MAIN ENTRY POINT FOR APPLICATION, EXPECTS LotteryModel QUERYSET FROM MAIN APP.
    """
    result = load_players(players, win_data)  # LOAD PLAYERS INTO GAME CLASS

    in_excess = result.get("in_excess")
    rtp = result.get("rtp", 0.00)
    rto = result.get("rto", 0.00)
    initial_rtp = result.get("initial_rtp", 0.00)
    total_stake_amount = result.get("total_stake_amount", 0.00)
    # amount = in_excess - running_balance
    amount = in_excess

    RunningBalance.create_update_running_balance(
        amount=int(round(amount, 2)),
        rtp=rtp,
        rto=rto,
        initial_rtp=initial_rtp,
        total_stake_amount=total_stake_amount,
    )
    pprint(result)
    return result
