from main.models import UserProfile
from pos_app.models import Agent<PERSON><PERSON><PERSON>
from wyse_ussd.tasks import ussd_lottery_winner_reward


def fund_user_or_agent_wallet(agent_id, amount_won, phone_no, game_play_id=None):
    if phone_no and not agent_id:  # credit user
        print("FUND USER WALLET")
        user = UserProfile.objects.filter(phone_number=phone_no).last()
        ussd_lottery_winner_reward.delay(user_id=user.id, amount=amount_won, trans_from="SOCCER_CASH_GAME_WIN")

    elif (agent_id and not phone_no) or (agent_id and phone_no):  # credit the agent
        # AgentWallet.credit_agent_winning_balance(agent_id, amount_won)
        AgentWallet.credit_agent_winning_balance(
            agent_id=agent_id,
            amount=amount_won,
            phone_number=phone_no,
            game_type="SOCCER_CASH",
            game_play_id=game_play_id,
        )
        print("FUND AGENT WALLET")

    else:
        print("NOT A VALID PROCESS")
