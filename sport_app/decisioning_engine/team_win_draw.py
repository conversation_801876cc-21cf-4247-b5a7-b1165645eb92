import random

from sport_app.decisioning_engine.soccer_draw import soccer_cash_draw
from wyse_ussd.helper.decisioning.game_settings import GAME_BANDS, RTO, RTP


class GameModel:
    """
    A TEST MODEL TO SIMULATE DARE'S MODEL WHILE BUILDING
    PLEASE IGNORE.
    """

    def __init__(self) -> None:
        self.id = random.randint(1, **********)
        self.team_id = 12
        self.phone = "*************"
        self.band_played = random.choice(["200", "500", "1000", "2000"])
        self.stake_amount = random.choice(["200", "500", "1000", "2000"])
        self.paid = True
        self.account_no = ""
        self.bank_name = ""
        self.date = ""
        self.paid_date = ""
        self.bank_code = ""


class Player:
    def __init__(
        self,
        id: int,
        phone: str,
        team_id: int,
        band_played: int,
        stake_amount: float,
        agent_id: int,
    ) -> None:
        self.phone: str = phone
        self.id: int = id
        self.team_id: int = team_id
        self.band_played: int = int(float(band_played))
        self.earning: int
        self.stake_amount: float = float(stake_amount)
        self.agent_id: int = agent_id

    def __str__(self) -> str:
        return f"{self.phone} - {self.id} - {self.team_id} - {self.band_played}"

    def __repr__(self) -> str:
        return f"{self.phone} : {self.id} : {self.band_played}\n"

    # @property
    # def earning(self):
    #     return self.band_played * 1000


class WinTeamGamePlay:
    def __init__(self) -> None:
        from sport_app.models import RunningBalance

        self.GAME_REVENUE = 0
        self.winners = 0
        self.PASSED = 0
        self.players = []
        self.game_result: dict = {}
        self.running_balance = RunningBalance.get_running_balance_amount()

    def load_players(self, raw_players: list, win_data: dict) -> bool:
        """
        EXPECTS LotteryModel QUERYSET FROM MAIN APP, LOOPS THROUGH AND CONVERTS THEM TO ACTUAL PLAYER OBJECTS TO FIT GAME REQUIREMENTS
        """

        # Initialize some lists
        all_players = []
        winner_players = []
        looser_players = []
        get_all_bands = []
        result = {}

        # Grab all bands dynamically from settings and set all players, winners and loosers
        for raw_player in raw_players:
            if {"band": raw_player.band_played} not in get_all_bands:
                get_all_bands.append({"band": raw_player.band_played})
            else:
                pass

            player = Player(
                raw_player.id,
                raw_player.team_final_list_id,
                raw_player.phone,
                raw_player.band_played,
                raw_player.stake_amount,
                raw_player.agent_id,
            )
            all_players.append(player)

            if raw_player.team_final_list_id == win_data["team_id"]:
                winner_players.append(player)
            else:
                looser_players.append(player)

        # Get total amount staked
        total_stake_amount = sum([player.band_played for player in all_players])
        print("I AM TOTAL STAKE AMOUNT", total_stake_amount)

        initial_rtp = (RTP / 100) * total_stake_amount
        get_rtp = initial_rtp + self.running_balance
        get_rto = (RTO / 100) * total_stake_amount
        winners_total_payable = 0
        result["final_payable"] = 0

        # Dynamically set win amount and get winners total payable
        for band in get_all_bands:
            result[band["band"]] = {"winners": [], "band_total_paid": 0}

            for game_band in GAME_BANDS.keys():
                if int(float(band["band"])) == game_band:
                    band["win_amount"] = GAME_BANDS[game_band]["win_amount"]

            for player in winner_players:
                print("BAND PLAY", player.band_played)
                if int(float(band["band"])) == player.band_played:
                    winners_total_payable += band["win_amount"]

        print("PAYABLE::::", winners_total_payable)
        print("RTP::::", get_rtp)

        """
            PROGRAMATICALLY TRYING TO GET THE FINAL WIN AMOUNT AMONGST BANDS
        """

        for band in get_all_bands:
            if winners_total_payable <= get_rtp:
                print("MONEY ENOUGH")

                band["final_win_amount"] = band["win_amount"]
                # result["in_excess"] = get_rtp - winners_total_payable

            else:
                discrepancy = winners_total_payable / get_rtp

                print("DISCREPANCY::", discrepancy)

                band["final_win_amount"] = band["win_amount"] / discrepancy

        if get_rtp >= winners_total_payable:
            result["shares_enough"] = True
        else:
            result["shares_enough"] = False

        for player in winner_players:
            for band in get_all_bands:
                if int(float(player.band_played)) == int(float(band["band"])):
                    result[band["band"]]["winners"].append(
                        {
                            "player_id": player.id,
                            "agent_id": player.agent_id,
                            "player_phone": player.phone,
                            "team_id": win_data["team_id"],
                            "earning": int(band["final_win_amount"]),
                        }
                    )

                    result[band["band"]]["band_total_paid"] += band["final_win_amount"]
                    result["final_payable"] += band["final_win_amount"]

        get_number_of_players = len(all_players)
        get_number_of_winners = len(winner_players)
        print(get_number_of_winners)
        print(get_number_of_players)
        if get_number_of_winners > 0:
            get_percent_of_winners = (get_number_of_winners / get_number_of_players) * 100
        else:
            get_percent_of_winners = (get_number_of_winners / get_number_of_players) * 100

        result["total_players"] = get_number_of_players
        result["total_winners"] = get_number_of_winners
        result["winners_percent"] = get_percent_of_winners
        result["rtp"] = get_rtp
        result["rto"] = get_rto
        result["total_stake_amount"] = total_stake_amount
        result["initial_rtp"] = initial_rtp
        result["in_excess"] = (get_rtp - result["final_payable"]) if (get_rtp - result["final_payable"]) > 0 else 0

        # pprint(result)

        return result

    def run(self, players: list, win_data: dict):
        return soccer_cash_draw(self.load_players, players, win_data, self.running_balance)


if __name__ == "__main__":
    game = WinTeamGamePlay()

    win_data = {"team_id": 10}
    game.run([GameModel() for _ in range(500)], win_data)
