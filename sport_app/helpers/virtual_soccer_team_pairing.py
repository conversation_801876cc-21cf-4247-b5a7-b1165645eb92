import random


def match_virtual_teams(queryset):
    teams = list(queryset)
    random.shuffle(teams)
    num_teams = len(teams)
    matches = []

    # If there is an odd number of teams, add a "bye" team
    if num_teams % 2 != 0:
        bye_team = teams.pop()
        num_teams -= 1

    # Create matches by pairing up adjacent teams
    for i in range(0, num_teams, 2):
        home_team = teams[i]
        away_team = teams[i + 1]
        match = f"{home_team.name} vs {away_team.name}"
        matches.append(
            {
                "home_team": home_team.name,
                "home_team_code": home_team.name_code,
                "home_team_id": home_team.id,
                "away_team": away_team.name,
                "away_team_code": away_team.name_code,
                "away_team_id": away_team.id,
                "match": match,
                "home_team_logo": home_team.logo,
                "away_team_logo": away_team.logo,
            }
        )

    # If there was a "bye" team, add them to the end of the matches
    if num_teams % 2 != 0:
        bye_match = f"{bye_team.name} has a bye week"
        matches.append(
            {
                "home_team": bye_team,
                "home_team_code": bye_team.name_code,
                "home_team_id": bye_team.id,
                "away_team": None,
                "away_team_code": None,
                "away_team_id": None,
                "match": bye_match,
                "home_team_logo": bye_team.logo,
                "away_team_logo": None,
            }
        )

    return matches


def virtual_soccer_related_match_score(score):
    """
    PARAMS: score (tuple) e.g (1, 4)
    """

    related_scores = []
    score_differences = [(-1, 1), (0, 1), (1, 1)]

    if isinstance(score, tuple):
        for diff in score_differences:
            a_score = score[0] + diff[0]
            b_score = score[1] + diff[1]
            if a_score >= 0 and b_score >= 0 and (a_score, b_score) != score:
                related_scores.append(a_score)
                related_scores.append(b_score)
                break

        return related_scores
    else:
        raise TypeError("score must be a tuple")
