import datetime

import requests
from django.conf import settings

from sport_app.models import (
    EuropaLeagueWeekHistory,
    FootballTable,
    FootballTeamShortCode,
    ImportantTeams,
)


class LotteryFootballEuropaLeague:
    def __init__(self):
        self.url = "https://api-football-v1.p.rapidapi.com/v3"
        self.headers = {"X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}", "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com"}

    def week_match(self):
        id = "3"

        def get_week(n):
            a_week = EuropaLeagueWeekHistory.objects.last()
            if a_week is None:
                # week = EuropaLeagueWeekHistory.objects.create(week_start_date="2022-10-03", week_end_date="2022-10-09")
                return get_week()
            else:
                start_date = a_week.week_start_date
                end_date = a_week.week_end_date
                season = f"{settings.EUROPA_LEAGUE_SEASON_YEAR}"

                url = f"{self.url}/fixtures"
                querystring = {
                    "league": f"{id}",
                    "season": f"{season}",
                    "from": f"{start_date}",
                    "to": f"{end_date}",
                    "timezone": "Africa/Lagos",
                    "status": "NS",
                }

                response = requests.request("GET", url, headers=self.headers, params=querystring)

                print(start_date)
                print(end_date)
                print("\nWEEK MATCH:::::", response.text, ":::::")
                resp = response.json()
                data = resp.get("response")

                print("ALL WEEK FIXTURES :::::::", data)
                if n == 0:
                    return None
                if data is None:
                    return None
                    # return {"message": "No match Currently Available"}
                if len(data) < 1:
                    print("No match this week")
                    date_1 = start_date + datetime.timedelta(days=7)
                    date_2 = end_date + datetime.timedelta(days=7)
                    EuropaLeagueWeekHistory.objects.create(week_start_date=date_1, week_end_date=date_2)
                    print("CURRENT VALUE OF COUNT", n)
                    get_week(n - 1)
                if len(data) >= 1:
                    return data

        get_week_match = get_week(4)
        if get_week_match is None:
            return {"message": "No match Currently Available"}
        else:
            content = ImportantTeams.objects.all()
            content_list = [x.team_id for x in content]
            print("The list is: ", content_list)
            first_data = []
            second_data = []

            print("match available this week")
            # match fixtures
            fixture_list = []
            fixtures_data = []
            app = get_week_match
            for i in app:
                home_id = i["teams"]["home"]["id"]
                away_id = i["teams"]["away"]["id"]
                home_team = i["teams"]["home"]["name"]
                away_team = i["teams"]["away"]["name"]
                home_fix = {
                    "fixture_id": i["fixture"]["id"],
                    "home_team": home_team,
                    "away_team": away_team,
                    "home_id": home_id,
                    "away_id": away_id,
                    "home_logo": i["teams"]["home"]["logo"],
                    "away_logo": i["teams"]["away"]["logo"],
                    "fixture_date": i["fixture"]["date"],
                    "league_type": "UEFA Europa League",
                    "league_id": f"{id}",
                }
                if str(home_id) in content_list or str(away_id) in content_list:
                    if home_fix not in first_data:
                        first_data.append(home_fix)
                    else:
                        pass
                else:
                    if home_fix not in second_data:
                        second_data.append(home_fix)
                    else:
                        pass
            print(first_data, "\n\n")
            print(second_data, "\n\n")
            fixture_list = [*first_data, *second_data]
            for x in fixture_list:
                if x not in fixtures_data:
                    fixtures_data.append(x)
                    if FootballTable.objects.filter(fixture_id=x["fixture_id"]).exists():
                        print(x["fixture_id"], " already exists :::::")
                    else:
                        home_id = x["home_id"]
                        away_id = x["away_id"]
                        home_team = x["home_team"]
                        away_team = x["away_team"]
                        home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()
                        away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()
                        FootballTable.objects.create(
                            fixture_id=x["fixture_id"],
                            home_team=home_team,
                            away_team=away_team,
                            home_id=home_id,
                            away_id=away_id,
                            home_team_code=home_team_code.team_code,
                            away_team_code=away_team_code.team_code,
                            home_logo=x["home_logo"],
                            away_logo=x["away_logo"],
                            fixture_date=x["fixture_date"],
                            league_type=x["league_type"],
                            league_id=x["league_id"],
                        )
                else:
                    pass
            print(":::::::::::::::")
            print(":::::::::::::::")
            print(":::::::::::::::")
            print(":::::::::::::::")
            print("FILTERED WEEK FIXTURE ::::::", fixtures_data)
            return fixtures_data

            # end of match fixtures

    def lottery_scores(self, id):
        url = f"{self.url}/odds"

        querystring = {"fixture": id}

        response = requests.request("GET", url, headers=self.headers, params=querystring)

        resp = response.json()

        bet_odd = resp["response"][0]["bookmakers"]

        find_exact = []
        for i in bet_odd:
            if i["id"] == 11:
                find_exact = i["bets"]
                # print(find_exact)
        for j in find_exact:
            if j["id"] == 10:
                data = {
                    "name": j["name"],
                    "values": j["values"],
                }
                print("\n\n\nEXACT SCORES :::::::::::::", data)

    def fulltime_scores(self, id):
        url = f"{self.url}/fixtures"

        querystring = {"id": f"{id}"}

        response = requests.request("GET", url, headers=self.headers, params=querystring)
        resp = response.json()
        home_score = resp.get("response")[0]["score"]["fulltime"]["home"]
        away_score = resp.get("response")[0]["score"]["fulltime"]["away"]
        home_team = resp.get("response")[0]["teams"]["home"]["name"]
        away_team = resp.get("response")[0]["teams"]["away"]["name"]
        print("\n\n\n FULLTIME RESULT :::::::", home_team, ":", home_score, " ", away_team, ":", away_score, "\n\n")

    def last_match(self):
        url = f"{self.url}/fixtures"

        querystring = {"date": "2022-10-09", "league": "3", "season": "2022"}

        response = requests.request("GET", url, headers=self.headers, params=querystring)
        print("\nMAN U LAST MATCH :::", response.text)
