import datetime

import requests
from django.conf import settings
from django.db.models import Q

from sport_app.models import (
    DualTeamFinalist,
    DualTeamFinalistPrediction,
    FootballTable,
    FootballTeamShortCode,
    GoalScorer,
    GoalScorerPrediction,
    ImportantTeams,
    PlayerGoal,
    TeamFinalist,
    TeamFinalistPrediction,
    WorldCupTeams,
    WorldCupWeekHistory,
)

# from wyse_ussd.models import GoalScorerPrediction


class LotteryFootballWorldCup:
    def __init__(self):
        self.url = "https://api-football-v1.p.rapidapi.com/v3"
        self.headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }

    def week_match(self):
        id = "1"

        def get_week(n):
            a_week = WorldCupWeekHistory.objects.last()
            if a_week is None:
                # week = WorldCupWeekHistory.objects.create(week_start_date="2022-10-03", week_end_date="2022-10-09")
                return get_week()
            else:
                start_date = a_week.week_start_date
                end_date = a_week.week_end_date
                season = f"{settings.WORLD_CUP_SEASON_YEAR}"

                url = f"{self.url}/fixtures"

                querystring = {
                    "league": f"{id}",
                    "season": f"{season}",
                    "from": f"{start_date}",
                    "to": f"{end_date}",
                    "timezone": "Africa/Lagos",
                    "status": "NS",
                }

                response = requests.request("GET", url, headers=self.headers, params=querystring)

                print(start_date)
                print(end_date)
                # print("\nWEEK MATCH:::::",response.text,":::::")
                resp = response.json()
                data = resp.get("response")
                print("ALL WEEK FIXTURES :::::::", data)

                if n == 0:
                    return None
                if data is None:
                    return None
                    # return {"message": "No match Currently Available"}
                if len(data) < 1:
                    print("No match this week")
                    date_1 = start_date + datetime.timedelta(days=7)
                    date_2 = end_date + datetime.timedelta(days=7)
                    WorldCupWeekHistory.objects.create(week_start_date=date_1, week_end_date=date_2)
                    print("CURRENT VALUE OF COUNT", n)
                    get_week(n - 1)
                if len(data) >= 1:
                    return data

        get_week_match = get_week(4)
        if get_week_match is None:
            return {"message": "No match Currently Available"}
        else:
            content = ImportantTeams.objects.all()
            content_list = [x.team_id for x in content]
            print("The list is: ", content_list)
            first_data = []
            second_data = []

            print("match available this week")
            # match fixtures
            fixture_list = []
            fixtures_data = []
            app = get_week_match
            for i in app:
                home_id = i["teams"]["home"]["id"]
                away_id = i["teams"]["away"]["id"]
                home_team = i["teams"]["home"]["name"]
                away_team = i["teams"]["away"]["name"]
                home_fix = {
                    "fixture_id": i["fixture"]["id"],
                    "home_team": home_team,
                    "away_team": away_team,
                    "home_id": home_id,
                    "away_id": away_id,
                    "home_logo": i["teams"]["home"]["logo"],
                    "away_logo": i["teams"]["away"]["logo"],
                    "fixture_date": i["fixture"]["date"],
                    "league_type": "World Cup",
                    "league_id": f"{id}",
                }
                if str(home_id) in content_list or str(away_id) in content_list:
                    if home_fix not in first_data:
                        first_data.append(home_fix)
                    else:
                        pass
                else:
                    if home_fix not in second_data:
                        second_data.append(home_fix)
                    else:
                        pass
            print(first_data, "\n\n")
            print(second_data, "\n\n")
            fixture_list = [*first_data, *second_data]
            for x in fixture_list:
                if x not in fixtures_data:
                    fixtures_data.append(x)
                    if FootballTable.objects.filter(fixture_id=x["fixture_id"]).exists():
                        print(x["fixture_id"], " already exists :::::")
                    else:
                        home_id = x["home_id"]
                        away_id = x["away_id"]
                        home_team = x["home_team"]
                        away_team = x["away_team"]
                        home_team_code = FootballTeamShortCode.objects.filter(team_id=home_id).last()
                        away_team_code = FootballTeamShortCode.objects.filter(team_id=away_id).last()
                        FootballTable.objects.create(
                            fixture_id=x["fixture_id"],
                            home_team=home_team,
                            away_team=away_team,
                            home_id=home_id,
                            away_id=away_id,
                            home_team_code=home_team_code.team_code,
                            away_team_code=away_team_code.team_code,
                            home_logo=x["home_logo"],
                            away_logo=x["away_logo"],
                            fixture_date=x["fixture_date"],
                            league_type=x["league_type"],
                            league_id=x["league_id"],
                        )
                else:
                    pass
            print(":::::::::::::::")
            print(":::::::::::::::")
            print(":::::::::::::::")
            print(":::::::::::::::")
            print("FILTERED WEEK FIXTURE ::::::", fixtures_data)
            return fixtures_data

            # end of match fixtures

    def lottery_scores(self, id):
        url = f"{self.url}/odds"

        querystring = {"fixture": id}

        response = requests.request("GET", url, headers=self.headers, params=querystring)

        resp = response.json()

        bet_odd = resp["response"][0]["bookmakers"]

        find_exact = []
        for i in bet_odd:
            if i["id"] == 11:
                find_exact = i["bets"]
                # print(find_exact)
        for j in find_exact:
            if j["id"] == 10:
                data = {
                    "name": j["name"],
                    "values": j["values"],
                }
                print("\n\n\nEXACT SCORES :::::::::::::", data)

    def fulltime_scores(self, id):
        url = f"{self.url}/fixtures"

        querystring = {"id": f"{id}"}

        response = requests.request("GET", url, headers=self.headers, params=querystring)
        resp = response.json()
        home_score = resp.get("response")[0]["score"]["fulltime"]["home"]
        away_score = resp.get("response")[0]["score"]["fulltime"]["away"]
        home_team = resp.get("response")[0]["teams"]["home"]["name"]
        away_team = resp.get("response")[0]["teams"]["away"]["name"]
        print(
            "\n\n\n FULLTIME RESULT :::::::",
            home_team,
            ":",
            home_score,
            " ",
            away_team,
            ":",
            away_score,
            "\n\n",
        )

    def last_match(self):
        url = f"{self.url}/fixtures"

        querystring = {"date": "2022-10-09", "league": "1", "season": "2022"}

        response = requests.request("GET", url, headers=self.headers, params=querystring)
        print("\nMAN U LAST MATCH :::", response.text)

    # get highest goal scorers player
    def get_players():
        top_scorers = {}
        players = GoalScorer.objects.filter(available=True)
        for p in players:
            top_scorers.update({p.id: 0})
            print(p.id)
            print(type(p.id))

        print("TOP SCORERS", top_scorers)
        get_player = GoalScorerPrediction.objects.all()
        for game in get_player:
            print("PLAYER_ID", game.goal_scorer.id)
            if int(game.goal_scorer.id) in top_scorers.keys():
                print(True)
                top_scorers[game.goal_scorer.id] = int(top_scorers[game.goal_scorer.id] + 1)

        score_sum = []
        score_data = []
        for unit_score in top_scorers:
            score_sum.append(top_scorers[unit_score])
        total_score = sum(score_sum)
        print(total_score)
        for player_id in top_scorers:
            try:
                percentage_prediction = (top_scorers[player_id] / total_score) * 100
                score_data.append(
                    {
                        "player": GoalScorer.objects.get(id=player_id).player_name,
                        "player_id": player_id,
                        "percentage": "{0:.2f}".format(percentage_prediction),
                    }
                )
            except ZeroDivisionError:
                score_data.append(
                    {
                        "player": GoalScorer.objects.get(id=player_id).player_name,
                        "player_id": player_id,
                        "percentage": "{0:.2f}".format(0),
                    }
                )
        return score_data

    # get highest goal scorers goals
    def get_players_goal(player_id):
        # player_goals = {}
        # goals = PlayerGoal.objects.all()
        # for g in goals:
        #     player_goals.update({g.goal: 0})
        #     print(g.goal)
        #     print(type(g.goal))

        # player_id = int(player_id)
        # get_player = GoalScorerPrediction.objects.filter(goal_scorer__id=player_id)
        # for game in get_player:
        #     print("PLAYER_ID", game.goal_scorer.id)
        #     print("PLAYER_GOALS", game.goal)
        #     if int(game.goal) in player_goals.keys():
        #         print(True)
        #         player_goals[game.goal]=int(player_goals[game.goal]+1)

        # score_sum = []
        # score_data = []
        # per_sum = []
        # for unit_score in player_goals:
        #     score_sum.append(player_goals[unit_score])
        # total_score = sum(score_sum)
        # print(total_score)
        # for goals in player_goals:
        #     try:
        #         percentage_prediction = (player_goals[goals]/total_score)*100
        #         score_data.append(
        #             {
        #                 "goals":goals,
        #                 "percentage":'{0:.2f}'.format(percentage_prediction)
        #             }
        #         )
        #     except ZeroDivisionError:
        #         score_data.append(
        #             {
        #                 "goals":goals,
        #                 "percentage": '{0:.2f}'.format(0)
        #             }
        #         )
        # goal_scorer =GoalScorer.objects.filter(id=player_id).last()
        # player_data  = {
        #     "player_id": player_id,
        #     "player_name": goal_scorer.player_name if goal_scorer else None,
        #     "country": goal_scorer.player_country.team_name if goal_scorer else None,
        #     "country_flag": goal_scorer.player_country.team_logo if goal_scorer else None,
        #     "player_goals": score_data
        # }
        # return player_data
        first_data = []
        second_data = []
        player_goals = {}
        goals = PlayerGoal.objects.all()
        for g in goals:
            player_goals.update({g.goal: 0})
            print(g.goal)
            print(type(g.goal))

        player_id = int(player_id)
        get_player = GoalScorerPrediction.objects.filter(goal_scorer__id=player_id)
        for game in get_player:
            print("PLAYER_ID", game.goal_scorer.id)
            print("PLAYER_GOALS", game.goal)
            if int(game.goal) in player_goals.keys():
                print(True)
                player_goals[game.goal] = int(player_goals[game.goal] + 1)

        score_sum = []
        score_data = []
        for unit_score in player_goals:
            score_sum.append(player_goals[unit_score])
        total_score = sum(score_sum)
        print(total_score)
        for goals in player_goals:
            try:
                percentage_prediction = (player_goals[goals] / total_score) * 100
                score_data.append(
                    {
                        "goals": goals,
                        "percentage": "{0:.2f}".format(percentage_prediction),
                    }
                )
            except ZeroDivisionError:
                score_data.append({"goals": goals, "percentage": "{0:.2f}".format(0)})
        goal_scorer = GoalScorer.objects.filter(id=player_id).last()
        player_data = {
            "player_id": player_id,
            "player_name": goal_scorer.player_name if goal_scorer else None,
            "country": goal_scorer.player_country.team_name if goal_scorer else None,
            "country_flag": goal_scorer.player_country.team_logo if goal_scorer else None,
            "player_goals": score_data,
        }
        first_data.append(player_data)

        # Get other players data
        all_scorer = GoalScorer.objects.exclude(id=player_id)
        all_scorer.filter(Q(available=True))
        o_goals = PlayerGoal.objects.all()
        print(all_scorer)
        other_goals = {}
        for o in o_goals:
            other_goals.update({o.goal: 0})
            print(o.goal)
            print(type(o.goal))
        for scorer in all_scorer:
            get_player = GoalScorerPrediction.objects.filter(goal_scorer__id=scorer.id)
            for o_game in get_player:
                print("PLAYER_ID", o_game.goal_scorer.id)
                print("PLAYER_GOALS", o_game.goal)
                if int(o_game.goal) in other_goals.keys():
                    print(True)
                    other_goals[o_game.goal] = int(other_goals[o_game.goal] + 1)

            score_sum = []
            score_data = []

            for unit_score in other_goals:
                score_sum.append(other_goals[unit_score])
            total_score = sum(score_sum)
            print(total_score)
            for goals in other_goals:
                try:
                    percentage_prediction = (other_goals[goals] / total_score) * 100
                    score_data.append(
                        {
                            "goals": goals,
                            "percentage": "{0:.2f}".format(percentage_prediction),
                        }
                    )
                except ZeroDivisionError:
                    score_data.append({"goals": goals, "percentage": "{0:.2f}".format(0)})
            other_scorer = GoalScorer.objects.filter(id=scorer.id).last()
            cup_team = WorldCupTeams.objects.filter(team_name=other_scorer.player_country).last()
            player_data = {
                "player_id": scorer.id,
                "player_name": other_scorer.player_name if other_scorer else None,
                "country": cup_team.team_name if cup_team else None,
                "country_flag": cup_team.team_logo if cup_team else None,
                "player_goals": score_data,
            }
            second_data.append(player_data)

        all_data = [*first_data, *second_data]
        data = {
            "all_data": all_data,
        }
        return data

    # get team finalist
    def get_team_finalist():
        team_finalist = {}
        teams = TeamFinalist.objects.filter(available=True)
        for t in teams:
            team_finalist.update({t.id: 0})
            print(t.id)
            print(type(t.id))

        print("FINALIST", team_finalist)
        get_teams = TeamFinalistPrediction.objects.all()
        for team in get_teams:
            print("FINALIST_ID", team.team_final_list.id)
            if int(team.team_final_list.id) in team_finalist.keys():
                print(True)
                team_finalist[team.team_final_list.id] = int(team_finalist[team.team_final_list.id] + 1)

        score_sum = []
        score_data = []
        for unit_score in team_finalist:
            score_sum.append(team_finalist[unit_score])
        total_score = sum(score_sum)
        print(total_score)
        for finalist_id in team_finalist:
            team = TeamFinalist.objects.filter(id=finalist_id).last()
            try:
                percentage_prediction = (team_finalist[finalist_id] / total_score) * 100

                score_data.append(
                    {
                        "finalist_id": finalist_id,
                        "finalist_team_id": team.team_name.id,
                        "finalist": team.team_name.team_name,
                        "finalist_logo": team.team_name.team_logo,
                        "percentage": "{0:.2f}".format(percentage_prediction),
                    }
                )
            except ZeroDivisionError:
                score_data.append(
                    {
                        "finalist_id": finalist_id,
                        "finalist_team_id": team.team_name.id,
                        "finalist": team.team_name.team_name,
                        "finalist_logo": team.team_name.team_logo,
                        "percentage": "{0:.2f}".format(0),
                    }
                )
        return score_data

    # get dual team finalist
    def get_dual_team_finalist():
        dual_team_finalist = {}
        teams = DualTeamFinalist.objects.filter(available=True)
        for t in teams:
            dual_team_finalist.update({t.id: 0})
            print(t.id)
            print(type(t.id))

        print("FINALIST", dual_team_finalist)
        get_teams = DualTeamFinalistPrediction.objects.all()
        for team in get_teams:
            print("FINALIST_ID", team.dual_team_finalist.id)
            if int(team.dual_team_finalist.id) in dual_team_finalist.keys():
                print(True)
                dual_team_finalist[team.dual_team_finalist.id] = int(dual_team_finalist[team.dual_team_finalist.id] + 1)

        score_sum = []
        score_data = []
        for unit_score in dual_team_finalist:
            score_sum.append(dual_team_finalist[unit_score])
        total_score = sum(score_sum)
        print(total_score)
        for finalist_id in dual_team_finalist:
            a_id = DualTeamFinalist.objects.get(id=finalist_id).team_a.id
            b_id = DualTeamFinalist.objects.get(id=finalist_id).team_b.id
            team_a_id = WorldCupTeams.objects.get(id=a_id).id
            team_b_id = WorldCupTeams.objects.get(id=b_id).id
            team_a_name = WorldCupTeams.objects.get(id=a_id).team_name
            team_b_name = WorldCupTeams.objects.get(id=b_id).team_name
            team_a_logo = WorldCupTeams.objects.get(id=a_id).team_logo
            team_b_logo = WorldCupTeams.objects.get(id=b_id).team_logo
            try:
                percentage_prediction = (dual_team_finalist[finalist_id] / total_score) * 100
                score_data.append(
                    {
                        "finalist_id": finalist_id,
                        "team_a": team_a_id,
                        "team_b": team_b_id,
                        "team_a_name": team_a_name,
                        "team_b_name": team_b_name,
                        "team_a_logo": team_a_logo,
                        "team_b_logo": team_b_logo,
                        "percentage": "{0:.2f}".format(percentage_prediction),
                    }
                )
            except ZeroDivisionError:
                score_data.append(
                    {
                        "finalist_id": finalist_id,
                        "team_a": team_a_id,
                        "team_b": team_b_id,
                        "team_a_name": team_a_name,
                        "team_b_name": team_b_name,
                        "team_a_logo": team_a_logo,
                        "team_b_logo": team_b_logo,
                        "percentage": "{0:.2f}".format(0),
                    }
                )
        print(score_data)
        return score_data
