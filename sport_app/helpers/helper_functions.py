import secrets
import string
from wyse_ussd.helper.decisioning.game import Game


def play_soccer_prediction_game(game_model, win_data):
    game = Game()
    result = game.run(game_model, win_data)

    # print(result)
    return result

def generate_link_id():
    from sport_app.models import SoccerPredictionLink
    alphabet = string.ascii_letters + string.digits
    loop_condition = True
    while loop_condition:
        link_id = "".join(secrets.choice(alphabet) for i in range(8))
        if (
            any(c.isupper() for c in link_id)
            and any(c.isupper() for c in link_id)
            and sum(c.isdigit() for c in link_id) >= 6
        ):
            loop_condition = False
    
    if SoccerPredictionLink.objects.filter(link_id=link_id).exists():
        generate_link_id()

    return link_id