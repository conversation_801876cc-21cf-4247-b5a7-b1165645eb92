import json
from datetime import datetime, timedelta
from itertools import groupby
from operator import itemgetter
from pathlib import Path

import requests
from django.conf import settings

# import datetime
from django.db.models import Q
from django.utils import timezone

from sport_app.models import (
    ChampionsLeagueWeekHistory,
    EnglishWeekHistory,
    EuropaLeagueWeekHistory,
    FootballTable,
    FootballTeamShortCode,
    ImportantTeams,
    LaligaWeekHistory,
    WorldCupWeekHistory,
)
from wyse_ussd.models import SoccerPrediction


class TeamShortCode:
    def short_code(id):
        url = f"https://api-football-v1.p.rapidapi.com/v2/teams/league/{id}"

        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }

        response = requests.request("GET", url, headers=headers)
        resp = response.json()
        if resp.get("api")["results"] == 0:
            data = {"message": "wrong league id"}
            return data
        else:
            teams = resp.get("api")["teams"]
            for t in teams:
                team_id = t["team_id"]
                team_name = t["name"]
                team_code = t["code"]
                get_team_id = FootballTeamShortCode.objects.filter(team_id=team_id).last()
                if get_team_id:
                    pass
                else:
                    FootballTeamShortCode.objects.create(team_id=team_id, team_name=team_name, team_code=team_code)
            data = {"message": "done"}
            return data

    def available_leagues(league_country, year):
        url = f"https://api-football-v1.p.rapidapi.com/v2/leagues/country/{league_country}/{year}"
        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }
        response = requests.request("GET", url, headers=headers)
        return response.json()

    # def league_fixture(id):
    #     print("The ID of the selected is", id)
    #     def league_date():
    #         if id == "140":
    #             a_week = LaligaWeekHistory.objects.last()
    #             return a_week
    #         if id == "1":
    #             a_week = WorldCupWeekHistory.objects.last()
    #             return a_week
    #         if id == "39":
    #             a_week = EnglishWeekHistory.objects.last()
    #             return a_week
    #         if id == "2":
    #             a_week = ChampionsLeagueWeekHistory.objects.last()
    #             return a_week
    #         if id == "3":
    #             a_week = EuropaLeagueWeekHistory.objects.last()
    #             return a_week
    #     a_week = league_date()
    #     start_date = a_week.week_start_date
    #     end_date = a_week.week_end_date + timedelta(days=1)

    #     print(start_date, end_date,"START TO FINISH ::::::::")
    #     content = ImportantTeams.objects.all()
    #     content_list = [x.team_id for x in content]
    #     # print("The list is: ", content_list)
    #     first_data = []
    #     second_data =[]
    #     # now = timezone.now()
    #     now = datetime.now().date()
    #     print(now, id, "NOW:::::::::::::")
    #     # fetch_match = FootballTable.objects.filter(fixture_date__gte=start_date, fixture_date__lte=end_date, league_id=id).order_by("-fixture_date")
    #     # print(start_date, end_date, list(fetch_match.filter(fixture_date__gte=start_date, fixture_date__lte=end_date, league_id=id)))
    #     # fetch_match = FootballTable.objects.filter(fixture_date__gte=start_date, fixture_date__lte=end_date, league_id=id).order_by("-fixture_date")
    #     # fetch_match = FootballTable.objects.filter(Q(fixture_date__range=(start_date, end_date), league_id=id)& Q(fixture_date__gte=now) ).order_by("-fixture_date")
    #     fetch_match = FootballTable.objects.filter(Q(fixture_date__range=(start_date, end_date), league_id=id)& Q(fixture_date__date=now) ).order_by("fixture_date")
    #     print(fetch_match, "FETCH MATCH :::::")
    #     for _week in fetch_match:
    #         year = _week.fixture_date.year
    #         month = _week.fixture_date.month
    #         day = _week.fixture_date.day
    #         fixture_day_date = f"{year}-{month}-{day}"

    #         #fetch fixture data
    #         content = json.loads((Path(__file__).parent / 'scores.json').read_text())

    #         get_data = SoccerPrediction.objects.filter(game_fixture_id=_week.fixture_id)
    #         for data in get_data:
    #             if f"{data.home_choice} : {data.away_choice}" in content.keys():
    #                 content[f"{data.home_choice} : {data.away_choice}"]=int(content[f"{data.home_choice} : {data.away_choice}"]+1)
    #             else:
    #                 content.update({f"{data.home_choice} : {data.away_choice}":int(1)})
    #         score_sum = []
    #         score_data = []
    #         per_sum = []
    #         for unit_score in content:
    #             score_sum.append(content[unit_score])
    #         total_score = sum(score_sum)
    #         for score in content:
    #             try:
    #                 percentage_prediction = (content[score]/total_score)*100
    #                 score_data.append(
    #                     {
    #                         "scores":score,
    #                         "percentage":'{0:.2f}'.format(percentage_prediction)
    #                     }
    #                 )
    #             except ZeroDivisionError:
    #                 score_data.append(
    #                     {
    #                         "scores":score,
    #                         "percentage":'{0:.2f}'.format(0)
    #                     }
    #                 )

    #         print("Total Score is ",total_score, "\n")
    #         print("User fixture Count", content, "\n")
    #         print("Score Data Percentage", score_data)

    #         # end of fixture data

    #         w_data = {
    #             "fixture_id": _week.fixture_id,
    #             "home_team": _week.home_team,
    #             "away_team": _week.away_team,
    #             "home_id": _week.home_id,
    #             "away_id": _week.away_id,
    #             "home_logo": _week.home_logo,
    #             "away_logo": _week.away_logo,
    #             "fixture_date": _week.fixture_date,
    #             "league_type": _week.league_type,
    #             "league_id": _week.league_id,
    #             "freemium" : _week.freemium,
    #             "fixture_day_date": fixture_day_date,
    #             "score_data": score_data,
    #         }
    #         if _week.home_id in content_list or _week.away_id in content_list:
    #             if w_data not in first_data:
    #                 first_data.append(w_data)
    #             else:
    #                 pass
    #         else:
    #             if w_data not in second_data:
    #                 second_data.append(w_data)
    #             else:
    #                 pass
    #     def myFunc(e):
    #         return e['fixture_date']

    #     fixtures = []
    #     second_data.sort(key=myFunc, reverse=False)
    #     for key, value in groupby(second_data,
    #                       key = itemgetter('fixture_day_date')):

    #         fixtures_data = []
    #         print(key)
    #         for k in value:
    #             print(k)
    #             fixtures_data.append(k)
    #         fix = {
    #             "date": key,
    #             "fixtures": fixtures_data
    #         }
    #         fixtures.append(fix)
    #     data = {
    #         "important_matches": first_data,
    #         "other_matches": fixtures,
    #     }
    #     return data

    def league_fixture(id):
        print("The ID of the selected is", id)

        def league_date():
            if id == "140":
                a_week = LaligaWeekHistory.objects.last()
                return a_week
            if id == "1":
                a_week = WorldCupWeekHistory.objects.last()
                return a_week
            if id == "39":
                a_week = EnglishWeekHistory.objects.last()
                return a_week
            if id == "2":
                a_week = ChampionsLeagueWeekHistory.objects.last()
                return a_week
            if id == "3":
                a_week = EuropaLeagueWeekHistory.objects.last()
                return a_week

        a_week = league_date()
        start_date = a_week.week_start_date
        end_date = a_week.week_end_date + timedelta(days=1)

        print(start_date, end_date, "START TO FINISH ::::::::")
        content = ImportantTeams.objects.all()
        content_list = [x.team_id for x in content]
        # print("The list is: ", content_list)
        first_data = []
        second_data = []
        now = timezone.now()
        # now = datetime.now().date()
        print(now, id, "NOW:::::::::::::")
        # fetch_match = FootballTable.objects.filter(fixture_date__gte=start_date, fixture_date__lte=end_date, league_id=id).order_by("-fixture_date")
        # print(start_date, end_date, list(fetch_match.filter(fixture_date__gte=start_date, fixture_date__lte=end_date, league_id=id)))
        # fetch_match = FootballTable.objects.filter(fixture_date__gte=start_date, fixture_date__lte=end_date, league_id=id).order_by("-fixture_date")
        # fetch_match = FootballTable.objects.filter(Q(fixture_date__range=(start_date, end_date), league_id=id)& Q(fixture_date__gte=now) ).order_by("-fixture_date")
        fetch_match = FootballTable.objects.filter(Q(fixture_date__range=(start_date, end_date), league_id=id)).order_by("fixture_date")
        print(fetch_match, "FETCH MATCH :::::")
        for _week in fetch_match:
            year = _week.fixture_date.year
            month = _week.fixture_date.month
            day = _week.fixture_date.day
            fixture_day_date = f"{year}-{month}-{day}"

            # fetch fixture data
            content = json.loads((Path(__file__).parent / "scores.json").read_text())

            get_data = SoccerPrediction.objects.filter(game_fixture_id=_week.fixture_id)
            for data in get_data:
                if f"{data.home_choice} : {data.away_choice}" in content.keys():
                    content[f"{data.home_choice} : {data.away_choice}"] = int(content[f"{data.home_choice} : {data.away_choice}"] + 1)
                else:
                    content.update({f"{data.home_choice} : {data.away_choice}": int(1)})
            score_sum = []
            score_data = []
            for unit_score in content:
                score_sum.append(content[unit_score])
            total_score = sum(score_sum)
            for score in content:
                try:
                    percentage_prediction = (content[score] / total_score) * 100
                    score_data.append(
                        {
                            "scores": score,
                            "percentage": "{0:.2f}".format(percentage_prediction),
                        }
                    )
                except ZeroDivisionError:
                    score_data.append({"scores": score, "percentage": "{0:.2f}".format(0)})

            print("Total Score is ", total_score, "\n")
            print("User fixture Count", content, "\n")
            print("Score Data Percentage", score_data)

            # end of fixture data
            fixture_available_date = _week.fixture_date + timedelta(hours=2)
            w_data = {
                "fixture_id": _week.fixture_id,
                "home_team": _week.home_team,
                "away_team": _week.away_team,
                "home_id": _week.home_id,
                "away_id": _week.away_id,
                "home_logo": _week.home_logo,
                "away_logo": _week.away_logo,
                "fixture_date": _week.fixture_date,
                "league_type": _week.league_type,
                "league_id": _week.league_id,
                "freemium": _week.freemium,
                "fixture_day_date": fixture_day_date,
                "fixture_available_date": fixture_available_date,
                "score_data": score_data,
            }
            if _week.home_id in content_list or _week.away_id in content_list:
                if w_data not in first_data:
                    if _week.fixture_date > now:
                        w_data.update({"STARTED": False})
                        first_data.append(w_data)
                    elif fixture_available_date > now:
                        w_data.update({"STARTED": True})
                        first_data.append(w_data)
                    else:
                        pass
                else:
                    pass
            else:
                if w_data not in second_data:
                    if _week.fixture_date > now:
                        w_data.update({"STARTED": False})
                        second_data.append(w_data)
                    elif fixture_available_date > now:
                        w_data.update({"STARTED": True})
                        second_data.append(w_data)
                    else:
                        pass

                else:
                    pass

        def myFunc(e):
            return e["fixture_date"]

        fixtures = []
        second_data.sort(key=myFunc, reverse=False)
        for key, value in groupby(second_data, key=itemgetter("fixture_day_date")):
            fixtures_data = []
            print(key)
            for k in value:
                print(k)
                fixtures_data.append(k)
            fix = {"date": key, "fixtures": fixtures_data}
            fixtures.append(fix)
        data = {
            "important_matches": first_data,
            "other_matches": fixtures,
        }
        return data

    def score_data(league_id, fixture_id=None):
        print(league_id, fixture_id, "A WEEK:::::::::")

        def league_date():
            if league_id == "140":
                a_week = LaligaWeekHistory.objects.last()
                return a_week
            if league_id == "1":
                a_week = WorldCupWeekHistory.objects.last()
                return a_week
            if league_id == "39":
                a_week = EnglishWeekHistory.objects.last()
                return a_week
            if league_id == "2":
                a_week = ChampionsLeagueWeekHistory.objects.last()
                return a_week
            if league_id == "3":
                a_week = EuropaLeagueWeekHistory.objects.last()
                return a_week

            a_week = EuropaLeagueWeekHistory.objects.last()
            return a_week

        a_week = league_date()
        print(a_week, "A WEEK:::::::::")
        start_date = a_week.week_start_date
        end_date = a_week.week_end_date + timedelta(days=1)

        first_data = []
        second_data = []
        now = timezone.now()
        print(now, id, "NOW:::::::::::::")
        # fetch_match = FootballTable.objects.filter(fixture_date__gte=start_date, fixture_date__lte=end_date, league_id=id).order_by("-fixture_date")
        # print(start_date, end_date, list(fetch_match.filter(fixture_date__gte=start_date, fixture_date__lte=end_date, league_id=id)))
        # fetch_match = FootballTable.objects.filter(fixture_date__gte=start_date, fixture_date__lte=end_date, league_id=id).order_by("-fixture_date")
        fetch_match = FootballTable.objects.filter(
            Q(fixture_date__range=(start_date, end_date), league_id=league_id) & Q(fixture_date__gte=now)
        ).order_by("fixture_date")
        print(fetch_match, "FETCH MATCH :::::")
        for _week in fetch_match:
            year = _week.fixture_date.year
            month = _week.fixture_date.month
            day = _week.fixture_date.day
            fixture_day_date = f"{year}-{month}-{day}"

            # fetch fixture data
            content = json.loads((Path(__file__).parent / "scores.json").read_text())

            get_data = SoccerPrediction.objects.filter(game_fixture_id=_week.fixture_id)
            for data in get_data:
                if f"{data.home_choice} : {data.away_choice}" in content.keys():
                    content[f"{data.home_choice} : {data.away_choice}"] = int(content[f"{data.home_choice} : {data.away_choice}"] + 1)
                else:
                    content.update({f"{data.home_choice} : {data.away_choice}": int(1)})
            score_sum = []
            score_data = []
            for unit_score in content:
                score_sum.append(content[unit_score])
            total_score = sum(score_sum)
            for score in content:
                try:
                    percentage_prediction = (content[score] / total_score) * 100
                    score_data.append(
                        {
                            "scores": score,
                            "percentage": "{0:.2f}".format(percentage_prediction),
                        }
                    )
                except ZeroDivisionError:
                    score_data.append({"scores": score, "percentage": "{0:.2f}".format(0)})

            print("Total Score is ", total_score, "\n")
            print("User fixture Count", content, "\n")
            print("Score Data Percentage", score_data)

            # end of fixture data

            w_data = {
                "fixture_id": _week.fixture_id,
                "home_team": _week.home_team,
                "away_team": _week.away_team,
                "home_id": _week.home_id,
                "away_id": _week.away_id,
                "home_logo": _week.home_logo,
                "away_logo": _week.away_logo,
                "fixture_date": _week.fixture_date,
                "league_type": _week.league_type,
                "league_id": _week.league_id,
                "freemium": _week.freemium,
                "fixture_day_date": fixture_day_date,
                "score_data": score_data,
            }
            if fixture_id == _week.fixture_id:
                if w_data not in first_data:
                    first_data.append(w_data)
                else:
                    pass
            else:
                if w_data not in second_data:
                    second_data.append(w_data)
                else:
                    pass

        all_data = [*first_data, *second_data]
        data = {
            "all_data": all_data,
        }
        return data

    def daily_matches():
        url = "https://api-football-v1.p.rapidapi.com/v3/predictions?fixture="
        # 1035128
        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }

        response = requests.request("GET", url, headers=headers)
        resp = response.json()
        print(resp)
        if "response" in resp:
            return resp.get("response")
        else:
            return None

    def prediction(fixture_id):
        url = f"https://api-football-v1.p.rapidapi.com/v3/predictions?fixture={fixture_id}"
        # 1035128
        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }

        response = requests.request("GET", url, headers=headers)
        resp = response.json()
        print(resp)
        if "response" in resp:
            return resp.get("response")
        else:
            return None
        
    def get_rapid_api_predictions(fixture_id):
        url = f"https://api-football-v1.p.rapidapi.com/v3/predictions?fixture={fixture_id}"
        # 1035128
        headers = {
            "X-RapidAPI-Key": f"{settings.FOOTBALL_API_KEY}",
            "X-RapidAPI-Host": "api-football-v1.p.rapidapi.com",
        }
        
        response = requests.request("GET", url, headers=headers)
        resp = response.json()
        data = resp["response"][0]
            
        return data