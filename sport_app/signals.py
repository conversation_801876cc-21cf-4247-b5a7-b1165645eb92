from django.contrib.auth import get_user_model
from django.db.models import Sum
from django.db.models.signals import post_save
from django.dispatch import receiver

from sport_app.models import FootballTable, SoccerOddPredictionTable
from wyse_ussd.models import SoccerPrediction

User = get_user_model()

soccer_multiplier = FootballTable.get_personal_soccer_multiplier()


@receiver(post_save, sender=SoccerPrediction)
def run_personal_soccer_play_type_draw(sender, instance, created, **kwargs):
    if created:
        pass

    else:
        if instance.score_checked is True and instance.is_drawn is False and instance.play_type == "PERSONAL":
            verified_predictions = SoccerPrediction.objects.filter(game_id=instance.game_id, score_checked=True)

            print("Verified Predictions Count :", verified_predictions.count())

            # check if all predictions are checked
            if verified_predictions.count() == 3:
                correct_prediction = verified_predictions.filter(is_score_valid=True)

                if correct_prediction.count() == 2:
                    # winning is 2/3
                    print("winning : 2/3")
                    # update potential winning

                    total_stake_amount = correct_prediction.aggregate(Sum("stake_amount"))["stake_amount__sum"]
                    # print("Total Stake Amount:", total_stake_amount)
                    amount_won = total_stake_amount * min(soccer_multiplier)
                    # print("Amount won:", amount_won)

                    SoccerPrediction.create_predictions_winners(amount_won=amount_won, prediction_ins=instance)
                    correct_prediction.update(is_drawn=True, won=True, potential_winning=amount_won / 2, active=False)

                elif correct_prediction.count() == 3:
                    # winning is 3/3
                    print("winning : 3/3")
                    # update potential winning

                    total_stake_amount = correct_prediction.aggregate(Sum("stake_amount"))["stake_amount__sum"]
                    amount_won = total_stake_amount * max(soccer_multiplier)

                    SoccerPrediction.create_predictions_winners(amount_won=amount_won, prediction_ins=instance)
                    verified_predictions.update(is_drawn=True, won=True, potential_winning=amount_won / 3, active=False)
                else:
                    # lost Ticket
                    verified_predictions.update(is_drawn=True, won=False, active=False)

                verified_predictions.update(is_drawn=True, active=False)

            else:
                pass

@receiver(post_save, sender=SoccerOddPredictionTable)
def subscription_allocation(sender, instance, created, **kwargs):
    if created:
        pass
    else:
        if instance.is_paid and not instance.is_subscription_allocated:
            if instance.duration_of_subscription_type =="DAILY":
                instance.duration_of_subscription = 1
                instance.subscription_days_left = 1
                instance.is_subscription_allocated = True
                instance.save()
            elif instance.duration_of_subscription_type =="WEEKLY":
                instance.duration_of_subscription = 7
                instance.subscription_days_left = 6
                instance.is_subscription_allocated = True
                instance.save()
            elif instance.duration_of_subscription_type =="MONTHLY":
                instance.instance.duration_of_subscription = 28
                instance.subscription_days_left = 27
                instance.is_subscription_allocated = True
                instance.save()
            user = User.objects.get(pk=instance.user_id)
            user.is_subscribed = True
            user.save()
