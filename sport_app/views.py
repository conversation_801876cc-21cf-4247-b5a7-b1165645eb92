# from rest_framework_simplejwt.authentication import J<PERSON><PERSON><PERSON>entication
import random
import uuid
from datetime import datetime, timedelta
from time import sleep
from django.utils import timezone

import pandas as pd
from django.db.models import Sum
from django.utils import timezone

# from datetime import datetime, timedelta
from django.utils.datastructures import MultiValueDictKeyError

# Create your views here.
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from account.authentication import (
    IsAgentRemittanceDuePermission,
    IsAgentSuspendedPermission,
    IsBlackListedPermission,
    PhoneNumberVerifedPermission,
)
from main.api.api_lottery_helpers import generate_game_play_id, generate_pin
from main.helpers.illution_feature import GameIllusion
from main.models import LotteryBatch, LotteryModel, LottoTicket, UserProfile
from main.ussd.helpers import Utility
from pos_app.models import Agent, LottoAgentRemittanceTable
from pos_app.pos_helpers import PosAgentHelper
from prices.game_price import VirtualSoccerPriceModel
from sport_app.helpers.champions_league_sport_api import LotteryFootballChampionsLeague

# from .serializers import CoralCustomerLookupSerializer, CoralVasPaymentSerializer, CoralVasBillerGroupIdSerializer, CoralVasBillerGroupSlugSerializer, CoralVasBillerSlugSerializer, CoralUSSDStringSerializer, CoralUSSDGetDetailSerializer, CoralTransactionEnquirySerializer, CoralUSSDWebLoginSerializer, CoralUSSDWebInvokeReferenceSerializer, CoralUSSDWebStatusQuerySerializer # noqa
from sport_app.helpers.english_sport_api import LotteryFootballPremierLeague
from sport_app.helpers.europa_league_sport_api import LotteryFootballEuropaLeague
from sport_app.helpers.laliga_sport_api import LotteryFootballLaliga
from sport_app.helpers.team_code import TeamShortCode
from sport_app.helpers.virtual_soccer_team_pairing import match_virtual_teams
from sport_app.helpers.world_cup_sport_api import LotteryFootballWorldCup
from sport_app.models import (
    CutoutTable,
    DualTeamFinalistPrediction,
    GoalScorer,
    GoalScorerPrediction,
    OddPredictionType,
    PredictionTable,
    SoccerOddPredictionTable,
    SoccerPrediction,
    SoccerPredictionLink,
    TeamFinalistPrediction,
    VirtualLeagues,
    VirtualMatches,
    VirtualTeams,
    WorldCupTeams,
    predict_match_score_web,
)
from sport_app.serializers import (
    CreatePredictionOddSerializer,
    EditPredictionOddSerializer,
    GetPredictOddSerializer,
    GoalScorerSerializer,
    PredictDualTeamFinalistSerializer,
    PredictGoalScorerSerializer,
    PredictionSubscriptionPaymentSerializer,
    PredictOddsSerializer,
    PredictTeamFinalistSerializer,
    SoccerCashGameSelectSerializer,
    VirtualPredictionMasterSerializer,
    VirtualSoccerResultSearchViewSerializer,
    WebSoccerPredictionSerializer,
    WorldCupTeamSerializer,
)
from wallet_app.helpers.payment_gateway import PaymentGateway
from wallet_app.models import PaystackTransaction, ServiceChargeWallet, UserWallet
from wyse_ussd.models import UssdLotteryPayment

# from rest_framework.permissions import IsAuthenticated
# from rest_framework.decorators import (permission_classes, authentication_classes, api_view, parser_classes, renderer_classes)
# from rest_framework.authentication import BasicAuthentication


class EPLWeekMatchAPIView(APIView):
    def get(self, request):
        try:
            get_prediction = CutoutTable.objects.get(prediction_name="pl_week")
            if get_prediction.is_on is True:
                week_match = LotteryFootballPremierLeague().week_match()
                return Response(week_match, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "endpoint not available"},
                    status=status.HTTP_200_OK,
                )
        except CutoutTable.DoesNotExist:
            return Response({"message": "there is no such game"}, status=status.HTTP_404_NOT_FOUND)


class LaligaWeekMatchAPIView(APIView):
    def get(self, request):
        try:
            get_prediction = CutoutTable.objects.get(prediction_name="laliga_week")
            if get_prediction.is_on is True:
                week_match = LotteryFootballLaliga().week_match()
                return Response(week_match, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "endpoint not available"},
                    status=status.HTTP_200_OK,
                )
        except CutoutTable.DoesNotExist:
            return Response({"message": "there is no such game"}, status=status.HTTP_404_NOT_FOUND)


class ChampionsLeagueWeekMatchAPIView(APIView):
    def get(self, request):
        try:
            get_prediction = CutoutTable.objects.get(prediction_name="champions_league_week")
            if get_prediction.is_on is True:
                week_match = LotteryFootballChampionsLeague().week_match()
                return Response(week_match, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "endpoint not available"},
                    status=status.HTTP_200_OK,
                )
        except CutoutTable.DoesNotExist:
            return Response({"message": "there is no such game"}, status=status.HTTP_404_NOT_FOUND)


class EuropaLeagueWeekMatchAPIView(APIView):
    def get(self, request):
        try:
            get_prediction = CutoutTable.objects.get(prediction_name="europa_league_week")
            if get_prediction.is_on is True:
                week_match = LotteryFootballEuropaLeague().week_match()
                return Response(week_match, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "endpoint not available"},
                    status=status.HTTP_200_OK,
                )
        except CutoutTable.DoesNotExist:
            return Response({"message": "there is no such game"}, status=status.HTTP_404_NOT_FOUND)


class WorldCupWeekMatchAPIView(APIView):
    def get(self, request):
        try:
            get_prediction = CutoutTable.objects.get(prediction_name="world_cup_week")
            if get_prediction.is_on is True:
                week_match = LotteryFootballWorldCup().week_match()
                return Response(week_match, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "endpoint not available"},
                    status=status.HTTP_200_OK,
                )
        except CutoutTable.DoesNotExist:
            return Response({"message": "there is no such game"}, status=status.HTTP_404_NOT_FOUND)


class TeamShortCodeAPIView(APIView):
    def get(self, request):
        try:
            league_id = request.query_params["league_id"]
            team_code = TeamShortCode.short_code(league_id)
            return Response(team_code, status=status.HTTP_200_OK)
        except MultiValueDictKeyError:
            return Response(
                {"param": "you must pass in the league_id"},
                status=status.HTTP_404_NOT_FOUND,
            )


class GetAvailableLeaguesAPIView(APIView):
    def get(self, request):
        try:
            get_prediction = CutoutTable.objects.get(prediction_name="available_leagues")
            if get_prediction.is_on is True:
                try:
                    league_country = request.query_params["league_country"]
                    year = request.query_params["year"]
                    available_league = TeamShortCode.available_leagues(league_country, year)
                    return Response(available_league, status=status.HTTP_200_OK)
                except MultiValueDictKeyError:
                    return Response(
                        {"param": "you must pass in the league_country and year"},
                        status=status.HTTP_404_NOT_FOUND,
                    )
            else:
                return Response(
                    {"message": "endpoint not available"},
                    status=status.HTTP_200_OK,
                )
        except CutoutTable.DoesNotExist:
            return Response({"message": "there is no such game"}, status=status.HTTP_404_NOT_FOUND)


class PremierLeagueAPIView(APIView):
    def get(self, request):
        premier_league = TeamShortCode.league_fixture("39")
        return Response(premier_league, status=status.HTTP_200_OK)


class LaligaAPIView(APIView):
    def get(self, request):
        laliga = TeamShortCode.league_fixture("140")
        return Response(laliga, status=status.HTTP_200_OK)


class ChampionsLeagueAPIView(APIView):
    def get(self, request):
        champions_league = TeamShortCode.league_fixture("2")
        return Response(champions_league, status=status.HTTP_200_OK)


class EuropaLeagueAPIView(APIView):
    def get(self, request):
        europa_league = TeamShortCode.league_fixture("3")
        return Response(europa_league, status=status.HTTP_200_OK)


class WorldCupAPIView(APIView):
    def get(self, request):
        world_cup = TeamShortCode.league_fixture("1")
        return Response(world_cup, status=status.HTTP_200_OK)


class ScoreDataAPIView(APIView):
    def get(self, request):
        try:
            league_id = request.query_params["league_id"]
            fixture_id = request.query_params["fixture_id"]
        except MultiValueDictKeyError:
            return Response(
                {"params": "you must pass in the league_id and fixture_id"},
                status=status.HTTP_404_NOT_FOUND,
            )

        data = TeamShortCode.score_data(league_id, fixture_id)
        return Response(data, status=status.HTTP_200_OK)


class SoccerCashGameSelect(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = [IsAuthenticated, PhoneNumberVerifedPermission]

    def post(self, request):
        serializer = SoccerCashGameSelectSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        game_play = serializer.validated_data.get("game_play")

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        # phone_no = ""

        if user_profile is None:
            data = {"message": "User not found"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        play_type = serializer.validated_data["play_type"]
        print(play_type)

        game_play = predict_match_score_web(user_profile.phone_number, game_play, play_type)
        # game_play = predict_match_score_web(phone_no, game_play)

        if game_play.get("succeeded") is True:
            return Response(game_play, status=status.HTTP_200_OK)
        else:
            return Response(game_play, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        id = request.query_params.get("id", None)
        if id is None:
            data = {"message": "Please provide the id of the game you want to delete"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            prediction = SoccerPrediction.objects.get(id=id)
            game_id = prediction.game_id

            lottery_payment = UssdLotteryPayment.objects.filter(game_play_id=game_id).last()

            lottery_payment.amount -= prediction.stake_amount
            lottery_payment.save()

            prediction.delete()

            prediction_query_set = SoccerPrediction.objects.filter(game_id=game_id).values()
            prediction_df = pd.DataFrame(prediction_query_set)

            try:
                fixtures = prediction_df["game_fixture_id"].unique()
            except KeyError:
                response_body = {
                    "succeeded": True,
                    "message": "Games deleted",
                    "game_play_id": None,
                    "game_summary": None,
                    "matches_played": 0,
                    "total_predictions": 0,
                    "total_stake_amount": 0,
                    "winning_amount": 0,
                }

            response_body = SoccerPrediction.soccer_cash_web_success_response(fixtures, game_id, user=None, method="DELETE")

            return Response(response_body, status=status.HTTP_200_OK)

        except SoccerPrediction.DoesNotExist:
            return Response(
                {"status": False, "message": "query object does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class GetGoalScorerApiView(APIView):
    # authentication_classes = (TokenAuthentication,)
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        query_set = GoalScorer.objects.all()
        serializer = GoalScorerSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class PredictGoalScorerApiView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = [IsAuthenticated, PhoneNumberVerifedPermission]

    def post(self, request):
        serializer = PredictGoalScorerSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        game_play = serializer.validated_data.get("game_play")

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        # phone_no = ""

        if user_profile is None:
            data = {"message": "User not found"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        game_play = GoalScorerPrediction.predict_goal_scorer_web(
            user_profile.phone_number,
            game_play,
            # phone_no,
            # game_play,
        )

        if game_play.get("succeeded") is True:
            return Response(game_play, status=status.HTTP_200_OK)
        else:
            return Response(game_play, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        id = request.query_params.get("id", None)
        if id is None:
            data = {"message": "Please provide the id of the game you want to delete"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            prediction = GoalScorerPrediction.objects.get(id=id)
            game_id = prediction.game_id

            lottery_payment = UssdLotteryPayment.objects.filter(game_play_id=game_id).last()

            lottery_payment.amount -= prediction.stake_amount
            lottery_payment.save()

            prediction.delete()

            prediction_query_set = GoalScorerPrediction.objects.filter(game_id=game_id).values()
            prediction_df = pd.DataFrame(prediction_query_set)
            # print(prediction_df)

            try:
                fixtures = prediction_df["goal_scorer_id"].unique()

            except KeyError:
                response_body = {
                    "succeeded": True,
                    "message": "Games deleted",
                    "game_play_id": None,
                    "game_summary": None,
                    "matches_played": 0,
                    "total_predictions": 0,
                    "total_stake_amount": 0,
                    "winning_amount": 0,
                }

            response_body = GoalScorerPrediction.goal_scorer_web_success_response(fixtures, game_id, user=None, method="DELETE")

            return Response(response_body, status=status.HTTP_200_OK)

        except GoalScorerPrediction.DoesNotExist:
            return Response(
                {"status": False, "message": "query object does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class GetPlayerGoalsAPIView(APIView):
    def get(self, request):
        try:
            player_id = request.query_params["player_id"]
        except MultiValueDictKeyError:
            return Response(
                {"param": "you must pass in the player_id"},
                status=status.HTTP_404_NOT_FOUND,
            )

        try:
            get_prediction = CutoutTable.objects.get(prediction_name="get_player_goals")
            if get_prediction.is_on is True:
                get_player_goal = LotteryFootballWorldCup.get_players_goal(player_id)
                return Response(get_player_goal, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "this game is currently not available"},
                    status=status.HTTP_200_OK,
                )
        except CutoutTable.DoesNotExist:
            return Response({"message": "there is no such game"}, status=status.HTTP_404_NOT_FOUND)


class GetAllPlayersAPIView(APIView):
    def get(self, request):
        try:
            get_prediction = CutoutTable.objects.get(prediction_name="get_player")
            if get_prediction.is_on is True:
                get_player = LotteryFootballWorldCup.get_players()
                return Response(get_player, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "this game is currently not available"},
                    status=status.HTTP_200_OK,
                )
        except CutoutTable.DoesNotExist:
            return Response({"message": "there is no such game"}, status=status.HTTP_404_NOT_FOUND)


class GetTeamFinalistAPIView(APIView):
    def get(self, request):
        try:
            get_prediction = CutoutTable.objects.get(prediction_name="get_finalist")
            if get_prediction.is_on is True:
                get_finalist = LotteryFootballWorldCup.get_team_finalist()
                return Response(get_finalist, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "this game is currently not available"},
                    status=status.HTTP_200_OK,
                )
        except CutoutTable.DoesNotExist:
            return Response({"message": "there is no such game"}, status=status.HTTP_404_NOT_FOUND)


class PredictTeamFinalistApiView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = [IsAuthenticated, PhoneNumberVerifedPermission]

    def post(self, request):
        serializer = PredictTeamFinalistSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        game_play = serializer.validated_data.get("game_play")

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        # phone_no = "2349029957463"

        if user_profile is None:
            data = {"message": "User not found"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        game_play = TeamFinalistPrediction.predict_team_finalist_web(
            user_profile.phone_number,
            game_play,
            # phone_no,
            # game_play,
        )

        if game_play.get("succeeded") is True:
            return Response(game_play, status=status.HTTP_200_OK)
        else:
            return Response(game_play, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        id = request.query_params.get("id", None)
        if id is None:
            data = {"message": "Please provide the id of the game you want to delete"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            prediction = TeamFinalistPrediction.objects.get(id=id)
            game_id = prediction.game_id

            lottery_payment = UssdLotteryPayment.objects.filter(game_play_id=game_id).last()

            lottery_payment.amount -= prediction.stake_amount
            lottery_payment.save()

            prediction.delete()

            prediction_query_set = TeamFinalistPrediction.objects.filter(game_id=game_id).values()
            prediction_df = pd.DataFrame(prediction_query_set)
            # print(prediction_df)

            try:
                fixtures = prediction_df["team_final_list_id"].unique()

            except KeyError:
                response_body = {
                    "succeeded": True,
                    "message": "Games deleted",
                    "game_play_id": None,
                    "game_summary": None,
                    "matches_played": 0,
                    "total_predictions": 0,
                    "total_stake_amount": 0,
                    "winning_amount": 0,
                }

            response_body = TeamFinalistPrediction.team_finalist_web_success_response(fixtures, game_id, user=None, method="DELETE")

            return Response(response_body, status=status.HTTP_200_OK)

        except TeamFinalistPrediction.DoesNotExist:
            return Response(
                {"status": False, "message": "query object does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class GetDualTeamFinalistAPIView(APIView):
    def get(self, request):
        try:
            get_prediction = CutoutTable.objects.get(prediction_name="get_dual_team_finalist")
            if get_prediction.is_on is True:
                get_dual_finalist = LotteryFootballWorldCup.get_dual_team_finalist()
                return Response(get_dual_finalist, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"message": "this game is currently not available"},
                    status=status.HTTP_200_OK,
                )
        except CutoutTable.DoesNotExist:
            return Response({"message": "there is no such game"}, status=status.HTTP_404_NOT_FOUND)


class WorldCupTeamsAPIView(APIView):
    # authentication_classes = (TokenAuthentication,)
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        query_set = WorldCupTeams.objects.all()
        serializer = WorldCupTeamSerializer(query_set, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class PredictDualTeamFinalistApiView(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = [IsAuthenticated, PhoneNumberVerifedPermission]

    def post(self, request):
        serializer = PredictDualTeamFinalistSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        game_play = serializer.validated_data.get("game_play")

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        # phone_no = ""

        if user_profile is None:
            data = {"message": "User not found"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        game_play = DualTeamFinalistPrediction.predict_dual_team_finalist_web(
            user_profile.phone_number,
            game_play,
            # phone_no,
            # game_play,
        )

        if game_play.get("succeeded") is True:
            return Response(game_play, status=status.HTTP_200_OK)
        else:
            return Response(game_play, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request):
        id = request.query_params.get("id", None)
        if id is None:
            data = {"message": "Please provide the id of the game you want to delete"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            prediction = DualTeamFinalistPrediction.objects.get(id=id)
            game_id = prediction.game_id

            lottery_payment = UssdLotteryPayment.objects.filter(game_play_id=game_id).last()

            lottery_payment.amount -= prediction.stake_amount
            lottery_payment.save()

            prediction.delete()

            prediction_query_set = DualTeamFinalistPrediction.objects.filter(game_id=game_id)

            if prediction_query_set.count() > 0:
                team_ids = []
                for prediction in prediction_query_set:
                    team_ids.append(
                        [
                            prediction.dual_team_finalist.team_a.id,
                            prediction.dual_team_finalist.team_b.id,
                        ]
                    )
                # print(team_ids)

                response_body = DualTeamFinalistPrediction.dual_team_finalist_web_success_response(team_ids, game_id, user=None, method="DELETE")

                return Response(response_body, status=status.HTTP_200_OK)
            else:
                response_body = {
                    "succeeded": True,
                    "message": "Games deleted",
                    "game_play_id": None,
                    "game_summary": None,
                    "matches_played": 0,
                    "total_predictions": 0,
                    "total_stake_amount": 0,
                    "winning_amount": 0,
                }
                return Response(response_body, status=status.HTTP_200_OK)

        except DualTeamFinalistPrediction.DoesNotExist:
            return Response(
                {"status": False, "message": "query object does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )


# ---------- VIRTUAL FOOTBALL POS CHANNEL ------------
class GetVirtualLeagues(APIView):
    permission_classes = [TokenAuthentication]

    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        query_set = VirtualLeagues.get_leagues()
        data = []
        if len(query_set) > 0:
            for league in query_set:
                item = {
                    "name": league.name,
                    "country": league.country,
                    "rapid_api_league_id": league.rapid_api_league_id,
                    "logo": league.logo,
                    "id": league.id,
                }

                if league.rapid_api_league_id == "39":
                    if len(data) > 0:
                        data.insert(0, item)
                    else:
                        data.append(item)

                elif league.rapid_api_league_id == "140":
                    if len(data) > 1:
                        data.insert(1, item)
                    else:
                        data.append(item)

                elif league.rapid_api_league_id == "135":
                    if len(data) > 2:
                        data.insert(2, item)
                    else:
                        data.append(item)

                else:
                    data.append(item)

        # serializer = VirtualLeagueSerializer(data)

        return Response(data, status=status.HTTP_200_OK)


class VirtualTeamsView(APIView):
    permission_classes = [TokenAuthentication]

    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
    ]

    serializer_class = VirtualPredictionMasterSerializer

    def get(self, request, channel):
        channels = ["mobile", "web"]
        league_id = request.query_params.get("league_id", None)
        if league_id is None:
            return Response(
                {"message": "Please provide a league id"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if str(channel).lower() not in channels:
            return Response(
                {"message": "Please provide a valid channel"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        query_set = VirtualTeams.get_teams_by_league_id(league_id)

        if len(query_set) == 0:
            return Response(
                {"message": "No teams found for this league"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        team_pairing = match_virtual_teams(query_set)

        return Response(team_pairing, status=status.HTTP_200_OK)

    def post(self, request, channel):
        channels = ["mobile", "web"]

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        if str(channel).lower() not in channels:
            return Response(
                {"message": "Please provide a valid channel"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if str(channel).lower() == "mobile":
            _channel = "POS"
        else:
            _channel = "WEB"

        # predictions = serializers.ListField(child=VirtualPredictionSerializer())
        predictions = serializer.validated_data.get("predictions")
        amount = serializer.validated_data.get("amount")
        transaction_pin = serializer.validated_data.get("pin")

        # payable_amount = [300, 600, 1200]

        virtual_soccer_price_model = VirtualSoccerPriceModel()
        price_model = virtual_soccer_price_model.get_ticket_price_details_with_stake_amount(channel=_channel, stake_amount=amount)

        if price_model is None:
            return Response(
                {"message": "Please provide a valid amount"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # return Response(
        #     {"message": "Please provide a valid amount"},
        #     status=status.HTTP_400_BAD_REQUEST,
        # )

        # if amount not in payable_amount:
        #     return Response(
        #         {"message": "Please provide a valid amount"},
        #         status=status.HTTP_400_BAD_REQUEST,
        #     )

        # validate instant cashout 2 line stake amount and match it with the amount
        # instant_cashout_two_line_stake_amount = LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
        #     2
        # ).get(
        #     "stake_amount"
        # )
        # if amount >= instant_cashout_two_line_stake_amount:
        #     pass
        # else:
        #     return Response(
        #         {"message": "Please provide a valid amount"},
        #         status=status.HTTP_400_BAD_REQUEST,
        #     )

        # if (
        #     price_model.get("ticket_price") + price_model.get("illusion_price", 0)
        #     >= 1000
        # ):
        #     excess_from_stake_amount = 200
        # else:
        #     excess_from_stake_amount = 100

        excess_from_stake_amount = price_model.get("illusion_price")

        number_of_lines = price_model.get("line_number")

        # if amount == 300:
        #     excess_from_stake_amount = 100
        #     number_of_lines = 1
        # elif amount == 600:
        #     excess_from_stake_amount = 100
        #     number_of_lines = 2
        # elif amount == 1200:
        #     excess_from_stake_amount = 200
        #     number_of_lines = 4

        # excess_from_stake_amount = amount - instant_cashout_two_line_stake_amount

        get_game_play_id = f"{generate_game_play_id()}-vs"
        identity_id = identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"
        pin = generate_pin()

        if str(channel).lower() == "web":  # ================== WEB CHANNEL ==================
            # get user profile

            user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()
            if user_profile is None:
                user_profile = UserProfile.objects.filter(email=request.user.email).last()
                if user_profile is None:
                    return Response(
                        {"message": "User not found"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # if user_profile.pin is None:
            #     return Response(
            #         {"message": "Please set your pin"},
            #         status=status.HTTP_400_BAD_REQUEST,
            #     )

            # validate user transaction pin
            # if check_password(pin, user_profile.pin) is False:
            #     data = {"status": status.HTTP_400_BAD_REQUEST, "message": "Wrong pin"}
            #     return Response(data, status=status.HTTP_400_BAD_REQUEST)

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                UserWallet.objects.create(user=user_profile, wallet_tag="WEB")
                return Response(
                    {"message": "Insufficient funds"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if user_wallet.game_available_balance < amount:
                return Response(
                    {"message": "Insufficient funds"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            print(
                f"""
            VIRUTAL SOCCER GAME PLAY
            user_wallet.game_available_balance: {user_wallet.game_available_balance}
            \n\n\n\n
            """
            )

            # user_wallet.game_available_balance -= amount
            # user_wallet.transaction_from = "VIRTUAL_SOCCER_GAME_PLAY"
            # user_wallet.save()

            wallet_payload = {"transaction_from": "VIRTUAL_SOCCER_GAME_PLAY"}

            UserWallet.deduct_wallet(
                user=user_wallet.user,
                amount=amount,
                channel="WEB",
                transaction_id=identity_id,
                user_wallet_type="GAME_PLAY_WALLET",
                **wallet_payload,
            )

            # print(
            #     f"""
            # AFTER DEDUCTION
            # user_wallet.game_available_balance: {user_wallet.game_available_balance}
            # \n\n\n\n
            # """
            # )

            africastalking_service_charge = price_model.get("africastalking_charge", 0)
            woven_service_charge = price_model.get("woven_service_charge", 0)

            if africastalking_service_charge > 0:
                ServiceChargeWallet().add_fund(amount=africastalking_service_charge, service="AFRICASTALKING")

            if woven_service_charge > 0:
                ServiceChargeWallet().add_fund(amount=woven_service_charge, service="WOVEN")

            if excess_from_stake_amount > 0:
                illusion_helper = GameIllusion()
                illusion_helper.set_excess_amount(amount=excess_from_stake_amount, game_type="VIRTUAL_SOCCER")

            team = VirtualTeams.objects.filter(id=predictions[0]["home_team_id"]).last()
            league = VirtualLeagues.objects.filter(id=team.league.id).last()
            # predictions = serializers.ListField(child=VirtualPredictionSerializer())

            virtual_match_queryset = []
            for prediction in predictions:
                home_team_instance = VirtualTeams.objects.filter(id=prediction["home_team_id"]).last()
                away_team_instance = VirtualTeams.objects.filter(id=prediction["away_team_id"]).last()

                virtual_match_queryset.append(
                    VirtualMatches(
                        user_profile=user_profile,
                        game_play_id=get_game_play_id,
                        home_team=home_team_instance,
                        away_team=away_team_instance,
                        league=league,
                        home_team_score=prediction["home_team_prediction"],
                        away_team_score=prediction["away_team_prediction"],
                        paid=True,
                        channel="WEB",
                    )
                )

            created_objects = VirtualMatches.objects.bulk_create(virtual_match_queryset)
            # print(
            #     f"""
            # ================== VIRTUAL SOCCER MATCHES CREATED ==================
            # \n\n\n\n\n
            # """
            # )

            # registering the game on lotto ticket table as VIRTUAL_SOCCER ticket

            # get instant cashout active batch
            instant_cashout_batch = LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT", is_active=True).last()
            if instant_cashout_batch is None:
                instant_cashout_batch = LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT", is_active=True)

            """
            THIS VIRTUAL SOCCER CASH IS BASICALLY INSTANT CASHOUT TWO LINE TICKET
            SO, WHEN REGISTERING IT, WE'LL MARK ONLY TWO TICKET AS PAID AND THE OTHER THREE AS NOT PAID.
            EACH LINE TO BE 200 NAIRA
            """  # noqa

            paid_recorded = 0

            for _i in range(0, number_of_lines):
                for _prediction in predictions:
                    prediction = _prediction

                amount_paid = (
                    LottoTicket.get_icash_ticket_price_without_illusion_price(number_of_line=number_of_lines).get("stake_amount") / number_of_lines
                )
                stake_amount = (
                    LottoTicket.get_icash_ticket_price_without_illusion_price(number_of_line=number_of_lines).get("stake_amount") / number_of_lines
                )

                LottoTicket.objects.create(
                    user_profile=user_profile,
                    batch=instant_cashout_batch,
                    phone=user_profile.phone_number,
                    stake_amount=stake_amount,
                    expected_amount=stake_amount,
                    potential_winning=LottoTicket.get_icash_ticket_price_without_illusion_price(number_of_line=number_of_lines).get("winning_amount"),
                    number_of_ticket=number_of_lines,
                    channel="WEB",
                    game_play_id=get_game_play_id,
                    lottery_type="VIRTUAL_SOCCER",
                    ticket=VirtualMatches.generate_lotto_number(
                        [
                            prediction["home_team_prediction"],
                            prediction["away_team_prediction"],
                        ]
                    ),
                    pin=pin,
                    amount_paid=amount_paid if paid_recorded < number_of_lines else 0,
                    paid=True if paid_recorded < number_of_lines else False,
                    identity_id=identity_id,
                )

                paid_recorded += 1

            for obj in created_objects:
                obj.refresh_from_db()

                ticket_data.append(  # noqa
                    {
                        "home_team": obj.home_team.name,
                        "home_team_code": obj.home_team.name_code,
                        "away_team": obj.away_team.name,
                        "away_team_code": obj.away_team.name_code,
                        "home_team_score": obj.home_team_score,
                        "away_team_score": obj.away_team_score,
                        "home_team_logo": obj.home_team.logo,
                        "away_team_logo": obj.away_team.logo,
                    }
                )  # noqa

            data = [{"game_play_id": get_game_play_id, "data": ticket_data}]  # noqa

            # update excess amount to excess wallet float
            # if excess_from_stake_amount > 0:
            #     illusion_helper = GameIllusion()
            #     illusion_helper.set_excess_amount(amount = excess_from_stake_amount, game_type = "VIRTUAL_SOCCER")

            # VirtualSoccerExcessWallet.update_virtual_soccer_excess_wallet_amount(
            #     amount=excess_from_stake_amount, phone_number=request.user.phone
            # )

            # RECEIPT #
            print(
                f"""
            ================== VIRTUAL SOCCER RECEIPT ==================
            {data}
            \n\n\n\n\n
            """
            )
            return Response(data, status=status.HTTP_200_OK)
        elif str(channel).lower() == "mobile":  # ================== MOBILE CHANNEL ==================
            # get agent profile
            agent_profile = Agent.objects.filter(phone=request.user.phone).last()
            if agent_profile is None:
                agent_profile = Agent.objects.filter(email=request.user.email).last()
                if agent_profile is None:
                    return Response(
                        {"message": "Agent not found"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            """  
            THIS RESPONSE WHY THIS SCRIPT IS TRYING  TO GET USER PROFILE AGAIN IS BECAUSE ALL
            USER GOES TO USER PROFILE AND BECAUSE OF THAT 'VirtualMatches' HAS  FOREIGN KEY TO USER PROFILE
            """  # noqa
            # get user profile
            user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()
            if user_profile is None:
                user_profile = UserProfile.objects.filter(email=request.user.email).last()
                if user_profile is None:
                    return Response(
                        {"message": "User not found"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # ------------------------------ VALIDATE AGENT LIBERTY PAY TRANSACTION PIN ------------------------------ #

            pos_agent_helper = PosAgentHelper(agent_instance=agent_profile, amount=0)

            if pos_agent_helper.verify_agent_transaction_pin(transaction_pin) is False:
                data = {
                    "message": "Invalid transaction pin",
                }

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            # ------------------------------ END VALIDATE AGENT LIBERTY PAY TRANSACTION PIN ------------------------------ #

            # validate remiitance amount
            def validate_remtance_amount(amount):
                response = LottoAgentRemittanceTable().valuate_amount_to_be_added(amount=amount, agent=agent_profile)
                if response.get("is_amount_much") is False:
                    return False, None
                else:
                    return True, response.get("amount")

            (
                validate_amount_in_remiitance_status,
                validate_amount_in_remiitance_status_amount,
            ) = validate_remtance_amount(amount)
            if validate_amount_in_remiitance_status is True:
                data = {
                    "message": f"Hi, please, fund your wallet with this amount {Utility.currency_formatter(validate_amount_in_remiitance_status_amount)} to continue game play",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            # handle agent wallet charge
            pos_agent_helper = PosAgentHelper(
                agent_instance=agent_profile,
                amount=amount,
                pin=pin,
            )

            # get_game_play_id = f"{generate_game_play_id()}-vs"

            charge_response = pos_agent_helper.handle_agent_charge_and_lottery_play(
                lottery_qs=None,
                return_after_successfull_charge=True,
                _game_play_id=get_game_play_id,
                lottery_type="VIRTUAL_SOCCER",
            )

            if charge_response == "success":
                if excess_from_stake_amount > 0:
                    illusion_helper = GameIllusion()
                    illusion_helper.set_excess_amount(amount=excess_from_stake_amount, game_type="VIRTUAL_SOCCER")

                # pin = generate_pin()

                team = VirtualTeams.objects.filter(id=predictions[0]["home_team_id"]).last()

                if team is None:
                    return Response(
                        {"message": "Team not found"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                league = VirtualLeagues.objects.filter(id=team.league.id).last()

                virtual_match_queryset = []
                for prediction in predictions:
                    home_team_instance = VirtualTeams.objects.filter(id=prediction["home_team_id"]).last()
                    away_team_instance = VirtualTeams.objects.filter(id=prediction["away_team_id"]).last()

                    virtual_match_queryset.append(
                        VirtualMatches(
                            user_profile=user_profile,
                            game_play_id=get_game_play_id,
                            home_team=home_team_instance,
                            away_team=away_team_instance,
                            league=league,
                            home_team_score=prediction["home_team_prediction"],
                            away_team_score=prediction["away_team_prediction"],
                            paid=True,
                            channel="MOBILE",
                        )
                    )

                created_objects = VirtualMatches.objects.bulk_create(virtual_match_queryset)

                instant_cashout_batch = LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT", is_active=True).last()
                if instant_cashout_batch is None:
                    instant_cashout_batch = LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT", is_active=True)

                paid_recorded = 0
                for _i in range(0, number_of_lines):
                    for _prediction in predictions:
                        prediction = _prediction

                    amount_paid = (
                        LottoTicket.get_icash_ticket_price_without_illusion_price(number_of_line=number_of_lines).get("stake_amount")
                        / number_of_lines
                    )
                    stake_amount = (
                        LottoTicket.get_icash_ticket_price_without_illusion_price(number_of_line=number_of_lines).get("stake_amount")
                        / number_of_lines
                    )

                    LottoTicket.objects.create(
                        user_profile=user_profile,
                        agent_profile=agent_profile,
                        batch=instant_cashout_batch,
                        phone=user_profile.phone_number,
                        stake_amount=stake_amount,
                        expected_amount=stake_amount,
                        potential_winning=LottoTicket.get_icash_ticket_price_without_illusion_price(number_of_line=number_of_lines).get(
                            "winning_amount"
                        ),
                        number_of_ticket=number_of_lines,
                        channel="POS_AGENT",
                        game_play_id=get_game_play_id,
                        lottery_type="VIRTUAL_SOCCER",
                        ticket=VirtualMatches.generate_lotto_number(
                            [
                                prediction["home_team_prediction"],
                                prediction["away_team_prediction"],
                            ]
                        ),
                        pin=pin,
                        amount_paid=amount_paid if paid_recorded < number_of_lines else 0,
                        paid=True if paid_recorded < number_of_lines else False,
                        identity_id=identity_id,
                    )

                    paid_recorded += 1

                ticket_data = []
                for obj in created_objects:
                    obj.refresh_from_db()

                    ticket_data.append(
                        {
                            "home_team": obj.home_team.name,
                            "home_team_code": obj.home_team.name_code,
                            "away_team": obj.away_team.name,
                            "away_team_code": obj.away_team.name_code,
                            "home_team_score": obj.home_team_score,
                            "away_team_score": obj.away_team_score,
                            "home_team_logo": obj.home_team.logo,
                            "away_team_logo": obj.away_team.logo,
                        }
                    )

                data = [{"game_play_id": get_game_play_id, "data": ticket_data}]

                # RECEIPT #

                mobile_receipt = []
                won_and_lost_matches_data = []

                virtual_matches = VirtualMatches.objects.filter(game_play_id=get_game_play_id)
                won_matches = virtual_matches.filter(won=True)
                lost_matches = virtual_matches.filter(won=False)

                # check if the game won
                if won_matches.count() > 0:
                    _won_instance = won_matches.last()
                    lotto_tickets = LottoTicket.objects.filter(game_play_id=get_game_play_id, paid=True)
                    amount_paid_for_tickets = lotto_tickets.aggregate(Sum("amount_paid"))["amount_paid__sum"]

                    if amount_paid_for_tickets is None:
                        amount_paid_for_tickets = 0

                    if amount_paid_for_tickets > 0:
                        if amount_paid_for_tickets < 1000:
                            amount_paid_for_tickets += 100
                        else:
                            amount_paid_for_tickets += 200

                    perc = (_won_instance.amount_won / amount_paid_for_tickets) * 100

                    cashback_percentage = f"{perc:.2f}%" if perc > 0 else "0.00%"

                win_status = "won"
                if won_matches.count() > 0:
                    for _won_match in won_matches:
                        # # won_match_instance = won_matches.last()

                        # if _won_match.win_type == "CASHBACK":
                        #     win_status = "cashback"
                        #     data["cashback_amount"] = _won_match.amount_won
                        #     data["cashback_percentage"] = cashback_percentage
                        # elif _won_match.win_type == "DRAW":
                        #     win_status = "cashback"

                        won_and_lost_matches_data.append(
                            {
                                "home_team": _won_match.home_team.name,
                                "home_team_code": _won_match.home_team.name_code,
                                "away_team": _won_match.away_team.name,
                                "away_team_code": _won_match.away_team.name_code,
                                "home_team_score": _won_match.home_team_score,
                                "away_team_score": _won_match.away_team_score,
                                "home_team_result": _won_match.home_team_result,
                                "away_team_result": _won_match.away_team_result,
                                "home_team_logo": _won_match.home_team.logo,
                                "away_team_logo": _won_match.away_team.logo,
                                "won": _won_match.won,
                                "status": win_status,
                            }
                        )

                if lost_matches.count() > 0:
                    for _lost_match in lost_matches:
                        won_and_lost_matches_data.append(
                            {
                                "home_team": _lost_match.home_team.name,
                                "home_team_code": _lost_match.home_team.name_code,
                                "away_team": _lost_match.away_team.name,
                                "away_team_code": _lost_match.away_team.name_code,
                                "home_team_score": _lost_match.home_team_score,
                                "away_team_score": _lost_match.away_team_score,
                                "home_team_result": _lost_match.home_team_result,
                                "away_team_result": _lost_match.away_team_result,
                                "home_team_logo": _lost_match.home_team.logo,
                                "away_team_logo": _lost_match.away_team.logo,
                                "won": _lost_match.won,
                                "status": "lost",
                            }
                        )

                lotto_ticket = LottoTicket.objects.filter(game_play_id=get_game_play_id).last()
                pin = 0000 if lotto_ticket is None else lotto_ticket.pin

                win_status = "won"
                if won_matches.count() > 0:
                    won_match_instance = won_matches.last()
                    if won_match_instance.win_type == "CASHBACK":
                        win_status = f"{cashback_percentage} cashback"
                    elif won_match_instance.win_type == "DRAW":
                        win_status = f"{cashback_percentage} cashback"
                else:
                    win_status = "lost"

                random.shuffle(won_and_lost_matches_data)
                random.shuffle(won_and_lost_matches_data)
                random.shuffle(won_and_lost_matches_data)

                # print(
                #     f"""
                # win_status: {win_status}
                # \n\n\n\n\n
                # """
                # )

                if "cashback" in win_status:
                    mobile_receipt.append(
                        {
                            "status": win_status,
                            "won": True if won_matches.count() > 0 else False,
                            "agent_id": agent_profile.user_uuid,
                            "game_play_id": get_game_play_id,
                            "pin": pin,
                            "game_type": "VIRTUAL_SOCCER",
                            "win_type": f"{won_matches.count()}/{virtual_matches.count()}",
                            "user_phone": virtual_matches.last().user_profile.phone_number,
                            "cashback_amount": 0 if won_matches.count() == 0 else won_matches.last().amount_won,
                            # "won_matches": won_matches_data,
                            # "lost_matches": lost_matches_data,
                            "matches": won_and_lost_matches_data,
                        }
                    )

                else:
                    mobile_receipt.append(
                        {
                            "status": win_status,
                            "won": True if won_matches.count() > 0 else False,
                            "agent_id": agent_profile.user_uuid,
                            "game_play_id": get_game_play_id,
                            "pin": pin,
                            "game_type": "VIRTUAL_SOCCER",
                            "win_type": f"{won_matches.count()}/{virtual_matches.count()}",
                            "user_phone": virtual_matches.last().user_profile.phone_number,
                            "amount_won": 0 if won_matches.count() == 0 else won_matches.last().amount_won,
                            # "won_matches": won_matches_data,
                            # "lost_matches": lost_matches_data,
                            "matches": won_and_lost_matches_data,
                        }
                    )

                data.append(
                    {
                        "receipt": mobile_receipt,
                    }
                )

                return Response(data, status=status.HTTP_200_OK)

            else:
                data = {
                    "status": "failed",
                    "message": charge_response,
                }

                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        return Response(data, status=status.HTTP_200_OK)


class VirtualSoccerResultSearchView(APIView):
    permission_classes = [TokenAuthentication]

    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
    ]

    serializer_class = VirtualSoccerResultSearchViewSerializer

    def post(self, request, channel):
        channels = ["mobile", "web"]

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        game_play_id = serializer.validated_data.get("game_play_id")

        if str(channel).lower() not in channels:
            return Response(
                {"status": "failed", "message": "Invalid channel"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        virtual_team_ticket = VirtualMatches.objects.filter(game_play_id=game_play_id)

        # print("virtual_team_ticket", virtual_team_ticket)

        if not virtual_team_ticket.exists():
            return Response(
                {"status": "failed", "message": "Invalid game play id"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        won_tickets = virtual_team_ticket.filter(won=True)
        lost_tickets = virtual_team_ticket.filter(won=False)

        won_and_lost_matches_data = []

        virtual_team_ticket.first()

        if str(channel).lower() == "mobile":
            agent_profile = Agent.objects.filter(phone=request.user.phone).last()
            if agent_profile is None:
                agent_profile = Agent.objects.filter(email=request.user.email).last()
                if agent_profile is None:
                    return Response(
                        {"message": "Agent not found"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            lotto_ticket = LottoTicket.objects.filter(game_play_id=game_play_id).last()
            data = {
                "status": "pending_draw",
                "won": False,
                "agent_id": agent_profile.user_uuid,
                "game_play_id": game_play_id,
                "pin": lotto_ticket.pin,
                "game_type": "VIRTUAL_SOCCER",
                # "win_type": f"{won_tickets.count()} / {virtual_team_ticket.count()}",
                # "user_phone": virtual_match_instance.user_profile.phone_number,
                "amount_won": 0,
            }

            # check if the game won
            if won_tickets.count() > 0:
                _won_instance = won_tickets.last()
                lotto_tickets = LottoTicket.objects.filter(game_play_id=game_play_id, paid=True)
                amount_paid_for_tickets = lotto_tickets.aggregate(Sum("amount_paid"))["amount_paid__sum"]

                if amount_paid_for_tickets is None:
                    amount_paid_for_tickets = 0

                if amount_paid_for_tickets > 0:
                    if amount_paid_for_tickets < 1000:
                        amount_paid_for_tickets += 100
                    else:
                        amount_paid_for_tickets += 200

                perc = (_won_instance.amount_won / amount_paid_for_tickets) * 100

                cashback_percentage = f"{perc:.2f}%" if perc > 0 else "0.00%"

            data["status"] = "won"
            win_status = "won"
            if won_tickets.count() > 0:
                for _won_match in won_tickets:
                    data["won"] = True
                    won_match_instance = won_tickets.last()
                    data["amount_won"] = won_match_instance.amount_won

                    if _won_match.win_type == "CASHBACK":
                        data["status"] = "cashback"
                        data["cashback_amount"] = _won_match.amount_won
                        data["cashback_percentage"] = cashback_percentage
                        data["amount_won"] = 0

                        win_status = "cashback"

                    elif _won_match.win_type == "DRAW":
                        data["status"] = "cashback"
                        data["cashback_amount"] = _won_match.amount_won
                        data["cashback_percentage"] = cashback_percentage
                        data["amount_won"] = 0

                        win_status = "cashback"

                    won_and_lost_matches_data.append(
                        {
                            "home_team": _won_match.home_team.name,
                            "away_team": _won_match.away_team.name,
                            "home_team_score": _won_match.home_team_score,
                            "away_team_score": _won_match.away_team_score,
                            "home_team_result": _won_match.home_team_result,
                            "away_team_result": _won_match.away_team_result,
                            "home_team_logo": _won_match.home_team.logo,
                            "away_team_logo": _won_match.away_team.logo,
                            "won": _won_match.won,
                            "status": win_status,
                        }
                    )

            if lost_tickets.count() > 0:
                if data["won"] is True:
                    pass
                else:
                    data["status"] = "lost"
                    data["won"] = False

                for _lost_match in lost_tickets:
                    won_and_lost_matches_data.append(
                        {
                            "home_team": _lost_match.home_team.name,
                            "away_team": _lost_match.away_team.name,
                            "home_team_score": _lost_match.home_team_score,
                            "away_team_score": _lost_match.away_team_score,
                            "home_team_result": _lost_match.home_team_result,
                            "away_team_result": _lost_match.away_team_result,
                            "home_team_logo": _lost_match.home_team.logo,
                            "away_team_logo": _lost_match.away_team.logo,
                            "won": _lost_match.won,
                            "status": "lost",
                        }
                    )

            # if won_tickets.count() > 0:
            #     data["status"] = "won"
            #     data["won"] = True
            #     data["amount_won"] = won_tickets.last().amount_won

            #     for won_ticket in won_tickets:
            #         won_tickets_data.append(
            #             {
            #                 "home_team": won_ticket.home_team.name,
            #                 "away_team": won_ticket.away_team.name,
            #                 "home_team_score": won_ticket.home_team_score,
            #                 "away_team_score": won_ticket.away_team_score,
            #                 "home_team_result": won_ticket.home_team_result,
            #                 "away_team_result": won_ticket.away_team_result,
            #                 "home_team_logo": won_ticket.home_team.logo,
            #                 "away_team_logo": won_ticket.away_team.logo,
            #             }
            #         )

            # if lost_tickets.count() > 0:
            #     if won_tickets.count() > 0:
            #         pass
            #     else:
            #         data["status"] = "lost"
            #         data["won"] = False

            #     for lost_ticket in lost_tickets:
            #         lost_tickets_data.append(
            #             {
            #                 "home_team": lost_ticket.home_team.name,
            #                 "away_team": lost_ticket.away_team.name,
            #                 "home_team_score": lost_ticket.home_team_score,
            #                 "away_team_score": lost_ticket.away_team_score,
            #                 "home_team_result": lost_ticket.home_team_result,
            #                 "away_team_result": lost_ticket.away_team_result,
            #                 "home_team_logo": lost_ticket.home_team.logo,
            #                 "away_team_logo": lost_ticket.away_team.logo,
            #             }
            #         )

            # data["won_matches"] = won_tickets_data
            # data["lost_matches"] = lost_tickets_data

            random.shuffle(won_and_lost_matches_data)
            random.shuffle(won_and_lost_matches_data)

            data["matches"] = won_and_lost_matches_data

            return Response(data, status=status.HTTP_200_OK)

        else:
            user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

            if user_profile is None:
                user_profile = UserProfile.objects.filter(email=request.user.email).last()
                if user_profile is None:
                    return Response(
                        {"message": "User not found"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            lotto_ticket = LottoTicket.objects.filter(game_play_id=game_play_id).last()
            data = {
                "status": "pending_draw",
                "won": False,
                "game_play_id": game_play_id,
                "game_type": "VIRTUAL_SOCCER",
                "amount_won": 0,
            }

            for _match in virtual_team_ticket:
                data["status"] = "won"
                win_status = "won"

                if won_tickets.count() > 0:
                    data["won"] = True
                    won_match_instance = won_tickets.last()
                    data["amount_won"] = won_match_instance.amount_won

                    if won_match_instance.win_type == "CASHBACK":
                        data["status"] = "cashback"
                        win_status = "cashback"

                    elif won_match_instance.win_type == "DRAW":
                        data["status"] = "cashback"
                        win_status = "cashback"
                else:
                    win_status = "lost"
                    data["status"] = "lost"
                    data["won"] = False

                won_and_lost_matches_data.append(
                    {
                        "home_team": _match.home_team.name,
                        "away_team": _match.away_team.name,
                        "home_team_score": _match.home_team_score,
                        "away_team_score": _match.away_team_score,
                        "home_team_result": _match.home_team_result,
                        "away_team_result": _match.away_team_result,
                        "home_team_logo": _match.home_team.logo,
                        "away_team_logo": _match.away_team.logo,
                        "won": _match.won,
                        "status": _match.win_type,
                    }
                )

            # if won_tickets.count() > 0:
            #     data["status"] = "won"
            #     data["won"] = True
            #     data["amount_won"] = won_tickets.last().amount_won

            #     for won_ticket in won_tickets:
            #         won_tickets_data.append(
            #             {
            #                 "home_team": won_ticket.home_team.name,
            #                 "away_team": won_ticket.away_team.name,
            #                 "home_team_score": won_ticket.home_team_score,
            #                 "away_team_score": won_ticket.away_team_score,
            #                 "home_team_result": won_ticket.home_team_result,
            #                 "away_team_result": won_ticket.away_team_result,
            #                 "home_team_logo": won_ticket.home_team.logo,
            #                 "away_team_logo": won_ticket.away_team.logo,
            #             }
            #         )

            # if lost_tickets.count() > 0:
            #     if won_tickets.count() > 0:
            #         pass
            #     else:
            #         data["status"] = "lost"
            #         data["won"] = False

            #     for lost_ticket in lost_tickets:
            #         lost_tickets_data.append(
            #             {
            #                 "home_team": lost_ticket.home_team.name,
            #                 "away_team": lost_ticket.away_team.name,
            #                 "home_team_score": lost_ticket.home_team_score,
            #                 "away_team_score": lost_ticket.away_team_score,
            #                 "home_team_result": lost_ticket.home_team_result,
            #                 "away_team_result": lost_ticket.away_team_result,
            #                 "home_team_logo": lost_ticket.home_team.logo,
            #                 "away_team_logo": lost_ticket.away_team.logo,
            #             }
            #         )

            # data["won_matches"] = won_tickets_data
            # data["lost_matches"] = lost_tickets_data

            data["matches"] = won_and_lost_matches_data

            return Response(data, status=status.HTTP_200_OK)


class PredictionApiView(APIView):
    def get(self, request):
        fixture_id = request.query_params.get("fixture_id")
        if not fixture_id:
            return Response({"message": "fixture_id is required"}, status=status.HTTP_400_BAD_REQUEST)
        prediction = TeamShortCode.get_rapid_api_predictions(fixture_id)
        # prediction = TeamShortCode.prediction(fixture_id)
        # if len(prediction) < 1:
        #     return Response(prediction, status=status.HTTP_200_OK)
        # else:
        #     return Response({"message":"no prediction available"}, status=status.HTTP_200_OK)
        return Response(prediction, status=status.HTTP_200_OK)


class PredictOddTypeAPIView(APIView):
    def get(self, request):
        all_prediction_type = OddPredictionType.objects.filter(is_deleted=False).order_by("-date_created")
        serializer = GetPredictOddSerializer(all_prediction_type, many=True)
        response = {"status": "success", "data": serializer.data}
        return Response(response, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = CreatePredictionOddSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        subscription_name = serializer.validated_data.get("subscription_name")
        response = {"status": "success", "message": f"{subscription_name} has been created successfully"}
        return Response(response, status=status.HTTP_200_OK)

    def put(self, request):
        subscription_id = request.query_params.get("subscription_id")
        get_subscription = OddPredictionType.objects.filter(id=subscription_id, is_active=True).first()
        if not get_subscription:
            return Response({"message": f"{subscription_id} does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        serializer = EditPredictionOddSerializer(get_subscription, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        response = {"status": "success", "message": "subscription edited successfully"}
        return Response(response, status=status.HTTP_200_OK)

    def delete(self, request):
        subscription_id = request.query_params.get("subscription_id")
        get_subscription = OddPredictionType.objects.filter(id=subscription_id, is_deleted=False).first()
        if not get_subscription:
            return Response({"message": f"{subscription_id} does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        get_subscription.is_active = False
        get_subscription.is_deleted = True
        get_subscription.save()
        response = {"status": "success", "message": "subscription deleted successfully"}
        return Response(response, status=status.HTTP_200_OK)


class PredictOddAPIView(APIView):
    def post(self, request):
        serializer = PredictOddsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.validated_data, status=status.HTTP_200_OK)


class PayForPredictionSubscription(APIView):
    serializer_class = PredictionSubscriptionPaymentSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data.get("phone_number")
        amount = serializer.validated_data.get("amount")
        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        amount = serializer.validated_data.get("amount")

        user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
        if user_profile is None:
            user_profile = UserProfile.objects.create(phone_number=phone_number)

        transaction_ref = transaction_ref = f"LTT-PAY{uuid.uuid4()}-web_ussd"

        paystack_payload = {
            "email": "<EMAIL>",
            "amount": float(amount) * 100,
            "currency": "NGN",
            "reference": transaction_ref,
        }

        # create transaction
        PaystackTransaction.objects.create(
            user=user_profile,
            amount=float(amount),
            reference=transaction_ref,
            created_at=timezone.now(),
            paid_at=timezone.now(),
            channel="USSD",
            raw_data=paystack_payload,
            payment_reason="LOTTERY_PAYMENT",
        )

        # initiate payment
        paystack_api = PaymentGateway().paystack_link_request(**paystack_payload)

        payment_link = paystack_api.get("authorization_url")

        data = {
            "message": "success",
            "payment_link": payment_link,
        }

        return Response(data, status=status.HTTP_200_OK)
    
class WebPredictionLinkAPIView(APIView):
    def get(self, request):
        link_id = request.query_params.get("link_id")
        today_date = datetime.now().date()
        link_ins = SoccerPredictionLink.objects.filter(link_id=link_id, link_expiry_date__gte=today_date, is_free=False).first()
        if not link_ins:
            return Response({"success": False, "message": "failed"}, status=status.HTTP_403_FORBIDDEN)
        
        all_match = link_ins.prediction_matches
        response = {
            "success": True,
            "message": "success",
            "all_prediction": all_match,
        }
        return Response(response, status=status.HTTP_200_OK)
    
class UpcomingMatchesAPIView(APIView):
    def get(self, request):
        all_match = []
        today_date = datetime.now()
        all_predictions = PredictionTable.objects.filter(fixture_date__gt=today_date)
        predicted_random = all_predictions.values_list('id', flat=True)
        random_predicted_id = random.sample(list(predicted_random), min(len(predicted_random), 10))
        query_set = all_predictions.filter(id__in=random_predicted_id)
        for fixture in query_set:
            all_match.append({
                "home_team": fixture.home_team,
                "away_team": fixture.away_team,
                "fixture_date": fixture.fixture_date,
                "league_name": fixture.league_type,
                "fixture_name": f"{fixture.home_team} vs {fixture.away_team}",
            })
        
        response = {
            "success": True,
            "message": "success",
            "all_prediction": all_match,
        }
        return Response(response, status=status.HTTP_200_OK)
    
class FreeWebPredictionLinkAPIView(APIView):
    def get(self, request):
        today_date = datetime.now().date()
        link_ins = SoccerPredictionLink.objects.filter(link_expiry_date__gte=today_date, is_free=True).first()
        if not link_ins:
            return Response({"success": False, "message": "no free games currently"}, status=status.HTTP_200_OK)
        
        all_match = link_ins.prediction_matches
        response = {
            "success": True,
            "message": "success",
            "all_prediction": all_match,
        }
        return Response(response, status=status.HTTP_200_OK)