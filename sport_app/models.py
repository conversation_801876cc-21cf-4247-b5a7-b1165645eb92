import random
import time
import uuid
from datetime import datetime

import pandas as pd
import pytz
from django.conf import settings
from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.db.models import Count, Sum
from django.db.models.functions import Random

# from cloudinary.models import CloudinaryField
# from django.db import models
# from django.db.models import Q, Sum
from django.utils import timezone

from main.api.api_lottery_helpers import generate_game_play_id
from main.helpers.whisper_sms_managers import (
    payment_receipt_sms,
    send_sms_winning_to_players_from_agent,
)
from main.models import ConstantVariable, UserProfile

# from main.tasks import winner_engange_event
from sport_app.helpers.helper_functions import play_soccer_prediction_game
from sport_app.tasks import (
    create_rapid_prediction_matches,
    rapid_api_prediction,
    send_sms_win_wise_game_draw_soccer_lost,
    send_sms_win_wise_game_draw_soccer_won,
)
from wallet_app.models import DebitCreditRecord, GeneralWithdrawableWallet, UserWallet
from wyse_ussd.models import SoccerPrediction, UssdLotteryPayment

# from wyse_ussd.tasks import ussd_lottery_winner_reward

# from wyse_ussd.tasks import send_soccer_cash_winner_sms

# Create your models here.

SUBSCRIPTION_TYPE = [
    ("DAILY", "DAILY"),
    ("WEEKLY", "WEEKLY"),
    ("MONTHLY", "MONTHLY"),
]


class EnglishWeekHistory(models.Model):
    week_start_date = models.DateField(null=True, blank=True)
    week_end_date = models.DateField(null=True, blank=True)
    week_date_added = models.DateTimeField(auto_now_add=True)


class LaligaWeekHistory(models.Model):
    week_start_date = models.DateField(null=True, blank=True)
    week_end_date = models.DateField(null=True, blank=True)
    week_date_added = models.DateTimeField(auto_now_add=True)


class ChampionsLeagueWeekHistory(models.Model):
    week_start_date = models.DateField(null=True, blank=True)
    week_end_date = models.DateField(null=True, blank=True)
    week_date_added = models.DateTimeField(auto_now_add=True)


class EuropaLeagueWeekHistory(models.Model):
    week_start_date = models.DateField(null=True, blank=True)
    week_end_date = models.DateField(null=True, blank=True)
    week_date_added = models.DateTimeField(auto_now_add=True)


class WorldCupWeekHistory(models.Model):
    week_start_date = models.DateField(null=True, blank=True)
    week_end_date = models.DateField(null=True, blank=True)
    week_date_added = models.DateTimeField(auto_now_add=True)


class FootballTable(models.Model):
    fixture_id = models.CharField(max_length=255, blank=True, null=True)
    league_type = models.CharField(max_length=255, blank=True, null=True)
    league_id = models.CharField(max_length=255, blank=True, null=True)
    home_team = models.CharField(max_length=255, blank=True, null=True)
    away_team = models.CharField(max_length=255, blank=True, null=True)
    home_id = models.CharField(max_length=255, blank=True, null=True)
    away_id = models.CharField(max_length=255, blank=True, null=True)
    home_team_code = models.CharField(max_length=255, blank=True, null=True)
    away_team_code = models.CharField(max_length=255, blank=True, null=True)
    full_time_score = models.CharField(max_length=255, default="0-0")
    full_time_score_text = models.CharField(max_length=2300, default="0-0")
    home_full_time_score = models.IntegerField(null=True, blank=True)
    away_full_time_score = models.IntegerField(null=True, blank=True)
    match_status = models.CharField(max_length=255, default="NS")
    game_completed = models.BooleanField(default=False)
    home_logo = models.URLField(blank=True, null=True)
    away_logo = models.URLField(blank=True, null=True)
    fixture_date = models.DateTimeField()
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    freemium = models.BooleanField(default=False)
    is_drawn = models.BooleanField(default=False)

    def __str__(self) -> str:
        return self.league_type

    @staticmethod
    def run_game(game_object, win_data):
        """This method runs the decisioning on save method when game completed is
        toggle true at the end of each match and also sends sms to winners and those
        that entered a wrong prediction for a specific match(with unique fixtures id)"""
        # Run personal soccer cash game draw

        try:
            # Run personal soccer cash game draw
            FootballTable.soccer_cash_personal_play_type_game_draw(football_object=game_object)

        except TypeError as e:
            # handle TypeError
            print(f"TypeError occurred in personal soccer cash game draw: {e}")

        try:
            # run game decisioning
            # paid game
            paid_game_players = SoccerPrediction.objects.filter(
                football_table=game_object,
                paid=True,
                freemium=False,
                is_drawn=False,
                play_type="SHARED",
            )

            FootballTable.run_game_disburse_amount(paid_game_players, win_data)

        except ZeroDivisionError:
            # handle ZeroDivisionError differently
            print("ZeroDivisionError occurred in Shared soccer game")

        # except Exception as e:
        #     # handle other expected exceptions
        #     print(f"Exception occurred in paid game: {e}")
        #     pass

        try:
            # freemium game
            total_eligible_to_win = ConstantVariable.get_constant_variable().get("freemium_winner_range")
            freemuim_game_players = SoccerPrediction.objects.filter(football_table=game_object, freemium=True).order_by("date")[
                :total_eligible_to_win
            ]

            FootballTable.run_game_disburse_amount(game_players=freemuim_game_players, win_data=win_data)

        except ZeroDivisionError:
            # handle ZeroDivisionError differently
            print("ZeroDivisionError occurred in Shared soccer game")

    def save(self, *args, **kwargs):
        # This check helps in order not to run the game draw twice
        if self.game_completed is True and self.is_drawn is False:
            print("Game Completed")
            win_data = {
                "home_score": self.home_full_time_score,
                "away_score": self.away_full_time_score,
                "game_id": self.fixture_id,
            }
            # print(win_data)
            # print(self)
            FootballTable.run_game(game_object=self, win_data=win_data)
            self.is_drawn = True
        super(FootballTable, self).save(*args, **kwargs)

    @classmethod
    def toggle_freemium(cls, id):
        # print("AT FOOTBALL TABLE TOOGLE")
        # fixtures_id = fixtures_id_list.split(",")
        # for id in fixtures_id:

        match = cls.objects.get(fixture_id=id)
        match.freemium = True
        match.save()

    @classmethod
    def run_game_disburse_amount(cls, game_players, win_data):
        game_winners = play_soccer_prediction_game(game_players, win_data)

        DecisioningResult.objects.create(result_payload=game_winners)
        # pprint(game_winners)

        for key, value in game_winners.items():
            if isinstance(value, dict):
                for winner_item in value.get("winners"):
                    winner_item["player_phone"]
                    winner_item["agent_id"]

                    match_prediction_instance = SoccerPrediction.objects.get(id=winner_item["player_id"])
                    amount_won = winner_item["earning"]

                    # agent = match_prediction_instance.agent
                    # game_id = match_prediction_instance.game_id
                    # player_id = winner_item["player_id"]
                    # game_fixture_id = winner_item["game_id"]
                    # stake_amount = match_prediction_instance.stake_amount

                    SoccerPrediction.create_predictions_winners(amount_won=amount_won, prediction_ins=match_prediction_instance)

                    match_prediction_instance.is_drawn = True
                    match_prediction_instance.score_checked = True
                    match_prediction_instance.won = True
                    match_prediction_instance.active = False
                    match_prediction_instance.is_score_valid = True
                    match_prediction_instance.save()

        # game lost
        # print("Top level............")
        fixtures_id = win_data.get("game_id")
        home_score = win_data.get("home_score")
        away_score = win_data.get("away_score")

        print(home_score, away_score, "==========================")

        # get games with wrong pridiction
        lost_games = SoccerPrediction.objects.filter(game_fixture_id=fixtures_id, paid=True).exclude(home_choice=home_score, away_choice=away_score)
        lost_games = SoccerPrediction.objects.filter(game_fixture_id=fixtures_id, paid=True).exclude(home_choice=home_score, away_choice=away_score)
        print(lost_games)
        # print("Game lost.................")
        # get total amount and count of winners
        won_games = SoccerCashWinner.objects.filter(game_fixture_id=fixtures_id).aggregate(Sum("earning"), Count("earning"))

        no_of_Winners = won_games["earning__count"]
        total_amount_won = won_games["earning__sum"]

        # print("\n\n")
        # print("TOTAL COUNT", won_games["earning__count"])
        # print("TOTAL SUM", won_games["earning__sum"])

        football_table = FootballTable.objects.get(fixture_id=fixtures_id)
        match = f"{football_table.home_team_code} vs {football_table.away_team_code}"

        # print("\n\n")
        # print("MATCH :::::::", match)

        for lost_game in lost_games:
            # send sms to players with wrong prediction
            lost_game.is_drawn = True
            lost_game.save()
            send_sms_win_wise_game_draw_soccer_lost.delay(
                lost_game.phone,
                total_amount_won,
                no_of_Winners,
                match,
                timezone.now().date(),
            )
            print("Sms sent")

    @classmethod
    def soccer_cash_personal_play_type_game_draw(cls, football_object):
        """
        This method works along side with the Soccer predictions
        signals which is in the sport app signals.py file

        as soon as score checked is toggle true the signals
        checks for the total game that the result is checked
        if the count is equal to 3
        if checks for the total valid score prediction
        then if none of the tickets wins that makes it a lost tickets
        but if either 2 or 3 of the ticket is valid there's a winning to that game

        """

        predictions_qs = SoccerPrediction.objects.filter(
            football_table_id=football_object,
            paid=True,
            freemium=False,
            is_drawn=False,
            score_checked=False,
            active=True,
            is_score_valid=False,
            play_type="PERSONAL",
        )

        print("PREDICTIONS-COUNT PERSONAL PLAY :", predictions_qs.count())
        prediction_objects = list(predictions_qs)  # fetch all the objects in the queryset into memory

        for prediction in prediction_objects:
            if prediction.away_choice == football_object.away_full_time_score and prediction.home_choice == football_object.home_full_time_score:
                # print("valid prediction")
                prediction.is_score_valid = True

            else:
                # print("invalid prediction")
                prediction.is_score_valid = False

            prediction.score_checked = True
            prediction.save()

    @classmethod
    def get_personal_soccer_multiplier(cls):
        # min for winnings that scores 2/3 and the max for 3/3
        return [5, 40]


class FootballHistory(models.Model):
    lotto_id = models.ForeignKey(
        FootballTable,
        related_name="football_history",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    fixture_id = models.CharField(max_length=255, blank=True, null=True)
    fixture_data = models.TextField()


class FootballTeamShortCode(models.Model):
    team_id = models.CharField(max_length=255, blank=True, null=True)
    team_code = models.CharField(max_length=255, blank=True, null=True)
    team_name = models.CharField(max_length=255, blank=True, null=True)


class SoccerCashWinner(models.Model):
    PREDICTION_SOURCE = [
        ("USSD", "USSD"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
    ]
    agent = models.ForeignKey("pos_app.Agent", on_delete=models.CASCADE, null=True, blank=True)
    phone_number = models.CharField(max_length=150)
    game_play_id = models.CharField(max_length=150)
    player_id = models.PositiveIntegerField(null=True, blank=True)
    share = models.CharField(max_length=150, null=True, blank=True)
    stake_amount = models.FloatField(null=True, blank=True)
    earning = models.FloatField(default=0.00)
    date_won = models.DateTimeField(auto_now_add=True)
    # last_updated = models.DateTimeField(auto_now=True)
    channel_played_from = models.CharField(max_length=150, choices=PREDICTION_SOURCE, default="USSD")
    game_fixture_id = models.CharField(max_length=150, null=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.pk:
            # add amount won to general withdrawable wallet
            GeneralWithdrawableWallet.add_fund(self.earning, self.phone_number, "Soccer_Cash")

        super(SoccerCashWinner, self).save(*args, **kwargs)

    @classmethod
    def create_winner_object(cls, match_prediction_instance, **kwargs):
        # print(kwargs)
        cls.objects.create(**kwargs)

        # send sms
        prediction = f"{match_prediction_instance.home_choice} - {match_prediction_instance.away_choice}"

        football_table = FootballTable.objects.get(fixture_id=kwargs.get("game_fixture_id"))
        match = f"{football_table.home_team_code} vs {football_table.away_team_code}"

        send_sms_win_wise_game_draw_soccer_won.delay(
            phone_number=kwargs["phone_number"],
            won_amount=kwargs["earning"],
            prediction=prediction,
            match=match,
            draw_date=f"{timezone.now().date()}",
        )


def predict_match_score_web(phone_no, game_play, play_type):
    """
    This end function takes all input from the player
    and carry out some checks one at a time

    STEP 1: checks if game is still active i.e
    if match is not fully played

    STEP 2: checks if game selected as freemium play
    is not part of the game admin will like to make free

    STEP 3: get count of the existing and most recent
    freemium game player played

    STEP 4: get the total count of freemium game player
    likes to play

    STEP 5: if all steps are met as required then it plays the game

    """

    const = ConstantVariable.get_constant_variable()
    # freemium_play = const.get("freemuim_play")

    soccer_freemium_fixtures_id = const.get("soccer_freemium_fixtures_id").split(",") if const.get("soccer_freemium_fixtures_id") else []

    max_free_game_count = const.get("freemuim_play_count")

    freemium_occurence = 0

    for prediction in game_play:  # all matches
        # print(prediction.get("predictions"))

        for freemium in prediction.get("predictions"):
            fixtures_id = prediction.get("fixtures_id")

            # STEP 1
            active_fixtures = FootballTable.objects.filter(
                fixture_id=fixtures_id,
                game_completed=False,
                is_drawn=False,
                fixture_date__gte=timezone.now(),
            ).last()

            if active_fixtures is None:
                response = {
                    "succeeded": False,
                    "message": f"match id {fixtures_id} is completed or invalid",
                }
                return response

            # STEP 2
            elif freemium.get("freemium") is True and (
                fixtures_id in soccer_freemium_fixtures_id
            ):  # get count of total freemium games selected by the player
                # print(soccer_freemium_fixtures_id)
                # print(prediction.get("fixtures_id"), freemium.get("freemium"))
                freemium_occurence += 1

            # check if selected league or match is on the freemium game
            # selected by the player

            elif freemium.get("freemium") is True and (fixtures_id not in soccer_freemium_fixtures_id):
                response = {
                    "succeeded": False,
                    "message": f"selected match {fixtures_id} is not on freemium play",
                }
                return response
    # print(freemium_occurence, "FREEMIUM COUNT OCCURENCE")
    # STEP 3
    # check if existing freemium prediction is still valid
    existing_freemium_prediction_count = SoccerPrediction.count_of_existing_yet_to_play_freemium_game(phone_no, max_free_game_count)
    # print(existing_freemium_prediction_count, max_free_game_count)
    if (existing_freemium_prediction_count >= max_free_game_count) and (freemium_occurence > 0):
        response = {
            "succeeded": False,
            "message": "player has reached max freemium game play",
        }
        return response

    # print("\n")
    # print("FREEMIUM COUNT == ", freemium_occurence)

    # STEP 4

    elif freemium_occurence > max_free_game_count:
        # print("\n")
        # print(f"CANNOT HAVE MORE THAN {max_free_game_count} FREE GAME \n")

        response = {
            "succeeded": False,
            "message": f"cannot exceed {max_free_game_count} free game play on freemium play",
        }
        return response

    # STEP 5
    else:  # play game
        game_summary_response = SoccerPrediction.create_match_prediction_web(phone_no, game_play, play_type)
        return game_summary_response


class DecisioningResult(models.Model):
    result_payload = models.TextField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)


class GoalScorer(models.Model):
    player_name = models.CharField(max_length=300)
    player_country = models.ForeignKey("sport_app.WorldCupTeams", on_delete=models.CASCADE, null=True, blank=True)
    goal = models.IntegerField(default=0)
    is_top_scorer = models.BooleanField(default=False)
    is_drawn = models.BooleanField(default=False)
    available = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.player_name}"

    def save(self, *args, **kwargs):
        from sport_app.decisioning_engine.top_goal_scorer import HighestGoalScorerWin

        if self.is_top_scorer is True and self.is_drawn is False:
            win_data = {"goal": self.goal, "goal_scorer_id": self.id}
            HighestGoalScorerWin.run_game(self, win_data)
            self.is_drawn = True

        super(GoalScorer, self).save(*args, **kwargs)


class GoalScorerPrediction(models.Model):
    PREDICTION_CHANNEL = [
        ("USSD", "USSD"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
    ]

    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    agent = models.ForeignKey("pos_app.Agent", on_delete=models.CASCADE, null=True, blank=True)
    goal_scorer = models.ForeignKey("sport_app.GoalScorer", on_delete=models.CASCADE, null=True, blank=True)
    bought_lottery_ticket = models.ForeignKey("pos_app.BoughtLotteryTickets", on_delete=models.CASCADE, null=True, blank=True)
    phone = models.CharField(max_length=300)
    game_id = models.CharField(max_length=150, null=True, blank=True)
    goal = models.PositiveIntegerField(null=True, blank=True)
    band_played = models.CharField(max_length=150, null=True, blank=True)
    stake_amount = models.FloatField(default=0.00)
    potential_winning = models.FloatField(default=0.00)
    amount_paid = models.FloatField(default=0.00)
    paid = models.BooleanField(default=False)
    account_no = models.CharField(max_length=150, null=True, blank=True)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    bank_code = models.CharField(max_length=150, null=True, blank=True)
    date = models.DateTimeField(auto_now_add=True)
    paid_date = models.DateTimeField(null=True, blank=True)
    channel = models.CharField(max_length=150, choices=PREDICTION_CHANNEL, default="USSD")

    # freemium = models.BooleanField(default=False)

    def __str__(self) -> str:
        return f"{self.game_id}"

    @classmethod
    def create_goal_scorer_predition_web(cls, phone_no, game_play):  # web
        # create soccer prediction object
        game_play_id = generate_game_play_id()
        player_ids = []

        for play_count in range(len(game_play)):
            player_id = game_play[play_count].get("player_id")
            player_ids.append(player_id)

            # gather all predictions and create tickets
            prediction_list = []

            user = UserProfile.objects.get(phone_number=phone_no)
            goal_scorer = GoalScorer.objects.get(id=player_id)

            predictions = game_play[play_count].get("predictions")

            for prediction_count in range(len(predictions)):
                stake_amount = predictions[prediction_count].get("stake_amount")

                # print(stake_amount, "Stake Amount")
                win_amount = SoccerPrediction.stake_and_win_amount_web(stake_amount).get("win_amount")

                prediction_list.append(
                    cls(
                        user_profile=user,
                        goal_scorer=goal_scorer,
                        phone=phone_no,
                        game_id=game_play_id,
                        goal=predictions[prediction_count].get("goal"),
                        band_played=stake_amount,
                        stake_amount=stake_amount,
                        potential_winning=win_amount,
                        amount_paid=stake_amount,
                        paid=False,
                        channel="WEB",
                    )
                )

            cls.objects.bulk_create(prediction_list, batch_size=100, ignore_conflicts=True)

        return cls.goal_scorer_web_success_response(player_ids, game_play_id, user, method="POST")

    @classmethod
    def predict_goal_scorer_web(cls, phone_no, game_play):
        for prediction in game_play:  # all matches
            print(prediction["player_id"])
            try:
                GoalScorer.objects.get(id=prediction["player_id"])
            except GoalScorer.DoesNotExist:
                response = {
                    "succeeded": False,
                    "message": "invalid scorer Id",
                }
                return response

        return cls.create_goal_scorer_predition_web(phone_no, game_play)

    @classmethod
    def goal_scorer_web_success_response(cls, player_ids, game_play_id, user, method):  # web
        # This functions structures the success response on game play

        game_play = []
        for i in range(len(player_ids)):
            prediction_per_selected_goal_scorer = cls.objects.filter(game_id=game_play_id, goal_scorer__id=player_ids[i])

            predictions = []
            for prediction_per_goal_scorer in prediction_per_selected_goal_scorer:
                player_country_name = prediction_per_goal_scorer.goal_scorer.player_country
                prediction = {
                    "id": prediction_per_goal_scorer.id,
                    "goal": prediction_per_goal_scorer.goal,
                    "stake_amount": prediction_per_goal_scorer.stake_amount,
                }
                predictions.append(prediction)

            # goal_scorer = GoalScorer.objects.filter(id=player_ids[i]).last()

            game_play_dict = {
                "player_id": player_ids[i],
                "player_name": prediction_per_goal_scorer.goal_scorer.player_name,
                "player_country": player_country_name.team_name if player_country_name else "",
                # "stake_amount": game.stake_amount,
                "predictions": predictions,
            }

            game_play.append(game_play_dict)

        prediction_query_set = cls.objects.filter(game_id=game_play_id).values()
        prediction_df = pd.DataFrame(prediction_query_set)
        # print(prediction_df)
        stake_amount = prediction_df["stake_amount"].sum()
        winning_amount = prediction_df["potential_winning"].sum()

        if method == "POST":
            UssdLotteryPayment.objects.create(
                user=user,
                amount=stake_amount,  # total stake amount
                game_play_id=game_play_id,
                game_type="SOCCER",
                channel="WEB",
                lottery_type="SOCCER_CASH",
            )
        else:
            pass

        response = {
            "succeeded": True,
            "message": "success",
            "game_play_id": game_play_id,
            "game_summary": game_play,
            "matches_played": prediction_df["goal_scorer_id"].nunique(),
            "total_predictions": len(prediction_df),
            "total_stake_amount": stake_amount,
            "winning_amount": winning_amount,
        }

        # pprint(response)
        return response


class PlayerGoal(models.Model):
    goal = models.IntegerField(default=0)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)


class TeamFinalist(models.Model):
    # team_name = models.CharField(max_length=300)
    team_name = models.ForeignKey("sport_app.WorldCupTeams", on_delete=models.CASCADE, null=True, blank=True)
    team_win = models.BooleanField(default=False)
    is_drawn = models.BooleanField(default=False)
    available = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.team_name}"

    def save(self, *args, **kwargs):
        from sport_app.decisioning_engine.team_win import WinningTeamDecisioning

        if self.team_win is True and self.is_drawn is False:
            win_data = {"team_id": self.id}
            WinningTeamDecisioning.run_game(self, win_data)
            self.is_drawn = True

        super(TeamFinalist, self).save(*args, **kwargs)


class TeamFinalistPrediction(models.Model):
    PREDICTION_CHANNEL = [
        ("USSD", "USSD"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
    ]

    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    agent = models.ForeignKey("pos_app.Agent", on_delete=models.CASCADE, null=True, blank=True)
    team_final_list = models.ForeignKey("sport_app.TeamFinalist", on_delete=models.CASCADE, null=True, blank=True)
    bought_lottery_ticket = models.ForeignKey("pos_app.BoughtLotteryTickets", on_delete=models.CASCADE, null=True, blank=True)
    phone = models.CharField(max_length=300)
    game_id = models.CharField(max_length=150, null=True, blank=True)
    band_played = models.CharField(max_length=150, null=True, blank=True)
    stake_amount = models.FloatField(default=0.00)
    potential_winning = models.FloatField(default=0.00)
    amount_paid = models.FloatField(default=0.00)
    paid = models.BooleanField(default=False)
    account_no = models.CharField(max_length=150, null=True, blank=True)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    bank_code = models.CharField(max_length=150, null=True, blank=True)
    date = models.DateTimeField(auto_now_add=True)
    paid_date = models.DateTimeField(null=True, blank=True)
    channel = models.CharField(max_length=150, choices=PREDICTION_CHANNEL, default="USSD")

    # freemium = models.BooleanField(default=False)

    def __str__(self) -> str:
        return f"{self.game_id}"

    @classmethod
    def create_team_finalist_predition_web(cls, phone_no, game_play):  # web
        # create soccer prediction object
        game_play_id = generate_game_play_id()
        team_ids = []

        for play_count in range(len(game_play)):
            team_id = game_play[play_count].get("team_id")
            team_ids.append(team_id)

            # gather all predictions and create tickets
            prediction_list = []

            user = UserProfile.objects.get(phone_number=phone_no)
            team_finalist = TeamFinalist.objects.get(id=team_id)

            predictions = game_play[play_count].get("predictions")

            for prediction_count in range(len(predictions)):
                stake_amount = predictions[prediction_count].get("stake_amount")

                # print(stake_amount, "Stake Amount")
                win_amount = SoccerPrediction.stake_and_win_amount_web(stake_amount).get("win_amount")

                prediction_list.append(
                    cls(
                        user_profile=user,
                        team_final_list=team_finalist,
                        phone=phone_no,
                        game_id=game_play_id,
                        band_played=stake_amount,
                        stake_amount=stake_amount,
                        potential_winning=win_amount,
                        amount_paid=stake_amount,
                        paid=False,
                        channel="WEB",
                    )
                )

            cls.objects.bulk_create(prediction_list, batch_size=100, ignore_conflicts=True)

        return cls.team_finalist_web_success_response(team_ids, game_play_id, user, method="POST")

    @classmethod
    def predict_team_finalist_web(cls, phone_no, game_play):
        for prediction in game_play:  # all matches
            print(prediction["team_id"])
            team_id = prediction["team_id"]
            try:
                TeamFinalist.objects.get(id=team_id)
            except TeamFinalist.DoesNotExist:
                response = {
                    "succeeded": False,
                    "message": f"invalid team Id {team_id}",
                }
                return response

        return cls.create_team_finalist_predition_web(phone_no, game_play)

    @classmethod
    def team_finalist_web_success_response(cls, team_ids, game_play_id, user, method):  # web
        # This functions structures the success response on game play

        game_play = []
        for i in range(len(team_ids)):
            prediction_per_selected_team_finalist = cls.objects.filter(game_id=game_play_id, team_final_list_id=team_ids[i])

            predictions = []
            for prediction_per_team_finalist in prediction_per_selected_team_finalist:
                prediction = {
                    "id": prediction_per_team_finalist.id,
                    "stake_amount": prediction_per_team_finalist.stake_amount,
                }
                predictions.append(prediction)

            game_play_dict = {
                "team_id": team_ids[i],
                "team_name": prediction_per_team_finalist.team_final_list.team_name.team_name,
                "team_logo": prediction_per_team_finalist.team_final_list.team_name.team_logo,
                "predictions": predictions,
            }

            game_play.append(game_play_dict)

        prediction_query_set = cls.objects.filter(game_id=game_play_id).values()
        prediction_df = pd.DataFrame(prediction_query_set)
        # print(prediction_df)
        stake_amount = prediction_df["stake_amount"].sum()
        winning_amount = prediction_df["potential_winning"].sum()

        if method == "POST":
            UssdLotteryPayment.objects.create(
                user=user,
                amount=stake_amount,  # total stake amount
                game_play_id=game_play_id,
                game_type="SOCCER",
                channel="WEB",
                lottery_type="SOCCER_CASH",
            )
        else:
            pass

        response = {
            "succeeded": True,
            "message": "success",
            "game_play_id": game_play_id,
            "game_summary": game_play,
            "matches_played": prediction_df["team_final_list_id"].nunique(),
            "total_predictions": len(prediction_df),
            "total_stake_amount": stake_amount,
            "winning_amount": winning_amount,
        }

        # pprint(response)
        return response


class DualTeamFinalist(models.Model):
    team_a = models.ForeignKey("sport_app.WorldCupTeams", on_delete=models.CASCADE, related_name="team_a")
    team_b = models.ForeignKey(
        "sport_app.WorldCupTeams",
        on_delete=models.CASCADE,
        related_name="team_b",
    )
    team_win = models.BooleanField(default=False)
    is_drawn = models.BooleanField(default=False)
    available = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.id}"

    @classmethod
    def get_existing_object(cls, team_a: int, team_b: int) -> dict:
        """
        This method checks if a dual
        team object alredy exist either in reversed
        order or just exactly how the data is coming
        from the user input and if it doesn't
        exist it create a new object
        """

        raw_team_list = [team_a, team_b]
        team_id_list = [team_a, team_b]
        count = 0
        for _ in range(2):
            dual_team_finalist = cls.objects.filter(team_a__id=team_id_list[0], team_b__id=team_id_list[1], is_drawn=False).last()

            if dual_team_finalist is None:
                team_id_list = team_id_list[::-1]
                continue
            else:
                count += 1

        return {
            "count": count,
            "team_id_list": team_id_list,
            "raw_team_list": raw_team_list,
        }

    @classmethod
    def get_or_create_new_object(cls, team_a: int, team_b: int):
        obj = cls.get_existing_object(team_a, team_b)
        # print(obj)
        obj_count_value = obj["count"]
        team_id_list = obj["team_id_list"]
        raw_team_list = obj["raw_team_list"]

        if obj_count_value > 0:
            return cls.objects.get(team_a__id=team_id_list[0], team_b__id=team_id_list[1])

        else:  # create new object
            try:
                team_id = lambda id: WorldCupTeams.objects.get(id=id)  # noqa
                # try get if there's an existing object which is drawn True

                existing_object = lambda team_a_id, team_b_id: cls.objects.filter(team_a=team_a_id, team_b=team_b_id, is_drawn=True).count()  # noqa

                if (
                    existing_object(
                        team_a_id=team_id(raw_team_list[0]),
                        team_b_id=team_id(raw_team_list[1]),
                    )
                    > 0
                ):
                    return None
                elif (
                    existing_object(
                        team_a_id=team_id(raw_team_list[1]),
                        team_b_id=team_id(raw_team_list[0]),
                    )
                    > 0
                ):
                    return None

                else:
                    return cls.objects.create(
                        team_a=team_id(raw_team_list[0]),
                        team_b=team_id(raw_team_list[1]),
                    )
            except WorldCupTeams.DoesNotExist:
                return None

    def save(self, *args, **kwargs):
        from sport_app.decisioning_engine.dual_team import DualTeamDecisioning

        if self.team_win is True and self.is_drawn is False:
            win_data = {"team_id": self.id}
            DualTeamDecisioning.run_game(self, win_data)
            self.is_drawn = True

        super(DualTeamFinalist, self).save(*args, **kwargs)


class WorldCupTeams(models.Model):
    team_id = models.CharField(max_length=255, blank=True, null=True)
    team_name = models.CharField(max_length=255, blank=True, null=True)
    team_logo = models.URLField(blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.team_name}"


class CutoutTable(models.Model):
    prediction_name = models.CharField(max_length=300)
    is_on = models.BooleanField(default=False)


class DualTeamFinalistPrediction(models.Model):
    PREDICTION_CHANNEL = [
        ("USSD", "USSD"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
    ]

    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    agent = models.ForeignKey("pos_app.Agent", on_delete=models.CASCADE, null=True, blank=True)
    dual_team_finalist = models.ForeignKey("sport_app.DualTeamFinalist", on_delete=models.CASCADE, null=True, blank=True)
    bought_lottery_ticket = models.ForeignKey("pos_app.BoughtLotteryTickets", on_delete=models.CASCADE, null=True, blank=True)
    phone = models.CharField(max_length=300)
    game_id = models.CharField(max_length=150, null=True, blank=True)
    band_played = models.CharField(max_length=150, null=True, blank=True)
    stake_amount = models.FloatField(default=0.00)
    potential_winning = models.FloatField(default=0.00)
    amount_paid = models.FloatField(default=0.00)
    paid = models.BooleanField(default=False)
    account_no = models.CharField(max_length=150, null=True, blank=True)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    bank_code = models.CharField(max_length=150, null=True, blank=True)
    date = models.DateTimeField(auto_now_add=True)
    paid_date = models.DateTimeField(null=True, blank=True)
    channel = models.CharField(max_length=150, choices=PREDICTION_CHANNEL, default="USSD")

    # freemium = models.BooleanField(default=False)

    def __str__(self) -> str:
        return f"{self.game_id}"

    @classmethod
    def create_dual_team_finalist_predition_web(cls, phone_no, game_play):  # web
        # create soccer prediction object
        game_play_id = generate_game_play_id()
        team_ids = []

        for play_count in range(len(game_play)):
            team_a_id = game_play[play_count].get("team_a_id")
            team_b_id = game_play[play_count].get("team_b_id")

            team_ids.append([team_a_id, team_b_id])

            # gather all predictions and create tickets
            prediction_list = []

            user = UserProfile.objects.get(phone_number=phone_no)
            dual_team_finalist = DualTeamFinalist.get_or_create_new_object(team_a_id, team_b_id)

            predictions = game_play[play_count].get("predictions")

            for prediction_count in range(len(predictions)):
                stake_amount = predictions[prediction_count].get("stake_amount")

                # print(stake_amount, "Stake Amount")
                win_amount = SoccerPrediction.stake_and_win_amount_web(stake_amount).get("win_amount")

                prediction_list.append(
                    cls(
                        user_profile=user,
                        dual_team_finalist=dual_team_finalist,
                        phone=phone_no,
                        game_id=game_play_id,
                        band_played=stake_amount,
                        stake_amount=stake_amount,
                        potential_winning=win_amount,
                        amount_paid=stake_amount,
                        paid=False,
                        channel="WEB",
                    )
                )

            cls.objects.bulk_create(prediction_list, batch_size=100, ignore_conflicts=True)

        return cls.dual_team_finalist_web_success_response(team_ids, game_play_id, user, method="POST")

    @classmethod
    def predict_dual_team_finalist_web(cls, phone_no, game_play):
        for play_count in range(len(game_play)):
            team_a_id = game_play[play_count].get("team_a_id")
            team_b_id = game_play[play_count].get("team_b_id")
            dual_team_finalist = DualTeamFinalist.get_or_create_new_object(team_a_id, team_b_id)
            # print(dual_team_finalist)
            if dual_team_finalist is None:
                return {
                    "succeeded": False,
                    # "message": f"invalid team Ids selection team_a_id:{team_a_id}, team_b_id {team_b_id}",
                    "message": "Game drawn.\nkindly make new selection",
                }
        return cls.create_dual_team_finalist_predition_web(phone_no, game_play)

    @classmethod
    def dual_team_finalist_web_success_response(cls, team_ids, game_play_id, user, method):  # web
        # This functions structures the success response on game play

        game_play = []
        for i in range(len(team_ids)):
            team_a_id = team_ids[i][0]
            team_b_id = team_ids[i][1]
            dual_team_finalist = DualTeamFinalist.objects.get(team_a__id=team_a_id, team_b__id=team_b_id)
            prediction_per_selected_team_finalist = cls.objects.filter(game_id=game_play_id, dual_team_finalist=dual_team_finalist)

            predictions = []
            for prediction_per_team_finalist in prediction_per_selected_team_finalist:
                prediction = {
                    "id": prediction_per_team_finalist.id,
                    "stake_amount": prediction_per_team_finalist.stake_amount,
                }
                predictions.append(prediction)

            game_play_dict = {
                "team_a_id": team_a_id,
                "team_b_id": team_b_id,
                "team_a_name": dual_team_finalist.team_a.team_name,
                "team_b_name": dual_team_finalist.team_b.team_name,
                "team_a_logo": dual_team_finalist.team_a.team_logo,
                "team_b_logo": dual_team_finalist.team_b.team_logo,
                "predictions": predictions,
            }

            game_play.append(game_play_dict)

        prediction_query_set = cls.objects.filter(game_id=game_play_id).values()
        prediction_df = pd.DataFrame(prediction_query_set)
        # print(prediction_df)
        stake_amount = prediction_df["stake_amount"].sum()
        winning_amount = prediction_df["potential_winning"].sum()

        if method == "POST":
            UssdLotteryPayment.objects.create(
                user=user,
                amount=stake_amount,  # total stake amount
                game_play_id=game_play_id,
                game_type="SOCCER",
                channel="WEB",
                lottery_type="SOCCER_CASH",
            )
        else:
            pass

        response = {
            "succeeded": True,
            "message": "success",
            "game_play_id": game_play_id,
            "game_summary": game_play,
            "matches_played": prediction_df["dual_team_finalist_id"].nunique(),
            "total_predictions": len(prediction_df),
            "total_stake_amount": stake_amount,
            "winning_amount": winning_amount,
        }

        return response


class ImportantTeams(models.Model):
    team_id = models.CharField(max_length=20, unique=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)


class RunningBalance(models.Model):
    amount = models.FloatField(default=0.00)
    amount_added = models.FloatField(default=0.00)
    rtp = models.FloatField(default=0.00)
    rto = models.FloatField(default=0.00)
    initial_rtp = models.FloatField(default=0.00)
    total_stake_amount = models.FloatField(default=0.00)
    active = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def create_update_running_balance(cls, amount, rtp, rto, initial_rtp, total_stake_amount):
        wallet_instance = cls.objects.last()
        # print(wallet_instance, rto, rtp)

        if wallet_instance:
            update_amount = wallet_instance.amount + amount
            cls.objects.create(
                amount=update_amount,
                amount_added=amount,
                active=True,
                rtp=rtp,
                rto=rto,
                initial_rtp=initial_rtp,
                total_stake_amount=total_stake_amount,
            )

        elif wallet_instance is None:
            cls.objects.create(
                amount=amount,
                amount_added=amount,
                active=True,
                rtp=rtp,
                rto=rto,
                initial_rtp=initial_rtp,
                total_stake_amount=total_stake_amount,
            )
        # else:

        #     wallet_instance.amount_added = amount
        #     wallet_instance.rtp = rtp
        #     wallet_instance.rto = rto
        #     wallet_instance.initial_rtp = initial_rtp
        #     wallet_instance.total_stake_amount = total_stake_amount

        #     wallet_instance.save()

    @classmethod
    def get_running_balance_amount(cls) -> int:
        cls_ins = cls.objects.last()
        if cls_ins is None:
            return 0
        else:
            return int(cls_ins.amount)


class AgentSoccerCashWinner(models.Model):
    PREDICTION_SOURCE = [
        ("POS_AGENT", "POS_AGENT"),
    ]
    agent = models.ForeignKey("pos_app.Agent", on_delete=models.CASCADE)
    user_profile = models.ForeignKey(
        UserProfile,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    game_id = models.CharField(max_length=300)
    amount_won = models.FloatField(default=0.00)
    pin = models.CharField(max_length=300, editable=False)
    # is_win_claimed = models.BooleanField(default=False)
    # withdrawl_initiated = models.BooleanField(default=False)
    channel_played_from = models.CharField(max_length=150, choices=PREDICTION_SOURCE, default="POS_AGENT")
    game_fixture_id = models.CharField(max_length=150, null=True, blank=True)
    player_id = models.PositiveIntegerField(null=True, blank=True)
    share = models.CharField(max_length=150, null=True, blank=True)
    stake_amount = models.FloatField(null=True, blank=True)
    date_won = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return super().__str__()

    # class Meta:
    #     verbose_name = ""
    #     verbose_name_plural = ""

    @classmethod
    def create_winner_object(cls, agent_player_exist, phone_no, **kwargs):
        if agent_player_exist:
            print(kwargs, "PLAYER EXIST")

            cls.objects.create(**kwargs)
            agent = kwargs.get("agent")

            first_name = agent.first_name
            last_name = agent.last_name

            send_sms_winning_to_players_from_agent(
                phone_number=phone_no,
                amount_won=kwargs.get("amount_won"),
                game_play_id=kwargs.get("game_id"),
                agent_name=f"{first_name} {last_name}",
                pin=kwargs.get("pin"),
            )

        else:  # create winner object and do not send sms
            print(kwargs, "PLAYER DOES NOT EXIST")

            cls.objects.create(**kwargs)

    @classmethod
    def get_winner_object(cls, game_id):
        # check if ticket with game_id won
        # send sms if user_profile is None
        pass

    @classmethod
    def agent_winner_exist(cls, agent_instane, game_id) -> bool:
        return cls.objects.filter(agent=agent_instane, user_profile__isnull=False, game_id=game_id).exists()

    @classmethod
    def agent_winner_not_exist(cls, agent_instane, game_id):
        return cls.objects.filter(agent=agent_instane, user_profile__isnull=True, game_id=game_id)

    @classmethod
    def send_sms_to_late_ticket_registering(cls, agent_instane, game_id, phone_no, user_profile):
        unregistered_winnings = cls.agent_winner_not_exist(agent_instane, game_id)

        if unregistered_winnings.exists():
            print("Won but hasn't register ticket -------------->")
            print("Updating....................")

            for win_obj in unregistered_winnings:
                print(phone_no, win_obj.amount_won, win_obj.game_id, win_obj.pin)

                first_name = win_obj.agent.first_name
                last_name = win_obj.agent.last_name

                send_sms_winning_to_players_from_agent(
                    phone_number=phone_no,
                    amount_won=win_obj.amount_won,
                    game_play_id=win_obj.game_id,
                    agent_name=f"{first_name} {last_name}",
                    pin=win_obj.pin,
                )

                win_obj.user_profile = user_profile
                win_obj.save()

        else:
            print("ticket not drawn or wining does not exist |<-------------->|")


# SOCCER VIRTUAL GAME
class VirtualLeagues(models.Model):
    name = models.CharField(max_length=300)
    country = models.CharField(max_length=300)
    rapid_api_league_id = models.CharField(max_length=50)
    logo = models.CharField(max_length=300, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "VIRTUAL LEAGUE"
        verbose_name_plural = "VIRTUAL LEAGUES"

    @classmethod
    def create_league(cls, name, country, rapid_api_league_id, logo):
        cls.objects.create(
            name=name,
            country=country,
            rapid_api_league_id=rapid_api_league_id,
            logo=logo,
        )

    def __str__(self) -> str:
        return self.name

    @classmethod
    def get_league(cls, league_id):
        return cls.objects.get(id=league_id)

    @classmethod
    def get_league_by_rapid_api_league_id(cls, rapid_api_league_id):
        return cls.objects.get(rapid_api_league_id=rapid_api_league_id)

    @classmethod
    def get_league_by_name(cls, name):
        return cls.objects.get(name=name)

    @classmethod
    def get_league_by_country(cls, country):
        return cls.objects.get(country=country)

    @classmethod
    def get_leagues(cls):
        return cls.objects.all()


class VirtualTeams(models.Model):
    name = models.CharField(max_length=300)
    name_code = models.CharField(max_length=300, null=True, blank=True)
    league = models.ForeignKey(VirtualLeagues, on_delete=models.CASCADE)
    rapid_api_team_id = models.CharField(max_length=50)
    logo = models.CharField(max_length=300, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.name

    class Meta:
        verbose_name = "VIRTUAL TEAM"
        verbose_name_plural = "VIRTUAL TEAMS"

    @classmethod
    def create_team(cls, name, league, rapid_api_team_id, logo):
        cls.objects.create(
            name=name,
            league=league,
            rapid_api_team_id=rapid_api_team_id,
            logo=logo,
        )

    @classmethod
    def get_team(cls, team_id):
        return cls.objects.get(id=team_id)

    @classmethod
    def get_team_by_rapid_api_team_id(cls, rapid_api_team_id):
        return cls.objects.get(rapid_api_team_id=rapid_api_team_id)

    @classmethod
    def get_team_by_name(cls, name):
        return cls.objects.get(name=name)

    @classmethod
    def get_teams_by_league_id(cls, id):
        return cls.objects.filter(league__id=id)

    @classmethod
    def get_teams(cls):
        return cls.objects.all()


class VirtualMatches(models.Model):
    WIN_TYPE_STATUS = (
        ("CASHBACK", "CASHBACK"),
        ("DRAW", "DRAW"),
        ("STRAIGHT_WIN", "STRAIGHT_WIN"),
        ("LOST", "LOST"),
    )

    CHANNEL = (
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
    )

    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    home_team = models.ForeignKey(VirtualTeams, on_delete=models.CASCADE, related_name="home_team")
    away_team = models.ForeignKey(VirtualTeams, on_delete=models.CASCADE, related_name="away_team")
    league = models.ForeignKey(VirtualLeagues, on_delete=models.CASCADE)
    home_team_score = models.IntegerField(default=0)
    away_team_score = models.IntegerField(default=0)
    home_team_result = models.IntegerField(default=0)
    away_team_result = models.IntegerField(default=0)
    game_play_id = models.CharField(max_length=50)
    paid = models.BooleanField(default=False)
    won = models.BooleanField(default=False)
    amount_won = models.FloatField(default=0)
    win_type = models.CharField(max_length=300, choices=WIN_TYPE_STATUS, default="STRAIGHT_WIN")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    channel = models.CharField(max_length=300, choices=CHANNEL, default="WEB")

    class Meta:
        verbose_name = "VIRTUAL MATCH"
        verbose_name_plural = "VIRTUAL MATCHES"

    @classmethod
    def create_match(
        cls,
        user_profile,
        agent_profile,
        home_team,
        away_team,
        league,
        home_team_score,
        away_team_score,
        game_play_id,
    ):
        cls.objects.create(
            user_profile=user_profile,
            agent_profile=agent_profile,
            home_team=home_team,
            away_team=away_team,
            league=league,
            home_team_score=home_team_score,
            away_team_score=away_team_score,
            game_play_id=game_play_id,
        )

    @classmethod
    def get_match(cls, match_id):
        return cls.objects.get(id=match_id)

    @classmethod
    def get_match_by_game_play_id(cls, game_play_id):
        return cls.objects.get(game_play_id=game_play_id)

    @classmethod
    def update_match_score(cls, game_play_id, home_team_score, away_team_score):
        match = cls.get_match_by_game_play_id(game_play_id)
        match.home_team_score = home_team_score
        match.away_team_score = away_team_score
        match.save()

    @classmethod
    def generate_lotto_number(cls, user_prediction: list):
        number = random.randint(1, 40)
        user_prediction.append(number)
        number2 = random.randint(1, 40)
        user_prediction.append(number2)

        # return as a string
        return ",".join(str(x) for x in user_prediction)

    @classmethod
    def update_match_as_lost(cls, game_play_id):
        matches = cls.objects.filter(game_play_id=game_play_id)
        generated_prediction_result = cls.generate_predictions(queryset=matches, type_of_winning="Lost", no_of_winning=len(matches))

        for team in generated_prediction_result:
            matches.filter(id=team.get("instance_id")).update(
                win_type="LOST",
                home_team_result=team.get("home_team_result"),
                away_team_result=team.get("away_team_result"),
            )

    @classmethod
    def update_match_as_won(cls, game_play_id, lotto_winner_instance):
        # lotto_winner_instance:LottoWinners
        matches = cls.objects.filter(game_play_id=game_play_id)

        if ((lotto_winner_instance.match_type == "PERM_4") and (lotto_winner_instance.is_illusion_winning is False)) and (
            lotto_winner_instance.win_flavour not in ["CASHBACK", "BLACK"]
        ):
            generated_prediction_result = cls.generate_predictions(queryset=matches, type_of_winning="Won", no_of_winning=5)

            print(
                f"""
            generated_prediction_result: {generated_prediction_result}
            \n\n\n\n
            """
            )
            for team in generated_prediction_result:
                matches.filter(id=team.get("instance_id")).update(
                    win_type="STRAIGHT_WIN",
                    home_team_result=team.get("home_team_result"),
                    away_team_result=team.get("away_team_result"),
                    won=True,
                    amount_won=lotto_winner_instance.earning,
                )

        else:
            if lotto_winner_instance.match_type == "PERM_5":
                _type_of_winnings_in_num = 5

            elif lotto_winner_instance.match_type == "PERM_4":
                _type_of_winnings_in_num = 4
            elif lotto_winner_instance.match_type == "PERM_3":
                _type_of_winnings_in_num = 3
            elif lotto_winner_instance.match_type == "PERM_2":
                _type_of_winnings_in_num = 2
            elif lotto_winner_instance.match_type == "PERM_1":
                _type_of_winnings_in_num = 1
            elif lotto_winner_instance.match_type == "PERM_0":
                _type_of_winnings_in_num = 1

            print(
                f"""
            lotto_winner_instance.match_type: {lotto_winner_instance.match_type}
            _type_of_winnings_in_num: {_type_of_winnings_in_num}

            """
            )
            shuffled_matches = matches.order_by(Random()).order_by(Random())

            if ((lotto_winner_instance.win_flavour == "CASHBACK") or (lotto_winner_instance.win_flavour == "BLACK")) and (
                lotto_winner_instance.is_illusion_winning is True
            ):
                generated_prediction_result = cls.generate_predictions(
                    queryset=shuffled_matches,
                    type_of_winning="Draw",
                    no_of_winning=_type_of_winnings_in_num,
                )

                for team in generated_prediction_result:
                    filtered_match = matches.filter(id=team.get("instance_id"))
                    if filtered_match:
                        for match in filtered_match:
                            match.win_type = team.get("status")
                            match.home_team_result = team.get("home_team_result")
                            match.away_team_result = team.get("away_team_result")
                            match.won = team.get("won")
                            match.amount_won = lotto_winner_instance.earning if team.get("won") is True else 0
                            match.save()

                        # filtered_match.update(
                        #     win_type=team.get("status"),
                        #     home_team_result=team.get("home_team_result"),
                        #     away_team_result=team.get("away_team_result"),
                        #     won=team.get("won"),
                        #     amount_won=lotto_winner_instance.earning
                        #     if team.get("won") is True
                        #     else 0,
                        # )

            elif (lotto_winner_instance.win_flavour == "CASHBACK") or (lotto_winner_instance.win_flavour == "BLACK"):
                generated_prediction_result = cls.generate_predictions(
                    queryset=shuffled_matches,
                    type_of_winning="Close Winning",
                    no_of_winning=_type_of_winnings_in_num,
                )

                for team in generated_prediction_result:
                    matches.filter(id=team.get("instance_id")).update(
                        win_type=team.get("status"),
                        home_team_result=team.get("home_team_result"),
                        away_team_result=team.get("away_team_result"),
                        won=team.get("won"),
                        amount_won=lotto_winner_instance.earning if team.get("won") is True else 0,
                    )
            else:
                generated_prediction_result = cls.generate_predictions(
                    queryset=shuffled_matches,
                    type_of_winning="Won",
                    no_of_winning=_type_of_winnings_in_num,
                )

                for team in generated_prediction_result:
                    matches.filter(id=team.get("instance_id")).update(
                        win_type=team.get("status"),
                        home_team_result=team.get("home_team_result"),
                        away_team_result=team.get("away_team_result"),
                        won=team.get("won"),
                        amount_won=lotto_winner_instance.earning if team.get("won") is True else 0,
                    )

    @classmethod
    def generate_predictions(cls, queryset, type_of_winning, no_of_winning):
        predictions = []

        if len(queryset) == 0:
            return predictions

        if type_of_winning == "Won":
            # Generate winning predictions
            # won_ticket = min(no_of_winning, len(queryset))
            won_ids = []

            for i in range(no_of_winning):
                if len(won_ids) > 0:
                    queryset = queryset.exclude(id__in=won_ids)

                random_queryset = random.choice(queryset)
                home_team = random_queryset
                instantance = random_queryset
                home_team = instantance
                away_team = instantance

                predictions.append(
                    {
                        "home_team": home_team.home_team.name,
                        "away_team": away_team.away_team.name,
                        "home_team_score": home_team.home_team_score,
                        "away_team_score": away_team.away_team_score,
                        "home_team_result": home_team.home_team_score,
                        "away_team_result": away_team.away_team_score,
                        "home_team_logo": home_team.home_team.logo,
                        "away_team_logo": away_team.away_team.logo,
                        "won": True,
                        "status": "STRAIGHT_WIN",
                        "instance_id": away_team.id,
                    }
                )
                if home_team.id not in won_ids:
                    print("home_team.id", home_team.id)
                    won_ids.append(home_team.id)

                # won_ids.append(home_team.id)

            queryset = queryset.exclude(id__in=won_ids)
            for team in queryset:
                home_team = team
                away_team = team

                home_team_score = random.randint(0, 5)
                away_team_score = random.randint(0, 5)

                while home_team_score == home_team.home_team_score:
                    home_team_score = random.randint(0, 5)

                while away_team_score == away_team.away_team_score:
                    away_team_score = random.randint(0, 5)

                while away_team_score == home_team_score:
                    away_team_score = random.randint(0, 5)

                predictions.append(
                    {
                        "home_team": home_team.home_team.name,
                        "away_team": away_team.away_team.name,
                        "home_team_score": home_team.home_team_score,
                        "away_team_score": away_team.away_team_score,
                        "home_team_result": home_team_score,
                        "away_team_result": away_team_score,
                        "home_team_logo": home_team.home_team.logo,
                        "away_team_logo": away_team.away_team.logo,
                        "won": False,
                        "status": "LOST",
                        "instance_id": away_team.id,
                    }
                )

        elif type_of_winning == "Lost":
            # Generate losing predictions
            for team in queryset:
                # away_team = random.choice(queryset.exclude(id=home_team.id))
                # home_team_score = random.randint(0, 5)
                # away_team_score = random.randint(0, 5)
                # while away_team_score == home_team_score:
                #     away_team_score = random.randint(0, 5)

                home_team = team
                away_team = team

                home_team_score = random.randint(0, 5)
                away_team_score = random.randint(0, 5)

                while home_team_score == home_team.home_team_score:
                    home_team_score = random.randint(0, 5)

                while away_team_score == away_team.away_team_score:
                    away_team_score = random.randint(0, 5)

                while away_team_score == home_team_score:
                    away_team_score = random.randint(0, 5)
                    if away_team_score == home_team_score:
                        away_team_score = random.randint(0, 5)

                predictions.append(
                    {
                        "home_team": home_team.home_team.name,
                        "away_team": away_team.away_team.name,
                        "home_team_score": home_team.home_team_score,
                        "away_team_score": away_team.away_team_score,
                        "home_team_result": home_team_score,
                        "away_team_result": away_team_score,
                        "home_team_logo": home_team.home_team.logo,
                        "away_team_logo": away_team.away_team.logo,
                        "won": False,
                        "status": "LOST",
                        "instance_id": team.id,
                    }
                )

        elif type_of_winning == "Draw":
            # Generate draw predictions
            min(no_of_winning, len(queryset))
            draw_ids = []
            for i in range(no_of_winning):
                if len(draw_ids) > 0:
                    queryset = queryset.exclude(id__in=draw_ids)

                random_queryset = random.choice(queryset)
                home_team = random_queryset
                instantance = random_queryset
                home_team = instantance
                away_team = instantance

                if home_team.home_team_score == away_team.away_team_score:
                    predictions.append(
                        {
                            "home_team": home_team.home_team.name,
                            "away_team": away_team.away_team.name,
                            "home_team_score": home_team.home_team_score,
                            "away_team_score": away_team.away_team_score,
                            "home_team_result": home_team.home_team_score,
                            "away_team_result": home_team.home_team_score,
                            "home_team_logo": home_team.home_team.logo,
                            "away_team_logo": away_team.away_team.logo,
                            "won": True,
                            "status": "DRAW",
                            "instance_id": away_team.id,
                        }
                    )

                    draw_ids.append(away_team.id)

                else:
                    home_team_score = random.randint(0, 5)

                    while home_team_score == home_team.home_team_score:
                        home_team_score = random.randint(0, 5)

                    predictions.append(
                        {
                            "home_team": home_team.home_team.name,
                            "away_team": away_team.away_team.name,
                            "home_team_score": home_team.home_team_score,
                            "away_team_score": away_team.away_team_score,
                            "home_team_result": home_team_score,
                            "away_team_result": home_team_score,
                            "home_team_logo": home_team.home_team.logo,
                            "away_team_logo": away_team.away_team.logo,
                            "won": True,
                            "status": "DRAW",
                            "instance_id": instantance.id,
                        }
                    )

                    # if home_team.id not in draw_ids:
                    #     draw_ids.append(home_team.id)

                    draw_ids.append(away_team.id)

            # Generate losing predictions for remaining teams
            non_draw_queryset = queryset.exclude(id__in=draw_ids)

            for team in non_draw_queryset:
                home_team = team
                away_team = team

                home_team_score = random.randint(0, 5)
                away_team_score = random.randint(0, 5)

                while home_team_score == home_team.home_team_score:
                    home_team_score = random.randint(0, 5)

                while away_team_score == away_team.away_team_score:
                    away_team_score = random.randint(0, 5)

                while away_team_score == home_team_score:
                    away_team_score = random.randint(0, 5)

                predictions.append(
                    {
                        "home_team": home_team.home_team.name,
                        "away_team": away_team.away_team.name,
                        "home_team_score": home_team.home_team_score,
                        "away_team_score": away_team.away_team_score,
                        "home_team_result": home_team_score,
                        "away_team_result": away_team_score,
                        "home_team_logo": home_team.home_team.logo,
                        "away_team_logo": away_team.away_team.logo,
                        "won": False,
                        "status": "LOST",
                        "instance_id": away_team.id,
                    }
                )

        elif type_of_winning == "Close Winning":
            # Sort the queryset by the absolute difference between home_team_prediction and away_team_prediction
            sorted_queryset = sorted(
                queryset,
                key=lambda x: abs(x.home_team_score - x.away_team_score),
            )

            # Slice the queryset to only include the number of teams specified by no_of_winning
            selected_teams = sorted_queryset[:no_of_winning]

            close_winning_ticket = []

            for team in selected_teams:
                home_team_score = random.randint(team.home_team_score - 1, team.home_team_score + 1)
                away_team_score = random.randint(team.away_team_score - 1, team.away_team_score + 1)

                home_team = team
                away_team = team

                # Generate close scores for both teams
                # avg_score = (home_team.home_team_score + away_team.away_team_score) / 2

                score_diff = abs(home_team.home_team_score - away_team.away_team_score)

                # Set the maximum amount that the close scores can differ from the original scores
                max_diff = min(score_diff, 3)

                # team1_close_score = int(avg_score + random.randint(-3, 3))
                # team2_close_score = int(avg_score + random.randint(-3, 3))

                team1_close_score = home_team.home_team_score + random.randint(-max_diff, max_diff)
                team2_close_score = away_team.away_team_score + random.randint(-max_diff, max_diff)

                team1_close_score = min(max(team1_close_score, 0), 5)
                team2_close_score = min(max(team2_close_score, 0), 5)

                # while team1_close_score == team2_close_score:
                #     team1_close_score = int(avg_score + random.randint(-3, 3))
                #     team2_close_score = int(avg_score + random.randint(-3, 3))
                #     team1_close_score = min(max(team1_close_score, 0), 5)
                #     team2_close_score = min(max(team2_close_score, 0), 5)

                while team1_close_score == team2_close_score:
                    team1_close_score += 1
                    team1_close_score = min(max(team1_close_score, 0), 5)
                    team2_close_score -= 1
                    team2_close_score = min(max(team2_close_score, 0), 5)

                predictions.append(
                    {
                        "home_team": home_team.home_team.name,
                        "away_team": away_team.away_team.name,
                        "home_team_score": home_team.home_team_score,
                        "away_team_score": away_team.away_team_score,
                        "home_team_result": team1_close_score,
                        "away_team_result": team2_close_score,
                        "home_team_logo": home_team.home_team.logo,
                        "away_team_logo": away_team.away_team.logo,
                        "won": True,
                        "status": "CASHBACK",
                        "instance_id": away_team.id,
                    }
                )

                # if away_team.id not in close_winning_ticket:

                close_winning_ticket.append(away_team.id)

            non_close_winning_queryset = queryset.exclude(id__in=close_winning_ticket)

            for team in non_close_winning_queryset:
                home_team = team
                away_team = team

                home_team_score = random.randint(0, 5)
                away_team_score = random.randint(0, 5)

                while home_team_score == home_team.home_team_score:
                    home_team_score = random.randint(0, 5)

                while away_team_score == away_team.away_team_score:
                    away_team_score = random.randint(0, 5)

                while away_team_score == home_team_score:
                    away_team_score = random.randint(0, 5)

                predictions.append(
                    {
                        "home_team": home_team.home_team.name,
                        "away_team": away_team.away_team.name,
                        "home_team_score": home_team.home_team_score,
                        "away_team_score": away_team.away_team_score,
                        "home_team_result": home_team_score,
                        "away_team_result": away_team_score,
                        "home_team_logo": home_team.home_team.logo,
                        "away_team_logo": away_team.away_team.logo,
                        "won": False,
                        "status": "LOST",
                        "instance_id": away_team.id,
                    }
                )

        return predictions

    @classmethod
    def get_result(cls, score1, score2, is_home_team=True):
        """Calculates the result of a game based on the two scores and whether the first score belongs to the home team"""
        if score1 > score2:
            return 1 if is_home_team else 2  # home team won or away team lost
        elif score1 < score2:
            return 2 if is_home_team else 1  # home team lost or away team won
        else:
            return 0  # draw


class PredictionTable(models.Model):
    API_TYPE = [
        ("RAPID_API", "RAPID_API"),
        ("SPORT_MONK", "SPORT_MONK"),
    ]
    fixture_id = models.CharField(max_length=255, blank=True, null=True)
    league_type = models.CharField(max_length=255, blank=True, null=True)
    api_type = models.CharField(max_length=200, choices=API_TYPE, default="RAPID_API", null=True, blank=True)
    league_id = models.CharField(max_length=255, blank=True, null=True)
    home_team = models.CharField(max_length=255, blank=True, null=True)
    away_team = models.CharField(max_length=255, blank=True, null=True)
    home_id = models.CharField(max_length=255, blank=True, null=True)
    away_id = models.CharField(max_length=255, blank=True, null=True)
    home_team_code = models.CharField(max_length=255, blank=True, null=True)
    away_team_code = models.CharField(max_length=255, blank=True, null=True)
    full_time_score = models.CharField(max_length=255, default="0-0")
    full_time_score_text = models.CharField(max_length=2300, default="0-0")
    home_full_time_score = models.IntegerField(null=True, blank=True)
    away_full_time_score = models.IntegerField(null=True, blank=True)
    match_status = models.CharField(max_length=255, default="NS")
    game_completed = models.BooleanField(default=False)
    home_logo = models.URLField(blank=True, null=True)
    away_logo = models.URLField(blank=True, null=True)
    predicted_fulltime_home_score = models.CharField(max_length=255, blank=True, null=True)
    predicted_fulltime_away_score = models.CharField(max_length=255, blank=True, null=True)
    predicted_halftime_home_score = models.CharField(max_length=255, blank=True, null=True)
    predicted_halftime_away_score = models.CharField(max_length=255, blank=True, null=True)
    predicted_home_score_odd = models.CharField(max_length=255, blank=True, null=True)
    predicted_away_score_odd = models.CharField(max_length=255, blank=True, null=True)
    predicted_under_or_over = models.CharField(max_length=255, blank=True, null=True)
    predicted_winner = models.CharField(max_length=255, blank=True, null=True)
    predicted_winner_id = models.CharField(max_length=255, blank=True, null=True)
    predicted_win_or_draw = models.BooleanField(default=False)
    predicted_home_goals = models.CharField(max_length=255, blank=True, null=True)
    predicted_away_goals = models.CharField(max_length=255, blank=True, null=True)
    predicted_advice = models.CharField(max_length=255, blank=True, null=True)
    is_predicted = models.BooleanField(default=False)
    correct_score = models.BooleanField(default=False)
    fixture_date = models.DateTimeField()
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)


class OddPredictionType(models.Model):
    subscription_name = models.CharField(max_length=255, blank=True, null=True)
    subscription_amount = models.FloatField(default=0.00)
    subscription_duration = models.IntegerField(default=0)
    duration_of_subscription_type = models.CharField(
        max_length=200,
        choices=SUBSCRIPTION_TYPE,
        default="DAILY",
        null=True,
        blank=True,
    )
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.subscription_name if self.subscription_name else ""


class UserPredictionSubscriptionTable(models.Model):
    user_profile = models.ForeignKey("main.UserProfile",
        on_delete=models.CASCADE,
        null=True,
        blank=True)
    phone_number = models.CharField(max_length=255, blank=True, null=True, unique=True)
    number_of_prediction_sent = models.IntegerField(default=0)
    accuracy_data = models.CharField(max_length=255, blank=True, null=True)
    duration_of_subscription = models.IntegerField(default=0)
    duration_of_subscription_type = models.CharField(max_length=200, choices=SUBSCRIPTION_TYPE, default="DAILY", null=True, blank=True)
    subscription_days_left = models.IntegerField(default=0)
    is_subscribed = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

class SoccerOddPredictionTable(models.Model):
    LOTTO_CHANNEL = [
        ("USSD", "USSD"),
        ("USSD_WEB", "USSD_WEB"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
        ("SYSTEM_BONUS", "SYSTEM_BONUS"),
    ]
    SUBSCRIPTION_TYPE = [
        ("PREDICTION_SUBSCRIPTION", "PREDICTION_SUBSCRIPTION"),
    ]
    user_profile = models.ForeignKey("main.UserProfile", on_delete=models.CASCADE, null=True, blank=True)
    phone_number = models.CharField(max_length=255, blank=True, null=True)
    subscription_type = models.ForeignKey(
        OddPredictionType,
        on_delete=models.CASCADE,
        null=True,
        blank=True)
    phone_number = models.CharField(max_length=255, blank=True, null=True)
    subscription_type = models.ForeignKey(OddPredictionType, on_delete=models.CASCADE, related_name="odd_subscription_plan")
    channel = models.CharField(
        max_length=150, choices=LOTTO_CHANNEL, default="USSD_WEB"
    )
    number_of_prediction_sent = models.IntegerField(default=0)
    accuracy_data = models.CharField(max_length=255, blank=True, null=True)
    duration_of_subscription = models.IntegerField(default=0)
    duration_of_subscription_type = models.CharField(
        max_length=200,
        choices=SUBSCRIPTION_TYPE,
        default="DAILY",
        null=True,
        blank=True,
    )
    subscription_days_left = models.IntegerField(default=0)
    consent = models.BooleanField(default=False)
    is_paid = models.BooleanField(default=False)
    amount = models.FloatField(default=0.00)
    amount_paid = models.FloatField(default=0.00)
    paid_date = models.DateTimeField(null=True, blank=True)
    lottery_type = models.CharField(
        max_length=150, choices=SUBSCRIPTION_TYPE, default="PREDICTION_SUBSCRIPTION"
    )
    is_active = models.BooleanField(default=False)
    is_subscription_allocated = models.BooleanField(default=False)
    game_pay_id = models.CharField(max_length=255, blank=True, null=True)
    played_from_telco_channel = models.BooleanField(default=False)
    is_active = models.BooleanField(default=False)
    list_of_games_received = ArrayField(models.TextField(), blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [models.Index(fields=["game_pay_id"])]

    @classmethod
    def handle_ussd_prediction_subscription_payment(cls, ussd_lottery_payment_instance, from_web=False):
        game_pay_amount = ussd_lottery_payment_instance.amount

        print(
            f"""
        game_pay_amount: {game_pay_amount}
        \n\n\n\n\n\n
        """
        )

        user_wallet = UserWallet.objects.filter(user__id=ussd_lottery_payment_instance.user.id, wallet_tag="WEB").last()

        if ussd_lottery_payment_instance.played_via_telco_channel is False:
            user_wallet_bal = user_wallet.game_available_balance
        else:
            user_wallet_bal = user_wallet.telco_wallet_balance

        if user_wallet_bal < game_pay_amount:
            """USER GAME PLAY BALANCE IS LESS THAN WHAT WE WANT TO DEDUCT
            WE'LL CHECK IF THE AMOUNT CAN PAY FOR HIS SUBSCRIPTION TICKETS
            """

            if from_web is False:
                pass
            else:
                transaction_ref = f"{uuid.uuid4()}-{int(time.time())}"

                debit_credit_record = DebitCreditRecord.create_record(
                    phone_number=ussd_lottery_payment_instance.user.phone_number,
                    amount=ussd_lottery_payment_instance.amount,
                    channel="WEB",
                    reference=transaction_ref,
                    transaction_type="CREDIT",
                )

                wallet_payload = {
                    "transaction_from": "GAME_PLAY_REVERSAL",
                    "game_type": ussd_lottery_payment_instance.lottery_type,
                    "game_play_id": ussd_lottery_payment_instance.game_play_id,
                }
                UserWallet.fund_wallet(
                    user=ussd_lottery_payment_instance.user,
                    amount=ussd_lottery_payment_instance.amount,
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="GAME_PLAY_WALLET",
                    from_telco_channel=ussd_lottery_payment_instance.played_via_telco_channel,
                    **wallet_payload,
                )

            return {"status": "failed", "message": "Insufficient amount charged"}

        else:
            prediction_subscription = cls.objects.filter(game_pay_id=ussd_lottery_payment_instance.game_play_id).first()
            if not prediction_subscription:
                return None

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=user_wallet.user.phone_number,
                amount=game_pay_amount,
                channel="WEB",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="DEBIT",
            )

            wallet_payload = {"transaction_from": "PREDICTION_SUBSCRIPTION_GAME_PLAY"}

            UserWallet.deduct_wallet(
                user=user_wallet.user,
                amount=game_pay_amount,
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="GAME_PLAY_WALLET",
                from_telco_channel=ussd_lottery_payment_instance.played_via_telco_channel,
                **wallet_payload,
            )
            prediction_subscription.is_paid = True
            prediction_subscription.paid_date = timezone.now()
            if prediction_subscription.channel == "WEB":
                prediction_subscription.amount_paid = prediction_subscription.amount
                prediction_subscription.is_paid = True
                prediction_subscription.save()

                ussd_lottery_payment_instance.amount_paid = prediction_subscription.amount
                ussd_lottery_payment_instance.save()
            else:
                prediction_subscription.amount_paid = prediction_subscription.amount
                prediction_subscription.is_paid = True
                prediction_subscription.save()

            payment_receipt_sms(
                phone_number=ussd_lottery_payment_instance.user.phone_number,
                amount=game_pay_amount,
            )

    @classmethod
    def send_prediction_to_telco_subscriber(cls, phone_number, is_a_new_subscription=False, network="MTN", number_of_retries=1):
        from broad_base_communication.bbc_helper import BBCTelcoAggregator

        try:
            user_profile = UserProfile.objects.get(phone_number=phone_number)
        except UserProfile.DoesNotExist:
            user_profile = UserProfile.objects.create(phone_number=phone_number)

        get_active_subscription_instance = cls.objects.filter(user_profile=user_profile, is_active=True, played_from_telco_channel=True).last()

        if get_active_subscription_instance is not None:
            list_of_games_received = get_active_subscription_instance.list_of_games_received
            if list_of_games_received is None:
                list_of_games_received = []

            _today = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
            todays_date = _today.date()

            prediction_created_today = PredictionTable.objects.filter(
                date_created__date=todays_date, is_predicted=True, api_type="RAPID_API"
            ).exclude(fixture_id__in=list_of_games_received)

            if prediction_created_today.exists():
                # db_ids = prediction_created_today.values_list("id", flat=True)

                # two_random_ids = random.sample(list(db_ids), 2)

                # result_objects = PredictionTable.objects.filter(id__in = two_random_ids)

                result_objects = prediction_created_today.order_by("?")[:2]

                fixture_ids_for_the_returned_objects = result_objects.values_list("fixture_id", flat=True)

                for i in fixture_ids_for_the_returned_objects:
                    list_of_games_received.append(i)

                message_to_subscriber = "Your predictions for today are ready. \n\n"

                for _prediction in result_objects:
                    if _prediction.correct_score is True:
                        message_to_subscriber += f"{_prediction.home_team} vs {_prediction.away_team} \n"
                        if _prediction.predicted_winner == _prediction.home_team:
                            message_to_subscriber += "Prediction: 1 (HOME WIN) \n\n"
                        elif _prediction.predicted_winner == _prediction.away_team:
                            message_to_subscriber += "Prediction: 2 (AWAY WIN) \n\n"
                    else:
                        message_to_subscriber += f"{_prediction.home_team} vs {_prediction.away_team} \n"
                        if _prediction.predicted_winner == _prediction.home_team:
                            message_to_subscriber += "Prediction: 1x (HOME WIN or DRAW) \n\n"
                        elif _prediction.predicted_winner == _prediction.away_team:
                            message_to_subscriber += "Prediction: 2x (AWAY WIN or DRAW) \n\n"

                    # message_to_subscriber += f"{_prediction.home_team} {_prediction.predicted_fulltime_home_score} : {_prediction.predicted_fulltime_away_score} {_prediction.away_team} \n"

                sms_payload = {
                    "phone_number": phone_number,
                    # "sender_name": "WINWISE",
                    "sender_name": "20144",
                    "message": message_to_subscriber,
                }

                broad_base_helper = BBCTelcoAggregator()

                sms_payload["use_json_format"] = True

                broad_base_helper.bbc_send_sms(**sms_payload)

                get_active_subscription_instance.list_of_games_received = list_of_games_received
                get_active_subscription_instance.save()

            else:
                create_rapid_prediction_matches()

                rapid_api_prediction()

                return cls.send_prediction_to_telco_subscriber(phone_number)

class SoccerPredictionLink(models.Model):
    link_id = models.CharField(max_length=255, blank=True, null=True)
    prediction_matches = models.JSONField(blank=True, null=True, default=dict)
    web_link = models.URLField(blank=True, null=True)
    link_expiry_date = models.DateField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    is_free = models.BooleanField(default=False)

    class Meta:
        verbose_name = "SOCCER PREDICTION LINK"
        verbose_name_plural = "SOCCER PREDICTION LINKS"