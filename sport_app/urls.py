from django.urls import path

from sport_app import views

english_pl = [
    path("pl_week/", views.EPLWeekMatchAPIView.as_view()),
]
laliga = [
    path("laliga_week/", views.LaligaWeekMatchAPIView.as_view()),
]
champions_league = [
    path("champions_league_week/", views.ChampionsLeagueWeekMatchAPIView.as_view()),
]
europa_league = [
    path("europa_league_week/", views.EuropaLeagueWeekMatchAPIView.as_view()),
]
world_cup = [
    path("world_cup_week/", views.WorldCupWeekMatchAPIView.as_view()),
]
short_code = [
    path("team_short_code/", views.TeamShortCodeAPIView.as_view()),
    path("available_league/", views.GetAvailableLeaguesAPIView.as_view()),
    path("premier_league/", views.PremierLeagueAPIView.as_view()),
    path("laliga/", views.LaligaAPIView.as_view()),
    path("europa_league/", views.EuropaLeagueAPIView.as_view()),
    path("champions_league/", views.ChampionsLeagueAPIView.as_view()),
    path("world_cup/", views.WorldCupAPIView.as_view()),
    path("score_data/", views.ScoreDataAPIView.as_view()),
]

soccer_prediction = [
    path("predict_score/", views.SoccerCashGameSelect.as_view()),
    path("goal_scorer/", views.GetGoalScorerApiView.as_view()),
    path("predict_goal_scorer/", views.PredictGoalScorerApiView.as_view()),
    path("get_player/", views.GetAllPlayersAPIView.as_view()),
    path("get_player_goal/", views.GetPlayerGoalsAPIView.as_view()),
    path("get_team_finalist/", views.GetTeamFinalistAPIView.as_view()),
    path("predict_team_finalist/", views.PredictTeamFinalistApiView.as_view()),
    path("get_dual_team_finalist/", views.GetDualTeamFinalistAPIView.as_view()),
    path("get_world_cup_teams/", views.WorldCupTeamsAPIView.as_view()),
    path("predict_team_finalist/dual/", views.PredictDualTeamFinalistApiView.as_view()),
    path("get_single_prediction/", views.PredictionApiView.as_view()),
]


# VIRTUAL SPORTS URLS #
VIRTUAL_SPORTS_URLS = [
    path("get_leagues/", views.GetVirtualLeagues.as_view()),
    path("team_pairing/<str:channel>/", views.VirtualTeamsView.as_view()),
    path(
        "virtual_soccer_result/<str:channel>/",
        views.VirtualSoccerResultSearchView.as_view(),
    ),
]

soccer_odds_prediction = [
    path("prediction_type/", views.PredictOddTypeAPIView.as_view()),
    path("prediction_odds/", views.PredictOddAPIView.as_view()),
    path("web_soccer_prediction/", views.WebPredictionLinkAPIView.as_view()),
    path("free_web_soccer_prediction/", views.FreeWebPredictionLinkAPIView.as_view()),
    path("upcoming_match/", views.UpcomingMatchesAPIView.as_view()),
    # path("pay_prediction_subscription/", views.PayForPredictionSubscription.as_view()),
]

urlpatterns = [
    *english_pl,
    *laliga,
    *champions_league,
    *europa_league,
    *world_cup,
    *short_code,
    *soccer_prediction,
    *VIRTUAL_SPORTS_URLS,
    *soccer_odds_prediction,
]
