from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from payout_process.models import PayoutProcessFirstStep, PayoutProcessSecondStep


# Register your models here.
class PayoutProcessFirstStepResource(resources.ModelResource):
    class Meta:
        model = PayoutProcessFirstStep


class PayoutProcessSecondStepResource(resources.ModelResource):
    class Meta:
        model = PayoutProcessSecondStep


class PayoutProcessFirstStepResourceAdmin(ImportExportModelAdmin):
    resource_class = PayoutProcessFirstStepResource

    date_hierarchy = "created_at"

    search_fields = ["user_phone_number", "user_name", "payout_referece"]

    list_filter = ["created_at", "status"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PayoutProcessSecondStepResourceAdmin(ImportExportModelAdmin):
    resource_class = PayoutProcessSecondStepResource

    date_hierarchy = "created_at"

    search_fields = ["user_phone_number", "user_name", "payout_referece"]

    list_filter = ["created_at", "status"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(PayoutProcessFirstStep, PayoutProcessFirstStepResourceAdmin)
admin.site.register(PayoutProcessSecondStep, PayoutProcessSecondStepResourceAdmin)
