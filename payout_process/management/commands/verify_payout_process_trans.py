from django.core.management.base import BaseCommand

from main.models import PayoutTransactionTable
from payout_process.models import PayoutProcessFirstStep
from payout_process.payout_process_enums import PayoutProcessStatus


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        pending_queryset = PayoutProcessFirstStep.objects.filter(status=PayoutProcessStatus.PENDING)
        for instance in pending_queryset:
            transaction_queryset = PayoutTransactionTable.objects.filter(
                game_play_id = instance.game_play_id,
                phone = instance.user_phone_number,
            )
            if transaction_queryset.exists():
                transaction = transaction_queryset.last()
                if transaction.is_verified is True:
                    if transaction.disbursed is False:
                        instance.status = PayoutProcessStatus.FAILED
                        instance.save()
                    else:
                        instance.status = PayoutProcessStatus.SUCCESSFUL
                        instance.save()

            else:
                instance.status = PayoutProcessStatus.FAILED
                instance.save()


                

            # Perform any necessary operations on the pending instances