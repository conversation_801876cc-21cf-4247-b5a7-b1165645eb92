from django.core.management.base import BaseCommand

from payout_process.models import PayoutProcessFirstStep

class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        was_successful = PayoutProcessFirstStep.created_record(
            game_play_id = "009",
            amount = 500,
            user_phone_number = "08012345678",
            user_name = "<PERSON>",
            payout_referece = "PAY12345678957898",
        )
        if was_successful is False:
            print("Failed to create record in PayoutProcessFirstStep")
        else:
            print("Record created successfully in PayoutProcessFirstStep")

          