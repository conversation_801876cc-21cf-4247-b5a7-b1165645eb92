from django.db import models
from django.db.models import Q, UniqueConstraint

from payout_process.payout_process_enums import PayoutProcessStatus


# Create your models here.
class PayoutProcessFirstStep(models.Model):
    game_play_id = models.CharField(max_length=255)
    amount = models.FloatField(default=0.0)
    user_phone_number = models.CharField(max_length=20)
    user_name = models.CharField(max_length=100)
    payout_referece = models.Char<PERSON>ield(max_length=255, unique=True)
    click_id = models.Char<PERSON>ield(max_length=255, unique=True, blank=True, null=True)
    status = models.Char<PERSON>ield(max_length=20, choices=PayoutProcessStatus.choices, default=PayoutProcessStatus.PENDING)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"GAME ID: {self.game_play_id}, Status: {self.status}"

    class Meta:
        verbose_name = "PAYOUT PROCESS FIRST STEP"
        verbose_name_plural = "PAYOUT PROCESS FIRST STEPS"

        constraints = [
            UniqueConstraint(
                fields=["game_play_id"],
                condition=Q(
                    status__in=[
                        PayoutProcessStatus.PENDING,
                        PayoutProcessStatus.SUCCESSFUL,
                    ]
                ),
                name="unique_payout_process_first_step_game_play_id",
            )
        ]

    @classmethod
    def created_record(cls, game_play_id, amount, user_phone_number, user_name, payout_referece, click_id):
        try:
            cls.objects.create(
                game_play_id=game_play_id,
                amount=amount,
                user_phone_number=user_phone_number,
                payout_referece=payout_referece,
                user_name=user_name,
                click_id=click_id,
            )

            PayoutProcessSecondStep.objects.create(
                game_play_id=game_play_id,
                amount=amount,
                user_phone_number=user_phone_number,
                user_name=user_name,
                payout_referece=payout_referece,
                status="SUCCESSFUL",
                click_id=click_id,
            )

            return True
        except Exception as e:
            PayoutProcessSecondStep.objects.create(
                game_play_id=game_play_id,
                amount=amount,
                user_phone_number=user_phone_number,
                user_name=user_name,
                payout_referece=payout_referece,
                status=str(e),
                click_id=click_id,
            )

            return False

    @classmethod
    def update_payout_process_one_as_successful(cls, payout_referece):
        try:
            cls.objects.filter(payout_referece=payout_referece).update(status=PayoutProcessStatus.SUCCESSFUL)
        except Exception:
            pass
        return True

    @classmethod
    def update_payout_process_one_as_failed(cls, payout_referece):
        try:
            cls.objects.filter(payout_referece=payout_referece).update(status=PayoutProcessStatus.FAILED)
        except Exception:
            pass
        return True


class PayoutProcessSecondStep(models.Model):
    game_play_id = models.CharField(max_length=255)
    amount = models.FloatField(default=0.0)
    user_phone_number = models.CharField(max_length=20)
    user_name = models.CharField(max_length=100)
    payout_referece = models.CharField(max_length=255, unique=True)
    click_id = models.CharField(max_length=255, unique=True, blank=True, null=True)
    status = models.CharField(max_length=300)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Transaction ID: {self.transaction_id}, Status: {self.status}"

    class Meta:
        verbose_name = "PAYOUT PROCESS SECOND STEP"
        verbose_name_plural = "PAYOUT PROCESS SECOND STEPS"
