from django.contrib import admin
from django.contrib.admin.models import LogEntry
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from django.contrib import admin, messages
from django.db.models import Sum

from decisioning_engine.draw import SalaryForLifeDraw
from main.api.api_lottery_helpers import generate_game_play_id
from main.dashboard_filters import StakeAmountFilter
from main.forms import FreemiumFixturesCleanForm, LotteryBatchCleanForm
from main.models import (
    BalanceBonus,
    BankerConstant,
    ConstantVariable,
    ConstantVariableParam,
    ContactUsForm,
    CreditCard,
    DisbursementTable,
    DrawData,
    DrawWinnersLog,
    ErroneousTransferRefundLog,
    Excesses,
    GamePlayEscrow,
    InstantCashoutPendingWinning,
    IPWhitelist,
    Jackpot,
    JackpotConstantVariable,
    JackpotWinner,
    LotteryBatch,
    LotteryGlobalJackPot,
    LotteryModel,
    LotteryTicketPrice,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    MaxWithdrawalThresold,
    PabblyCallBack,
    PaymentCollectionDetail,
    PaymentTransaction,
    PayoutTransactionTable,
    RapidFireWinning,
    S4LCombos,
    SalaryForLifeParticipant,
    SalaryForLifeSponsor,
    ScratchCardPartnership,
    Seeder,
    TestimonalTable,
    UserProfile,
    Waitlist,
    WatuPayCallBack,
    WemaInstantPaymentAccounts,
    WovenAccountDetail,
    WovenCallBack,
    DrawLog,
    DrawTracker
)
from main.tasks import celery_sms_for_instant_cash_winners
from pos_app.pos_helpers import machine_number_serializer
from pos_app.utils import serialize_ticket
from wyse_ussd.tasks import salary_for_life_and_instant_cashout_won_sms_on_telco

# Register your models here.


# LogEntry
@admin.register(LogEntry)
class LogEntryAdmin(admin.ModelAdmin):
    date_hierarchy = "action_time"

    list_filter = [
        # 'user',
        "content_type",
        "action_flag",
    ]

    search_fields = ["object_repr", "change_message"]

    list_display = [
        "action_time",
        "user",
        "content_type",
        # "object_link",
        "object_id",
        "action_flag",
        "object_repr",
    ]

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser


# RESOURCES
class LotteryModelResource(resources.ModelResource):
    class Meta:
        model = LotteryModel

class DrawTrackerResource(resources.ModelResource):
    class Meta:
        model = DrawTracker


class LotteryBatchResource(resources.ModelResource):
    class Meta:
        model = LotteryBatch


class PaymentTransactionTableResource(resources.ModelResource):
    class Meta:
        model = PaymentTransaction


class PaymentCollectionDetailResource(resources.ModelResource):
    class Meta:
        model = PaymentCollectionDetail


class WatuPayCallBackResource(resources.ModelResource):
    class Meta:
        model = WatuPayCallBack


class WovenCallBackResource(resources.ModelResource):
    class Meta:
        model = WovenCallBack


class PabblyCallBackResource(resources.ModelResource):
    class Meta:
        model = PabblyCallBack


class LotteryWinnersTableResource(resources.ModelResource):
    class Meta:
        model = LotteryWinnersTable


class DisbursementTableResource(resources.ModelResource):
    class Meta:
        model = DisbursementTable


class UserProfileResource(resources.ModelResource):
    class Meta:
        model = UserProfile


class ConstantVariableResource(resources.ModelResource):
    class Meta:
        model = ConstantVariable


class ConstantVariableParamResource(resources.ModelResource):
    class Meta:
        model = ConstantVariableParam


class WovenAccountDetailResource(resources.ModelResource):
    class Meta:
        model = WovenAccountDetail


class ExcessesResource(resources.ModelResource):
    class Meta:
        model = Excesses


class PayoutTransactionTableResource(resources.ModelResource):
    class Meta:
        model = PayoutTransactionTable


class TestimonalTableResource(resources.ModelResource):
    class Meta:
        model = TestimonalTable


class CreditCardResource(resources.ModelResource):
    class Meta:
        model = CreditCard


class LottoWinnersResource(resources.ModelResource):
    class Meta:
        model = LottoWinners


class JackpotResource(resources.ModelResource):
    class Meta:
        model = Jackpot


class JackpotWinnerResource(resources.ModelResource):
    class Meta:
        model = JackpotWinner


class LotteryGlobalJackPotResource(resources.ModelResource):
    class Meta:
        model = LotteryGlobalJackPot


class InstantCashoutPendingWinningResource(resources.ModelResource):
    class Meta:
        model = InstantCashoutPendingWinning


class LotteryTicketPriceResource(resources.ModelResource):
    class Meta:
        model = LotteryTicketPrice


class JackpotConstantVariableResource(resources.ModelResource):
    class Meta:
        model = JackpotConstantVariable


class BankerConstantResource(resources.ModelResource):
    class Meta:
        model = BankerConstant


class IPWhitelistResource(resources.ModelResource):
    class Meta:
        model = IPWhitelist


class S4LCombosResource(resources.ModelResource):
    class Meta:
        model = S4LCombos


class MaxWithdrawalThresoldResource(resources.ModelResource):
    class Meta:
        model = MaxWithdrawalThresold


class WaitlistResource(resources.ModelResource):
    class Meta:
        model = Waitlist


class WemaInstantPaymentAccountsResource(resources.ModelResource):
    class Meta:
        model = WemaInstantPaymentAccounts


class SalaryForLifeSponsorResource(resources.ModelResource):
    class Meta:
        model = SalaryForLifeSponsor


class SalaryForLifeParticipantResource(resources.ModelResource):
    class Meta:
        model = SalaryForLifeParticipant


class ContactUsFormResource(resources.ModelResource):
    class Meta:
        model = ContactUsForm


class ErroneousTransferRefundLogResource(resources.ModelResource):
    class Meta:
        model = ErroneousTransferRefundLog


class ScratchCardPartnershipResource(resources.ModelResource):
    class Meta:
        model = ScratchCardPartnership



class DrawWinnersLogResource(resources.ModelResource):
    class Meta:
        model = DrawWinnersLog


class GamePlayEscrowResource(resources.ModelResource):
    class Meta:
        model = GamePlayEscrow


#######################################################################
#
# ADMINS
class LotteryModelResourceAdmin(ImportExportModelAdmin):
    resource_class = LotteryModelResource
    raw_id_fields = ("user_profile", "agent_profile")
    autocomplete_fields = ["batch", "user_profile"]
    search_fields = [
        "id",
        "game_play_id",
        "phone",
        "band",
        "batch__batch_uuid",
        "pool",
        "lucky_number",
    ]
    list_filter = ("date", "telco_network", "paid_date", "paid", "channel", "lottery_type")
    date_hierarchy = "date"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

class DrawTrackerResourceAdmin(ImportExportModelAdmin):
    resource_class = DrawTrackerResource
    list_filter = ("date", "game_type")
    date_hierarchy = "date"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LotteryBatchResourceAdmin(ImportExportModelAdmin):
    form = LotteryBatchCleanForm
    resource_class = LotteryBatchResource
    search_fields = ["batch_uuid", "RTO", "RTP", "total_revenue"]
    list_filter = ("lottery_type", "created_date", "is_active")
    date_hierarchy = "created_date"

    def get_list_display(self, request):
        resources = [field.name for field in self.model._meta.concrete_fields]
        resources.remove("list_of_ticket_numbers")

        return resources


    actions = [
        "filterbankwinners",
        "filterretailsalaryforlifewinners",
        "run_banker_draw",
        "run_salary_for_life_draw",
    ]

    @admin.action(description="MANUALLY RUN DRAW BANKER WINNING FILTER")
    def filterbankwinners(modeladmin, request, queryset):

        # messages.success(request, "successfully filtered banker winners")


        for obj in queryset:
            if obj.manually_filtered_winnings is True:
                messages.error(request, "Batch has already been filtered")
                continue
            if obj.lottery_type == "BANKER":


                lottery_qs = LottoTicket.objects.filter(
                    batch=obj,
                    paid=True,
                    channel = "POS_AGENT"
                )

                plays = list(
                    map(
                        lambda x: (
                            x.number_of_ticket,
                            [int(i) for i in x.ticket.split(",")],
                            x.rtp,
                        ),
                        lottery_qs,
                    )
                )

                obj.manually_filtered_winnings = True
                obj.save()

                prices = LottoTicket.construct_earning(lottery_qs)

                rtp = lottery_qs.distinct().aggregate(Sum("effective_rtp"))["effective_rtp__sum"]
                real_rtp = lottery_qs.distinct().aggregate(Sum("rtp"))["rtp__sum"]  # FALL BACK  REAL RTP WHEN NO RTP
                rtp = rtp or real_rtp
                rtp = rtp or 0

                salary_for_life_jackpot_instance = LotteryGlobalJackPot.objects.filter(is_active=True).last()

                if salary_for_life_jackpot_instance is not None:
                    jackpot_amount = salary_for_life_jackpot_instance.threshold
                else:
                    jackpot_amount = 1000.00


                best_match_combo = machine_number_serializer(obj.lottery_winner_ticket_number)
                best_match_combo = best_match_combo[0]
                # best_match_combo = ",".join(str(i) for i in best_match_combo)

                # print("best_match_combo", best_match_combo, "\n\n")


                filterd_winners = SalaryForLifeDraw.filter_banker_winnings(best_match_combo, plays, prices, jackpot_amount)

                # total_winning = SalaryForLifeDraw.deep_sum(filterd_winners)

                if filterd_winners:

                    for ticker_won in filterd_winners:
                        match_win_type = ticker_won[0]
                        match_ticket = ticker_won[1]
                        amount_won = ticker_won[2]
                        _orignal_stake_amount = ticker_won[1][2]

                        ticket = list(match_ticket)[1]

                        ticket_db_filter_qs = LottoTicket.objects.filter(
                            batch=obj,
                            ticket=serialize_ticket(ticket),
                            rtp=_orignal_stake_amount,
                        )


                        win_type = "PERM_4"

                        if match_win_type == 4:
                            win_type = "PERM_4"
                        elif match_win_type == 3:
                            win_type = "PERM_3"
                        elif match_win_type == 2:
                            win_type = "PERM_2"


                        amount_won = prices[ticker_won[1][2]][ticker_won[0]]


                        if ticket_db_filter_qs:
                            for lotto_ticket_instance in ticket_db_filter_qs:

                                amount_won = ticker_won[2]

                                if LottoWinners.objects.filter(game_play_id__iexact=lotto_ticket_instance.game_play_id).exists():
                                    pass
                                else:
                                    LottoWinners.create_lotto_winner_obj(
                                        lottery=lotto_ticket_instance,
                                        batch=obj,
                                        phone_number=lotto_ticket_instance.user_profile.phone_number,
                                        ticket=ticket,
                                        win_type="ORDINARY_WINNER",
                                        match_type=win_type,
                                        lotto_type="BANKER",
                                        game_play_id=lotto_ticket_instance.game_play_id,
                                        stake_amount=lotto_ticket_instance.stake_amount,
                                        earning=amount_won,
                                        channel_played_from=lotto_ticket_instance.channel,
                                        run_batch_id=obj.batch_uuid,
                                    )

                messages.success(request, "successfully filtered banker winners")
            else:
                messages.error(request, "Batch is not a banker batch")

    @admin.action(description="MANUALLY FILTER RETAIL SALARY FOR LIFE WINNINGS")
    def filterretailsalaryforlifewinners(modeladmin, request, queryset):
        messages.success(request, "successfully filtered salary for life winners")
        # for obj in queryset:
        #     if obj.manually_filtered_winnings is True:
        #         messages.error(request, "Batch has already been filtered")
        #         continue
        #     if obj.lottery_type == "SALARY_FOR_LIFE":
        #         lottery_qs = LottoTicket.objects.filter(
        #             batch=obj,
        #             paid=True,
        #             channel = "POS_AGENT"
        #         )

        #         serialized_system_pick_num = machine_number_serializer(obj.lottery_winner_ticket_number)
        #         winning_number = []

        #         obj.manually_filtered_winnings = True
        #         obj.save()

        #         # print("serialized_system_pick_num", serialized_system_pick_num, "\n\n")

        #         if len(serialized_system_pick_num) > 0:
        #             individual_lists = [item for item in serialized_system_pick_num]
        #             winning_number = [x for x in individual_lists][0]


        #             for ticket in lottery_qs:
        #                 try:
        #                     LottoWinners.objects.get(lottery = ticket)
        #                     continue
        #                 except LottoWinners.DoesNotExist:
        #                     # check matching
        #                     ticket_numbers = [int(num.strip()) for num in ticket.ticket.split(',')]

        #                     matches = [num for num in ticket_numbers if num in winning_number]
        #                     match_count = len(matches)


        #                     ticket_count = len(LottoTicket.objects.filter(batch=obj,paid=True,channel = "POS_AGENT", game_play_id = ticket.game_play_id))


        #                     if match_count > 1:
        #                         amount_won = LottoTicket.salary_for_life_win_per_line(
        #                             match = match_count,
        #                             number_of_line = ticket_count
        #                         )

        #                         win_type = "PERM_4"

        #                         if match_count == 4:
        #                             win_type = "PERM_4"
        #                         elif match_count == 3:
        #                             win_type = "PERM_3"
        #                         elif match_count == 2:
        #                             win_type = "PERM_2"


        #                         _draw_batch_id = generate_game_play_id()


        #                         LottoWinners.create_lotto_winner_obj(
        #                             batch=ticket.batch,
        #                             phone_number=ticket.user_profile.phone_number,
        #                             ticket=[int(i) for i in str(ticket.ticket).split(",")],
        #                             lottery=ticket,
        #                             win_type="ORDINARY_WINNER",
        #                             match_type=win_type,
        #                             lotto_type="SALARY_FOR_LIFE",
        #                             game_play_id=ticket.game_play_id,
        #                             stake_amount=ticket.stake_amount,
        #                             earning=amount_won,
        #                             channel_played_from=ticket.channel,
        #                             run_batch_id=_draw_batch_id,
        #                             played_via_telco_channel=ticket.played_via_telco_channel,
        #                         )


        #         # print("winning_number", winning_number, "\n\n")

        # messages.success(request, "successfully filtered salary for life winners")

    @admin.action(description="RUN BANKER DRAW FOR SELECTED BATCHES")
    def run_banker_draw(modeladmin, request, queryset):
        from main.models import LottoTicket
        from pprint import pprint
        for obj in queryset:
            try:
                tickets = LottoTicket.objects.filter(
                    batch=obj,
                    date__gte=obj.batch_start,
                    date__lte=obj.batch_end,
                    played_via_telco_channel=False,
                    lottery_type="BANKER"
                )

                # Update tickets to inherit the batch
                tickets.update(batch=obj)

                ticket_numbers = [t.ticket for t in tickets]
                pprint({"batch": obj.batch_uuid, "tickets": ticket_numbers})
                LottoTicket.banker_draw(batch_id=obj.id, run_manually=True)
                messages.success(request, f"Banker draw run for batch {obj.batch_uuid}")
            except Exception as e:
                messages.error(request, f"Failed to run banker draw for batch {obj.batch_uuid}: {e}")

    @admin.action(description="RUN SALARY FOR LIFE DRAW FOR SELECTED BATCHES")
    def run_salary_for_life_draw(modeladmin, request, queryset):
        from main.models import LottoTicket
        from pprint import pprint
        for obj in queryset:
            try:
                # Filter only tickets with lottery type "SALARY_FOR_LIFE"
                tickets = LottoTicket.objects.filter(
                    batch=obj,
                    date__gte=obj.batch_start,
                    date__lt=obj.batch_end,
                    played_via_telco_channel=False,
                    lottery_type="SALARY_FOR_LIFE"  # Added filter for lottery type
                )

                # Update tickets to inherit the batch
                tickets.update(batch=obj)

                ticket_numbers = [t.ticket for t in tickets]
                pprint({"batch": obj.batch_uuid, "tickets": ticket_numbers})

                # Run salary for life draw with correct parameters
                LottoTicket.salary_for_life_draw_original_multiprocessing(
                    batch_id=obj.id,
                    include_telco_tickets=False
                )

                messages.success(request, f"Salary for Life draw run for batch {obj.batch_uuid}")
            except Exception as e:
                messages.error(request, f"Failed to run Salary for Life draw for batch {obj.batch_uuid}: {e}")


class PaymentTransactionTableResourceAdmin(ImportExportModelAdmin):
    resource_class = PaymentTransactionTableResource
    search_fields = [
        "lottery_player__phone_number",
        "payment_channel",
        "liberty_transaction_reference",
        "provider_transaction_reference",
        "status",
    ]
    autocomplete_fields = ["lottery_player"]
    list_filter = ("date_paid", "has_paid")
    date_hierarchy = "date_paid"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PaymentCollectionDetailResourceAdmin(ImportExportModelAdmin):
    resource_class = PaymentCollectionDetailResource
    search_fields = [
        "lottery_player__phone_number",
        "vnuban",
        "acct_name",
        "pabbly_plan_id",
    ]
    autocomplete_fields = ["lottery_player"]
    list_filter = ("date_created",)
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WatuPayCallBackResourceAdmin(ImportExportModelAdmin):
    resource_class = WatuPayCallBackResource

    # search_fields = ["batch_uuid",]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WovenCallBackResourceAdmin(ImportExportModelAdmin):
    resource_class = WovenCallBackResource
    search_fields = [
        "transaction__provider_transaction_reference",
    ]
    date_hierarchy = "date"
    raw_id_fields = [
        "transaction",
    ]
    date_hierarchy = "date"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PabblyCallBackResourceAdmin(ImportExportModelAdmin):
    resource_class = PabblyCallBackResource

    # readonly_fields = ['lottery_player', 'vnuban', 'acct_name', 'bank_name', 'payload']

    # search_fields = ["batch_uuid",]

    date_hierarchy = "date"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DisbursementTableResourceAdmin(ImportExportModelAdmin):
    autocomplete_fields = ["lottery_batch", "lotery_winner"]
    resource_class = DisbursementTableResource
    search_fields = [
        "player_phone_num",
        "lottery_batch__batch_uuid",
        "payout_account_num",
        "payout_account_name",
        "payout_bank_name",
    ]
    list_filter = ("date_created", "is_disbursed", "stattus")
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LotteryWinnersTableResourceAdmin(ImportExportModelAdmin):
    resource_class = LotteryWinnersTableResource
    search_fields = [
        "batch__batch_uuid",
        "game_play_id",
        "run_batch_id",
        "phone_number",
        "win_type",
        "pool",
        "share",
        "earning",
        "total_jackpot_amount",
    ]
    list_filter = ("date_won", "lottery_source_tag", "win_type")
    date_hierarchy = "date_won"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserProfileResourceAdmin(ImportExportModelAdmin):
    resource_class = UserProfileResource
    search_fields = [
        "email",
        "phone_number",
        "first_name",
        "last_name",
        "middle_name",
        "account_num",
        "account_name",
        "bank_name",
        "bank_code",
        "loandisk_player_id",
        "ministry",
    ]

    list_filter = ("date_added", "network_provider", "on_loandisk")

    date_hierarchy = "date_added"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ConstantVariableResourceAdmin(ImportExportModelAdmin):
    resource_class = ConstantVariableResource
    form = FreemiumFixturesCleanForm

    # search_fields = ["phone_number",]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ConstantVariableParamResourceAdmin(ImportExportModelAdmin):
    resource_class = ConstantVariableParamResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WovenAccountDetailResourceAdmin(ImportExportModelAdmin):
    resource_class = WovenAccountDetailResource
    search_fields = ["phone_number", "vnuban", "acct_name", "account_ref"]
    list_filter = ("date", "is_active", "acct_provider")
    date_hierarchy = "date"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ExcessesResourceAdmin(ImportExportModelAdmin):
    resource_class = ExcessesResource
    search_fields = ["phone_number", "in_excess_amount", "last_played_unique_id"]
    list_filter = ("date_added",)
    date_hierarchy = "date_added"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PayoutTransactionTableResourceAdmin(ImportExportModelAdmin):
    resource_class = PayoutTransactionTableResource
    search_fields = [
        "name",
        "batch__batch_uuid",
        "game_play_id",
        "source",
        "phone",
        "payout_trans_ref",
        "source_unique_ref",
        "disbursement_unique_id",
    ]
    list_filter = (
        "channel",
        "status",
        "telco_network",
        "source",
        "date_added",
        "disbursed",
        "is_verified",
        "payout_verified",
        "type_of_transaction",
        "source_wallet",
        "recipient_wallet",
    )

    list_display = [
        "id",
        "source",
        "type_of_transaction",
        "phone",
        "name",
        "joined_since",
        "amount",
        "balance_before",
        "balance_after",
        "disbursed",
        "is_late_withdrawal",
        "channel",
        "game_play_id",
        "unique_game_play_id",
        "source_wallet",
        "recipient_wallet",
        "telco_network",
        "is_verified",
        "is_agent_commission",
        "is_super_agent_commission",
        "payment_initiated",
        "payout_trans_ref",
        "disbursement_unique_id",
        "source_unique_ref",
        "payout_payload",
        "source_response_payload",
        "date_added",
        "last_updated",
    ]
    date_hierarchy = "date_added"

    actions = [
        "markdataasunverified",
        "removeuniquegameplayid",
        "markdataasagentcommission",
        "markdataassuperagentcommission"
    ]

    @admin.action(description="MARK DATA AS UNVERIFIED")
    def markdataasunverified(modeladmin, request, queryset):
        for obj in queryset:
            if obj.disbursed is True:
                continue

            obj.is_verified = False
            obj.save()

        messages.success(request, "Successfully")
        
        
    @admin.action(description="MARK DATA AS AGENT COMMISSION")
    def markdataasagentcommission(modeladmin, request, queryset):
        for obj in queryset:
            if obj.type_of_transaction == "COMMISSION":
                obj.is_agent_commission = True
                obj.save()

        messages.success(request, "Successfully")
        
    @admin.action(description="MARK DATA AS SUPER AGENT COMMISSION")
    def markdataassuperagentcommission(modeladmin, request, queryset):
        for obj in queryset:
            if obj.type_of_transaction == "COMMISSION":
                obj.is_super_agent_commission = True
                obj.save()

        messages.success(request, "Successfully")


    @admin.action(description="REMOVE UNIQUE GAME PLAY ID")
    def removeuniquegameplayid(modeladmin, request, queryset):
        for obj in queryset:

            obj.unique_game_play_id = None
            obj.save()

        messages.success(request, "Successfully")




    # def get_list_display(self, request):
    #     return [field.name for field in self.model._meta.concrete_fields]


class TestimonalTableResourceAdmin(ImportExportModelAdmin):
    resource_class = TestimonalTableResource
    search_fields = ["user__phone_number", "message", "date_added"]
    autocomplete_fields = ["user"]

    date_hierarchy = "date_added"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoTicketResource(resources.ModelResource):
    class Meta:
        model = LottoTicket


class LottoTicketResourceAdmin(ImportExportModelAdmin):
    # autocomplete_fields = [
    #     "user_profile",
    # ]

    raw_id_fields = ("user_profile", "agent_profile", "batch")
    resource_class = LottoTicketResource
    search_fields = [
        "id",
        "batch__batch_uuid",
        "user_profile__phone_number",
        "agent_profile__email",
        "ticket",
        "game_play_id",
    ]
    list_filter = (
        "date",
        "channel",
        "telco_network",
        "lottery_type",
        "paid",
        "is_agent",
        "played_via_telco_channel",
        "instant_cashout_drawn",
        "pos_instant_cashout_drawn",
        "icash_counted",
        "icash_2_counted",
        "is_duplicate",
        "game_type",
        "s4l_drawn",
        "content_delivery_sms_sent",
    )
    date_hierarchy = "date"

    def get_list_display(self, request):
        data = [field.name for field in self.model._meta.concrete_fields]
        data.remove("user_profile")
        data.remove("agent_profile")
        # data.remove("batch")

        return data


class CreditCardResourceAdmin(ImportExportModelAdmin):
    resource_class = CreditCardResource
    search_fields = [
        "card_email",
        "user__phone_number",
    ]
    # list_filter = (
    #     ('transaction_from',)
    # )
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LottoWinnersResourceAdmin(ImportExportModelAdmin):
    resource_class = LottoWinnersResource
    search_fields = ["game_play_id", "phone_number"]

    list_filter = (
        "win_type",
        "win_flavour",
        "date_won",
        "match_type",
        "channel_played_from",
        "lotto_type",
        "is_illusion_winning",
        "played_via_telco_channel",
    )
    date_hierarchy = "date_won"
    raw_id_fields = ["batch", "lottery"]
    autocomplete_fields = ["batch", "lottery"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


    def get_queryset(self, request):
        queryset = super().get_queryset(request)

        # Check if user is a superuser
        if not request.user.is_superuser:
            import datetime
            today = datetime.date.today()
            first_day_of_month = today.replace(day=1)

            queryset = queryset.filter(date_won__gte=first_day_of_month)

        return queryset


class JackpotResourceAdmin(ImportExportModelAdmin):
    resource_class = JackpotResource
    search_fields = [
        "jackpot_id",
    ]
    list_filter = ("jackpot_type",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class JackpotWinnerResourceAdmin(ImportExportModelAdmin):
    resource_class = JackpotWinnerResource
    search_fields = [
        "user__phone_number",
    ]
    # list_filter = (
    #     ('jackpot',)
    # )
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class InstantCashoutPendingWinningResourceAdmin(ImportExportModelAdmin):
    resource_class = InstantCashoutPendingWinningResource
    search_fields = ["batch", "amount", "tier", "suppresed"]
    list_filter = (
        "created_at",
        "tag",
        "is_avialable",
        "win_flavour",
        "suppresed",
        "claimant_channel",
        StakeAmountFilter,
    )
    date_hierarchy = "created_at"

    readonly_fields = ("bonus_target_agent", "claimant")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LotteryGlobalJackPotResourceAdmin(ImportExportModelAdmin):
    resource_class = LotteryGlobalJackPotResource
    search_fields = [
        "lottery_type",
    ]
    list_filter = ("lottery_type",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BalanceBonusAdmin(admin.ModelAdmin):
    list_display = [
        "date_created",
        "bonus_amount",
        "game_type",
        "balance_before",
        "balance_after",
        "transaction_type",
        "deployed",
        "narration",
    ]


class DrawDataAdmin(admin.ModelAdmin):
    list_display = [
        "created_at",
        "updated_at",
        "game_type",
        "factor1",
        "factor2",
        "factor3",
        "factor4",
        "extra",
    ]
    list_filter = [
        "game_type",
    ]


class LotteryTicketPriceResourceAdmin(ImportExportModelAdmin):
    resource_class = LotteryTicketPriceResource
    # search_fields = [
    #     "lottery_type",
    # ]
    list_filter = ("created_at", "winning_type", "lottery_type")


class JackpotConstantVariableResourceAdmin(ImportExportModelAdmin):
    resource_class = JackpotConstantVariableResource
    # search_fields = [
    #     "lottery_type",
    # ]
    # list_filter = ("lottery_type",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BankerConstantResourceAdmin(ImportExportModelAdmin):
    resource_class = BankerConstantResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class IPWhitelistResourceAdmin(ImportExportModelAdmin):
    resource_class = IPWhitelistResource

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class S4LCombosResourceAdmin(ImportExportModelAdmin):
    resource_class = S4LCombos

    search_fields = [
        "combo",
    ]
    date_hierarchy = "updated_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MaxWithdrawalThresoldResourceAdmin(ImportExportModelAdmin):
    resource_class = MaxWithdrawalThresoldResource

    search_fields = [
        "phone_number",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalaryForLifeSponsorResourceAdmin(ImportExportModelAdmin):
    resource_class = SalaryForLifeSponsorResource

    search_fields = [
        "brand_name",
        "designation",
        "email",
        "phone_number",
    ]
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SalaryForLifeParticipantResourceAdmin(ImportExportModelAdmin):
    resource_class = SalaryForLifeParticipantResource

    search_fields = [
        "name",
        "email",
        "phone_number",
        "location",
        "referrer",
    ]
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ContactUsFormResourceAdmin(ImportExportModelAdmin):
    resource_class = ContactUsFormResource

    search_fields = [
        "full_name",
        "email",
        "phone_no",
        "message",
    ]

    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WaitlistResourceAdmin(ImportExportModelAdmin):
    resource_class = WaitlistResource
    # list_display = ('first_name', 'email', 'created_at', 'account_name', 'account_number')
    list_filter = ("created_at",)
    search_fields = ("first_name", "email")
    readonly_fields = ("created_at", "updated_at")
    ordering = ("-created_at",)

    fieldsets = (
        ("User Information", {"fields": ("first_name", "email")}),
        ("Timestamps", {"fields": ("created_at", "updated_at"), "classes": ("collapse",)}),
    )

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of waitlist entries
        return False

    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editing an existing object
            return self.readonly_fields + ("email",)
        return self.readonly_fields


class WemaInstantPaymentAccountsResourceAdmin(ImportExportModelAdmin):
    resource_class = WemaInstantPaymentAccountsResource

    date_hierarchy = "created_at"
    search_fields = ["phone_number", "account_number", "account_name", "reference"]

    list_filter = ["created_at", "payment_received", "purpose"]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class SeederAdmin(ImportExportModelAdmin):
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RapidFireWinningAdmin(ImportExportModelAdmin):
    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ErroneousTransferRefundLogResourceAdmin(ImportExportModelAdmin):
    resource_class = ErroneousTransferRefundLogResource

    date_hierarchy = "created_at"
    search_fields = ["phone_number", "reference", "requested_by"]

    list_filter = [
        "created_at",
    ]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ScratchCardPartnershipResourceAdmin(ImportExportModelAdmin):
    resource_class = ScratchCardPartnershipResource

    search_fields = [
        "name",
        "email",
        "phone_number",
        "location",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]



class DrawWinnersLogResourceAdmin(ImportExportModelAdmin):
    resource_class = DrawWinnersLogResource

    search_fields = [
        "game_play_id",
        "batch_uuid",
    ]
    date_hierarchy = "created_at"

    list_filter = ["created_at","game_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class GamePlayEscrowResourceAdmin(ImportExportModelAdmin):
    resource_class = GamePlayEscrowResource

    search_fields = [
        "game_play_id",
        "batch_uuid",
    ]
    date_hierarchy = "created_at"

    list_filter = ["created_at","game_type"]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DrawLogAdmin(admin.ModelAdmin):
    list_display = [field.name for field in DrawLog._meta.fields]


admin.site.register(DrawData, DrawDataAdmin)
admin.site.register(Waitlist, WaitlistResourceAdmin)
admin.site.register(BalanceBonus, BalanceBonusAdmin)
admin.site.register(LotteryModel, LotteryModelResourceAdmin)
admin.site.register(LotteryBatch, LotteryBatchResourceAdmin)
admin.site.register(PaymentTransaction, PaymentTransactionTableResourceAdmin)
admin.site.register(PaymentCollectionDetail, PaymentCollectionDetailResourceAdmin)
admin.site.register(WatuPayCallBack, WatuPayCallBackResourceAdmin)
admin.site.register(WovenCallBack, WovenCallBackResourceAdmin)
admin.site.register(PabblyCallBack, PabblyCallBackResourceAdmin)
admin.site.register(LotteryWinnersTable, LotteryWinnersTableResourceAdmin)
admin.site.register(DisbursementTable, DisbursementTableResourceAdmin)
admin.site.register(UserProfile, UserProfileResourceAdmin)
admin.site.register(ConstantVariable, ConstantVariableResourceAdmin)
admin.site.register(ConstantVariableParam, ConstantVariableParamResourceAdmin)
admin.site.register(WovenAccountDetail, WovenAccountDetailResourceAdmin)
admin.site.register(Excesses, ExcessesResourceAdmin)
admin.site.register(PayoutTransactionTable, PayoutTransactionTableResourceAdmin)
admin.site.register(TestimonalTable, TestimonalTableResourceAdmin)
admin.site.register(LottoTicket, LottoTicketResourceAdmin)
admin.site.register(CreditCard, CreditCardResourceAdmin)
admin.site.register(LottoWinners, LottoWinnersResourceAdmin)
admin.site.register(Jackpot, JackpotResourceAdmin)
admin.site.register(JackpotWinner, JackpotWinnerResourceAdmin)
admin.site.register(LotteryGlobalJackPot, LotteryGlobalJackPotResourceAdmin)
admin.site.register(InstantCashoutPendingWinning, InstantCashoutPendingWinningResourceAdmin)
admin.site.register(LotteryTicketPrice, LotteryTicketPriceResourceAdmin)


admin.site.register(JackpotConstantVariable, JackpotConstantVariableResourceAdmin)
admin.site.register(BankerConstant, BankerConstantResourceAdmin)
admin.site.register(IPWhitelist, IPWhitelistResourceAdmin)
admin.site.register(S4LCombos, S4LCombosResourceAdmin)
admin.site.register(MaxWithdrawalThresold, MaxWithdrawalThresoldResourceAdmin)
admin.site.register(WemaInstantPaymentAccounts, WemaInstantPaymentAccountsResourceAdmin)

admin.site.register(RapidFireWinning, RapidFireWinningAdmin)
admin.site.register(Seeder, SeederAdmin)
admin.site.register(DrawLog, DrawLogAdmin)


admin.site.register(SalaryForLifeSponsor, SalaryForLifeSponsorResourceAdmin)
admin.site.register(SalaryForLifeParticipant, SalaryForLifeParticipantResourceAdmin)
admin.site.register(ErroneousTransferRefundLog, ErroneousTransferRefundLogResourceAdmin)
admin.site.register(ContactUsForm, ContactUsFormResourceAdmin)
admin.site.register(ScratchCardPartnership, ScratchCardPartnershipResourceAdmin)
admin.site.register(DrawWinnersLog, DrawWinnersLogResourceAdmin)
admin.site.register(GamePlayEscrow, GamePlayEscrowResourceAdmin)
admin.site.register(DrawTracker, DrawTrackerResourceAdmin)


