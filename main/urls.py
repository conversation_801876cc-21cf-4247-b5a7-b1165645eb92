from django.urls import path

from account import views as account_views
from main import views
from pos_app.views import LibertyPayVFDPayoutVerification

# URLCOnf

callbacks = [
    path("api/watupay/call_back/", views.WatuPayRecievePaymentAPIView.as_view()),
    path("api/woven/call_back/", views.WovenRecievePaymentAPIView.as_view()),
    path("api/pabbly/call_back/", views.PabblyRecievePaymentAPIView.as_view()),
    path("verify_liberty_pay_vfd_payout/", LibertyPayVFDPayoutVerification.as_view()),
    path("vfd_float_balance/", views.VFDFloatBalance.as_view()),
    path("all_vfd_float_balance/", views.AllVFDFloatBalance.as_view()),
    path("generate_random_system_pick/", views.GenerateFailedSystemPickNumberForALotteryBatch.as_view()),
    path("download_media_file/<str:file_name>", views.PickyAssistMediaMediaUniqueUrl.as_view()),
    path("manually_fund_debit_user_account/", views.WebFundDebitPlayWinningWallet.as_view()),
    path("mtn_sms_campaign/", views.MtnSMSCampaignAPIView.as_view()),
    path("send_mtn_sms/", views.SendMtnSMSAPIView.as_view()),
    path("erroneous_refund/", views.ErroneousTransferRefundLogView.as_view()),
    path("manully_create_retail_winnings/", views.ManuallyCreateARetailWinningView.as_view()),
    path("fetch_lotto_ticket_details/", views.FetchLottoTicketDetailsView.as_view()),
]

winners_list = [
    path("api/view_winners/", views.NonWinnerListAPIView.as_view()),
]

main_urls = [
    path("api/bank_verification/", views.VerifyAccountNumber.as_view()),
    path("api/waitlist/signup/", views.waitlist_signup, name="waitlist_signup"),
]

telco_numbers_urls = [
    path("api/telco_numbers/create", account_views.TelcosPhoneNumberCreate.as_view()),
]

winwise_data = [
    path("winwise_user_log/", views.WinWiseLogUserAPIView.as_view()),
    path("winwise_web_game/", views.WinWiseGamePlayedWebAPIView.as_view()),
    path("winwise_pos_game/", views.WinWiseGamePlayedPOSAPIView.as_view()),
    path("winwise_ussd_game/", views.WinWiseGamePlayedUSSDAPIView.as_view()),
    path("winwise_payout_data/", views.WinWiseGamePayOutAPIView.as_view()),
    path("winwise_pos_game_won/", views.WinWiseGameWonPOSAPIView.as_view()),
    path("winwise_ussd_game_won/", views.WinWiseGameWonUSSDAPIView.as_view()),
    path("winwise_web_game_won/", views.WinWiseGameWonWEBAPIView.as_view()),
    path("watch_logs/", views.view_logs),
    path("health_check/", views.ServerHealthCheckEndpoint.as_view()),
]

wema_fund_wallet = [
    path("api/wema/topup/", views.CoreBankingFundWalletCallBack.as_view()),
    path("api/wema/confirm_payment/", views.ConfirmWemaTransferPayment.as_view()),
]

salary_for_life_urls = [
    path("create_sponsor/", views.SalaryForLifeSponsorView.as_view(), name="create_sponsor"),
    path("create_participant/", views.SalaryForLifeParticipantView.as_view(), name="create_participant"),
    path("fill_contact_form/", views.ContactUsFormView.as_view(), name="fill_contact_form"),
    path("scratch_card_partnership/", views.ScratchCardPartnershipView.as_view(), name="scratch_card_partnership"),
]
BANKER_DRAW = [
    path("manually_run_banker_draw/", views.RunBankerDrawManuallyView.as_view()),
]
urlpatterns = [*callbacks, *winners_list, *main_urls, *winwise_data, *wema_fund_wallet, *telco_numbers_urls, *salary_for_life_urls, *BANKER_DRAW]
