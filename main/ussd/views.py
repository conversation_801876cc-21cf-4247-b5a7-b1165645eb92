import json

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from main.helpers.helper_functions import fetch_account_name
from main.models import (
    DisbursementTable,
    LotteryModel,
    LotteryWinnersTable,
    UserProfile,
)
from main.ussd.bankdb import filter_bank


class CreateLotteryModel(APIView):
    def post(self, request):
        try:
            request_body = dict(request.POST.items())
        except Exception:
            request_body = request.body
            request_body = json.loads(request_body.decode("utf-8").replace("'", '"'))

        LotteryModel.create_lottery_object(**request_body)

        data = {"message": "success"}

        return Response(data, status=status.HTTP_200_OK)

    def put(self, request):
        try:
            request_body = dict(request.POST.items())
        except Exception:
            request_body = request.body
            request_body = json.loads(request_body.decode("utf-8").replace("'", '"'))

        phone = request.GET.get("phone")

        if phone is None:
            data = {"message": "phone is required"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        request_body["phone"] = phone

        get_bank_code = filter_bank(request_body.get("bank_name"))
        if get_bank_code is None:
            pass
        else:
            request_body["bank_name"] = get_bank_code.get("name")
            request_body["bank_code"] = get_bank_code.get("cbn_code")

        LotteryModel._update_lottery_model(**request_body)

        data = {"message": "success"}
        return Response(data, status=status.HTTP_200_OK)


class CreateUserProfileView(APIView):
    def post(self, request):
        try:
            request_body = dict(request.POST.items())
        except Exception:
            request_body = request.body
            request_body = json.loads(request_body.decode("utf-8").replace("'", '"'))

        UserProfile.create_player_borrower(**request_body)

        data = {"message": "success"}

        return Response(data, status=status.HTTP_200_OK)


class BankVerifyView(APIView):
    def post(self, request):
        try:
            request_body = dict(request.POST.items())
        except Exception:
            request_body = request.body
            request_body = json.loads(request_body.decode("utf-8").replace("'", '"'))

        get_user_profile = UserProfile.objects.filter(phone_number=request_body.get("phone")).last()
        check_record = request_body.get("check_record")
        print(f"first query {request_body.get('phone')}")
        if check_record == "True":
            if get_user_profile and get_user_profile.account_num is not None and get_user_profile.bank_code is not None:
                data = {"status": "Found", "message": "Account already exists"}
                return Response(data, status=status.HTTP_200_OK)
            elif get_user_profile is None:
                data = {"status": "Not Found", "message": "No account found"}
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = {"status": "pass", "message": ""}
                return Response(data, status=status.HTTP_200_OK)

        elif request_body.get("check_record") == "False":
            account_number = request_body.get("account_number")
            get_bank_code = filter_bank(name=request_body.get("bank_name"))
            bank_code = get_bank_code.get("cbn_code")
            get_account_details = fetch_account_name(account_number, bank_code)

            # update the user profile
            if isinstance(get_account_details, dict) and get_account_details.get("status") is True:
                get_account_details["data"]["bank_name"] = get_bank_code.get("name")
                get_account_details["data"]["bank_code"] = bank_code

                print(request_body.get("phone"))
                UserProfile.objects.filter(phone_number=request_body.get("phone")).last().update_user_profile(**get_account_details)

                data = {"message": "success", "data": get_account_details}

                return Response(data, status=status.HTTP_200_OK)

            data = {"message": "error", "data": get_account_details}

            return Response(data, status=status.HTTP_200_OK)


class VerifyWinnersView(APIView):
    def post(self, request):
        try:
            request_body = dict(request.POST.items())
        except Exception:
            request_body = request.body
            request_body = json.loads(request_body.decode("utf-8").replace("'", '"'))

        phone = LotteryModel.format_number_from_back_add_234(request_body.get("phone"))

        fetch_lottery_winner_object = LotteryWinnersTable.objects.select_related("batch").filter(phone_number=phone).last()

        if fetch_lottery_winner_object is None:
            data = {"status": "error", "message": "no winner found"}
            return Response(data, status=status.HTTP_404_NOT_FOUND)

        _batch = fetch_lottery_winner_object.batch.id
        # get disbursement object
        _disbursement = DisbursementTable.objects.filter(lottery_batch__id=_batch).last()

        if _disbursement and _disbursement.is_disbursed is True:
            data = {"status": "error", "message": "disbursement already made"}
            return Response(data, status=status.HTTP_302_FOUND)

        # check user profile db for his account number
        _user_profile = UserProfile.objects.filter(phone_number=phone).last()
        if _user_profile.account_num is not None and _user_profile.bank_code is not None:
            data = {"status": "error", "message": "account details already exists"}
            return Response(data, status=status.HTTP_302_FOUND)

        # allow the user to input bank details
        data = {"status": "success", "message": "ok"}
        return Response(data, status=status.HTTP_302_FOUND)
