import math
import random
from random import randint


class LotteryHelpers:
    def scale_options(amount, option):
        data = ""
        amt = int(amount) / 5
        op = int(option)
        print(f"amt >>>>>>>>> {amt}")
        for i in range(1, 5 + 1):
            data += f"{i}. {Utility.currency_formatter(op*i)} to get {Utility.currency_formatter(amt * i)}\n"
        return data


class Utility:
    def currency_formatter(amount, round=False, return_currency=True):
        if round:
            currency = f"{Utility.rounding_amt_up(amount)}"
            currency = "N{:,}".format(int(currency))

            if return_currency is False:
                currency = str(currency).replace("N", "")
        else:
            if amount is None:
                currency = "N0"
            else:
                currency = "N{:,}".format(int(amount))

            if return_currency is False:
                currency = str(currency).replace("N", "")

        return currency

    def generate_lucky_number():
        n = 5
        range_start = 10 ** (n - 1)
        range_end = (10**n) - 1
        value = randint(range_start, range_end)
        return str(value)

    def generate_code(length=3):
        range_start = 10 ** (length - 1)
        range_end = (10**length) - 1
        value = randint(range_start, range_end)
        value = randint(range_start, range_end)
        return str(value)

    def rounding_amt_up(value):
        if type(value) is float:
            new_value = int(math.ceil(value / 100.0)) * 100
            return new_value
        elif type(value) is str:
            value = int(value.replace("N", "").replace(",", ""))
            new_value = int(math.ceil(value / 100.0)) * 100
            return new_value

        elif type(value) is int:
            new_value = int(math.ceil(value / 100.0)) * 100
            return new_value

    def generate_five_seperated_lucky_num() -> str:
        generate_lucky_numbers = random.sample(range(1, 50), 5)
        num_to_sting = [str(num) for num in generate_lucky_numbers]
        ticket_num = ",".join(num_to_sting)
        return str(ticket_num)

    def generate_four_seperated_lucky_num() -> str:
        num_list = []

        while len(num_list) < 4:
            num = random.randint(1, 40)
            if num not in num_list:
                num_list.append(num)

        return ",".join([str(num) for num in num_list])

    def generate_round_figure(decimal, decimal_places=2):
        return round(decimal, decimal_places)
