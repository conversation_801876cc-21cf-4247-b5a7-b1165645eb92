import collections
import json
import random
import string
import uuid
from datetime import datetime, time, timedelta
from functools import reduce
from random import choice, randint
from time import sleep

import pytz
import redis
import requests

# import traceprint
from cloudinary.models import CloudinaryField
from cryptography.fernet import Fernet
from dateutil.relativedelta import relativedelta
from decouple import config
from django.conf import settings
from django.contrib.postgres.fields import ArrayField
from django.core.exceptions import ValidationError
from django.db import IntegrityError, models, transaction
from django.db.models import F, Q, Sum
from django.db.models.functions import Now, TruncDate
from django.db.models.signals import post_save
from django.utils import timezone
from django.utils.translation import gettext as _
from pytz import utc

# from pos_app.models import Agent
from rest_framework import status

from flask_app import cloud_init

# from decisioning_engine.game_final import draw
# from decisioning_engine.utils import search_number_occurences
from main.api.api_lottery_helpers import (
    generate_game_play_id,
    generate_lucky_number,
    generate_pin,
)
from main.helpers.find_number import find_num
from main.helpers.helper_functions import (
    ByteHelper,
    fetch_account_name,
    notify_admin_on_fund_balance_for_payout,
)
from main.helpers.loandisk import (
    LoandiskHelper,
    create_pool_on_loandisk,
    full_name_split,
)
from main.helpers.redis_storage import RedisStorage
from main.helpers.remita_ussd import RemitaUssd
from main.helpers.vfd_disbursement_helper import VfdDisbursementHelperFunc
from main.helpers.woven_manager import WovenHelper
from main.tasks import (
    celery_sms_for_instant_cash_winners,
    celery_sms_for_s4lgame_lost,
    celery_sms_for_wyse_lost,
    celery_sms_for_wyse_winners,
    create_balance_bonus,
    notify_agents_on_lottery_batch_draw,
    winner_engange_event,
)
from main.ussd.bankdb import filter_bank, get_bank_name_func
from main.ussd.helpers import Utility

# from overide_print import print
from pos_app.models import (
    AgencyBankingToken,
    Agent,
    AgentConstantVariables,
    AgentWallet,
    AgentWalletTransaction,
    GamesDailyActivities,
    GeneralRetailLottoGames,
    LottoVerticalLeadWallet,
    PosLotteryWinners,
    RetailWalletTransactions,
    SupervisorWallet,
)
from pos_app.prices.structure import Quika
from pos_app.utils import DommyName, serialize_ticket
from sms_campaign.models import SmsChargeWallet
from wallet_app.helpers.payment_gateway import PaymentGateway

# traceprint.set_options(
#     limit=3,                        # Limit depth of stack entries displayed, if limit is above zero
#     right_align=40,                 # Number of characters to offset the link text to the right by
#     flatten_recurring_outputs=False  # Display recurring outputs (e.g. from a loop) inline instead of multiline
# )
from wallet_system.models import Wallet
from wema.wema_manager import WemaBank
from wyse_ussd.enums import PurposeChoices

SERVICE_TYPE = [
    ("AWOOF", "AWOOF"),
    ("INSURANCE", "INSURANCE"),
]


class UserProfile(models.Model):
    GENDER_CHOICES = [
        ("MALE", "MALE"),
        ("FEMALE", "FEMALE"),
        ("OTHER", "OTHER"),
    ]

    CHANNEL = [
        ("USSD", "USSD"),
        ("WEB", "WEB"),
        ("USSD/WEB", "USSD/WEB"),
        ("WEB/USSD", "WEB/USSD"),
        ("MOBILE", "MOBILE"),
        ("POS", "POS"),
    ]

    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
    )
    phone_number = models.CharField(max_length=100, unique=True)
    email = models.EmailField(null=True, blank=True)
    first_name = models.CharField(max_length=100, null=True, blank=True)
    last_name = models.CharField(max_length=100, null=True, blank=True)
    middle_name = models.CharField(max_length=100, null=True, blank=True)
    account_num = models.CharField(max_length=150, null=True, blank=True)
    account_name = models.CharField(max_length=150, null=True, blank=True)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    bank_code = models.CharField(max_length=150, null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    loandisk_player_id = models.CharField(max_length=150, null=True, blank=True)
    on_loandisk = models.BooleanField(default=False)
    ministry = models.CharField(max_length=300, null=True, blank=True)
    auth_code = models.CharField(max_length=100, null=True, blank=True)
    bvn_number = models.CharField(max_length=100, null=True, blank=True)
    gender = models.CharField(max_length=200, choices=GENDER_CHOICES, default="OTHER")
    profile_img = CloudinaryField("image", null=True, blank=True)
    channel = models.CharField(max_length=200, choices=CHANNEL, default="USSD")
    pin = models.CharField(max_length=125, null=True, blank=True)
    has_pin = models.BooleanField(default=False)
    has_web_virtual_account = models.BooleanField(default=False)
    has_sudo_phone_number = models.BooleanField(default=False)
    has_sudo_email = models.BooleanField(default=False)
    suspended = models.BooleanField(default=False)
    avatar = models.URLField(max_length=2300, null=True, blank=True)
    debt_amount = models.FloatField(default=0.00)
    recovered_debt = models.FloatField(default=0.00)
    from_telco = models.BooleanField(default=False)
    verification_code = models.CharField(max_length=250, editable=False, null=True, blank=True)
    email_is_verified = models.BooleanField(default=False)
    preverified_email = models.CharField(max_length=125, null=True, blank=True)
    network_provider = models.CharField(max_length=125, null=True, blank=True, choices=NETWORK_PROVIDER)

    def __str__(self) -> str:
        return str(self.phone_number)

    def save(self, *args, **kwargs):
        if not self.pk:
            if self.phone_number:
                self.phone_number = LotteryModel.format_number_from_back_add_234(self.phone_number)
                if UserProfile.objects.filter(phone_number=self.phone_number).exists():
                    pass
                else:
                    return super(UserProfile, self).save(*args, **kwargs)

        else:
            self.phone_number = LotteryModel.format_number_from_back_add_234(self.phone_number)

            # if UserProfile.objects.filter(phone_number=self.phone_number).exists():
            #     self.phone_number = self.__original_phone_number
            # if (
            #     LotteryModel.format_number_from_back_add_234(self.phone_number)
            #     != self.__original_phone_number
            # ):
            #     pass
            # else:
            #     # print("phone number not changed")
            #     self.phone_number = LotteryModel.format_number_from_back_add_234(
            #         self.phone_number
            #     )

            #     if UserProfile.objects.filter(
            #         phone_number=LotteryModel.format_number_from_back_add_234(
            #             self.phone_number
            #         )
            #     ).exists():
            #         self.phone_number = self.__original_phone_number

            #     else:
            #         del self.__original_phone_number

            return super(UserProfile, self).save(*args, **kwargs)

    @staticmethod
    def create_player_borrower(**kwargs):
        phone = kwargs.get("phone")
        get_user_profile = UserProfile.objects.filter(phone_number=phone)
        if get_user_profile.exists():
            user = get_user_profile.last()
            if user.channel == "WEB":
                user.channel = "WEB/USSD"
                user.save()

            data = {"message": "phone number already exists"}
            return data

        profile_instance = UserProfile.objects.create(phone_number=phone)
        email = f"libertyassured+{profile_instance.id}@gmail.com"
        profile_instance.email = email
        profile_instance.save()

    def update_user_profile(self, **kwargs):
        """
        Update user profile from USSD channel
        Kwargs: phone, first_name, last_name, middle_name, account_num, account_name, bank_name, bank
        """
        self.account_num = kwargs.get("data").get("account_number")
        self.account_name = kwargs.get("data").get("account_name")
        self.bank_name = kwargs.get("data").get("bank_name")
        self.bank_code = kwargs.get("data").get("cbn_code")

        name = full_name_split(kwargs.get("data").get("account_name"))
        self.first_name = name.get("first_name")
        self.last_name = name.get("last_name")
        self.middle_name = name.get("middle_name")
        self.save()

        kwargs = {
            "phone": self.phone_number,
            "account_num": self.account_num,
            "account_name": self.account_name,
            "bank_code": self.bank_code,
            "id": self.id,
            "loandisk_player_id": self.loandisk_player_id,
        }

        # loandisk
        if self.on_loandisk is False and self.loandisk_player_id is None:
            res = LoandiskHelper().create_player(**kwargs)
            if isinstance(res, dict) and res["response"]["borrower_id"]:
                self.loandisk_player_id = res["response"]["borrower_id"]
                self.on_loandisk = True
                self.save()

        LoandiskHelper().update_player(**kwargs)
        self.on_loandisk = True
        self.save()

        data = {"message": "success"}
        return data

    @staticmethod
    def user_profile_update(user, **kwargs):
        """
        Update user profile from API put http method
        Args: user instnace from request
        Kwargs: profile_img: files/image object[not required/might be empty]
                email: email field[not required/might be empty],
                phone: string[not required/might be empty],
                gender: string[not required/might be empty]
        """
        from web_app.models import UserFileUpload

        serialized_data = kwargs.keys()

        # print("serialized_data", serialized_data)

        # return None

        img_url = ""
        user = UserProfile.objects.filter(id=user.id).last()
        response = {}
        for key in serialized_data:
            if key == "profile_img":
                if kwargs.get("profile_img") is None or kwargs.get("profile_img") == "":
                    response["profile_img"] = None
                else:
                    # img_url = upload_image(kwargs.get("profile_img"))
                    # user.profile_img = img_url
                    # response["profile_img"] = img_url
                    file = kwargs.get("profile_img")
                    image = UserFileUpload.objects.create(user=user, file=file)
                    img_url = image.get_url
                    domain = kwargs.get("domain")
                    user.avatar = f"{domain}{img_url}"
                    response["profile_img"] = img_url
            elif key == "gender":
                if kwargs.get("gender") is None or kwargs.get("gender") == "":
                    response["gender"] = ""

                else:
                    gender = kwargs["gender"]
                    user.gender = gender.upper()
                    response["gender"] = gender

            elif key == "phone_number":
                if kwargs.get("phone_number") is None or kwargs.get("phone_number") == "":
                    response["phone_number"] = ""
                else:
                    phone = LotteryModel.format_number_from_back_add_234(kwargs.get("phone_number"))
                    user.has_sudo_phone_number = False
                    user.phone_number = phone
                    response["phone"] = phone

            elif key == "email":
                if kwargs.get("email") is None or kwargs.get("email") == "":
                    response["email"] = ""
                else:
                    email = kwargs.get("email")
                    user.email = email
                    response["email"] = email

            elif key == "bvn":
                if kwargs.get("bvn") is None or kwargs.get("bvn") == "":
                    response["bvn"] = ""
                else:
                    user.bvn_number = kwargs.get("bvn")
                    response["bvn"] = kwargs.get("bvn")

            elif key == "first_name":
                if kwargs.get("first_name") is None or kwargs.get("first_name") == "":
                    response["first_name"] = ""
                else:
                    user.first_name = kwargs.get("first_name")
                    response["first_name"] = kwargs.get("first_name")

            elif key == "last_name":
                if kwargs.get("last_name") is None or kwargs.get("last_name") == "":
                    response["last_name"] = ""
                else:
                    user.last_name = kwargs.get("last_name")
                    response["last_name"] = kwargs.get("last_name")

            user.save()

        return response

    @property
    def referalcode(self):
        if self.referal_code.all().last() is None:
            return None
        return self.referal_code.all().last().referral_code

    @property
    def referal_url(self):
        if self.referal_code.all().last() is None:
            return None
        return self.referal_code.all().last().url

    @property
    def user_referal_code(self):
        if self.referal_code.all().last() is None:
            return None
        return self.referal_code.all().last().referral_code

    @property
    def referral_wallet_balance(self):
        if self.referral_wallet.all().last() is None:
            return 0
        return self.referral_wallet.all().last().available_balance

    @property
    def wallet_balance(self):
        if self.wallet.all().last() is None:
            return 0
        for i in self.wallet.all():
            if i.wallet_tag == "WEB":
                return i.withdrawable_available_balance + i.game_available_balance

    @property
    def winning_wallet_balance(self):
        if self.wallet.all().last() is None:
            return 0

        for i in self.wallet.all():
            if i.wallet_tag == "WEB":
                return i.withdrawable_available_balance

    @property
    def play_wallet_balance(self):
        if self.wallet.all().last() is None:
            return 0

        for i in self.wallet.all():
            if i.wallet_tag == "WEB":
                return i.game_available_balance

    @property
    def referred_count(self):
        return self.referred.all().count()

    @property
    def total_amount_played(self):
        _lottery_model = (self.user_profile.filter(paid=True).aggregate(sum=Sum("stake_amount"))).get("sum")
        _lotto_ticket = (
            LottoTicket.objects.filter(user_profile=self, paid=True).aggregate(sum=Sum("stake_amount"))
        ).get("sum")

        return (_lottery_model or 0) + (_lotto_ticket or 0)

    class Meta:
        verbose_name = "USER PROFILE"
        verbose_name_plural = "USER PROFILES"

    @classmethod
    def create_user_profile_if_none_exist(cls, phone_no, channel):
        try:
            user_profile = UserProfile.objects.get(phone_number=phone_no)

        except UserProfile.DoesNotExist:
            user_profile = UserProfile.objects.create(phone_number=phone_no, channel=channel)

        return user_profile

    @classmethod
    def get_and_update_user_debt(cls, phone_number, amount, play_wallet=False, **kwargs):
        from wallet_app.models import DebtCollection, UserWallet, WalletTransaction
        from wyse_ussd.models import UssdConstantVariable

        print(
            f"""
        UssdConstantVariable.is_debt_collection_active: {UssdConstantVariable.is_debt_collection_active}
        \n\n\n
        """
        )

        if UssdConstantVariable.is_debt_collection_active() is False:
            return {"status": False, "amount": amount}

        user_profile = cls.objects.filter(phone_number=phone_number, debt_amount__gt=0).last()

        kwargs.get("bal_before")
        bal_after = kwargs.get("bal_after")
        wallet_tag = kwargs.get("wallet_tag")

        withdrawable_available_balance = kwargs.get("withdrawable_available_balance")
        game_play_available_balance = kwargs.get("game_play_available_balance")

        if user_profile:
            user_debt = user_profile.debt_amount

            if user_debt >= amount:
                if play_wallet is True:
                    _deduction_from = "USER_PLAY_WALLET"
                    game_play_available_balance = game_play_available_balance - amount
                else:
                    _deduction_from = "USER_WINNING_WALLET"
                    withdrawable_available_balance = withdrawable_available_balance - amount

                DebtCollection.create_record(
                    user=user_profile,
                    amount=float(amount),
                    wallet_type=_deduction_from,
                    debt_after=user_debt - amount,
                )

                user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag=wallet_tag).last()

                user_wallet.transaction_from = "DEBT_COLLECTION"

                WalletTransaction.create_wallet_transaction(
                    wallert_instance=user_wallet,
                    transaction_type="debit",
                    amount=float(amount),
                    status="successful",
                    bal_before=bal_after,
                    bal_after=bal_after - amount,
                    withdrawable_wallet_balance=withdrawable_available_balance,
                    game_play_available_balance=game_play_available_balance,
                )

                # UPDATE USER DEBT
                UserProfile.objects.filter(id=user_profile.id).update(
                    debt_amount=user_debt - amount,
                    recovered_debt=user_profile.recovered_debt + amount,
                )

                return {"status": True, "amount": 0}

            else:
                if play_wallet is True:
                    _deduction_from = "USER_PLAY_WALLET"
                    game_play_available_balance = game_play_available_balance - user_debt
                else:
                    _deduction_from = "USER_WINNING_WALLET"
                    withdrawable_available_balance = withdrawable_available_balance - user_debt

                DebtCollection.create_record(
                    user=user_profile,
                    amount=user_debt,
                    wallet_type=_deduction_from,
                    debt_after=0,
                )

                user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="USSD").last()

                user_wallet.transaction_from = "DEBT_COLLECTION"
                WalletTransaction.create_wallet_transaction(
                    wallert_instance=user_wallet,
                    transaction_type="debit",
                    amount=user_debt,
                    status="successful",
                    bal_before=bal_after,
                    bal_after=bal_after - user_debt,
                    withdrawable_wallet_balance=withdrawable_available_balance,
                    game_play_available_balance=game_play_available_balance,
                )

                # UPDATE USER DEBT
                UserProfile.objects.filter(id=user_profile.id).update(
                    debt_amount=0,
                    recovered_debt=user_profile.recovered_debt + user_debt,
                )

                return {"status": True, "amount": amount - user_debt}

        else:
            return {"status": False, "amount": amount}

    @classmethod
    def get_wema_acct_name(cls, phone_number):
        """
        Retrieve a possible account name based on a given phone number.

        Args:
            cls: The class (usually a model) to query for user data.
            phone_number (str): The phone number for which to retrieve the account name.

        Returns:
            dict: A dictionary with possible account name components:
                - "first_name" (str): The user's first name or "WinWise" if empty.
                - "last_name" (str): The user's last name or the provided phone number if empty.
                Returns None if no user is found with the given phone number.
        """
        # Retrieve the latest user instance with the given phone_number
        user_instance = cls.objects.filter(phone_number=phone_number).last()

        # Check if a user instance with the provided phone_number exists
        if not user_instance:
            return None  # No user found with the specified phone_number
        else:
            # Extract the first name and last name from the user instance
            first_name = user_instance.first_name
            last_name = user_instance.last_name

            # Determine a possible account name based on the retrieved data
            possible_account_name = {
                "user_instance": user_instance,
                "first_name": (first_name if first_name else "WinWise"),  # Use "WinWise" if first name is empty
                "last_name": (last_name if last_name else str(phone_number)),  # Use phone number if last name is empty
            }

            return possible_account_name  # Return the possible account name

    @classmethod
    def generate_mixed_pin(cls):
        characters = string.ascii_letters + string.digits

        # Ensure at least 2 numeric digits
        pin = "".join(random.choice(string.digits) for _ in range(2))

        # Fill the rest of the PIN with random characters
        pin += "".join(random.choice(characters) for _ in range(3))

        pin = "".join(random.sample(pin, len(pin)))
        return pin

    @classmethod
    def update_verification_code(cls, phone_number, channel) -> bool:
        """
        Update the verification code for a user with the given phone number or create a new user if one doesn't exist.

        Args:
            cls: The class representing the user model.
            phone_number (str): The phone number of the user to update.
            channel (str): The channel which the request is made from.

        Returns:
            bool: True if the verification code was successfully updated, or a new user was created, False otherwise.

        This function attempts to update the verification code for a user with the provided phone number.
        If a user with the given phone number doesn't exist, a new user instance is created.
        If the user exists but does not have a verification code, it generates a new mixed alphanumeric PIN
        and assigns it to the user, saving the changes to the database.

        Example:
            User.update_verification_code(User, "1234567890")

        Note:
            - Make sure to adapt this function to your specific user model and database structure.
            - The `generate_mixed_pin` function should be defined elsewhere to generate a mixed alphanumeric PIN.
        """
        mixed_pin = cls.generate_mixed_pin()

        user_instance, created = cls.objects.get_or_create(phone_number=phone_number)
        if created:
            user_instance.channel = channel
            user_instance.verification_code = mixed_pin
            user_instance.save()
            return True

        else:
            if not user_instance.verification_code:
                user_instance.verification_code = mixed_pin
                user_instance.save()

            return True

    @classmethod
    def verification_code_match(cls, phone_number, verification_code) -> bool:
        """
        Check if a user with the given phone number and verification code exists.

        Args:
            cls: The class representing the user model.
            phone_number (str): The phone number to match.
            verification_code (str): The verification code to match.

        Returns:
            bool: True if a matching user exists, False otherwise.

        This class method checks whether a user with the provided phone number and verification code exists in the database.
        If a matching user is found, it returns True; otherwise, it returns False.

        Example:
            user_exists = User.verification_code_match("1234567890", "ABC12")

        """
        return cls.objects.filter(phone_number=phone_number, verification_code=verification_code).exists()

    @classmethod
    def have_verification_code(cls, phone_number) -> bool:
        """
        Check if a user with a given phone number has a verification code.

        Args:
            cls (class): The class containing the database model for users.
            phone_number (str): The phone number to check for verification code.

        Returns:
            bool: True if a user with the provided phone number has a verification code;
                  False otherwise.

        Example:
            UserVerification.have_verification_code(User, "1234567890")

        Note:
            This method checks the existence of a verification code for a user with
            the specified phone number. It queries the database model for users
            associated with the provided phone number and verifies if their
            'verification_code' attribute is not null.

        """
        return cls.objects.filter(phone_number=phone_number, verification_code__isnull=False).exists()

    def get_duration(self):
        """Calculate the duration since the user was created."""
        if not self.date_added:
            return "Unknown"

        current_time = datetime.now()
        created_time = self.date_added.replace(tzinfo=None)  # Ensure timezone compatibility
        delta = relativedelta(current_time, created_time)

        duration_parts = []
        if delta.years:
            duration_parts.append(f"{delta.years} year{'s' if delta.years > 1 else ''}")
        if delta.months:
            duration_parts.append(f"{delta.months} month{'s' if delta.months > 1 else ''}")

        return ", ".join(duration_parts) if duration_parts else "Less than a month"


def generate_batch_uuid_func():
    formatted_month = datetime.now().strftime("%b%Y")  # Make sure to import datetime
    return f"{formatted_month}-{uuid.uuid4()}"


class LotteryBatch(models.Model):
    # super_winners
    # total_unique_paid_players_in_pool

    formatted_month = datetime.strftime(datetime.now(), "%b")

    LOTTERY_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("WHYSE_LOAN", "WHYSE_LOAN"),
        ("QUIKA", "QUIKA"),
        ("BANKER", "BANKER"),
    ]

    batch_uuid = models.CharField(max_length=150, default=generate_batch_uuid_func)
    total_players_in_pool = models.PositiveIntegerField(null=True, blank=True)
    total_unique_players_in_pool = models.PositiveIntegerField(null=True, blank=True)
    global_jackpot = models.ForeignKey("LotteryGlobalJackPot", on_delete=models.CASCADE, null=True, blank=True)
    total_unique_paid_playyers_in_pool = models.PositiveIntegerField(null=True, blank=True)
    total_accumulated_unpaid = models.FloatField(null=True, blank=True)
    total_accumulated_paid = models.FloatField(null=True, blank=True)
    total_accumulated_all = models.FloatField(null=True, blank=True)
    total_jackpot_amount_10 = models.FloatField(null=True, blank=True)
    total_jackpot_amount_50 = models.FloatField(null=True, blank=True)
    total_jackpot_amount_250 = models.FloatField(null=True, blank=True)
    total_jackpot_amount_500 = models.FloatField(null=True, blank=True)
    total_jackpot_amount_1000 = models.FloatField(null=True, blank=True)
    total_amount_won = models.FloatField(null=True, blank=True)
    RTO = models.FloatField(default=30)
    RTP = models.FloatField(null=True, blank=True)
    total_revenue = models.FloatField(null=True, blank=True)
    super_winers = models.JSONField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    is_pos_active = models.BooleanField(default=False)
    created_date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    draw_date = models.DateTimeField(blank=True, null=True)
    batch_start = models.DateTimeField(null=True, blank=True)
    batch_end = models.DateTimeField(null=True, blank=True)
    lottery_type = models.CharField(max_length=100, choices=LOTTERY_TYPE, default="WHYSE_LOAN")
    lottery_winner_ticket = models.TextField(
        blank=True,
        null=True,
        help_text="Serialization of the winner ticket/selection of numbers",
    )
    lottery_winner_ticket_number = models.TextField(blank=True, null=True, help_text="The winning ticket number")
    instant_cashout_player_threshold = models.PositiveIntegerField(default=0, editable=False)
    is_pos_batch = models.BooleanField(default=False)
    list_of_ticket_numbers = ArrayField(models.IntegerField(), blank=True, null=True)
    manually_filtered_winnings = models.BooleanField(default=False)

    @staticmethod
    def batch_paid_amount(batch):
        return list(
            PaymentTransaction.objects.filter(Q(has_paid=True) & Q(lottery_batch=batch))
            .aggregate(Sum("amount"))
            .values()
        )[0]

    @staticmethod
    def batch_paid_amount_today(batch):
        # start_date = datetime.today()
        # end_date
        # return list(
        #     PaymentTransaction.objects.filter(
        #         Q(has_paid=True)
        #         & Q(lottery_batch__id=batch.id)
        #         & Q(date_paid__date=datetime.today().date())
        #     )
        #     .aggregate(Sum("amount"))
        #     .values()
        # )[0]W

        if LotteryModel.objects.filter(batch__id=batch.id).exists():
            return (
                LotteryModel.objects.filter(batch__id=batch.id, date__date=timezone.now().date(), paid=True)
                .aggregate(Sum("amount_paid"))
                .get("amount_paid__sum")
            )

        elif LottoTicket.objects.filter(batch__id=batch.id).exists():
            return (
                LottoTicket.objects.filter(batch__id=batch.id, date__date=timezone.now().date(), paid=True)
                .aggregate(Sum("amount_paid"))
                .get("amount_paid__sum")
            )

        else:
            0

    def __str__(self):
        return str(self.batch_uuid)

    class Meta:
        verbose_name = "LOTTERY BATCH"
        verbose_name_plural = "LOTTERY BATCHES"

    @classmethod
    def get_current_batch(cls):
        return cls.objects.filter(is_active=True).last()

    def instant_cash_draw(self):
        # if self.lottery_type == "INSTANT_CASHOUT":
        #     self.lottery_winner_ticket = serialize_ticket([1,3,45,6,7,8,8])
        #     self.save()

        pass

    def clean(self):
        if self.lottery_type == "SALARY_FOR_LIFE" and self.global_jackpot is None:
            raise ValidationError("Global jackpot is required for salary for life")

    def save(self, *args, **kwargs):
        if not self.pk:
            if self.lottery_type == "INSTANT_CASHOUT":
                # select random nth player for instant cashout draw
                number_range = ConstantVariable.get_constant_variable().get("instant_cashout_win_range_number")
                random_number = random.randrange(int(number_range.split("-")[0]), int(number_range.split("-")[1]), 10)

                self.instant_cashout_player_threshold = int(random_number)

                redis_storage = RedisStorage(f"{self.batch_uuid}-instant_cashout-nth_player")
                redis_storage.set_data(random_number)

                # save active bacth to redis
                redis_storage = RedisStorage("instant_cashout_active_batch")
                redis_storage.set_data(self.batch_uuid)

            if self.lottery_type == "SALARY_FOR_LIFE":
                if self.list_of_ticket_numbers is None or self.list_of_ticket_numbers == []:
                    self.list_of_ticket_numbers = LotteryBatch.salary_for_life_list_of_ticket_numbers()

            # --------------------------------------------  raise exception if a lottery batch is already active -------------------------------------------- #
            if self.is_active is True:
                if self.is_pos_batch is False:
                    if LotteryBatch.objects.filter(
                        lottery_type=self.lottery_type,
                        is_active=True,
                        is_pos_batch=False,
                    ).exists():
                        return ValidationError("A lottery batch is already active")

                # save lottery batch rto and rtp in redis
                redis_storage = RedisStorage(f"{self.lottery_type}-rto")
                redis_storage.set_data(self.RTO)

            # --------------------------------------------  raise exception if a lottery batch is already active -------------------------------------------- #

            # check if lottery batch uuid already exists
            formatted_month = datetime.strftime(datetime.now(), "%b")
            if LotteryBatch.objects.filter(batch_uuid=self.batch_uuid).exists():
                self.batch_uuid = f"{formatted_month}-{str(uuid.uuid4())}"

        return super(LotteryBatch, self).save(*args, **kwargs)

    @classmethod
    def create_batch(cls, **kwargs):
        global_jackpot = LotteryGlobalJackPot.get_jackpot_instance()

        instance = cls()
        for key, value in kwargs.items():
            if hasattr(instance, key):
                setattr(instance, key, value)

        instance.global_jackpot = global_jackpot
        instance.save()

        return instance

    @classmethod
    def salary_for_life_list_of_ticket_numbers(cls):
        # random number from 1 to 50
        random_list_50_numbers = random.sample(range(1, 51), 50)

        # random single number from 1 to 39
        random_single_number = random.randint(1, 38)

        slicer_pointer = random_single_number + 10

        sliced_random_number_list = random_list_50_numbers[random_single_number:slicer_pointer]

        poped_list_from_random_list_50_numbers = (
            random_list_50_numbers[0:random_single_number] + random_list_50_numbers[slicer_pointer:-1]
        )

        num_list_1 = poped_list_from_random_list_50_numbers * 100
        num_list_2 = sliced_random_number_list * 2

        return num_list_1 + num_list_2


class LotteryModel(models.Model):  # wyse cash
    TEN_THOUSAND = "TEN_THOUSAND"
    FIFTY_THOUSAND = "FIFTY_THOUSAND"
    ONE_HUNDRED_THOUSAND = "ONE_HUNDRED_THOUSAND"
    TWO_HUNDRED_THOUSAND = "TWO_HUNDRED_THOUSAND"
    TWO_FIFTY_THOUSAND = "TWO_FIFTY_THOUSAND"
    FIVE_HUNDRED_THOUSAND = "FIVE_HUNDRED_THOUSAND"
    ONE_MILLION = "ONE_MILLION"
    TWO_MILLION = "TWO_MILLION"

    POOL_CHOICES = [
        (TEN_THOUSAND, "TEN_THOUSAND"),
        (FIFTY_THOUSAND, "FIFTY_THOUSAND"),
        (ONE_HUNDRED_THOUSAND, "ONE_HUNDRED_THOUSAND"),
        (TWO_HUNDRED_THOUSAND, "TWO_HUNDRED_THOUSAND"),
        #
        (TWO_FIFTY_THOUSAND, "TWO_FIFTY_THOUSAND"),
        (FIVE_HUNDRED_THOUSAND, "FIVE_HUNDRED_THOUSAND"),
        (ONE_MILLION, "ONE_MILLION"),
        (TWO_MILLION, "TWO_MILLION"),
    ]

    LOTTERY_CHANNEL = [
        ("USSD", "USSD"),
        ("USSD_WEB", "USSD_WEB"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
        ("SYSTEM_BONUS", "SYSTEM_BONUS"),
    ]

    LOTTERY_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("WHYSE_LOAN", "WHYSE_LOAN"),
        ("WHYSE_CASH_BUSINESS", "WHYSE_CASH_BUSINESS"),
    ]

    LOTTO_SOURCE = [
        ("NORMAL", "NORMAL"),
        ("BONUS", "BONUS"),
    ]

    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
    )

    user_profile = models.ForeignKey(
        UserProfile,
        related_name="user_profile",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    agent_profile = models.ForeignKey(Agent, on_delete=models.CASCADE, null=True, blank=True)
    batch = models.ForeignKey(LotteryBatch, related_name="lottery_players", on_delete=models.CASCADE)
    unique_id = models.CharField(max_length=300, null=True, blank=True)
    phone = models.CharField(max_length=300)
    instance_number = models.PositiveIntegerField(default=1)
    pool = models.CharField(max_length=150, choices=POOL_CHOICES)
    band = models.FloatField(default=50000)
    stake_amount = models.FloatField(default=0.00)
    lucky_number = models.CharField(max_length=300)
    paid = models.BooleanField(default=False)
    account_no = models.CharField(max_length=150, null=True, blank=True)
    bank_name = models.CharField(max_length=300, null=True, blank=True)
    ussd_watupay_bank_code = models.CharField(max_length=100, blank=True, null=True)
    date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    paid_date = models.DateTimeField(blank=True, null=True)
    has_interest = models.BooleanField(default=True)
    payout_account_no = models.CharField(max_length=150, null=True, blank=True)
    payout_bank_name = models.CharField(max_length=300, null=True, blank=True)
    payout_bank_code = models.CharField(max_length=100, blank=True, null=True)
    loandisk_pool_id = models.CharField(max_length=150, null=True, blank=True)
    exist_on_loandisk = models.BooleanField(default=False)
    loandisk_status_id = models.CharField(max_length=150, default="216235")
    channel = models.CharField(max_length=150, choices=LOTTERY_CHANNEL, default="USSD")
    consent = models.BooleanField(default=True)
    game_play_id = models.CharField(max_length=150, null=True, blank=True)
    unique_game_play_id = models.CharField(max_length=150, null=True, blank=True)
    expected_amount = models.FloatField(default=0.00)
    amount_paid = models.FloatField(default=0.00)
    rto = models.FloatField(default=0.00)
    rtp = models.FloatField(default=0.00)
    rtp_per = models.FloatField(default=0.00)
    commission_per = models.FloatField(default=0.00)
    commission_value = models.FloatField(default=0.00)
    win_commission_per = models.FloatField(default=0.00)
    win_commission_value = models.FloatField(default=0.00)
    win_commission_per = models.FloatField(default=0.00)
    win_commission_value = models.FloatField(default=0.00)
    lottery_type = models.CharField(max_length=150, choices=LOTTERY_TYPE, default="WHYSE_LOAN")
    lottery_source = models.CharField(max_length=150, choices=LOTTO_SOURCE, default="NORMAL")
    business_name = models.CharField(max_length=300, blank=True, null=True)
    business_file = models.CharField(max_length=300, blank=True, null=True)
    business_description = models.TextField(blank=True, null=True)

    is_duplicate = models.BooleanField(default=False)
    is_agent = models.BooleanField(default=False)
    telco_network = models.CharField(max_length=150, choices=NETWORK_PROVIDER, blank=True, null=True)

    pin = models.CharField(
        max_length=150,
        null=True,
        blank=True,
        help_text="Pin use for cashout if the lottery is played from POS_AGENT and the player didn't provide his phone number",
    )

    identity_id = models.CharField(
        max_length=500,
        null=True,
        blank=True,
        help_text="This is basically created for transactional purpose",
    )

    played_via_telco_channel = models.BooleanField(default=False)

    def __str__(self) -> str:
        return f"{self.phone}-{self.id}"

    class Meta:
        verbose_name = "WYSE CASH LOTTERY TICKET"
        verbose_name_plural = "WYSE CASH LOTTERY TICKETS"
        indexes = [models.Index(fields=["game_play_id"], name="game_play_id_idx")]  # Shortened index name

    def save(self, *args, **kwargs):
        """
        Override the default save method for the LotteryModel to handle financial calculations and wallet updates.

        This method performs several operations:
        1. Rounds financial values
        2. Tracks payment status changes
        3. Calculates commissions based on channel type
        4. Processes RTP (Return to Player) and RTO (Return to Operator) values
        5. Updates various wallet types
        6. Handles jackpot contributions

        Args:
            *args: Variable length argument list passed to parent save method
            **kwargs: Arbitrary keyword arguments passed to parent save method

        Returns:
            The result of the parent save method
        """
        # Round financial values to 2 decimal places
        self.amount_paid = round(self.amount_paid, 2)
        self.expected_amount = round(self.expected_amount, 2)

        # For existing records, check if payment status has changed
        if self.pk:
            # Retrieve original payment status
            _original_paid = self._get_original_payment_status()

            # Process payment status change
            if _original_paid != self.paid and _original_paid is False and self.paid is True:
                self._process_new_payment()

                # UPDATING RTP AND RTO IN GAME DAILY ACTIVITIES TABLE
                if self.agent_profile:
                    _from_lotto_agent = True if self.agent_profile.terminal_id is not None else False
                    try:
                        GamesDailyActivities.create_record(
                            game_type=self.lottery_type, rtp=self.rtp, rto=self.rto, from_lotto_agent=_from_lotto_agent
                        )
                    except:
                        pass

                    try:
                        RetailWalletTransactions.create_debit_record_for_game_play(
                            amount=self.amount_paid,
                            wallet_value=self.rtp,
                            rto_value=self.rto,
                            rtp_value=self.rtp,
                            game_type=self.lottery_type,
                        )
                    except:
                        pass

                    agent_instance = self.agent_profile
                    GeneralRetailLottoGames.create_record(
                        agent_phone_number=agent_instance.phone,
                        agent_name=agent_instance.full_name,
                        agent_email=agent_instance.email,
                        batch_uuid=self.batch.batch_uuid,
                        game_play_id=self.game_play_id,
                        game_pin=self.pin,
                        lucky_number=self.lucky_number,
                        purchase_amount=self.amount_paid,
                        lotto_db_id=self.id,
                        paid=self.paid,
                        number_of_ticket=self.instance_number,
                        rtp=self.rtp,
                        rto=self.rto,
                        commission_percentage=self.commission_per,
                        type_of_agent=agent_instance.agent_type,
                        lotto_game_type=self.lottery_type,
                        potential_winnings=self.band,
                    )

        # For new records with agent profile
        if not self.id and self.agent_profile:
            self.is_agent = True

        # Set unique game play ID
        self.unique_game_play_id = self.game_play_id

        # Call parent save method
        return super(LotteryModel, self).save(*args, **kwargs)

    def _get_original_payment_status(self):
        """
        Retrieve the original payment status of the record from the database.

        Returns:
            bool: The original payment status
        """
        _original_paid = False

        # Get original record from database
        old = self.__class__.objects.get(pk=self._get_pk_val())

        # Retrieve fields we care about
        for field in self.__class__._meta.fields:
            if field.name == "paid":
                _original_paid = field.value_from_object(old)
            elif field.name == "amount_paid":
                field.value_from_object(old)

        return _original_paid

    def _process_new_payment(self):
        """
        Process a newly paid ticket, handling commission calculations and wallet updates.

        This method is called when a ticket transitions from unpaid to paid status.
        It handles all financial calculations and wallet updates required for the payment.
        """
        # Skip processing if amount is zero
        if self.amount_paid <= 0.00:
            return

        # Initialize starting amount
        amount_paid_after_removing_comission = self.amount_paid

        # Process based on channel type
        if self.channel == "POS_AGENT":
            amount_paid_after_removing_comission = self._process_pos_agent_commission(
                amount_paid_after_removing_comission
            )
        elif self.played_via_telco_channel and self.channel == "USSD":
            amount_paid_after_removing_comission = self._process_telco_commission(amount_paid_after_removing_comission)

        # Calculate RTP and RTO
        amount_paid_after_removing_comission = self._calculate_rtp_rto(amount_paid_after_removing_comission)

        # Process SMS charges
        amount_paid_after_removing_comission = self._process_sms_charges(amount_paid_after_removing_comission)

        # Process global jackpot contribution
        amount_paid_after_removing_comission = self._process_jackpot_contribution(amount_paid_after_removing_comission)

        # Process win commission for POS agents
        if self.channel == "POS_AGENT":
            amount_paid_after_removing_comission = self._process_win_commission(amount_paid_after_removing_comission)

        # Update RTO wallet
        self._update_rto_wallet()

        # Set final RTP amount
        self.rtp = amount_paid_after_removing_comission

        # Add funds to general withdrawable wallet
        self._update_general_wallet()

        # Set paid date
        self.paid_date = timezone.now()

        # Update agent commission
        if self.channel == "POS_AGENT":
            if self.agent_profile:
                AgentWallet.reward_commission(
                    agent_id=self.agent_profile.id,
                    game_play_amount=self.amount_paid,
                    commission_type="COMMISSION_ON_GAME_PLAY",
                    game_type=self.lottery_type,
                    rto_amount=self.rto,
                )

    def _process_pos_agent_commission(self, amount_paid):
        """
        Calculate and process commission for POS agent channel.

        Args:
            amount_paid (float): Current amount after previous deductions

        Returns:
            float: Amount after commission deduction
        """
        # Default commission percentage
        self.commission_per = AgentConstantVariables().get_agent_sales_commission()

        # Get agent sales commission from Redis
        # redis_db = RedisStorage(redis_key="agent_sales_commission")
        # agent_sales_commission = redis_db.get_data()

        # if agent_sales_commission is None:
        #     agent_sales_commission = AgentConstantVariables().get_agent_sales_commission()
        # else:
        #     agent_sales_commission = float(agent_sales_commission.decode("utf-8"))

        agent_sales_commission = AgentConstantVariables.get_draw_game_commission_percentage()

        # Set commission percentage based on channel type
        if not self.played_via_telco_channel:
            self.commission_per = agent_sales_commission
        else:
            self.commission_per = 0

        # Calculate commission value
        self.commission_value = round(self.amount_paid * self.commission_per, 2)

        # Deduct commission from amount
        return amount_paid - self.commission_value

    def _process_telco_commission(self, amount_paid):
        """
        Calculate and process commission for telco channel (USSD).

        Args:
            amount_paid (float): Current amount after previous deductions

        Returns:
            float: Amount after telco commission deductions
        """
        # Calculate telco commission
        ussd_telco_commission = ConstantVariable().get_telco_commission()
        self.ussd_telco_commission = ussd_telco_commission
        self.ussd_telco_commission_value = round(self.amount_paid * ussd_telco_commission, 2)

        # Deduct telco commission
        amount_after_telco = amount_paid - self.ussd_telco_commission_value

        # Calculate aggregator commission
        ussd_telco_aggregator_commission = ConstantVariable().get_aggregator_commission()
        self.ussd_telco_aggregator_commission = ussd_telco_aggregator_commission
        self.ussd_telco_aggregator_commission_value = amount_after_telco * ussd_telco_aggregator_commission

        # Deduct aggregator commission
        return amount_after_telco - self.ussd_telco_aggregator_commission_value

    def _calculate_rtp_rto(self, amount_paid):
        """
        Calculate Return to Player (RTP) and Return to Operator (RTO) values.

        Args:
            amount_paid (float): Current amount after previous deductions

        Returns:
            float: Amount after RTO deduction
        """
        # Determine appropriate RTP percentage based on channel and lottery type
        if self.played_via_telco_channel and self.channel == "USSD":
            _rtp = (ConstantVariable().telco_rtp_perc()).get("rtp")
        else:
            _rtp = (ConstantVariable().rto_rtp()).get("rtp")

        # Special handling for WYSE_CASH and WHYSE_LOAN
        if (self.lottery_type in ["WYSE_CASH", "WHYSE_LOAN"]) and (not self.played_via_telco_channel):
            _rtp = ConstantVariable.objects.all().last().rtp_s4l_bank_wysecash / 100

        # Set RTP percentage
        self.rtp_per = float(_rtp) * 100

        # Calculate RTO percentage
        rto_per = (100 - (float(_rtp) * 100)) / 100

        # Calculate RTO amount
        self.rto = round(amount_paid * rto_per, 2)

        # Deduct RTO from amount
        return amount_paid - self.rto

    def _process_sms_charges(self, amount_paid):
        """
        Apply SMS charges based on channel type.

        Args:
            amount_paid (float): Current amount after previous deductions

        Returns:
            float: Amount after SMS charges
        """
        # Skip SMS charges for POS agents
        if self.channel == "POS_AGENT":
            sms_charge = 0
        else:
            sms_charge = ConstantVariable.objects.last().general_sms_charges

        # Add charge to SMS wallet
        SmsChargeWallet().add_charge(provider="WHISPER_SMS", amount=sms_charge)

        # Deduct SMS charge from amount
        return amount_paid - sms_charge

    def _process_jackpot_contribution(self, amount_paid):
        """
        Calculate and process global jackpot contribution.

        Args:
            amount_paid (float): Current amount after previous deductions

        Returns:
            float: Amount after jackpot contribution
        """
        # Get global jackpot percentage
        global_jackpot_per = JackpotConstantVariable().get_percentage_to_share()

        # Calculate jackpot contribution
        _globa_jackpot = amount_paid * global_jackpot_per

        # Add to jackpot
        Jackpot.add_to_jackpot(_globa_jackpot)

        # Deduct jackpot contribution from amount
        return amount_paid - _globa_jackpot

    def _process_win_commission(self, amount_paid):
        """
        Calculate and process win commission for POS agents.

        Args:
            amount_paid (float): Current amount after previous deductions

        Returns:
            float: Amount after win commission
        """
        from wallet_app.models import CommissionWallet

        # Calculate win commission
        self.win_commission_per = AgentConstantVariables().get_win_agent_commission()
        self.win_commission_value = amount_paid * self.win_commission_per

        # Update commission wallet
        CommissionWallet().update_commission_wallet_amount(
            amount=self.win_commission_value,
            phone_number=self.user_profile.phone_number,
        )

        # Deduct win commission from amount
        return amount_paid - self.win_commission_value

    def _update_rto_wallet(self):
        """
        Update the RTO wallet with the calculated RTO amount.
        """

        # Determine wallet type based on channel
        # if self.played_via_telco_channel:
        #     wallet_type = "TELCO"
        # else:
        #     wallet_type = "DEFAULT"
        # Update RTO wallet
        # RtoWallet().update_rto_wallet_amount(
        #     amount=self.rto,
        #     phone_number=self.user_profile.phone_number,
        #     wallet_type=wallet_type,
        # )

        if self.played_via_telco_channel is False:

            rto = self.rto
            supervisor_commission = 0
            vertical_lead_commission = 0

            ## calculate veritical lead and supervisor commission
            if self.agent_profile is not None:
                if self.agent_profile.supervisor is not None:
                    supervisor_commission = round(
                        rto * AgentConstantVariables.get_supervisor_commission_percentage(), 2
                    )

                    SupervisorWallet.fund_commission_wallet(
                        supervisor_phone_number=self.agent_profile.supervisor.phone,
                        amount=supervisor_commission,
                        transaction_type="CREDIT",
                        lottery_type=self.lottery_type,
                    )

                    if self.agent_profile.supervisor.vertical_lead is not None:
                        vertical_lead_commission = round(
                            rto * AgentConstantVariables.get_vertical_lead_commission_percentage(), 2
                        )

                        LottoVerticalLeadWallet.fund_commission_wallet(
                            vertical_lead_phone_number=self.agent_profile.supervisor.vertical_lead.phone,
                            amount=vertical_lead_commission,
                            transaction_type="CREDIT",
                            lottery_type=self.lottery_type,
                        )

            self.rto = rto - (supervisor_commission + vertical_lead_commission)

            Wallet.fund_wallet(amount=self.rto, wallet_type="RTO_WALLET", game_type=self.lottery_type)

    def _update_general_wallet(self):
        """
        Update the general withdrawable wallet with the final RTP amount.
        """

        # Add funds to general withdrawable wallet
        # GeneralWithdrawableWallet.add_fund(
        #     self.rtp,
        #     self.user_profile.phone_number,
        #     self.lottery_type,
        # )
        if self.channel == "POS_AGENT":
            Wallet.fund_wallet(amount=self.rtp, wallet_type="RETAIL_RTP_WALLET", game_type=self.lottery_type)

    @staticmethod
    def format_number_from_back_add_234(phone) -> str:
        if phone is None:
            return None

        formatted_num = phone[-10:]
        if formatted_num[0] == "0":
            return None
        else:
            return "234" + formatted_num

    @staticmethod
    def create_lottery_object(**kwargs):
        """
        Create a new lottery object from USSD channel
        Kwargs:
            phone: phone number of the user
            pool: pool of the user
            band: band of the user
            stake_amount: stake amount of the user
            instance_number: instance number of the band which will determine
                             the number of lucky number to be created
        Returns: None
        """
        current_batch = LotteryBatch.get_current_batch()
        unique_id = f"{current_batch.batch_uuid[0:5]}-{str(uuid.uuid4())[0:5]}"

        num = int(kwargs.get("num"))

        phone = LotteryModel.format_number_from_back_add_234(kwargs.get("phone"))

        for i in range(0, num):
            lucky_number = Utility.generate_lucky_number()
            lottery_player = LotteryModel.objects.create(
                phone=phone,
                unique_id=unique_id,
                band=kwargs.get("band"),
                stake_amount=int(kwargs.get("stake_amount")),
                lucky_number=lucky_number,
                batch=current_batch,
                pool=kwargs.get("pool"),
                instance_number=num,
            )

            get_user_profile = UserProfile.objects.filter(phone_number=phone).last()
            if get_user_profile:
                lottery_player.user_profile = get_user_profile
                lottery_player.save()

    def _update_lottery_model(**kwargs):
        """
        Update a lottery object from USSD channel
        """
        from main.tasks import create_collection_details_for_lottery_player

        phone = LotteryModel.format_number_from_back_add_234(kwargs.get("phone"))
        num = int(kwargs.get("num"))
        bank_code = kwargs.get("bank_code")
        bank_name = kwargs.get("bank_name")

        _model_queryset = LotteryModel.objects.filter(phone=phone)
        lottery_model_queryset = _model_queryset.filter(phone=phone).order_by("-id")[:num]
        LotteryModel.objects.filter(id__in=lottery_model_queryset).update(
            payout_bank_code=bank_code, bank_name=bank_name
        )
        # send sms to player
        Utility.currency_formatter(lottery_model_queryset.aggregate(Sum("stake_amount"))["stake_amount__sum"])
        # amount = Utility.currency_formatter(amount)
        band = _model_queryset.last().band

        # Get total amount to be won by player
        instance_count = lottery_model_queryset.count()
        total_stake_amount = int(float(band) * instance_count)

        new_band_amount = int(0.00)

        # Get total amount to be paid by player
        for i in lottery_model_queryset:
            new_band_amount += int(i.stake_amount)

        print(new_band_amount)

        # new_band = Utility.currency_formatter(lottery_model_queryset.aggregate(Sum("band"))["band__sum"])
        # player = _model_queryset.last()
        # create collection details for lottery player and send sms to player
        # swith band with stake

        # save lottery pool on loandisk
        for i in lottery_model_queryset:
            _loan_disk_res = create_pool_on_loandisk(
                phone=i.phone,
                lucky_num=i.lucky_number,
                lottery_instance_id=i.id,
                stake_amount=i.stake_amount,
                band=i.band,
                unique_id=i.unique_id,
            )

            if isinstance(_loan_disk_res, dict) and _loan_disk_res.get("response").get("loan_id"):
                if (
                    _loan_disk_res.get("response").get("loan_id") is None
                    or _loan_disk_res.get("response").get("loan_id") == ""
                ):
                    pass
                else:
                    i.loandisk_pool_id = _loan_disk_res.get("response").get("loan_id")
                    i.exist_on_loandisk = True
                    i.save()

        create_collection_details_for_lottery_player.delay(
            phone=phone,
            amount=new_band_amount,
            bank_code=bank_code,
            stake=total_stake_amount,
        )

    @staticmethod
    def determine_stake_for_payment_collection(lottery_player):
        if lottery_player.pool == "TEN_THOUSAND":
            return 100.00
        elif lottery_player.pool == "FIFTY_THOUSAND":
            return 100.00
        elif lottery_player.pool == "TWO_FIFTY_THOUSAND":
            return 200.00
        elif lottery_player.pool == "FIVE_HUNDRED_THOUSAND":
            return 500.00
        elif lottery_player.pool == "ONE_MILLION":
            return 1000.00
        elif lottery_player.pool == "TWO_MILLION":
            return 2000.00

    @staticmethod
    def band_integer(pool):
        if pool == "TEN_THOUSAND":
            return 10000
        elif pool == "FIFTY_THOUSAND":
            return 50000
        elif pool == "TWO_FIFTY_THOUSAND":
            return 250000
        elif pool == "FIVE_HUNDRED_THOUSAND":
            return 500000
        elif pool == "ONE_MILLION":
            return 1000000
        elif pool == "TWO_MILLION":
            return 2000000

    @staticmethod
    def wyse_cash_pool(band):
        if band >= 10000 and band < 49999:
            return "TEN_THOUSAND"
        elif band >= 50000 and band < 99999:
            return "FIFTY_THOUSAND"
        elif band >= 100000 and band < 199999:
            return "ONE_HUNDRED_THOUSAND"
        elif band >= 200000 and band <= 500000:
            return "TWO_HUNDRED_THOUSAND"

    @staticmethod
    def wyse_cash_band_price(band):
        if band >= 10000 and band < 49999:
            return 100.00
        elif band >= 50000 and band < 99999:
            return 200.00
        elif band >= 100000 and band < 199999:
            return 500.00
        elif band >= 200000 and band <= 500000:
            return 1000.00

    @staticmethod
    def share_payment_accross_pool(amount, player, instance_number, wallet):
        pass

        batch = LotteryBatch.get_current_batch()

        LotteryModel.determine_stake_for_payment_collection(player)

        player_instances_on_table = LotteryModel.objects.filter(
            Q(phone=player.phone) & Q(paid=False) & Q(has_interest=True)
        ).order_by("-id")
        player_last_instance = LotteryModel.objects.filter(phone=player.phone).order_by("-id").last()

        print("here nowwwww")
        print(wallet)
        print("I am the wallet to settle")
        # print()

        # Update Payment for player

        depletable_amount = amount
        for player in player_instances_on_table:
            if depletable_amount < player.stake_amount:
                pass
            else:
                print("should come twice")
                player.paid = True
                player.paid_date = datetime.now()
                print(player.batch)
                player.batch = batch
                player.loandisk_status_id = "216234"
                player.save()
                print(player.batch)
                depletable_amount -= player.stake_amount

                # depletable_amount -= lotto_stake

        if depletable_amount > 100.00:
            if wallet is None:
                Excesses.objects.create(
                    phone_number=player.phone,
                    last_played_unique_id=player_last_instance.unique_id,
                    in_excess_amount=depletable_amount,
                )
            else:
                # UserWallet.fund_wallet(user=wallet.user, amount=depletable_amount)
                pass

                # if wallet.funding_count == 0

    @staticmethod
    def assign_player_to_batch(player):
        # Check current batch and update
        current_batch = LotteryBatch.get_current_batch()

        player_batch = player.batch
        if player_batch.is_active is False:
            player.batch = current_batch
            player.save()

    @staticmethod
    def spread_lottery_amount(user, amount) -> bool:
        """
        Get all lottery models for these user instance that have "amount" pass
        in the function paramter as "expected amount" and
        spread verified payment across lottery model

        """
        lottery_models = LotteryModel.objects.filter(
            user_profile=user,
            paid=False,
            has_interest=True,
        ).last()

        update_actual_lottery = LotteryModel.objects.filter(game_play_id=lottery_models.game_play_id)

        if update_actual_lottery.exists():
            for lottery in update_actual_lottery:
                lottery.paid = True
                lottery.amount_paid = lottery.stake_amount
                lottery.save()
                #   update_actual_lottery .update(paid=True, amount_paid=amount, paid_date=datetime.now()
            return True
        return False

    @classmethod
    def get_active_games(cls):
        """
        Get all active games
        """
        return cls.objects.filter(paid=True, has_interest=True, batch__is_active=True).order_by("-date")

    @classmethod
    def wyse_cash_game_draw(cls):
        from decisioning_engine.draw import WyseCashDraw
        from wyse_ussd.models import PendingAsyncTask
        from wyse_ussd.tasks import ussd_lottery_winner_reward

        # check if we should merge decisioning for POS and other channels
        ConstantVariable.get_constant_variable().get("merge_decisioning")

        # get wyse cash active game batch
        wyse_cash_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH", is_active=True).last()

        batch_database_id = wyse_cash_batch.id

        active_lottery_batch_id = wyse_cash_batch.id

        # if merge_decisioning is True:

        #     batch_lottery_qs = LotteryModel.objects.filter(
        #         batch__id=active_lottery_batch_id, lottery_type="WYSE_CASH", paid=True
        #     )

        # else:
        #     batch_lottery_qs = LotteryModel.objects.filter(
        #         batch__id=active_lottery_batch_id,
        #         lottery_type="WYSE_CASH",
        #         paid=True,
        #         channel="POS_AGENT",
        #     )

        batch_lottery_qs = LotteryModel.objects.filter(
            batch__id=active_lottery_batch_id, lottery_type="WYSE_CASH", paid=True
        )

        if not batch_lottery_qs.exists():
            pass
        else:
            wyse_cash_batch.is_active = False
            wyse_cash_batch.draw_date = datetime.now()
            wyse_cash_batch.save()

            try:
                LotteryBatch.objects.create(lottery_type="WYSE_CASH")
            except Exception:
                pass

            wyse_cash_draw_response = WyseCashDraw(query_set=batch_lottery_qs).draw()

            """

            SAMPLE RESPONSE DATA
            {"upper_tier":{"count":2.59,"reward":40000.0,"pool":"TWO_HUNDRED_THOUSAND","jackpot":34577.9,"jkpt_count":0.17,"winners":["400g3A9","f100T19"],"jkpt_winners":[]},"middle_tier":{"count":1.47,"reward":80000.0,"pool":"ONE_HUNDRED_THOUSAND","jackpot":31397.24,"jkpt_count":0.66,"winners":["v8005W6"],"jkpt_winners":[]},"lower_tier":{"count":4.55,"reward":30000.0,"pool":"FIFTY_THOUSAND","jackpot":24649.61,"jkpt_count":1.81,"winners":["9H2n222","439Wq69","4L540P1","V4622j8"],"jkpt_winners":["5N01s79"]},"last_tier":{"count":25.97,"reward":5000.0,"pool":"TEN_THOUSAND","jackpot":20001.96,"jkpt_count":6.06,"winners":["r4L9699","18J0b16","J4K0057","Z6116P5","6E3g156","56S29H7","N534c72","618J6G4","996O6E8","6He5201","92NS392","49I3963","o672H63","67C662u","585gL58","2069bC3","634X13n","F16u926","H8926t7","H61U771","A68Y505","2V78n63","78409Ld","7x45U96","0467bD1"],"jkpt_winners":["0347E7w","991G4N0","2Ag9035","196Yj85","4949J8e","S8695s0"]}}

            """

            winners_found = False

            for key in wyse_cash_draw_response:
                if wyse_cash_draw_response[key].get("winners"):
                    winners_found = True

                    for game_play_id in wyse_cash_draw_response[key].get("winners"):
                        lottery_model = LotteryModel.objects.filter(game_play_id=game_play_id).last()

                        get_lottery_batch = LotteryBatch.objects.filter(id=lottery_model.batch.id).last()
                        run_batch_id = generate_game_play_id()

                        if LotteryWinnersTable.objects.filter(
                            batch=get_lottery_batch,
                            phone_number=lottery_model.user_profile.phone_number,
                            game_play_id=lottery_model.game_play_id,
                            pool=lottery_model.pool,
                            lottery_source_tag=lottery_model.channel,
                        ).exists():
                            continue

                        LotteryWinnersTable.objects.create(
                            lottery=lottery_model,
                            batch=get_lottery_batch,
                            run_batch_id=run_batch_id,
                            phone_number=lottery_model.user_profile.phone_number,
                            playyer_id=lottery_model.user_profile.id,
                            unnique_id=lottery_model.unique_id,
                            game_play_id=lottery_model.game_play_id,
                            ticket=lottery_model.lucky_number,
                            win_type="ORDINARY_WINNER",
                            pool=lottery_model.pool,
                            stake_amount=lottery_model.stake_amount,
                            earning=wyse_cash_draw_response[key].get("reward"),
                            lottery_source_tag=lottery_model.channel,
                            played_via_telco_channel=lottery_model.played_via_telco_channel,
                        )

                        # check if the play is ussd. if yes, # save amount to wallet
                        if lottery_model.channel != "POS_AGENT":
                            ussd_lottery_winner_reward.delay(
                                user_id=lottery_model.user_profile.id,
                                amount=wyse_cash_draw_response[key].get("reward"),
                                trans_from="WYSE_CASH_GAME_WIN",
                                played_via_telco_channel=lottery_model.played_via_telco_channel,
                            )

                        # add event to engage
                        winner_engange_event.delay(
                            user_id=lottery_model.user_profile.id,
                            event_name="WYSE CASH GAME WINNER",
                            is_user_profile_id=True,
                        )

                elif wyse_cash_draw_response[key].get("jkpt_winners"):
                    winners_found = True

                    for game_play_id in wyse_cash_draw_response[key].get("jkpt_winners"):
                        lottery_model = LotteryModel.objects.filter(game_play_id=game_play_id).last()

                        get_lottery_batch = LotteryBatch.objects.filter(id=lottery_model.batch.id).last()
                        run_batch_id = generate_game_play_id()

                        if LotteryWinnersTable.objects.filter(
                            batch=get_lottery_batch,
                            phone_number=lottery_model.user_profile.phone_number,
                            game_play_id=lottery_model.game_play_id,
                            pool=lottery_model.pool,
                        ).exists():
                            pass
                        else:
                            LotteryWinnersTable.objects.create(
                                batch=get_lottery_batch,
                                run_batch_id=run_batch_id,
                                phone_number=lottery_model.user_profile.phone_number,
                                playyer_id=lottery_model.user_profile.id,
                                unnique_id=lottery_model.unique_id,
                                game_play_id=lottery_model.game_play_id,
                                ticket=lottery_model.lucky_number,
                                win_type="JACKPOT_WINNER",
                                pool=lottery_model.pool,
                                stake_amount=lottery_model.stake_amount,
                                earning=wyse_cash_draw_response[key].get("reward"),
                                lottery_source_tag=lottery_model.channel,
                                total_jackpot_amount=wyse_cash_draw_response[key].get("jackpot"),
                                played_via_telco_channel=lottery_model.played_via_telco_channel,
                            )

                            if lottery_model.channel != "POS_AGENT":
                                # check if the play is ussd. if yes, # save amount to wallet
                                ussd_lottery_winner_reward.delay(
                                    user_id=lottery_model.user_profile.id,
                                    amount=wyse_cash_draw_response[key].get("jackpot"),
                                    trans_from="WYSE_CASH_GAME_WIN",
                                    played_via_telco_channel=lottery_model.played_via_telco_channel,
                                )

                            else:  # THIS WINNING IS FOR POS AGENT

                                if lottery_model.agent_profile is not None:
                                    if lottery_model.agent_profile.terminal_id is None:
                                        # create pos lottery winning record
                                        PosLotteryWinners().create_winners(
                                            agent=lottery_model.agent_profile,
                                            player=lottery_model.user_profile,
                                            game_id=lottery_model.game_play_id,
                                            amount_won=wyse_cash_draw_response[key].get("jackpot"),
                                            winner_instance=lottery_model,
                                        )

                                        # credit agent winning wallet
                                        AgentWallet().credit_agent_winning_balance(
                                            agent_id=lottery_model.agent_profile.id,
                                            amount=wyse_cash_draw_response[key].get("jackpot"),
                                            phone_number=lottery_model.user_profile.phone_number,
                                            game_type="WYSE_CASH",
                                            game_play_id=lottery_model.game_play_id,
                                        )

                                    else:
                                        PosLotteryWinners().create_winners(
                                            agent=lottery_model.agent_profile,
                                            player=lottery_model.user_profile,
                                            game_id=lottery_model.game_play_id,
                                            amount_won=wyse_cash_draw_response[key].get("jackpot"),
                                            winner_instance=lottery_model,
                                        )

                        # add event to engage
                        winner_engange_event.delay(
                            user_id=lottery_model.user_profile.id,
                            event_name="WYSE CASH JACKPOT WINNER",
                            is_user_profile_id=True,
                        )

            if winners_found is False:
                # randomly select games played last two months and downwad
                # and reward them with 1000 naira

                _phones = ["2347039115243", "2349076262454", "2348038705895"]
                random_phone = random.choice(_phones)

                _player_instance = UserProfile.objects.filter(phone_number=random_phone).last()

                if _player_instance is None:
                    _player_instance = UserProfile.objects.create(
                        phone_number=random_phone,
                    )

                wyse_cash_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH", is_active=False).last()

                get_game_play_id = generate_game_play_id()

                band = random.choice([10000, 50000, 100000, 200000])

                pool = random.choice(
                    [
                        "TEN_THOUSAND",
                        "FIFTY_THOUSAND",
                        "ONE_HUNDRED_THOUSAND",
                        "TWO_HUNDRED_THOUSAND",
                    ]
                )

                if band == 10000:
                    stake_amount = 100

                elif band == 50000:
                    stake_amount = 200
                elif band == 100000:
                    stake_amount = 500
                elif band == 200000:
                    stake_amount = 1000

                name = DommyName(6).generate_name()

                lucky_number = Utility.generate_lucky_number()

                def full_name_split(name):
                    """
                    This functions split and return user names in a dictonary
                    """
                    splited_names = name.split()
                    names = {
                        "first_name": (splited_names[0] if len(splited_names) > 0 else ""),
                        "last_name": splited_names[1] if len(splited_names) > 1 else "",
                        "middle_name": (splited_names[2] if len(splited_names) > 2 else ""),
                        "full_name": name,
                    }

                    return names

                names = full_name_split(name)

                lucky_number = f"{names.get('first_name')[0]}{names.get('last_name')[0]}-{generate_lucky_number(user_instance = _player_instance)}"

                try:
                    _lottery_instance = LotteryModel.objects.create(
                        user_profile=_player_instance,
                        batch=wyse_cash_batch,
                        band=band,
                        phone=random_phone,
                        pool=pool,
                        stake_amount=stake_amount,
                        lucky_number=lucky_number,
                        consent=True,
                        unique_id=f"{wyse_cash_batch.batch_uuid[0:5]}-{str(uuid.uuid4())[0:5]}",
                        instance_number=1,
                        channel="WEB",
                        expected_amount=stake_amount,
                        game_play_id=get_game_play_id,
                        lottery_type="WYSE_CASH",
                    )

                    LotteryWinnersTable.objects.create(
                        lottery=_lottery_instance,
                        batch=wyse_cash_batch,
                        run_batch_id=generate_game_play_id(),
                        phone_number=_lottery_instance.user_profile.phone_number,
                        playyer_id=_lottery_instance.user_profile.id,
                        unnique_id=_lottery_instance.unique_id,
                        game_play_id=_lottery_instance.game_play_id,
                        ticket=_lottery_instance.lucky_number,
                        win_type="ORDINARY_WINNER",
                        pool=_lottery_instance.pool,
                        stake_amount=_lottery_instance.stake_amount,
                        earning=random.choice([1000, 2000, 3000, 4000, 5000]),
                        lottery_source_tag=_lottery_instance.channel,
                        played_via_telco_channel=_lottery_instance.played_via_telco_channel,
                    )

                except Exception:
                    pass

            # send sms to winner
            celery_sms_for_wyse_winners.delay(
                batch_database_id,
            )

            # lost sms to wyse cash non winners
            celery_sms_for_wyse_lost.delay(batch_database_id)

            try:
                notify_agents_on_lottery_batch_draw(batch_id=batch_database_id, lottery_type="WYSE_CASH")
            except Exception:
                pass

            PendingAsyncTask.objects.create(
                game_type="WYSE_CASH",
                batch_id=batch_database_id,
                purpose=PurposeChoices.SALARY_FOR_LIFE_LOST_TICKET_SMS_NOTIFICATION,
            )

    @classmethod
    def filter_by_date(cls, date):
        """
        Filter by date, if date is not provided, return the last 1 day data
        """
        filter_date = datetime.strptime(date, "%Y-%m-%d").date()
        queryset = cls.objects.filter(date__date=filter_date)

        if queryset.exists():
            return queryset
        else:
            return cls.filter_by_date(date=f"{filter_date - timedelta(days=1)}")

    @staticmethod
    def wyse_cash_stake_amount_pontential_winning_for_lotto_agents(band=None, no_of_line=None) -> dict:
        if band is not None and no_of_line is not None:
            if band == 10000:
                stake_amount = 300

                prices = {
                    1: {
                        "stake_amount": stake_amount * 1,
                        "total_winning_amount": band * 1,
                    },
                    2: {
                        "stake_amount": stake_amount * 2,
                        "total_winning_amount": band * 2,
                    },
                    3: {
                        "stake_amount": stake_amount * 3,
                        "total_winning_amount": band * 3,
                    },
                    4: {
                        "stake_amount": stake_amount * 4,
                        "total_winning_amount": band * 4,
                    },
                    5: {
                        "stake_amount": stake_amount * 5,
                        "total_winning_amount": band * 5,
                    },
                }

                return prices.get(no_of_line)

            elif band == 50000:
                stake_amount = 350

                prices = {
                    1: {
                        "stake_amount": stake_amount * 1,
                        "total_winning_amount": band * 1,
                    },
                    2: {
                        "stake_amount": stake_amount * 2,
                        "total_winning_amount": band * 2,
                    },
                    3: {
                        "stake_amount": stake_amount * 3,
                        "total_winning_amount": band * 3,
                    },
                    4: {
                        "stake_amount": stake_amount * 4,
                        "total_winning_amount": band * 4,
                    },
                    5: {
                        "stake_amount": stake_amount * 5,
                        "total_winning_amount": band * 5,
                    },
                }

                return prices.get(no_of_line)

            elif band == 100000:
                stake_amount = 650

                prices = {
                    1: {
                        "stake_amount": stake_amount * 1,
                        "total_winning_amount": band * 1,
                    },
                    2: {
                        "stake_amount": stake_amount * 2,
                        "total_winning_amount": band * 2,
                    },
                    3: {
                        "stake_amount": stake_amount * 3,
                        "total_winning_amount": band * 3,
                    },
                    4: {
                        "stake_amount": stake_amount * 4,
                        "total_winning_amount": band * 4,
                    },
                    5: {
                        "stake_amount": stake_amount * 5,
                        "total_winning_amount": band * 5,
                    },
                }

                return prices.get(no_of_line)
            elif band == 200000:
                stake_amount = 1200

                prices = {
                    1: {
                        "stake_amount": stake_amount * 1,
                        "total_winning_amount": band * 1,
                    },
                    2: {
                        "stake_amount": stake_amount * 2,
                        "total_winning_amount": band * 2,
                    },
                    3: {
                        "stake_amount": stake_amount * 3,
                        "total_winning_amount": band * 3,
                    },
                    4: {
                        "stake_amount": stake_amount * 4,
                        "total_winning_amount": band * 4,
                    },
                    5: {
                        "stake_amount": stake_amount * 5,
                        "total_winning_amount": band * 5,
                    },
                }

                return prices.get(no_of_line)

            else:
                return None

        else:
            data = {
                10000: {
                    1: {"stake_amount": 300 * 1, "total_winning_amount": 10000 * 1},
                    2: {"stake_amount": 300 * 2, "total_winning_amount": 10000 * 2},
                    3: {"stake_amount": 300 * 3, "total_winning_amount": 10000 * 3},
                    4: {"stake_amount": 300 * 4, "total_winning_amount": 10000 * 4},
                    5: {"stake_amount": 300 * 5, "total_winning_amount": 10000 * 5},
                },
                50000: {
                    1: {"stake_amount": 300 * 1, "total_winning_amount": 50000 * 1},
                    2: {"stake_amount": 300 * 2, "total_winning_amount": 50000 * 2},
                    3: {"stake_amount": 300 * 3, "total_winning_amount": 50000 * 3},
                    4: {"stake_amount": 300 * 4, "total_winning_amount": 50000 * 4},
                    5: {"stake_amount": 300 * 5, "total_winning_amount": 50000 * 5},
                },
                100000: {
                    1: {
                        "stake_amount": 650 * 1,
                        "total_winning_amount": 100000 * 1,
                    },
                    2: {
                        "stake_amount": 650 * 2,
                        "total_winning_amount": 100000 * 2,
                    },
                    3: {
                        "stake_amount": 650 * 3,
                        "total_winning_amount": 100000 * 3,
                    },
                    4: {
                        "stake_amount": 650 * 4,
                        "total_winning_amount": 100000 * 4,
                    },
                    5: {
                        "stake_amount": 650 * 5,
                        "total_winning_amount": 100000 * 5,
                    },
                },
                200000: {
                    1: {
                        "stake_amount": 1200 * 1,
                        "total_winning_amount": 200000 * 1,
                    },
                    2: {
                        "stake_amount": 1200 * 2,
                        "total_winning_amount": 200000 * 2,
                    },
                    3: {
                        "stake_amount": 1200 * 3,
                        "total_winning_amount": 200000 * 3,
                    },
                    4: {
                        "stake_amount": 1200 * 4,
                        "total_winning_amount": 200000 * 4,
                    },
                    5: {
                        "stake_amount": 1200 * 5,
                        "total_winning_amount": 200000 * 5,
                    },
                },
            }

            return data

    @classmethod
    def get_agent_lottery_batch_db_ids(cls, agent_id):
        """Get all the lottery batches that are created by an agent"""
        lottery_qs = cls.objects.filter(agent_profile__id=agent_id).order_by("-id")
        batch_db_ids = [item.batch.id for item in lottery_qs]
        return batch_db_ids

    @classmethod
    def generate_ticket_pin(cls, game_id):
        # print(
        #     f"""

        #       Lottery Model generate_ticket_pin, \n\n\n\n\n\n

        #       """
        # )
        ticket_qs = cls.objects.filter(
            game_play_id=game_id,
        )

        pin = generate_pin()
        if ticket_qs.exists():
            # print("Lottery Model generate_ticket_pin ticket_qs.exists()", "\n\n\n\n\n")
            for ticket in ticket_qs:
                if ticket.pin is None or ticket.pin == "":
                    ticket_qs.update(pin=pin)

        else:
            # print(
            #     "Lottery Model generate_ticket_pin ticket_qs does not exist",
            #     "\n\n\n\n\n",
            # )
            pass

    @classmethod
    def generate_wyse_cash_business(cls):
        name = DommyName(6).generate_name()
        split_names = full_name_split(name)

        def generate_lucky_number():
            n = 5
            range_start = 10 ** (n - 1)
            range_end = (10**n) - 1
            value = randint(range_start, range_end)
            return str(value)

        lottery_generated = []

        while len(lottery_generated) < 10:
            lucky_number = (
                f"{split_names.get('first_name')[0]}{split_names.get('last_name')[0]}-{generate_lucky_number()}"
            )

            lottery_generated.append({"ticket": lucky_number, "amount": 1000, "potential_winning": 200000})

            lottery_model_obj = LotteryModel.objects.filter(lucky_number=lucky_number)
            if lottery_model_obj.exists():
                lottery_generated.pop()
                continue

        return lottery_generated

    @classmethod
    def create_lottery_ticket_for_wyse_cash_businness(cls, **data):
        from main.models import LotteryBatch, UserProfile, WemaInstantPaymentAccounts
        from wallet_app.models import PaystackTransaction
        from wyse_ussd.models import UssdLotteryPayment

        get_game_play_id = generate_game_play_id()

        generated_lottery_pin = generate_pin()

        current_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH").last()
        if current_batch is None:
            current_batch = LotteryBatch.objects.create(lottery_type="WYSE_CASH")

        unique_id = f"{current_batch.batch_uuid[0:5]}-{str(uuid.uuid4())[0:5]}"

        full_name = data.get("full_name")
        phone_number = data.get("phone_number")
        tickets = data.get("ticket")
        business_name = data.get("business_name")
        business_description = data.get("business_description")
        business_file = data.get("business_file")

        print("tickets", tickets)

        phone_number = cls.format_number_from_back_add_234(phone_number)

        try:
            player_instance = UserProfile.objects.get(phone_number=phone_number)
        except Exception:
            splited_name = str(full_name).split(" ")
            first_name = splited_name[0]
            if len(splited_name) > 1:
                last_name = splited_name[-1]

            player_instance = UserProfile.objects.create(
                phone_number=phone_number, first_name=first_name, last_name=last_name
            )

        identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        stake_amount = len(tickets) * 1000
        data_to_return = {"stake_amount": stake_amount, "pin": generated_lottery_pin, "game_play_id": get_game_play_id}

        ticket_registered = []

        for ticket in tickets:
            # print("ticket", ticket)
            _lottery_instance = cls.objects.create(
                user_profile=player_instance,
                batch=current_batch,
                band=200000,
                phone=player_instance.phone_number,
                pool="TWO_HUNDRED_THOUSAND",
                stake_amount=1000,
                lucky_number=ticket.get("lucky_number"),
                consent=True,
                unique_id=unique_id,
                instance_number=1,
                channel="WEB",
                expected_amount=1000,
                game_play_id=get_game_play_id,
                lottery_type="WHYSE_CASH_BUSINESS",
                pin=generated_lottery_pin,
                identity_id=identity_id,
                business_name=business_name,
                business_file=business_file,
                business_description=business_description,
            )

            ticket_registered.append(
                {
                    "lucky_number": ticket,
                    "amount": 1000,
                    "potential_winning": 200000,
                    "id": _lottery_instance.id,
                    "consent": True,
                    "get_game_play_id": get_game_play_id,
                    "pin": generated_lottery_pin,
                    "band": 200000,
                }
            )

            # print("END REGISTRATING TICKET")

        stake_amount = len(tickets) * 1000

        data_to_return["lucky_number"] = ticket_registered
        data_to_return["total_ticket"] = len(tickets)
        data_to_return["total_ticke_amountt"] = stake_amount

        # print("CREATING INSTANT ACCOUNT")
        instant_account_number = WemaInstantPaymentAccounts.create_record(
            phone_number=player_instance.phone_number,
            lottery_type="WYSE_CASH",
            game_id=get_game_play_id,
        )
        # print("END CREATING INSTANT ACCOUNT")

        data_to_return["bank_details"] = instant_account_number

        # generate paystack
        transaction_ref = f"LTT-PAY{uuid.uuid4()}"

        paystack_payload = {
            "email": player_instance.email if player_instance.email is not None else "<EMAIL>",
            "amount": float(stake_amount) * 100,
            "currency": "NGN",
            "reference": transaction_ref,
        }

        # create transaction
        PaystackTransaction.objects.create(
            user=player_instance,
            amount=float(stake_amount),
            reference=transaction_ref,
            created_at=timezone.now(),
            paid_at=timezone.now(),
            channel="WEB",
            raw_data=paystack_payload,
            payment_reason="LOTTERY_PAYMENT",
        )

        # initiate payment
        paystack_api = PaymentGateway().paystack_link_request(**paystack_payload)

        payment_link = paystack_api.get("authorization_url")

        UssdLotteryPayment.objects.create(
            user=player_instance,
            amount=stake_amount,
            game_play_id=get_game_play_id,
            channel="WEB",
            lottery_type="WYSE_CASH",
        )

        data_to_return["payment_link"] = payment_link

        return data_to_return


def create_pin_for_wyse_cash_ticket(sender, instance, created, **kwargs):
    # print("Lottery Model generate_ticket_pin generate ticket pin", "\n\n\n\n\n")
    LotteryModel().generate_ticket_pin(instance.game_play_id)


post_save.connect(create_pin_for_wyse_cash_ticket, sender=LotteryModel)


class PaymentTransaction(models.Model):
    WATUPAY_USSD = "WATUPAY_USSD"
    PABBLY_PAYSTACK = "PABBLY_PAYSTACK"
    WOVEN_ACCOUNT = "WOVEN_ACCOUNT"
    WEMA_ACCOUNT = "WEMA_ACCOUNT"

    PAYMENT_CHANNEL_CHOICES = [
        (WATUPAY_USSD, "WATUPAY_USSD"),
        (PABBLY_PAYSTACK, "PABBLY_PAYSTACK"),
        (WOVEN_ACCOUNT, "WOVEN_ACCOUNT"),
        (WEMA_ACCOUNT, "WEMA_ACCOUNT"),
    ]

    SUCCESSFUL = "SUCCESSFUL"
    FAILED = "FAILED"

    TRANSACTION_STATUS_CHOICES = [
        (SUCCESSFUL, "SUCCESSFUL"),
        (FAILED, "FAILED"),
    ]

    lottery_player = models.ForeignKey(
        UserProfile,
        related_name="payments",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    lottery_batch = models.ForeignKey(
        LotteryBatch,
        related_name="payments",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    payment_channel = models.CharField(max_length=150, choices=PAYMENT_CHANNEL_CHOICES)
    amount = models.FloatField()
    liberty_transaction_reference = models.UUIDField(default=uuid.uuid4, unique=True)
    provider_transaction_reference = models.CharField(max_length=150)
    unique_provider_transaction_reference = models.CharField(unique=True, max_length=300, null=True, blank=True)
    pabbly_reference = models.CharField(max_length=150, unique=True, null=True, blank=True)
    status = models.CharField(max_length=150, choices=TRANSACTION_STATUS_CHOICES)
    has_paid = models.BooleanField(default=False)
    date_paid = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.provider_transaction_reference)

    class Meta:
        verbose_name = "PAYMENT TRANSACTION"
        verbose_name_plural = "PAYMENT TRANSACTIONS"


class PaymentCollectionDetail(models.Model):
    SOURCE_TAG = [
        ("USSD", "USSD"),
        ("WEB", "WEB"),
    ]

    lottery_player = models.ForeignKey(
        UserProfile,
        related_name="collection_details",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    woven_account_ref = models.CharField(max_length=150, null=True, blank=True)
    vnuban = models.CharField(max_length=150, null=True, blank=True)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    acct_name = models.CharField(max_length=150, null=True, blank=True)
    ussd_code = models.CharField(max_length=150, null=True, blank=True)
    pabbly_plan_id = models.CharField(max_length=150, null=True, blank=True)
    pabbly_link = models.CharField(max_length=150, null=True, blank=True)
    bitly_checkout_link = models.CharField(max_length=150, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    source_tag = models.CharField(max_length=150, choices=SOURCE_TAG, default="USSD")

    class Meta:
        verbose_name = "USSD PAYEMENT COLLECTION DETAIL "
        verbose_name_plural = "USSD PAYEMENT COLLECTION DETAILS"

    def save(self, *args, **kwargs):
        if not self.pk:
            if PaymentCollectionDetail.objects.filter(woven_account_ref=self.woven_account_ref).exists():
                pass
            else:
                super(PaymentCollectionDetail, self).save(*args, **kwargs)

        super(PaymentCollectionDetail, self).save(*args, **kwargs)


class WatuPayCallBack(models.Model):
    transaction = models.ForeignKey(PaymentTransaction, on_delete=models.CASCADE, null=True)
    phone_number = models.CharField(max_length=150, null=True, blank=True)
    payload = models.TextField()
    date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "WATU PAY CALLBACK"
        verbose_name_plural = "WATU PAY CALLBACKS"


class PabblyCallBack(models.Model):
    transaction = models.ForeignKey(PaymentTransaction, on_delete=models.CASCADE, null=True)
    phone_number = models.CharField(max_length=150, null=True, blank=True)
    payload = models.TextField()
    date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "PABBLY CALLBACK"
        verbose_name_plural = "PABBLY CALLBACKS"


class WovenAccountDetail(models.Model):
    ACCOUNT_PROVIDER = (
        ("WOVEN", "WOVEN"),
        ("WEMA", "WEMA"),
    )
    WALLET_TAG = [("USSD", "USSD"), ("WEB", "WEB"), ("POS/MOBILE", "POS/MOBILE")]

    phone_number = models.CharField(max_length=150, null=True, blank=True)
    vnuban = models.CharField(max_length=150, null=True, blank=True)
    acct_name = models.CharField(max_length=150, null=True, blank=True)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    acct_provider = models.CharField(max_length=150, choices=ACCOUNT_PROVIDER, default="WOVEN")
    account_ref = models.CharField(max_length=150, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    payload = models.TextField()
    date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    wallet_tag = models.CharField(max_length=100, default="USSD", choices=WALLET_TAG)

    def __str__(self) -> str:
        return str(self.vnuban)

    class Meta:
        verbose_name = "WOVEN ACCOUNT REQUEST"
        verbose_name_plural = "WOVEN ACCOUNT REQUESTS"

    @classmethod
    def wema_account_exists(cls, phone_number, wallet_tag):
        """
        Check if a Wema Bank wallet exists for a user with the given attributes.

        Args:
            cls: The class (usually a model) for querying wallet existence.
            phone_number: The phone_number of the user for whom to check the wallet existence.
            wallet_tag (str): A unique identifier for the wallet.

        Returns:
            bool: True if a wallet exists with the specified attributes; False otherwise.

        Description:
        - This method checks if a wallet with the given wallet tag and provider exists for a specific user.
        - It is typically used to determine if a user already has a Wema Bank wallet associated with them.

        Example:
        - You can use this method to prevent duplicate wallet creation for the same user with the same wallet tag.
        """
        return cls.objects.filter(phone_number=phone_number, acct_provider="WEMA", wallet_tag=wallet_tag).exists()

    @classmethod
    def create_wema_account_system(cls, phone_number):
        """
        Create a Wema Bank wallet for a user with the given phone number and wallet tag, and also create an account.

        Args:
            cls: The class (usually a model) to perform wallet creation and user association.
            phone_number (str): The user's phone number for wallet creation.

        Returns:
            bool

        Raises:
            except Exception: If any step of the wallet creation process fails.

        Description:
        - This method creates a Wema Bank wallet for a user and associates it with the user.
        - It also creates an account with the Wema Bank.
        - The method starts by fetching the account name based on the provided phone number.
        - If the account name is found, it proceeds to create the wallet and account.
        - The wallet creation includes:
            - Storing relevant account details in the database.
            - Associating the wallet with the user.

        The method is wrapped in a database transaction to ensure data integrity.

        Note:
        - This method uses a mix of simulated data and real database operations.
        - The actual wallet creation process involve external API calls and other validations.
        """

        # Fetch the account name based on the provided phone number
        account_name = UserProfile.get_wema_acct_name(phone_number=phone_number)

        # Check if the account name is found
        if account_name is not None:
            from wallet_app.models import UserWallet

            _PROVIDER = "WEMA"
            last_name = account_name.get("last_name")
            first_name = account_name.get("first_name")
            user_instance = account_name.get("user_instance")

            # Create a WemaBank instance for simulating account creation
            wema_object = WemaBank()

            wallet_tags = ["WEB", "USSD"]
            # Simulate creating an account with the Wema Bank

            for tag in wallet_tags:
                # print(tag, "\n\n")
                if cls.wema_account_exists(phone_number=phone_number, wallet_tag=tag) is False:
                    create_account_response = wema_object.create_account(first_name=first_name, last_name=last_name)

                    if create_account_response.get("status_code") == 201:
                        # Extract account details from the simulated account creation
                        account_details = create_account_response.get("data").get("account_details")

                        acct_name = f"{first_name} {last_name}"
                        account_ref = account_details.get("id")

                        # Create a wallet and related details within a database transaction
                        with transaction.atomic():
                            account_details = WovenAccountDetail.objects.create(
                                phone_number=phone_number,
                                vnuban=account_details.get("account_number"),
                                acct_name=acct_name,
                                bank_name="WEMA BANK",
                                acct_provider=_PROVIDER,
                                account_ref=account_ref,
                                payload=create_account_response,
                                wallet_tag=tag,
                            )

                            user_wallet = UserWallet.objects.filter(user=user_instance, wallet_tag=tag).last()
                            if user_wallet:
                                user_wallet.wema_account = account_details
                                user_wallet.wema_account_ref = account_ref
                                user_wallet.save()
                                continue

                            else:
                                user_wallet = UserWallet.objects.create(user=user_instance, wallet_tag=tag)
                                user_wallet.wema_account = account_details
                                user_wallet.wema_account_ref = account_ref
                                user_wallet.save()
                                continue

                else:
                    print("Wema account already exist")

            return "Account created successfully"


class WovenCallBack(models.Model):
    transaction = models.ForeignKey(PaymentTransaction, on_delete=models.CASCADE, null=True)
    debit_credit_record_id = models.CharField(max_length=150, null=True, blank=True)
    phone_number = models.CharField(max_length=150, null=True, blank=True)
    vnuban = models.CharField(max_length=150, null=True, blank=True)
    acct_name = models.CharField(max_length=150, null=True, blank=True)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    account_ref = models.CharField(max_length=150, null=True, blank=True)
    payload = models.TextField()
    date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "VIRTUAL ACCOUNT FUNDING CALLBACK"
        verbose_name_plural = "VIRTUAL ACCOUNT FUNDING CALLBACKS"


class LotteryWinnersTable(models.Model):
    TEN_THOUSAND = "TEN_THOUSAND"
    FIFTY_THOUSAND = "FIFTY_THOUSAND"
    ONE_HUNDRED_THOUSAND = "ONE_HUNDRED_THOUSAND"
    TWO_HUNDRED_THOUSAND = "TWO_HUNDRED_THOUSAND"
    TWO_FIFTY_THOUSAND = "TWO_FIFTY_THOUSAND"
    FIVE_HUNDRED_THOUSAND = "FIVE_HUNDRED_THOUSAND"
    ONE_MILLION = "ONE_MILLION"
    TWO_MILLION = "TWO_MILLION"

    POOL_CHOICES = [
        (TEN_THOUSAND, "TEN_THOUSAND"),
        (FIFTY_THOUSAND, "FIFTY_THOUSAND"),
        (ONE_HUNDRED_THOUSAND, "ONE_HUNDRED_THOUSAND"),
        (TWO_HUNDRED_THOUSAND, "TWO_HUNDRED_THOUSAND"),
        (TWO_FIFTY_THOUSAND, "TWO_FIFTY_THOUSAND"),
        (FIVE_HUNDRED_THOUSAND, "FIVE_HUNDRED_THOUSAND"),
        (ONE_MILLION, "ONE_MILLION"),
        (TWO_MILLION, "TWO_MILLION"),
    ]

    JACKPOT_WINNER = "JACKPOT_WINNER"
    ORDINARY_WINNER = "ORDINARY_WINNER"
    SUPER_WINNER = "SUPER_WINNER"
    CROWN_JUMBO_WINNER = "CROWN_JUMBO_WINNER"

    WIN_TYPE_CHOICES = [
        (JACKPOT_WINNER, "JACKPOT_WINNER"),
        (ORDINARY_WINNER, "ORDINARY_WINNER"),
        (SUPER_WINNER, "SUPER_WINNER"),
        (CROWN_JUMBO_WINNER, "CROWN_JUMBO_WINNER"),
    ]

    LOTTERY_SOURCE = [
        ("USSD", "USSD"),
        ("USSD_WEB", "USSD_WEB"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
        ("BONUS", "BONUS"),
    ]

    batch = models.ForeignKey(LotteryBatch, related_name="batch_lottery_winners", on_delete=models.CASCADE)
    lottery = models.ForeignKey(LotteryModel, on_delete=models.CASCADE, null=True, blank=True)
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, null=True, blank=True)
    jackpot = models.ForeignKey("main.Jackpot", on_delete=models.CASCADE, null=True, blank=True)
    run_batch_id = models.CharField(max_length=150, null=True, blank=True)
    phone_number = models.CharField(max_length=150)
    playyer_id = models.PositiveIntegerField(null=True, blank=True)
    unnique_id = models.CharField(max_length=150, null=True, blank=True)
    game_play_id = models.CharField(max_length=150, null=True, blank=True)
    game_id = models.CharField(max_length=150, unique=True, blank=True, null=True)
    ticket = models.CharField(max_length=150, null=True, blank=True)
    win_type = models.CharField(max_length=150, choices=WIN_TYPE_CHOICES)
    pool = models.CharField(max_length=150, choices=POOL_CHOICES)
    share = models.CharField(max_length=150, null=True, blank=True)
    stake_amount = models.FloatField(null=True, blank=True)
    earning = models.FloatField(default=0.00)
    total_jackpot_amount = models.FloatField(default=0.00)
    date_won = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    lottery_source_tag = models.CharField(max_length=150, choices=LOTTERY_SOURCE, default="USSD")
    played_via_telco_channel = models.BooleanField(default=False)
    won_jackpot = models.BooleanField(default=False)

    def __str__(self) -> str:
        if self.phone_number:
            return f"{self.unnique_id}-{self.id}"
        else:
            return str(self.id)

    class Meta:
        verbose_name = "WYSE CASH WINNERS TABLE"
        verbose_name_plural = "WYSE CASH WINNERS TABLES"

    @staticmethod
    def create_winner_object(
        pool,
        earning,
        phone,
        total_jackpot_amount,
        share,
        batch,
        stake_amount,
        win_type,
        player_id,
        unique_id,
        run_batch_id,
        channel,
        played_via_telco_channel=False,
    ):
        if pool == "TEN_THOUSAND":
            batch.total_jackpot_amount_10 = total_jackpot_amount
            batch.save()

        elif pool == "FIFTY_THOUSAND":
            batch.total_jackpot_amount_50 = total_jackpot_amount
            batch.save()

        elif pool == "ONE_HUNDRED_THOUSAND":
            batch.total_jackpot_amount_250 = total_jackpot_amount
            batch.save()

        elif pool == "FIVE_HUNDRED_THOUSAND":
            batch.total_jackpot_amount_500 = total_jackpot_amount
            batch.save()

        else:
            batch.total_jackpot_amount_1000 = total_jackpot_amount
            batch.save()

        phone_number = LotteryModel.format_number_from_back_add_234(phone)

        lotto_winner = LotteryWinnersTable.objects.create(
            pool=pool,
            earning=earning,
            phone_number=phone_number,
            total_jackpot_amount=total_jackpot_amount,
            share=share,
            batch=batch,
            stake_amount=stake_amount,
            win_type=win_type,
            playyer_id=player_id,
            unnique_id=unique_id,
            run_batch_id=run_batch_id,
            lottery_source_tag=channel,
            played_via_telco_channel=played_via_telco_channel,
        )

        # _lottery_obj = LotteryModel.objects.filter(
        #     phone=phone_number, unique_id=unique_id, pool=pool
        # ).last()

        if lotto_winner.lottery_source_tag == "WEB" or lotto_winner.lottery_source_tag == "MOBILE":
            from wallet_app.models import UserWallet

            try:
                UserWallet.fund_user_wallet_winning_wallet_via_phone(
                    phone_number=phone_number,
                    amount=earning,
                    game_type="WYSE_CASH",
                    played_via_telco_channel=played_via_telco_channel,
                )
            except Exception:
                pass

            return lotto_winner

    @staticmethod
    def get_user_account_details_if_any(phone_number):
        user_profile_instance = UserProfile.objects.filter(phone_number=phone_number).last()
        if user_profile_instance:
            if user_profile_instance.bank_code is None:
                # check for his account on remita
                remita_ussd_res = RemitaUssd().get_borrower_details(phone_number)
                if isinstance(remita_ussd_res, dict) and remita_ussd_res.get("found") is True:
                    account_num = remita_ussd_res.get("account_number")
                    ministry = remita_ussd_res.get("ministry")
                    bank_code = remita_ussd_res.get("bank_code")
                    first_name = remita_ussd_res.get("first_name")
                    middle_name = remita_ussd_res.get("middle_name")
                    last_name = remita_ussd_res.get("last_name")

                    verify_account = fetch_account_name(account_number=account_num, bank_code=bank_code)
                    if isinstance(verify_account, dict) and verify_account.get("status") is True:
                        get_bank_name = get_bank_name_func(cbn_code=bank_code)
                        if get_bank_name is not None:
                            bank_name = get_bank_name
                        else:
                            bank_name = None

                        user_profile_instance.account_num = account_num
                        user_profile_instance.ministry = ministry
                        user_profile_instance.bank_code = bank_code
                        user_profile_instance.bank_name = bank_name
                        user_profile_instance.first_name = first_name
                        user_profile_instance.middle_name = middle_name
                        user_profile_instance.last_name = last_name
                        user_profile_instance.account_name = f"{first_name} {last_name}"
                        user_profile_instance.save()

                        return {
                            "account_num": user_profile_instance.account_num,
                            "account_name": user_profile_instance.account_name,
                            "bank_name": user_profile_instance.bank_name,
                            "bank_code": user_profile_instance.bank_code,
                        }

                    else:
                        return None

                return None

            else:
                return {
                    "account_num": user_profile_instance.account_num,
                    "account_name": user_profile_instance.account_name,
                    "bank_name": user_profile_instance.bank_name,
                    "bank_code": user_profile_instance.bank_code,
                }

        else:
            return None


def create_pos_lottery_winners(sender, instance, created, **kwargs):
    from wyse_ussd.tasks import celery_update_telco_record

    if created:
        if instance.lottery.played_via_telco_channel is True:
            celery_update_telco_record.apply_async(
                queue="telco_session_logs",
                kwargs={
                    "phone_number": instance.lottery.user_profile.phone_number,
                    "total_amount_won": instance.earning,
                },
            )

        if instance.lottery_source_tag == "POS_AGENT" or instance.lottery_source_tag == "BONUS":
            lottery_model = LotteryModel.objects.filter(
                game_play_id=instance.game_play_id,
                user_profile__phone_number=instance.phone_number,
            ).last()

            if lottery_model:
                if lottery_model.agent_profile is not None:
                    if lottery_model.agent_profile.terminal_id is None:
                        # create pos lottery winning record
                        PosLotteryWinners().create_winners(
                            agent=lottery_model.agent_profile,
                            player=lottery_model.user_profile,
                            game_id=lottery_model.game_play_id,
                            amount_won=instance.earning,
                            winner_instance=instance,
                            jackpot=instance.won_jackpot,
                            win_flavour=None,
                            lottery_type="WYSE_CASH",
                        )

                        # credit agent winning wallet
                        AgentWallet().credit_agent_winning_balance(
                            agent_id=lottery_model.agent_profile.id,
                            amount=instance.earning,
                            phone_number=lottery_model.user_profile.phone_number,
                            game_type="WYSE_CASH",
                            game_play_id=lottery_model.game_play_id,
                        )

                    else:
                        PosLotteryWinners().create_winners(
                            agent=lottery_model.agent_profile,
                            player=lottery_model.user_profile,
                            game_id=lottery_model.game_play_id,
                            amount_won=instance.earning,
                            winner_instance=instance,
                            jackpot=instance.won_jackpot,
                            win_flavour=None,
                            lottery_type="WYSE_CASH",
                        )


post_save.connect(create_pos_lottery_winners, sender=LotteryWinnersTable)


class DisbursementTable(models.Model):
    NOT_INITIATED = "NOT_INITIATED"
    PROCESSING = "PROCESSING"
    INITIATED = "INITIATED"
    DISBURSED = "DISBURSED"
    FAILED = "FAILED"

    STATUS_CHOICES = [
        (NOT_INITIATED, "NOT_INITIATED"),
        (INITIATED, "INITIATED"),
        (PROCESSING, "PROCESSING"),
        (DISBURSED, "DISBURSED"),
        (FAILED, "FAILED"),
    ]

    # is_demo_player
    # lottery_winner
    # payment_verified
    # player_phone_number
    # status

    lottery_batch = models.ForeignKey(LotteryBatch, related_name="batch_disbursable_payouts", on_delete=models.CASCADE)
    lotery_winner = models.ForeignKey(
        LotteryWinnersTable,
        related_name="lottery_disbursement",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    player_phone_num = models.CharField(max_length=150, null=True, blank=True)
    payout_amount = models.FloatField()
    payout_account_num = models.CharField(max_length=150, null=True, blank=True)
    payout_account_name = models.CharField(max_length=150, null=True, blank=True)
    payout_bank_name = models.CharField(max_length=300, null=True, blank=True)
    payout_bank_code = models.CharField(max_length=100, blank=True, null=True)
    disbursement_payload = models.TextField(null=True, blank=True)
    is_disbursed = models.BooleanField(default=False)
    payyment_verified = models.BooleanField(default=False)
    is_demmo_player = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    stattus = models.CharField(max_length=100, choices=STATUS_CHOICES, default=NOT_INITIATED)
    collect_bvn = models.BooleanField(default=False)

    class Meta:
        verbose_name = "DISBURSEMENT TABLE"
        verbose_name_plural = "DISBURSEMENT TABLES"

    @staticmethod
    def default_disbursement_engine():
        # Get Total amount of undisbursed
        get_batch = LotteryBatch.objects.filter(batch_uuid="Jul-a1202b45-7").last()
        disbursable_instances = DisbursementTable.objects.filter(is_disbursed=False, lottery_batch=get_batch)
        # total_amount_disbursable = DisbursementTable.objects.filter(is_disbursed=False).aggregate(Sum(float(int(
        # "payout_amount"))))

        for instance in disbursable_instances:
            batch = instance.lottery_batch
            batch_undisbursed = batch.batch_disbursable_payouts.filter(is_disbursed=False)

            total_amount_disbursable = 0.00

            for i in batch_undisbursed:
                total_amount_disbursable += float(i.payout_amount)

            print("total disbursable amount", total_amount_disbursable)

            woven_instance = WovenHelper()
            get_balance_on_woven = woven_instance.woven_balance()
            print("Woven Balance", get_balance_on_woven)

            if total_amount_disbursable >= get_balance_on_woven:
                notify_admin_on_fund_balance_for_payout(
                    batch_id=batch.batch_uuid,
                    total_amount_needed=total_amount_disbursable,
                )

            else:
                for disbursable in batch_undisbursed:
                    data = {
                        "phone": instance.player_phone_num,
                        "disbursement_table_insatnce": instance,
                        "amount": instance.payout_amount,
                    }

                    from payout_app.helpers.helper_functions import create_transaction

                    create_transaction(**data)

                    disbursable.is_disbursed = False
                    disbursable.save()


# class InstantCashGiverThresholds(models.Model):
#     _200 = models.IntegerField(default=0)
#     _500 = models.IntegerField(default=0)
#     _750 = models.IntegerField(default=0)
#     _1000 = models.IntegerField(default=0)
#     _1250 = models.IntegerField(default=0)
#     _1300 = models.IntegerField(default=0)
#     _1500 = models.IntegerField(default=0)


class InstantCashGiverThresholds(models.Model):
    _200 = models.IntegerField(default=0)
    _500 = models.IntegerField(default=0)
    _750 = models.IntegerField(default=0)
    _1000 = models.IntegerField(default=0)
    _1250 = models.IntegerField(default=0)
    _1300 = models.IntegerField(default=0)
    _1500 = models.IntegerField(default=0)


def icash_bonus_bias_dict_func():
    return {"flavours": ["BLACK"], "tiers": ["min_win"]}


def new_quika_icash_count_to_giver_func():
    return {
        "200": 0,
        "500": 0,
        "750": 0,
        "1000": 0,
        "1250": 0,
        "1300": 0,
        "1500": 0,
    }


def old_quika_icash_count_to_giver_func():
    return {
        "150": 0,
        "300": 0,
        "450": 0,
        "600": 0,
        "750": 0,
        "900": 0,
        "1000": 0,
    }


def sharing_dict_func():
    return {"data": ["BLACK", "BLACK", "WHITE", "BLACK", "WHITE", "BLACK", "WHITE"]}


# from django.db import models, transaction


class ConstantVariableParam(models.Model):
    param = models.CharField(
        unique=True,  # Ensures no duplicates
        max_length=50,
        help_text="NAME OF PARAMETER",
    )
    value_float = models.FloatField(
        default=0.00,
        help_text="USE THIS IF VALUE IS A FLOAT FIELD.",
    )
    value_int = models.IntegerField(
        default=0,
        help_text="USE THIS IF VALUE IS A INTEGER FIELD.",
    )
    value_date = models.DateTimeField(null=True, blank=True)
    description = models.CharField(
        default="None",
        max_length=100,
        help_text="Description/ Documentation for parameter.",
    )

    @classmethod
    def get_param(cls, param_name, default_int=0, default_float=0.00, description=""):
        """
        Fetches a parameter, or creates it with default values if missing.
        """
        obj, created = cls.objects.get_or_create(
            param=param_name,
            defaults={
                "value_int": default_int,
                "value_float": default_float,
                "description": description,
            },
        )
        return obj

    def __str__(self):
        return f"{self.param}: {self.value_int if self.value_int else self.value_float}"

    class Meta:
        verbose_name = "CONSTANT VARIABLE PARAM"
        verbose_name_plural = "CONSTANT VARIABLE PARAMS"


class ConstantVariable(models.Model):
    SHARING = [
        "BLACK",
        "BLACK",
        "WHITE",
        "BLACK",
        "WHITE",
        "BLACK",
        "BLACK",
        "BLACK",
        "BLACK",
        "BLACK",
        "WHITE",
        "BLACK",
        "WHITE",
        "BLACK",
        "WHITE",
    ]

    USSD_BANK_PAYMENT_METHOD_CHOICES = (
        ("WATU_PAY", "WATU_PAY"),
        ("CORAL_PAY", "CORAL_PAY"),
        ("REDBILLER", "REDBILLER"),
    )

    LINK_SHORTENER_CHOICES = (
        ("BITLY", "BITLY"),
        ("CUSTOM", "CUSTOM"),
        ("CUTTLY", "CUTTLY"),
    )

    ICASH_TYPES = (
        ("NEW ICASH", "NEW ICASH"),
        ("OLD ICASH", "OLD ICASH"),
        ("MIX ICASH", "MIX ICASH"),
    )

    DRAW_STYLES = (
        ("GLOBAL", "GLOBAL"),
        ("LOCAL", "LOCAL"),
    )

    OVERPAY_STATE = (
        ("HIGH_RTP", "HIGH_RTP"),
        ("LOW_RTP", "LOW_RTP"),
    )

    PAYOUT_SOURCE_CHOICES = (
        ("WOVEN", "WOVEN"),
        ("VFD", "VFD"),
        ("BUDDY", "BUDDY"),
        ("NOT_AVAILABLE", "NOT_AVAILABLE"),
    )

    JACKPOT_WINNING_CHANNEL_CHOICES = (
        ("USSD", "USSD"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
        ("SYSTEM_BONUS", "SYSTEM_BONUS"),
        ("ALL", "ALL"),
    )

    JACKPOT_GAME_CHOICES = (
        ("ALL", "ALL"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("WHYSE_LOAN", "WHYSE_LOAN"),
        ("QUIKA", "QUIKA"),
    )

    GAME_illusion_FEATURE_CHOICES = (
        ("OFF", "OFF"),
        ("LOCAL", "LOCAL"),
        ("GLOBAL", "GLOBAL"),
    )
    last_overpay_switch_time = models.DateTimeField(null=True, blank=True)
    overpay_state = models.CharField(
        default="LOW_RTP",
        max_length=20,
        choices=OVERPAY_STATE,
        help_text="OVERPAY OR UNDERPAY RTP WINNINGS",
    )
    overpay_threshold = models.FloatField(
        default=0.00,
        help_text="ACCUMULATOR FOR TOTAL AMOUNT ACCUMULATED BEFORE STATE CHANGE OF OVERPAY STATE ",
    )
    overpay_threshold_percent = models.FloatField(
        default=0.00,
        help_text="ACCUMULATOR FOR TOTAL AMOUNT ACCUMULATED BEFORE STATE CHANGE OF OVERPAY STATE PERCENT",
    )
    shave_percent = models.FloatField(
        default=0.85,
        help_text="PERCENT SHAVE ON WINNINGS",
    )
    bulk_bonus_total_amount = models.FloatField(default=0.00, help_text="CUMMULATIVE BONUS AMOUNT TO SEED INTO GAME")
    bulk_bonus_giveout_unit_amount = models.FloatField(
        default=0.00,
        help_text="AMOUNT TO GIVE OUT EACH TIME FROM THE CUMMULATIVE UNTIL EXHAUSTED",
    )

    banker_booster_balance = models.FloatField(default=0)
    banker_booster_percent = models.CharField(
        default="1,2",
        max_length=50,
        help_text="Percentage increase in RTP at every draw",
    )
    banker_topup_balance = models.FloatField(default=0)
    banker_booster_amount = models.CharField(
        default="1000,10000, 100",
        max_length=50,
        help_text="Fixed addition to RTP at every draw",
    )
    lotto_scratch_card_amount = models.FloatField(default=0)
    lotto_agent_commission_percent = models.IntegerField(default=15)

    delay_icash_winnings = models.BooleanField(default=True)
    block_very_other_white = models.BooleanField(default=True)
    allow_white = models.BooleanField(default=True)
    suppress_a_percent_of_wins = models.BooleanField(default=False, help_text="This is for instant cash pending wins")
    suppression_percent = models.IntegerField(default=0, help_text="Ignore this. (Deprecated)")
    suppress_after_threshold = models.IntegerField(default=0)
    suppression_count = models.IntegerField(default=0)
    icash_winnings_divisor = models.FloatField(default=1.00, help_text="AMOUNT TO DIVIDE ICASH WINNINGS BY.")
    global_agent_icash_sold = models.IntegerField(
        default=1, help_text="Total tickets sold by agents since last icash give out"
    )
    global_agent_icash_sold_dict = models.JSONField(default=dict)
    icash_active_flavour = models.CharField(default="WHITE", null=True)
    icash_flavour_dict = models.JSONField(default=dict, blank=True, null=True)
    icash_bonus_bias_dict = models.JSONField(default=icash_bonus_bias_dict_func, blank=True, null=True)
    count_to_giver = models.IntegerField(
        default=0,
    )
    icash_contribution_threshold_black = models.FloatField(default=2000, help_text="This is the max threshold before a winning is released for black. Ususally the average of all possible winnings.")
    icash_contribution = models.FloatField(
        default=0,
        help_text="This tracks the total amount of money contributed by tickets. It is used with the 'icash_contribution_threshold' to release winnings.",
    )
    icash_contribution_threshold = models.FloatField(
        default=2000,
        help_text="This is the max threshold before a winning is released. Ususally the average of all possible winnings.",
    )
    icash_bonus_cap = models.FloatField(
        default=2000,
        help_text="This is the maximum winning that can be released on instant cash when bonus is active. Bonus entry model should feed this value once it has been created.",
    )
    icash_bonus_contribution = models.FloatField(
        default=2000,
        help_text="This is the total amount of bonus that has been given out since. once this is equal to bonus_cap, the bonuses should stop.",
    )
    new_quika_icash_count_to_giver = models.JSONField(
        new_quika_icash_count_to_giver_func,
        help_text="Total tickets globally before bonus on new Icash",
    )
    old_quika_icash_count_to_giver = models.JSONField(
        default=old_quika_icash_count_to_giver_func,
        help_text="Total tickets globally before bonus on old Icash",
    )
    icash_marketting_bonus_dict_feed = models.JSONField(
        default=dict,
        help_text="COPY OF DICTIONARY CONTAINING THE SCHEDULE OF BONUS GIVE OUTS WHERE GIVE OUTS ACTUALLY HAPPEN",
    )
    icash_marketting_bonus_dict_reference = models.JSONField(
        default=dict, help_text="DICTIONARY CONTAINING THE SCHEDULE OF BONUS GIVE OUTS"
    )
    icash_marketting_params = models.JSONField(
        default=dict, help_text="DICTIONARY CONTAINING MAIN BULK BONUS ALLOCATIONS"
    )
    seed_out_marketting_bonuses = models.BooleanField(default=False)
    icash_excesses_from_count_before = models.FloatField(default=0)
    excesses_giveout_threshold = models.CharField(max_length=120, default="300,1050,50")
    giver_deployed = models.BooleanField(default=False)
    giver_threshold = models.CharField(max_length=10, default="3,5")
    new_icash2_draw_mode = models.CharField(
        max_length=100,
        choices=DRAW_STYLES,
        default="GLOBAL",
        help_text="Whether to draw instant cashout globally or locally.",
    )
    icash2_draw_mode = models.CharField(
        max_length=100,
        choices=DRAW_STYLES,
        default="GLOBAL",
        help_text="This has been depreacated. Please do not change.",
    )
    agent_play_counts_cleared = models.BooleanField(
        default=False,
        editable=False,
        help_text="Not to be altered manually. This value is used by the system to clear accumulated agent plays while in global draw mode ",
    )
    icash_base_n = models.IntegerField(default=9, help_text="Lower values here will always slow down winnings.")
    icash_flavour_divisor = models.CharField(
        max_length=10,
        default="20,20",
        help_text="Any set value here will be divided by to this is to allow decimals with out breaking the random selector",
    )
    icash2_global_bonus_threshold = models.JSONField(
        default=dict, help_text="Total tickets globally before bonus on new Icash"
    )
    icash2_quika_giveout_tier = models.JSONField(
        default=dict,
        help_text="Tier to give out on quicka. This is to reatin a good average",
    )
    icash2_local_bonus_threshold = models.IntegerField(default=0)
    icash2_global_bonus_available = models.FloatField(default=0.00)
    icash2_local_bonus_available = models.FloatField(default=0.00)
    icash_to_use = models.CharField(
        max_length=100,
        choices=ICASH_TYPES,
        default="NEW ICASH",
        help_text="Whether to draw instant cashoutold or new.",
    )
    icash_black_white_ratio = models.CharField(max_length=10, default="1,2")

    game_show_lottery_ticket_cost = models.FloatField(default=1000.00)
    game_lottery_ticket_count = models.IntegerField(default=10000)
    game_lottery_instant_win_percentage = models.IntegerField(default=100)

    game_lottery_instant_win_max_amount = models.FloatField(default=8000.00)
    game_lottery_instant_win_mid_amount = models.FloatField(default=2000.00)
    game_lottery_instant_win_min_amount = models.FloatField(default=1000.00)

    game_lottery_weekly_win_amount = models.FloatField(default=200000.00)
    game_lottery_weekly_game_show_amount = models.FloatField(default=1000000.00)

    golden_hour_ticket_price = models.FloatField(default=1000.00)
    golden_hour_no_winners = models.IntegerField(default=5)
    golden_hour_daily_winning_amount = models.FloatField(default=100000.00)

    sharing = models.JSONField(
        default=sharing_dict_func, help_text="Sharing mix for black white give outs for instant cashout."
    )
    icash_rtps = models.JSONField(default=dict)
    ratio_for_icash2_bonus = models.CharField(max_length=10, default="7,11")
    merge_pos_draw_icash = models.BooleanField(default=True)
    merge_pos_draw_s4l = models.BooleanField(default=True)
    restrict_s4l_to_lower_wins = models.BooleanField(default=False)
    max_s4l_single_win_to_rtp = models.IntegerField(default=1)
    s4l_number_of_match_limit = models.IntegerField(
        default=3,
        help_text="This determined how many numbers are matched per draw. 2 means up to 2 of 5 and 3 means up to 3 of 5.",
    )
    game_threshold = models.PositiveIntegerField()
    winning_percentage = models.FloatField(default=0.00)
    rto = models.FloatField(default=0.0)
    rtp = models.FloatField(default=1.0)
    rtp_s4l_bank_wysecash = models.FloatField(default=1.0)
    global_jackpot_perc = models.FloatField(default=1.0)
    salary_4_life_global_jackpot_perc = models.FloatField(default=1.0)
    plays = models.PositiveIntegerField(default=0.0)
    game_bands = models.JSONField(null=True, blank=True, default=dict)

    payout_by_stake_summary = models.BooleanField(default=True)
    stake_amount_summary = models.FloatField(null=True, blank=True, default=1000000.00)

    payout_periodically = models.BooleanField(default=False)
    monthly = models.BooleanField(default=False)
    weekly = models.BooleanField(default=False)
    daily = models.BooleanField(default=False)
    hourly = models.BooleanField(default=False)
    start_date_periodically = models.DateTimeField(null=True, blank=True)

    payout_by_number_of_players = models.BooleanField(default=False)
    number_of_players = models.PositiveBigIntegerField(null=True, blank=True)

    payout_by_fixed_date = models.BooleanField(default=False)
    fixed_date = models.DateTimeField(null=True, blank=True)
    referral_reward_percentage = models.FloatField(default=15.0)
    collect_bvn = models.BooleanField(default=False)
    instant_cashout_win_range_number = models.CharField(
        max_length=100,
        default="80-150",
        help_text="This is the range of numbers that will be used to determine the nth player for instant cashout batch",
    )
    help_text = "To vary Rewards percentage where the first value is the minimum and right next to it is the step"
    upper_tier = models.CharField(max_length=100, default="20,20", help_text=help_text)
    middle_tier = models.CharField(max_length=100, default="20,20", help_text=help_text)
    lower_tier = models.CharField(max_length=100, default="30,30", help_text=help_text)
    last_tier = models.CharField(max_length=100, default="50,10", help_text=help_text)
    wyse_cash_win_factor = models.JSONField(null=True, blank=True)
    s4l_win_factor_constant = models.CharField(max_length=100, default="100,100")
    nummber_of_duplicate_lottery_to_create = models.PositiveIntegerField(
        default=4, help_text="Number of duplicate lottery to create"
    )

    pos_agent_commission = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.05,
        help_text="This is the commission percentage for POS agent",
    )

    ussd_bank_payment_method = models.CharField(
        max_length=100, choices=USSD_BANK_PAYMENT_METHOD_CHOICES, default="WATU_PAY"
    )
    coralpay_biller_code = models.CharField(max_length=100, default="542")

    wyse_cash_running_balance = models.FloatField(default=0.00, editable=False)
    instant_cashout_running_balance = models.FloatField(default=0.00, editable=False)
    salary_4_life_running_balance = models.FloatField(default=0.00)  # Making field editable
    salary_4_life_current_total_winnings = models.FloatField(default=0.00, editable=False)
    s4l_telco_tickets_inclusion_ratio = models.CharField(
        max_length=10,
        default="3:4",
        help_text="Ratio for including telco tickets in SalaryForLife draws (e.g., '3:4' means include telco tickets in 3 out of 7 draws)",
    )

    pos_wyse_cash_running_balance = models.FloatField(default=0.00)
    pos_instant_cashout_running_balance = models.FloatField(default=0.00)
    pos_salary_4_life_running_balance = models.FloatField(default=0.00)
    salary_4_life_draw_ip = models.CharField(max_length=100, default="localhost")
    pos_base_pps = models.FloatField(default=0.00)
    pos_bonus_wysecash_max_amount = models.FloatField(default=1500.00)
    pos_bonus_wysecash_max_qty = models.FloatField(default=20)
    pos_bonus_cutoff_amount = models.FloatField(
        default=20,
        help_text="If an agent has reached this amount in sales, he may not receive more bonus",
    )
    pos_plays_since_last_icash = models.FloatField(
        default=5,
        help_text="how many plays since last i-cash pending win was made available for pos",
    )
    plays_since_last_icash = models.FloatField(
        default=5,
        help_text="how many plays since last i-cash pending win was made available web",
    )
    plays_before_icash = models.CharField(
        default="5,16",
        max_length=10,
        help_text="how many plays since last i-cash pending win was made available web",
    )

    merge_wysecash_draws = models.BooleanField(default=False)
    merge_icash_draws = models.BooleanField(default=False)
    merge_salary4life_draws = models.BooleanField(default=False)

    # Scoccer Cash Const Variable
    freemuim_play = models.BooleanField(default=False)
    soccer_freemium_fixtures_id = models.CharField(max_length=300, null=True, blank=True)
    freemium_winner_range = models.IntegerField(default=0)
    freemium_leagues_count = models.IntegerField(default=0)
    freemuim_play_count = models.IntegerField(default=1)

    base_pps = models.FloatField(default=0.00)

    merge_decisioning = models.BooleanField(
        default=False,
        help_text="This is to enable or disable the merge decisioning. If enabled, the merge decisioning for all lottery games will be merged",
    )
    create_pos_duplicate_lottery = models.BooleanField(
        default=False,
        help_text="This is to enable or disable the creation of duplicate lottery games for POS. If enabled, the duplicate lottery games for all lottery games will be created",
    )

    agent_bonus_amount = models.FloatField(default=0.00)

    instant_cashout_n = models.FloatField(default=0.00)
    bonus_amount = models.FloatField(default=0.00)
    updated_at = models.DateTimeField(auto_now=True)
    payout_source = models.CharField(max_length=100, choices=PAYOUT_SOURCE_CHOICES, default="NOT_AVAILABLE")
    paid_global_jackpot_count = models.IntegerField(default=11)
    jackpot_winning_channel = models.CharField(
        max_length=100, choices=JACKPOT_WINNING_CHANNEL_CHOICES, null=True, blank=True
    )
    jackpot_game_choice = models.CharField(max_length=100, choices=JACKPOT_GAME_CHOICES, default="INSTANT_CASHOUT")
    # link_shortener = models.CharField(
    #     max_lenhth=100, choices=LINK_SHORTENER_CHOICES, default="BITLY"
    # )
    instant_cashout_line_restriction = models.CharField(default="1,3,5", max_length=20)
    quika_line_restriction = models.CharField(default="1", max_length=20)
    quika_stake_amount_restriction = models.CharField(default="100", max_length=200)
    quika_winning_amount_restriction = models.CharField(default="1000", max_length=200)
    game_illusion_feature = models.CharField(max_length=100, default="OFF", choices=GAME_illusion_FEATURE_CHOICES)
    banker_running_balance = models.FloatField(default=0.00)
    limit_wining_amount = models.BooleanField(
        default=True,
        help_text="This value will flip flop, and allow large winnings one time "
        "when false and limit large winning when true on instantcash("
        "banker)",
    )
    general_sms_charges = models.FloatField(default=3)
    banker_draw_countdown_time = models.DateTimeField(blank=True, null=True)
    use_new_icash_price = models.BooleanField(default=False)
    winwise_agents_salary_amount = models.FloatField(
        default=0.00,
        help_text="This represents the monthly salary amount for Winwise Staff agents",
    )
    telco_commission = models.FloatField(
        default=0.00,
        help_text="this represents the percentage charge for all game played via telco channel. NOTE. any figure put in here will be divided by 100. e.g 90 / 100 = 0.9",
    )
    telco_rtp = models.FloatField(
        default=0.00,
        help_text="this represents the percentage charge for all game played via telco channel. NOTE. any figure put in here will be divided by 100. e.g 90 / 100 = 0.9",
    )
    aggregator_commission = models.FloatField(
        default=0.00,
        help_text="this represents the percentage charge for all game played via telco channel. NOTE. any figure put in here will be divided by 100. e.g 90 / 100 = 0.9",
    )
    last_lotto_ticket_count = models.FloatField(
        default=0.00,
        help_text="Number of instantcash tickets sold at last count",
    )
    telco_bonus_purse_value = models.FloatField(
        default=0.00,
        help_text="Total amount waiting to be given out to telco",
    )
    telco_bonus_give_threshold = models.IntegerField(
        default=0.00,
        help_text="Count before dropping each telco bonus",
    )
    i_cash_jackpot_winning_amount = models.FloatField(default=0.00)
    total_icash_jackpot_winning = models.FloatField(default=0.00)
    mancala_running_bal = models.FloatField(default=1000.00)

    new_icash_fifty_drought_bonus_draw_type = models.CharField(
        max_length=100,
        default="BONUS",
    )
    new_icash_fifty_drought_bonus_ticket_count = models.IntegerField(default=0)
    new_icash_fifty_drought_bonus_contributed_ticket_count = models.IntegerField(default=0)
    new_icash_fifty_iteration_reset_number = models.IntegerField(default=15)
    new_icash_fifty_iterated_count = models.IntegerField(default=0)

    new_icash_hundred_drought_bonus_draw_type = models.CharField(
        max_length=100,
        default="BONUS",
    )
    new_icash_hundred_drought_bonus_ticket_count = models.IntegerField(default=0)
    new_icash_hundred_drought_bonus_contributed_ticket_count = models.IntegerField(default=0)
    new_icash_hundred_iteration_reset_number = models.IntegerField(default=15)
    new_icash_hundred_iterated_count = models.IntegerField(default=0)

    general_drought_control_bonus_draw_type = models.CharField(
        max_length=100,
        default="BONUS",
    )
    general_drought_control_expected_contribution = models.FloatField(default=200)
    general_drought_control_contributed_value = models.FloatField(default=0)
    general_drought_control_count_value = models.FloatField(default=0)
    general_drought_control_iteration_reset_number = models.IntegerField(default=12)
    general_drought_control_iterated_count = models.IntegerField(default=0)
    users_exempted_from_limit = models.TextField(default="")
    drought_enabled = models.BooleanField(
        default=False, help_text="Enable bonus winnings after a number of plays if there are no winnings"
    )

    @classmethod
    def update_giver(cls, stake_amount, reset=False):
        const_obj: ConstantVariable = cls.objects.all().last()
        giver_dict = const_obj.new_quika_icash_count_to_giver

        if reset:
            giver_dict[stake_amount] = 0
        else:
            giver_dict[stake_amount] = giver_dict[stake_amount] + 1

        const_obj.new_quika_icash_count_to_giver = giver_dict
        const_obj.save()

        return True

    @classmethod
    def boost_banker_rtp(cls, amount):
        boost_data = {"boost": 0, "percent": 0}

        if amount <= 0:
            return boost_data

        x, y = cls.objects.all().last().banker_booster_percent.split(",")
        percent = (randint(int(x), int(y))) / 100
        boost = amount * percent

        if cls.objects.all().last().banker_booster_balance < boost:
            return {"boost": 0, "percent": 0}

        ConstantVariable.objects.all().update(banker_booster_balance=models.F("banker_booster_balance") - boost)

        return {"boost": boost, "percent": percent}

    @classmethod
    def top_up_banker_rtp(cls, amount):
        topup = 0

        if amount <= 0:
            return topup

        x, y, z = cls.objects.all().last().banker_booster_amount.split(",")

        topup = choice(range(int(x), int(y), int(z)))

        if cls.objects.all().last().banker_topup_balance < topup:
            return 0

        ConstantVariable.objects.all().update(banker_topup_balance=models.F("banker_topup_balance") - topup)

        return topup

    def clean(self):
        if (
            self.payout_by_stake_summary
            and self.payout_periodically
            and self.payout_by_number_of_players
            and self.payout_by_fixed_date
        ):
            raise ValidationError("Must select only one threshold per time")

        elif self.payout_by_stake_summary and self.payout_periodically:
            raise ValidationError("Must select only one threshold per time")

        elif self.payout_by_number_of_players and self.payout_by_fixed_date:
            raise ValidationError("Must select only one threshold per time")

        elif self.payout_by_stake_summary and self.payout_by_fixed_date:
            raise ValidationError("Must select only one threshold per time")

        elif self.payout_by_number_of_players and self.payout_periodically:
            raise ValidationError("Must select only one threshold per time")

        # Stake Summary
        elif (self.payout_by_stake_summary and not self.stake_amount_summary) and (
            not self.stake_amount_summary and self.payout_by_stake_summary
        ):
            raise ValidationError('Must select "payout_by_stake_summary" and "stake_amount_summary')

        # Number of Players
        elif (self.payout_by_number_of_players and not self.number_of_players) and (
            not self.number_of_players and self.payout_by_number_of_players
        ):
            raise ValidationError('Must select "payout_by_number_of_players" and "number_of_players')

        # Fixed Date
        elif (self.payout_by_fixed_date and not self.fixed_date) or (
            not self.payout_by_fixed_date and self.fixed_date
        ):
            raise ValidationError('Must select "fixed_date" and "payout_by_fixed_date')

        elif self.payout_by_stake_summary and self.payout_by_number_of_players:
            raise ValidationError('Must select either "payout_by_stake_summary" or "payout_by_number_of_players')

        # ALLLLLL
        elif (not self.payout_by_fixed_date and not self.payout_by_number_of_players) and (
            not self.payout_by_stake_summary and not self.payout_periodically
        ):
            raise ValidationError("Must select at least one payout decider")

        elif (
            self.payout_periodically
            and not self.start_date_periodically
            and not (self.monthly or self.weekly or self.daily and self.hourly)
        ):
            raise ValidationError('Must select one instance of "payout_periodically')

        elif self.payout_periodically and not self.start_date_periodically:
            raise ValidationError('Must select "start_date_periodically" of "payout_periodically')

        elif (
            (self.monthly and self.weekly)
            or (self.daily and self.hourly)
            or (self.monthly and self.daily)
            or (self.hourly and self.monthly)
            or (self.daily and self.weekly)
            or (self.monthly and self.hourly)
            or (self.weekly and self.hourly)
        ):
            raise ValidationError("Must select only one instance of periodic schedule")

        elif self.instant_cashout_win_range_number:
            values = self.instant_cashout_win_range_number.split("-")
            if len(values) < 2:
                raise ValidationError("Must enter a valid range of numbers. e.g 80-150")

        # elif self.quika_stake_amount_restriction:
        #     quika_lines = len(self.quika_line_restriction.split(","))
        #     if len(self.quika_stake_amount_restriction.split(",")) != quika_lines:
        #         raise ValidationError(
        #             "Quika Line, Stake and Winning amount must be same length"
        #         )
        #
        #
        # # if quika_lines == len(
        # #     self.quika_stake_amount_restriction.split(",")
        # # ) and quika_lines == len(self.quika_winning_amount_restriction):
        # #     print(
        # #         len(self.quika_line_restriction.split(",")),
        # #         len(self.quika_stake_amount_restriction.split(",")),
        # #         len(self.quika_winning_amount_restriction.split(",")),
        # #     )
        # #     print("\n\n\n\n================================")
        # #     pass
        # #
        # # else:
        # #     raise ValidationError(
        # #         "Quika Line, Stake and Winning amount must be same length"
        # #     )

    @classmethod
    def remit_marketting_bonus(cls):
        import random
        from datetime import datetime

        const_obj = cls.objects.all().first()
        data = const_obj.icash_marketting_bonus_dict_feed

        # Get the current date
        current_date = datetime.now()

        # Get the ISO calendar week number of the month
        iso_week_number = current_date.isocalendar()[1]

        def week_of_month(date):
            first_day = date.replace(day=1)
            dom = date.day
            adjusted_dom = dom + first_day.weekday()
            return int((adjusted_dom - 1) / 7) + 1

        print(f"Week of the month: {week_of_month(current_date)}")
        iso_week_number = week_of_month(current_date)

        # Get the current date
        vals = [0, 0, 1, 0, 0, 1, 1, 0, 0, 1, 0]
        give_or_not = random.choice(vals)

        # Get the numeric day of the week (0 for Monday, 1 for Tuesday, ..., 6 for Sunday)
        numeric_day_of_week = current_date.weekday()

        print(f"Numeric day of the week:{numeric_day_of_week} Numeric week of the month:{iso_week_number}")

        def distribute_bonuses(week_data, hourly_probability):
            result = {}

            for week, day_bonuses in week_data.items():
                week = str(week)
                result[week] = []

                for day in day_bonuses:
                    if int(numeric_day_of_week) == int(day[0]) and int(week) == int(iso_week_number) and give_or_not:
                        bonus = day
                        return bonus

            return [0, 0, "No Bonus", 0]

        # Example usage with hourly probability set to 0.5 (50% chance of giving bonus hourly)
        # data = {"1": [[0, 2300], [1, 30000], [2, 3400], [3, 2600], [4, 2200], [5, 2400], [6, 2400], [0, 2900], [1, 2400], [2, 3400], [3, 2700], [4, 3700], [5, 2100], [6, 2000], [0, 2300], [1, 3300], [2, 2500], [3, 3100], [4, 3100], [5, 3600], [6, 2600], [0, 2500], [1, 3400], [2, 30000], [3, 3400], [4, 2600], [5, 3200], [6, 2000]], "2": [[0, 30000], [1, 2700], [2, 2100], [3, 3100], [4, 3900], [5, 2400], [6, 2700], [0, 3700], [1, 2500], [2, 3200], [3, 2100], [4, 2800], [5, 3500], [6, 2200], [0, 2300], [1, 3000], [2, 3500], [3, 2300], [4, 3400], [5, 2100], [6, 3600], [0, 2400], [1, 2400], [2, 30000], [3, 3100], [4, 2100], [5, 30000], [6, 2900]], "3": [[0, 50000], [1, 3600], [2, 3800], [3, 2200], [4, 2600], [5, 50000], [6, 2000], [0, 2100], [1, 3300], [2, 3900], [3, 2100], [4, 3900], [5, 3700], [6, 2500], [0, 50000], [1, 2000], [2, 3400], [3, 2900], [4, 2400], [5, 2600], [6, 2300], [0, 3500], [1, 3000], [2, 3000], [3, 3800], [4, 2800], [5, 2300], [6, 3900]], "4": [[0, 3200], [1, 3500], [2, 3500], [3, 2300], [4, 2500], [5, 3300], [6, 3800], [0, 2200], [1, 2900], [2, 3900], [3, 2800], [4, 3900], [5, 3500], [6, 100000], [0, 2900], [1, 2300], [2, 3800], [3, 2900], [4, 3800], [5, 2200], [6, 2800], [0, 2500], [1, 2900], [2, 3700], [3, 2900], [4, 3200], [5, 3300], [6, 3500]]}

        hourly_probability = 0.5
        result_data = distribute_bonuses(data, hourly_probability)
        print(f"RESULT DATA::{result_data}")

        try:
            _, amount = result_data
            data[str(iso_week_number)].remove(result_data)
            cls.objects.all().update(icash_marketting_bonus_dict_feed=data)
            # InstantCashoutPendingWinning.create_pending_winning(
            #     f"MARKETTING-BONUS-{datetime.now()}",
            #     amount,
            #     "mid_win",
            #     999,
            #     is_avialable=True,
            #     is_available_for_pos=False,
            #     is_for_pos_only=True,
            #     is_for_telco_only=False,
            #     bonus_target_agent=None,
            #     count_before=0,
            #     flavour="WHITE",
            # )
            InstantCashoutPendingWinning.create_pending_winning(
                f"MARKETTING-BONUS-{datetime.now()}",
                amount,
                "mid_win",
                999,
                is_avialable=False,
                is_available_for_pos=False,
                is_for_pos_only=False,
                is_for_telco_only=False,
                bonus_target_agent=None,
                count_before=0,
                flavour="WHITE",
            )
        except Exception as e:
            print(e)

        return result_data

    @classmethod
    def generate_bonus_schedule_dict(cls):
        import pprint
        import random

        from overide_print import print

        constant_obj = cls.objects.all().first()
        icash_marketting_params = constant_obj.icash_marketting_params

        weekly_allocations = icash_marketting_params.get("weekly_allocations")
        bulk_bonuses = icash_marketting_params.get("bulk_bonuses")

        total_amount = icash_marketting_params.get("total_weekly_allocations")
        {
            "weekly_allocations": {
                1: 100000,
                2: 100000,
                3: 70000,  # Note: Corrected weekly amount for week 3
                4: 100000,
            },
            "bulk_bonuses": {
                1: [30000, 30000, 30000],
                2: [30000, 30000, 30000],
                3: [50000, 50000, 50000],
                4: [100000],
            },
        }
        days_of_week = [0, 1, 2, 3, 4, 5, 6]

        minor_bonuses = {}
        for week, total_amount in weekly_allocations.items():
            amounts = []
            while total_amount > 0:
                amount = random.randint(2000, 4000)
                amount = int(amount / 100) * 100
                amounts.append(amount)
                total_amount -= amount
            minor_bonuses[week] = amounts

        print(minor_bonuses)

        def seed_bulk_bonuses(minor_bonuses, bulk_bonuses):
            for key, value in minor_bonuses.items():
                new_list: list = value + bulk_bonuses[key]
                random.shuffle(new_list)

                minor_bonuses[key] = new_list

            return minor_bonuses

        # Seed the values from bulk_bonuses into minor_bonuses
        resulting_minor_bonuses = seed_bulk_bonuses(minor_bonuses, bulk_bonuses)

        for key, value in resulting_minor_bonuses.items():
            days = days_of_week * 4
            len_bonuses = len(value)

            adjusted_days = days[: len_bonuses + 1]
            merged_bonuses = list(zip(adjusted_days, value))

            resulting_minor_bonuses[key] = merged_bonuses

        pprint.pprint(resulting_minor_bonuses)

        cls.objects.all().update(
            icash_marketting_bonus_dict_feed=resulting_minor_bonuses,
            icash_marketting_bonus_dict_reference=resulting_minor_bonuses,
        )

        return resulting_minor_bonuses

    def save(self, *args, **kwargs):
        pass

        if not self.payout_periodically:
            self.monthly = False
            self.weekly = False
            self.daily = False
            self.hourly = False
            self.start_date_periodically = None

        if self.pk:
            pass

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "rtp":
                    field.value_from_object(old)

                elif field.name == "global_jackpot_perc":
                    field.value_from_object(old)

                elif field.name == "salary_4_life_global_jackpot_perc":
                    field.value_from_object(old)

                elif field.name == "game_illusion_feature":
                    field.value_from_object(old)

                elif field.name == "telco_commission":
                    field.value_from_object(old)

                elif field.name == "aggregator_commission":
                    field.value_from_object(old)

                # print(
                #     f"""
                # RTP: {self.rtp} - {__original_rtp}
                # \n\n
                # """
                # )
                # if self.rtp != __original_rtp:
                #     redis_db = RedisStorage(redis_key="ticket_rtp")
                #     redis_db.set_data(self.rtp / 100)

                # if self.global_jackpot_perc != __original_global_jackpot_perc:
                #     redis_db = RedisStorage(redis_key="global_jackpot_perc")
                #     redis_db.set_data(self.global_jackpot_perc / 100)

                # if self.salary_4_life_global_jackpot_perc != __original_salary_4_life_global_jackpot_perc:
                #     redis_db = RedisStorage(redis_key="sla4life_global_jackpot_perc")
                #     redis_db.set_data(self.salary_4_life_global_jackpot_perc / 100)

                # if self.game_illusion_feature != __origina_illusion_level:
                #     redis_db = RedisStorage(redis_key="illusion_r_level")
                #     redis_db.set_data(self.game_illusion_feature)
                # if self.telco_commission != __origin_telco_commission:
                #     redis_db = RedisStorage(redis_key="telco_percentage_charge_")
                #     redis_db.set_data(self.telco_commission / 100)

                # if self.aggregator_commission != __origin_aggregator_commission:
                #     redis_db = RedisStorage(redis_key="telco_aggregator_charge_")
                #     redis_db.set_data(self.aggregator_commission / 100)

        # FootballTable.toggle_freemium(self.soccer_freemium_fixtures_id)

        "================================================================================"
        "======================CLEAR AGENT ACCUM PLAYS ON MODE SWITCH===================="
        "================================================================================"

        if self.icash2_draw_mode == "LOCAL" and self.agent_play_counts_cleared is False:
            Agent.objects.update(icash_sold_dict=dict(), icash_sold=0)
            self.agent_play_counts_cleared = True

        if self.icash2_draw_mode == "GLOBAL" and self.agent_play_counts_cleared is True:
            self.agent_play_counts_cleared = False

        "================================================================================"
        "================================================================================"

        if not self.icash_flavour_dict:
            self.icash_flavour_dict = {
                "150": self.sharing,
                "200": self.sharing,
                "300": self.sharing,
                "500": self.sharing,
                "450": self.sharing,
                "600": self.sharing,
                "750": self.sharing,
                "900": self.sharing,
                "1000": self.sharing,
                "1250": self.sharing,
                "1300": self.sharing,
                "1500": self.sharing,
            }
        return super(ConstantVariable, self).save(*args, **kwargs)

    def build_flavour_list(self):
        sharing = self.sharing
        # random.shuffle(self.sharing)
        return sharing

    @staticmethod
    def get_constant_variable():
        try:
            _constant = ConstantVariable.objects.all().last()

            if not _constant:
                data = {
                    "game_threshold": 100,
                    "winning_percentage": 100,
                    "rto": 100,
                    "rtp": 100,
                    "plays": 100,
                    "game_bands": 100,
                    "collect_bvn": False,
                    "referral_reward_percentage": 0.00,
                    "instant_cashout_win_range_number": "80-150",
                    "pos_agent_commission": 0.05,
                    "ussd_bank_payment_method": "WATU_PAY",
                    "coralpay_biller_code": "542",
                    "freemuim_play": False,
                    "soccer_freemium_fixtures_id": "",
                    "freemium_winner_range": 0,
                    "freemium_leagues_count": 0,
                    "freemuim_play_count": 1,
                    "create_pos_duplicate_lottery": False,
                    "nummber_of_duplicate_lottery_to_create": 1,
                    "agent_bonus_amount": 0.00,
                    "instant_cashout_n": 0.00,
                    "bonus_amount": 0.00,
                    "payout_source": "NOT_AVAILABLE",
                    "users_exempted_from_limit": "",
                }

                return data

            data = {
                "game_threshold": _constant.game_threshold,
                "winning_percentage": _constant.winning_percentage,
                "rto": _constant.rto,
                "rtp": _constant.rtp,
                "plays": _constant.plays,
                "game_bands": _constant.game_bands,
                "collect_bvn": _constant.collect_bvn,
                "referral_reward_percentage": _constant.referral_reward_percentage,
                "instant_cashout_win_range_number": _constant.instant_cashout_win_range_number,
                "pos_agent_commission": _constant.pos_agent_commission,
                "ussd_bank_payment_method": _constant.ussd_bank_payment_method,
                "coralpay_biller_code": _constant.coralpay_biller_code,
                "freemuim_play": _constant.freemuim_play,
                "soccer_freemium_fixtures_id": _constant.soccer_freemium_fixtures_id,
                "freemium_winner_range": _constant.freemium_winner_range,
                "freemium_leagues_count": _constant.freemium_leagues_count,
                "freemuim_play_count": _constant.freemuim_play_count,
                "create_pos_duplicate_lottery": _constant.create_pos_duplicate_lottery,
                "nummber_of_duplicate_lottery_to_create": _constant.nummber_of_duplicate_lottery_to_create,
                "agent_bonus_amount": _constant.agent_bonus_amount,
                "instant_cashout_n": _constant.instant_cashout_n,
                "bonus_amount": _constant.bonus_amount,
                "payout_source": _constant.payout_source,
                "users_exempted_from_limit": _constant.users_exempted_from_limit,
            }

            return data

        except Exception:
            data = {
                "game_threshold": 100,
                "winning_percentage": 100,
                "rto": 100,
                "rtp": 100,
                "plays": 100,
                "game_bands": 100,
                "collect_bvn": False,
                "referral_reward_percentage": 0.00,
                "instant_cashout_win_range_number": "80-150",
                "pos_agent_commission": 0.05,
                "ussd_bank_payment_method": "WATU_PAY",
                "coralpay_biller_code": "542",
                "freemuim_play": False,
                "soccer_freemium_fixtures_id": "",
                "freemium_winner_range": 0,
                "freemium_leagues_count": 0,
                "freemuim_play_count": 1,
                "create_pos_duplicate_lottery": False,
                "nummber_of_duplicate_lottery_to_create": 1,
                "agent_bonus_amount": 0.00,
                "instant_cashout_n": 0.00,
                "bonus_amount": 0.00,
                "payout_source": "NOT_AVAILABLE",
                "users_exempted_from_limit": "",
            }

            return data

    @staticmethod
    def default_batch_management_engine():
        from main.tasks import send_task_to_lotto_decisioning

        constant_instance = ConstantVariable.objects.last()
        get_batches = LotteryBatch.objects.filter(is_active=True)

        if constant_instance.payout_by_stake_summary:
            amount_to_stop_batch = constant_instance.stake_amount_summary
            if get_batches:
                for batch in get_batches:
                    total_amount = list(
                        batch.lottery_players.filter(paid=True).aggregate(Sum("stake_amount")).values()
                    )[0]

                    if total_amount >= amount_to_stop_batch:
                        batch.is_active = False
                        batch.save()
                        # send_out_batch = send_task_to_lotto_decisioning(batch_instance=batch)

                        # Create New Batch

                    else:
                        pass

        elif constant_instance.payout_periodically:
            start_date_time = constant_instance.start_date_periodically.replace(tzinfo=utc)

            for batch in get_batches:
                if constant_instance.monthly:
                    new_date = start_date_time + relativedelta(months=1)

                    if datetime.now().replace(tzinfo=utc) >= start_date_time:
                        batch.is_active = False
                        batch.save()
                        # send_out_batch = send_task_to_lotto_decisioning(batch_instance=batch)

                        # Create New Batch

                        constant_instance.start_date_time = datetime.now()
                        constant_instance.save()

                elif constant_instance.weekly:
                    new_date = start_date_time + relativedelta(weeks=1)

                    if datetime.now().replace(tzinfo=utc) >= start_date_time:
                        print(new_date)
                        batch.is_active = False
                        batch.save()
                        # send_out_batch = send_task_to_lotto_decisioning(batch_instance=batch)

                        # Create New Batch

                        constant_instance.start_date_time = datetime.now()
                        constant_instance.save()

                elif constant_instance.daily:
                    new_date = start_date_time + relativedelta(days=1)

                    if datetime.now().replace(tzinfo=utc) >= start_date_time:
                        batch.is_active = False
                        batch.save()
                        # send_out_batch = send_task_to_lotto_decisioning(batch_instance=batch)

                        # Create New Batch

                        constant_instance.start_date_time = datetime.now()
                        constant_instance.save()

                else:
                    new_date = start_date_time + relativedelta(hours=1)
                    print(new_date)
                    if datetime.now().replace(tzinfo=utc) >= start_date_time:
                        batch.is_active = False
                        batch.save()
                        # send_out_batch = send_task_to_lotto_decisioning(batch_instance=batch)

                        # Create New Batch

                        constant_instance.start_date_time = datetime.now()
                        constant_instance.save()

        elif constant_instance.payout_by_number_of_players:
            number_of_players_before_stop = constant_instance.number_of_players

            for batch in get_batches:
                get_total_number_of_players = batch.lottery_players.filter(paid=True).count()
                if get_total_number_of_players >= number_of_players_before_stop:
                    batch.is_active = False
                    batch.save()
                    send_task_to_lotto_decisioning(batch_instance=batch)

                    # Create New Batch

        elif constant_instance.payout_by_fixed_date and constant_instance.fixed_date:
            start_date_time = constant_instance.fixed_date.replace(tzinfo=utc)
            for batch in get_batches:
                if datetime.now().replace(tzinfo=utc) >= start_date_time:
                    batch.is_active = False
                    batch.save()
                    send_task_to_lotto_decisioning(batch_instance=batch)

                    # Create New Batch

                    constant_instance.fixed_date = None
                    constant_instance.save()

    @classmethod
    def wysecash_percentage_range(cls):
        const = cls.objects.last()
        if const is None:
            const = cls.objects.create(game_threshold=1)
        split_element = lambda element: element.split(",")  # noqa

        last_tier = split_element(const.last_tier)
        lower_tier = split_element(const.lower_tier)
        middle_tier = split_element(const.middle_tier)
        upper_tier = split_element(const.upper_tier)

        range_difference = {
            10: {"min": int(last_tier[0]), "step": int(last_tier[1])},
            50: {"min": int(lower_tier[0]), "step": int(lower_tier[1])},
            100: {"min": int(middle_tier[0]), "step": int(middle_tier[1])},
            200: {"min": int(upper_tier[0]), "step": int(upper_tier[1])},
        }

        # print(range_difference)
        return range_difference

    @classmethod
    def rto_rtp(cls):
        const = cls.objects.last()
        value = {"rtp": const.rtp / 100, "rto": const.rto / 100}
        # store the value in redis db
        redis_db = RedisStorage(redis_key="ticket_rtp")
        redis_db.set_data(value.get("rtp"))
        return value

    @classmethod
    def telco_rtp_perc(cls):
        # redis_db = RedisStorage(redis_key="telco_rtp_perc_")
        # _rtp = redis_db.get_data()
        # if _rtp is None:
        #     const = cls.objects.last()
        #     value = {"rtp": const.telco_rtp / 100}
        #     # store the value in redis db
        #     redis_db.set_data(value.get("rtp"))
        #     return value
        # else:
        #     return {"rtp": _rtp.decode("utf-8")}

        const = cls.objects.last()
        return {"rtp": const.telco_rtp / 100}

    @classmethod
    def get_telco_commission(cls):
        # redis_db = RedisStorage(redis_key="telco_percentage_charge_")
        # _rtp = redis_db.get_data()
        # if _rtp is None:
        #     const = cls.objects.last()
        #     value = const.telco_commission / 100
        #     # store the value in redis db
        #     redis_db.set_data(value)
        #     return value
        # else:
        #     return float(_rtp)

        const = cls.objects.last()
        value = const.telco_commission / 100

        return value

    @classmethod
    def get_aggregator_commission(cls):
        # redis_db = RedisStorage(redis_key="telco_aggregator_charge_")
        # _rtp = redis_db.get_data()
        # if _rtp is None:
        #     const = cls.objects.last()
        #     value = const.aggregator_commission / 100
        #     # store the value in redis db
        #     redis_db.set_data(value)
        #     return value
        # else:
        #     return float(_rtp.decode("utf-8"))

        const = cls.objects.last()
        return const.aggregator_commission / 100

    @classmethod
    def global_jackpot_percentage(cls):
        const = cls.objects.last()
        const = const.global_jackpot_perc / 100

        # store the value in redis db
        redis_db = RedisStorage(redis_key="global_jackpot_perc")
        redis_db.set_data(const)

        return const

    @classmethod
    def salary_for_life_global_jackpot_percentage(cls):
        const = cls.objects.last()
        const = const.salary_4_life_global_jackpot_perc / 100

        # store the value in redis db
        redis_db = RedisStorage(redis_key="sla4life_global_jackpot_perc")
        redis_db.set_data(const)
        return const

    @classmethod
    def get_global_jackpot_player_paid_range(cls) -> int:
        paid_ticket_count = cls.objects.last().paid_global_jackpot_count
        return paid_ticket_count

    @classmethod
    def jackpot_available_channel(cls):
        return cls.objects.last().jackpot_winning_channel

    @classmethod
    def get_jackpot_constants(cls) -> dict:
        jackpot_const = cls.objects.last()
        const = {
            "channel": jackpot_const.jackpot_winning_channel,
            "lotto_type": jackpot_const.jackpot_game_choice,
            "paid_ticket_count": jackpot_const.paid_global_jackpot_count,
        }
        return const

    @classmethod
    def quika_line_restriction_and_amount(cls):
        """
        This method represent the data structure
         returned to the mobile developers
        """
        agent_constant = cls.objects.last()
        # lines = [int(line) for line in agent_constant.quika_line_restriction.split(",")]
        lines = list(
            map(
                int,
                agent_constant.quika_line_restriction.split(","),
            )
        )

        stake_amount = list(
            map(
                int,
                agent_constant.quika_stake_amount_restriction.split(","),
            )
        )

        winning_amount = list(
            map(
                int,
                agent_constant.quika_winning_amount_restriction.split(","),
            )
        )
        # print(lines, stake_amount, winning_amount)
        restriction_values = []
        count = 0
        for line in lines:
            result = {
                "line_number": line,
                "stake_amount": stake_amount[count],
                "winning_amount": winning_amount[count],
            }

            restriction_values.append(result)
            count += 1

        return restriction_values

    @classmethod
    def quika_admin_stake_and_winning_amount(cls):
        const = cls.objects.last()
        lines = const.quika_line_restriction.split(",")
        stake_amount = const.quika_stake_amount_restriction.split(",")
        total_winning = const.quika_winning_amount_restriction.split(",")

        return Quika(lines, stake_amount, total_winning).prices()

    @classmethod
    def instant_cash_out_line_restriction(cls):
        num_lines = cls.objects.last().instant_cashout_line_restriction.split(",")
        return [int(line) for line in num_lines]

    @classmethod
    def is_salary_for_life_draw_merge(cls):
        const = cls.objects.last()
        return const.merge_pos_draw_s4l

    @classmethod
    def get_game_illusion_level(cls):
        redis_db = RedisStorage(redis_key="illusion_r_level")
        value = redis_db.get_data()

        if value is None:
            const = cls.objects.last()

            redis_db.set_data(const.game_illusion_feature)

            return const.game_illusion_feature
        else:
            return value.decode("utf-8")

    @classmethod
    def deplete_mancala_running_bal(cls, play_amount):
        """
        deplete_mancala_play_amount(cls, play_amount)

        Description:
            Class method to deplete the Mancala play amount from the last instance of the class.

        Args:
            play_amount (float): The amount to be subtracted from the Mancala target amount.

        Returns:
            None

        Example:
            # >>> ConstantVariable.deplete_mancala_running_bal(10.5)
            # Assuming the Mancala target amount was 100, after this method call, it becomes 89.5
        """
        _const = cls.objects.last()
        _const.mancala_running_bal -= float(play_amount)
        _const.save()

    @classmethod
    def get_mancala_running_bal(cls) -> float:
        """
        get_mancala_target_balance(cls)

        Description:
            Class method to retrieve the Mancala target balance from the last instance of the class.

        Returns:
            float: The Mancala target amount.

        Example:
            # >>> target_balance = ConstantVariable.get_mancala_running_bal()
            # >>> print(target_balance)
            100  # Assuming 100 is the Mancala target amount
        """
        _const = cls.objects.last()
        return _const.mancala_running_bal

    @classmethod
    def get_new_icash_fifty_drought_bonus_draw_type(cls):
        const = cls.objects.last()
        return const.new_icash_fifty_drought_bonus_draw_type

    @classmethod
    def get_new_icash_fifty_drought_bonus_ticket_count(cls):
        const = cls.objects.last()
        return const.new_icash_fifty_drought_bonus_ticket_count

    @classmethod
    def get_new_icash_fifty_drought_bonus_contributed_ticket_count(cls):
        const = cls.objects.last()
        return const.new_icash_fifty_drought_bonus_contributed_ticket_count

    @classmethod
    def get_new_icash_fifty_iteration_reset_number(cls):
        const = cls.objects.last()
        return const.new_icash_fifty_iteration_reset_number

    @classmethod
    def get_new_icash_fifty_iterated_count(cls):
        const = cls.objects.last()
        return const.new_icash_fifty_iterated_count

    @classmethod
    def get_new_icash_hundred_drought_bonus_draw_type(cls):
        const = cls.objects.last()
        return const.new_icash_hundred_drought_bonus_draw_type

    @classmethod
    def get_new_icash_hundred_drought_bonus_ticket_count(cls):
        const = cls.objects.last()
        return const.new_icash_hundred_drought_bonus_ticket_count

    @classmethod
    def get_new_icash_hundred_drought_bonus_contributed_ticket_count(cls):
        const = cls.objects.last()
        return const.new_icash_hundred_drought_bonus_contributed_ticket_count

    @classmethod
    def get_new_icash_hundred_iteration_reset_number(cls):
        const = cls.objects.last()
        return const.new_icash_hundred_iteration_reset_number

    @classmethod
    def get_new_icash_hundred_iterated_count(cls):
        const = cls.objects.last()
        return const.new_icash_hundred_iterated_count

    @classmethod
    def get_general_drought_control_bonus_draw_type(cls):
        const = cls.objects.last()
        return const.general_drought_control_bonus_draw_type

    @classmethod
    def get_general_drought_control_expected_contribution(cls):
        const = cls.objects.last()
        return const.general_drought_control_expected_contribution

    @classmethod
    def get_general_drought_control_contributed_value(cls):
        const = cls.objects.last()
        return const.general_drought_control_contributed_value

    @classmethod
    def get_general_drought_control_iteration_reset_number(cls):
        const = cls.objects.last()
        return const.general_drought_control_iteration_reset_number

    @classmethod
    def get_general_drought_control_iterated_count(cls):
        const = cls.objects.last()
        return const.general_drought_control_iterated_count

    class Meta:
        verbose_name = "CONSTANTS TABLE"
        verbose_name_plural = "CONSTANTS TABLES"

    @classmethod
    def release_draft(cls, amount):
        const_obj = cls.objects.first()
        print(f"||||||||||||||||||||||||||||||{const_obj.general_drought_control_contributed_value}")

        const_obj.general_drought_control_contributed_value = F("general_drought_control_contributed_value") + amount
        const_obj.general_drought_control_count_value = F("general_drought_control_count_value") + 1
        const_obj.save()

        const_obj.refresh_from_db()

        drought_count = int(const_obj.general_drought_control_count_value)

        total_contributed_amount = const_obj.general_drought_control_contributed_value
        print(f"||||||||||||||||||||||||||||||{const_obj.general_drought_control_contributed_value}")
        print(f"||||||||||||||||||||||||||||||{const_obj.general_drought_control_contributed_value}")
        print(f"||||||||||||||||||||||||||||||{const_obj.general_drought_control_contributed_value}")
        print(f"||||||||||||||||||||||||||||||{const_obj.general_drought_control_contributed_value}")
        print(f"||||||||||||||||||||||||||||||{const_obj.general_drought_control_contributed_value}")
        print(f"|||||||||||||||DROUGHT|||||||||||||||{drought_count}")
        print(f"||||||||||||||||||||||||||||||{drought_count}")
        print(f"|||||||||||total_contributed_amount|||||||||||||{total_contributed_amount}")
        print(f"||||||||||||||||||||||||||||||{drought_count}")
        print(f"||||||||||||||||||||||||||||||{drought_count}")

        if drought_count > 8 and drought_count > 10:
            cls.objects.update(general_drought_control_contributed_value=0)
            cls.objects.update(general_drought_control_count_value=0)
            return [True, 0]

        elif drought_count == 8:
            return [True, 200]

        elif drought_count > 4 and drought_count < 8:
            return [False, 0]

        elif drought_count == 4:
            return [True, 100]

        elif drought_count == 3:
            return [False, 0]  # Changed to False to reduce rtp being paid out

        elif drought_count == 2:
            return [False, 0]  # Changed to 0 to reduce rtp being paid out

        elif drought_count == 1:
            return [False, 0]

        return [True, 0]


class Excesses(models.Model):
    phone_number = models.CharField(max_length=150, null=True, blank=True)
    last_played_unique_id = models.CharField(max_length=150, null=True, blank=True)
    in_excess_amount = models.FloatField(null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "EXCESSES TABLE"
        verbose_name_plural = "EXCESSES TABLES"


class PayoutTransactionTable(models.Model):
    WOVEN = "WOVEN"

    TRANSACTION_CHOICES = [
        (WOVEN, "WOVEN"),
        ("LIBERTY_PAY", "LIBERTY_PAY"),
        ("REMITTANCE", "REMITTANCE"),
        ("VFD", "VFD"),
        ("BUDDY", "BUDDY"),
    ]

    STATUS_CHOICES = [
        ("PENDING", "PENDING"),
        ("SUCCESS", "SUCCESS"),
        ("FAILED", "FAILED"),
    ]

    CHANNEL = [
        ("POS", "POS"),
        ("WEB", "WEB"),
        ("USSD", "USSD"),
        ("USSD_WEB", "USSD_WEB"),
        ("REQUEST", "REQUEST"),
    ]

    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
    )

    TYPE_OF_TRANSACTION = [
        ("PAYOUT", "PAYOUT"),
        ("COMMISSION", "COMMISSION"),
    ]

    SOURCE_WALLET = [
        ("AGENT_FUNDING_WALLET", "AGENT_FUNDING_WALLET"),
        ("NON_RETAIL_WALLET", "NON_RETAIL_WALLET"),
        ("RETAIL_RTP_WALLET", "RETAIL_RTP_WALLET"),
        ("GHANA_RTP_WALLET", "GHANA_RTP_WALLET"),
        ("RETAIL_COMMISSION_WALLET", "RETAIL_COMMISSION_WALLET"),
    ]

    recipient_wallet = [
        ("RETAIL_RTP_WALLET", "RETAIL_RTP_WALLET"),
        ("GHANA_RTP_WALLET", "GHANA_RTP_WALLET"),
        ("AGENT_COMMISSION_WALLET", "AGENT_COMMISSION_WALLET"),
        ("USER_WALLET", "USER_WALLET"),
        ("RTO_WALLET", "RTO_WALLET"),
        ("RETAIL_COMMISSION_WALLET", "RETAIL_COMMISSION_WALLET"),
    ]

    source = models.CharField(max_length=300, choices=TRANSACTION_CHOICES, default=WOVEN)
    type_of_transaction = models.CharField(max_length=300, choices=TYPE_OF_TRANSACTION, default="PAYOUT")
    amount = models.FloatField()
    balance_before = models.FloatField(default=0.0)
    balance_after = models.FloatField(default=0.0)
    joined_since = models.CharField(max_length=300, null=True, blank=True)
    phone = models.CharField(max_length=150)
    payout_trans_ref = models.CharField(max_length=300)
    narration = models.CharField(max_length=300, default="Payout")
    disbursed = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    is_agent_commission = models.BooleanField(default=False)
    is_super_agent_commission = models.BooleanField(default=False)
    is_vertical_lead_commission = models.BooleanField(default=False)
    is_supervisor_commission = models.BooleanField(default=False)
    is_airtime_purchase = models.BooleanField(default=False)
    payment_initiated = models.BooleanField(default=True)
    bank_code = models.CharField(max_length=100, null=True, blank=True)
    disbursement_unique_id = models.CharField(max_length=70, unique=True)
    source_unique_ref = models.CharField(max_length=300, null=True, blank=True)
    name = models.CharField(max_length=300, null=True, blank=True)
    payout_verified = models.BooleanField(default=False)
    batch = models.ForeignKey(LotteryBatch, on_delete=models.CASCADE, null=True, blank=True)
    payout_payload = models.TextField(null=True, blank=True)
    source_response_payload = models.TextField(null=True, blank=True)
    verification_response_payload = models.TextField(null=True, blank=True)
    disbursement = models.ForeignKey(DisbursementTable, on_delete=models.CASCADE, null=True, blank=True)
    user_withdrawal = models.BooleanField(default=False)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    status = models.CharField(max_length=300, choices=STATUS_CHOICES, default="PENDING")
    channel = models.CharField(max_length=300, choices=CHANNEL, default="USSD")
    game_play_id = models.CharField(max_length=300, null=True, blank=True)
    unique_game_play_id = models.CharField(max_length=300, null=True, blank=True, unique=True)
    date_won = models.DateTimeField(null=True, blank=True)
    telco_network = models.CharField(max_length=100, null=True, blank=True, choices=NETWORK_PROVIDER)
    source_wallet = models.CharField(max_length=300, choices=SOURCE_WALLET, default="AGENT_FUNDING_WALLET")
    recipient_wallet = models.CharField(max_length=300, choices=recipient_wallet, default="USER_WALLET")
    is_late_withdrawal = models.BooleanField(default=False)

    def __str__(self):
        return self.payout_trans_ref

    # def save(self, *args, **kwargs):
    #     if self.game_play_id is None or self.game_play_id == "":
    #         pass
    #     else:
    #         self.unique_game_play_id = self.game_play_id

    #     return super(PayoutTransactionTable, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "PAYOUT TRANSACTION TABLE"
        verbose_name_plural = "PAYOUT TRANSACTION TABLES"
        indexes = [
            models.Index(
                fields=[
                    "phone",
                ]
            ),
        ]

    def save(self, *args, **kwargs):
        # self.disbursement_unique_id = f"{self.disbursement.id}-{self.disbursement.player_phone_num}"
        one_hour_ago = datetime.now() - timedelta(hours=1)
        payout_transactions = PayoutTransactionTable.objects.filter(
            phone=self.phone, amount=self.amount, date_added__gte=one_hour_ago
        )
        if (
            payout_transactions.count() >= 3
            and self.amount > 2000
            and (self.phone not in ConstantVariable.get_constant_variable().get("users_exempted_from_limit"))
        ):
            raise Exception(
                f"TOO MANY SIMILAR AMOUNTS TRANSACTIONS IN LESS THAN 1HR FOR USER {self.phone},AMOUNT:: N{self.amount}"
            )

        super(PayoutTransactionTable, self).save(*args, **kwargs)

    @classmethod
    def verify_or_generate_payout_ref(cls, ref=None):
        if not ref:
            obj = cls.objects.filter(disbursement_unique_id=ref)
            if obj.exists():
                new_ref = "withdraw-{}".format(uuid.uuid4())
                return {"already_exists": True, "new_ref": new_ref}
            else:
                return {"already_exists": False, "new_ref": ref}
        else:
            new_ref = "withdraw-{}".format(uuid.uuid4())
            return {"already_exists": True, "new_ref": new_ref}

    @classmethod
    def agent_woven_disbursement(
        cls,
        amount,
        phone,
        account_number,
        bank_code,
        agent_wallet_instance,
        _agent_instance,
        narration,
        pos_lottery_winner_instance,
        game_id,
    ):
        from rest_framework.response import Response

        from wallet_app.models import (
            DebitCreditRecord,
            GeneralWithdrawableWallet,
            UserWallet,
        )

        # VERIFY BANK DETAILS
        paystack = PaymentGateway()
        verify_bank_response = paystack.fetch_account_name(account_number=account_number, bank_code=bank_code)

        if isinstance(verify_bank_response, dict):
            if verify_bank_response.get("status") is False:
                data = {"message": "Invalid bank details"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            name = verify_bank_response.get("data").get("account_name")
            if pos_lottery_winner_instance.payout_ref is None or pos_lottery_winner_instance.payout_ref == "":
                pos_lottery_winner_instance.payout_ref = PosLotteryWinners().generate_transaction_ref()
                pos_lottery_winner_instance.save()
                sleep(1)

            # verify if payout reference is unique
            verify_ref_res = cls.verify_or_generate_payout_ref(pos_lottery_winner_instance.payout_ref)
            if verify_ref_res.get("already_exists") is True:
                pos_lottery_winner_instance.payout_ref = verify_ref_res.get("new_ref")
                pos_lottery_winner_instance.save()
                sleep(1)

            reference = pos_lottery_winner_instance.payout_ref

            _filter_bank_details = filter_bank(cbn_code=bank_code)

            if _filter_bank_details is None:
                data = {"message": "Invalid bank details"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            _bank_code = _filter_bank_details.get("cbn_code")

            # update agent wallet
            # agent_wallet_instance.winnings_bal -= amount
            # agent_wallet_instance.transaction_from = "WINNINGS_WITHDRAW"
            # agent_wallet_instance.phone_number = phone
            # agent_wallet_instance.save()

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=agent_wallet_instance.agent.phone,
                amount=float(amount),
                channel="POS/MOBILE",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="DEBIT",
            )

            wallet_payload = {
                "transaction_from": "WINNINGS_WITHDRAW",
            }

            UserWallet.deduct_wallet(
                user=agent_wallet_instance.agent,
                amount=float(amount),
                channel="POS",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="WINNINGS_WALLET",
                **wallet_payload,
            )

            # remove the money from general withdrawable wallet
            GeneralWithdrawableWallet.deduct_fund(amount=float(amount), phone=_agent_instance.phone)

            # disbursement payload
            _narration = "disbursement"

            if narration is not None and narration != "":
                _narration = narration

            woven_payload = {
                "beneficiary_account_name": name,
                "beneficiary_nuban": account_number,
                "beneficiary_bank_code": _bank_code,
                "bank_code_scheme": "NIP",
                "currency_code": "NGN",
                "narration": _narration,
                "callback_url": "",
                "reference": reference,
                "amount": pos_lottery_winner_instance.amount_won,
            }

            _bank_name = _filter_bank_details.get("name")

            _withdraw_table_instance = cls.objects.create(
                source="WOVEN",
                amount=float(amount),
                disbursement_unique_id=reference,
                phone=_agent_instance.phone,
                payout_trans_ref=reference,
                bank_code=_bank_code,
                name=name,
                channel="POS",
                game_play_id=game_id,
                payout_payload=woven_payload,
                date_won=pos_lottery_winner_instance.date_created,
            )

            woven_payload["source_account"] = settings.WOVEN_DISBURSEMENT_SOURCE_ACCOUNT
            woven_payload["PIN"] = settings.WOVEN_DISBURSEMENT_PAYMENT_PIN

            # update PosLotteryWinners db
            pos_lottery_winner_instance.is_win_claimed = True
            pos_lottery_winner_instance.withdrawl_initiated = True
            pos_lottery_winner_instance.save()

            # initiate payout
            _woven_helper = WovenHelper()

            payout_response = _woven_helper.initaite_payout_to_winners(**woven_payload)

            _withdraw_table_instance.source_response_payload = payout_response
            _withdraw_table_instance.save()

            data = {
                "status": "success",
                "message": "Withdrawal initiated",
                "transaction_ref": reference,
                "agent_id": _agent_instance.user_uuid,
                "account_name": name,
                "bank_name": _bank_name,
                "amount": amount,
                "date": datetime.now(),
            }
            return Response(data, status=status.HTTP_200_OK)

        else:
            data = {"message": "Sorry we could not process your request"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    @classmethod
    def user_woven_disbursement(
        cls,
        amount,
        user_profile_instance,
        user_id,
        bank_name,
        account_number,
        channel="USSD",
        game_id=None,
    ):
        from wallet_app.models import (
            DebitCreditRecord,
            FloatWallet,
            GeneralWithdrawableWallet,
            UserWallet,
        )
        from wyse_ussd.helper.bank import BankManger

        if channel == "USSD" or channel == "USSD_WEB":
            payout_ref = f"ussd-payout{uuid.uuid4()}{user_id}"
        else:
            payout_ref = f"web-payout{uuid.uuid4()}{user_id}"

        bank_db = BankManger().bank_details(bank_name=bank_name)

        user_wallet = UserWallet.objects.filter(user=user_profile_instance, wallet_tag="WEB").last()
        if user_wallet.withdrawable_available_balance < float(amount):
            return {"status": "fail", "message": "Insufficient funds"}

        # user_wallet.withdrawable_available_balance -= float(amount)
        # user_wallet.transaction_from = "WITHDRAWAL"
        # user_wallet.save()

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=user_wallet.user.phone_number,
            amount=float(amount),
            channel="WEB",
            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="DEBIT",
        )

        wallet_payload = {
            "transaction_from": "WITHDRAWAL",
        }

        UserWallet.deduct_wallet(
            user=user_wallet.user.phone_number,
            amount=int(amount),
            channel="WEB",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **wallet_payload,
        )

        # remove the money from general withdrawable wallet
        GeneralWithdrawableWallet.deduct_fund(amount=float(amount), phone=user_wallet.user.phone_number)

        # initiate payout
        woven = WovenHelper()
        woven_payload = {
            "beneficiary_account_name": user_profile_instance.account_name,
            "beneficiary_nuban": account_number,
            "beneficiary_bank_code": bank_db.get("cbn_code"),
            "bank_code_scheme": "NIP",
            "currency_code": "NGN",
            "narration": "winning disbursement",
            "callback_url": settings.WOVEN_CALL_BACK,
            "reference": payout_ref,
            "amount": amount,
        }

        payout_trans_instance = cls.objects.create(
            source="WOVEN",
            amount=int(amount),
            disbursement_unique_id=payout_ref,
            phone=user_profile_instance.phone_number,
            payout_trans_ref=payout_ref,
            bank_code=bank_db.get("bank_code"),
            name=user_profile_instance.account_name,
            payout_payload=woven_payload,
            channel=channel,
            game_play_id=game_id,
        )

        woven_payload["source_account"] = settings.WOVEN_DISBURSEMENT_SOURCE_ACCOUNT
        woven_payload["PIN"] = settings.WOVEN_DISBURSEMENT_PAYMENT_PIN

        woven_response = woven.initaite_payout_to_winners(**woven_payload)

        payout_trans_instance.source_response_payload = woven_response
        payout_trans_instance.save()

        # update float wallet
        FloatWallet().update_float_wallet()

        return {"status": "success", "message": "Woven Payout initiated"}

    @classmethod
    def agent_vfd_disbursement(
        cls,
        amount,
        phone,
        account_number,
        bank_code,
        agent_wallet_instance,
        _agent_instance,
        narration,
        pos_lottery_winner_instance,
        game_id,
    ):
        from rest_framework.response import Response

        from wallet_app.models import DebitCreditRecord, UserWallet

        filter_bank_details = filter_bank(cbn_code=bank_code)
        if filter_bank_details is None:
            data = {"message": "Invalid bank details"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        _bank_code = filter_bank_details.get("bank_code")
        vfd_helper = VfdDisbursementHelperFunc()

        (
            account_enquiry_response,
            account_enquiry_response_status_code,
        ) = vfd_helper.account_enquiry(account_number, _bank_code)

        if account_enquiry_response_status_code != 200:
            data = {"message": "Failed to fetch account name"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        if isinstance(account_enquiry_response, dict):
            account_enquiry_data = account_enquiry_response.get("data")

            if len(account_enquiry_data) == 0:
                data = {"message": "Invalid bank details"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # update agent wallet
            # agent_wallet_instance.winnings_bal -= amount
            # agent_wallet_instance.transaction_from = "WINNINGS_WITHDRAW"
            # agent_wallet_instance.phone_number = phone
            # agent_wallet_instance.save()

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=agent_wallet_instance.agent.phone,
                amount=float(amount),
                channel="POS",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="DEBIT",
            )

            wallet_payload = {
                "transaction_from": "WINNINGS_WITHDRAW",
            }

            UserWallet.deduct_wallet(
                user=agent_wallet_instance.agent,
                amount=int(amount),
                channel="POS",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="WINNINGS_WALLET",
                **wallet_payload,
            )

            # remove the money from general withdrawable wallet
            # GeneralWithdrawableWallet.deduct_fund(amount=float(amount), phone=_agent_instance.phone)

            Wallet.debit_wallet(wallet_type="RETAIL_RTP_WALLET", amount=float(amount))

            # disbursement payload
            # disbursement payload
            _narration = "disbursement"

            if narration is not None and narration != "":
                _narration = narration

            if pos_lottery_winner_instance.payout_ref is None or pos_lottery_winner_instance.payout_ref == "":
                pos_lottery_winner_instance.payout_ref = PosLotteryWinners().generate_transaction_ref()
                pos_lottery_winner_instance.save()
                sleep(1)

            # verify if payout reference is unique
            verify_ref_res = cls.verify_or_generate_payout_ref(pos_lottery_winner_instance.payout_ref)
            if verify_ref_res.get("already_exists") is True:
                pos_lottery_winner_instance.payout_ref = verify_ref_res.get("new_ref")
                pos_lottery_winner_instance.save()
                sleep(1)

            reference = pos_lottery_winner_instance.payout_ref
            name = account_enquiry_data.get("account_name")

            _bank_name = filter_bank_details.get("name")

            vfd_payload = {
                "from_wallet_type": "COLLECTION",
                "data": [
                    {
                        "account_number": account_number,
                        "account_name": name,
                        "bank_code": _bank_code,
                        "bank_name": _bank_name,
                        "amount": amount - 25,
                        "narration": _narration,
                        "is_beneficiary": False,
                        "save_beneficiary": True,
                        "remove_beneficiary": False,
                        "is_recurring": False,
                        "ledger_commission": 20,
                        "commission_type": None,
                        "customer_reference": reference,
                    }
                ],
                "total_amount": 50.00,
                "total_amount_with_charge": 75.00,
            }

            _withdraw_table_instance = cls.objects.create(
                source="VFD",
                amount=float(amount),
                disbursement_unique_id=reference,
                phone=_agent_instance.phone,
                payout_trans_ref=reference,
                bank_code=_bank_code,
                name=name,
                channel="POS",
                game_play_id=game_id,
                payout_payload=vfd_payload,
                date_won=pos_lottery_winner_instance.date_created,
                balance_before=0,
                balance_after=0,
                joined_since=agent_wallet_instance.agent.get_duration(),
                source_wallet="RETAIL_RTP_WALLET",
                recipient_wallet="USER_WALLET",
            )

            vfd_payload["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN

            # update PosLotteryWinners db
            pos_lottery_winner_instance.is_win_claimed = True
            pos_lottery_winner_instance.is_verified = True
            pos_lottery_winner_instance.withdrawl_initiated = True
            pos_lottery_winner_instance.save()

            # initiate payout
            payout_response = vfd_helper.initiate_payout(**vfd_payload)

            _withdraw_table_instance.source_response_payload = payout_response.text
            _withdraw_table_instance.save()

            try:
                res = payout_response.json()
                escrow_id = res.get("data", {}).get("escrow_id")
                _withdraw_table_instance.source_unique_ref = escrow_id
                _withdraw_table_instance.save()
            except Exception:
                pass

            data = {
                "status": "success",
                "message": "Withdrawal initiated",
                "transaction_ref": reference,
                "agent_id": _agent_instance.user_uuid,
                "account_name": name,
                "bank_name": _bank_name,
                "amount": amount,
                "date": datetime.now(),
            }
            return Response(data, status=status.HTTP_200_OK)

        else:
            data = {"message": "Failed to fetch account name"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    @classmethod
    def user_vfd_payout(
        cls,
        amount,
        user_profile_instance,
        user_id,
        bank_name,
        account_number,
        channel="USSD",
        game_id=None,
    ):
        from datetime import datetime

        from rest_framework.response import Response

        from wallet_app.models import DebitCreditRecord, FloatWallet, UserWallet
        from wyse_ussd.helper.bank import BankManger
        from wyse_ussd.models import TelcoUsers

        # print("bank_name", bank_name)

        bank_db = BankManger().bank_details(bank_name=bank_name)
        # print("bank_db", bank_db, "\n\n\n")
        filter_bank_details = filter_bank(cbn_code=bank_db.get("cbn_code"))

        if filter_bank_details is None:
            data = {"message": "Invalid bank details"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        _bank_code = filter_bank_details.get("bank_code")
        vfd_helper = VfdDisbursementHelperFunc()

        # check if user has attempted more than 1 payout in a day
        payout_count = cls.objects.filter(
            phone=user_profile_instance.phone_number, date_added__date=datetime.now().date()
        ).count()
        if payout_count >= 1:
            data = {"message": "You have reached the maximum payout limit for the day"}

        if channel == "USSD" or channel == "USSD_WEB":
            payout_ref = f"ussd-payout{uuid.uuid4()}{user_id}"
        else:
            payout_ref = f"web-payout{uuid.uuid4()}{user_id}"

        user_wallet = UserWallet.objects.filter(user=user_profile_instance, wallet_tag="WEB").last()

        # GeneralWithdrawableWallet.deduct_fund(amount=float(amount), phone=user_wallet.user.phone_number)

        full_name = None
        if user_profile_instance.first_name is not None:
            full_name = f"{user_profile_instance.first_name} {user_profile_instance.last_name}"

        Wallet.debit_wallet(
            wallet_type="NON_RETAIL_WALLET",
            amount=amount,
            user_phone=user_profile_instance.phone_number,
            user_name=full_name,
        )

        (
            account_enquiry_response,
            account_enquiry_response_status_code,
        ) = vfd_helper.account_enquiry(account_number, _bank_code)

        # account_enquiry_response_status_code, account_enquiry_response = 200, {
        #     "data": {"account_name": "John Doe"}
        # }
        if account_enquiry_response_status_code != 200:
            data = {"message": "Failed to fetch account name"}

            user_wallet = UserWallet.objects.filter(user=user_profile_instance, wallet_tag="WEB").last()

            # user_wallet.withdrawable_available_balance += float(amount)
            # user_wallet.transaction_from = "REVERSAL"
            # user_wallet.save()

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=user_wallet.user.phone_number,
                amount=float(amount),
                channel="WEB",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="CREDIT",
            )

            wallet_payload = {
                "transaction_from": "REVERSAL",
            }

            UserWallet.fund_wallet(
                user=user_wallet.user,
                amount=int(amount),
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="WINNINGS_WALLET",
                **wallet_payload,
            )

            # GeneralWithdrawableWallet.add_fund(
            #     amount=float(amount),
            #     phone=user_wallet.user.phone_number,
            #     game_type=None,
            # )

            Wallet.fund_wallet(
                wallet_type="NON_RETAIL_WALLET",
                amount=float(amount),
            )

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if isinstance(account_enquiry_response, dict):
            account_enquiry_data = account_enquiry_response.get("data")

            if len(account_enquiry_data) == 0:
                data = {"message": "Invalid bank details"}

                debit_credit_record = DebitCreditRecord.create_record(
                    phone_number=user_wallet.user.phone_number,
                    amount=float(amount),
                    channel="WEB",
                    reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                    transaction_type="CREDIT",
                )

                wallet_payload = {
                    "transaction_from": "REVERSAL",
                }

                UserWallet.fund_wallet(
                    user=user_wallet.user,
                    amount=int(amount),
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="WINNINGS_WALLET",
                    **wallet_payload,
                )

                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            name = account_enquiry_data.get("account_name")
            _bank_name = filter_bank_details.get("name")

            vfd_payload = {
                "from_wallet_type": "COLLECTION",
                "data": [
                    {
                        "account_number": account_number,
                        "account_name": name,
                        "bank_code": _bank_code,
                        "bank_name": _bank_name,
                        "amount": float(amount) - 25,
                        "narration": "winning disbursement",
                        "is_beneficiary": False,
                        "save_beneficiary": True,
                        "remove_beneficiary": False,
                        "is_recurring": False,
                        "ledger_commission": 20,
                        "commission_type": None,
                        "customer_reference": payout_ref,
                    }
                ],
                "total_amount": 50.00,
                "total_amount_with_charge": 75.00,
            }

            telco_network = user_wallet.user.network_provider
            if telco_network is None:
                try:
                    telco_user = TelcoUsers.objects.using("external2").get(
                        phone_number=user_profile_instance.phone_number
                    )
                except Exception:
                    pass

                else:
                    telco_network = telco_user.network
                    user_wallet.user.network_provider = telco_network
                    user_wallet.save()

            user_wallet.refresh_from_db()

            _withdraw_table_instance = cls.objects.create(
                source="VFD",
                amount=float(amount),
                disbursement_unique_id=payout_ref,
                phone=user_profile_instance.phone_number,
                payout_trans_ref=payout_ref,
                bank_code=_bank_code,
                name=name,
                channel=channel,
                game_play_id=game_id,
                payout_payload=vfd_payload,
                telco_network=telco_network,
                balance_before=user_wallet.withdrawable_available_balance + float(amount),
                balance_after=user_wallet.withdrawable_available_balance,
                joined_since=user_profile_instance.get_duration(),
                source_wallet="NON_RETAIL_WALLET",
                recipient_wallet="USER_WALLET",
            )

            print(
                f"""
            vfd_payload: {vfd_payload}
            """
            )

            cp_vfd_payload = dict(vfd_payload)
            # cp_vfd_payload["transaction_pin"] = config("AGENCY_BANKING_NON_RETAIL_WALLET_TRANSACTION_PIN")
            cp_vfd_payload["transaction_pin"] = "l"

            cp_vfd_payload["customer_reference"] = _withdraw_table_instance.payout_trans_ref

            print(
                f"""

                  cp_vfd_payload: {cp_vfd_payload}, \n\n\n

                  """
            )
            print(
                f"""

                  cp_vfd_payload: {cp_vfd_payload}, \n\n\n

                  """
            )

            # initiate payout
            payout_response = vfd_helper.initiate_payout(**cp_vfd_payload)

            _withdraw_table_instance.source_response_payload = payout_response.text
            _withdraw_table_instance.save()

            # update float wallet
            FloatWallet().update_float_wallet()

            return {
                "status": "success",
                "message": "VFD Payout initiated",
                "data": payout_response,
            }

        else:
            data = {"message": "Failed to fetch account name"}

            user_wallet = UserWallet.objects.filter(user=user_profile_instance, wallet_tag="WEB").last()

            # user_wallet.withdrawable_available_balance += float(amount)
            # user_wallet.transaction_from = "REVERSAL"
            # user_wallet.save()

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=user_wallet.user.phone_number,
                amount=float(amount),
                channel="WEB",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="CREDIT",
            )

            wallet_payload = {
                "transaction_from": "REVERSAL",
            }

            UserWallet.fund_wallet(
                user=user_wallet.user,
                amount=int(amount),
                channel="Web",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="WINNINGS_WALLET",
                **wallet_payload,
            )

            # GeneralWithdrawableWallet.add_fund(
            #     amount=float(amount),
            #     phone=user_wallet.user.phone_number,
            #     game_type=None,
            # )

            Wallet.fund_wallet(
                wallet_type="NON_RETAIL_WALLET",
                amount=amount,
            )

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    @classmethod
    def agent_buddy_disbursement(
        cls,
        amount,
        phone,
        account_number,
        bank_code,
        agent_wallet_instance,
        _agent_instance,
        narration,
        pos_lottery_winner_instance,
        game_id,
    ):
        from rest_framework.response import Response

        from wallet_app.models import DebitCreditRecord, FloatWallet, UserWallet

        # update agent wallet
        # agent_wallet_instance.winnings_bal -= amount
        # agent_wallet_instance.transaction_from = "WINNINGS_WITHDRAW"
        # agent_wallet_instance.phone_number = phone
        # agent_wallet_instance.save()

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent_wallet_instance.agent.phone,
            amount=float(amount),
            channel="POS",
            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="DEBIT",
        )

        wallet_payload = {
            "transaction_from": "WINNINGS_WITHDRAW",
        }

        UserWallet.deduct_wallet(
            user=agent_wallet_instance.agent,
            amount=int(amount),
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **wallet_payload,
        )

        # remove the money from general withdrawable wallet
        # GeneralWithdrawableWallet.deduct_fund(amount=float(amount), phone=_agent_instance.phone)

        Wallet.debit_wallet(
            wallet_type="RETAIL_RTP_WALLET",
            amount=float(amount),
            game_type=pos_lottery_winner_instance.lottery_type,
            user_phone=_agent_instance.phone,
            user_name=f"{_agent_instance.first_name} {_agent_instance.last_name}",
            game_play_id=game_id,
        )

        _narration = "disbursement"

        if narration is not None and narration != "":
            _narration = narration

        payload = {
            "from_wallet_type": "COLLECTION",
            "to_wallet_type": "COLLECTION",
            "data": [
                {
                    "buddy_phone_number": _agent_instance.phone,
                    "amount": float(amount),
                    "narration": _narration,
                    "is_beneficiary": "False",
                    "save_beneficiary": "True",
                    "remove_beneficiary": "False",
                    "is_recurring": "False",
                }
            ],
        }

        if pos_lottery_winner_instance.payout_ref is None or pos_lottery_winner_instance.payout_ref == "":
            pos_lottery_winner_instance.payout_ref = PosLotteryWinners().generate_transaction_ref()
            pos_lottery_winner_instance.save()
            sleep(1)

        # verify if payout reference is unique
        verify_ref_res = cls.verify_or_generate_payout_ref(pos_lottery_winner_instance.payout_ref)
        if verify_ref_res.get("already_exists") is True:
            pos_lottery_winner_instance.payout_ref = verify_ref_res.get("new_ref")
            pos_lottery_winner_instance.save()
            sleep(1)

        # verify if payout reference is unique
        verify_ref_res = cls.verify_or_generate_payout_ref(pos_lottery_winner_instance.payout_ref)
        if verify_ref_res.get("already_exists") is True:
            pos_lottery_winner_instance.payout_ref = verify_ref_res.get("new_ref")
            pos_lottery_winner_instance.save()
            sleep(1)

        reference = pos_lottery_winner_instance.payout_ref

        _withdraw_table_instance = cls.objects.create(
            source="BUDDY",
            amount=float(amount),
            disbursement_unique_id=reference,
            phone=_agent_instance.phone,
            payout_trans_ref=reference,
            name=_agent_instance.name,
            channel="POS",
            game_play_id=game_id,
            payout_payload=payload,
            date_won=pos_lottery_winner_instance.date_created,
            source_wallet="RETAIL_RTP_WALLET",
            recipient_wallet="USER_WALLET",
        )

        payload["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN

        # update PosLotteryWinners db
        pos_lottery_winner_instance.is_win_claimed = True
        pos_lottery_winner_instance.is_verified = True
        pos_lottery_winner_instance.withdrawl_initiated = True
        pos_lottery_winner_instance.payout_successful = True
        pos_lottery_winner_instance.payout_verified = True
        pos_lottery_winner_instance.save()

        if (settings.DEBUG is True) or (settings.DEBUG is True):
            return {"message": "success"}

        vfd_disbursement_helper = VfdDisbursementHelperFunc()
        payout_response = vfd_disbursement_helper.liberty_agency_payout(_agent_instance.phone, **payload)

        _withdraw_table_instance.source_response_payload = payout_response
        _withdraw_table_instance.save()

        if payout_response.get("message") == "success":
            _withdraw_table_instance.disbursed = True
            _withdraw_table_instance.is_verified = True
            _withdraw_table_instance.save()

        # update float wallet
        FloatWallet().update_float_wallet()

        data = {
            "status": "success",
            "message": "Withdrawal initiated",
            "transaction_ref": reference,
            "agent_id": _agent_instance.user_uuid,
            "account_name": f"{_agent_instance.name}",
            "bank_name": "WALLET ACCOUNT",
            "amount": amount,
            "date": datetime.now(),
        }

        # data = {"status": "success", "message": "Liberty agency buddy Payout initiated"}
        return Response(data, status=status.HTTP_200_OK)


class TestimonalTable(models.Model):
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name="testimonal")
    message = models.TextField(null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.id)

    class Meta:
        verbose_name = "TESTIMONAL"
        verbose_name_plural = "TESTIMONALS"


def get_default_earning_multiplier():
    return {"tier1": 15, "tier2": 25, "tier3": 35}


class BankerConstant(models.Model):
    multiplier = models.CharField(max_length=250, default="15,25,35", help_text="")
    minimum_stake = models.FloatField(default=200)
    rto = models.FloatField(default=0.0)
    rtp = models.FloatField(default=1.0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    earning_multiplier = models.JSONField(default=get_default_earning_multiplier)

    @classmethod
    def get_const_instance(cls):
        return cls.objects.filter(is_active=True).last()

    @classmethod
    def get_multiplier(cls) -> list:
        const_multipliers_str = cls.get_const_instance().multiplier
        return list(map(int, const_multipliers_str.split(",")))

    @classmethod
    def get_banker_minimum_stake(cls):
        return cls.objects.last().minimum_stake if cls.objects.last() else 200

    class Meta:
        verbose_name = "BANKER CONSTANT VARIABLES"
        verbose_name_plural = "BANKER CONSTANT VARIABLES"


def debuginfo(message):
    import inspect

    caller = inspect.getframeinfo(inspect.stack()[1][0])
    print(f"{caller.filename}:{caller.function}:{caller.lineno} - {message}")


from datetime import datetime, time, timedelta

from django.db import models


class DrawTracker(models.Model):
    DRAW_TIME_CHOICES = [
        ("08:00 - 08:30", "08:00 - 08:30"),
        ("08:30 - 09:00", "08:30 - 09:00"),
        ("09:00 - 09:30", "09:00 - 09:30"),
        ("09:30 - 10:00", "09:30 - 10:00"),
        ("10:00 - 10:30", "10:00 - 10:30"),
        ("10:30 - 11:00", "10:30 - 11:00"),
        ("11:00 - 11:30", "11:00 - 11:30"),
        ("11:30 - 12:00", "11:30 - 12:00"),
        ("12:00 - 12:30", "12:00 - 12:30"),
        ("12:30 - 13:00", "12:30 - 13:00"),
        ("13:00 - 13:30", "13:00 - 13:30"),
        ("13:30 - 14:00", "13:30 - 14:00"),
        ("14:00 - 14:30", "14:00 - 14:30"),
        ("14:30 - 15:00", "14:30 - 15:00"),
        ("15:00 - 15:30", "15:00 - 15:30"),
        ("15:30 - 16:00", "15:30 - 16:00"),
        ("16:00 - 16:30", "16:00 - 16:30"),
        ("16:30 - 17:00", "16:30 - 17:00"),
        ("17:00 - 17:30", "17:00 - 17:30"),
        ("17:30 - 18:00", "17:30 - 18:00"),
        ("18:00 - 18:30", "18:00 - 18:30"),
        ("18:30 - 19:00", "18:30 - 19:00"),
        ("19:00 - 19:30", "19:00 - 19:30"),
        ("19:30 - 20:00", "19:30 - 20:00"),
        ("20:00 - 20:30", "20:00 - 20:30"),
        ("20:30 - 21:00", "20:30 - 21:00"),
    ]

    GAME_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASH", "INSTANT_CASH"),
        ("BANKER", "BANKER"),
    ]

    date = models.DateField()
    game_type = models.CharField(max_length=20, choices=GAME_TYPE_CHOICES)
    draw_time = models.CharField(max_length=20, choices=DRAW_TIME_CHOICES)
    start_time = models.DateTimeField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)
    completed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("date", "draw_time")

    def save(self, *args, **kwargs):
        if not self.start_time or not self.end_time:
            try:
                start_str, end_str = self.draw_time.split(" - ")

                if self.draw_time == "08:00 - 08:30":
                    # Special case: span from 9pm previous day to 8am today
                    previous_day = self.date - timedelta(days=1)
                    start_dt = datetime.combine(previous_day, time(hour=21, minute=0))  # 9:00 PM previous day
                    end_dt = datetime.combine(self.date, time(hour=8, minute=0))  # 8:00 AM today
                else:
                    start_dt = datetime.combine(self.date, datetime.strptime(start_str, "%H:%M").time())
                    end_dt = datetime.combine(self.date, datetime.strptime(end_str, "%H:%M").time())

                self.start_time = start_dt
                self.end_time = end_dt

            except Exception as e:
                raise ValueError(f"Invalid draw_time format: {self.draw_time}") from e

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.date} | {self.draw_time} ({'Done' if self.completed else 'Pending'})"


class LottoTicket(models.Model):
    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
    )

    LOTTO_CHANNEL = [
        ("USSD", "USSD"),
        ("USSD_WEB", "USSD_WEB"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
        ("SYSTEM_BONUS", "SYSTEM_BONUS"),
    ]

    LOTTO_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("QUIKA", "QUIKA"),  # new game
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("BANKER", "BANKER"),
    ]

    LOTTO_SOURCE = [
        ("NORMAL", "NORMAL"),
        ("BONUS", "BONUS"),
    ]

    GAME_TYPE = [
        ("AWOOF", "AWOOF"),
        ("NORMAL", "NORMAL"),
    ]

    DRAWN_FOR_CHOICES = [
        ("GLOBAL", "GLOBAL"),
        ("LOCAL", "LOCAL"),
    ]

    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    agent_profile = models.ForeignKey(Agent, on_delete=models.CASCADE, null=True, blank=True)
    batch = models.ForeignKey(LotteryBatch, on_delete=models.CASCADE, null=True, blank=True)
    phone = models.CharField(max_length=300)
    stake_amount = models.FloatField(default=0.00)
    potential_winning = models.FloatField(default=0.00)
    expected_amount = models.FloatField(default=0.00)
    amount_paid = models.FloatField(default=0.00, db_index=True)
    illusion = models.FloatField(default=0.00)
    rto = models.FloatField(default=0.00)
    rtp = models.FloatField(default=0.00)
    rtp_per = models.FloatField(default=0.00)
    effective_rtp = models.FloatField(default=0.00)
    commission_per = models.FloatField(default=0.00)
    commission_value = models.FloatField(default=0.00)
    salary_for_life_jackpot_per = models.FloatField(default=0.00)
    salary_for_life_jackpot_amount = models.FloatField(default=0.00)
    win_commission_per = models.FloatField(default=0.00)
    win_commission_value = models.FloatField(default=0.00)
    ussd_telco_commission = models.FloatField(default=0.00)
    ussd_telco_commission_value = models.FloatField(default=0.00)
    ussd_telco_aggregator_commission = models.FloatField(default=0.00)
    ussd_telco_aggregator_commission_value = models.FloatField(default=0.00)
    paid = models.BooleanField(default=False, db_index=True)
    date = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)
    number_of_ticket = models.IntegerField(default=0, db_index=True)
    channel = models.CharField(max_length=150, choices=LOTTO_CHANNEL, default="POS_AGENT", db_index=True)
    game_play_id = models.CharField(max_length=150, null=True, blank=True, db_index=True)
    unique_game_play_id = models.CharField(max_length=150, null=True, blank=True)

    awoof_game_play_id = models.CharField(max_length=150, null=True, blank=True)
    lottery_type = models.CharField(max_length=150, choices=LOTTO_TYPE, default="SALARY_FOR_LIFE", db_index=True)
    lottery_source = models.CharField(max_length=150, choices=LOTTO_SOURCE, default="NORMAL", db_index=True)
    game_type = models.CharField(max_length=150, choices=GAME_TYPE, default="NORMAL", db_index=True)
    service_type = models.CharField(
        max_length=150,
        choices=SERVICE_TYPE,
        default="AWOOF",
        db_index=True,
        help_text="the type of service played, Awoof or Insurance",
    )
    has_interest = models.BooleanField(default=True)
    ticket = models.CharField(
        max_length=200,
        help_text="Serialization of the ticket/selection of numbers made by the player",
    )
    system_generated_num = models.CharField(max_length=300, null=True, blank=True)
    win_combo = models.CharField(
        max_length=200,
        help_text="Actual complete winning number for batch",
        null=True,
        blank=True,
    )
    is_agent = models.BooleanField(default=False)
    s4l_drawn = models.BooleanField(default=False)
    instant_cashout_drawn = models.BooleanField(default=False)
    pos_instant_cashout_drawn = models.BooleanField(default=False)
    icash_counted = models.BooleanField(default=False)
    icash_2_counted = models.BooleanField(default=False)
    icash_local_counted = models.BooleanField(default=False)
    is_duplicate = models.BooleanField(default=False)
    game_id_treated = models.BooleanField(
        default=False,
        help_text="This tracks if or not a game id has been treated or not, because instant cash treats individual tickets seperately, but the paid amounts are not lumped, but have to be infered by a multiplication by the game ID.",
    )
    pin = models.CharField(
        max_length=150,
        null=True,
        blank=True,
        help_text="Pin use for cashout if the lottery is played from POS_AGENT and "
        "the player didn't provide his phone number. This's not the same as the pin used for retail ticket",
    )
    identity_id = models.CharField(
        max_length=500,
        null=True,
        blank=True,
        help_text="This is basically created for transactional purpose",
    )
    played_via_telco_channel = models.BooleanField(default=False)
    is_new_quika_game = models.BooleanField(default=False)
    telco_network = models.CharField(
        max_length=150,
        choices=NETWORK_PROVIDER,
        blank=True,
        null=True,
    )
    drawn_for = models.CharField(max_length=25, choices=DRAWN_FOR_CHOICES, default="GLOBAL")
    seeder_status = models.CharField(
        max_length=100,
        choices=(("COMPLETE", "COMPLETE"), ("PROCESSING", "PROCESSING"), ("PENDING", "PENDING")),
        default="PENDING",
        help_text="Current status of the seeder for this ticket.",
    )
    content_delivery_sms_sent = models.BooleanField(default=False)
    product_id = models.CharField(max_length=150, null=True, blank=True)

    def __str__(self):
        return str(self.id)

    # def __init__(self, *args, **kwargs):
    #     super(LottoTicket, self).__init__(*args, **kwargs)
    #     self._original_paid = self.paid
    #     self._original_amount_paid = self.amount_paid

    class Meta:
        verbose_name = "SALARY FOR LIFE & INSTANT CASHOUT TICKET"
        verbose_name_plural = "SALARY FOR LIFE & INSTANT CASHOUT TICKETS"
        indexes = [
            models.Index(fields=["phone", "paid", "channel"]),
            models.Index(fields=["game_play_id"]),
            models.Index(fields=["paid"]),
            models.Index(fields=["channel"]),
            models.Index(fields=["phone"]),
            models.Index(fields=["lottery_type"]),
            models.Index(fields=["seeder_status"]),
            models.Index(TruncDate("date"), "date", name="date_date_idx"),
        ]

    def save(self, *args, **kwargs):
        """
        Override the default save method for the LottoTicket model to handle various business logic.

        This method performs several operations:
        1. Ensures batch records are saved
        2. Handles agent-related operations
        3. Processes financial calculations (commissions, RTO, RTP, etc.)
        4. Updates various wallets based on ticket status and channel
        5. Manages jackpot contributions

        Args:
            *args: Variable length argument list passed to parent save method
            **kwargs: Arbitrary keyword arguments passed to parent save method

        Returns:
            The result of the parent save method
        """
        # Save associated batch if it exists but hasn't been saved yet
        if self.batch is not None and self.batch.pk is None:
            self.batch.save()

        # Handle agent profile settings
        if self.agent_profile:
            self.is_agent = True

        # Track iCash local counting for agent profiles
        if self.paid and self.agent_profile and not self.icash_local_counted:
            self.icash_local_counted = True

        # Round financial values to 2 decimal places
        self.amount_paid = round(self.amount_paid, 2)
        self.expected_amount = round(self.expected_amount, 2)
        self.illusion = round(self.illusion, 2)

        # Handle existing ticket updates
        if self.pk:
            # Retrieve original values for comparison
            _original_paid = False

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "paid":
                    _original_paid = field.value_from_object(old)
                elif field.name in ["amount_paid", "instant_cashout_drawn"]:
                    field.value_from_object(old)

            # Process payment status changes
            if _original_paid != self.paid and self.paid is True:
                self._process_payment()

                # UPDATING RTP AND RTO IN GAME DAILY ACTIVITIES TABLE
                if self.agent_profile:
                    _from_lotto_agent = True if self.agent_profile.terminal_id is not None else False
                    try:
                        GamesDailyActivities.create_record(
                            game_type=self.lottery_type, rtp=self.rtp, rto=self.rto, from_lotto_agent=_from_lotto_agent
                        )
                    except:
                        pass

                    try:
                        RetailWalletTransactions.create_debit_record_for_game_play(
                            amount=self.amount_paid,
                            wallet_value=self.rtp,
                            rto_value=self.rto,
                            rtp_value=self.rtp,
                            game_type=self.lottery_type,
                        )
                    except:
                        pass

                    agent_instance = self.agent_profile
                    GeneralRetailLottoGames.create_record(
                        agent_phone_number=agent_instance.phone,
                        agent_name=agent_instance.full_name,
                        agent_email=agent_instance.email,
                        batch_uuid=self.batch.batch_uuid,
                        game_play_id=self.game_play_id,
                        game_pin=self.pin,
                        lucky_number=self.ticket,
                        purchase_amount=self.amount_paid,
                        lotto_db_id=self.id,
                        paid=self.paid,
                        number_of_ticket=self.number_of_ticket,
                        rtp=self.rtp,
                        rto=self.rto,
                        commission_percentage=self.commission_per,
                        type_of_agent=agent_instance.agent_type,
                        lotto_game_type=self.lottery_type,
                        potential_winnings=self.potential_winning,
                    )

        # Set unique game play ID
        self.unique_game_play_id = self.game_play_id

        # Ensure batch is saved
        if self.batch is not None and self.batch.pk is None:
            self.batch.save()

        # Call parent save method
        return super(LottoTicket, self).save(*args, **kwargs)

    def _process_payment(self):
        """
        Process payment for an existing ticket that has been marked as paid.

        This method handles:
        - Commission calculations
        - RTP (Return to Player) calculations
        - RTO (Return to Operator) calculations
        - Jackpot contributions
        - Wallet updates

        Returns:
            None, or early returns None if batch is missing
        """

        # Early return if batch is missing
        if self.batch is None:
            return None

        amount_paid_after_removing_comission = self.amount_paid

        # Handle POS_AGENT / MOBILE APP / WEB channels
        if self.channel in ["POS_AGENT", "MOBILE", "WEB"]:
            self._process_agent_mobile_web_payment(amount_paid_after_removing_comission)
        else:
            # Handle USSD and other channels
            self._process_other_channels_payment(amount_paid_after_removing_comission)

        # Update agent commission
        if self.channel == "POS_AGENT":
            if self.agent_profile:
                AgentWallet.reward_commission(
                    agent_id=self.agent_profile.id,
                    game_play_amount=self.amount_paid,
                    commission_type="COMMISSION_ON_GAME_PLAY",
                    game_type=self.lottery_type,
                    rto_amount=self.rto,
                )

    def _process_agent_mobile_web_payment(self, amount_paid_after_removing_comission):
        """
        Process payments made through POS_AGENT, MOBILE, or WEB channels.

        Args:
            amount_paid_after_removing_comission: The amount paid for the ticket

        Returns:
            None
        """
        from wallet_app.models import CommissionWallet

        # Set default commission percentage
        self.commission_per = AgentConstantVariables().get_agent_sales_commission()

        # Process POS_AGENT specific commission
        if self.channel == "POS_AGENT":
            # Get agent sales commission from Redis
            instant_game_commission_percentage = AgentConstantVariables.get_instant_game_commission_percentage()
            draw_game_commission_percentage = AgentConstantVariables.get_draw_game_commission_percentage()

            # Apply commission only for non-telco channel games
            if not self.played_via_telco_channel:
                if self.lottery_type in ["BANKER", "SALARY_FOR_LIFE"]:
                    self.commission_per = draw_game_commission_percentage
                else:
                    self.commission_per = instant_game_commission_percentage
            else:
                self.commission_per = 0

            # Calculate commission value
            self.commission_value = round(self.amount_paid * self.commission_per, 2)

        # Skip calculations for AWOOF game type
        if self.game_type == "AWOOF":
            return

        # Get RTP percentage
        _rtp = (ConstantVariable().rto_rtp()).get("rtp")

        # Special RTP handling for BANKER and SALARY_FOR_LIFE
        if (self.lottery_type in ["BANKER", "SALARY_FOR_LIFE"]) and (not self.played_via_telco_channel):
            # Use separate RTP for BANKER and SALARY_FOR_LIFE
            _rtp = ConstantVariable.objects.all().last().rtp_s4l_bank_wysecash / 100

        # Set RTP percentage
        self.rtp_per = float(_rtp) * 100

        # Calculate RTO percentage
        rto_per = ((100 - (float(_rtp) * 100)) / 100) - self.commission_per

        # Calculate RTO amount
        self.rto = round(self.amount_paid * rto_per, 2)

        # Calculate total percentage for agent commission + RTO
        agent_sales_commission_plus_per = rto_per + self.commission_per

        # Calculate amount after removing commission and RTO
        amount_paid_after_removing_comission = self.amount_paid - (
            self.amount_paid * agent_sales_commission_plus_per
        )  # rtp

        # Handle Global Jackpot contributions
        if self.lottery_type == "BANKER":
            _globa_jackpot = 0  # No percentage deduction for BANKER
        else:
            # Get global jackpot percentage and calculate contribution
            global_jackpot_per = JackpotConstantVariable().get_percentage_to_share()
            _globa_jackpot = amount_paid_after_removing_comission * global_jackpot_per

            # Add amount to jackpot
            Jackpot.add_to_jackpot(_globa_jackpot)

        # Remove jackpot contribution from amount
        amount_paid_after_removing_comission = amount_paid_after_removing_comission - _globa_jackpot

        # POS_AGENT win commission
        self.win_commission_per = AgentConstantVariables().get_win_agent_commission()
        self.win_commission_value = round(
            amount_paid_after_removing_comission * self.win_commission_per,
            2,
        )

        # Update commission wallet
        CommissionWallet().update_commission_wallet_amount(
            amount=self.win_commission_value,
            phone_number=self.user_profile.phone_number,
        )

        # Remove win commission from amount
        amount_paid_after_removing_comission = amount_paid_after_removing_comission - self.win_commission_value

        # Handle SALARY_FOR_LIFE specific calculations
        if self.lottery_type == "SALARY_FOR_LIFE":
            self._process_salary_for_life_jackpot(amount_paid_after_removing_comission)
            amount_paid_after_removing_comission = (
                amount_paid_after_removing_comission - self.salary_for_life_jackpot_amount
            )

        # Add to RTO wallet
        if self.played_via_telco_channel is False:
            # RtoWallet().update_rto_wallet_amount(
            #     amount=self.rto,
            #     phone_number=self.user_profile.phone_number,
            #     wallet_type="DEFAULT" if self.played_via_telco_channel else "TELCO",
            # )

            rto = self.rto

            vertical_lead_commission = 0
            supervisor_commission = 0

            ## calculate veritical lead and supervisor commission
            if self.agent_profile is not None:
                if self.agent_profile.supervisor is not None:
                    supervisor_commission = round(
                        rto * AgentConstantVariables.get_supervisor_commission_percentage(), 2
                    )

                    SupervisorWallet.fund_commission_wallet(
                        supervisor_phone_number=self.agent_profile.supervisor.phone,
                        amount=supervisor_commission,
                        transaction_type="CREDIT",
                        lottery_type=self.lottery_type,
                    )

                    if self.agent_profile.supervisor.vertical_lead is not None:
                        vertical_lead_commission = round(
                            rto * AgentConstantVariables.get_vertical_lead_commission_percentage(), 2
                        )

                        LottoVerticalLeadWallet.fund_commission_wallet(
                            vertical_lead_phone_number=self.agent_profile.supervisor.vertical_lead.phone,
                            amount=vertical_lead_commission,
                            transaction_type="CREDIT",
                            lottery_type=self.lottery_type,
                        )

            rto = rto - (vertical_lead_commission + supervisor_commission)

            self.rto = rto
            Wallet.fund_wallet(amount=self.rto, wallet_type="RTO_WALLET", game_type=self.lottery_type)

        # Set final RTP amount
        self.rtp = amount_paid_after_removing_comission

        # Add funds to general withdrawable wallet
        # GeneralWithdrawableWallet.add_fund(
        #     self.rtp,
        #     self.user_profile.phone_number,
        #     self.lottery_type,
        # )
        if self.channel == "POS_AGENT":
            Wallet.fund_wallet(amount=self.rtp, wallet_type="RETAIL_RTP_WALLET", game_type=self.lottery_type)

    def _process_other_channels_payment(self, amount_paid_after_removing_comission):
        """
        Process payments made through channels other than POS_AGENT, MOBILE, or WEB.

        Args:
            amount_paid_after_removing_comission: The amount paid for the ticket

        Returns:
            None
        """

        # Handle telco aggregator channel for USSD
        if (self.played_via_telco_channel is True) and (self.channel == "USSD") and (self.game_type != "AWOOF"):
            self._process_telco_commissions(amount_paid_after_removing_comission)
            amount_paid_after_removing_comission = self.amount_paid - (
                self.ussd_telco_aggregator_commission_value + self.ussd_telco_commission_value
            )

        # Skip calculations for AWOOF game type
        if self.game_type == "AWOOF":
            return

        # Get appropriate RTP percentage
        if self.played_via_telco_channel is True:
            _rtp = (ConstantVariable().telco_rtp_perc()).get("rtp")
        else:
            _rtp = (ConstantVariable().rto_rtp()).get("rtp")

        # Special RTP handling for BANKER and SALARY_FOR_LIFE
        if (self.lottery_type in ["BANKER", "SALARY_FOR_LIFE"]) and (not self.played_via_telco_channel):
            _rtp = ConstantVariable.objects.all().last().rtp_s4l_bank_wysecash / 100

        # Set RTP percentage
        self.rtp_per = float(_rtp) * 100

        # Calculate RTO percentage
        rto_per = (100 - (float(_rtp) * 100)) / 100

        # Calculate RTO amount
        self.rto = round(amount_paid_after_removing_comission * rto_per, 2)

        # Remove RTO from amount
        amount_paid_after_removing_comission = amount_paid_after_removing_comission - self.rto

        # Handle SMS charges
        sms_charge = ConstantVariable.objects.last().general_sms_charges
        SmsChargeWallet().add_charge(provider="WHISPER_SMS", amount=sms_charge)
        amount_paid_after_removing_comission = amount_paid_after_removing_comission - sms_charge

        # Handle global jackpot for non-BANKER/SALARY_FOR_LIFE games
        if self.lottery_type not in ["BANKER", "SALARY_FOR_LIFE"]:
            global_jackpot_per = JackpotConstantVariable().get_percentage_to_share()
            _globa_jackpot = amount_paid_after_removing_comission * global_jackpot_per
            Jackpot.add_to_jackpot(_globa_jackpot)
            amount_paid_after_removing_comission = amount_paid_after_removing_comission - _globa_jackpot

        # Handle SALARY_FOR_LIFE specific calculations
        if self.lottery_type == "SALARY_FOR_LIFE":
            self._process_salary_for_life_jackpot(amount_paid_after_removing_comission)
            amount_paid_after_removing_comission = (
                amount_paid_after_removing_comission - self.salary_for_life_jackpot_amount
            )

        # Set final RTP amount
        self.rtp = amount_paid_after_removing_comission

        # Add to RTO wallet based on channel
        # wallet_type = "TELCO" if self.played_via_telco_channel else "DEFAULT"
        if self.played_via_telco_channel is False:

            # RtoWallet().update_rto_wallet_amount(
            #     amount=self.rto,
            #     phone_number=self.user_profile.phone_number,
            #     wallet_type=wallet_type,
            # )

            rto = self.rto

            vertical_lead_commission = 0
            supervisor_commission = 0

            ## calculate veritical lead and supervisor commission
            if self.agent_profile is not None:
                if self.agent_profile.supervisor is not None:
                    supervisor_commission = round(
                        rto * AgentConstantVariables.get_supervisor_commission_percentage(), 2
                    )

                    SupervisorWallet.fund_commission_wallet(
                        supervisor_phone_number=self.agent_profile.supervisor.phone,
                        amount=supervisor_commission,
                        transaction_type="CREDIT",
                        lottery_type=self.lottery_type,
                    )

                    if self.agent_profile.supervisor.vertical_lead is not None:
                        vertical_lead_commission = round(
                            rto * AgentConstantVariables.get_vertical_lead_commission_percentage(), 2
                        )

                        LottoVerticalLeadWallet.fund_commission_wallet(
                            vertical_lead_phone_number=self.agent_profile.supervisor.vertical_lead.phone,
                            amount=vertical_lead_commission,
                            transaction_type="CREDIT",
                            lottery_type=self.lottery_type,
                        )

            rto = rto - (vertical_lead_commission + supervisor_commission)

            self.rto = rto
            Wallet.fund_wallet(amount=self.rto, wallet_type="RTO_WALLET", game_type=self.lottery_type)

        # Add funds to general withdrawable wallet
        # GeneralWithdrawableWallet.add_fund(
        #     self.rtp,
        #     self.user_profile.phone_number,
        #     self.lottery_type,
        # )

        if self.channel == "POS_AGENT":
            Wallet.fund_wallet(amount=self.rtp, wallet_type="RETAIL_RTP_WALLET", game_type=self.lottery_type)

    def _process_telco_commissions(self, amount_paid_after_removing_comission):
        """
        Process telco and aggregator commissions for USSD channel payments.

        Args:
            amount_paid_after_removing_comission: The amount paid for the ticket

        Returns:
            None
        """
        # Get telco commission
        ussd_telco_commission = ConstantVariable().get_telco_commission()
        self.ussd_telco_commission = ussd_telco_commission
        self.ussd_telco_commission_value = round(self.amount_paid * ussd_telco_commission, 2)

        # Get aggregator commission
        ussd_telco_aggregator_commission = ConstantVariable().get_aggregator_commission()
        self.ussd_telco_aggregator_commission = ussd_telco_aggregator_commission
        self.ussd_telco_aggregator_commission_value = self.amount_paid * ussd_telco_aggregator_commission

    def _process_salary_for_life_jackpot(self, amount_paid_after_removing_comission):
        """
        Process Salary for Life jackpot calculations.

        Args:
            amount_paid_after_removing_comission: The amount after removing commissions

        Returns:
            None
        """
        # Get salary for life jackpot percentage from Redis
        redis_db = RedisStorage(redis_key="sla4life_global_jackpot_perc")
        sal_4_life_jackpot_per = redis_db.get_data()

        if sal_4_life_jackpot_per is None:
            sal_4_life_jackpot_per = ConstantVariable().salary_for_life_global_jackpot_percentage()
        else:
            sal_4_life_jackpot_per = float(sal_4_life_jackpot_per.decode("utf-8"))

        # Calculate salary for life jackpot amount
        _salary_for_life_jackpot = round(amount_paid_after_removing_comission * sal_4_life_jackpot_per, 2)

        # Set jackpot values
        self.salary_for_life_jackpot_per = sal_4_life_jackpot_per
        self.salary_for_life_jackpot_amount = _salary_for_life_jackpot

    def _process_payment_for_new_ticket(self):
        """
        Process payment for a newly created ticket that has been marked as paid.

        This method handles payment processing specifically for VIRTUAL_SOCCER lottery type.

        Returns:
            None
        """
        amount_paid_after_removing_comission = self.amount_paid

        # Only process VIRTUAL_SOCCER lottery type
        if self.lottery_type == "VIRTUAL_SOCCER":
            pass

            # Handle POS_AGENT commission for VIRTUAL_SOCCER
            if self.channel == "POS_AGENT":
                # # Get agent sales commission from Redis
                # redis_db = RedisStorage(redis_key="agent_sales_commission")
                # agent_sales_commission = redis_db.get_data()

                instant_game_commission_percentage = AgentConstantVariables.get_instant_game_commission_percentage()
                draw_game_commission_percentage = AgentConstantVariables.get_draw_game_commission_percentage()

                if not self.played_via_telco_channel:
                    if self.lottery_type in ["BANKER", "SALARY_FOR_LIFE"]:
                        self.commission_per = draw_game_commission_percentage
                    else:
                        self.commission_per = instant_game_commission_percentage
                else:
                    self.commission_per = 0

                # if agent_sales_commission is None:
                #     agent_sales_commission = AgentConstantVariables().get_agent_sales_commission()
                # else:
                #     agent_sales_commission = float(agent_sales_commission.decode("utf-8"))

                # Set commission percentage
                # self.commission_per = agent_sales_commission

            # Get RTP from Redis
            redis_db = RedisStorage(redis_key="ticket_rtp")
            _rtp = redis_db.get_data()

            if _rtp is None:
                _rtp = (ConstantVariable().rto_rtp()).get("rtp")
            else:
                _rtp = float(_rtp.decode("utf-8"))

            # Set RTP percentage
            self.rtp_per = float(_rtp) * 100

            # Calculate RTP and RTO
            rtp = amount_paid_after_removing_comission * _rtp
            rto = amount_paid_after_removing_comission - rtp

            # Add RTP to general withdrawable wallet
            # GeneralWithdrawableWallet.add_fund(rtp, self.user_profile.phone_number, self.lottery_type)
            if self.channel == "POS_AGENT":
                Wallet.fund_wallet(amount=rtp, wallet_type="RETAIL_RTP_WALLET", game_type=self.lottery_type)

                vertical_lead_commission = 0
                supervisor_commission = 0

                ## calculate veritical lead and supervisor commission
                if self.agent_profile is not None:
                    if self.agent_profile.supervisor is not None:
                        supervisor_commission = round(
                            rto * AgentConstantVariables.get_supervisor_commission_percentage(), 2
                        )

                        SupervisorWallet.fund_commission_wallet(
                            supervisor_phone_number=self.agent_profile.supervisor.phone,
                            amount=supervisor_commission,
                            transaction_type="CREDIT",
                            lottery_type=self.lottery_type,
                        )

                        if self.agent_profile.supervisor.vertical_lead is not None:
                            vertical_lead_commission = round(
                                rto * AgentConstantVariables.get_vertical_lead_commission_percentage(), 2
                            )

                            LottoVerticalLeadWallet.fund_commission_wallet(
                                vertical_lead_phone_number=self.agent_profile.supervisor.vertical_lead.phone,
                                amount=vertical_lead_commission,
                                transaction_type="CREDIT",
                                lottery_type=self.lottery_type,
                            )

                rto = rto - (vertical_lead_commission + supervisor_commission)

                Wallet.fund_wallet(amount=rto, wallet_type="RTO_WALLET", game_type=self.lottery_type)
            elif self.played_via_telco_channel is False:

                vertical_lead_commission = 0
                supervisor_commission = 0

                ## calculate veritical lead and supervisor commission
                if self.agent_profile is not None:
                    if self.agent_profile.supervisor is not None:
                        supervisor_commission = round(
                            rto * AgentConstantVariables.get_supervisor_commission_percentage(), 2
                        )

                        SupervisorWallet.fund_commission_wallet(
                            supervisor_phone_number=self.agent_profile.supervisor.phone,
                            amount=supervisor_commission,
                            transaction_type="CREDIT",
                            lottery_type=self.lottery_type,
                        )

                        if self.agent_profile.supervisor.vertical_lead is not None:
                            vertical_lead_commission = round(
                                rto * AgentConstantVariables.get_vertical_lead_commission_percentage(), 2
                            )

                            LottoVerticalLeadWallet.fund_commission_wallet(
                                vertical_lead_phone_number=self.agent_profile.supervisor.vertical_lead.phone,
                                amount=vertical_lead_commission,
                                transaction_type="CREDIT",
                                lottery_type=self.lottery_type,
                            )

                rto = rto - (vertical_lead_commission + supervisor_commission)

                Wallet.fund_wallet(amount=rto, wallet_type="RTO_WALLET", game_type=self.lottery_type)

            # Handle POS_AGENT win commission
            # if self.channel == "POS_AGENT":
            #     self.win_commission_per = AgentConstantVariables().get_win_agent_commission()
            #     self.win_commission_value = rtp * self.win_commission_per

            #     # Update commission wallet
            #     CommissionWallet().update_commission_wallet_amount(
            #         amount=self.win_commission_value,
            #         phone_number=self.user_profile.phone_number,
            #     )

            # Add to RTO wallet
            # RtoWallet().update_rto_wallet_amount(amount=rto, phone_number=self.user_profile.phone_number)

            # Handle SMS charges
            sms_charge = ConstantVariable.objects.last().general_sms_charges
            SmsChargeWallet().add_charge(provider="WHISPER_SMS", amount=sms_charge)

            # Calculate final RTP after deductions
            self.rtp = rtp - (
                self.salary_for_life_jackpot_amount
                + self.win_commission_value
                + self.salary_for_life_jackpot_amount
                + sms_charge
            )

    @classmethod
    def get_active_games(cls):
        return cls.objects.filter(paid=True, has_interest=True, batch__is_active=True).order_by("-date")

    @staticmethod
    def salary_for_life_stake_amount_pontential_winning(ticket_count) -> dict:
        if ticket_count == 1:
            return {"stake_amount": 100, "total_winning_amount": 5000}
        elif ticket_count == 2:
            return {"stake_amount": 300, "total_winning_amount": 15000}
        elif ticket_count == 3:
            return {"stake_amount": 600, "total_winning_amount": 50000}
        elif ticket_count == 4:
            return {"stake_amount": 1200, "total_winning_amount": 150000}
        elif ticket_count == 5:
            return {"stake_amount": 2000, "total_winning_amount": 250000}
        elif ticket_count == 6:
            return {"stake_amount": 2400, "total_winning_amount": 500000}
        elif ticket_count == 7:
            return {"stake_amount": 2800, "total_winning_amount": 750000}
        elif ticket_count == 8:
            return {"stake_amount": 3200, "total_winning_amount": 900000}
        elif ticket_count == 9:
            return {"stake_amount": 3600, "total_winning_amount": 1250000}
        elif ticket_count == 10:
            return {"stake_amount": 5000, "total_winning_amount": 1500000}
        else:
            return {"stake_amount": 0, "total_winning_amount": 0}

    @staticmethod
    def salary_for_life_stake_amount_pontential_winning_for_lotto_agents(
        ticket_count=None,
    ) -> dict:
        prices = {
            1: {"stake_amount": 150, "total_winning_amount": 5000},
            2: {"stake_amount": 350, "total_winning_amount": 15000},
            3: {"stake_amount": 700, "total_winning_amount": 50000},
            4: {"stake_amount": 1400, "total_winning_amount": 150000},
            5: {"stake_amount": 2030, "total_winning_amount": 250000},
            6: {"stake_amount": 2800, "total_winning_amount": 500000},
            7: {"stake_amount": 3300, "total_winning_amount": 750000},
            8: {"stake_amount": 3700, "total_winning_amount": 900000},
            9: {"stake_amount": 4200, "total_winning_amount": 1250000},
            10: {"stake_amount": 5800, "total_winning_amount": 1500000},
        }

        if ticket_count is not None:
            return prices.get(ticket_count)
        else:
            return prices

    @staticmethod
    def instant_stake_amount_pontential_winning(ticket_count) -> dict:
        amount = 150
        if ticket_count == 1:
            return {"stake_amount": amount, "total_winning_amount": 11250}
        elif ticket_count == 2:
            return {"stake_amount": amount * 2, "total_winning_amount": 13500}

        elif ticket_count == 3:
            return {"stake_amount": amount * 3, "total_winning_amount": 15750}

        elif ticket_count == 4:
            return {"stake_amount": amount * 4, "total_winning_amount": 18000}

        elif ticket_count == 5:
            return {"stake_amount": amount * 5, "total_winning_amount": 18750}

        elif ticket_count == 6:
            return {"stake_amount": amount * 6, "total_winning_amount": 22500}

        elif ticket_count == 7:
            return {"stake_amount": 1000, "total_winning_amount": 25000}

    @staticmethod
    def instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
        ticket_count=None,
    ) -> dict:
        # amount = 200
        prices = {
            1: {"stake_amount": 200, "total_winning_amount": 1000},
            2: {"stake_amount": 500, "total_winning_amount": 1666.67},
            3: {"stake_amount": 750, "total_winning_amount": 2000},
            4: {"stake_amount": 800, "total_winning_amount": 2400},
            5: {"stake_amount": 1250, "total_winning_amount": 2666.67},
            6: {"stake_amount": 1300, "total_winning_amount": 3000},
            7: {"stake_amount": 1400, "total_winning_amount": 3333.33},
        }

        if ticket_count is not None:
            return prices.get(ticket_count)
        return prices

    @staticmethod
    def salary_for_life_win_per_line(match, number_of_line, prices=False):
        """
        pontential winnings

        1 line, 4 matches = 5000
        1 line, 3 matches = 3000

        2 line, 4 matches = 7500
        2 line, 3 matches = 4500

        3 line, 4 matches = 16666
        3 line, 3 matches = 10000

        4 line, 4 matches = 37500
        4 line, 3 matches = 22500

        5 line, 4 matches = 50000
        5 line, 3 matches = 30000

        6 line, 4 matches = 83333
        6 line, 3 matches = 50000

        7 line, 4 matches = 108142
        7 line, 3 matches = 64285

        8 line, 4 matches = 125000
        8 line, 3 matches = 67500

        9 line, 4 matches = 138888
        9 line, 3 matches = 83333

        10 line, 4 matches = 150000
        10 line, 3 matches = 90000

        """

        # prices = {
        #     1: 5000.00 * factor,
        #     2: 15000.00 * factor,
        #     3: 50000.00 * factor,
        #     4: 150000.00 * factor,
        #     5: 250000.00 * factor,
        #     6: 500000.00 * factor,
        #     7: 750000.00 * factor,
        #     8: 900000.00 * factor,
        #     9: 1250000.00 * factor,
        #     10: 1500000.00 * factor,
        # }

        line_values = {4: 1, 3: 0.6, 2: 0.3}

        if prices:
            return prices[number_of_line] * line_values.get(match, 0)

        else:
            if number_of_line == 1:
                if match == 4:
                    return 5000
                elif match == 3:
                    return 3000
                elif match == 2:
                    return 1500
                elif match == 5:  # TEMP
                    return 15000

            elif number_of_line == 2:
                if match == 4:
                    return 7500
                elif match == 3:
                    return 4500
                elif match == 2:
                    return 2250

            elif number_of_line == 3:
                if match == 4:
                    return 16666
                elif match == 3:
                    return 10000
                elif match == 2:
                    return 4999

            elif number_of_line == 4:
                if match == 4:
                    return 37500
                elif match == 3:
                    return 22500
                elif match == 2:
                    return 11250

            elif number_of_line == 5:
                if match == 4:
                    return 50000
                elif match == 3:
                    return 30000

                elif match == 2:
                    return 15000

            elif number_of_line == 6:
                if match == 4:
                    return 83333
                elif match == 3:
                    return 50000

                elif match == 2:
                    return 25000

            elif number_of_line == 7:
                if match == 4:
                    return 108142
                elif match == 3:
                    return 64285

                elif match == 2:
                    return 32442

            elif number_of_line == 8:
                if match == 4:
                    return 125000
                elif match == 3:
                    return 67500

                elif match == 2:
                    return 33750

            elif number_of_line == 9:
                if match == 4:
                    return 138888
                elif match == 3:
                    return 83333
                elif match == 2:
                    return 41666

            elif number_of_line == 10:
                if match == 4:
                    return 150000
                elif match == 3:
                    return 90000
                elif match == 2:
                    return 45000

            else:
                return 0

            return 0

    @staticmethod
    def salary_for_life_draw_original(re_run=False, new_rtp=0, batch_id=0, recursion_depth=0):
        return
        from decisioning_engine.draw import SalaryForLifeDraw
        from wyse_ussd.tasks import (
            celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table,
            salary_for_life_and_instant_cashout_won_sms_on_telco,
        )

        """
        This function is used to draw the salary for life daily consolation prize
        """
        # get latest batch for salary for life
        # batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active = True).last()
        # print("STARTING DRAW")
        const_obj = ConstantVariable.objects.all().last()

        # ==========================================
        # ==========================================
        MAX_RECURSION_DEPTH = 5
        recursion_depth += 1

        if recursion_depth >= MAX_RECURSION_DEPTH:
            print("RECURSION DEPTH ::", recursion_depth)
            return
        # ==========================================
        # ==========================================

        if const_obj.merge_pos_draw_s4l is True:
            pass
        else:
            return

        rtp_percentage = 0

        if re_run is True:
            # print("recursive function called")
            batch = LotteryBatch.objects.filter(id=batch_id).last()
            batch_db_id = batch.id
        else:
            batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active=True).last()
            batch_db_id = batch.id

        # print("batch_db_id before update", batch_db_id)

        # close batch
        if re_run is True:
            pass
        else:
            batch.is_active = False
            batch.draw_date = datetime.now()
            batch.save()

        # get gloabal salary for life jackpot
        salary_for_life_jackpot = LotteryGlobalJackPot.get_jackpot_instance()

        try:
            LotteryBatch.objects.create(
                lottery_type="SALARY_FOR_LIFE",
                global_jackpot=salary_for_life_jackpot,
            )
        except Exception:
            pass

        match_batch = LotteryBatch.objects.filter(id=batch_db_id, lottery_type="SALARY_FOR_LIFE").last()
        # print("match_batch", match_batch)
        # print("match_batch", match_batch.id)
        # lottery qs to be drawn
        if re_run is True:
            lottery_ids = [
                (
                    i.id
                    if not LottoWinners.objects.filter(
                        batch=match_batch, phone_number=i.user_profile.phone_number
                    ).exists()
                    else 0
                )
                for i in LottoTicket.objects.filter(batch=match_batch)
            ]
            lottery_ids = [i for i in lottery_ids if i != 0]

            lottery_qs = LottoTicket.objects.filter(batch=match_batch, id__in=lottery_ids, paid=True)
        else:
            lottery_qs = LottoTicket.objects.filter(batch=match_batch, paid=True)

        print("plays count", lottery_qs.count())

        plays = list(
            map(
                lambda x: (x.number_of_ticket, [int(i) for i in x.ticket.split(",")]),
                lottery_qs,
            )
        )

        if re_run is True:
            rtp = new_rtp
        else:
            rtp = lottery_qs.distinct().aggregate(models.Sum("rtp"))["rtp__sum"]

            # get running balance and add it to rtp

            if const_obj:
                if rtp:
                    rtp += const_obj.salary_4_life_running_balance

                else:
                    rtp = const_obj.salary_4_life_running_balance

        print("rtp", rtp, "\n\n\n\n\n")

        """S4L --> Salary For Life"""
        s4l_win_factor_constant = const_obj.s4l_win_factor_constant or "100,100"
        s4lmin, s4lmax = s4l_win_factor_constant.split(",")
        factor = int(random.choice(range(int(s4lmin), int(s4lmax) + 1))) / 100

        print("FACTOR ::", factor)

        # original_prices = {
        #     1: 5000.00,
        #     2: 15000.00,
        #     3: 50000.00,
        #     4: 150000.00,
        #     5: 250000.00,
        #     6: 500000.00,
        #     7: 750000.00,
        #     8: 900000.00,
        #     9: 1250000.00,
        #     10: 1500000.00,
        # }  # noqa
        prices = {
            1: 5000.00 / 1 * factor,
            2: 15000.00 / 2 * factor,
            3: 50000.00 / 3 * factor,
            4: 150000.00 / 4 * factor,
            5: 250000.00 / 5 * factor,
            6: 500000.00 / 6 * factor,
            7: 750000.00 / 7 * factor,
            8: 900000.00 / 8 * factor,
            9: 1250000.00 / 9 * factor,
            10: 1500000.00 / 10 * factor,
        }

        # pprint.pprint(prices)

        salary_for_life_jackpot_instance = LotteryGlobalJackPot.objects.filter(is_active=True).last()

        if salary_for_life_jackpot_instance is not None:
            jackpot_amount = salary_for_life_jackpot_instance.threshold
        else:
            jackpot_amount = 5000000.00

        # print("plays  ", plays, "\n")
        # print("rtp  ", rtp, "\n")
        # print("prices  ", prices, "\n")
        # print("jackpot_amount  ", jackpot_amount, "\n")

        # draw_response = draw(plays, rtp, prices, jackpot_amount)
        # print("rtp first run", rtp)
        # print("batch_db_id", batch_db_id, "\n\n\n\n")

        print("STARTING DRAW")
        draw_response = SalaryForLifeDraw.draw(plays, rtp, prices, jackpot_amount)

        # best_match, best_match_combo, return_valu1, return_val2, return_val3, retrun_val3 = draw_response.values()

        (
            best_match,
            best_match_combo,
            best_match_with_jkpt,
            best_match_with_jkpt_combo,
            best_match_witho_jkpt,
            best_match_witho_jkpt_combo,
        ) = draw_response.values()

        # print("best_match", best_match, "\n\n")
        # print("best_match_combo", best_match_combo, "\n\n")
        # print("best_match_with_jkpt", best_match_with_jkpt, "\n\n")
        # print("best_match_with_jkpt_combo", best_match_with_jkpt_combo, "\n\n")
        # print("best_match_witho_jkpt", best_match_witho_jkpt, "\n\n")
        # print("best_match_witho_jkpt_combo", best_match_witho_jkpt_combo, "\n\n")

        # print(
        #     "best_match_combo",
        #     best_match_combo,
        # )

        # print("best_match", best_match, ":::::::::::")
        filterd_winners = SalaryForLifeDraw.filter_winnings(best_match_combo, plays, prices, jackpot_amount)

        DrawData.objects.create(
            game_type="SALARY_FOR_LIFE",
            factor1=prices,
            factor2={
                "RTP": rtp,
                "running_balance": const_obj.salary_4_life_running_balance,
                "actual_rtp": lottery_qs.distinct().aggregate(models.Sum("rtp"))["rtp__sum"],
                "jackpot_amount": jackpot_amount,
            },
            factor3=draw_response,
            factor4=dict(factor=factor, filterd_winners=filterd_winners),
        )

        # save result in batch model
        # draw batch
        _draw_batch_id = generate_game_play_id()
        draw_batch_id = {
            _draw_batch_id: filterd_winners,
            "match_combo": best_match_combo,
        }
        # match_batch.draw_batch_id = match_batch.lottery_winner_ticket + f""
        old_winning_ticket_record = (
            match_batch.lottery_winner_ticket if match_batch.lottery_winner_ticket is not None else ""
        )
        match_batch.lottery_winner_ticket = f"{old_winning_ticket_record}, {draw_batch_id}"

        old_super_combo = (
            match_batch.lottery_winner_ticket_number if match_batch.lottery_winner_ticket_number is not None else ""
        )
        match_batch.lottery_winner_ticket_number = f"{old_super_combo}, {best_match_combo}"
        match_batch.save()

        rtp_percentage = 0
        if filterd_winners:
            for ticker_won in filterd_winners:
                match_win_type = ticker_won[0]
                match_ticket = ticker_won[1]
                amount_won = ticker_won[2]

                # get ticket
                # sample match data = (10, [11, 2, 40, 8, 7])
                ticket = list(match_ticket)[1]  # output = [11, 2, 40, 8, 7]

                # filter db to check who has this ticket in this batch
                ticket_db_filter_qs = LottoTicket.objects.filter(batch=match_batch, ticket=serialize_ticket(ticket))

                # win type. this block will decided if the win type is PERM_4 or PERM_3
                win_type = "PERM_4"

                if match_win_type == 4:
                    win_type = "PERM_4"
                elif match_win_type == 3:
                    win_type = "PERM_3"
                elif match_win_type == 2:
                    win_type = "PERM_2"

                amount_won = LottoTicket.salary_for_life_win_per_line(match_win_type, list(match_ticket)[0], prices)
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n\n")
                print(match_win_type, list(match_ticket)[0])
                print("RETURNED :: ", amount_won)
                print("\n\n@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")

                # get
                creation_data = []
                if ticket_db_filter_qs:
                    for lotto_ticket_instance in ticket_db_filter_qs:
                        if LottoWinners.objects.filter(
                            phone_number=lotto_ticket_instance.user_profile.phone_number,
                            game_play_id=lotto_ticket_instance.game_play_id,
                            lottery=lotto_ticket_instance,
                            channel_played_from=lotto_ticket_instance.channel,
                        ).exists():
                            creation_data.append(("PASSED", ticker_won))
                            continue

                        amount_won = LottoTicket.salary_for_life_win_per_line(
                            match_win_type,
                            lotto_ticket_instance.number_of_ticket,
                            prices,
                        )

                        if LottoWinners.objects.filter(
                            game_play_id__iexact=lotto_ticket_instance.game_play_id
                        ).exists():
                            pass
                        else:
                            LottoWinners.create_lotto_winner_obj(
                                lottery=lotto_ticket_instance,
                                batch=match_batch,
                                phone_number=lotto_ticket_instance.user_profile.phone_number,
                                ticket=ticket,
                                win_type="ORDINARY_WINNER",
                                match_type=win_type,
                                lotto_type="SALARY_FOR_LIFE",
                                game_play_id=lotto_ticket_instance.game_play_id,
                                stake_amount=lotto_ticket_instance.stake_amount,
                                earning=amount_won,
                                channel_played_from=lotto_ticket_instance.channel,
                                run_batch_id=_draw_batch_id,
                            )

                            if amount_won > 0:
                                if lotto_ticket_instance.played_via_telco_channel is False:
                                    celery_sms_for_instant_cash_winners.delay(
                                        phone_number=lotto_ticket_instance.user_profile.phone_number,
                                        amount_Won=amount_won,
                                        lottery_type="SALARY_FOR_LIFE",
                                        game_play_id=lotto_ticket_instance.game_play_id,
                                    )
                                else:
                                    salary_for_life_and_instant_cashout_won_sms_on_telco(
                                        phone=lotto_ticket_instance.user_profile.phone_number,
                                        ticket_num=lotto_ticket_instance.ticket,
                                        amount=amount_won,
                                        game_play_id=lotto_ticket_instance.game_play_id,
                                        lottery_type="SALARY FOR LIFE",
                                    )

                            # add event to engage
                            winner_engange_event.delay(
                                user_id=lotto_ticket_instance.user_profile.id,
                                event_name="SALARY FOR LIFE GAME WINNER",
                                is_user_profile_id=True,
                            )
            DrawData.objects.create(
                game_type="S4L CREATION DETAIL",
                factor1=prices,
                factor2={},
                factor3=draw_response,
                factor4=creation_data,
            )

            # check the percent of payout, minus it from rtp. if there's any amount left
            # check the low limit threshold. if the amount is less than the threshold, we leave
            # else. this function will recall itself with the new rtp

            rtp_percentage = rtp * (int(best_match) / 100)

            # print("recursive function ", rtp - rtp_percentage)
            # print("rtp_percentage", rtp_percentage, "\n\n")

            # if (rtp - rtp_percentage) > 10000:
            #     new_rtp = rtp - rtp_percentage
            #     # print("e-running", "\n\n\n\n")
            #     # print("new_rtp", new_rtp)
            #     # print("batch_id", match_batch.id)
            #     LottoTicket.salary_for_life_draw_original(
            #         re_run=True, new_rtp=new_rtp, batch_id=batch_db_id
            #     )

            # else:
            #     # send sms to winner

            #     # send sms to None winners
            #     celery_sms_for_s4lgame_lost.delay(batch_db_id)

            # salary for life running balance
            const_obj = ConstantVariable.objects.all().last()

            if const_obj:
                if const_obj.salary_4_life_running_balance >= (rtp - rtp_percentage):
                    const_obj.salary_4_life_running_balance = rtp - rtp_percentage
                    # const_obj.save()

                    const_obj = ConstantVariable.objects.all().update(
                        salary_4_life_running_balance=const_obj.salary_4_life_running_balance
                    )

                    celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table.delay()

            else:
                ConstantVariable.objects.create(
                    game_threshold=1,
                    salary_4_life_running_balance=(rtp - rtp_percentage),
                )
                const_obj.salary_4_life_running_balance = rtp - rtp_percentage
                const_obj.save()

        else:
            const_obj = ConstantVariable.objects.all().last()
            if const_obj:
                if True:
                    # const_obj.salary_4_life_running_balance = rtp
                    # const_obj.save()
                    const_obj = ConstantVariable.objects.all().update(salary_4_life_running_balance=rtp)

                    celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table.delay()

            else:
                ConstantVariable.objects.create(
                    game_threshold=1,
                    salary_4_life_running_balance=(rtp),
                )

                if True:  # (const_obj.salary_4_life_running_balance >= rtp) and re_run is False:
                    const_obj.salary_4_life_running_balance = rtp
                    const_obj = ConstantVariable.objects.all().update(salary_4_life_running_balance=rtp)
                    # const_obj.save()

        DrawData.objects.create(
            game_type="SALARY_FOR_LIFE",
            factor1=0,
            factor2={
                "RTP": rtp,
                "running_balance": rtp - rtp_percentage,
                "actual_rtp": 0,
                "jackpot_amount": 0,
            },
            factor3=0,
            factor4=0,
        )

        # notify lotto agent on batch draw
        notify_agents_on_lottery_batch_draw(batch_id=batch_db_id, lottery_type="SALARY_FOR_LIFE")

        # =========================================================
        # =========================================================
        const_obj = ConstantVariable.objects.all().last()
        print("RUNNING BALANCE ::", const_obj.salary_4_life_running_balance)
        if (const_obj.salary_4_life_running_balance) > 10000:
            new_rtp = const_obj.salary_4_life_running_balance
            LottoTicket.salary_for_life_draw_original(
                re_run=True,
                new_rtp=new_rtp,
                batch_id=batch_db_id,
                recursion_depth=recursion_depth,
            )
        else:
            # send sms to winner
            # send sms to None winners
            celery_sms_for_s4lgame_lost.delay(batch_db_id)
        # =========================================================
        # =========================================================

        return filterd_winners

    @staticmethod
    def salary_for_life_draw_original_multiprocessing(
        re_run=False, new_rtp=0, batch_id=0, recursion_depth=0, include_telco_tickets=True
    ):

        from decisioning_engine.draw import SalaryForLifeDraw
        from wyse_ussd.models import PendingAsyncTask
        from wyse_ussd.tasks import (
            celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table,
        )

        DrawLog.create_draw_log(frequency_minutes=120, draw_name="SALARY_FOR_LIFE")
        """
        This function is used to draw the salary for life daily consolation prize
        """

        # mark all tickets on game play escrow table as paid
        escrow_qs = GamePlayEscrow.objects.filter(
            batch_uuid=batch_id,
            game_type="SALARY_FOR_LIFE",
        )

        if escrow_qs.exists():
            escrow_game_plays = list(escrow_qs.values_list("game_play_id", flat=True))

            try:
                LottoTicket.objects.filter(game_play_id__in=escrow_game_plays).update(paid=True)
            except:
                pass

        const_obj = ConstantVariable.objects.all().last()
        print("STARTING DRAW")
        print(f"Include telco tickets: {include_telco_tickets}")

        # ==========================================
        # ==========================================
        MAX_RECURSION_DEPTH = 5
        recursion_depth += 1

        if recursion_depth >= MAX_RECURSION_DEPTH:
            print("RECURSION DEPTH ::", recursion_depth)
            return
        # ==========================================
        # ===============================``===========

        if const_obj.merge_pos_draw_s4l is True:
            pass
        else:
            return

        rtp_percentage = 0

        if batch_id == 0:
            batches = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active=True)
            batch = batches.last()
            batches.update(is_active=False, draw_date=datetime.now())

            batch_db_id = batch.id

        elif batch_id:
            batch = LotteryBatch.objects.filter(id=batch_id).last()
            batch_db_id = batch.id

        # get gloabal salary for life jackpot
        salary_for_life_jackpot = LotteryGlobalJackPot.get_jackpot_instance()

        try:
            LotteryBatch.objects.create(
                lottery_type="SALARY_FOR_LIFE",
                global_jackpot=salary_for_life_jackpot,
                is_active=True,
            )
        except Exception:
            pass

        match_batch = LotteryBatch.objects.filter(id=batch_db_id, lottery_type="SALARY_FOR_LIFE").last()

        # Apply telco ticket filter if needed
        if not include_telco_tickets:
            lottery_qs = LottoTicket.objects.filter(batch=match_batch, paid=True, played_via_telco_channel=False)
        else:
            lottery_qs = LottoTicket.objects.filter(batch=match_batch, paid=True)

        print("plays count", lottery_qs.count())

        plays = list(
            map(
                lambda x: (x.number_of_ticket, [int(i) for i in x.ticket.split(",")]),
                lottery_qs,
            )
        )

        if re_run is True:
            rtp = 0
        else:
            rtp = lottery_qs.distinct().aggregate(models.Sum("rtp"))["rtp__sum"]

            # get running balance and add it to rtp

            if const_obj:
                if rtp:
                    running_balance = const_obj.salary_4_life_running_balance
                    rtp += running_balance

                else:
                    running_balance = const_obj.salary_4_life_running_balance
                    rtp = running_balance

            first_draw_time = time(9, 0)

            _timezone = pytz.timezone(settings.TIME_ZONE)
            current_datetime = datetime.now(tz=_timezone)
            current_time = current_datetime.time()

            current_minutes = current_time.hour * 60 + current_time.minute
            first_draw_minutes = first_draw_time.hour * 60 + first_draw_time.minute

            time_difference = current_minutes - first_draw_minutes

            if 0 <= time_difference <= 2:
                try:
                    GamesDailyActivities.update_running_balance(
                        game_type="SALARY_FOR_LIFE", running_balance=running_balance
                    )
                except:
                    pass

        print("rtp", rtp, "\n\n\n\n\n")

        """S4L --> Salary For Life"""
        s4l_win_factor_constant = const_obj.s4l_win_factor_constant or "100,100"
        s4lmin, s4lmax = s4l_win_factor_constant.split(",")
        factor = int(random.choice(range(int(s4lmin), int(s4lmax) + 1))) / 100

        print("FACTOR ::", factor)

        # original_prices = {
        #     1: 5000.00,
        #     2: 15000.00,
        #     3: 50000.00,
        #     4: 150000.00,
        #     5: 250000.00,
        #     6: 500000.00,
        #     7: 750000.00,
        #     8: 900000.00,
        #     9: 1250000.00,
        #     10: 1500000.00,
        # }
        prices = {
            1: 5000.00 / 1 * factor,
            2: 15000.00 / 2 * factor,
            3: 50000.00 / 3 * factor,
            4: 150000.00 / 4 * factor,
            5: 250000.00 / 5 * factor,
            6: 500000.00 / 6 * factor,
            7: 750000.00 / 7 * factor,
            8: 900000.00 / 8 * factor,
            9: 1250000.00 / 9 * factor,
            10: 1500000.00 / 10 * factor,
        }

        # pprint.pprint(prices)

        salary_for_life_jackpot_instance = LotteryGlobalJackPot.objects.filter(is_active=True).last()

        if salary_for_life_jackpot_instance is not None:
            jackpot_amount = salary_for_life_jackpot_instance.threshold
        else:
            jackpot_amount = 5000000.00

        print("STARTING DRAW")
        droplet_ip = "localhost"

        url = f"http://{droplet_ip}:3000/draw"
        payload = json.dumps({"plays": plays, "rtp": rtp, "jackpot_amount": *********})

        headers = {"Content-Type": "application/json"}

        draw_response = requests.request("POST", url, headers=headers, data=payload)

        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        print(draw_response.text)
        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        draw_response = draw_response.json()

        best_match, best_match_combo, return_valu1, return_val2, return_val3, retrun_val3 = draw_response.values()

        (
            best_match,
            best_match_combo,
            best_match_with_jkpt,
            best_match_with_jkpt_combo,
            best_match_witho_jkpt,
            best_match_witho_jkpt_combo,
        ) = draw_response.values()

        filterd_winners = SalaryForLifeDraw.filter_winnings(best_match_combo, plays, prices, jackpot_amount)

        winning_sum = 0

        for i in filterd_winners:
            winning_sum += i[2]

        const_obj.refresh_from_db()
        DrawData.objects.create(
            game_type="SALARY_FOR_LIFE",
            factor1=prices,
            factor2={
                "RTP": rtp,
                "running_balance": const_obj.salary_4_life_running_balance,
                "actual_rtp": lottery_qs.distinct().aggregate(models.Sum("rtp"))["rtp__sum"],
                "jackpot_amount": jackpot_amount,
                "Total Winnings": winning_sum,
            },
            factor3=draw_response,
            factor4=dict(factor=factor, filterd_winners=filterd_winners),
        )

        # print("CREATING DRAW DATA")

        # # save result in batch model
        # # draw batch
        _draw_batch_id = generate_game_play_id()
        draw_batch_id = {
            _draw_batch_id: filterd_winners,
            "match_combo": best_match_combo,
        }
        # match_batch.draw_batch_id = match_batch.lottery_winner_ticket + f""
        old_winning_ticket_record = (
            match_batch.lottery_winner_ticket if match_batch.lottery_winner_ticket is not None else ""
        )
        match_batch.lottery_winner_ticket = f"{old_winning_ticket_record}, {draw_batch_id}"

        old_super_combo = (
            match_batch.lottery_winner_ticket_number if match_batch.lottery_winner_ticket_number is not None else ""
        )
        match_batch.lottery_winner_ticket_number = f"{old_super_combo}, {best_match_combo}"
        match_batch.save()

        # if batch_id != 0:
        #     return

        rtp_percentage = 0
        rtp - winning_sum
        if filterd_winners:
            for ticker_won in filterd_winners:
                match_win_type = ticker_won[0]
                match_ticket = ticker_won[1]
                amount_won = ticker_won[2]

                # get ticket
                # sample match data = (10, [11, 2, 40, 8, 7])
                ticket = list(match_ticket)[1]  # output = [11, 2, 40, 8, 7]

                # filter db to check who has this ticket in this batch
                ticket_db_filter_qs = LottoTicket.objects.filter(batch=match_batch, ticket=serialize_ticket(ticket))

                # win type. this block will decided if the win type is PERM_4 or PERM_3
                win_type = "PERM_4"

                if match_win_type == 4:
                    win_type = "PERM_4"
                elif match_win_type == 3:
                    win_type = "PERM_3"
                elif match_win_type == 2:
                    win_type = "PERM_2"

                amount_won = LottoTicket.salary_for_life_win_per_line(match_win_type, list(match_ticket)[0], prices)
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n\n")
                print(match_win_type, list(match_ticket)[0])
                print("RETURNED :: ", amount_won)
                print("\n\n@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")

                # get
                creation_data = []
                if ticket_db_filter_qs:
                    for lotto_ticket_instance in ticket_db_filter_qs:
                        if LottoWinners.objects.filter(
                            phone_number=lotto_ticket_instance.user_profile.phone_number,
                            game_play_id=lotto_ticket_instance.game_play_id,
                            lottery=lotto_ticket_instance,
                            channel_played_from=lotto_ticket_instance.channel,
                        ).exists():
                            creation_data.append(("PASSED", ticker_won))
                            continue

                        amount_won = LottoTicket.salary_for_life_win_per_line(
                            match_win_type,
                            lotto_ticket_instance.number_of_ticket,
                            prices,
                        )

                        LottoWinners.create_lotto_winner_obj(
                            lottery=lotto_ticket_instance,
                            batch=match_batch,
                            phone_number=lotto_ticket_instance.user_profile.phone_number,
                            ticket=ticket,
                            win_type="ORDINARY_WINNER",
                            match_type=win_type,
                            lotto_type="SALARY_FOR_LIFE",
                            game_play_id=lotto_ticket_instance.game_play_id,
                            stake_amount=lotto_ticket_instance.stake_amount,
                            earning=amount_won,
                            channel_played_from=lotto_ticket_instance.channel,
                            run_batch_id=_draw_batch_id,
                            played_via_telco_channel=lotto_ticket_instance.played_via_telco_channel,
                        )

                        if amount_won > 0:
                            if lotto_ticket_instance.played_via_telco_channel is False:
                                celery_sms_for_instant_cash_winners.delay(
                                    phone_number=lotto_ticket_instance.user_profile.phone_number,
                                    amount_Won=amount_won,
                                    lottery_type="SALARY_FOR_LIFE",
                                    game_play_id=lotto_ticket_instance.game_play_id,
                                )
                            else:
                                PendingAsyncTask.objects.create(
                                    phone_number=lotto_ticket_instance.user_profile.phone_number,
                                    ticket=lotto_ticket_instance.ticket,
                                    amount=amount_won,
                                    game_play_id=lotto_ticket_instance.game_play_id,
                                    game_type="SALARY FOR LIFE",
                                    network=lotto_ticket_instance.telco_network,
                                    purpose=PurposeChoices.GAME_WINNING_NOTIFICATION,
                                )

            # check the percent of payout, minus it from rtp. if there's any amount left
            # check the low limit threshold. if the amount is less than the threshold, we leave
            # else. this function will recall itself with the new rtp

            rtp_percentage = rtp * (int(best_match) / 100)

        ConstantVariable.objects.all().update(salary_4_life_running_balance=(rtp - rtp_percentage))
        celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table.delay()

        DrawData.objects.create(
            game_type="SALARY_FOR_LIFE",
            factor1=0,
            factor2={
                "RTP": rtp,
                "running_balance": rtp - rtp_percentage,
                "actual_rtp": 0,
                "jackpot_amount": 0,
            },
            factor3=0,
            factor4=0,
        )

        # notify lotto agent on batch draw
        notify_agents_on_lottery_batch_draw(batch_id=batch_db_id, lottery_type="SALARY_FOR_LIFE")

        PendingAsyncTask.objects.create(
            game_type="SALARY_FOR_LIFE",
            batch_id=batch_db_id,
            purpose=PurposeChoices.SALARY_FOR_LIFE_LOST_TICKET_SMS_NOTIFICATION,
        )

        # =========================================================
        # =========================================================
        # const_obj = ConstantVariable.objects.all().last()
        # print("RUNNING BALANCE ::", const_obj.salary_4_life_running_balance)
        # if (const_obj.salary_4_life_running_balance) > 10000:
        #     new_rtp = const_obj.salary_4_life_running_balance
        #     LottoTicket.salary_for_life_draw_original(
        #         re_run=True,
        #         new_rtp=new_rtp,
        #         batch_id=batch_db_id,
        #         recursion_depth=recursion_depth,
        #     )
        # else:
        #     # send sms to winner
        #     # send sms to None winners
        #     celery_sms_for_s4lgame_lost.delay(batch_db_id)
        # =========================================================
        # =========================================================
        # if not droplet_deleted:
        #     digital_ocean_log.is_killed = False
        #     digital_ocean_log.save()
        #     raise RuntimeError("DROPLET NOT SUCCESSFULLY DELETED.!!!")
        # else:
        #     digital_ocean_log.is_killed = True
        #     digital_ocean_log.date_killed = timezone.now()
        #     digital_ocean_log.save()

        return filterd_winners

    @staticmethod
    def salary_for_life_draw(re_run=False, new_rtp=0, batch_id=0, recursion_depth=0):
        from decisioning_engine.draw import SalaryForLifeDraw
        from wyse_ussd.tasks import (
            celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table,
            salary_for_life_and_instant_cashout_won_sms_on_telco,
        )

        """
        This function is used to draw the salary for life daily consolation prize
        """
        return
        # get latest batch for salary for life
        # batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active = True).last()

        const_obj = ConstantVariable.objects.all().last()

        # ==========================================
        # ==========================================
        MAX_RECURSION_DEPTH = 5
        recursion_depth += 1

        if recursion_depth >= MAX_RECURSION_DEPTH:
            print("RECURSION DEPTH ::", recursion_depth)
            return
        # ==========================================
        # ==========================================

        if const_obj.merge_pos_draw_s4l is False:
            pass
        else:
            return

        rtp_percentage = 0

        if re_run is True:
            # print("recursive function called")
            batch = LotteryBatch.objects.filter(id=batch_id).last()
            batch_db_id = batch.id
        else:
            batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active=True).last()
            batch_db_id = batch.id

        # print("batch_db_id before update", batch_db_id)
        # get global salary for life jackpot
        salary_for_life_jackpot = LotteryGlobalJackPot.get_jackpot_instance()

        # close batch
        if re_run is True:
            pass
        else:
            batch.is_active = False
            batch.draw_date = timezone.now()
            batch.save()

            # CREATE POS BATCH
            pos_batch = LotteryBatch(
                is_active=False,
                is_pos_batch=True,
                is_pos_active=True,
                lottery_type="SALARY_FOR_LIFE",
                global_jackpot=salary_for_life_jackpot,
            )
            pos_batch.save()

            pos_lottery_qs = LottoTicket.objects.filter(batch=batch, channel="POS_AGENT", paid=True)
            pos_lottery_qs.update(batch_id=pos_batch.id)

        try:
            LotteryBatch.objects.create(
                lottery_type="SALARY_FOR_LIFE",
                global_jackpot=salary_for_life_jackpot,
            )
        except Exception:
            pass

        match_batch = LotteryBatch.objects.filter(id=batch_db_id, lottery_type="SALARY_FOR_LIFE").last()
        # print("match_batch", match_batch)
        # print("match_batch", match_batch.id)
        # lottery qs to be drawn
        if re_run is True:
            lottery_ids = [
                (
                    i.id
                    if not LottoWinners.objects.filter(
                        batch=match_batch, phone_number=i.user_profile.phone_number
                    ).exists()
                    else 0
                )
                for i in LottoTicket.objects.filter(batch=match_batch)
            ]
            lottery_ids = [i for i in lottery_ids if i != 0]

            lottery_qs = LottoTicket.objects.filter(batch=match_batch, id__in=lottery_ids, paid=True)
        else:
            lottery_qs = LottoTicket.objects.filter(batch=match_batch, paid=True)

        print("plays count", lottery_qs.count())

        plays = list(
            map(
                lambda x: (x.number_of_ticket, [int(i) for i in x.ticket.split(",")]),
                lottery_qs,
            )
        )

        if re_run is True:
            rtp = new_rtp
        else:
            rtp = lottery_qs.distinct().aggregate(models.Sum("rtp"))["rtp__sum"]

            # get running balance and add it to rtp
            print(f"#Rerun ::: {re_run}")
            if const_obj:
                if rtp:
                    rtp += const_obj.salary_4_life_running_balance

                else:
                    rtp = const_obj.salary_4_life_running_balance

        print("rtp", rtp, "\n\n\n\n\n")

        """S4L --> Salary For Life"""
        s4l_win_factor_constant = const_obj.s4l_win_factor_constant or "100,100"
        s4lmin, s4lmax = s4l_win_factor_constant.split(",")
        factor = int(random.choice(range(int(s4lmin), int(s4lmax) + 1))) / 100

        print("FACTOR ::", factor)

        # original_prices = {
        #     1: 5000.00,
        #     2: 15000.00,
        #     3: 50000.00,
        #     4: 150000.00,
        #     5: 250000.00,
        #     6: 500000.00,
        #     7: 750000.00,
        #     8: 900000.00,
        #     9: 1250000.00,
        #     10: 1500000.00,
        # }
        prices = {
            1: 5000.00 / 1 * factor,
            2: 15000.00 / 2 * factor,
            3: 50000.00 / 3 * factor,
            4: 150000.00 / 4 * factor,
            5: 250000.00 / 5 * factor,
            6: 500000.00 / 6 * factor,
            7: 750000.00 / 7 * factor,
            8: 900000.00 / 8 * factor,
            9: 1250000.00 / 9 * factor,
            10: 1500000.00 / 10 * factor,
        }

        import pprint

        pprint.pprint(prices)

        salary_for_life_jackpot_instance = LotteryGlobalJackPot.objects.filter(is_active=True).last()

        if salary_for_life_jackpot_instance is not None:
            jackpot_amount = salary_for_life_jackpot_instance.threshold
        else:
            jackpot_amount = 5000000.00

        # print("plays  ", plays, "\n")
        # print("rtp  ", rtp, "\n")
        # print("prices  ", prices, "\n")
        # print("jackpot_amount  ", jackpot_amount, "\n")

        # draw_response = draw(plays, rtp, prices, jackpot_amount)
        # print("rtp first run", rtp)
        # print("batch_db_id", batch_db_id, "\n\n\n\n")

        draw_response = SalaryForLifeDraw.draw(plays, rtp, prices, jackpot_amount)

        # best_match, best_match_combo, return_valu1, return_val2, return_val3, retrun_val3 = draw_response.values()

        (
            best_match,
            best_match_combo,
            best_match_with_jkpt,
            best_match_with_jkpt_combo,
            best_match_witho_jkpt,
            best_match_witho_jkpt_combo,
        ) = draw_response.values()

        # print("best_match", best_match, "\n\n")
        # print("best_match_combo", best_match_combo, "\n\n")
        # print("best_match_with_jkpt", best_match_with_jkpt, "\n\n")
        # print("best_match_with_jkpt_combo", best_match_with_jkpt_combo, "\n\n")
        # print("best_match_witho_jkpt", best_match_witho_jkpt, "\n\n")
        # print("best_match_witho_jkpt_combo", best_match_witho_jkpt_combo, "\n\n")

        print(
            "best_match_combo",
            best_match_combo,
        )

        # DrawData.objects.create(
        #     game_type="SALARY_FOR_LIFE",
        #     factor1=prices,
        #     factor2={"RTP": rtp, "jackpot_amount": jackpot_amount},
        #     factor3=draw_response,
        #     factor4=factor,
        # )

        # # print("best_match", best_match, ":::::::::::")
        # filterd_winners = SalaryForLifeDraw.filter_winnings(
        #     best_match_combo, plays, prices, jackpot_amount
        # )

        # print("best_match", best_match, ":::::::::::")
        filterd_winners = SalaryForLifeDraw.filter_winnings(best_match_combo, plays, prices, jackpot_amount)

        DrawData.objects.create(
            game_type="SALARY_FOR_LIFE",
            factor1=prices,
            factor2={
                "RTP": rtp,
                "running_balance": const_obj.salary_4_life_running_balance,
                "actual_rtp": lottery_qs.distinct().aggregate(models.Sum("rtp"))["rtp__sum"],
                "jackpot_amount": jackpot_amount,
            },
            factor3=draw_response,
            factor4=dict(factor=factor, filterd_winners=filterd_winners),
        )

        total_winnings = 0
        for _, __, amount in filterd_winners:  # noqa
            total_winnings += amount

        const_obj.salary_4_life_current_total_winnings = F("salary_4_life_current_total_winnings") + total_winnings
        const_obj.save()

        const_obj.refresh_from_db()

        # save result in batch model
        # draw batch
        _draw_batch_id = generate_game_play_id()
        draw_batch_id = {
            _draw_batch_id: filterd_winners,
            "match_combo": best_match_combo,
        }
        # match_batch.draw_batch_id = match_batch.lottery_winner_ticket + f""
        old_winning_ticket_record = (
            match_batch.lottery_winner_ticket if match_batch.lottery_winner_ticket is not None else ""
        )
        match_batch.lottery_winner_ticket = f"{old_winning_ticket_record}, {draw_batch_id}"

        old_super_combo = (
            match_batch.lottery_winner_ticket_number if match_batch.lottery_winner_ticket_number is not None else ""
        )
        match_batch.lottery_winner_ticket_number = f"{old_super_combo}, {best_match_combo}"
        match_batch.save()

        if filterd_winners:
            for ticker_won in filterd_winners:
                match_win_type = ticker_won[0]
                match_ticket = ticker_won[1]
                amount_won = ticker_won[2]

                # get ticket
                # sample match data = (10, [11, 2, 40, 8, 7])
                ticket = list(match_ticket)[1]  # output = [11, 2, 40, 8, 7]

                # filter db to check who has this ticket in this batch
                ticket_db_filter_qs = LottoTicket.objects.filter(batch=match_batch, ticket=serialize_ticket(ticket))

                # win type. this block will decided if the win type is PERM_4 or PERM_3
                win_type = "PERM_4"

                if match_win_type == 4:
                    win_type = "PERM_4"
                elif match_win_type == 3:
                    win_type = "PERM_3"
                elif match_win_type == 2:
                    win_type = "PERM_2"

                amount_won = LottoTicket.salary_for_life_win_per_line(match_win_type, list(match_ticket)[0], prices)
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n\n")
                print(match_win_type, list(match_ticket)[0])
                print("RETURNED :: ", amount_won)
                print("\n\n@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")

                # get
                creation_data = []
                if ticket_db_filter_qs:
                    for lotto_ticket_instance in ticket_db_filter_qs:
                        if LottoWinners.objects.filter(
                            phone_number=lotto_ticket_instance.user_profile.phone_number,
                            game_play_id=lotto_ticket_instance.game_play_id,
                            lottery=lotto_ticket_instance,
                            channel_played_from=lotto_ticket_instance.channel,
                        ).exists():
                            creation_data.append(("PASSED", ticker_won))
                            continue

                        amount_won = LottoTicket.salary_for_life_win_per_line(
                            match_win_type,
                            lotto_ticket_instance.number_of_ticket,
                            prices,
                        )

                        if LottoWinners.objects.filter(
                            game_play_id__iexact=lotto_ticket_instance.game_play_id
                        ).exists():
                            pass
                        else:
                            LottoWinners.create_lotto_winner_obj(
                                lottery=lotto_ticket_instance,
                                batch=match_batch,
                                phone_number=lotto_ticket_instance.user_profile.phone_number,
                                ticket=ticket,
                                win_type="ORDINARY_WINNER",
                                match_type=win_type,
                                lotto_type="SALARY_FOR_LIFE",
                                game_play_id=lotto_ticket_instance.game_play_id,
                                stake_amount=lotto_ticket_instance.stake_amount,
                                earning=amount_won,
                                channel_played_from=lotto_ticket_instance.channel,
                                run_batch_id=_draw_batch_id,
                            )

                            if lotto_ticket_instance.channel != "POS_AGENT":
                                # send sms to winner
                                if lotto_ticket_instance.played_via_telco_channel is False:
                                    celery_sms_for_instant_cash_winners.delay(
                                        phone_number=lotto_ticket_instance.user_profile.phone_number,
                                        amount_Won=amount_won,
                                        lottery_type="SALARY_FOR_LIFE",
                                        game_play_id=lotto_ticket_instance.game_play_id,
                                    )

                                else:
                                    salary_for_life_and_instant_cashout_won_sms_on_telco.delay(
                                        phone=lotto_ticket_instance.user_profile.phone_number,
                                        ticket_num=lotto_ticket_instance.ticket,
                                        amount=amount_won,
                                        game_play_id=lotto_ticket_instance.game_play_id,
                                        lottery_type="SALARY FOR LIFE",
                                    )

                            # add event to engage
                            winner_engange_event.delay(
                                user_id=lotto_ticket_instance.user_profile.id,
                                event_name="SALARY FOR LIFE GAME WINNER",
                                is_user_profile_id=True,
                            )

            DrawData.objects.create(
                game_type="S4L CREATION DETAIL",
                factor1=prices,
                factor2={},
                factor3=draw_response,
                factor4=creation_data,
            )

            # check the percent of payout, minus it from rtp. if there's any amount left
            # check the low limit threshold. if the amount is less than the threshold, we leave
            # else. this function will recall itself with the new rtp

            rtp_percentage = rtp * (int(best_match) / 100)

            # print("recursive function ", rtp - rtp_percentage)
            # print("rtp_percentage", rtp_percentage, "\n\n")

            # if (rtp - rtp_percentage) > 10000:
            #     new_rtp = rtp - rtp_percentage
            #     # print("e-running", "\n\n\n\n")
            #     # print("new_rtp", new_rtp)
            #     # print("batch_id", match_batch.id)
            #     LottoTicket.salary_for_life_draw(
            #         re_run=True, new_rtp=new_rtp, batch_id=batch_db_id
            #     )

            # else:
            #     # send sms to winner

            #     # send sms to None winners
            #     celery_sms_for_s4lgame_lost.delay(batch_db_id)

            # salary for life running balance
            const_obj = ConstantVariable.objects.all().last()

            if const_obj:
                if True:
                    const_obj.salary_4_life_running_balance = rtp - rtp_percentage
                    const_obj.save()

                    celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table.delay()

            else:
                ConstantVariable.objects.create(
                    game_threshold=1,
                    salary_4_life_running_balance=(rtp - rtp_percentage),
                )
                const_obj.salary_4_life_running_balance = rtp - rtp_percentage
                const_obj.save()

        else:
            const_obj = ConstantVariable.objects.all().last()
            if const_obj:
                if True:
                    const_obj.salary_4_life_running_balance = rtp
                    const_obj.save()

                    celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table.delay()

            else:
                ConstantVariable.objects.create(
                    game_threshold=1,
                    salary_4_life_running_balance=(rtp),
                )

                if True:
                    const_obj.salary_4_life_running_balance = rtp
                    const_obj.save()

        # notify lotto agent on batch draw
        notify_agents_on_lottery_batch_draw(batch_id=batch_db_id, lottery_type="SALARY_FOR_LIFE")

        # =========================================================
        # =========================================================
        print("RUNNING BALANCE ::", const_obj.salary_4_life_running_balance)
        print((const_obj.salary_4_life_running_balance) > 10000)
        if (const_obj.salary_4_life_running_balance) > 10000:
            new_rtp = const_obj.salary_4_life_running_balance
            LottoTicket.salary_for_life_draw(
                re_run=True,
                new_rtp=new_rtp,
                batch_id=batch_db_id,
                recursion_depth=recursion_depth,
            )
        else:
            # send sms to winner
            # send sms to None winners
            celery_sms_for_s4lgame_lost.delay(batch_db_id)
        # =========================================================
        # =========================================================

        return filterd_winners

    @staticmethod
    def salary_for_life_draw_pos(re_run=False, new_rtp=0, batch_id=0, recursion_depth=0):
        return

        """
        This function is used to draw the salary for life daily consolation prize
        """
        # get latest batch for salary for life
        # batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active = True).last()

        const_obj = ConstantVariable.objects.all().last()

        # ==========================================
        # ==========================================
        MAX_RECURSION_DEPTH = 5
        recursion_depth += 1

        if recursion_depth >= MAX_RECURSION_DEPTH:
            print("RECURSION DEPTH ::", recursion_depth)
            return
        # ==========================================
        # ==========================================

        if const_obj.merge_pos_draw_s4l is False:
            pass
        else:
            return

        rtp_percentage = 0

        if re_run is True:
            # print("recursive function called")
            batch = LotteryBatch.objects.filter(id=batch_id).last()
            batch_db_id = batch_id
        else:
            batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_pos_active=True).last()
            batch_db_id = batch.id
            print(batch.batch_uuid)

        # print("batch_db_id before update", batch_db_id)
        # get global salary for life jackpot
        LotteryGlobalJackPot.get_jackpot_instance()

        # close batch
        if re_run is True:
            pass
        else:
            batch.is_pos_active = False
            batch.draw_date = datetime.now()
            batch.save()

        # try:
        #     LotteryBatch.objects.create(
        #         lottery_type="SALARY_FOR_LIFE",
        #         global_jackpot=salary_for_life_jackpot,
        #     )

        # except Exception:
        #     pass

        match_batch = LotteryBatch.objects.filter(id=batch_db_id, lottery_type="SALARY_FOR_LIFE").last()
        # print("match_batch", match_batch)
        # print("match_batch", match_batch.id)
        # lottery qs to be drawn
        if re_run is True:
            lottery_ids = [
                (
                    i.id
                    if not LottoWinners.objects.filter(
                        batch=match_batch, phone_number=i.user_profile.phone_number
                    ).exists()
                    else 0
                )
                for i in LottoTicket.objects.filter(batch=match_batch)
            ]
            lottery_ids = [i for i in lottery_ids if i != 0]

            lottery_qs = LottoTicket.objects.filter(batch=match_batch, id__in=lottery_ids, paid=True)
        else:
            lottery_qs = LottoTicket.objects.filter(batch=match_batch, paid=True)
            print(match_batch.batch_uuid)

        print("plays count", lottery_qs.count())

        plays = list(
            map(
                lambda x: (x.number_of_ticket, [int(i) for i in x.ticket.split(",")]),
                lottery_qs,
            )
        )

        if re_run is True:
            rtp = new_rtp
        else:
            rtp = lottery_qs.distinct().aggregate(models.Sum("rtp"))["rtp__sum"]

            # get running balance and add it to rtp
            # print(f"#Rerun ::: {re_run}")
            if const_obj:
                if rtp:
                    rtp += const_obj.salary_4_life_running_balance

                else:
                    rtp = const_obj.salary_4_life_running_balance

        # print("rtp", rtp, "\n\n\n\n\n")

        """S4L --> Salary For Life"""
        s4l_win_factor_constant = const_obj.s4l_win_factor_constant or "100,100"
        s4lmin, s4lmax = s4l_win_factor_constant.split(",")
        factor = int(random.choice(range(int(s4lmin), int(s4lmax) + 1))) / 100

        # print("FACTOR ::", factor)

        # original_prices = {
        #     1: 5000.00,
        #     2: 15000.00,
        #     3: 50000.00,
        #     4: 150000.00,
        #     5: 250000.00,
        #     6: 500000.00,
        #     7: 750000.00,
        #     8: 900000.00,
        #     9: 1250000.00,
        #     10: 1500000.00,
        # }
        prices = {
            1: 5000.00 / 1 * factor,
            2: 15000.00 / 2 * factor,
            3: 50000.00 / 3 * factor,
            4: 150000.00 / 4 * factor,
            5: 250000.00 / 5 * factor,
            6: 500000.00 / 6 * factor,
            7: 750000.00 / 7 * factor,
            8: 900000.00 / 8 * factor,
            9: 1250000.00 / 9 * factor,
            10: 1500000.00 / 10 * factor,
        }

        import pprint

        pprint.pprint(prices)

        salary_for_life_jackpot_instance = LotteryGlobalJackPot.objects.filter(is_active=True).last()

        if salary_for_life_jackpot_instance is not None:
            jackpot_amount = salary_for_life_jackpot_instance.threshold
        else:
            jackpot_amount = 5000000.00

        # print("plays  ", plays, "\n")
        # print("rtp  ", rtp, "\n")
        # print("prices  ", prices, "\n")
        # print("jackpot_amount  ", jackpot_amount, "\n")

        # draw_response = draw(plays, rtp, prices, jackpot_amount)
        # print("rtp first run", rtp)
        # print("batch_db_id", batch_db_id, "\n\n\n\n")

        draw_response = SalaryForLifeDraw.draw(plays, rtp, prices, jackpot_amount)  # noqa

        # best_match, best_match_combo, return_valu1, return_val2, return_val3, retrun_val3 = draw_response.values()

        (
            best_match,
            best_match_combo,
            best_match_with_jkpt,
            best_match_with_jkpt_combo,
            best_match_witho_jkpt,
            best_match_witho_jkpt_combo,
        ) = draw_response.values()

        # print("best_match", best_match, "\n\n")
        # print("best_match_combo", best_match_combo, "\n\n")
        # print("best_match_with_jkpt", best_match_with_jkpt, "\n\n")
        # print("best_match_with_jkpt_combo", best_match_with_jkpt_combo, "\n\n")
        # print("best_match_witho_jkpt", best_match_witho_jkpt, "\n\n")
        # print("best_match_witho_jkpt_combo", best_match_witho_jkpt_combo, "\n\n")

        # print(
        #     "best_match_combo",
        #     best_match_combo,
        # )

        # DrawData.objects.create(
        #     game_type="SALARY_FOR_LIFE",
        #     factor1=prices,
        #     factor2={"RTP": rtp, "jackpot_amount": jackpot_amount},
        #     factor3=draw_response,
        #     factor4=factor,
        # )

        # # print("best_match", best_match, ":::::::::::")
        # filterd_winners = SalaryForLifeDraw.filter_winnings(
        #     best_match_combo, plays, prices, jackpot_amount
        # )

        # print("best_match", best_match, ":::::::::::")
        filterd_winners = SalaryForLifeDraw.filter_winnings(best_match_combo, plays, prices, jackpot_amount)  # noqa

        DrawData.objects.create(
            game_type="SALARY_FOR_LIFE_POS",
            factor1=prices,
            factor2={
                "RTP": rtp,
                "running_balance": const_obj.pos_salary_4_life_running_balance,
                "actual_rtp": lottery_qs.distinct().aggregate(models.Sum("rtp"))["rtp__sum"],
                "jackpot_amount": jackpot_amount,
            },
            factor3=draw_response,
            factor4=dict(factor=factor, filterd_winners=filterd_winners),
        )

        # save result in batch model
        # draw batch
        _draw_batch_id = generate_game_play_id()
        draw_batch_id = {
            _draw_batch_id: filterd_winners,
            "match_combo": best_match_combo,
        }
        # match_batch.draw_batch_id = match_batch.lottery_winner_ticket + f""
        old_winning_ticket_record = (
            match_batch.lottery_winner_ticket if match_batch.lottery_winner_ticket is not None else ""
        )
        match_batch.lottery_winner_ticket = f"{old_winning_ticket_record}, {draw_batch_id}"

        old_super_combo = (
            match_batch.lottery_winner_ticket_number if match_batch.lottery_winner_ticket_number is not None else ""
        )
        match_batch.lottery_winner_ticket_number = f"{old_super_combo}, {best_match_combo}"
        match_batch.save()

        if filterd_winners:
            for ticker_won in filterd_winners:
                match_win_type = ticker_won[0]
                match_ticket = ticker_won[1]
                amount_won = ticker_won[2]

                # get ticket
                # sample match data = (10, [11, 2, 40, 8, 7])
                ticket = list(match_ticket)[1]  # output = [11, 2, 40, 8, 7]

                # filter db to check who has this ticket in this batch
                ticket_db_filter_qs = LottoTicket.objects.filter(batch=match_batch, ticket=serialize_ticket(ticket))

                # win type. this block will decided if the win type is PERM_4 or PERM_3
                win_type = "PERM_4"

                if match_win_type == 4:
                    win_type = "PERM_4"
                elif match_win_type == 3:
                    win_type = "PERM_3"
                elif match_win_type == 2:
                    win_type = "PERM_2"

                amount_won = LottoTicket.salary_for_life_win_per_line(match_win_type, list(match_ticket)[0], prices)
                # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n\n")
                # print(match_win_type, list(match_ticket)[0])
                # print("RETURNED :: ", amount_won)
                # print("\n\n@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")

                # get
                creation_data = []
                if ticket_db_filter_qs:
                    for lotto_ticket_instance in ticket_db_filter_qs:
                        if LottoWinners.objects.filter(
                            phone_number=lotto_ticket_instance.user_profile.phone_number,
                            game_play_id=lotto_ticket_instance.game_play_id,
                            lottery=lotto_ticket_instance,
                            channel_played_from=lotto_ticket_instance.channel,
                        ).exists():
                            creation_data.append(("PASSED", ticker_won))
                            continue

                        amount_won = LottoTicket.salary_for_life_win_per_line(
                            match_win_type,
                            lotto_ticket_instance.number_of_ticket,
                            prices,
                        )

                        if LottoWinners.objects.filter(
                            game_play_id__iexact=lotto_ticket_instance.game_play_id
                        ).exists():
                            pass
                        else:
                            LottoWinners.create_lotto_winner_obj(
                                lottery=lotto_ticket_instance,
                                batch=match_batch,
                                phone_number=lotto_ticket_instance.user_profile.phone_number,
                                ticket=ticket,
                                win_type="ORDINARY_WINNER",
                                match_type=win_type,
                                lotto_type="SALARY_FOR_LIFE",
                                game_play_id=lotto_ticket_instance.game_play_id,
                                stake_amount=lotto_ticket_instance.stake_amount,
                                earning=amount_won,
                                channel_played_from=lotto_ticket_instance.channel,
                                run_batch_id=_draw_batch_id,
                            )

                            if lotto_ticket_instance.channel != "POS_AGENT":
                                # send sms to winner
                                if lotto_ticket_instance.played_via_telco_channel is False:
                                    celery_sms_for_instant_cash_winners.delay(
                                        phone_number=lotto_ticket_instance.user_profile.phone_number,
                                        amount_Won=amount_won,
                                        lottery_type="SALARY_FOR_LIFE",
                                        game_play_id=lotto_ticket_instance.game_play_id,
                                    )

                                else:
                                    # salary_for_life_and_instant_cashout_won_sms_on_telco.delay(
                                    #     phone=lotto_ticket_instance.user_profile.phone_number,
                                    #     ticket_num=lotto_ticket_instance.ticket,
                                    #     amount=amount_won,
                                    #     game_play_id=lotto_ticket_instance.game_play_id,
                                    #     lottery_type="SALARY FOR LIFE",
                                    # )  # noqa
                                    pass

                            # add event to engage
                            winner_engange_event.delay(
                                user_id=lotto_ticket_instance.user_profile.id,
                                event_name="SALARY FOR LIFE GAME WINNER",
                                is_user_profile_id=True,
                            )

            DrawData.objects.create(
                game_type="S4L CREATION DETAIL",
                factor1=prices,
                factor2={},
                factor3=draw_response,
                factor4=creation_data,
            )

            # check the percent of payout, minus it from rtp. if there's any amount left
            # check the low limit threshold. if the amount is less than the threshold, we leave
            # else. this function will recall itself with the new rtp

            rtp_percentage = rtp * (int(best_match) / 100)

            # print("recursive function ", rtp - rtp_percentage)
            # print("rtp_percentage", rtp_percentage, "\n\n")

            # if (rtp - rtp_percentage) > 10000:
            #     new_rtp = rtp - rtp_percentage
            #     # print("e-running", "\n\n\n\n")
            #     # print("new_rtp", new_rtp)
            #     # print("batch_id", match_batch.id)
            #     LottoTicket.salary_for_life_draw_pos(
            #         re_run=True, new_rtp=new_rtp, batch_id=batch_db_id
            #     )

            # else:
            #     # send sms to winner

            #     # send sms to None winners
            #     celery_sms_for_s4lgame_lost.delay(batch_db_id)

            # salary for life running balance
            const_obj = ConstantVariable.objects.all().last()

            if const_obj:
                if True:
                    const_obj.pos_salary_4_life_running_balance = rtp - rtp_percentage
                    const_obj.save()

            else:
                ConstantVariable.objects.create(
                    game_threshold=1,
                    pos_salary_4_life_running_balance=(rtp - rtp_percentage),
                )
                const_obj.pos_salary_4_life_running_balance = rtp - rtp_percentage
                const_obj.save()

        else:
            const_obj = ConstantVariable.objects.all().last()
            if const_obj:
                if True:
                    const_obj.pos_salary_4_life_running_balance = rtp
                    const_obj.save()

            else:
                ConstantVariable.objects.create(
                    game_threshold=1,
                    pos_salary_4_life_running_balance=(rtp),
                )

                if True:
                    const_obj.pos_salary_4_life_running_balance = rtp
                    const_obj.save()

        # notify lotto agent on batch draw
        notify_agents_on_lottery_batch_draw(batch_id=batch_db_id, lottery_type="SALARY_FOR_LIFE")

        # =========================================================
        # =========================================================
        print("RUNNING BALANCE ::", const_obj.pos_salary_4_life_running_balance)
        print((const_obj.salary_4_life_running_balance) > 10000)
        if (const_obj.pos_salary_4_life_running_balance) > 10000:
            new_rtp = const_obj.pos_salary_4_life_running_balance
            LottoTicket.salary_for_life_draw_original(
                re_run=True,
                new_rtp=new_rtp,
                batch_id=batch_db_id,
                recursion_depth=recursion_depth,
            )
        else:
            # send sms to winner
            # send sms to None winners
            celery_sms_for_s4lgame_lost.delay(batch_db_id)
        # =========================================================
        # =========================================================

        return filterd_winners

    @classmethod
    def filter_by_date(cls, date, lottery_type):
        """
        Filter by date, if date is not provided, return the last 1 day data
        """
        filter_date = datetime.strptime(date, "%Y-%m-%d").date()
        queryset = cls.objects.filter(date__date=filter_date, lottery_type=lottery_type)

        if queryset.exists():
            return queryset
        else:
            return cls.filter_by_date(date=f"{filter_date - timedelta(days=1)}", lottery_type=lottery_type)

    @classmethod
    def get_agent_sal_4_life_lottery_batch_db_ids(cls, agent_id, date=None):
        """Get all the lottery batches that are created by an agent"""
        if date is None:
            lottery_qs = cls.objects.filter(agent_profile__id=agent_id, lottery_type="SALARY_FOR_LIFE").order_by("-id")
        else:
            lottery_qs = cls.objects.filter(
                agent_profile__id=agent_id,
                lottery_type="SALARY_FOR_LIFE",
                date__date=date,
            ).order_by("-id")

        batch_db_ids = [item.batch.id for item in lottery_qs]
        return batch_db_ids

    @classmethod
    def add_instant_cash_system_pick_number(cls, instant_id, system_pick_number):
        """Add system pick number to instant cash"""
        instant_obj = cls.objects.get(id=instant_id)
        instant_obj.system_pick_number = system_pick_number
        instant_obj.save()

    @classmethod
    def on_creating_instant_cashout_record_update_system_pick_num(cls, player_phone, winning_ticket, game_id):
        # return None

        ticket_qs = cls.objects.filter(
            user_profile__phone_number=player_phone,
            game_play_id=game_id,
            lottery_type="INSTANT_CASHOUT",
        )

        print(
            f"""
            on_creating_instant_cashout_record_update_system_pick_num"
            f"ticket_qs: {ticket_qs}"
            \n\n\n\n\n\n
        """
        )

        ticket_data = []
        if ticket_qs:
            for ticket in ticket_qs:
                _ticket = [int(i) for i in ticket.ticket.split(",")]
                ticket_data.append(_ticket)

            instant_winning_instance = LottoWinners.objects.filter(ticket=winning_ticket, game_play_id=game_id).last()

            if (instant_winning_instance is not None) and (instant_winning_instance.win_flavour == "WHITE"):
                if instant_winning_instance.match_type == "PERM_4":
                    # print(
                    #     f"""

                    #     ticket_data: {ticket_data}
                    #     instant_winning_instance: 4
                    #     \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n
                    #     """
                    # )
                    # print(
                    #     f"""

                    #     ticket_data: {ticket_data}
                    #     instant_winning_instance: 4
                    #     \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n
                    #     """
                    # )
                    generated_number = find_num(
                        numbers=ticket_data,
                        matches_required=4,
                        log=True,
                        game_play_id=game_id,
                    )

                    if len(generated_number) > 0:
                        ",".join([str(i) for i in generated_number])

                        # ticket_qs.update(system_generated_num=system_pick_number)

                    else:
                        LottoWinners.objects.filter(ticket=winning_ticket, game_play_id=game_id).delete()

                        PosLotteryWinners.objects.filter(game_id=game_id).delete()

                elif instant_winning_instance.match_type == "PERM_3":
                    # print(
                    #     f"""

                    #     ticket_data: {ticket_data}
                    #     instant_winning_instance: 3
                    #     \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n
                    #     """
                    # )

                    generated_number = find_num(
                        numbers=ticket_data,
                        matches_required=3,
                        log=True,
                        game_play_id=game_id,
                    )

                    if len(generated_number) > 0:
                        ",".join([str(i) for i in generated_number])
                        # ticket_qs.update(system_generated_num=system_pick_number)

                    else:
                        LottoWinners.objects.filter(ticket=winning_ticket, game_play_id=game_id).delete()

                elif instant_winning_instance.match_type == "PERM_2":
                    # print(
                    #     f"""

                    #     ticket_data: {ticket_data}
                    #     instant_winning_instance: 2
                    #     \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n
                    #     """
                    # )

                    generated_number = find_num(
                        numbers=ticket_data,
                        matches_required=2,
                        log=True,
                        game_play_id=game_id,
                    )

                    # print(
                    #     f"""
                    #     generated_number: {generated_number}
                    #     \n\n\n\n
                    # """
                    # )

                    if len(generated_number) > 0:
                        ",".join([str(i) for i in generated_number])
                        # ticket_qs.update(system_generated_num=system_pick_number)
                    else:
                        LottoWinners.objects.filter(ticket=winning_ticket, game_play_id=game_id).delete()

    @classmethod
    def generate_instantcashout_system_generated_num(cls, ticket):
        _ticket = [int(i) for i in ticket.split(",")]

        new_ticket = set([])

        while len(new_ticket) < 4:
            random_number = random.randint(1, 40)
            if random_number not in _ticket:
                new_ticket.add(str(random_number))

        return ",".join(new_ticket)

    @classmethod
    def generate_random_system_pick_number_for_instanct_cashout_tickets(cls, game_id):
        """
        This method takes agme id and filter all instant cashout tickets with the game id,
        and generate 4 system pick number that will not match with the ticket number
        """

        instant_cashout_ticket = cls.objects.filter(
            Q(lottery_type="INSTANT_CASHOUT") | Q(lottery_type="QUIKA"),
            Q(game_play_id=game_id),
            Q(system_generated_num__isnull=True),
        )
        # instant_cashout_ticket = cls.objects.filter(
        #     game_play_id=game_id, lottery_type="INSTANT_CASHOUT"
        # )
        if instant_cashout_ticket.exists():
            print(
                f"""
            GENERATING SYSTEM PICK NUMBER FOR INSTANT CASHOUT TICKETS THAT WON'T MATCH
            instant_cashout_ticket.last().game_play_id: {instant_cashout_ticket.last().game_play_id}
            \n\n\n\n\n
            """
            )
            instant_cashout_ticket = cls.objects.filter(
                Q(lottery_type="INSTANT_CASHOUT") | Q(lottery_type="QUIKA"),
                Q(game_play_id=game_id),
            )

            user_picks = set([])
            system_pick = set([])
            for ticket in instant_cashout_ticket:
                ticket_number = ticket.ticket.split(",")
                user_picks.update(ticket_number)

            user_picks = list(user_picks)

            # generate system pick number that
            while len(system_pick) < 4:
                random_number = random.randint(1, 40)
                if str(random_number) not in user_picks:
                    system_pick.add(str(random_number))

            system_pick = list(system_pick)

            cls.objects.filter(game_play_id=instant_cashout_ticket.last().game_play_id).update(
                system_generated_num=",".join(system_pick)
            )
            return ",".join(system_pick)

    @classmethod
    def generate_ticket_pin(cls, game_id):
        ticket_qs = cls.objects.filter(game_play_id=game_id, pin__isnull=True)

        pin = generate_pin()

        if ticket_qs.exists():
            ticket_qs.update(pin=pin)
            # for ticket in ticket_qs:
            #     if ticket.pin is None or ticket.pin == "":
            #         ticket_qs.update(pin=pin)

    # +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++> BANKER

    @classmethod
    def construct_earning(cls, plays):
        prices = dict()
        constant_shares: BankerConstant = BankerConstant.get_const_instance().earning_multiplier

        for ticket in plays:
            earning = {
                1: *********,
                2: ticket.amount_paid * constant_shares["tier1"],
                3: ticket.amount_paid * constant_shares["tier2"],
                4: ticket.amount_paid * constant_shares["tier3"],
                5: *********,
            }
            prices[ticket.rtp] = earning
        # print("@ - prices", prices)
        return prices

    @staticmethod
    def banker_draw(re_run=False, new_rtp=0, batch_id=0, recursion_depth=0, run_manually=False):
        from decisioning_engine.draw import SalaryForLifeDraw

        re_run = False

        # from pos_app.pos_helpers import PosAgentHelper
        # from wyse_ussd.tasks import ussd_lottery_winner_reward
        from pos_app.utils import serialize_ticket
        from wyse_ussd.tasks import salary_for_life_and_instant_cashout_won_sms_on_telco

        """
        This function is used to draw the salary for life daily consolation prize
        """
        DrawLog.create_draw_log(frequency_minutes=30, draw_name="BANKER")
        # print("|||||||\  /|||||||")
        # print("|||||||\  /|||||||")
        # print("||||| NEW RUN ||||")
        # print("||||| NEW RTP ||||")
        # print("    ",  new_rtp)
        # print("|||||||/  \|||||||")
        # print("|||||||/  \|||||||")
        # get latest batch for salary for life
        # batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active = True).last()

        const_obj = ConstantVariable.objects.all().last()

        # ==========================================
        # ==========================================
        MAX_RECURSION_DEPTH = 2
        recursion_depth += 1
        boost_percent = 0
        boost = 0
        topup = 0

        if recursion_depth >= MAX_RECURSION_DEPTH:
            print("RECURSION DEPTH ::", recursion_depth)
            return
        # ==========================================
        # ==========================================

        # if const_obj.merge_pos_draw_s4l is False:
        #     pass
        # else:
        #     return
        # print("running draw")

        if run_manually is False:
            if re_run is True:
                batch = LotteryBatch.objects.filter(id=batch_id).last()
                batch_db_id = batch.id
            else:
                batch = LotteryBatch.objects.filter(lottery_type="BANKER", is_active=True).last()
                batch_db_id = batch.id
        else:
            batch = LotteryBatch.objects.filter(id=batch_id).last()
            batch_db_id = batch.id

        salary_for_life_jackpot = LotteryGlobalJackPot.get_jackpot_instance()
        # print("batch", batch)

        if run_manually is False:
            if re_run is True:
                pass
            else:
                batch.is_active = False
                batch.draw_date = timezone.now()
                batch.save()

                try:
                    LotteryBatch.objects.create(
                        lottery_type="BANKER",
                        global_jackpot=salary_for_life_jackpot,
                    )
                    print("CREATED BATCH")
                except Exception:
                    print("FAILED TO CREATE BATCH")
        else:
            batch.is_active = False
            batch.save()

        match_batch = batch
        print(batch.batch_uuid)
        # match_batch = LotteryBatch.objects.filter(
        #     id=batch_db_id, lottery_type="BANKER"
        # ).last()

        if re_run is True:
            lottery_ids = [
                (0 if LottoWinners.objects.filter(batch=match_batch, lottery=i).exists() else i.id)
                for i in LottoTicket.objects.filter(batch=match_batch, paid=True)
            ]

            lottery_ids = [i for i in lottery_ids if i != 0]

            lottery_qs = LottoTicket.objects.filter(batch=match_batch, id__in=lottery_ids, paid=True)

            # print("coun---------------------------------------t::::::::::::::::::::::::::::::::::::::",
            #       lottery_qs.count())
        else:
            lottery_qs = LottoTicket.objects.filter(
                batch=match_batch,
                paid=True,  # lottery_type="BANKER"
            )

            print("plays count", lottery_qs.count())

        plays = list(
            map(
                lambda x: (
                    x.number_of_ticket,
                    [int(i) for i in x.ticket.split(",")],
                    x.rtp,
                ),
                lottery_qs,
            )
        )

        if re_run is True:
            rtp = new_rtp
        else:
            rtp = lottery_qs.distinct().aggregate(models.Sum("effective_rtp"))["effective_rtp__sum"]
            real_rtp = lottery_qs.distinct().aggregate(models.Sum("rtp"))[
                "rtp__sum"
            ]  # FALL BACK  REAL RTP WHEN NO RTP
            rtp = rtp or real_rtp
            rtp = real_rtp or 0

            # reference_rtp = rtp * 1.0 # multiply to eliminate memory sharing by multiple variables ie reference rto and rtp before boosting

            print(f"#ORIG-RTP ::: {rtp}")

            boost, boost_percent = ConstantVariable.boost_banker_rtp(rtp).values()
            rtp = rtp + boost

            first_draw_time = time(8, 30)

            _timezone = pytz.timezone(settings.TIME_ZONE)
            current_datetime = datetime.now(tz=_timezone)
            current_time = current_datetime.time()

            current_minutes = current_time.hour * 60 + current_time.minute
            first_draw_minutes = first_draw_time.hour * 60 + first_draw_time.minute

            time_difference = current_minutes - first_draw_minutes

            if 0 <= time_difference <= 2:
                try:
                    GamesDailyActivities.update_running_balance(game_type="BANKER", running_balance=boost)
                except:
                    pass

            topup = ConstantVariable.top_up_banker_rtp(rtp)
            rtp = rtp + topup

            # get running balance and add it to rtp
            print(f"#Rerun ::: {re_run}")
            print("boost %:::", boost_percent)
            print("topup %:::", topup)
            if const_obj:
                print(const_obj.banker_running_balance)
                print("\n\n\n")
                print(rtp)
                print("\n\n")
                rtp += const_obj.banker_running_balance
                print("RTP AFTER SUMMING UP  WITH BANKER RUNNING BALANCE :::::", rtp)

        print("rtp", rtp, "\n\n\n\n\n")

        """S4L --> Salary For Life"""
        s4l_win_factor_constant = const_obj.s4l_win_factor_constant or "100,100"
        s4lmin, s4lmax = s4l_win_factor_constant.split(",")
        factor = int(random.choice(range(int(s4lmin), int(s4lmax) + 1))) / 100

        print("FACTOR ::", factor)

        prices = {1: 1 / 1 * factor}

        prices = LottoTicket.construct_earning(lottery_qs)
        # print("PRICES AFTER RTP AND RUNNING BALANCE SUMMATION :::", prices)

        salary_for_life_jackpot_instance = LotteryGlobalJackPot.objects.filter(is_active=True).last()

        if salary_for_life_jackpot_instance is not None:
            jackpot_amount = salary_for_life_jackpot_instance.threshold
        else:
            jackpot_amount = 5000000.00

        from pprint import pprint

        draw_response = SalaryForLifeDraw.banker_decisioning(plays, rtp, prices, jackpot_amount)
        pprint(draw_response)

        (
            best_match,
            best_match_combo,
            best_match_with_jkpt,
            best_match_with_jkpt_combo,
            best_match_witho_jkpt,
            best_match_witho_jkpt_combo,
            LIMIT_WINING_AMOUNT,
        ) = draw_response.values()

        print("best_match", best_match, ":::::::::::")
        filterd_winners = SalaryForLifeDraw.filter_banker_winnings(best_match_combo, plays, prices, jackpot_amount)

        total_winning = SalaryForLifeDraw.deep_sum(filterd_winners)

        if total_winning > rtp:
            best_match_combo = SalaryForLifeDraw.least_occurring_numbers(plays)

        # filterd_winners = SalaryForLifeDraw.filter_banker_winnings(best_match_combo, plays, prices, jackpot_amount)

        DrawData.objects.create(
            game_type="BANKER",
            factor1=prices,
            factor2={"RTP": rtp, "jackpot_amount": jackpot_amount},
            factor3=draw_response,
            factor4=dict(factor=factor),
        )

        DrawData.objects.create(
            game_type="BANKER",
            factor1=prices,
            factor2={
                "RTP": rtp,
                "running_balance": const_obj.banker_running_balance,
                "actual_rtp": rtp,
                "jackpot_amount": jackpot_amount,
                "boost_percent": boost_percent,
                "boost": boost,
                "topup": topup,
            },
            factor3=draw_response,
            factor4=dict(factor=factor, filterd_winners=filterd_winners),
        )
        print("FILTERED WINNERS")
        print(filterd_winners)

        # match_batch.draw_batch_id = match_batch.lottery_winner_ticket + f""
        _draw_batch_id = generate_game_play_id()
        draw_batch_id = {
            _draw_batch_id: filterd_winners,
            "match_combo": best_match_combo,
        }

        old_winning_ticket_record = (
            match_batch.lottery_winner_ticket if match_batch.lottery_winner_ticket is not None else ""
        )
        match_batch.lottery_winner_ticket = f"{old_winning_ticket_record}, {draw_batch_id}"

        old_super_combo = (
            match_batch.lottery_winner_ticket_number if match_batch.lottery_winner_ticket_number is not None else ""
        )

        if isinstance(best_match_combo, list) and len(best_match_combo) < 1:
            pass
        else:
            match_batch.lottery_winner_ticket_number = f"{old_super_combo}, {best_match_combo}"
            match_batch.save()

        if filterd_winners:
            # save result in batch model
            # draw batch

            print(filterd_winners)

            for ticker_won in filterd_winners:
                match_win_type = ticker_won[0]
                match_ticket = ticker_won[1]
                amount_won = ticker_won[2]
                _orignal_stake_amount = ticker_won[1][2]

                # get ticket
                # sample match data = (10, [11, 2, 40, 8, 7])
                ticket = list(match_ticket)[1]  # output = [11, 2, 40, 8, 7]

                # filter db to check who has this ticket in this batch
                ticket_db_filter_qs = LottoTicket.objects.filter(
                    batch=match_batch,
                    ticket=serialize_ticket(ticket),
                    rtp=_orignal_stake_amount,
                )

                # win type. this block will decided if the win type is PERM_4 or PERM_3
                win_type = "PERM_4"

                if match_win_type == 4:
                    win_type = "PERM_4"
                elif match_win_type == 3:
                    win_type = "PERM_3"
                elif match_win_type == 2:
                    win_type = "PERM_2"

                # amount_won = SureBanker.salary_for_life_win_per_line(
                #     match_win_type, list(match_ticket)[0], prices
                # )

                amount_won = prices[ticker_won[1][2]][ticker_won[0]]

                # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n\n")
                # print(match_win_type, list(match_ticket)[0])
                # print("RETURNED :: ", amount_won,
                #       {g.id: {"game_id": g.game_play_id, "stake": g.stake_amount} for g in ticket_db_filter_qs})
                # print("\n\n@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                # print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
                # print(prices)

                # get
                creation_data = []
                if ticket_db_filter_qs:
                    for lotto_ticket_instance in ticket_db_filter_qs:
                        # if LottoWinners.objects.filter(
                        #         phone_number=lotto_ticket_instance.user_profile.phone_number,
                        #         game_play_id=lotto_ticket_instance.game_play_id,
                        #         lottery=lotto_ticket_instance,
                        #         channel_played_from=lotto_ticket_instance.channel,
                        #         stake_amount = lotto_ticket_instance.amount_paid
                        # ).exists():
                        # creation_data.append(("PASSED", ticker_won))
                        # print("PASSING...", lotto_ticket_instance.game_play_id)
                        # continue

                        # amount_won = LottoTicket.salary_for_life_win_per_line(
                        #     match_win_type,
                        #     lotto_ticket_instance.number_of_ticket,
                        #     prices,
                        # )
                        amount_won = ticker_won[2]  # prices[ticker_won[1][2]][ticker_won[0]]

                        print("PLAYED :::", ticker_won)
                        print("EARNINGS :::", prices[ticker_won[1][2]], [ticker_won[0]])

                        # LottoTicket.salary_for_life_win_per_line(
                        #     match_win_type,
                        #     lotto_ticket_instance.number_of_ticket,
                        #     prices,
                        # )

                        print("@ Create lotto ticket winner")
                        print("CALLING EARNING :::", amount_won)

                        if LottoWinners.objects.filter(
                            game_play_id__iexact=lotto_ticket_instance.game_play_id
                        ).exists():
                            pass
                        else:
                            LottoWinners.create_lotto_winner_obj(
                                lottery=lotto_ticket_instance,
                                batch=match_batch,
                                phone_number=lotto_ticket_instance.user_profile.phone_number,
                                ticket=ticket,
                                win_type="ORDINARY_WINNER",
                                match_type=win_type,
                                lotto_type="BANKER",
                                game_play_id=lotto_ticket_instance.game_play_id,
                                stake_amount=lotto_ticket_instance.stake_amount,
                                earning=amount_won,
                                channel_played_from=lotto_ticket_instance.channel,
                                run_batch_id=_draw_batch_id,
                            )

                            if lotto_ticket_instance.channel != "POS_AGENT":
                                # send sms to winner
                                if lotto_ticket_instance.played_via_telco_channel is False:
                                    celery_sms_for_instant_cash_winners.delay(
                                        phone_number=lotto_ticket_instance.user_profile.phone_number,
                                        amount_Won=amount_won,
                                        lottery_type="BANKER",
                                        game_play_id=lotto_ticket_instance.game_play_id,
                                    )
                                else:
                                    salary_for_life_and_instant_cashout_won_sms_on_telco.delay(
                                        phone=lotto_ticket_instance.user_profile.phone_number,
                                        ticket_num=lotto_ticket_instance.ticket,
                                        amount=amount_won,
                                        game_play_id=lotto_ticket_instance.game_play_id,
                                        lottery_type="BANKER",
                                    )

                            # add event to engage
                            winner_engange_event.delay(
                                user_id=lotto_ticket_instance.user_profile.id,
                                event_name="BANKER",
                                is_user_profile_id=True,
                            )

            DrawData.objects.create(
                game_type="BANKER",
                factor1=prices,
                factor2={},
                factor3=draw_response,
                factor4=dict(creation_data=creation_data, batch=(batch.id, batch.batch_uuid)),
            )

            # check the percent of payout, minus it from rtp. if there's any amount left
            # check the low limit threshold. if the amount is less than the threshold, we leave
            # else. this function will recall itself with the new rtp

            rtp * (int(best_match) / 100)

            # print("recursive function ", rtp - rtp_percentage)
            # print("rtp_percentage", rtp_percentage, "\n\n")

            # if (rtp - rtp_percentage) > 10000:
            #     new_rtp = rtp - rtp_percentage
            #     # print("e-running", "\n\n\n\n")
            #     # print("new_rtp", new_rtp)
            #     # print("batch_id", match_batch.id)
            #     SureBanker.salary_for_life_draw(
            #         re_run=True, new_rtp=new_rtp, batch_id=batch_db_id
            #     )

            # else:
            #     # send sms to winner

            #     # send sms to None winners
            #     celery_sms_for_s4lgame_lost.delay(batch_db_id)

            # banker for life running balance
            const_obj = ConstantVariable.objects.all()

            if const_obj:
                if True:
                    const_obj.update(banker_running_balance=rtp - total_winning)
                    # const_obj.save()

            else:
                ConstantVariable.objects.create(
                    game_threshold=1,
                    banker_running_balance=(rtp - total_winning),
                )
                const_obj.banker_running_balance = rtp - total_winning
                const_obj.save()

        else:
            const_obj = ConstantVariable.objects.all()
            if const_obj:
                if True:
                    const_obj.update(banker_running_balance=rtp)
                    # const_obj.save()

            else:
                ConstantVariable.objects.create(
                    game_threshold=1,
                    banker_running_balance=rtp,
                )

                if True:
                    const_obj.banker_running_balance = rtp
                    const_obj.save()

        # =========================================================
        # =========================================================
        # print("RUNNING BALANCE ::", const_obj.banker_running_balance)
        # print(const_obj.banker_running_balance > 10000)

        # if const_obj.banker_running_balance > 10000:
        #     new_rtp = const_obj.banker_running_balance
        #     LottoTicket.banker_draw(
        #         re_run=True,
        #         new_rtp=new_rtp,
        #         batch_id=batch_db_id,
        #         recursion_depth=recursion_depth,
        #     )
        # else:
        #     # send sms to winner
        #     # send sms to None winners
        #     # celery_sms_for_s4lgame_lost.delay(batch_db_id)
        #     pass
        # =========================================================
        # =========================================================

        # notify lotto agent on batch draw
        notify_agents_on_lottery_batch_draw(batch_id=batch_db_id, lottery_type="BANKER")

        return filterd_winners

    @staticmethod
    def get_icash_ticket_price_without_illusion_price(amount=0, number_of_line=0):
        """
        WHEN THE AMOUNT IS 0, IT MEANS THAT YOU WANT ALL THE PRICES
        WHEN THE AMOUNT IS NOT 0, IT MEANS THAT YOU WANT THE DETAILS FOR THAT AMOUNT
        """

        if number_of_line > 0:
            data = [
                {"stake_amount": 200, "line_number": 1, "winning_amount": 2500.0},
                {"stake_amount": 500, "line_number": 2, "winning_amount": 3300.0},
                {"stake_amount": 750, "line_number": 3, "winning_amount": 3500.0},
                {"stake_amount": 1000, "line_number": 4, "winning_amount": 4000.0},
                {"stake_amount": 1250, "line_number": 5, "winning_amount": 4100.0},
                {"stake_amount": 1300, "line_number": 6, "winning_amount": 5500.0},
                {"stake_amount": 1500, "line_number": 7, "winning_amount": 6000.0},
            ]

            for i in data:
                if i.get("line_number") == number_of_line:
                    return i

        elif amount == 0:
            data = [
                {"stake_amount": 200, "line_number": 1, "winning_amount": 2500.0},
                {"stake_amount": 500, "line_number": 2, "winning_amount": 3300.0},
                {"stake_amount": 750, "line_number": 3, "winning_amount": 3500.0},
                {"stake_amount": 1000, "line_number": 4, "winning_amount": 4000.0},
                {"stake_amount": 1250, "line_number": 5, "winning_amount": 4100.0},
                {"stake_amount": 1300, "line_number": 6, "winning_amount": 5500.0},
                {"stake_amount": 1500, "line_number": 7, "winning_amount": 6000.0},
            ]

            return data

        else:
            data = {
                200: {"stake_amount": 200, "line_number": 1, "winning_amount": 2500.0},
                500: {"stake_amount": 500, "line_number": 2, "winning_amount": 3300.0},
                750: {"stake_amount": 750, "line_number": 3, "winning_amount": 3500.0},
                1000: {
                    "stake_amount": 1000,
                    "line_number": 4,
                    "winning_amount": 4000.0,
                },
                1250: {
                    "stake_amount": 1250,
                    "line_number": 5,
                    "winning_amount": 4100.0,
                },
                1300: {
                    "stake_amount": 1300,
                    "line_number": 6,
                    "winning_amount": 5500.0,
                },
                1500: {
                    "stake_amount": 1500,
                    "line_number": 7,
                    "winning_amount": 6000.0,
                },
            }

            return data.get(amount)

    def get_icash_ticket_price_with_illusion_price(amount=0):
        """
        WHEN THE AMOUNT IS 0, IT MEANS THAT YOU WANT ALL THE PRICES
        WHEN THE AMOUNT IS NOT 0, IT MEANS THAT YOU WANT THE DETAILS FOR THAT AMOUNT
        """

        if amount == 0:
            data = [
                {"stake_amount": 300, "line_number": 1, "winning_amount": 2500.0},
                {"stake_amount": 500, "line_number": 2, "winning_amount": 3300.0},
                {"stake_amount": 850, "line_number": 3, "winning_amount": 3500.0},
                {"stake_amount": 1200, "line_number": 4, "winning_amount": 4000.0},
                {"stake_amount": 1450, "line_number": 5, "winning_amount": 4100.0},
                {"stake_amount": 1500, "line_number": 6, "winning_amount": 5500.0},
                {"stake_amount": 1700, "line_number": 7, "winning_amount": 6000.0},
            ]

            return data

        else:
            data = {
                300: {"stake_amount": 200, "line_number": 1, "winning_amount": 2500.0},
                600: {"stake_amount": 500, "line_number": 2, "winning_amount": 3300.0},
                850: {"stake_amount": 750, "line_number": 3, "winning_amount": 3500.0},
                1200: {
                    "stake_amount": 1000,
                    "line_number": 4,
                    "winning_amount": 4000.0,
                },
                1450: {
                    "stake_amount": 1250,
                    "line_number": 5,
                    "winning_amount": 4100.0,
                },
                1500: {
                    "stake_amount": 1300,
                    "line_number": 6,
                    "winning_amount": 5500.0,
                },
                1700: {
                    "stake_amount": 1500,
                    "line_number": 7,
                    "winning_amount": 6000.0,
                },
            }

            return data.get(amount)

    @staticmethod
    def generate_random_system_pick_number_for_sal_4_life_and_banker(numbers, matches_required, line_len=5):
        all_nums = list(range(1, 49))
        system_pick_number = []

        print(
            f"""
        NUMBERS :: {numbers}
        \n\n\n
        """
        )

        final = reduce(lambda x, y: x + y, numbers)
        final = list(final)

        set_form = [set(num) for num in numbers]
        non_occuring = set(all_nums).difference(set(final))
        num_non_occurring = list(non_occuring)

        non_occuring_required = list(num_non_occurring[: line_len - matches_required])

        print("NON OCCURING :::", non_occuring)

        occurrences = collections.Counter(final)
        sorted_occurences = sorted(occurrences.items(), key=lambda x: x[1])
        occurences = [key[0] for key in sorted_occurences]

        def line_matches(reference, target, match_len):
            for line in reference:
                matches = line.intersection(target)

                if len(matches) > match_len:
                    return False

            return True

        for _ in range(1000000):  # noqa
            target_combo = non_occuring_required + random.choices(list(occurences), k=line_len)
            target_combo = target_combo[:5]

            matches = line_matches(set_form, target_combo, matches_required)

            if matches:
                winning_num = target_combo
                print(target_combo)
                print(non_occuring)
                break
        else:
            winning_num = []

        if len(winning_num) > 0:
            # shuffle winning number
            random.shuffle(winning_num)
            random.shuffle(winning_num)

        if len(winning_num) > 0:
            system_pick_number = ",".join([str(i) for i in winning_num])

        # return winning_num, system_pick_number
        return system_pick_number


class InstantCashoutPendingWinning(models.Model):
    WIN_FLAVOUR = [("BLACK", "BLACK"), ("WHITE", "WHITE"), ("CASHBACK", "CASHBACK")]

    ILLUSION_LEVEL = (("GLOBAL", "GLOBAL"), ("LOCAL", "LOCAL"))

    batch = models.CharField(max_length=300, null=True, blank=True)
    tag = models.CharField(max_length=300, null=True, blank=True)
    amount = models.IntegerField(default=0)
    win_flavour = models.CharField(max_length=150, choices=WIN_FLAVOUR, default="WHITE")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    released_at = models.CharField(max_length=300, null=True, blank=True)
    count_before = models.CharField(max_length=300, null=True, blank=True)
    tickets_before = models.FloatField(default=0)
    rtp = models.FloatField(default=0)
    is_avialable = models.BooleanField(default=True)
    is_available_for_pos = models.BooleanField(default=False)
    is_for_pos_only = models.BooleanField(default=False)
    is_for_telco_only = models.BooleanField(default=False)
    tier = models.CharField(max_length=300, null=True, blank=True)
    stake_amount = models.IntegerField(default=0)
    pending = models.BooleanField(null=True, blank=True, default=True)
    suppresed_white = models.BooleanField(null=True, blank=True, default=False)
    suppresed = models.BooleanField(null=True, blank=True, default=False)
    bonus_target_agent = models.ForeignKey(Agent, on_delete=models.CASCADE, null=True, blank=True)
    claimant_phone = models.CharField(max_length=300, null=True, blank=True)
    claimant = models.ForeignKey(LottoTicket, on_delete=models.CASCADE, null=True, blank=True)
    claimant_channel = models.CharField(max_length=300, null=True, blank=True)
    is_illusion = models.BooleanField(default=False)
    illusion_level = models.CharField(max_length=150, choices=ILLUSION_LEVEL, blank=True, null=True)
    shaved = models.BooleanField(default=False)
    suppression_checked = models.BooleanField(default=False)
    local_checked = models.BooleanField(default=True)
    is_avail_50_n_100 = models.BooleanField(default=False)

    def __str__(self) -> str:
        return str(self.id)

    class Meta:
        verbose_name = "INSTANT CASHOUT PENDING WINNING"
        verbose_name_plural = "INSTANT CASHOUT PENDING WINNINGS"

    def save(self, *args, **kwargs) -> None:
        if not self.shaved:
            const_obj: ConstantVariable = ConstantVariable.objects.all().last()

            if self.rtp == 0:
                self.rtp = const_obj.rtp

            self.amount = (self.amount // 50) * 50

        if not self.tag:

            if "G-GIVER" in self.batch:

                self.tag = "NORM GIVER"

            elif "RAPID" in self.batch:

                self.tag = "RAPID FIRE"

            elif "GIVER-G" in self.batch:

                self.tag = "NORM GIVER"

            elif "EXCESS" in self.batch:

                self.tag = "EXCESS"

            elif "MARKETTING" in self.batch:

                self.tag = "MARKETTING"

            elif "DROUGHT" in self.batch:

                self.tag = "DROUGHT"

            else:

                self.tag = "NORM WINNING"

        # if not self.pk and self.win_flavour != "CASHBACK" and const_obj.new_icash2_draw_mode == "LOCAL":

        if not self.pk and const_obj.new_icash2_draw_mode == "LOCAL":
            ready_agent_for_local: Agent = Agent.objects.filter(
                agent_icash_sold__gte=900
            ).last()  # USE THIS TO REWARD LOCAL

            if ready_agent_for_local:
                ready_agent_for_local.agent_icash_sold -= 900
                ready_agent_for_local.save()

                self.is_available_for_pos = True
                self.is_for_pos_only = True
                self.bonus_target_agent = ready_agent_for_local

        if not self.pk and self.win_flavour != "CASHBACK":

            param = ConstantVariableParam.get_param(f"icash_{self.stake_amount}_count")
            self.tickets_before = param.value_float

            ConstantVariableParam.objects.filter(param=f"icash_{self.stake_amount}_count").update(value_float=0)

        if self.claimant_phone is None and self.claimant is not None:

            self.claimant_phone = self.claimant.phone

        return super(InstantCashoutPendingWinning, self).save(*args, **kwargs)

    def attempt_to_reserve_for_local(self):
        icash2_draw_mode = ConstantVariable.objects.all().first().icash2_draw_mode
        if icash2_draw_mode == "LOCAL":
            agent = Agent.objects.filter(agent_icash_sold__gte=900)
            agent = agent.first()
            return agent
        else:
            return None

    @classmethod
    def create_pending_winning(
        cls,
        batch,
        amount,
        tier,
        stake_amount,
        is_avialable=True,
        is_available_for_pos=False,
        is_for_pos_only=False,
        is_for_telco_only=False,
        bonus_target_agent=None,
        count_before=0,
        flavour="WHITE",
        is_avail_50_n_100=False,
    ):
        if ConstantVariable.objects.all().first().delay_icash_winnings:
            cls.objects.create(
                batch=batch,
                amount=float(amount),
                tier=tier,
                stake_amount=stake_amount,
                is_avialable=is_avialable,
                is_for_pos_only=is_for_pos_only,
                is_available_for_pos=is_available_for_pos,
                is_for_telco_only=is_for_telco_only,
                bonus_target_agent=bonus_target_agent,
                count_before=count_before,
                win_flavour=flavour,
                is_avail_50_n_100=is_avail_50_n_100,
            )

        else:
            cls.objects.create(
                batch=batch,
                amount=float(amount),
                tier=tier,
                stake_amount=stake_amount,
                is_avialable=is_avialable,
                is_for_pos_only=is_for_pos_only,
                is_available_for_pos=is_available_for_pos,
                is_for_telco_only=is_for_telco_only,
                bonus_target_agent=bonus_target_agent,
                pending=False,
                count_before=count_before,
                win_flavour=flavour,
                is_avail_50_n_100=is_avail_50_n_100,
            )

    @classmethod
    def handle_game_winning_list(cls, batch, data: list):
        for tier, band, winning in data:
            cls.create_pending_winning(
                is_available_for_pos=True,
                batch=f"OLD-{batch}",
                is_for_pos_only=True,
                stake_amount=band,
                amount=winning,
                tier=tier,
            )

    @classmethod
    def handle_game_winning_list_pos_icash2(
        cls,
        batch,
        data: list,
        agent=None,
        count_before=0,
        flavour="BLACK",
        is_for_pos_only=True,
        is_avail_50_n_100=False,
    ):
        """LOCAL-GLOBAL STYLE INSTANT CASH HANDLER FOR."""
        print(data)
        cls.create_pending_winning(
            batch=batch,
            stake_amount=data[1],
            tier=data[0],
            amount=data[2],
            is_avialable=True,
            is_for_pos_only=is_for_pos_only,
            is_available_for_pos=True,
            bonus_target_agent=agent,
            count_before=count_before,
            flavour=flavour,
            is_avail_50_n_100=is_avail_50_n_100,
        )

    @classmethod
    def handle_game_winning_list_pos(cls, batch, data: list, flavour):
        for tier, band, winning in data:
            cls.create_pending_winning(
                batch=batch,
                stake_amount=band,
                tier=tier,
                amount=winning,
                is_avialable=True,
                is_for_pos_only=True,
                is_available_for_pos=True,
                flavour=flavour,
            )

    @classmethod
    def create_bonus_pending_winning(cls, agent=None):
        cls.objects.create(
            batch=LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT").last(),
            bonus_target_agent=agent,
            is_avialable=True,
            is_for_pos_only=False,
            is_available_for_pos=True,
            stake_amount=200,
            tier="min_win",
            amount=1000,
            pending=False,
            released_at="Agent-Bonus",
        )

    @classmethod
    def break_large_wins(cls):
        delta = int(input("Please enter deviation :: "))
        print(Now() - timedelta(hours=delta))
        large_wins = cls.objects.filter(
            created_at__lt=Now() - timedelta(hours=delta), is_avialable=True
        ) | cls.objects.filter(created_at__lt=Now() - timedelta(hours=delta), is_available_for_pos=True)
        print(large_wins.count())
        for win in large_wins.values():
            # print(win)
            print(f"POS: {win.get('is_for_pos_only')} ALL' {win.get('is_avialable')}  TIME: {win.get('created_at')}")


class LottoWinners(models.Model):
    JACKPOT_WINNER = "JACKPOT_WINNER"
    ORDINARY_WINNER = "ORDINARY_WINNER"
    SUPER_WINNER = "SUPER_WINNER"
    CROWN_JUMBO_WINNER = "CROWN_JUMBO_WINNER"

    WIN_TYPE_CHOICES = [
        (JACKPOT_WINNER, "JACKPOT_WINNER"),
        (ORDINARY_WINNER, "ORDINARY_WINNER"),
        (SUPER_WINNER, "SUPER_WINNER"),
        (CROWN_JUMBO_WINNER, "CROWN_JUMBO_WINNER"),
    ]

    LOTTERY_SOURCE = [
        ("USSD", "USSD"),
        ("USSD_WEB", "USSD_WEB"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
        ("SYSTEM_BONUS", "SYSTEM_BONUS"),
    ]

    LOTTO_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("QUIKA", "QUIKA"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("BANKER", "BANKER"),
        ("AWOOF", "AWOOF"),
    ]

    MATCH_TYPE = [
        ("NAP_5", "NAP_5"),
        ("PERM_5", "PERM_5"),
        ("PERM_4", "PERM_4"),
        ("PERM_3", "PERM_3"),
        ("PERM_2", "PERM_2"),
        ("PERM_1", "PERM_1"),
        ("PERM_0", "PERM_0"),
        ("JACKPOT", "JACKPOT"),
    ]

    WIN_FLAVOUR = [("BLACK", "BLACK"), ("WHITE", "WHITE"), ("CASHBACK", "CASHBACK")]

    win_flavour = models.CharField(max_length=150, choices=WIN_FLAVOUR, default="WHITE", blank=True, null=True)

    batch = models.ForeignKey(LotteryBatch, on_delete=models.CASCADE)
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, null=True, blank=True)
    lottery = models.ForeignKey(LottoTicket, on_delete=models.CASCADE, null=True, blank=True)
    jackpot = models.ForeignKey("main.Jackpot", on_delete=models.CASCADE, null=True, blank=True)
    run_batch_id = models.CharField(max_length=150, null=True, blank=True)
    phone_number = models.CharField(max_length=150)
    game_play_id = models.CharField(max_length=150)
    game_id = models.CharField(max_length=150, unique=True, blank=True, null=True)
    instant_cashout_pending_winning_id = models.ForeignKey(
        "InstantCashoutPendingWinning", on_delete=models.CASCADE, null=True, blank=True
    )
    unique_pending_winning = models.IntegerField(null=True, blank=True, unique=True)
    playyer_id = models.PositiveIntegerField(null=True, blank=True)
    ticket = models.CharField(max_length=150, null=True, blank=True)
    win_type = models.CharField(max_length=150, choices=WIN_TYPE_CHOICES)
    lotto_type = models.CharField(max_length=150, choices=LOTTO_TYPE)
    share = models.CharField(max_length=150, null=True, blank=True)
    stake_amount = models.FloatField(null=True, blank=True)
    earning = models.FloatField(default=0.00)
    total_jackpot_amount = models.FloatField(default=0.00)
    date_won = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    channel_played_from = models.CharField(max_length=150, choices=LOTTERY_SOURCE, default="USSD")
    match_type = models.CharField(max_length=150, choices=MATCH_TYPE, default="PERM_3")
    updated_at = models.DateTimeField(auto_now=True)
    won_jackpot = models.BooleanField(default=False)
    virtual_soccer_lost_but_mark_as_won = models.BooleanField(default=False)
    is_illusion_winning = models.BooleanField(default=False)
    played_via_telco_channel = models.BooleanField(default=False)
    service_type = models.CharField(
        max_length=150,
        choices=SERVICE_TYPE,
        default="AWOOF",
        db_index=True,
        help_text="the type of service played, Awoof or Insurance",
    )

    def __str__(self) -> str:
        if self.phone_number:
            return f"{self.phone_number}"
        else:
            return str(self.id)

    def save(self, *args, **kwargs):
        print("CREATING WIN FOR::::", self.phone_number)
        print("CREATING WIN FOR::::", self.phone_number)
        print("CREATING WIN FOR::::", self.phone_number)
        print("CREATING WIN FOR::::", self.phone_number)
        print("CREATING WIN FOR::::", self.phone_number)
        print("CREATING WIN FOR::::", self.phone_number)
        print("CREATING WIN FOR::::", self.phone_number)
        print("CREATING WIN FOR::::", self.phone_number)
        if not self.pk:
            print("INNER CREATING WIN FOR::::", self.phone_number)

            if self.instant_cashout_pending_winning_id:
                self.unique_pending_winning = self.instant_cashout_pending_winning_id.id

            if LottoWinners.objects.filter(
                phone_number=self.phone_number,
                channel_played_from=self.channel_played_from,
                game_play_id__iexact=self.game_play_id,
            ).exists():
                pass
            else:
                # print("_save_create_pos_lotto_winners")
                # LottoTicket().on_creating_instant_cashout_record_update_system_pick_num(
                #     player_phone=self.lottery.user_profile.phone_number,
                #     ticket=self.ticket,
                #     game_id=self.game_play_id,
                # )

                self.game_id = self.game_play_id

                return super(LottoWinners, self).save(*args, **kwargs)
        else:
            return super(LottoWinners, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "SALARIES FOR LIFE & INSTANT CASHOUT WINNERS TABLE"
        verbose_name_plural = "SALARIES FOR LIFE & INSTANT CASHOUT WINNERS TABLES"

    @classmethod
    def create_lotto_winner_obj(
        cls,
        batch,
        phone_number,
        ticket,
        win_type,
        lotto_type,
        game_play_id,
        stake_amount,
        earning,
        channel_played_from,
        run_batch_id,
        lottery,
        agent=None,
        jackpot_instance=None,
        jackpot=False,
        match_type="PERM_3",
        win_flavour=None,
        is_illusion_winning=False,
        instant_cashout_pending_instance=None,
        played_via_telco_channel=False,
    ):
        print("EARNING BEGIN :::", earning)

        _lotto_ticket = serialize_ticket(ticket)

        if lottery.agent_profile is not None:
            # check if game id has already been record,
            # if yes, check if agent has not claimed it, if agent has not claimed it, then
            # increament the amount

            winning_already_exists = True
            try:
                existing_winning_instance = cls.objects.get(
                    agent=agent, phone_number=phone_number, game_play_id=game_play_id, lotto_type=lotto_type
                )
            except cls.DoesNotExist:
                winning_already_exists = False

            print(
                f"""
                  game_play_id {game_play_id}
                  winning_already_exists: {winning_already_exists}
                \n\n

            """
            )

            if winning_already_exists:
                try:
                    existing_pos_lottery_winnings = PosLotteryWinners.objects.get(
                        agent=lottery.agent_profile,
                        player=lottery.user_profile,
                        game_id=game_play_id,
                        is_win_claimed=False,
                    )
                except PosLotteryWinners.DoesNotExist:
                    return

                existing_pos_lottery_winnings.amount_won += earning
                existing_pos_lottery_winnings.save()

                existing_pos_lottery_winnings.refresh_from_db()

                existing_winning_instance.earning += earning
                existing_winning_instance.save()
                existing_winning_instance.refresh_from_db()

                transaction_reference = f"{existing_winning_instance.game_play_id}-{uuid.uuid4()}"

                agent_wallet = AgentWallet.objects.get(agent=lottery.agent_profile)

                balance_before = agent_wallet.winnings_bal
                excess_amount = 0
                balance_after = balance_before + earning

                game_play_balance_plus_winning = agent_wallet.game_play_bal + balance_after

                type_of_agent = "OTHER_AGENT"
                from_lotto_agent = False
                if agent_wallet.agent.is_winwise_staff_agent is True:
                    type_of_agent = "WINWISE_AGENT"
                    from_lotto_agent = True

                AgentWalletTransaction.objects.create(
                    transaction_reference=transaction_reference,
                    unique_transaction_reference=transaction_reference,
                    agent_wallet=agent_wallet,
                    transaction_type="CREDIT",
                    amount=earning,
                    status="SUCCESSFUL",
                    transaction_from="WINNINGS",
                    bal_before=balance_before,
                    bal_after=balance_after,
                    excess_balance=excess_amount,
                    game_type=existing_winning_instance.lotto_type,
                    game_play_id=game_play_id,
                    game_play_bal_plus_winnings=game_play_balance_plus_winning,
                    rewarded_commission=0,
                    phone_number=lottery.agent_profile.phone,
                    expected_remittance=0,
                    type_of_agent=type_of_agent,
                    type_of_user=lottery.agent_profile.agent_type,
                    agent_name=lottery.agent_profile.first_name + " " + lottery.agent_profile.last_name,
                    agent_phone_number=lottery.agent_profile.phone,
                    agent_email=lottery.agent_profile.email,
                    terminal_id=lottery.agent_profile.terminal_id,
                    terminal_serial_number=lottery.agent_profile.terminal_serial_number,
                    wave=lottery.agent_profile.get_wave(),
                )

                GamesDailyActivities.create_record(
                    game_type=existing_winning_instance.lotto_type,
                    winnings=earning,
                    from_lotto_agent=from_lotto_agent,
                    game_id=game_play_id,
                )

                return

        try:
            lotto_winner = cls.objects.create(
                batch=batch,
                agent=agent,
                jackpot=jackpot_instance,
                phone_number=phone_number,
                ticket=_lotto_ticket,
                win_type=win_type,
                lotto_type=lotto_type,
                game_play_id=game_play_id,
                game_id=game_play_id,
                stake_amount=stake_amount,
                earning=earning,
                channel_played_from=channel_played_from,
                run_batch_id=run_batch_id,
                lottery=lottery,
                match_type=match_type,
                won_jackpot=jackpot,
                win_flavour=win_flavour,
                is_illusion_winning=is_illusion_winning,
                instant_cashout_pending_winning_id=instant_cashout_pending_instance,
                played_via_telco_channel=played_via_telco_channel,
            )

        except IntegrityError:
            return None

        # _lott0_obj = LottoTicket.objects.filter(phone=phone_number, ticket=_lotto_ticket).last()

        if lotto_winner.lottery.agent_profile is not None:
            print("PASSING BECAUSE IS POS AGENT OR SYSTEM BONUS")

        else:
            from wallet_app.models import UserWallet

            try:
                UserWallet.fund_user_wallet_winning_wallet_via_phone(
                    phone_number=phone_number,
                    amount=earning,
                    game_type=lotto_winner.lotto_type,
                    played_via_telco_channel=lotto_winner.played_via_telco_channel,
                )
            except Exception:
                pass

        return lotto_winner

    @property
    def get_win_flovour(self):
        return self.win_flavour

    @staticmethod
    def round_to_nearest_10(number):
        return int(round((number / 10.0))) * 10

    @property
    def get_cashback_percentage(self):
        lotto_ticket = LottoTicket.objects.filter(
            user_profile__phone_number=self.lottery.user_profile.phone_number,
            game_play_id=self.game_play_id,
        )
        if lotto_ticket.exists():
            amount_paid_for_tickets = lotto_ticket.aggregate(Sum("amount_paid"))["amount_paid__sum"]

            if amount_paid_for_tickets is None:
                amount_paid_for_tickets = 0

            perc = (self.earning / amount_paid_for_tickets) * 100

            return LottoWinners.round_to_nearest_10(perc)

        else:
            return 0


def create_pos_lotto_winners(sender, instance, created, **kwargs):
    from wyse_ussd.tasks import (
        celery_update_telco_record,
        won_sms_for_icash_telco_players,
    )

    if created:
        print("create_pos_lotto_winners", "\n\n\n\n")
        LottoTicket().on_creating_instant_cashout_record_update_system_pick_num(
            player_phone=instance.lottery.user_profile.phone_number,
            winning_ticket=instance.ticket,
            game_id=instance.game_play_id,
        )

        if instance.channel_played_from == "POS_AGENT" or instance.channel_played_from == "SYSTEM_BONUS":
            lotto_obj = LottoTicket.objects.filter(game_play_id=instance.game_play_id, ticket=instance.ticket).last()

            print("ENTERED POS_AGENT CODE BLOCK")
            print(
                f"""
                    instance.game_play_id: {instance.game_play_id}
                    instance.ticket: {instance.ticket}
                  """
            )

            if lotto_obj and lotto_obj.agent_profile is not None:
                print("USER HAS AN AGENT")
                agent = Agent.objects.filter(id=lotto_obj.agent_profile.id).last()

                # ------------------------------------
                # create pos lottery winning record
                # try:
                #     PosLotteryWinners().create_winners(
                #         agent=agent,
                #         player=instance.lottery.user_profile,
                #         game_id=lotto_obj.game_play_id,
                #         amount_won=instance.earning,
                #         winner_instance=instance,
                #         jackpot=instance.won_jackpot,
                #         win_flavour=instance.win_flavour,
                #         lottery_type=instance.lotto_type,
                #     )
                # except Exception:
                #     pass

                if agent.terminal_id is None:
                    PosLotteryWinners().create_winners(
                        agent=agent,
                        player=instance.lottery.user_profile,
                        game_id=lotto_obj.game_play_id,
                        amount_won=instance.earning,
                        winner_instance=instance,
                        jackpot=instance.won_jackpot,
                        win_flavour=instance.win_flavour,
                        lottery_type=instance.lotto_type,
                    )

                    # # credit agent winning wallet
                    AgentWallet().credit_agent_winning_balance(
                        agent_id=instance.lottery.agent_profile.id,
                        amount=instance.earning,
                        phone_number=instance.lottery.user_profile.phone_number,
                        game_type=instance.lotto_type,
                        game_play_id=lotto_obj.game_play_id,
                    )

                else:
                    PosLotteryWinners().create_winners(
                        agent=agent,
                        player=instance.lottery.user_profile,
                        game_id=lotto_obj.game_play_id,
                        amount_won=instance.earning,
                        winner_instance=instance,
                        jackpot=instance.won_jackpot,
                        win_flavour=instance.win_flavour,
                        lottery_type=instance.lotto_type,
                    )

                # notify agent of winning
                # try:
                #     pos_agent_helper.notify_agent_on_lottery_win()
                # except Exception:
                #     pass
        else:
            print("not found create_pos_lotto_winners")

        if instance.lotto_type == "VIRTUAL_SOCCER":
            from sport_app.models import VirtualMatches

            VirtualMatches.update_match_as_won(game_play_id=instance.game_play_id, lotto_winner_instance=instance)

        if instance.lotto_type == "SALARY_FOR_LIFE":
            pass
        else:
            if instance.lottery.played_via_telco_channel is False:
                celery_sms_for_instant_cash_winners.apply_async(
                    kwargs={
                        "phone_number": instance.lottery.user_profile.phone_number,
                        "amount_Won": instance.earning,
                        "lottery_type": instance.lotto_type,
                        "winning_no": instance.lottery.ticket,
                        "game_play_id": instance.lottery.game_play_id,
                    },
                    countdown=190,
                )
            else:
                celery_update_telco_record.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "phone_number": instance.lottery.user_profile.phone_number,
                        "total_amount_won": instance.earning,
                    },
                )

        if (
            (instance.lottery.played_via_telco_channel is True)
            and (instance.lotto_type == "INSTANT_CASHOUT")
            or (instance.win_flavour == "CASHBACK")
        ):
            won_sms_for_icash_telco_players.apply_async(
                kwargs={
                    "phone_number": instance.lottery.user_profile.phone_number,
                    "amount_won": instance.earning,
                    "ticket": instance.lottery.ticket,
                },
                countdown=190,
            )


post_save.connect(create_pos_lotto_winners, sender=LottoWinners)


class CreditCard(models.Model):
    user = models.ForeignKey(to=UserProfile, on_delete=models.CASCADE, null=True)
    paystack_customer_id = models.CharField(max_length=250, editable=False, null=True, blank=True)
    paystack_customer_code = models.CharField(max_length=250, editable=False, null=True, blank=True)
    card_email = models.CharField(max_length=250, null=True, blank=True)
    bin = models.CharField(max_length=250, null=True, blank=True)
    last_four_digits = models.CharField(max_length=250, null=True, blank=True)
    exp_month = models.CharField(max_length=250, null=True, blank=True)
    exp_year = models.CharField(max_length=250, null=True, blank=True)
    channel = models.CharField(max_length=250, null=True, blank=True)
    card_type = models.CharField(max_length=250, null=True, blank=True)
    bank = models.CharField(max_length=250, null=True, blank=True)
    country_code = models.CharField(max_length=250, null=True, blank=True)
    brand = models.CharField(max_length=250, null=True, blank=True)
    reusable = models.BooleanField(default=False)
    signature = models.CharField(max_length=250, editable=False, unique=True, null=True, blank=True)
    account_name = models.CharField(max_length=250, null=True, blank=True)
    authorization_code = models.TextField(editable=False, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "CREDIT CARD"
        verbose_name_plural = "CREDIT CARDS"

    @classmethod
    def card_tokenization(cls, user_instance, data):
        """
        This method is used to tokenize a card
        args: user_instance (databse id), data

        sample data

        {
        "event":"charge.success",
        "data":{
            "id":**********,
            "domain":"test",
            "status":"success",
            "reference":"LTT-PAY69d1b477-e848-4149-a0ee-7023f9698651-53",
            "amount":90000,
            "message":"None",
            "gateway_response":"Successful",
            "paid_at":"2022-09-03T21:02:51.000Z",
            "created_at":"2022-09-03T21:02:30.000Z",
            "channel":"card",
            "currency":"NGN",
            "ip_address":"*************",
            "metadata":"",
            "fees_breakdown":"None",
            "log":"None",
            "fees":1350,
            "fees_split":"None",
            "authorization":{
                "authorization_code":"AUTH_bhja9a563v",
                "bin":"408408",
                "last4":"4081",
                "exp_month":"12",
                "exp_year":"2030",
                "channel":"card",
                "card_type":"visa ",
                "bank":"TEST BANK",
                "country_code":"NG",
                "brand":"visa",
                "reusable":true,
                "signature":"SIG_Y16F9YNBnqlKB1MBeEho",
                "account_name":"None"
            },
            "customer":{
                "id":********,
                "first_name":"None",
                "last_name":"None",
                "email":"<EMAIL>",
                "customer_code":"CUS_y109soa8okqu7a4",
                "phone":"None",
                "metadata":"None",
                "risk_action":"default",
                "international_format_phone":"None"
            },
            "plan":{

            },
            "subaccount":{

            },
            "split":{

            },
            "order_id":"None",
            "paidAt":"2022-09-03T21:02:51.000Z",
            "requested_amount":90000,
            "pos_transaction_data":"None",
            "source":{
                "type":"api",
                "source":"merchant_api",
                "entry_point":"transaction_initialize",
                "identifier":"None"
            }
        }

        """

        if isinstance(data, dict):
            card_data = data
        else:
            card_data = json.loads(json.loads(data))

        card_auth = card_data.get("data").get("authorization")

        customer = card_data.get("data").get("customer")
        auth_code = card_auth.get("authorization_code")

        try:
            cls.objects.get(
                signature=card_auth.get("signature"),
                last_four_digits=card_auth.get("last4"),
            )

        except cls.DoesNotExist:
            exp_month = card_auth.get("exp_month")
            exp_year = card_auth.get("exp_year")

            paystack_fernet_string = settings.PAYSTACK_BS64
            fernet = Fernet(bytes(paystack_fernet_string, encoding="utf-8"))
            encrypted_auth_code = ByteHelper.convert_byte_to_string(fernet.encrypt(auth_code.encode()))

            _user = UserProfile.objects.get(id=user_instance)

            cls.objects.create(
                user=_user,
                bin=card_auth.get("bin"),
                last_four_digits=card_auth.get("last4"),
                exp_month=exp_month,
                exp_year=exp_year,
                channel=card_auth.get("channel"),
                card_type=card_auth.get("card_type"),
                bank=card_auth.get("bank"),
                country_code=card_auth.get("country_code"),
                brand=card_auth.get("brand"),
                reusable=card_auth.get("reusable"),
                signature=card_auth.get("signature"),
                account_name=card_auth.get("account_name", None),
                paystack_customer_id=customer.get("id"),
                paystack_customer_code=customer.get("customer_code"),
                card_email=customer.get("email"),
                authorization_code=encrypted_auth_code,
            )


class Jackpot(models.Model):
    """
    This model is used to store the jackpot amount
    """

    JACKPOT_TYPES = [
        ("CROWN_JUMBO_JACKPOT", "CROWN_JUMBO_JACKPOT"),
        ("MEGA_JACKPOT", "MEGA_JACKPOT"),
        ("SUPER_JACKPOT", "SUPER_JACKPOT"),
    ]

    threshold = models.FloatField(default=0.0)
    contributed_amount = models.FloatField(default=0.0)
    alltime_contributed_amount = models.FloatField(default=0.0)
    jackpot_type = models.CharField(max_length=250, choices=JACKPOT_TYPES)
    is_active = models.BooleanField(default=True)
    is_drawn = models.BooleanField(default=False)
    give_out_count = models.IntegerField(default=0)
    drawn_200_band = models.BooleanField(default=False)
    drawn_400_band = models.BooleanField(default=False)
    drawn_800_band = models.BooleanField(default=False)
    drawn_1400_band = models.BooleanField(default=False)
    total_amount_given_out = models.FloatField(default=0.0)
    jackpot_id = models.CharField(max_length=250, default=uuid.uuid4, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # def __init__(self, *args, **kwargs):
    #     super(self, Jackpot).__init__(*args, **kwargs)
    #     self._original_is_active = self.is_active

    def __str__(self):
        return self.jackpot_type

    def save(self, *args, **kwargs):
        if not self.pk:
            if Jackpot.objects.filter(jackpot_id=self.jackpot_id).exists():
                self.jackpot_id = uuid.uuid4()

        if self.pk:
            _original_is_active = False
            # ------------------- GETTING OLD VALUES ------------------- #

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "is_active":
                    _original_is_active = field.value_from_object(old)

            # ------------------- GETTING OLD VALUES ENDS HERE ------------------- #

            if _original_is_active is True and self.is_active is False:
                redis_instance = RedisStorage(f"{self.jackpot_type}-threshold")
                if redis_instance.get_data() is not None:
                    redis_instance.delete_data()

                # delete contributed amount
                redis_instance = RedisStorage(self.jackpot_type)
                if redis_instance.get_data() is not None:
                    redis_instance.delete_data()

            # self.contributed_amount = round(self.contributed_amount, 2)
        return super(Jackpot, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "GENERAL JACKPOT"
        verbose_name_plural = "GENERAL JACKPOTS"

    # @classmethod
    # def is_available(cls):
    #     """
    #     This method is used to check if the jackpot is available
    #     """
    #     jackpot = cls.objects.filter(
    #         is_drawn=False, is_active=False, jackpot_type="SUPER_JACKPOT"
    #     ).order_by("id")
    #     return jackpot.first() if jackpot.exists() else None

    @classmethod
    def is_available(cls):
        return None
        """
        This method is used to check if the jackpot is available
        """
        jackpot = cls.objects.filter(is_active=True, jackpot_type="SUPER_JACKPOT").last()

        if jackpot:
            # get constant variable instance
            const = ConstantVariable.objects.last()

            i_cash_jackpot_winning_amount = const.i_cash_jackpot_winning_amount

            if jackpot.contributed_amount >= i_cash_jackpot_winning_amount:
                try:
                    give_out_count = int(jackpot.contributed_amount / i_cash_jackpot_winning_amount)
                except ZeroDivisionError:
                    give_out_count = 0

                jackpot.give_out_count = give_out_count
                jackpot.total_amount_given_out = F("total_amount_given_out") + i_cash_jackpot_winning_amount
                jackpot.contributed_amount = F("contributed_amount") - i_cash_jackpot_winning_amount
                jackpot.save()

                return jackpot

            else:
                return None

        else:
            return None

    @classmethod
    def get_jackpot(cls):
        """
        This method is used to get the jackpot amount
        """

        data = []

        # # for CROWN_JUMBO_JACKPOT
        # redis_instance = RedisStorage("CROWN_JUMBO_JACKPOT-threshold")  # threshold
        # threshold = redis_instance.get_data()

        # redis_instance = RedisStorage("CROWN_JUMBO_JACKPOT")  # contributed amount
        # contributed_amount = redis_instance.get_data()

        # if threshold is not None and contributed_amount is not None:

        #     threshold = float(threshold.decode("utf-8"))
        #     contributed_amount = float(contributed_amount.decode("utf-8"))

        #     data.append(
        #         {
        #             "threshold": threshold,
        #             "contributed_amount": contributed_amount,
        #             "jackpot_type": "CROWN_JUMBO_JACKPOT",
        #             "jackpot_id": "CROWN_JUMBO_JACKPOT",
        #             "percentage": round((contributed_amount / threshold) * 100),
        #         }
        #     )

        # else:
        jackpot = cls.objects.filter(jackpot_type="CROWN_JUMBO_JACKPOT", is_active=True).last()
        if jackpot:
            try:
                percentage = round((jackpot.contributed_amount / jackpot.threshold) * 100)
            except ZeroDivisionError:
                percentage = 0
            data.append(
                {
                    "threshold": jackpot.threshold,
                    "contributed_amount": jackpot.contributed_amount,
                    "jackpot_type": jackpot.jackpot_type,
                    "jackpot_id": jackpot.jackpot_id,
                    "percentage": percentage,
                }
            )

        # for MEGA_JACKPOT
        # redis_instance = RedisStorage("MEGA_JACKPOT-threshold")  # threshold
        # threshold = redis_instance.get_data()

        # redis_instance = RedisStorage("MEGA_JACKPOT")  # contributed amount
        # contributed_amount = redis_instance.get_data()

        # if threshold is not None and contributed_amount is not None:

        #     threshold = float(threshold.decode("utf-8"))
        #     contributed_amount = float(contributed_amount.decode("utf-8"))

        #     data.append(
        #         {
        #             "threshold": threshold,
        #             "contributed_amount": contributed_amount,
        #             "jackpot_type": "MEGA_JACKPOT",
        #             "jackpot_id": "MEGA_JACKPOT",
        #             "percentage": round((contributed_amount / threshold) * 100),
        #         }
        #     )

        # else:
        jackpot = cls.objects.filter(jackpot_type="MEGA_JACKPOT", is_active=True).last()
        if jackpot:
            try:
                percentage = round((jackpot.contributed_amount / jackpot.threshold) * 100)
            except ZeroDivisionError:
                percentage = 0

            data.append(
                {
                    "threshold": jackpot.threshold,
                    "contributed_amount": jackpot.contributed_amount,
                    "jackpot_type": jackpot.jackpot_type,
                    "jackpot_id": jackpot.jackpot_id,
                    "percentage": percentage,
                }
            )

        # for SUPER_JACKPOT
        # redis_instance = RedisStorage("SUPER_JACKPOT-threshold")  # threshold
        # threshold = redis_instance.get_data()

        # redis_instance = RedisStorage("SUPER_JACKPOT")  # contributed amount
        # contributed_amount = redis_instance.get_data()

        # if threshold is not None and contributed_amount is not None:

        #     threshold = float(threshold.decode("utf-8"))
        #     contributed_amount = float(contributed_amount.decode("utf-8"))

        #     data.append(
        #         {
        #             "threshold": threshold,
        #             "contributed_amount": contributed_amount,
        #             "jackpot_type": "SUPER_JACKPOT",
        #             "jackpot_id": "SUPER_JACKPOT",
        #             "percentage": round((contributed_amount / threshold) * 100),
        #         }
        #     )

        # else:
        jackpot = cls.objects.filter(jackpot_type="SUPER_JACKPOT", is_active=True).last()
        if jackpot:
            try:
                percentage = round((jackpot.contributed_amount / jackpot.threshold) * 100)
            except ZeroDivisionError:
                percentage = 0
            data.append(
                {
                    "threshold": jackpot.threshold,
                    "contributed_amount": jackpot.contributed_amount,
                    "jackpot_type": jackpot.jackpot_type,
                    "jackpot_id": jackpot.jackpot_id,
                    "percentage": percentage,
                }
            )

        return data

    @classmethod
    def update_jackpot(cls, amount, jackpot_id):
        """
        This method is used to update the jackpot amount
        """
        jackpot = cls.objects.filter(jackpot_id=jackpot_id).last()
        if jackpot:
            jackpot.contributed_amount = F("contributed_amount") + amount
            jackpot.alltime_contributed_amount = F("alltime_contributed_amount") + amount
            jackpot.save()
        else:
            pass

    @classmethod
    def add_to_jackpot(cls, amount):
        """
        This method is used to add to jackpot amount

        percent of amount to be shared acros the three jackpot
        super_jackpot: 50%
        megae_jackpot: 30%
        crown_jumbo_jackpot: 20%
        """

        # TEMP IMPLEMENTATION #
        super_jackpot_amt = amount

        super_jackpot = cls.objects.filter(jackpot_type="SUPER_JACKPOT", is_active=True).last()

        if not super_jackpot:
            super_jackpot = cls.objects.create(
                threshold=JackpotConstantVariable().get_super_jackpot_threshold(),
                jackpot_type="SUPER_JACKPOT",
                contributed_amount=super_jackpot_amt,
            )

        else:
            super_jackpot.contributed_amount += super_jackpot_amt
            super_jackpot.save()

            if super_jackpot.contributed_amount >= super_jackpot.threshold:
                super_jackpot.is_active = False
                super_jackpot.save()

                cls.objects.create(
                    threshold=JackpotConstantVariable().get_super_jackpot_threshold(),
                    jackpot_type="SUPER_JACKPOT",
                    contributed_amount=super_jackpot_amt,
                )

        # super_jackpot_amt = (
        #         amount * JackpotConstantVariable().get_super_jackpot_interest_perc()
        # )

        # mega_jackpot_amt = (
        #         amount * JackpotConstantVariable().get_mega_jackpot_interest_perc()
        # )
        # crown_jumbo_jackpot_amt = (
        #         amount * JackpotConstantVariable().get_crown_jumbo_jackpot_interest_perc()
        # )

        # super_jackpot = cls.objects.filter(
        #     jackpot_type="SUPER_JACKPOT", is_active=True
        # ).last()
        # mega_jackpot = cls.objects.filter(
        #     jackpot_type="MEGA_JACKPOT", is_active=True
        # ).last()
        # crown_jumbo_jackpot = cls.objects.filter(
        #     jackpot_type="CROWN_JUMBO_JACKPOT", is_active=True
        # ).last()

        # if not super_jackpot:
        #     super_jackpot = cls.objects.create(
        #         threshold=JackpotConstantVariable().get_super_jackpot_threshold(),
        #         jackpot_type="SUPER_JACKPOT",
        #         contributed_amount=super_jackpot_amt,
        #     )

        # else:
        #     super_jackpot.contributed_amount += super_jackpot_amt
        #     super_jackpot.save()

        #     if super_jackpot.contributed_amount >= super_jackpot.threshold:
        #         super_jackpot.is_active = False
        #         super_jackpot.save()

        #         cls.objects.create(
        #             threshold=JackpotConstantVariable().get_super_jackpot_threshold(),
        #             jackpot_type="SUPER_JACKPOT",
        #             contributed_amount=super_jackpot_amt,
        #         )

        # if not mega_jackpot:
        #     mega_jackpot = cls.objects.create(
        #         threshold=JackpotConstantVariable().get_mega_jackpot_threshold(),
        #         jackpot_type="MEGA_JACKPOT",
        #         contributed_amount=mega_jackpot_amt,
        #     )
        # else:
        #     mega_jackpot.contributed_amount += mega_jackpot_amt
        #     mega_jackpot.save()

        #     if mega_jackpot.contributed_amount >= mega_jackpot.threshold:
        #         mega_jackpot.is_active = False
        #         mega_jackpot.save()

        #         cls.objects.create(
        #             threshold=JackpotConstantVariable().get_mega_jackpot_threshold(),
        #             jackpot_type="MEGA_JACKPOT",
        #             contributed_amount=mega_jackpot_amt,
        #         )

        # if not crown_jumbo_jackpot:
        #     crown_jumbo_jackpot = cls.objects.create(
        #         threshold=JackpotConstantVariable().get_crown_jumbo_jackpot_threshold(),
        #         jackpot_type="CROWN_JUMBO_JACKPOT",
        #         contributed_amount=crown_jumbo_jackpot_amt,
        #     )
        # else:
        #     crown_jumbo_jackpot.contributed_amount += crown_jumbo_jackpot_amt
        #     crown_jumbo_jackpot.save()

        #     if crown_jumbo_jackpot.contributed_amount >= crown_jumbo_jackpot.threshold:
        #         crown_jumbo_jackpot.is_active = False
        #         crown_jumbo_jackpot.save()

        #         cls.objects.create(
        #             threshold=JackpotConstantVariable().get_crown_jumbo_jackpot_threshold(),
        #             jackpot_type="CROWN_JUMBO_JACKPOT",
        #             contributed_amount=crown_jumbo_jackpot_amt,
        #         )

    @classmethod
    def next_available_jackpot(cls):
        jackpot = cls.objects.filter(
            is_drawn=False,
            is_active=False,
            drawn_200_band=False,
            jackpot_type="SUPER_JACKPOT",
        ).order_by("id")
        return jackpot.first() if jackpot.count() > 0 else None


# save global jackpot to redis
def add_global_jackpot_to_redis_db(sender, instance: Jackpot, created, **kwargs):
    if created:
        # save threshold to redis
        redis_instance = RedisStorage(f"{instance.jackpot_type}-threshold")
        redis_instance.set_data(instance.threshold)

        # save contributed amount to redis
        redis_instance = RedisStorage(instance.jackpot_type)
        redis_instance.set_data(instance.contributed_amount)

    else:
        if instance.is_active:
            # save threshold to redis
            redis_instance = RedisStorage(f"{instance.jackpot_type}-threshold")

            if redis_instance.get_data() is None:
                redis_instance.set_data(instance.threshold)

            redis_instance = RedisStorage(instance.jackpot_type)
            get_data = redis_instance.get_data()
            if get_data is None:
                redis_instance.set_data(instance.contributed_amount)
            else:
                amount = float(get_data.decode("utf-8")) + instance.contributed_amount
                redis_instance.set_data(amount)


# post_save.connect(add_global_jackpot_to_redis_db, sender=Jackpot)


class JackpotWinner(models.Model):
    """
    This model is used to store the jackpot winner
    """

    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    jackpot = models.ForeignKey(Jackpot, on_delete=models.CASCADE)
    amount = models.FloatField(default=0.0)
    game_id = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.phone_number

    class Meta:
        verbose_name = "GENERAL JACKPOT WINNER"
        verbose_name_plural = "GENERAL JACKPOT WINNER"


class LotteryGlobalJackPot(models.Model):
    """
    This model is used to store the salary for life jackpot amount
    """

    LOTTERY_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
    ]

    threshold = models.FloatField(default=0.0)
    contributed_amount = models.FloatField(default=0.0)
    lottery_type = models.CharField(max_length=250, choices=LOTTERY_TYPE, default="SALARY_FOR_LIFE")
    is_active = models.BooleanField(default=True)
    is_drawn = models.BooleanField(default=False)
    jackpot_id = models.CharField(max_length=250, default=uuid.uuid4, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.lottery_type}-{self.jackpot_id}"

    class Meta:
        verbose_name = "SALARY FOR LIFE JACKPOT"
        verbose_name_plural = "SALARY FOR LIFE JACKPOT"

    def save(self, *args, **kwargs):
        if not self.pk:
            # check if jackpot is already active

            if LotteryGlobalJackPot.objects.filter(jackpot_id=self.jackpot_id).exists():
                self.jackpot_id = uuid.uuid4()
        return super(Jackpot, self).save(*args, **kwargs)

    @classmethod
    def get_jackpot(cls):
        """
        This method is used to get the jackpot amount
        """
        jackpot = cls.objects.filter(is_active=True).last()

        try:
            percentage = round((jackpot.contributed_amount / jackpot.threshold) * 100)
        except ZeroDivisionError:
            percentage = 0

        if jackpot:
            return {
                "threshold": jackpot.threshold,
                "contributed_amount": jackpot.contributed_amount,
                "percentage": percentage,
            }
        return {}

    @classmethod
    def get_jackpot_instance(cls):
        """
        This method is used to get the jackpot amount
        """
        jackpot = cls.objects.filter(is_active=True).last()

        if jackpot:
            return jackpot
        else:
            jackpot = cls.objects.create(threshold=1000000, contributed_amount=0.0)
            return jackpot

    @classmethod
    def update_jackpot(cls, amount):
        """
        This method is used to update the jackpot amount
        """
        jackpot = cls.objects.filter(is_active=True).last()
        if jackpot:
            jackpot.alltime_contributed_amount += amount
            jackpot.contributed_amount = amount
            jackpot.save()
        else:
            pass

    @classmethod
    def add_to_jackpot(cls, amount, lottery_type):
        """
        This method is used to add to the jackpot amount
        """
        jackpot = cls.objects.filter(is_active=True, lottery_type=lottery_type).last()
        if jackpot:
            jackpot.alltime_contributed_amount += amount
            jackpot.contributed_amount += amount
            jackpot.save()
        else:
            cls.objects.create(
                threshold=1000000,
                contributed_amount=float(amount),
                lottery_type=lottery_type,
            )

    def save(self, *args, **kwargs):  # noqa
        if not self.pk:
            # check if there is an active jackpot
            jackpot = self.__class__.objects.filter(is_active=True).last()
            if jackpot:
                raise ValidationError("There is an active jackpot")

        return super(LotteryGlobalJackPot, self).save(*args, **kwargs)


class BalanceBonus(models.Model):
    GAME_CHOICES = (
        ("PICK A GAME", "PICK A GAME"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("INSTANT_CASHOUT_POS", "INSTANT_CASHOUT_POS"),
        ("INSTANT_CASHOUT_2_POS", "INSTANT_CASHOUT_2_POS"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("SALARY_FOR_LIFE_POS", "SALARY_FOR_LIFE_POS"),
        ("BANKER BOOSTER", "BANKER BOOSTER"),
        ("BANKER TOP UP", "BANKER TOP UP"),
        ("WYSE_CASH", "WYSE_CASH"),
    )
    AGENT_GIVEOUT_MODES = (
        ("NOT ICASH 2", "NOT ICASH 2"),
        ("GLOBAL BONUS", "GLOBAL BONUS"),
        ("LOCAL BONUS", "LOCAL BONUS"),
        ("ALL AGENTS", "ALL AGENTS"),
        ("SINGLE AGENT", "SINGLE AGENT"),
    )
    TRANSACTION_TYPE = (
        ("PICK A TRANSACTION TYPE", "PICK A TRANSACTION TYPE"),
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
    )

    game_type = models.CharField(max_length=100, choices=GAME_CHOICES, default="PICK A GAME")
    transaction_type = models.CharField(max_length=100, choices=TRANSACTION_TYPE, default="PICK A TRANSACTION TYPE")
    icash2_giveout_mode = models.CharField(
        max_length=100,
        choices=AGENT_GIVEOUT_MODES,
        default=1,
        help_text="PICK AN AGENT GIVE OUT MODE IF GIVING TO AGENT",
    )
    target_agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, blank=True, null=True)
    bonus_amount = models.PositiveIntegerField(default=0.0)
    balance_before = models.FloatField(default=0.0)
    balance_after = models.FloatField(default=0.0)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    deployed = models.BooleanField(default=True)
    narration = models.CharField(max_length=100, null=True, blank=True)

    def save(self, *args, **kwargs):
        print(self.game_type, self.icash2_giveout_mode)
        TRX_TYPE_MULTIPLIERS = {"DEBIT": -1, "CREDIT": 1}
        bonus_amount = self.bonus_amount * TRX_TYPE_MULTIPLIERS.get(self.transaction_type, 0)

        if self.deployed is True:
            super(BalanceBonus, self).save(*args, **kwargs)
            return

        self.deployed = True

        if self.game_type == "INSTANT_CASHOUT":
            const_obj: ConstantVariable = ConstantVariable.objects.all().last()
            self.balance_before = const_obj.instant_cashout_running_balance
            const_obj.instant_cashout_running_balance += bonus_amount
            self.balance_after = const_obj.instant_cashout_running_balance
            const_obj.save()
            super(BalanceBonus, self).save(*args, **kwargs)
        elif self.game_type == "INSTANT_CASHOUT_POS":
            const_obj: ConstantVariable = ConstantVariable.objects.all().last()
            self.balance_before = const_obj.pos_instant_cashout_running_balance
            const_obj.pos_instant_cashout_running_balance += bonus_amount
            self.balance_after = const_obj.pos_instant_cashout_running_balance
            const_obj.save()
            super(BalanceBonus, self).save(*args, **kwargs)

        elif self.game_type == "INSTANT_CASHOUT_2_POS" and self.icash2_giveout_mode == "GLOBAL BONUS":
            const_obj: ConstantVariable = ConstantVariable.objects.all().last()
            self.balance_before = const_obj.icash2_global_bonus_available
            const_obj.icash2_global_bonus_available += bonus_amount
            self.balance_after = const_obj.icash2_global_bonus_available
            const_obj.save()
            super(BalanceBonus, self).save(*args, **kwargs)
        elif self.game_type == "INSTANT_CASHOUT_2_POS" and self.icash2_giveout_mode == "LOCAL BONUS":
            const_obj: ConstantVariable = ConstantVariable.objects.all().last()
            self.balance_before = const_obj.icash2_local_bonus_available
            const_obj.icash2_local_bonus_available += bonus_amount
            self.balance_after = const_obj.icash2_local_bonus_available
            const_obj.save()
            super(BalanceBonus, self).save(*args, **kwargs)

        elif self.game_type == "INSTANT_CASHOUT_2_POS" and self.icash2_giveout_mode == "ALL AGENTS":
            total_agent_balance = 0

            all_agents = Agent.objects.all()
            all_agents_count = all_agents.count()
            total_balance_before = all_agents.aggregate(models.Sum("icash2_bonus_left")).get("icash2_bonus_left__sum")
            bonus_amount = self.bonus_amount
            unit_chunk = bonus_amount / all_agents_count

            for agent in all_agents:
                actual_agent: Agent = Agent.objects.get(id=agent.id)

                if bonus_amount >= unit_chunk:
                    bonus_amount = bonus_amount - unit_chunk
                    actual_agent.icash2_bonus_left += unit_chunk
                    actual_agent.save()

                    total_agent_balance += actual_agent.icash2_bonus_left

            self.balance_before = total_balance_before
            self.balance_after = total_agent_balance

            super(BalanceBonus, self).save(*args, **kwargs)

        elif self.game_type == "INSTANT_CASHOUT_2_POS" and self.icash2_giveout_mode == "SINGLE AGENT":
            total_agent_balance = 0

            all_agents = Agent.objects.filter(id=self.target_agent.id)
            all_agents_count = all_agents.count()
            total_balance_before = all_agents.aggregate(models.Sum("icash2_bonus_left")).get("icash2_bonus_left__sum")
            bonus_amount = self.bonus_amount
            unit_chunk = bonus_amount / all_agents_count

            for agent in all_agents:
                actual_agent: Agent = Agent.objects.get(id=agent.id)

                if bonus_amount >= unit_chunk:
                    bonus_amount = bonus_amount - unit_chunk
                    actual_agent.icash2_bonus_left += unit_chunk
                    actual_agent.save()

                    total_agent_balance += actual_agent.icash2_bonus_left

            self.balance_before = total_balance_before
            self.balance_after = total_agent_balance

            super(BalanceBonus, self).save(*args, **kwargs)

        elif self.game_type == "SALARY_FOR_LIFE":
            const_obj: ConstantVariable = ConstantVariable.objects.all().last()
            self.balance_before = const_obj.salary_4_life_running_balance
            const_obj.salary_4_life_running_balance += bonus_amount
            self.balance_after = const_obj.salary_4_life_running_balance
            const_obj.save()
            super(BalanceBonus, self).save(*args, **kwargs)

        elif self.game_type == "SALARY_FOR_LIFE_POS":
            const_obj: ConstantVariable = ConstantVariable.objects.all().last()
            self.balance_before = const_obj.pos_salary_4_life_running_balance
            const_obj.pos_salary_4_life_running_balance += bonus_amount
            self.balance_after = const_obj.pos_salary_4_life_running_balance
            const_obj.save()
            super(BalanceBonus, self).save(*args, **kwargs)

        elif self.game_type == "WYSE_CASH":
            const_obj: ConstantVariable = ConstantVariable.objects.all().last()
            self.balance_before = const_obj.wyse_cash_running_balance
            const_obj.wyse_cash_running_balance += bonus_amount
            self.balance_after = const_obj.wyse_cash_running_balance
            const_obj.save()
            super(BalanceBonus, self).save(*args, **kwargs)

        elif self.game_type == "BANKER BOOSTER":
            self.balance_before = ConstantVariable.objects.all().last().banker_booster_balance
            ConstantVariable.objects.all().update(
                banker_booster_balance=models.F("banker_booster_balance") + bonus_amount
            )
            self.balance_after = ConstantVariable.objects.all().last().banker_booster_balance
            super(BalanceBonus, self).save(*args, **kwargs)

        elif self.game_type == "BANKER TOP UP":
            self.balance_before = ConstantVariable.objects.all().last().banker_topup_balance
            ConstantVariable.objects.all().update(banker_topup_balance=models.F("banker_topup_balance") + bonus_amount)
            self.balance_after = ConstantVariable.objects.all().last().banker_topup_balance
            super(BalanceBonus, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "BALANCE BONUS"
        verbose_name_plural = "BALANCE BONUS"

    @classmethod
    def seed_bonuses(cls):
        # bulk_bonus_amount: int

        GAME_CHOICES = (
            ("PICK A GAME", "PICK A GAME"),
            ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
            ("INSTANT_CASHOUT_POS", "INSTANT_CASHOUT_POS"),
            ("SALARY_FOR_LIFE_POS", "SALARY_FOR_LIFE_POS"),
            ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
            ("WYSE_CASH", "WYSE_CASH"),
        )

        for game in GAME_CHOICES:
            const_obj: ConstantVariable = ConstantVariable.objects.all().last()

            if game[1] == "PICK A GAME" or game[1] == "INSTANT_CASHOUT" or game[1] == "SALARY_FOR_LIFE":
                continue  # IGNORE PICK A GAME OPTION.!!

            const_obj.bulk_bonus_total_amount = (
                const_obj.bulk_bonus_total_amount - const_obj.bulk_bonus_giveout_unit_amount
            )

            if const_obj.bulk_bonus_total_amount < 0:
                return {
                    "status": False,
                    "balance": const_obj.bulk_bonus_total_amount,
                    "message": f"Insufficient bonus at {game[1]}",
                }

            if const_obj.bulk_bonus_giveout_unit_amount <= 0:
                return {
                    "status": False,
                    "balance": const_obj.bulk_bonus_total_amount,
                    "message": "Bonus less than one",
                }

            const_obj.save()
            create_balance_bonus.delay(
                game[1],
                "CREDIT",
                const_obj.bulk_bonus_giveout_unit_amount,
                False,
                f"SEEDED BONUS N{const_obj.bulk_bonus_total_amount} LEFT",
            )

            print("#")
            print("#")
            print("ADDING BALANCE BONUS")
            print("#")
            print("#")
            sleep(1)

        return {
            "status": True,
            "balance": const_obj.bulk_bonus_total_amount,
            "message": "Ok",
        }


class DrawData(models.Model):
    GAME_CHOICES = (
        ("PICK A GAME", "PICK A GAME"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("SALARY_FOR_LIFE_POS", "SALARY_FOR_LIFE_POS"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("S4L CREATION DETAIL", "S4L CREATION DETAIL"),
        ("BANKER", "BANKER"),
    )

    game_type = models.CharField(max_length=100, choices=GAME_CHOICES, default="PICK A GAME")
    factor1 = models.JSONField(null=True, blank=True)
    factor2 = models.JSONField(null=True, blank=True)
    factor3 = models.JSONField(null=True, blank=True)
    factor4 = models.JSONField(null=True, blank=True)
    extra = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "DRAW META DATA"
        verbose_name_plural = "DRAW META DATA"


class LotteryTicketPrice(models.Model):
    LOTTO_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
    ]

    WINNING_TYPE = [
        ("PERM_4", "PERM_4"),
        ("PERM_3", "PERM_3"),
        ("PERM_2", "PERM_2"),
    ]

    band = models.IntegerField(default=0)
    number_of_tickets = models.IntegerField(default=0)
    pontential_winning = models.FloatField(default=0.0)
    ticket_price = models.FloatField(default=0.0)
    lottery_type = models.CharField(max_length=100, choices=LOTTO_TYPE)
    winning_type = models.CharField(max_length=100, choices=WINNING_TYPE, default="PERM_2")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Ticket Price: {self.ticket_price}"

    class Meta:
        verbose_name = "LOTTERY TICKET PRICE"
        verbose_name_plural = "LOTTERY TICKET PRICES"


class JackpotConstantVariable(models.Model):
    """
    This model is used to store the global jackpot constant variables
    """

    percentage_to_share = models.FloatField(
        default=5,
        help_text="PLEASE ENTER AN INTEGER NUMBER, IT'LL BE DIVIDED BY 100 ON THE BACKEND",
    )
    crown_jumbo_jackpot_interest_perc = models.FloatField(
        default=15,
        help_text="PLEASE ENTER AN INTEGER NUMBER, IT'LL BE DIVIDED BY 100 ON THE BACKEND",
    )
    mega_jackpot_interest_perc = models.FloatField(
        default=25,
        help_text="PLEASE ENTER AN INTEGER NUMBER, IT'LL BE DIVIDED BY 100 ON THE BACKEND",
    )
    super_jackpot_interest_perc = models.FloatField(
        default=60,
        help_text="PLEASE ENTER AN INTEGER NUMBER, IT'LL BE DIVIDED BY 100 ON THE BACKEND",
    )
    crown_jumbo_jackpot_threshold = models.FloatField(default=500000)
    mega_jackpot_threshold = models.FloatField(default=150000)
    super_jackpot_threshold = models.FloatField(default=42000)
    icash_super_jackpot_sharing = models.CharField(
        max_length=300,
        default="11.9,19.0,28.6,40.5",
        help_text="'11.9,19.0,28.6,40.5' THIS'S THE PERCENTAGE INSTANT CASHOUT 200,400 & 800 BAND ARE GOING TO USE TO SHARE THE JACKPOT WINNING",
    )
    count_icash_jkpt = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if self.pk:
            __original_percentage_to_share = 5
            __original_crown_jumbo_jackpot_interest_perc = 15
            __original_mega_jackpot_interest_perc = 25
            __original_super_jackpot_interest_perc = 60

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "percentage_to_share":
                    __original_percentage_to_share = field.value_from_object(old)

                elif field.name == "crown_jumbo_jackpot_interest_perc":
                    __original_crown_jumbo_jackpot_interest_perc = field.value_from_object(old)

                elif field.name == "mega_jackpot_interest_perc":
                    __original_mega_jackpot_interest_perc = field.value_from_object(old)

                elif field.name == "super_jackpot_interest_perc":
                    __original_super_jackpot_interest_perc = field.value_from_object(old)

            if self.percentage_to_share != __original_percentage_to_share:
                redis_db = RedisStorage(redis_key="global_jackpot_percentage_to_share")
                redis_db.set_data(self.percentage_to_share)

            if self.crown_jumbo_jackpot_interest_perc != __original_crown_jumbo_jackpot_interest_perc:
                redis_db = RedisStorage(redis_key="global_jackpot_crown_jumbo_jackpot_interest_perc")
                redis_db.set_data(self.crown_jumbo_jackpot_interest_perc)

            if self.mega_jackpot_interest_perc != __original_mega_jackpot_interest_perc:
                redis_db = RedisStorage(redis_key="global_jackpot_mega_jackpot_interest_perc")
                redis_db.set_data(self.mega_jackpot_interest_perc)

            if self.super_jackpot_interest_perc != __original_super_jackpot_interest_perc:
                redis_db = RedisStorage(redis_key="global_jackpot_super_jackpot_interest_perc")
                redis_db.set_data(self.mega_jackpot_interest_perc)

        return super(JackpotConstantVariable, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "GENERAL JACKPOT CONSTANT VARIABLE"
        verbose_name_plural = "GENERAL JACKPOT CONSTANT VARIABLE"

    @classmethod
    def get_percentage_to_share(cls):
        redis_db = RedisStorage(redis_key="global_jackpot_percentage_to_share")
        get_per_to_share_per = redis_db.get_data()
        if get_per_to_share_per is None:
            per = cls.objects.all().last().percentage_to_share / 100 if cls.objects.all().last() else 0
            redis_db.set_data(per)
            return per
        else:
            return float(get_per_to_share_per.decode("utf-8"))

    @classmethod
    def get_jackpot_constant_variables(cls):
        return cls.objects.all().last()

    @classmethod
    def get_crown_jumbo_jackpot_interest_perc(cls):
        redis_db = RedisStorage(redis_key="global_jackpot_crown_jumbo_jackpot_interest_perc")
        get_per_crown_jkp = redis_db.get_data()
        if get_per_crown_jkp is None:
            per = cls.objects.all().last().percentage_to_share / 100 if cls.objects.all().last() else 0
            redis_db.set_data(per)
            return per
        else:
            return float(get_per_crown_jkp.decode("utf-8"))

    @classmethod
    def get_mega_jackpot_interest_perc(cls):
        redis_db = RedisStorage(redis_key="global_jackpot_mega_jackpot_interest_perc")
        get_per_mega_jkp = redis_db.get_data()
        if get_per_mega_jkp is None:
            per = cls.objects.all().last().percentage_to_share / 100 if cls.objects.all().last() else 0
            redis_db.set_data(per)
            return per
        else:
            return float(get_per_mega_jkp.decode("utf-8"))

    @classmethod
    def get_super_jackpot_interest_perc(cls):
        redis_db = RedisStorage(redis_key="global_jackpot_super_jackpot_interest_perc")
        get_per_super_jkp = redis_db.get_data()
        if get_per_super_jkp is None:
            per = cls.objects.all().last().percentage_to_share / 100 if cls.objects.all().last() else 0
            redis_db.set_data(per)
            return per
        else:
            return float(get_per_super_jkp.decode("utf-8"))

    @classmethod
    def get_crown_jumbo_jackpot_threshold(cls):
        return cls.objects.all().last().crown_jumbo_jackpot_threshold if cls.objects.all().last() else 0

    @classmethod
    def get_mega_jackpot_threshold(cls):
        return cls.objects.all().last().mega_jackpot_threshold if cls.objects.all().last() else 0

    @classmethod
    def get_super_jackpot_threshold(cls):
        return cls.objects.all().last().super_jackpot_threshold if cls.objects.all().last() else 0

    @classmethod
    def get_icash_jackpot_sharing_perc(cls):
        icash_sharing = cls.objects.all().last().icash_super_jackpot_sharing if cls.objects.all().last() else None

        if icash_sharing is None:
            return [0, 0, 0, 0]

        else:
            icash_sharing_list = icash_sharing.split(",")
            if len(icash_sharing_list) == 4:
                return [float(i) for i in icash_sharing_list]
            elif len(icash_sharing_list) == 2:
                return [float(icash_sharing_list[0]), float(icash_sharing_list[1]), 0]
            elif len(icash_sharing_list) == 1:
                return [float(icash_sharing_list[0]), 0, 0]
            else:
                return [0, 0, 0, 0]


class IPWhitelist(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    server = models.CharField(max_length=255, blank=True, null=True)
    ip_address = models.CharField(max_length=255, blank=True, null=True)
    allowed = models.BooleanField(default=False)

    def __str__(self):
        return self.ip_address

    class Meta:
        verbose_name = "WHITE LISTED IP"
        verbose_name_plural = "WHITE LISTED IP"


class S4LCombos(models.Model):
    updated_at = models.DateTimeField(auto_now=True)
    combo = models.CharField(default=0, max_length=50)
    value = models.FloatField(default=0)
    winners = models.FloatField(default=0)

    class Meta:
        verbose_name_plural = "S4L COMBOS"
        indexes = [
            models.Index(
                fields=[
                    "combo",
                ]
            ),
        ]


class MaxWithdrawalThresold(models.Model):
    phone_number = models.CharField(max_length=100)
    total_amount_withdrawn = models.FloatField(default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "MAX WITHDRAWAL THRESOLD"
        verbose_name_plural = "MAX WITHDRAWAL THRESOLD"

    @classmethod
    def check_and_update_thresold(cls, phone_number: str, amount: float):
        todays_date = datetime.now().date()

        try:
            instance = cls.objects.get(created_at__date=todays_date, phone_number=phone_number)
        except cls.DoesNotExist:
            instance = cls.objects.create(phone_number=phone_number)

        new_amount = instance.total_amount_withdrawn + amount

        if new_amount > 20000:
            return True

        instance.total_amount_withdrawn = new_amount
        instance.save()

        return False


class Waitlist(models.Model):
    """
    Model for managing waitlist signups
    """

    first_name = models.CharField(max_length=100)
    email = models.EmailField(unique=True)
    account_number = models.CharField(max_length=10, blank=True, null=True)  # Added account number field
    account_name = models.CharField(max_length=100, blank=True, null=True)  # Added account name field
    bank_name = models.CharField(max_length=100, blank=True, null=True)  # Added account name field
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "WAITLIST SIGNUP"
        verbose_name_plural = "WAITLIST SIGNUPS"
        indexes = [
            models.Index(fields=["email"], name="waitlist_email_idx"),
            models.Index(fields=["created_at"], name="waitlist_date_idx"),
        ]

    def __str__(self):
        return f"{self.first_name} - {self.email}"


class WemaInstantPaymentAccounts(models.Model):
    PURPOSE = [
        ("GAME_PLAY", "GAME_PLAY"),
    ]

    LOTTERY_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("WHYSE_LOAN", "WHYSE_LOAN"),
        ("WHYSE_CASH_BUSINESS", "WHYSE_CASH_BUSINESS"),
    ]

    phone_number = models.CharField(max_length=250)
    account_number = models.CharField(max_length=250, blank=True, null=True)
    account_name = models.CharField(max_length=250, blank=True, null=True)
    bank_name = models.CharField(max_length=250, blank=True, null=True)
    bank_code = models.CharField(max_length=250, blank=True, null=True)
    amount = models.FloatField(default=0.0)
    reference = models.CharField(max_length=250, unique=True)
    response_payload = models.TextField(null=True, blank=True)
    purpose = models.CharField(max_length=250, choices=PURPOSE, default="GAME_PLAY")
    payment_received = models.BooleanField(default=False)
    payment_response_payload = models.TextField(null=True, blank=True)
    game_id = models.CharField(max_length=300, blank=True, null=True)
    lottery_type = models.CharField(max_length=150, choices=LOTTERY_TYPE, default="WHYSE_LOAN")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "WEMA INSTANT PAYMENT ACCOUNT"
        verbose_name_plural = "WEMA INSTANT PAYMENT ACCOUNTS"

    @classmethod
    def create_record(cls, phone_number, lottery_type, game_id, purpose="GAME_PLAY"):
        """
        Creates a new record for a lottery entry and initiates an account transfer.

        This method generates a unique reference for the lottery entry using the provided
        phone number and a UUID. It creates a new instance of the class with the specified
        phone number, lottery type, game ID, and purpose. It then calls the WemaBank API
        to initiate an account transfer and retrieves the response.

        If the transfer is successful, it updates the instance with the account details
        returned from the API and saves the instance.

        Args:
            phone_number (str): The phone number associated with the lottery entry.
            lottery_type (str): The type of lottery for the entry.
            game_id (str): The ID of the game associated with the lottery entry.
            purpose (str, optional): The purpose of the record, defaults to "GAME_PLAY".

        Returns:
            dict: A dictionary containing the account number, account name, and bank name
            if the transfer is successful; otherwise, it returns a dictionary with None values.
        """

        from wema.wema_manager import WemaBank

        reference = f"{phone_number}_{str(uuid.uuid4())}"
        instance = cls.objects.create(
            phone_number=phone_number, purpose=purpose, reference=reference, lottery_type=lottery_type, game_id=game_id
        )

        transfer_response = WemaBank().instant_accounts(reference)
        instance.response_payload = transfer_response
        instance.save()

        data = {
            "account_number": None,
            "account_name": None,
            "bank_name": None,
        }

        if isinstance(transfer_response, dict):
            if transfer_response.get("status") == "success":
                instance.account_number = (
                    transfer_response.get("data", {}).get("account_details", {}).get("account_number")
                )
                instance.account_name = transfer_response.get("data").get("account_details", {}).get("account_name")
                instance.bank_name = transfer_response.get("data").get("account_details", {}).get("bank_name")
                instance.bank_code = transfer_response.get("data").get("account_details", {}).get("bank_code")
                instance.save()

                data = {
                    "account_number": instance.account_number,
                    "account_name": instance.account_name,
                    "bank_name": instance.bank_name,
                }

        return data

    @classmethod
    def receive_payment(cls, account_number, amount, reference, raw_payload):
        """
        Processes a payment for a given account number and amount.

        This method checks for an existing payment instance that matches the provided
        account number and amount, and has not yet been marked as received. If such an
        instance is found, it updates the instance to mark the payment as received and
        stores the raw payload of the payment response.

        Check the lottery type of the instrance it also updates related
        lottery entries by marking them as paid and distributing the payment amount
        evenly among them.

        Args:
            account_number (str): The account number associated with the payment.
            amount (float): The amount of the payment being processed.
            raw_payload (dict): The raw payload data received from the payment processor.

        Returns:
            None: This method does not return a value. It modifies the database entries
            directly.
        """

        print("RECEIVING PAYMENT", "\n\n")

        instance = cls.objects.filter(
            account_number=account_number, reference=reference, payment_received=False
        ).last()

        if instance:

            instance.payment_received = True
            instance.payment_response_payload = raw_payload
            instance.save()

            if instance.lottery_type == "WYSE_CASH":
                wyse_cash = LotteryModel.objects.filter(game_play_id=instance.game_id, paid=False)
                if wyse_cash.exists():
                    for wyse in wyse_cash:
                        wyse.paid = True
                        wyse.amount_paid = amount
                        wyse.save()

        return None


class Seeder(models.Model):
    batch = models.CharField(max_length=14)
    date = models.DateField()
    seed_time = models.TimeField()
    wave = models.CharField(max_length=50)
    seed_value = models.FloatField(default=0)
    gross_sales = models.FloatField(default=200)
    rtp = models.FloatField(default=0)
    rtp_seeder = models.FloatField(default=0)
    rto = models.FloatField(default=0)
    rto_percent = models.FloatField(default=0)
    agent_commission = models.FloatField(default=0)
    seed_ratio = models.FloatField(default=0)
    gain_loss = models.FloatField(default=0)
    effective_rtp = models.FloatField(default=0)
    rtp_multiplier = models.FloatField(default=0)
    effective_multiplier = models.IntegerField(default=0)
    effective_rtp_before = models.FloatField(default=0)
    rtp_game_type = models.CharField(
        max_length=100,
        choices=(
            ("Instant Games", "Instant Games"),
            ("Banker", "Banker"),
            ("Kenya", "Kenya"),
            ("Kenya 30", "Kenya 30"),
        ),
    )
    is_seeder_active = models.BooleanField(default=False)
    is_seeder_close = models.BooleanField(default=False)
    release_after_event = models.IntegerField(default=0)
    count_after_break = models.IntegerField(default=0)
    used_winning_count = models.IntegerField(default=0)
    burst_amount = models.FloatField(default=0)
    seeder_rtp_trigger = models.FloatField(default=0)
    seeder_level = models.CharField(max_length=255, choices=(("LOCAL", "LOCAL"), ("GLOBAL", "GLOBAL")))
    is_on_break = models.BooleanField(
        default=False, help_text="tells if the seeder (especially the rapid fire) has gone on break"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class RapidFireWinning(models.Model):
    amount = models.FloatField(default=0.0)
    batch = models.CharField(max_length=255)
    release_at = models.PositiveIntegerField(default=0)
    games_count = models.PositiveIntegerField(default=0)
    is_used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class SalaryForLifeSponsor(models.Model):
    brand_name = models.CharField(max_length=255, null=True, blank=True)
    designation = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField()
    phone_number = models.CharField(max_length=255, null=True, blank=True)
    date_created = models.DateTimeField(_("date_created"), auto_now_add=True)

    def __str__(self):
        return f"{self.brand_name} | {self.email}"

    class Meta:
        verbose_name = "SALARY FOR LIFE SPONSOR"
        verbose_name_plural = "SALARY FOR LIFE SPONSORS"


class SalaryForLifeParticipant(models.Model):
    name = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField()
    phone_number = models.CharField(max_length=255, null=True, blank=True)
    location = models.CharField(max_length=255)
    referrer = models.CharField(max_length=255, null=True, blank=True)
    date_created = models.DateTimeField(_("date_created"), auto_now_add=True)

    def __str__(self):
        return f"{self.name} | {self.email}"

    class Meta:
        verbose_name = "SALARY FOR LIFE PARTICIPANT"
        verbose_name_plural = "SALARY FOR LIFE PARTICIPANTS"


class DigitalOceanDroplet(models.Model):
    """ "
    For managing salary for life draw digital ocean instances.
    """

    droplet_id = models.CharField(max_length=1200, null=True, blank=True)
    initiation_response = models.TextField(blank=True, null=True)
    deletion_response = models.TextField(blank=True, null=True)
    is_killed = models.BooleanField(default=False)
    date_killed = models.DateTimeField(_("date_killed"), blank=True, null=True)
    date_created = models.DateTimeField(_("date_created"), auto_now_add=True)
    date_updated = models.DateTimeField(_("date_updated"), auto_now=True)

    class Meta:
        verbose_name = "DIGITAL OCEAN DROPLET - salary for life"
        verbose_name_plural = "DIGITAL OCEAN DROPLETS - salary for life"

    @classmethod
    def kill_digital_ocean_droplet(cls, droplet_id):
        """ "
        Reusable method for killing digital ocean droplet.
        """
        try:
            droplet_instance = cls.objects.get(droplet_id=droplet_id)
        except DigitalOceanDroplet.DoesNotExist:
            return "Droplet not found"

        if droplet_instance.is_killed:
            return

        statement, response = cloud_init.delete_droplet(
            DIGITALOCEAN_KEY=settings.DIGITALOCEAN_KEY, DROPLET_ID=droplet_id
        )

        droplet_instance.deletion_response = response
        droplet_instance.is_killed = statement
        droplet_instance.date_killed = timezone.now()
        droplet_instance.save()

        return "Killed successfully"


class ErroneousTransferRefundLog(models.Model):
    phone_number = models.CharField(max_length=255)
    requested_by = models.CharField(max_length=255)
    amount = models.FloatField(default=0.0)
    reference = models.CharField(max_length=255, blank=True, null=True)
    request_payload = models.TextField()
    response_payload = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "ERRONEOUS TRANSFER REFUND LOG"
        verbose_name_plural = "ERRONEOUS TRANSFER REFUND LOGS"

    @classmethod
    def create_log(cls, phone_number, requested_by, amount):

        from pos_app.pos_helpers import agent_login

        payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        payload = {
            "from_wallet_type": "COLLECTION",
            "to_wallet_type": "COLLECTION",
            "data": [
                {
                    "buddy_phone_number": phone_number,
                    "amount": amount,
                    "narration": "Erroneous Refund",
                    "is_beneficiary": "False",
                    "save_beneficiary": "True",
                    "remove_beneficiary": "False",
                    "is_recurring": "False",
                    "customer_reference": payout_reference,
                }
            ],
        }

        instance = cls.objects.create(
            phone_number=phone_number,
            requested_by=requested_by,
            amount=amount,
            request_payload=payload,
            reference=payout_reference,
        )
        cp_payload = payload.copy()

        cp_payload["transaction_pin"] = config("AGENCY_BANKING_GHANA_LOTTO_WALLET_TRANSACTION_PIN")

        token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")

        url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        try:
            response = requests.post(url, headers=headers, json=cp_payload)

            if response.status_code == 401:
                redis_db = redis.StrictRedis(
                    host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                )
                redis_db.delete(f"agent_login_token")

                return cls.create_log(phone_number=phone_number, requested_by=requested_by, amount=amount)

        except requests.exceptions.RequestException:
            return None

        try:
            instance.response_payload = response.json()
        except Exception:
            instance.response_payload = response.text
        instance.save()

        return instance.response_payload


class ContactUsForm(models.Model):
    full_name = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField()
    phone_no = models.CharField(max_length=255, null=True, blank=True)
    message = models.TextField()
    date_created = models.DateTimeField(_("date_created"), auto_now_add=True)

    class Meta:
        verbose_name = "CONTACT US FORM"
        verbose_name = "CONTACT US FORMS"

    def __str__(self):
        return f"{self.full_name}'s Message"


class DrawLog(models.Model):
    """
    Model to track draw execution times and monitor for missed draws.

    This model creates an object at every draw and enables email alerts when any draw
    does not run within its expected frequency. The last draw time is compared to the
    frequency_minutes to determine if an alarm should be raised by sending an email.

    The monitoring system compares the time difference between the last_run_time and
    the current time against the frequency_minutes to detect draw failures and trigger
    email notifications to administrators.
    """

    draw_name = models.CharField(
        max_length=255,
        help_text="Name of the draw/lottery game being monitored (e.g., 'INSTANT CASH', 'SALARY FOR LIFE')",
    )
    last_run_time = models.DateTimeField(
        default=timezone.now, help_text="Timestamp of when this draw was last executed successfully"
    )
    frequency_minutes = models.IntegerField(
        help_text="Expected frequency in minutes between draw executions. Used to determine if a draw is overdue"
    )

    def __str__(self):
        return f"{self.draw_name} (Last Run: {self.last_run_time}, Frequency: {self.frequency_minutes} mins)"

    @classmethod
    def create_draw_log(cls, draw_name, frequency_minutes):
        """
        Create a new draw log entry for monitoring draw execution.

        Args:
            draw_name (str): Name of the draw/lottery game to monitor
            frequency_minutes (int): Expected frequency in minutes between executions

        Returns:
            DrawLog: The created DrawLog instance
        """
        return cls.objects.create(draw_name=draw_name, frequency_minutes=frequency_minutes)


class ScratchCardPartnership(models.Model):
    name = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField()
    phone_number = models.CharField(max_length=255, null=True, blank=True)
    location = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(_("date_created"), auto_now_add=True)

    def __str__(self):
        return f"{self.name}"

    class Meta:
        verbose_name = "SCRATCH CARD PARTNERSHIP"
        verbose_name_plural = "SCRATCH CARD PARTNERSHIPS"


class DrawWinnersLog(models.Model):
    LOTTO_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("QUIKA", "QUIKA"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("BANKER", "BANKER"),
    ]

    game_type = models.CharField(max_length=100, choices=LOTTO_TYPE, default="BANKER")
    game_play_id = models.CharField(max_length=100, blank=True, null=True)
    batch_uuid = models.CharField(max_length=100, blank=True, null=True)
    amount_won = models.FloatField(default=0.0)
    ticket = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "DRAW WINNERS LOG"
        verbose_name_plural = "DRAW WINNERS LOGS"


class GamePlayEscrow(models.Model):

    LOTTERY_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("BANKER", "BANKER"),
        ("QUIKA", "QUIKA"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("NIGERIA_LOTTO", "NIGERIA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW"),
    ]

    game_play_id = models.CharField(max_length=100, blank=True, null=True)
    game_type = models.CharField(max_length=100, choices=LOTTERY_TYPE_CHOICES, default="BANKER")
    batch_uuid = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GAME PLAY ESCROW"
        verbose_name_plural = "GAME PLAY ESCROWS"
