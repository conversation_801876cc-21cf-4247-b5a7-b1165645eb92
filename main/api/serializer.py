from rest_framework import serializers

from account.models import User
from main.models import LotteryModel, LotteryWinnersTable, TestimonalTable, UserProfile
from main.ussd.helpers import Utility


class LoginSerializer(serializers.Serializer):  # noqa
    email = serializers.EmailField()
    password = serializers.CharField()


class ResetPasswordSerializer(serializers.Serializer):  # noqa
    email = serializers.EmailField()


class ResetPasswordConfirmationSerializer(serializers.Serializer):  # noqa
    uuid = serializers.CharField()
    token = serializers.CharField()
    password = serializers.CharField()
    confirm_password = serializers.Char<PERSON>ield()

    def validate(self, data):
        if data["password"] != data["confirm_password"]:
            raise serializers.ValidationError("password does not match")
        return data


class ChangePasswordSerializer(serializers.Serializer):  # noqa
    old_password = serializers.CharField()
    new_password = serializers.Char<PERSON><PERSON>()
    confirm_password = serializers.Cha<PERSON><PERSON><PERSON>()

    def validate(self, data):
        if data["new_password"] != data["confirm_password"]:
            raise serializers.ValidationError("password does not match")
        return data


class SignupSerializer(serializers.Serializer):  # noqa
    full_name = serializers.CharField()
    email = serializers.EmailField()
    phone = serializers.CharField()
    referral_code = serializers.CharField(allow_blank=True, allow_null=True)
    gender = serializers.CharField()
    password = serializers.CharField()

    extra_kwargs = {
        "referral_code": {"required": False, "allow_null": True, "allow_blank": True},
    }


class WinnerSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryWinnersTable
        fields = [
            "id",
            "unnique_id",
            "playyer_id",
            "win_type",
            "pool",
            "share",
            "stake_amount",
            "earning",
            "total_jackpot_amount",
            "date_won",
        ]

    def to_representation(self, obj):
        serialized_data = super(WinnerSerializer, self).to_representation(obj)
        serialized_data["earning"] = "N0" if serialized_data.get("earning") is None else Utility.currency_formatter(serialized_data.get("earning"))
        serialized_data["stake_amount"] = (
            "N0" if serialized_data.get("stake_amount") is None else Utility.currency_formatter(serialized_data.get("stake_amount"))
        )
        serialized_data["total_jackpot_amount"] = (
            "N0" if serialized_data.get("total_jackpot_amount") is None else Utility.currency_formatter(serialized_data.get("total_jackpot_amount"))
        )
        serialized_data["share"] = "N0" if serialized_data.get("share") is None else Utility.currency_formatter(serialized_data.get("share"))
        serialized_data["phone_number"] = obj.phone_number
        return serialized_data


class UserProfileSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = UserProfile
        fields = [
            "phone_number",
            "email",
            "first_name",
            "last_name",
            "middle_name",
            "account_num",
            "account_name",
            "bank_name",
            "bank_code",
            "bvn_number",
            "gender",
            "channel",
            "verification_code",
        ]

    def to_representation(self, obj):
        serialized_data = super(UserProfileSerializer, self).to_representation(obj)
        serialized_data["referalcode"] = obj.referalcode
        serialized_data["referal_url"] = obj.referal_url
        serialized_data["referal_wallet_balance"] = obj.referral_wallet_balance
        serialized_data["play_balance"] = obj.play_wallet_balance
        serialized_data["winning_balance"] = obj.winning_wallet_balance
        serialized_data["referrals_quantity"] = obj.referred_count
        serialized_data["wallet_balance"] = obj.wallet_balance
        serialized_data["phone_number"] = obj.phone_number if obj.has_sudo_phone_number is False else None
        try:
            serialized_data["phone_number_verified"] = User.objects.filter(phone=obj.phone_number).last().phone_is_verified
        except Exception:
            serialized_data["phone_number_verified"] = False

        image_url = None
        # if str(obj.profile_img) != "None":
        # image_url = str(obj.profile_img)
        if str(obj.avatar) != "None":
            image_url = str(obj.avatar)
        serialized_data["user_profile_image"] = image_url

        # # get user virtual account details
        # _account_details = WovenAccountDetail.objects.filter(
        #     phone_number=obj.phone_number, bank_name="WEMA BANK"
        # ).last()
        # if _account_details:
        #     accounts = {
        #         "account_number": _account_details.vnuban,
        #         "account_name": _account_details.acct_name,
        #         "bank_name": _account_details.bank_name,
        #     }
        #     serialized_data["account_details"] = accounts
        # else:
        # create_wema_collection_account(phone_number=obj.phone_number)

        # _account_details = WovenAccountDetail.objects.filter(
        #     phone_number=obj.phone_number, bank_name="WEMA BANK"
        # ).last()
        # if _account_details:
        #     accounts = {
        #         "account_number": _account_details.vnuban,
        #         "account_name": _account_details.acct_name,
        #         "bank_name": _account_details.bank_name,
        #     }
        #     serialized_data["account_details"] = accounts
        # else:
        #     accounts = {
        #         "account_number": None,
        #         "account_name": None,
        #         "bank_name": None,
        #     }
        #     serialized_data["account_details"] = accounts
        return serialized_data


class UpdateUserProfileSerializer(serializers.Serializer):
    profile_img = serializers.ImageField(required=False, allow_null=True)
    email = serializers.EmailField(required=False, allow_null=True)
    phone_number = serializers.CharField(required=False, allow_null=True)
    gender = serializers.CharField(required=False, allow_null=True)
    bvn = serializers.CharField(required=False, allow_null=True)
    first_name = serializers.CharField(required=False, allow_null=True)
    last_name = serializers.CharField(required=False, allow_null=True)


# class ApiLotterySerializer(serializers.ModelSerializer):
#     class Meta:
#         model = LotteryModel
#         fields = ["band", "stake_amount"]
class ApiLotterySerializer(serializers.Serializer):  # noqa
    # band = serializers.CharField(required=True)
    stake_amount = serializers.FloatField(required=True)
    lucky_number = serializers.CharField(required=True)
    consent = serializers.BooleanField(default=True)
    band = serializers.CharField(required=True)
    # fields = ["band", "stake_amount", "lucky_number", "consent"]


class DataSetSerializer(serializers.Serializer):  # noqa
    ten = serializers.ListSerializer(child=ApiLotterySerializer(), required=False)
    fifty = serializers.ListSerializer(child=ApiLotterySerializer(), required=False)
    hundred = serializers.ListSerializer(child=ApiLotterySerializer(), required=False)
    two_hundred = serializers.ListSerializer(child=ApiLotterySerializer(), required=False)
    # ten = serializers.ListSerializer(child=ApiLotterySerializer())


class LotteryPaymentSerializer(serializers.Serializer):  # noqa
    from_referral_wallet = serializers.BooleanField(required=True)
    from_play_wallet = serializers.BooleanField(required=True)
    paystack = serializers.BooleanField(required=True)
    amount = serializers.FloatField(required=True)
    game_play_id = serializers.CharField()
    # pin = serializers.CharField(required=False, allow_blank = True)

    def validate(self, data):
        if (
            data["from_referral_wallet"]
            and data["from_play_wallet"]
            or data["from_referral_wallet"]
            and data["paystack"]
            or data["from_play_wallet"]
            and data["paystack"]
        ):
            raise serializers.ValidationError("Please select only one payment method")

        if not data["from_referral_wallet"] and not data["from_play_wallet"] and not data["paystack"]:
            raise serializers.ValidationError("please select at least one payment method")

        return data


class WyseCashLotteryPaymentSerializer(serializers.Serializer):
    from_referral_wallet = serializers.BooleanField(required=True)
    from_main_wallet = serializers.BooleanField(required=True)
    paystack = serializers.BooleanField(required=True)
    amount = serializers.FloatField(required=True)
    # game_play_id = serializers.CharField()
    pin = serializers.CharField(required=False, allow_blank=True)

    def validate(self, data):
        if (
            data["from_referral_wallet"]
            and data["from_main_wallet"]
            or data["from_referral_wallet"]
            and data["paystack"]
            or data["from_main_wallet"]
            and data["paystack"]
        ):
            raise serializers.ValidationError("Please select only one payment method")

        if not data["from_referral_wallet"] and not data["from_main_wallet"] and not data["paystack"]:
            raise serializers.ValidationError("please select at least one payment method")

        return data


class UserLotteryApplicationSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryModel
        fields = ["id", "lucky_number", "game_play_id", "paid", "batch", "stake_amount"]

    def to_representation(self, obj):
        serialized_data = super(UserLotteryApplicationSerializer, self).to_representation(obj)
        serialized_data["date"] = obj.date.date()

        return serialized_data


class TestimonialSerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = TestimonalTable
        fields = ["id", "message", "date_added"]

        extra_kwargs = {
            "message": {"required": False, "allow_null": True},
            "date_added": {"required": True, "allow_null": False},
        }

    def to_representation(self, obj):
        serialized_data = super(TestimonialSerializer, self).to_representation(obj)
        serialized_data["image"] = str(obj.user.profile_img)
        serialized_data["name"] = obj.user.first_name + " " + obj.user.last_name
        return serialized_data


class ReferralCampaignSerializer(serializers.Serializer):
    first_name = serializers.CharField()
    phone_no = serializers.CharField()


class ReArrangeLotterySerializer(serializers.Serializer):
    game_play_id = serializers.CharField()
