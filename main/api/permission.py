import json

from rest_framework import permissions

from main.api.exceptions.execptions import (
    EntryDisallowedException,
    PaymentDisallowedException,
)
from main.models import UserProfile


class CanLogin(permissions.BasePermission):
    """ """

    def is_authenticated(self, token):
        return UserProfile.objects.filter(auth_code=token)

    def has_permission(self, request, view):
        auth_token = request.headers.get("Authorization")
        if auth_token is None:
            return False
        user = self.is_authenticated(auth_token)

        if user.exists():
            request.user = user.last()

            return True

        else:
            raise EntryDisallowedException()


class ValidLotteryAmount(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.user is not None:
            try:
                request_body = dict(request.data)
                # request_body = dict(request.POST.items())
            except Exception:
                request_body = request.body
                request_body = json.loads(request_body.decode("utf-8").replace("'", '"'))

            # validate pin
            from_referral_wallet = request_body.get("from_referral_wallet")
            from_main_wallet = request_body.get("from_main_wallet")
            # pin = request_body.get("pin")
            paystack = request_body.get("paystack")

            request_body.get("amount", None)

            if from_referral_wallet is not False or from_main_wallet is not False:
                pass
                # if request_body.get("pin") is None:
                #     raise TransactionPinException()
                # else:
                #     if request.user.pin is None or request.user.pin == "":
                #         raise NoTransactionPinException()
                #     elif check_password(pin, request.user.pin) is False:
                #         raise InvalidTransactionPinException()
                #     else:

                #         if amount is not None:
                #             is_lottery_valid = LotteryModel.objects.filter(user_profile = request.user, expected_amount=float(amount), paid = False).exists()
                #             if is_lottery_valid:
                #                 return True
                #             else:
                #                 raise PaymentDisallowedException()
                #         else:
                #             raise AmountException()

                # user_profile = UserProfile.objects.filter(phone_number=request.user.phone)

                # if amount is not None:
                #     is_lottery_valid = LotteryModel.objects.filter(
                #         user_profile=user_profile,
                #         expected_amount=float(amount),
                #         paid=False,
                #     ).exists()
                #     if is_lottery_valid:
                #         return True
                #     else:
                #         raise PaymentDisallowedException()
                # else:
                #     raise AmountException()

            elif paystack is not False:
                # is_lottery_valid = LotteryModel.objects.filter(user_profile = user_profile, expected_amount=float(amount), paid = False).exists()
                # if is_lottery_valid:
                #     return True
                # else:
                #     raise PaymentDisallowedException()

                return True
            else:
                return PaymentDisallowedException()

        else:
            raise EntryDisallowedException()


class UpstreamDataSyncPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            x_forwarded_for.split(",")[0]
        else:
            request.META.get("REMOTE_ADDR")

        request_data = request.body

        data_str = request_data.decode("utf-8")

        try:
            result_dict = json.loads(data_str)
        except Exception:
            result_dict = {}

            return True

        trans_ref = result_dict.get("trxId")

        if "upstream" in trans_ref:
            pass
