import requests
from rest_framework import status

from main.helpers.loandisk import full_name_split
from main.models import LotteryModel, UserProfile, WovenAccountDetail
from referral_system.models import ReferralCode, ReferralTable
from wallet_app.tasks import create_woven_virtual_wallet


class WhisperApiHelper:
    def __init__(self):
        self.base_url = "https://whispersms.xyz"

    def login(self, **kwargs):
        url = f"{self.base_url}/auth/token/login/"

        payload = {
            "email": kwargs.get("email"),
            "password": kwargs.get("password"),
        }
        headers = {"Content-Type": "application/json"}

        response = requests.request("POST", url, headers=headers, json=payload)

        res = response.json()

        if isinstance(res, dict) and res.get("auth_token") is not None:
            # check if user exist in Lotto User Profile model
            whisper_user = self.get_user_details(res.get("auth_token"))

            if isinstance(whisper_user, dict) and whisper_user.get("first_name") is not None:
                phone = LotteryModel.format_number_from_back_add_234(whisper_user.get("phone"))
                user = UserProfile.objects.filter(phone_number=phone).last()
                if user is None:
                    UserProfile.objects.create(
                        phone_number=phone,
                        email=whisper_user.get("email"),
                        first_name=whisper_user.get("first_name"),
                        last_name=whisper_user.get("last_name"),
                        account_name=f"{whisper_user.get('first_name')} {whisper_user.get('last_name')}",
                        auth_code=res.get("auth_token"),
                        channel="WEB",
                    )

                else:
                    user.auth_code = res.get("auth_token")
                    user.save()

                    # check if user has active virtual account
                    _woven_account_details = WovenAccountDetail.objects.filter(
                        phone_number=user.phone_number, wallet_tag="WEB", is_active=True
                    ).last()
                    if _woven_account_details is None:
                        create_woven_virtual_wallet.delay(user.id)

                    if user.channel == "USSD":
                        user.channel = "USSD/WEB"
                        user.save()

        status_code = response.status_code
        response = response.json()
        response["status"] = status_code
        return response

    def create_user(self, **kwargs):
        """ """

        url = f"{self.base_url}/auth/users/"

        email = kwargs.get("email")
        splited_name = full_name_split(kwargs.get("full_name"))
        gender = kwargs.get("gender")
        bvn = kwargs.get("bvn")
        phone = LotteryModel.format_number_from_back_add_234(kwargs.get("phone"))

        payload = {
            "email": email,
            "first_name": splited_name.get("first_name"),
            "last_name": splited_name.get("last_name"),
            "password": kwargs.get("password"),
            "re_password": kwargs.get("password"),
            "phone": phone,
        }

        headers = {"Content-Type": "application/json"}

        response = requests.request("POST", url, headers=headers, json=payload)

        res = response.json()
        if isinstance(res, dict) and res.get("first_name") == splited_name.get("first_name"):
            user_profile = UserProfile.objects.filter(phone_number=phone)
            if user_profile.exists():
                user = user_profile.last()
                user.email = email
                user.first_name = splited_name.get("first_name")
                user.last_name = splited_name.get("last_name")
                user.account_name = splited_name.get("full_name")
                user.bvn_number = bvn
                user.gender = gender.upper()
                user.save()

                # re-save user channel
                if user.channel == "USSD":
                    user.channel = "USSD/WEB"
                    user.save()

                # check referral code
                referral_code = kwargs.get("referral_code")
                if referral_code is not None:
                    # get referral code instance
                    referral_code_instance = ReferralCode.objects.filter(referral_code=referral_code).last()

                    # generate referral code
                    get_code = ReferralCode.create_referal_code(user)

                    # create referral code in ReferralTable
                    ReferralTable.objects.create(
                        user=user,
                        referral_code=get_code,
                        referred_by=referral_code_instance.user,
                    )

            else:
                user = UserProfile.objects.create(
                    phone_number=phone,
                    email=email,
                    first_name=splited_name.get("first_name"),
                    last_name=splited_name.get("last_name"),
                    middle_name=splited_name.get("middle_name"),
                    bvn_number=bvn,
                    account_name=splited_name.get("full_name"),
                    gender=gender.upper(),
                    channel="WEB",
                )

                referral_code = kwargs.get("referral_code")
                if referral_code is not None:
                    # get referral code instance
                    referral_code_instance = ReferralCode.objects.filter(referral_code=referral_code).last()

                    # generate referral code
                    get_user_code = ReferralCode.objects.filter(user__id=user.id).last()

                    # create referral code in ReferralTable
                    ReferralTable.objects.create(
                        user=user,
                        referral_code=get_user_code,
                        referred_by=referral_code_instance.user,
                    )

        status_code = response.status_code
        response = response.json()
        response["status"] = status_code
        return response

    def get_user_details(self, token):
        url = f"{self.base_url}/whisper/get_details"

        headers = {"Authorization": f"Token {token}"}

        response = requests.request("GET", url, headers=headers)

        return response.json()

    def reset_password(self, **kwargs):
        url = f"{self.base_url}/auth/users/reset_password/"

        user = UserProfile.objects.filter(email=kwargs.get("email")).last()
        if user is None:
            data = {
                "status": status.HTTP_404_NOT_FOUND,
                "message": "email does not exist.",
            }
            return data

        if user.auth_code is None:
            data = {
                "status": status.HTTP_404_NOT_FOUND,
                "message": "auth code does not exist. please login again.",
            }
            return data

        payload = {
            "email": kwargs.get("email"),
        }
        headers = {
            "Authorization": f"Token {user.auth_code}",
        }

        response = requests.request("POST", url, headers=headers, json=payload)

        print(user)
        print(response.text)
        # print(headers)
        print(kwargs.get("email"))

        if isinstance(response, list):
            if "email does not exist." in response[0]:
                data = {"status": status.HTTP_404_NOT_FOUND, "message": response[0]}
                return data

        elif response.status_code == 204:
            UserProfile.objects.filter(id=user.id).update(auth_code=None)
            data = {
                "status": status.HTTP_204_NO_CONTENT,
                "message": "Password reset link has been sent to your email",
            }
            return data

    def reset_password_confirmation(self, **kwargs):
        url = f"{self.base_url}/auth/users/reset_password_confirm/"

        payload = {
            "uid": kwargs.get("uuid"),
            "token": kwargs.get("token"),
            "new_password": kwargs.get("password"),
        }

        print(payload)

        headers = {"Content-Type": "application/json", "Accept": "application/json"}

        response = requests.request("POST", url, headers=headers, json=payload)

        if response.status_code:
            data = {"status": response.status_code, "message": response.text}
            return data

        data = {"status": response.status_code, "message": response.json()}
        return data

    def change_password(self, user, **kwargs):
        url = f"{self.base_url}/auth/users/set_password/"

        user = UserProfile.objects.filter(id=user.id).last()

        if user is None:
            data = {
                "status": status.HTTP_404_NOT_FOUND,
                "message": "user does not exist.",
            }
            return data

        payload = {
            "new_password": kwargs.get("new_password"),
            "current_password": kwargs.get("old_password"),
        }

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Token {user.auth_code}",
        }

        response = requests.request("POST", url, headers=headers, json=payload)

        if response.status_code:
            data = {"status": response.status_code, "message": response.text}
            return data

        data = {"status": response.status_code, "message": response.json()}
        return data
