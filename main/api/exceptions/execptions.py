from django.utils.encoding import force_str
from rest_framework import status
from rest_framework.exceptions import APIException


class EntryDisallowedException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "Sorry, You cannot access this page.",
    }
    default_code = "Not permitted"


class PaymentDisallowedException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "sorry, no pending lottery with these amount",
    }
    default_code = "Not permitted"


class AmountException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "amount field is required",
    }
    default_code = "Not permitted"


class TransactionPinException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "pin field is required",
    }
    default_code = "Not permitted"


class NoTransactionPinException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "you don't have transaction pin",
    }
    default_code = "Not permitted"


class InvalidTransactionPinException(APIException):
    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = {
        "error": "error",
        "message": "incorrect transaction pin",
    }
    default_code = "Not permitted"


class CustomErrorValidation(APIException):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = "A server error occurred."

    def __init__(self, detail, status_code):
        if status_code is not None:
            self.status_code = status_code
        if detail is not None:
            self.detail = force_str(detail)
            print(self.detail)
        else:
            self.detail = force_str(self.default_detail)
