import secrets
import string
import uuid
from datetime import datetime
from random import randint

from django.core.paginator import Paginator as django_core_paginator
from django.db.models import Count, Q, Sum
from django.utils import timezone
from rest_framework import status

from main.helpers.redis_storage import RedisStorage
from pos_app.utils import Dom<PERSON><PERSON>ame
from prices.game_price import WyseCashPriceModel
import time


def generate_pin():
    n = 6
    range_start = 10 ** (n - 1)
    range_end = (10**n) - 1
    value = randint(range_start, range_end)
    return str(value)


def check_batch_status(game_play_id) -> bool:
    """
    Check the batch status
    """
    from main.models import LotteryBatch, LotteryModel

    lottery_obj = LotteryModel.objects.filter(game_play_id=game_play_id)
    if lottery_obj.exists():
        batch_obj = LotteryBatch.objects.filter(id=lottery_obj.last().batch.id).last()

        if batch_obj and batch_obj.is_active is True:
            return True
        else:
            return False
    return False


def generate_lucky_number(user_instance, from_pos=False, from_mobile_agent=False):
    """
    Generate a lucky number for a user from web / mobile api
    """

    from main.models import LotteryModel

    if from_pos:
        name = DommyName(6).generate_name()
    elif from_mobile_agent:
        name = f"{user_instance.first_name} {user_instance.last_name}"
    else:
        if user_instance.account_name is None:
            name = DommyName(6).generate_name()
        else:
            name = user_instance.account_name

    loop_band = ["10000", "50000", "100000", "200000"]
    lucky_numbers = []
    pack_data = {}
    while len(lucky_numbers) < 4:

        def generate_lucky_number():
            n = 5
            range_start = 10 ** (n - 1)
            range_end = (10**n) - 1
            value = randint(range_start, range_end)
            return str(value)

        def full_name_split(name):
            """
            This functions split and return user names in a dictonary
            """
            splited_names = name.split()
            names = {
                "first_name": splited_names[0] if len(splited_names) > 0 else "",
                "last_name": splited_names[1] if len(splited_names) > 1 else "",
                "middle_name": splited_names[2] if len(splited_names) > 2 else "",
                "full_name": name,
            }

            return names

        names = full_name_split(name)
        lottery_generated = []

        while len(lottery_generated) < 10:
            lucky_number = f"{names.get('first_name')[0]}{names.get('last_name')[0]}-{generate_lucky_number()}"

            lottery_generated.append(lucky_number)

            lottery_model_obj = LotteryModel.objects.filter(lucky_number=lucky_number)
            if lottery_model_obj.exists():
                lottery_generated.pop()
                continue

        if from_pos is False:
            if len(loop_band) == 0:
                data = {
                    "status": status.HTTP_200_OK,
                    "message": "success",
                    "data": {
                        "lottery_data": pack_data,
                        "bands": {
                            "100": "10000",
                            "200": "50000",
                            "500": "100000",
                            "1000": "200000",
                        },
                    },
                }
                return data

            elif loop_band[0] == "10000":
                _band = []
                for e in lottery_generated:
                    _band += [{"number": e, "stake": "100"}]

                pack_data["100"] = _band

                loop_band.pop(0)

            elif loop_band[0] == "50000":
                _band = []
                for e in lottery_generated:
                    _band += [{"number": e, "stake": "200"}]

                pack_data["200"] = _band
                loop_band.pop(0)

            elif loop_band[0] == "100000":
                _band = []
                for e in lottery_generated:
                    _band += [{"number": e, "stake": "500"}]

                pack_data["500"] = _band

                loop_band.pop(0)

            elif loop_band[0] == "200000":
                _band = []
                for e in lottery_generated:
                    _band += [{"number": e, "stake": "1000"}]

                pack_data["1000"] = _band
                loop_band.pop(0)

        else:
            if len(loop_band) == 0:
                data = {
                    "status": status.HTTP_200_OK,
                    "message": "success",
                    "data": {
                        "lottery_data": pack_data,
                        "bands": {
                            "150": "10000",
                            "250": "50000",
                            "600": "100000",
                            "1200": "200000",
                        },
                    },
                }
                return data

            elif loop_band[0] == "10000":
                _band = []
                for e in lottery_generated:
                    _band += [{"number": e, "stake": "150"}]

                pack_data["150"] = _band

                loop_band.pop(0)

            elif loop_band[0] == "50000":
                _band = []
                for e in lottery_generated:
                    _band += [{"number": e, "stake": "250"}]

                pack_data["250"] = _band
                loop_band.pop(0)

            elif loop_band[0] == "100000":
                _band = []
                for e in lottery_generated:
                    _band += [{"number": e, "stake": "600"}]

                pack_data["600"] = _band

                loop_band.pop(0)

            elif loop_band[0] == "200000":
                _band = []
                for e in lottery_generated:
                    _band += [{"number": e, "stake": "1200"}]

                pack_data["1200"] = _band
                loop_band.pop(0)


def create_lottery(
    user,
    data,
    from_pos=False,
    player_instance=None,
    channel=None,
    lottery_type="WYSE_CASH",
):
    """
    Create a lottery for a user.
    the user in the parameter can be user instance or agent instance,
    depending if 'from_pos' is True or False.
    """
    unique_id_store = {}

    from main.models import ConstantVariable, LotteryBatch, LotteryModel
    from main.tasks import lottery_play_engange_event
    from wyse_ussd.models import UssdLotteryPayment

    # ------------------------------ CHECK BATCH STATUS ------------------------------ #
    if channel == "POS_AGENT":
        create_pos_duplicate_lottery = ConstantVariable.get_constant_variable().get("create_pos_duplicate_lottery")

        if create_pos_duplicate_lottery is True:
            current_batch = LotteryBatch.objects.filter(
                is_active=True,
                lottery_type=lottery_type,
            ).last()
            if not current_batch:
                current_batch = LotteryBatch.objects.create(
                    lottery_type=lottery_type,
                )
    else:
        current_batch = LotteryBatch.objects.filter(is_active=True, lottery_type=lottery_type).last()
        if not current_batch:
            current_batch = LotteryBatch.objects.create(lottery_type=lottery_type)

    # ------------------------------ CHECK BATCH STATUS ------------------------------ #

    get_game_play_id = generate_game_play_id()
    identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"
    pin = generate_pin()

    instance_num = get_lottery_count(data)

    _player_instance = player_instance if player_instance else user
    _player_phone = _player_instance.phone_number if _player_instance else user.phone_number

    _channel = channel if channel else "WEB"

    wyse_cash_price_model = WyseCashPriceModel()

    if from_pos:
        print("from_pos", from_pos)
        created_lottery = wyse_cash_price_model.bands_base_amount(channel="POS")
        price_model_channel = "POS"
    else:
        created_lottery = wyse_cash_price_model.bands_base_amount(channel="WEB")
        price_model_channel = "WEB"

    print("created_lottery", created_lottery, "\n\n")

    total_stake_amount = 0

    current_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH").last()

    generated_lottery_pin = generate_pin()

    for item in data:
        if item:
            for sub_item in item:
                obj_data = dict(sub_item)

                band = obj_data.get("band")

                get_unique_id = unique_id_store.get(band)
                if get_unique_id is None:
                    unique_id = f"{current_batch.batch_uuid[0:5]}-{str(uuid.uuid4())[0:5]}"
                    unique_id_store[band] = unique_id

                    unique_id = f"{current_batch.batch_uuid[0:5]}-{str(uuid.uuid4())[0:5]}"
                    unique_id_store[band] = unique_id

                pool = "TEN_THOUSAND"

                if band == "10000":
                    pool = "TEN_THOUSAND"
                    new_stake = 100
                elif band == "50000":
                    pool = "FIFTY_THOUSAND"
                    new_stake = 200
                elif band == "100000":
                    pool = "ONE_HUNDRED_THOUSAND"
                    new_stake = 500
                elif band == "200000":
                    pool = "TWO_HUNDRED_THOUSAND"
                    new_stake = 1000

                price_model = wyse_cash_price_model.ticket_price(channel=price_model_channel, band=int(band), no_of_line=1)
                expected_amount = (
                    price_model.get("stake_amount") + price_model.get("woven_service_charge", 0) + price_model.get("africastalking_charge", 0)
                )

                stake_amount = obj_data.get("stake_amount")
                lucky_number = obj_data.get("lucky_number")
                consent = obj_data.get("consent")

                # print(
                #     f"""
                #       stake_amount: {stake_amount}
                #       """
                # )

                total_stake_amount += stake_amount

                _lottery_instance = LotteryModel.objects.create(
                    user_profile=_player_instance,
                    batch=current_batch,
                    band=band,
                    phone=_player_phone,
                    pool=pool,
                    stake_amount=new_stake,
                    lucky_number=lucky_number,
                    consent=consent,
                    unique_id=unique_id_store[band],
                    instance_number=instance_num[band],
                    channel=_channel,
                    expected_amount=expected_amount,
                    game_play_id=get_game_play_id,
                    lottery_type=lottery_type,
                    pin=pin,
                    identity_id=identity_id,
                )

                print("stake_amount saved", stake_amount)

                if from_pos:
                    _lottery_instance.agent_profile = user
                    _lottery_instance.save()

                    """

                        CHECK if the channel is POS_AGENT and if it is,
                        check if the agent uses his phone number to play, if he does,
                        generate 6 digit pin for the ticket, for cashout if the case may be.
                    """

                    # if user.phone == _lottery_instance.agent_profile:
                    #     _lottery_instance.pin = generated_lottery_pin
                    #     _lottery_instance.save()

                    print(
                        f"""

                          int(band): {int(band)}
                          int(instance_num[band]: {int(instance_num[band])}

                          """
                    )
                    # update stake amount and pontential win amount
                    agent_wyse_cash_stake_amount = LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
                        band=int(band), no_of_line=1
                    )

                    # _lottery_instance.stake_amount = agent_wyse_cash_stake_amount.get(
                    #     "stake_amount"
                    # )
                    # _lottery_instance.band = agent_wyse_cash_stake_amount.get(
                    #     "total_winning_amount"
                    # )
                    # _lottery_instance.save()

                    created_lottery[str(stake_amount).replace(".0", "")].append(
                        {
                            "stake_amount": agent_wyse_cash_stake_amount.get("stake_amount"),
                            "lucky_number": lucky_number,
                            "consent": True,
                            "band": band,
                            "id": _lottery_instance.id,
                            "get_game_play_id": get_game_play_id,  # noqa
                            "pin": generated_lottery_pin,
                        },
                    )

                    # _lottery_instance.pin = generated_lottery_pin
                    # _lottery_instance.save()

                else:
                    try:
                        created_lottery[str(stake_amount).replace(".0", "")].append(
                            {
                                "stake_amount": stake_amount,
                                "lucky_number": lucky_number,
                                "id": _lottery_instance.id,
                                "consent": True,
                                "band": band,
                                "get_game_play_id": get_game_play_id,
                            },
                        )
                    except KeyError:
                        return {"status": "failed", "message": "invalid stake amount"}

                # check if duplicate ticket for POS channel is turned on

                create_pos_duplicate_lottery = ConstantVariable.get_constant_variable().get("create_pos_duplicate_lottery")

    UssdLotteryPayment.objects.create(
        user=_player_instance,
        amount=total_stake_amount,
        game_play_id=get_game_play_id,
        channel=channel,
        lottery_type="WYSE_CASH",
    )

    engage_event_payload = {
        "event": "WYSE CASH LOTTERY PLAY",
        "properties": {
            "game_id": get_game_play_id,
        },
    }
    lottery_play_engange_event.delay(user_id=user.id, is_user_profile_id=True, **engage_event_payload)

    # print("created_lottery", created_lottery)
    return created_lottery


def get_lottery_count(lottery_dict):
    """
    Get the count of lottery for a user
    """
    count_object = {
        "10000": 0,
        "50000": 0,
        "100000": 0,
        "200000": 0,
    }
    total_amount = 0
    for item in lottery_dict:
        if item:
            for e in item:
                band = e["band"]
                if band == "10000":
                    count_object["10000"] += 1
                    total_amount += 100
                elif band == "50000":
                    count_object["50000"] += 1
                    total_amount += 200
                elif band == "100000":
                    count_object["100000"] += 1
                    total_amount += 500
                elif band == "200000":
                    count_object["200000"] += 1
                    total_amount += 1000

    count_object["total_amount"] = total_amount
    return count_object


def generate_game_play_id():
    """
    Generate a unique 11-character game ID with the following constraints:
    - Contains exactly 3 lowercase letters
    - Contains exactly 8 digits
    - Excludes 'l' and '0' to avoid confusion
    - Minimizes database queries for better performance
    """
    # Create alphabet excluding 'l' and '0'
    letters = string.ascii_lowercase.replace('l', '')  # Remove 'l'
    digits = string.digits.replace('0', '')  # Remove '0' 
    alphabet = letters + digits
    
    max_attempts = 100  # Prevent infinite loops
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        
        # Generate ID with guaranteed constraints
        game_id = generate_constrained_id(letters, digits)
        
        # Single database query to check all models at once
        if not id_exists_in_database(game_id):
            return game_id
    
    # Fallback: use timestamp-based ID if max attempts reached
    return generate_timestamp_based_id()

def generate_constrained_id(letters, digits):
    """Generate an 11-character ID with exactly 3 letters and 8 digits"""
    # Ensure we have exactly 3 letters and 8 digits
    id_chars = []
    
    # Add exactly 3 random letters
    id_chars.extend(secrets.choice(letters) for _ in range(3))
    
    # Add exactly 8 random digits
    id_chars.extend(secrets.choice(digits) for _ in range(8))
    
    # Shuffle to randomize positions
    secrets.SystemRandom().shuffle(id_chars)
    
    return ''.join(id_chars)

def id_exists_in_database(game_id):
    """
    Check if game_id exists in any of the relevant tables with a single query approach
    Returns True if ID exists, False otherwise
    """
    from africa_lotto.models import AfricaLotto
    from awoof_app.models import AwoofGameTable
    from main.models import LotteryModel, LottoTicket
    from sport_app.models import SoccerOddPredictionTable
    from wyse_ussd.models import SoccerPrediction
    
    # Check each model - you could optimize this further with a union query
    # if your database supports it and all tables are in the same database
    
    checks = [
        LotteryModel.objects.filter(game_play_id=game_id).exists(),
        LottoTicket.objects.filter(game_play_id=game_id).exists(),
        SoccerPrediction.objects.filter(game_id=game_id).exists(),
        AwoofGameTable.objects.filter(Q(game_play_id=game_id) | Q(ticket_id=game_id)).exists(),
        SoccerOddPredictionTable.objects.filter(game_pay_id=game_id).exists(),
        AfricaLotto.objects.filter(game_play_id=game_id).exists(),
    ]
    
    return any(checks)

def generate_timestamp_based_id():
    """
    Fallback method using timestamp + random chars
    This virtually guarantees uniqueness
    """
    letters = string.ascii_lowercase.replace('l', '')
    digits = string.digits.replace('0', '')
    
    # Use microsecond timestamp for uniqueness
    timestamp = str(int(time.time() * 1000000))[-8:]  # Last 8 digits
    random_letters = ''.join(secrets.choice(letters) for _ in range(3))
    
    # Combine and shuffle
    chars = list(timestamp + random_letters)
    secrets.SystemRandom().shuffle(chars)
    
    return ''.join(chars)

# Alternative: UUID-based approach (even more collision-resistant)
def generate_game_play_id_uuid():
    """
    Alternative implementation using UUID for better collision resistance
    """
    import uuid
    
    letters = string.ascii_lowercase.replace('l', '')
    digits = string.digits.replace('0', '')
    
    # Generate UUID and extract characters
    uuid_str = str(uuid.uuid4()).replace('-', '')
    
    # Filter and map characters to our allowed set
    filtered_chars = []
    for char in uuid_str:
        if char.isdigit() and char != '0':
            filtered_chars.append(char)
        elif char.islower() and char != 'l':
            filtered_chars.append(char)
        elif char.isdigit() and char == '0':
            filtered_chars.append(secrets.choice(digits))
        elif char == 'l':
            filtered_chars.append(secrets.choice(letters))
    
    # Take first 11 characters and ensure constraints
    result = filtered_chars[:11]
    
    # Ensure we have exactly 3 letters and 8 digits
    letter_count = sum(1 for c in result if c.islower())
    digit_count = sum(1 for c in result if c.isdigit())
    
    if letter_count < 3:
        # Replace some digits with letters
        for i in range(len(result)):
            if result[i].isdigit() and letter_count < 3:
                result[i] = secrets.choice(letters)
                letter_count += 1
                digit_count -= 1
    
    if digit_count < 8:
        # Replace some letters with digits
        for i in range(len(result)):
            if result[i].islower() and digit_count < 8 and letter_count > 3:
                result[i] = secrets.choice(digits)
                digit_count += 1
                letter_count -= 1
    
    return ''.join(result)


def delete_lottery(id, user_profile=None):
    """
    Delete a lottery for a user
    """

    from main.models import LotteryModel
    from wyse_ussd.models import UssdLotteryPayment

    if user_profile is None:
        lottery_obj = LotteryModel.objects.filter(id=id).last()
    else:
        lottery_obj = LotteryModel.objects.filter(id=id, user_profile=user_profile).last()
    if lottery_obj is None:
        return None
    else:
        # update the instance number
        instance_obj = LotteryModel.objects.filter(unique_id=lottery_obj.unique_id)
        if instance_obj.count() > 1:
            instance_num = instance_obj.count() - 1
            instance_obj.update(instance_number=instance_num)

        stake_amount = lottery_obj.stake_amount
        expected_amount = lottery_obj.expected_amount
        new_expected_amount = expected_amount - stake_amount
        LotteryModel.objects.filter(game_play_id=lottery_obj.game_play_id).update(expected_amount=new_expected_amount)
        lottery_obj.delete()

        instance_obj = instance_obj.last()
        lottery_pending_payemnt_instance = UssdLotteryPayment.objects.filter(game_play_id=instance_obj.game_play_id).last()

        if lottery_pending_payemnt_instance:
            amount = LotteryModel.objects.filter(game_play_id=instance_obj.game_play_id).aggregate(sum=Sum("stake_amount")).get("sum")
            lottery_pending_payemnt_instance.amount = amount
            lottery_pending_payemnt_instance.save()

        return True


class Paginator:
    @staticmethod
    def paginate(request, queryset, page):
        request_get_data = request.GET
        if page is None or page == "":
            page = 1
        elif int(page) < 0:
            page = 1
        else:
            page = page

        paginator = django_core_paginator(queryset, int(request_get_data.get("size", 30)))
        requested_page = int(request_get_data.get("page", page))

        verified_page = requested_page if requested_page < paginator.num_pages else paginator.num_pages

        page = paginator.page(verified_page)

        return page


def user_lottery_application_filter_helper(user, serializer):
    """
    Filter the user lottery application base on data pass
    and format the data the way it can be used in the frontend
    """
    _date_list = []

    from main.models import LotteryBatch, LotteryWinnersTable

    data = {}

    for i in serializer.data:
        if i["date"] not in _date_list:
            _date_list.append(i["date"])

    for e in _date_list:
        data[str(e)] = []

        data[str(e)].append({"awaiting_payment": []})
        data[str(e)].append({"pending": []})
        data[str(e)].append({"cancelled": []})
        data[str(e)].append({"won": []})

    _game_play_id = []
    _game_play = {}

    for n in serializer.data:
        if n["paid"] is False:
            str(n["date"])

            if int(n["stake_amount"]) == 100:
                if n["game_play_id"] not in _game_play_id:
                    _game_play_id.append(n["game_play_id"])
                    _game_play[n["game_play_id"]] = {
                        "stake_amount": 100.0,
                        "winning_amount": 10000,
                        "date": str(n["date"]),
                        "application_status": "awaiting_payment",
                        "game_play_id": n["game_play_id"],
                        "is_batch_active": check_batch_status(n["game_play_id"]),
                        "data": [n],
                    }

                else:
                    _game_play[n["game_play_id"]]["stake_amount"] += 100.0
                    _game_play[n["game_play_id"]]["winning_amount"] += 10000
                    _game_play[n["game_play_id"]]["data"].append(n)

            elif int(n["stake_amount"]) == 200:
                if n["game_play_id"] not in _game_play_id:
                    _game_play_id.append(n["game_play_id"])
                    _game_play[n["game_play_id"]] = {
                        "stake_amount": 200.0,
                        "winning_amount": 50000,
                        "date": str(n["date"]),
                        "application_status": "awaiting_payment",
                        "game_play_id": n["game_play_id"],
                        "is_batch_active": check_batch_status(n["game_play_id"]),
                        "data": [n],
                    }

                else:
                    _game_play[n["game_play_id"]]["stake_amount"] += 200.0
                    _game_play[n["game_play_id"]]["winning_amount"] += 50000
                    _game_play[n["game_play_id"]]["data"].append(n)

            elif int(n["stake_amount"]) == 500:
                if n["game_play_id"] not in _game_play_id:
                    _game_play_id.append(n["game_play_id"])
                    _game_play[n["game_play_id"]] = {
                        "stake_amount": 500.0,
                        "winning_amount": 100000,
                        "date": str(n["date"]),
                        "application_status": "awaiting_payment",
                        "game_play_id": n["game_play_id"],
                        "is_batch_active": check_batch_status(n["game_play_id"]),
                        "data": [n],
                    }

                else:
                    _game_play[n["game_play_id"]]["stake_amount"] += 500.0
                    _game_play[n["game_play_id"]]["winning_amount"] += 100000
                    _game_play[n["game_play_id"]]["data"].append(n)

            elif int(n["stake_amount"]) == 1000:
                if n["game_play_id"] not in _game_play_id:
                    _game_play_id.append(n["game_play_id"])
                    _game_play[n["game_play_id"]] = {
                        "stake_amount": 1000.0,
                        "winning_amount": 200000,
                        "date": str(n["date"]),
                        "application_status": "awaiting_payment",
                        "game_play_id": n["game_play_id"],
                        "is_batch_active": check_batch_status(n["game_play_id"]),
                        "data": [n],
                    }

                else:
                    _game_play[n["game_play_id"]]["stake_amount"] += 1000.0
                    _game_play[n["game_play_id"]]["winning_amount"] += 200000
                    _game_play[n["game_play_id"]]["data"].append(n)

        elif n["paid"] is True:
            # check if batch is active
            _batch = LotteryBatch.objects.filter(id=n["batch"]).last()

            if _batch and _batch.is_active is True:
                str(n["date"])

                if int(n["stake_amount"]) == 100:
                    if n["game_play_id"] not in _game_play_id:
                        _game_play_id.append(n["game_play_id"])
                        _game_play[n["game_play_id"]] = {
                            "stake_amount": 100.0,
                            "winning_amount": 10000,
                            "date": str(n["date"]),
                            "application_status": "pending",
                            "game_play_id": n["game_play_id"],
                            "is_batch_active": check_batch_status(n["game_play_id"]),
                            "data": [n],
                        }

                    else:
                        _game_play[n["game_play_id"]]["stake_amount"] += 100.0
                        _game_play[n["game_play_id"]]["winning_amount"] += 10000
                        _game_play[n["game_play_id"]]["data"].append(n)

                elif int(n["stake_amount"]) == 200:
                    if n["game_play_id"] not in _game_play_id:
                        _game_play_id.append(n["game_play_id"])
                        _game_play[n["game_play_id"]] = {
                            "stake_amount": 200.0,
                            "winning_amount": 50000,
                            "date": str(n["date"]),
                            "application_status": "pending",
                            "game_play_id": n["game_play_id"],
                            "is_batch_active": check_batch_status(n["game_play_id"]),
                            "data": [n],
                        }

                    else:
                        _game_play[n["game_play_id"]]["stake_amount"] += 200.0
                        _game_play[n["game_play_id"]]["winning_amount"] += 50000
                        _game_play[n["game_play_id"]]["data"].append(n)

                elif int(n["stake_amount"]) == 500:
                    if n["game_play_id"] not in _game_play_id:
                        _game_play_id.append(n["game_play_id"])
                        _game_play[n["game_play_id"]] = {
                            "stake_amount": 500.0,
                            "winning_amount": 100000,
                            "date": str(n["date"]),
                            "application_status": "pending",
                            "game_play_id": n["game_play_id"],
                            "is_batch_active": check_batch_status(n["game_play_id"]),
                            "data": [n],
                        }

                    else:
                        _game_play[n["game_play_id"]]["stake_amount"] += 500.0
                        _game_play[n["game_play_id"]]["winning_amount"] += 100000
                        _game_play[n["game_play_id"]]["data"].append(n)

                elif int(n["stake_amount"]) == 1000:
                    if n["game_play_id"] not in _game_play_id:
                        _game_play_id.append(n["game_play_id"])
                        _game_play[n["game_play_id"]] = {
                            "stake_amount": 1000.0,
                            "winning_amount": 200000,
                            "date": str(n["date"]),
                            "application_status": "pending",
                            "game_play_id": n["game_play_id"],
                            "is_batch_active": check_batch_status(n["game_play_id"]),
                            "data": [n],
                        }

                    else:
                        _game_play[n["game_play_id"]]["stake_amount"] += 1000.0
                        _game_play[n["game_play_id"]]["winning_amount"] += 200000
                        _game_play[n["game_play_id"]]["data"].append(n)

            elif _batch and _batch.is_active is False:
                _winners_table = LotteryWinnersTable.objects.filter(batch=_batch, phone_number=user.phone_number)

                if _winners_table:
                    str(n["date"])
                    if int(n["stake_amount"]) == 100:
                        if n["game_play_id"] not in _game_play_id:
                            _game_play_id.append(n["game_play_id"])
                            _game_play[n["game_play_id"]] = {
                                "stake_amount": 100.0,
                                "winning_amount": 10000,
                                "date": str(n["date"]),
                                "application_status": "won",
                                "game_play_id": n["game_play_id"],
                                "is_batch_active": check_batch_status(n["game_play_id"]),
                                "data": [n],
                            }

                        else:
                            _game_play[n["game_play_id"]]["stake_amount"] += 100.0
                            _game_play[n["game_play_id"]]["winning_amount"] += 10000
                            _game_play[n["game_play_id"]]["data"].append(n)

                    elif int(n["stake_amount"]) == 200:
                        if n["game_play_id"] not in _game_play_id:
                            _game_play_id.append(n["game_play_id"])
                            _game_play[n["game_play_id"]] = {
                                "stake_amount": 200.0,
                                "winning_amount": 50000,
                                "date": str(n["date"]),
                                "application_status": "won",
                                "game_play_id": n["game_play_id"],
                                "is_batch_active": check_batch_status(n["game_play_id"]),
                                "data": [n],
                            }

                        else:
                            _game_play[n["game_play_id"]]["stake_amount"] += 200.0
                            _game_play[n["game_play_id"]]["winning_amount"] += 50000
                            _game_play[n["game_play_id"]]["data"].append(n)

                    elif int(n["stake_amount"]) == 500:
                        if n["game_play_id"] not in _game_play_id:
                            _game_play_id.append(n["game_play_id"])
                            _game_play[n["game_play_id"]] = {
                                "stake_amount": 500.0,
                                "winning_amount": 100000,
                                "date": str(n["date"]),
                                "application_status": "won",
                                "game_play_id": n["game_play_id"],
                                "is_batch_active": check_batch_status(n["game_play_id"]),
                                "data": [n],
                            }

                        else:
                            _game_play[n["game_play_id"]]["stake_amount"] += 500.0
                            _game_play[n["game_play_id"]]["winning_amount"] += 100000
                            _game_play[n["game_play_id"]]["data"].append(n)

                    elif int(n["stake_amount"]) == 1000:
                        if n["game_play_id"] not in _game_play_id:
                            _game_play_id.append(n["game_play_id"])
                            _game_play[n["game_play_id"]] = {
                                "stake_amount": 1000.0,
                                "winning_amount": 200000,
                                "date": str(n["date"]),
                                "application_status": "won",
                                "game_play_id": n["game_play_id"],
                                "is_batch_active": check_batch_status(n["game_play_id"]),
                                "data": [n],
                            }

                        else:
                            _game_play[n["game_play_id"]]["stake_amount"] += 1000.0
                            _game_play[n["game_play_id"]]["winning_amount"] += 200000
                            _game_play[n["game_play_id"]]["data"].append(n)

                else:
                    str(n["date"])

                    if int(n["stake_amount"]) == 100:
                        if n["game_play_id"] not in _game_play_id:
                            _game_play_id.append(n["game_play_id"])
                            _game_play[n["game_play_id"]] = {
                                "stake_amount": 100.0,
                                "winning_amount": 10000,
                                "date": str(n["date"]),
                                "application_status": "cancelled",
                                "game_play_id": n["game_play_id"],
                                "is_batch_active": check_batch_status(n["game_play_id"]),
                                "data": [n],
                            }

                        else:
                            _game_play[n["game_play_id"]]["stake_amount"] += 100.0
                            _game_play[n["game_play_id"]]["winning_amount"] += 10000
                            _game_play[n["game_play_id"]]["data"].append(n)

                    elif int(n["stake_amount"]) == 200:
                        if n["game_play_id"] not in _game_play_id:
                            _game_play_id.append(n["game_play_id"])
                            _game_play[n["game_play_id"]] = {
                                "stake_amount": 200.0,
                                "winning_amount": 50000,
                                "date": str(n["date"]),
                                "application_status": "cancelled",
                                "game_play_id": n["game_play_id"],
                                "is_batch_active": check_batch_status(n["game_play_id"]),
                                "data": [n],
                            }

                        else:
                            _game_play[n["game_play_id"]]["stake_amount"] += 200.0
                            _game_play[n["game_play_id"]]["winning_amount"] += 50000
                            _game_play[n["game_play_id"]]["data"].append(n)

                    elif int(n["stake_amount"]) == 500:
                        if n["game_play_id"] not in _game_play_id:
                            _game_play_id.append(n["game_play_id"])
                            _game_play[n["game_play_id"]] = {
                                "stake_amount": 500.0,
                                "winning_amount": 100000,
                                "date": str(n["date"]),
                                "application_status": "cancelled",
                                "game_play_id": n["game_play_id"],
                                "is_batch_active": check_batch_status(n["game_play_id"]),
                                "data": [n],
                            }

                        else:
                            _game_play[n["game_play_id"]]["stake_amount"] += 500.0
                            _game_play[n["game_play_id"]]["winning_amount"] += 100000
                            _game_play[n["game_play_id"]]["data"].append(n)

                    elif int(n["stake_amount"]) == 1000:
                        if n["game_play_id"] not in _game_play_id:
                            _game_play_id.append(n["game_play_id"])
                            _game_play[n["game_play_id"]] = {
                                "stake_amount": 1000.0,
                                "winning_amount": 200000,
                                "date": str(n["date"]),
                                "application_status": "cancelled",
                                "game_play_id": n["game_play_id"],
                                "is_batch_active": check_batch_status(n["game_play_id"]),
                                "data": [n],
                            }

                        else:
                            _game_play[n["game_play_id"]]["stake_amount"] += 1000.0
                            _game_play[n["game_play_id"]]["winning_amount"] += 200000
                            _game_play[n["game_play_id"]]["data"].append(n)
            else:
                if int(n["stake_amount"]) == 100:
                    if n["game_play_id"] not in _game_play_id:
                        _game_play_id.append(n["game_play_id"])
                        _game_play[n["game_play_id"]] = {
                            "stake_amount": 100.0,
                            "winning_amount": 10000,
                            "date": str(n["date"]),
                            "application_status": "cancelled",
                            "game_play_id": n["game_play_id"],
                            "is_batch_active": check_batch_status(n["game_play_id"]),
                            "data": [n],
                        }

                    else:
                        _game_play[n["game_play_id"]]["stake_amount"] += 100.0
                        _game_play[n["game_play_id"]]["winning_amount"] += 10000
                        _game_play[n["game_play_id"]]["data"].append(n)

                elif int(n["stake_amount"]) == 200:
                    if n["game_play_id"] not in _game_play_id:
                        _game_play_id.append(n["game_play_id"])
                        _game_play[n["game_play_id"]] = {
                            "stake_amount": 200.0,
                            "winning_amount": 50000,
                            "date": str(n["date"]),
                            "application_status": "cancelled",
                            "game_play_id": n["game_play_id"],
                            "is_batch_active": check_batch_status(n["game_play_id"]),
                            "data": [n],
                        }

                    else:
                        _game_play[n["game_play_id"]]["stake_amount"] += 200.0
                        _game_play[n["game_play_id"]]["winning_amount"] += 50000
                        _game_play[n["game_play_id"]]["data"].append(n)

                elif int(n["stake_amount"]) == 500:
                    if n["game_play_id"] not in _game_play_id:
                        _game_play_id.append(n["game_play_id"])
                        _game_play[n["game_play_id"]] = {
                            "stake_amount": 500.0,
                            "winning_amount": 100000,
                            "date": str(n["date"]),
                            "application_status": "cancelled",
                            "game_play_id": n["game_play_id"],
                            "is_batch_active": check_batch_status(n["game_play_id"]),
                            "data": [n],
                        }

                    else:
                        _game_play[n["game_play_id"]]["stake_amount"] += 500.0
                        _game_play[n["game_play_id"]]["winning_amount"] += 100000
                        _game_play[n["game_play_id"]]["data"].append(n)

                elif int(n["stake_amount"]) == 1000:
                    if n["game_play_id"] not in _game_play_id:
                        _game_play_id.append(n["game_play_id"])
                        _game_play[n["game_play_id"]] = {
                            "stake_amount": 1000.0,
                            "winning_amount": 200000,
                            "date": str(n["date"]),
                            "application_status": "cancelled",
                            "game_play_id": n["game_play_id"],
                            "is_batch_active": check_batch_status(n["game_play_id"]),
                            "data": [n],
                        }

                    else:
                        _game_play[n["game_play_id"]]["stake_amount"] += 1000.0
                        _game_play[n["game_play_id"]]["winning_amount"] += 200000
                        _game_play[n["game_play_id"]]["data"].append(n)

    for _gameID in _game_play_id:
        game_data = _game_play[_gameID]

        if data[game_data["date"]]:
            for _data in data[game_data["date"]]:
                if _data.get(game_data.get("application_status")) is None:
                    continue

                _data.get(game_data.get("application_status")).append(game_data)

    _game_play_id.clear()
    _game_play.clear()
    _date_list.clear()

    return data


def share_payment_across_pool_when_virtual_account_is_funded(user_instance):
    from main.models import LotteryModel
    from wallet_app.models import UserWallet

    _wallet = UserWallet.objects.filter(user=user_instance, wallet_tag="WEB").last()

    _user_lottery = LotteryModel.objects.filter(user_profile=user_instance, paid=False, channel="WEB").last()

    if _user_lottery is None:
        return {"message": "No lottery found"}

    # check time difference between now and lottery date
    now = timezone.now()
    _diff = now - _user_lottery.date

    if _diff.days > 0:
        return {"message": "Lottery has expired"}

    # check if lottery batch is active
    if _user_lottery.batch.is_active is False:
        return {"message": "Lottery batch is not active"}

    amount = _user_lottery.expected_amount

    if _wallet.game_available_balance <= amount:
        # combine game_available_balance and withdrawable_available_balance
        play_amount = _wallet.game_available_balance + _wallet.withdrawable_available_balance

        if play_amount <= amount:
            # since user does not have enough money to play the lottery
            # we'll dedeuct the amount he funded from his game play wallet
            # and match it to lottery and play it for him

            _play_amount = _wallet.game_available_balance

            filter_user_lottery = LotteryModel.objects.filter(game_play_id=_user_lottery.game_play_id)

            amount_to_deduct_from_wallet = 0

            for _lottery in filter_user_lottery:
                _lottery_amount = LotteryModel.determine_stake_for_payment_collection(_lottery)
                if _play_amount < _lottery_amount:
                    continue
                else:
                    _lottery.paid = True
                    _lottery.save()

                    amount_to_deduct_from_wallet += _lottery_amount

                    # update lottery_expected_amount in redis
                    redis_db = RedisStorage(_lottery.batch.batch_uuid)
                    get_redis_item = redis_db.get_data()
                    if get_redis_item is not None:
                        lottery_expected_amount = float(get_redis_item.decode("utf-8"))

                        # update redis item
                        redis_db.set_data(lottery_expected_amount + _lottery_amount)
                    else:
                        redis_db.set_data(_lottery_amount)

                    _play_amount -= _lottery_amount
                    if _play_amount < 1:
                        break

            if amount_to_deduct_from_wallet > 0:
                _wallet.game_available_balance -= amount_to_deduct_from_wallet
                _wallet.save()

                return {"message": "payment verified successfully"}

    else:
        amount_to_deduct_from_wallet = _user_lottery.expected_amount

        pay_for_lottery = LotteryModel.spread_lottery_amount(user_instance, amount_to_deduct_from_wallet)
        if pay_for_lottery:
            _wallet.game_available_balance -= amount_to_deduct_from_wallet
            _wallet.save()

            redis_db = RedisStorage(_lottery.batch.batch_uuid)
            get_redis_item = redis_db.get_data()
            if get_redis_item is not None:
                lottery_expected_amount = float(get_redis_item.decode("utf-8"))

                # update redis item
                redis_db.set_data(lottery_expected_amount + amount_to_deduct_from_wallet)
            else:
                redis_db.set_data(amount_to_deduct_from_wallet)

            return {"message": "payment verified successfully"}


def lottery_statistics_helper(_qs):
    """
    Helper function to get lottery statistics
    Args: LotteryModel queryset
    Returns: dictionary of lottery statistics
    """

    data = []

    if _qs is None:
        return data

    played_band = _qs.values("pool").distinct()
    played_band_count = played_band.count()

    _total_sum_of_played_band = 0

    for _band in played_band:
        if _band["pool"] == "TEN_THOUSAND":
            _ten_thousand_count = _qs.filter(pool="TEN_THOUSAND").count()

            _total_sum_of_played_band += _ten_thousand_count

            _ten_thousand_band_game_play_id = _qs.filter(pool="TEN_THOUSAND").values("game_play_id").annotate(Count("game_play_id"))
            _ten_thousand_highest_game_play_count = 0

            for _game_play_id in _ten_thousand_band_game_play_id:
                if _ten_thousand_highest_game_play_count < _game_play_id["game_play_id__count"]:
                    _ten_thousand_highest_game_play_count = _game_play_id["game_play_id__count"]

            data.append(
                {
                    "pool": _band["pool"],
                    "amount": 100 * _ten_thousand_highest_game_play_count if _ten_thousand_highest_game_play_count > 0 else 100,
                    "payout_band_start": "10000",
                    "payout_band_end": "50000",
                    "count": _ten_thousand_count,
                }
            )

        elif _band["pool"] == "FIFTY_THOUSAND":
            fifty_thousand_count = _qs.filter(pool=_band["pool"]).count()

            _total_sum_of_played_band += fifty_thousand_count

            fifty_thousand_band_game_play_id = _qs.filter(pool=_band["pool"]).values("game_play_id").annotate(Count("game_play_id"))
            fifty_thousand_highest_game_play_count = 0

            for _game_play_id in fifty_thousand_band_game_play_id:
                if fifty_thousand_highest_game_play_count < _game_play_id["game_play_id__count"]:
                    fifty_thousand_highest_game_play_count = _game_play_id["game_play_id__count"]

            data.append(
                {
                    "pool": _band["pool"],
                    "amount": 200 * fifty_thousand_highest_game_play_count if fifty_thousand_highest_game_play_count > 0 else 200,
                    "payout_band_start": "50000",
                    "payout_band_end": "100000",
                    "count": fifty_thousand_count,
                }
            )

        elif _band["pool"] == "ONE_HUNDRED_THOUSAND":
            two_fifty_thousand_count = _qs.filter(pool=_band["pool"]).count()

            _total_sum_of_played_band += two_fifty_thousand_count

            two_fifty_thousand_band_game_play_id = _qs.filter(pool=_band["pool"]).values("game_play_id").annotate(Count("game_play_id"))
            two_fifty_thousand_highest_game_play_count = 0

            for _game_play_id in two_fifty_thousand_band_game_play_id:
                if two_fifty_thousand_highest_game_play_count < _game_play_id["game_play_id__count"]:
                    two_fifty_thousand_highest_game_play_count = _game_play_id["game_play_id__count"]

            data.append(
                {
                    "pool": _band["pool"],
                    "amount": 500 * two_fifty_thousand_highest_game_play_count if two_fifty_thousand_highest_game_play_count > 0 else 500,
                    "percentage": round(float((played_band_count / two_fifty_thousand_count) * 100)),
                    "payout_band_start": "100000",
                    "payout_band_end": "1100000",
                    "count": two_fifty_thousand_count,
                }
            )

        elif _band["pool"] == "TWO_HUNDRED_THOUSAND":
            five_hundred_thousand_count = _qs.filter(pool=_band["pool"]).count()

            _total_sum_of_played_band += five_hundred_thousand_count

            five_hundred_thousand_band_game_play_id = _qs.filter(pool=_band["pool"]).values("game_play_id").annotate(Count("game_play_id"))
            five_hundred_thousand_highest_game_play_count = 0

            for _game_play_id in five_hundred_thousand_band_game_play_id:
                if five_hundred_thousand_highest_game_play_count < _game_play_id["game_play_id__count"]:
                    five_hundred_thousand_highest_game_play_count = _game_play_id["game_play_id__count"]

            data.append(
                {
                    "pool": _band["pool"],
                    "amount": 1000 * five_hundred_thousand_highest_game_play_count if five_hundred_thousand_highest_game_play_count > 0 else 1000,
                    "payout_band_start": "200000",
                    "payout_band_end": "1000000",
                    "count": five_hundred_thousand_count,
                }
            )

        elif _band["pool"] == "ONE_MILLION":
            one_million_count = _qs.filter(pool=_band["pool"]).count()

            one_million_band_game_play_id = _qs.filter(pool=_band["pool"]).values("game_play_id").annotate(Count("game_play_id"))
            one_million_highest_game_play_count = 0

            for _game_play_id in one_million_band_game_play_id:
                if one_million_highest_game_play_count < _game_play_id["game_play_id__count"]:
                    one_million_highest_game_play_count = _game_play_id["game_play_id__count"]

            data.append(
                {
                    "pool": _band["pool"],
                    "amount": 200 * one_million_highest_game_play_count,
                    "payout_band_start": "50000",
                    "payout_band_end": "100000",
                    "count": one_million_count,
                }
            )

        elif _band["pool"] == "TWO_MILLION":
            two_million_count = _qs.filter(pool=_band["pool"]).count()

            _total_sum_of_played_band += two_million_count

            two_million_band_game_play_id = _qs.filter(pool=_band["pool"]).values("game_play_id").annotate(Count("game_play_id"))
            two_million_highest_game_play_count = 0

            for _game_play_id in two_million_band_game_play_id:
                if two_million_highest_game_play_count < _game_play_id["game_play_id__count"]:
                    two_million_highest_game_play_count = _game_play_id["game_play_id__count"]

            data.append(
                {
                    "pool": _band["pool"],
                    "amount": 200 * two_million_highest_game_play_count,
                    "payout_band_start": "50000",
                    "payout_band_end": "100000",
                    "count": two_million_count,
                }
            )

    # add percentage to data
    for _band in data:
        _band["percentage"] = round(float((_band["count"] / _total_sum_of_played_band) * 100))
        del _band["count"]

    return data


def re_arrange_lottery(user_instance, game_play_id):
    from main.models import LotteryModel

    last_user_lottery = LotteryModel.objects.filter(user_profile=user_instance).last()

    if last_user_lottery and last_user_lottery.game_play_id == game_play_id:
        return last_user_lottery

    else:
        lottery_qs = LotteryModel.objects.filter(user_profile=user_instance, game_play_id=game_play_id)

        new_game_play_id = generate_game_play_id()

        if lottery_qs:
            for _lottery in lottery_qs:
                new_obj = LotteryModel.objects.create(
                    user_profile=_lottery.user_profile,
                    batch=_lottery.batch,
                    band=_lottery.band,
                    phone=_lottery.user_profile.phone_number,
                    pool=_lottery.pool,
                    stake_amount=_lottery.stake_amount,
                    lucky_number=_lottery.lucky_number,
                    consent=_lottery.consent,
                    unique_id=_lottery.unique_id,
                    paid=_lottery.paid,
                    paid_date=_lottery.paid_date,
                    instance_number=_lottery.instance_number,
                    payout_account_no=_lottery.payout_account_no,
                    channel="WEB",
                    account_no=_lottery.account_no,
                    bank_name=_lottery.bank_name,
                    ussd_watupay_bank_code=_lottery.ussd_watupay_bank_code,
                    expected_amount=_lottery.expected_amount,
                    game_play_id=new_game_play_id,
                )

                new_obj.date = _lottery.date
                new_obj.save()

            LotteryModel.objects.filter(user_profile=user_instance, game_play_id=game_play_id).delete()


def validate_pos_wyse_cash_price(serialized_data) -> bool:
    pass

    get_lottery_count(serialized_data)

    is_price_valid = False

    wyse_cash_price_model = WyseCashPriceModel()

    for item in serialized_data:
        # if breake_the_loop is True:
        #     break
        if item:
            for sub_item in item:
                obj_data = dict(sub_item)
                band = obj_data.get("band")

                stake_amount = obj_data.get("stake_amount", 0)
                stake_amount = int(stake_amount)

                agent_wyse_cash_stake_amount = wyse_cash_price_model.ticket_price(channel="POS", band=int(band), no_of_line=1)

                print(
                    int(agent_wyse_cash_stake_amount.get("stake_amount", 0))
                    + int(agent_wyse_cash_stake_amount.get("woven_service_charge", 0))
                    + int(agent_wyse_cash_stake_amount.get("africastalking_charge", 0))
                    == stake_amount
                )

                if stake_amount == (
                    int(agent_wyse_cash_stake_amount.get("stake_amount", 0))
                    + int(agent_wyse_cash_stake_amount.get("woven_service_charge", 0))
                    + int(agent_wyse_cash_stake_amount.get("africastalking_charge", 0))
                ):
                    is_price_valid = True

                else:
                    is_price_valid = False
                    break

    return is_price_valid
