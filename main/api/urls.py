from django.urls import include, path

from main.api.views import (
    ApiConstant,
    BankView,
    ChangePasswordView,
    CreateUserView,
    LoginApiView,
    LogoutView,
    LotteryApiView,
    LotteryPaymentView,
    LotteryStatisticsView,
    LotteryStatus,
    ReArrangeLotteryView,
    ReferralCampaignView,
    ResetPasswordConfirmationView,
    ResetPasswordView,
    TestimonalView,
    UserLotteryApplicationsView,
    UserProfileApiView,
    UserTestimonalView,
    WinnersApiView,
)

urlpatterns = [
    path("login/", LoginApiView.as_view()),
    path("create_account/", CreateUserView.as_view()),
    path("user/", UserProfileApiView.as_view()),
    path("user/logout/", LogoutView.as_view()),
    path("user/reset_password/", ResetPasswordView.as_view()),
    path("user/reset_password_confirm/", ResetPasswordConfirmationView.as_view()),
    path("user/change_password/", ChangePasswordView.as_view()),
    path("lottery/", LotteryApiView.as_view()),
    path("re_arrange_lottery/", ReArrangeLotteryView.as_view()),
    path("get_winners/", WinnersApiView.as_view()),
    path("dynamic_data/", ApiConstant.as_view()),
    path("fetch_banks/", BankView.as_view()),
    path("lottery_payment/", LotteryPaymentView.as_view()),
    path("user_testimonal/", UserTestimonalView.as_view()),
    path("testimonals/", TestimonalView.as_view()),
    path("referral_campaign/", ReferralCampaignView.as_view()),
    path("lottery_application/", UserLotteryApplicationsView.as_view()),
    path("lottery_statistics/", LotteryStatisticsView.as_view()),
    path("transaction/", include("wallet_app.urls")),
    path("lottery_status/", LotteryStatus.as_view()),
]
