import uuid
from datetime import datetime

from django.contrib.auth import authenticate
from django.db.models import Sum
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.authtoken.models import Token
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from account.authentication import CustomTokenAuthentication
from account.models import User
from account.serializers import EmailLoginSerializer
from main.api.api_lottery_helpers import (
    Paginator,
    create_lottery,
    delete_lottery,
    generate_lucky_number,
    lottery_statistics_helper,
    re_arrange_lottery,
    user_lottery_application_filter_helper,
)
from main.api.api_whisper_helper import WhisperApiHelper
from main.api.serializer import (
    ChangePasswordSerializer,
    DataSetSerializer,
    LoginSerializer,
    ReArrangeLotterySerializer,
    ReferralCampaignSerializer,
    ResetPasswordConfirmationSerializer,
    ResetPasswordSerializer,
    SignupSerializer,
    TestimonialSerializer,
    UpdateUserProfileSerializer,
    UserLotteryApplicationSerializer,
    UserProfileSerializer,
    WinnerSerializer,
    WyseCashLotteryPaymentSerializer,
)
from main.helpers.redis_storage import RedisStorage
from main.helpers.whisper_sms_managers import referral_campaign_sms
from main.models import (
    ConstantVariable,
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    TestimonalTable,
    UserProfile,
)
from main.ussd.bankdb import bankdb
from mobile_app.serializers import SecondMobileGameHistorySerializer
from pos_app.pos_helpers import (
    lotto_ticket_search_response_remove_duplicate,
    serialize_WYSE_CASH_game_history,
)
from pos_app.serializers import MobileGameHistorySerializer
from referral_system.models import ReferralCode, ReferralWallet
from sport_app.models import FootballTable
from wallet_app.helpers.payment_gateway import PaymentGateway
from wallet_app.models import PaystackTransaction, UserWallet
from wyse_ussd.models import SoccerPrediction


@method_decorator(csrf_exempt, name="dispatch")
class LotteryApiView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        user_profile = UserProfile.objects.get(phone_number=request.user.phone)
        generate_lottery_nums = generate_lucky_number(user_profile)

        return Response(generate_lottery_nums, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = DataSetSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        ten = serializer.validated_data.get("ten")
        fifty = serializer.validated_data.get("fifty")
        two_fifty = serializer.validated_data.get("two_fifty")
        five_hundred = serializer.validated_data.get("five_hundred")

        all_data = [ten, fifty, two_fifty, five_hundred]

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        lottery = create_lottery(user_profile, all_data, channel="WEB")

        data = {"message": "success", "data": lottery}

        return Response(data=data, status=status.HTTP_200_OK)

    def delete(self, request):
        id = request.GET.get("id")

        if id is None:
            data = {
                "message": "error. id is required",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        try:
            delete_lottery_response = delete_lottery(id)
        except Exception:
            pass
        else:
            return Response(data={"message": "success"}, status=status.HTTP_200_OK)
            if delete_lottery_response is None:
                data = {
                    "message": "error. id is not found",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        return Response(data={"message": "success"}, status=status.HTTP_200_OK)

        # delete_lottery_response = delete_lottery(id)

        # if delete_lottery_response is None:
        #     data = {
        #         "message": "error. id is not found",
        #     }
        #     return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # return Response(data={"message": "success"}, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name="dispatch")
class LoginApiView(APIView):
    serializer_class = LoginSerializer

    def post(self, request):
        # serializer = LoginSerializer(data=request.data)
        # serializer.is_valid(raise_exception=True)
        # response = WhisperApiHelper().login(**serializer.validated_data)
        # return Response(response, status=response.get("status"))

        serializer = EmailLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data["email"]
        password = serializer.validated_data["password"]

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({"message": "Invalid Credentials"}, status=status.HTTP_400_BAD_REQUEST)

        if not user.is_active:
            return Response(
                {"message": "Please activate your account"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            user = authenticate(email=email, password=password)
        except Exception as e:
            return Response({"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)

        token, _ = Token.objects.get_or_create(user=user)
        return Response({"auth_token": token.key}, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name="dispatch")
class CreateUserView(APIView):
    serializer_class = SignupSerializer

    def post(self, request):
        serializer = SignupSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # check if referral code is not empty and verify if it is valid
        if serializer.validated_data.get("referral_code") is not None:
            if serializer.validated_data.get("referral_code") == "":
                del serializer.validated_data["referral_code"]

            else:
                if serializer.validated_data["referral_code"] != "" or serializer.validated_data["referral_code"] is not None:
                    check_referral_code = ReferralCode.get_referer_user(serializer.validated_data["referral_code"])
                    if check_referral_code is None:
                        data = {"message": "Invalid referral code"}
                        return Response(data, status=status.HTTP_400_BAD_REQUEST)

        response = User.create_user(serializer.validated_data)
        # activate user
        email = serializer.validated_data.get("email")
        User.objects.filter(email=email).update(is_active=True)

        return response


@method_decorator(csrf_exempt, name="dispatch")
class LogoutView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        UserProfile.objects.filter(id=request.user.id).update(auth_code=None)
        return Response(status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name="dispatch")
class ResetPasswordView(APIView):
    def post(self, request):
        serializer = ResetPasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        response = WhisperApiHelper().reset_password(**serializer.validated_data)
        return Response(response, status=response.get("status"))


@method_decorator(csrf_exempt, name="dispatch")
class ResetPasswordConfirmationView(APIView):
    def post(self, request):
        serializer = ResetPasswordConfirmationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        response = WhisperApiHelper().reset_password_confirmation(**serializer.validated_data)
        return Response(response, status=response.get("status"))


@method_decorator(csrf_exempt, name="dispatch")
class ChangePasswordView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        response = WhisperApiHelper().change_password(user_profile, **serializer.validated_data)
        return Response(response, status=response.get("status"))


class WinnersApiView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        winners = LotteryWinnersTable.objects.exclude(unnique_id__isnull=True).order_by("-earning")[:10]
        serilaizer = WinnerSerializer(winners, many=True)
        data = {
            "status": status.HTTP_200_OK,
            "message": "success",
            "data": serilaizer.data,
        }
        return Response(data, status=status.HTTP_200_OK)


class UserProfileApiView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)
    parser_classes = (
        MultiPartParser,
        JSONParser,
    )

    def get(self, request):
        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()
        serializer = UserProfileSerializer(user_profile)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request):
        serializer = UpdateUserProfileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        response = UserProfile.user_profile_update(user_profile, **serializer.validated_data)
        return Response(response, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name="dispatch")
class ApiConstant(APIView):
    authentication_classes = (TokenAuthentication, CustomTokenAuthentication)
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        print("request.user.phone", request.user.phone)

        if user_profile is None:
            return Response(
                {"message": "User Profile not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # print("testimonal", user_profile.testimonal)
        data = {
            "has_pin": True if user_profile.pin is not None else False,
            "has_profile_img": True if user_profile.profile_img is not None else False,
            "has_bvn": True if user_profile.bvn_number is not None else False,
            "collect_bvn": ConstantVariable.get_constant_variable().get("collect_bvn"),
            "has_testimonal": TestimonalTable.objects.filter(user__id=user_profile.id).exists(),
        }

        return Response(data, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name="dispatch")
class BankView(APIView):
    authentication_classes = [TokenAuthentication, CustomTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        data = bankdb()

        return Response(data, status=status.HTTP_200_OK)


class LotteryPaymentView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [
        IsAuthenticated,
    ]

    # serializer_class = LotteryPaymentSerializer

    def post(self, request):
        serializer = WyseCashLotteryPaymentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        from_referral_wallet = serializer.validated_data.get("from_referral_wallet")
        from_main_wallet = serializer.validated_data.get("from_main_wallet")
        with_paystack = serializer.validated_data.get("paystack")
        amount = serializer.validated_data.get("amount")

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        # pay using referral wallet
        if from_referral_wallet is True:
            lottery_wallet = ReferralWallet.objects.filter(user=user_profile).last()
            if lottery_wallet.can_use_wallet is False:
                data = {"message": "You cannot use referral wallet yet"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                if lottery_wallet.available_balance < amount:
                    data = {"message": "Insufficient funds"}
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
                else:
                    pay_for_lottery = LotteryModel.spread_lottery_amount(user_profile, amount)

                    if pay_for_lottery:
                        lottery_wallet.available_balance -= amount
                        lottery_wallet.save()
                        data = {"message": "success"}
                        return Response(data, status=status.HTTP_200_OK)
                    else:
                        data = {"message": "Error occured while paying for lottery"}
                        return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # pay from main wallet
        elif from_main_wallet is True:
            main_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if main_wallet is None:
                data = {"message": "You have no wallet"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            if main_wallet.game_available_balance < amount:
                # if user has insufficient funds in game_available_balance
                # we will check if user has sufficient funds in withdrawable_available_balance
                # if yes, we will play the game from his withdrawable_available_balance balance
                # if not, we will return error message

                if main_wallet.withdrawable_available_balance < amount:
                    # combine game_available_balance and withdrawable_available_balance
                    play_amount = main_wallet.game_available_balance + main_wallet.withdrawable_available_balance
                    if play_amount < amount:
                        data = {"message": "Insufficient funds"}
                        return Response(data, status=status.HTTP_400_BAD_REQUEST)

                    pay_for_lottery = LotteryModel.spread_lottery_amount(user_profile, amount)
                    if pay_for_lottery:
                        play_amount -= amount
                        main_wallet.game_available_balance = 0.0
                        main_wallet.withdrawable_available_balance = play_amount
                        main_wallet.save()
                        data = {"message": "success"}

                        # redis storage
                        _lottery = LotteryModel.objects.filter(user_profile=user_profile, expected_amount=amount, paid=True).last()

                        redis_db = RedisStorage(_lottery.batch.batch_uuid)
                        get_redis_item = redis_db.get_data()
                        if get_redis_item is not None:
                            lottery_expected_amount = float(get_redis_item.decode("utf-8"))

                            # update redis item
                            redis_db.set_data(lottery_expected_amount + amount)
                        else:
                            redis_db.set_data(amount)

                        return Response(data, status=status.HTTP_200_OK)
                    else:
                        data = {"message": "Error occured while paying for lottery"}
                        return Response(data, status=status.HTTP_400_BAD_REQUEST)
                else:
                    pay_for_lottery = LotteryModel.spread_lottery_amount(user_profile, amount)
                    if pay_for_lottery:
                        main_wallet.withdrawable_available_balance -= amount
                        main_wallet.save()

                        # redis storage
                        _lottery = LotteryModel.objects.filter(user_profile=user_profile, expected_amount=amount, paid=True).last()

                        redis_db = RedisStorage(_lottery.batch.batch_uuid)
                        get_redis_item = redis_db.get_data()
                        if get_redis_item is not None:
                            lottery_expected_amount = float(get_redis_item.decode("utf-8"))

                            # update redis item
                            redis_db.set_data(lottery_expected_amount + amount)
                        else:
                            redis_db.set_data(amount)

                        data = {"message": "success"}
                        return Response(data, status=status.HTTP_200_OK)
                    else:
                        data = {"message": "Error occured while paying for lottery"}
                        return Response(data, status=status.HTTP_400_BAD_REQUEST)

            else:
                pay_for_lottery = LotteryModel.spread_lottery_amount(user_profile, amount)
                if pay_for_lottery:
                    main_wallet.game_available_balance -= amount
                    main_wallet.save()

                    # redis storage
                    _lottery = LotteryModel.objects.filter(user_profile=user_profile, expected_amount=amount, paid=True).last()

                    redis_db = RedisStorage(_lottery.batch.batch_uuid)
                    get_redis_item = redis_db.get_data()
                    if get_redis_item is not None:
                        lottery_expected_amount = float(get_redis_item.decode("utf-8"))

                        # update redis item
                        redis_db.set_data(lottery_expected_amount + amount)
                    else:
                        redis_db.set_data(amount)

                    data = {"message": "success"}
                    return Response(data, status=status.HTTP_200_OK)
                else:
                    data = {"message": "Error occured while paying for lottery"}
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)

        elif with_paystack is True:
            # get lottery the user want to pay for

            lottery = LotteryModel.objects.filter(
                user_profile=user_profile,
                paid=False,
                has_interest=True,
                expected_amount=float(amount),
            ).last()

            lottery = LotteryModel.objects.filter(user_profile=user_profile, paid=False, has_interest=True).last()

            # paystack payload
            transaction_ref = f"LTT-PAY{uuid.uuid4()}-{lottery.id}"

            paystack_payload = {
                "email": user_profile.email,
                "amount": float(amount) * 100,
                "currency": "NGN",
                "reference": transaction_ref,
            }

            # create transaction
            PaystackTransaction.objects.create(
                user=user_profile,
                amount=float(amount),
                reference=transaction_ref,
                created_at=timezone.now(),
                paid_at=timezone.now(),
                channel="WEB",
                raw_data=paystack_payload,
                payment_reason="LOTTERY_PAYMENT",
            )

            # initiate payment
            paystack_api = PaymentGateway().paystack_link_request(**paystack_payload)

            payment_link = paystack_api.get("authorization_url")

            data = {
                "message": "success",
                "payment_link": payment_link,
            }
            return Response(data, status=status.HTTP_200_OK)
        else:
            data = {"message": "Invalid request"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class UserLotteryApplicationsView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        lottery_applications = LotteryModel.objects.filter(user_profile=user_profile).order_by("-date")

        page = request.GET.get("page")
        paginate_all_lottery_applications = Paginator.paginate(request=request, queryset=lottery_applications, page=page)

        serializer = UserLotteryApplicationSerializer(paginate_all_lottery_applications, many=True)

        data = user_lottery_application_filter_helper(user_profile, serializer)
        return Response(data, status=status.HTTP_200_OK)


class UserTestimonalView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        """
        GET TESTIMONAL FOR USER INSTANCE
        """

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        testimonials = TestimonalTable.objects.filter(user=user_profile).order_by("-date_added")

        serializer = TestimonialSerializer(testimonials, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = TestimonialSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        comment = serializer.validated_data.get("message")
        if comment is not None:
            _list_comment = comment.split(" ")
            if len(_list_comment) < 20 or len(_list_comment) > 200:
                data = {"message": "Comment must be between 50 and 200 characters"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            TestimonalTable.objects.create(
                user=user_profile,
                message=comment,
            )
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class TestimonalView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        """
        GET TESTIMONAL FOR ALL USER
        """
        testimonials = TestimonalTable.objects.all().order_by("-date_added")

        page = request.GET.get("page")
        paginate_all_testimonal = Paginator.paginate(request=request, queryset=testimonials, page=page)

        serializer = TestimonialSerializer(paginate_all_testimonal, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)


class ReferralCampaignView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        serializer = ReferralCampaignSerializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        referral_campaign_sms(user_profile, serializer.data)
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class LotteryStatisticsView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        """
        GET LOTTERY STATISTICS FOR All USERS
        """

        _date = request.GET.get("date")
        _status = request.GET.get("status")
        page = request.GET.get("page", 1)

        _filter_date = timezone.now().date()

        get_last_win_date = LotteryWinnersTable.objects.all().last().date_won.date()

        if _date is not None:
            _filter_date = datetime.strptime(_date, "%Y-%m-%d").date()

        if _status is None:
            lottery_statistics_qs = LotteryModel.objects.filter(paid=True, date__date=_filter_date)

        elif _status.casefold() == "processing":
            lottery_statistics_qs = LotteryModel.objects.filter(paid=True, date__date=_filter_date)

        elif _status.casefold() == "approved":
            if _date is None:
                _filter_date = get_last_win_date

            lottery_winners = (
                LotteryWinnersTable.objects.filter(date_won__date=_filter_date).distinct("unnique_id").values_list("unnique_id", flat=True)
            )

            lottery_statistics_qs = LotteryModel.objects.filter(paid=True, unique_id__in=lottery_winners)

        else:  # no need for this, but i had to
            lottery_statistics_qs = LotteryModel.objects.filter(paid=True, date__date=_filter_date)

        data = lottery_statistics_helper(lottery_statistics_qs)

        if _status.casefold() == "approved":
            new_data_list = {}

            new_data_list["last_win_date"] = get_last_win_date

            new_data_list["stats"] = data

            _winners_qs = LotteryWinnersTable.objects.filter(date_won__date=_filter_date)

            # top 3 winners
            top_three_winners = _winners_qs.order_by("-earning")[:3]
            serializer = WinnerSerializer(top_three_winners, many=True)

            new_data_list["top_three_winners"] = serializer.data

            paginate_winners_qs = Paginator.paginate(request=request, queryset=_winners_qs, page=page)
            serializer = WinnerSerializer(paginate_winners_qs, many=True)

            new_data_list["winners"] = serializer.data

            print(type(new_data_list))

            return Response(data=new_data_list, status=status.HTTP_200_OK)

        return Response(data=data, status=status.HTTP_200_OK)


class ReArrangeLotteryView(APIView):
    authentication_classes = (CustomTokenAuthentication,)
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        serializer = ReArrangeLotterySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        game_play_id = serializer.validated_data.get("game_play_id")

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        re_arrange_lottery(user_profile, game_play_id)
        return Response({"message": "success"}, status=status.HTTP_200_OK)


class LotteryStatus(APIView):
    serializer_class = ReArrangeLotterySerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        game_play_id = serializer.validated_data.get("game_play_id")

        try:
            lottery = LotteryModel.objects.filter(game_play_id=game_play_id)
            if not lottery.exists():
                raise LotteryModel.DoesNotExist
            else:
                paid_lottery = LotteryModel.objects.filter(game_play_id=game_play_id, paid=True)

                unpaid_lottery = LotteryModel.objects.filter(game_play_id=game_play_id, paid=False)

                if paid_lottery.exists():
                    _serialized_data = SecondMobileGameHistorySerializer(paid_lottery, many=True)

                elif unpaid_lottery.exists():
                    _serialized_data = SecondMobileGameHistorySerializer(unpaid_lottery, many=True)

                else:
                    _serialized_data = SecondMobileGameHistorySerializer(lottery, many=True)

                WYSE_CASH_data = serialize_WYSE_CASH_game_history(_serialized_data.data)

                return Response(data=WYSE_CASH_data, status=status.HTTP_200_OK)

        except LotteryModel.DoesNotExist:
            lotto_ticket = LottoTicket.objects.filter(game_play_id=game_play_id)
            if not lotto_ticket.exists():
                # --------------------------- GAME SEARCH FOR SOCCER CASH --------------------------- #

                soccer_cash_qs = SoccerPrediction.objects.filter(game_id=game_play_id)
                if not soccer_cash_qs.exists():
                    data = {"message": "game not found"}
                    return Response(data=data, status=status.HTTP_404_NOT_FOUND)

                if soccer_cash_qs.exists():
                    total_stake = soccer_cash_qs.aggregate(Sum("stake_amount"))["stake_amount__sum"]
                    pontential_win = soccer_cash_qs.aggregate(Sum("potential_winning"))["potential_winning__sum"]

                    has_paid = soccer_cash_qs.filter(paid=True).exists()

                    data = {
                        "status": "Accepted",
                        "game_type": "Soccer Cash",
                        "game_play_id": game_play_id,
                        "total_stake": total_stake,
                        "pontential_win": pontential_win,
                        "number_of_ticket": soccer_cash_qs.count(),
                        "date": soccer_cash_qs.last().date,
                    }

                    prediction_data = []

                    if has_paid is True:
                        data["paid"] = True
                        data["number_of_ticket"] = soccer_cash_qs.filter(paid=True).count()

                        paid_queryset = soccer_cash_qs.filter(paid=True)

                        data["number_of_bets"] = paid_queryset.count()

                        for prediction in paid_queryset:
                            football_table_instance = FootballTable.objects.filter(fixture_id=prediction.game_fixture_id).last()

                            if football_table_instance is None:
                                continue
                            else:
                                if football_table_instance.game_completed is True:
                                    if int(prediction.home_choice) == int(football_table_instance.home_full_time_score) and int(
                                        prediction.away_choice
                                    ) == int(football_table_instance.away_full_time_score):
                                        data["won"] = True
                                        prediction_data.append(
                                            {
                                                "home_team": football_table_instance.home_team,
                                                "away_team": football_table_instance.away_team,
                                                "prediction": f"{prediction.home_choice} - {prediction.away_choice}",
                                                "won": True,
                                                "fixture_id": football_table_instance.fixture_id,
                                                "drawn": True,
                                            }
                                        )

                                    else:
                                        if data.get("won") is not True:
                                            data["won"] = False

                                        prediction_data.append(
                                            {
                                                "home_team": football_table_instance.home_team,
                                                "away_team": football_table_instance.away_team,
                                                "prediction": f"{prediction.home_choice} - {prediction.away_choice}",
                                                "won": False,
                                                "fixture_id": football_table_instance.fixture_id,
                                                "drawn": True,
                                            }
                                        )

                                else:
                                    prediction_data.append(
                                        {
                                            "home_team": football_table_instance.home_team,
                                            "away_team": football_table_instance.away_team,
                                            "prediction": f"{prediction.home_choice} - {prediction.away_choice}",
                                            "won": False,
                                            "fixture_id": football_table_instance.fixture_id,
                                            "drawn": True,
                                        }
                                    )

                    else:
                        data["paid"] = False
                        data["won"] = False

                        for prediction in soccer_cash_qs:
                            football_table_instance = FootballTable.objects.filter(fixture_id=prediction.game_fixture_id).last()

                            if football_table_instance is None:
                                data = {"message": "issue with game fixture id"}

                                return Response(data=data, status=status.HTTP_404_NOT_FOUND)

                            prediction_data.append(
                                {
                                    "home_team": football_table_instance.home_team,
                                    "away_team": football_table_instance.away_team,
                                    "prediction": f"{prediction.home_choice} - {prediction.away_choice}",
                                    "won": False,
                                    "fixture_id": football_table_instance.fixture_id,
                                    "drawn": False,
                                }
                            )

                # --------------------------- GAME SEARCH FOR SOCCER CASH --------------------------- #

                data["data"] = prediction_data

                return Response(data, status=status.HTTP_200_OK)

            paid_lotto = LottoTicket.objects.filter(game_play_id=game_play_id, paid=True)

            unpaid_lotto = LottoTicket.objects.filter(game_play_id=game_play_id, paid=False)

            if paid_lotto.exists():
                _serialized_data = MobileGameHistorySerializer(paid_lotto, many=True)

            elif unpaid_lotto.exists():
                _serialized_data = MobileGameHistorySerializer(unpaid_lotto, many=True)

            else:
                _serialized_data = MobileGameHistorySerializer(lotto_ticket, many=True)

            # remove duplicates
            _lotto_data = lotto_ticket_search_response_remove_duplicate(
                _serialized_data.data,
                paid=paid_lotto.exists(),
                not_paid=unpaid_lotto.exists(),
            )

            return Response(data=_lotto_data, status=status.HTTP_200_OK)
