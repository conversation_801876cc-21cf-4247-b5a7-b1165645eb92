from unittest.mock import patch

from django.conf import settings
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from main.models import SalaryForLifeSponsor
from main.serializers import S4LSponsorSerializer

from unittest.mock import patch
from django.test import TestCase
from main.tasks import send_email

class SalaryForLifeSponsorViewTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.url = reverse("create_sponsor")
        self.valid_payload = {
            "brand_name": "Test Brand",
            "designation": "Test Designation",
            "email": "<EMAIL>",
            "phone_number": "1234567890",
        }
        self.invalid_payload = {"brand_name": "", "designation": "Test Designation", "email": "invalid-email", "phone_number": "123"}

    def test_create_valid_sponsor(self):
        response = self.client.post(self.url, data=self.valid_payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(SalaryForLifeSponsor.objects.count(), 1)
        sponsor = SalaryForLifeSponsor.objects.first()
        S4LSponsorSerializer(sponsor).data
        self.assertEqual(response.data["sponsor_details"]["id"], sponsor.id)
        self.assertEqual(response.data["message"], "Salary 4 Life Sponsor Profile Created Successfully!")

    def test_create_invalid_sponsor(self):
        response = self.client.post(self.url, data=self.invalid_payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(SalaryForLifeSponsor.objects.count(), 0)

    def test_create_sponsor_with_exception(self):

        with patch("main.models.SalaryForLifeSponsor.objects.create") as mock_create:
            mock_create.side_effect = Exception("Test Exception")
            response = self.client.post(self.url, data=self.valid_payload, format="json")
            self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
            self.assertEqual(response.data["message"], "An error occurred while attempting to create Salary 4 Life Sponsor")
            self.assertEqual(response.data["error"], "Test Exception")


class SalaryForLifeParticipantViewTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.url = reverse("create_participant")
        self.valid_payload = {"name": "John Doe", "email": "<EMAIL>", "phone_number": "1234567890", "location": "Lagos"}
        self.invalid_payload = {"name": "", "email": "invalid-email", "phone_number": "123", "location": " "}

    def test_create_sponsor_with_exception(self):

        with patch("main.models.SalaryForLifeParticipant.objects.create") as mock_create:
            mock_create.side_effect = Exception("Test Exception")
            response = self.client.post(self.url, data=self.valid_payload, format="json")
            print(response.data, "\n\n\n\n\n")
            self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
            self.assertEqual(response.data["message"], "An error occurred while attempting to create Salary 4 Life participant!")
            self.assertEqual(response.data["error"], "Test Exception")


class ContactFormViewTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.url = reverse("fill_contact_form")
        self.valid_payload = {       
            "full_name": "Ini-ubong Isemin",
            "email": "<EMAIL>",
            "phone_no": "08123456789",
            "message": "Hi Liberty"
        }
        self.invalid_payload = {       
            "full_name": "",
            "email": "eugeneinie.com",
            "phone_no": "0812345",
            "message": " "
        }

    def test_create_sponsor_with_exception(self):
        with patch("main.models.ContactUsForm.objects.create") as mock_create:
            mock_create.side_effect = Exception("Test Exception")
            response = self.client.post(self.url, data=self.valid_payload, format="json")
            self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
            self.assertEqual(response.data["message"], "Salary for Life contact form submission failed!")
            self.assertEqual(response.data["error"], "Test Exception")


# class SendEmailTaskTest(TestCase):

#     @patch("main.tasks.requests.post")
#     def test_send_email_without_template(self, mock_post):

#         mock_post.return_value.status_code = 200
#         mock_post.return_value.text = "EMAIL SENT"

#         result = send_email(
#             email="<EMAIL>",
#             subject="Test Subject",
#             template_dir="sponsor_confirmation_email.html",
#             use_template=False,
#             body="Test email body",
#         )

#         mock_post.assert_called_once_with(
#             "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
#             auth=("api", f"{settings.MAILGUN_API_KEY}"),
#             data={
# <AUTHOR> <EMAIL>",
#                 "to": "<EMAIL>",
#                 "subject": "Test Subject",
#                 "html": None,
#                 "text": "Test email body",
#             },
#         )

#         self.assertEqual(result, "EMAIL SENT")

#     @patch("main.tasks.requests.post")
#     def test_send_email_failure(self, mock_post):

#         mock_post.side_effect = Exception("API Error")
#         result = send_email(
#             email="<EMAIL>", subject="Test Subject", template_dir="sponsor_confirmation_email.html", use_template=True, name="Test User"
#         )

#         self.assertEqual(result, "EMAIL FAILED!")
