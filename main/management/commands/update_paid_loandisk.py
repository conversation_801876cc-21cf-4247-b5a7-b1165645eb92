from django.core.management.base import BaseCommand

from main.helpers.loandisk import Loandisk<PERSON><PERSON>per
from main.models import LotteryModel


class Command(BaseCommand):
    help = "THIS COMMAND HELP UPDATE PAID LOTTERY ON LOAN DISK"

    def handle(self, *args, **kwargs):
        lottery_queryset = LotteryModel.objects.filter(exist_on_loandisk=True, paid=True, loandisk_status_id="216235").order_by("-id")[:500]

        if lottery_queryset:
            for lottery in lottery_queryset:
                if lottery.user_profile is None:
                    print(f"User profile is None. \n\n unique_id {lottery.unique_id}\n phone{lottery.phone} \n\n\n\n")
                else:
                    data = {
                        "player_loandisk_id": lottery.user_profile.loandisk_player_id,
                        "phone": lottery.phone,
                        "lucky_num": lottery.lucky_number,
                        "id": lottery.id,
                        "stake_amount": lottery.stake_amount,
                        "bank_code": lottery.user_profile.bank_code,
                        "account_num": lottery.user_profile.account_num,
                        "bank_name": lottery.user_profile.bank_name,
                        "band": lottery.band,
                        "unique_id": lottery.unique_id,
                        "loan_status_id": "216234",
                        "loan_id": lottery.loandisk_pool_id,
                    }

                    loandisk_instance = LoandiskHelper()
                    loandisk_response = loandisk_instance.update_pool(**data)

                    if isinstance(loandisk_response, dict) and loandisk_response["response"]["loan_id"]:
                        lottery.loandisk_status_id = "216234"
                        lottery.save()
                        print("DONE SAVING IN DATABASE")

        print("DONE")
