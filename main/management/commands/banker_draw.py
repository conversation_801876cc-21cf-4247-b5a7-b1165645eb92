from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # LottoWinners.objects.all().delete()
        # LottoTicket.objects.all().delete()
        # LottoTicket.banker_draw()
        # create_dommy_salary_for_life_data()
        # lotto_winner = LottoWinners.objects.all().aggregate(Sum("earning"))
        # print(lotto_winner)

        # i_cash_started_count_time_string = RedisStore.get_data(key="i_cash__filter_date_string").decode()
        # datetime_object = datetime.datetime.fromisoformat(i_cash_started_count_time_string)
        # lotto_qs = LottoTicket.objects.filter(lottery_type="INSTANT_CASHOUT", updated_at__gte=datetime_object,
        #                                       channel="POS_AGENT", paid=True)
        #
        # print(lotto_qs.count())
        # print(datetime_object)
        pass
