from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta, time
from main.models import LottoTicket
from django.db import models

class Command(BaseCommand):
    help = 'Prints the number of BANKER tickets for each 30-minute batch (8am-9pm) from 22nd May 2025 till today, only paid and not via telco.'

    def handle(self, *args, **options):
        start_date = datetime(2025, 5, 22, 13, 30, 0, tzinfo=timezone.get_current_timezone())
        end_date = timezone.now()

        # Get all relevant tickets
        tickets = LottoTicket.objects.filter(
            lottery_type="BANKER",
            paid=True,
            played_via_telco_channel=False,
            date__gte=start_date,
            date__lte=end_date
        ).order_by('date')

        # Group tickets by day and 30-min batch
        current_date = start_date.date()
        from main.models import LotteryBatch
        while current_date <= end_date.date():
            day_start = datetime.combine(current_date, time(8, 0), tzinfo=timezone.get_current_timezone())
            day_end = datetime.combine(current_date, time(21, 0), tzinfo=timezone.get_current_timezone())
            prev_day = current_date - timedelta(days=1)
            prev_day_nine_pm = datetime.combine(prev_day, time(21, 0), tzinfo=timezone.get_current_timezone())
            curr_day_eight_am = day_start
            # Only print this batch if current_date > start_date.date() (i.e., not the very first day)
            if current_date > start_date.date():
                overnight_tickets = tickets.filter(date__gte=prev_day_nine_pm, date__lt=curr_day_eight_am)
                batch_agg = overnight_tickets.aggregate(
                    count=models.Count('id'),
                    total_amount_paid=models.Sum('amount_paid'),
                    total_rtp=models.Sum('rtp'),
                    total_effective_rtp=models.Sum('effective_rtp')
                )
                count = batch_agg['count'] or 0
                total_amount_paid = batch_agg['total_amount_paid'] or 0
                total_rtp = batch_agg['total_rtp'] or 0
                total_effective_rtp = batch_agg['total_effective_rtp'] or 0
                print(f"{current_date.strftime('%Y-%m-%d')} (Prev) 21:00 - 08:00: {count} tickets | Amount Paid: {total_amount_paid} | RTP: {total_rtp} | Effective RTP: {total_effective_rtp}")
                if count > 0:
                    period_str = f"{curr_day_eight_am.strftime('%Y-%m-%d')}_Prev2100-0800"
                    batch_uuid = f"BANKER-{period_str}-BATCH"
                    batch, _ = LotteryBatch.objects.get_or_create(
                        batch_uuid=batch_uuid,
                        defaults={
                            'lottery_type': 'BANKER',
                            'is_active': False,
                            'draw_date': curr_day_eight_am,
                        }
                    )
                    overnight_tickets.update(batch=batch)
                    # Update total_accumulated_paid
                    batch.total_accumulated_paid = (batch.total_accumulated_paid or 0) + total_amount_paid
                    batch.save(update_fields=["total_accumulated_paid"])
            # 8:00am to 9:00pm batches
            batch_start = curr_day_eight_am
            # Align batch_start to the next 30-min slot if start_date is not exactly on the slot
            # if current_date == start_date.date() and batch_start < start_date:
            #     # Find the first slot >= start_date
            #     minutes_since_8am = (start_date.hour - 8) * 60 + start_date.minute
            #     slot = (minutes_since_8am + 29) // 30  # round up to next slot
            #     batch_start = datetime.combine(current_date, time(8, 0), tzinfo=timezone.get_current_timezone()) + timedelta(minutes=slot*30)
            while batch_start < day_end:
                batch_end = batch_start + timedelta(minutes=30)
                slot_tickets = tickets.filter(date__gte=batch_start, date__lt=batch_end)
                batch_agg = slot_tickets.aggregate(
                    count=models.Count('id'),
                    total_amount_paid=models.Sum('amount_paid'),
                    total_rtp=models.Sum('rtp'),
                    total_effective_rtp=models.Sum('effective_rtp')
                )
                count = batch_agg['count'] or 0
                total_amount_paid = batch_agg['total_amount_paid'] or 0
                total_rtp = batch_agg['total_rtp'] or 0
                total_effective_rtp = batch_agg['total_effective_rtp'] or 0
                print(f"{batch_start.strftime('%Y-%m-%d %H:%M')} - {batch_end.strftime('%H:%M')}: {count} tickets | Amount Paid: {total_amount_paid} | RTP: {total_rtp} | Effective RTP: {total_effective_rtp}")
                if count > 0:
                    period_str = f"{batch_start.strftime('%Y-%m-%d_%H%M')}-{batch_end.strftime('%H%M')}"
                    batch_uuid = f"BANKER-{period_str}-BATCH"
                    batch, _ = LotteryBatch.objects.get_or_create(
                        batch_uuid=batch_uuid,
                        defaults={
                            'lottery_type': 'BANKER',
                            'is_active': False,
                            'draw_date': batch_start+ timedelta(minutes=30),
                        }
                    )
                    slot_tickets.update(batch=batch)
                    # Update total_accumulated_paid
                    batch.total_accumulated_paid = (batch.total_accumulated_paid or 0) + total_amount_paid
                    batch.save(update_fields=["total_accumulated_paid"])
                batch_start = batch_end
            current_date += timedelta(days=1)



