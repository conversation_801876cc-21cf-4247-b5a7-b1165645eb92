from django.core.management.base import BaseCommand

from main.helpers.whisper_sms_managers import sms_recollect_bank_account_nums
from main.models import DisbursementTable


class Command(BaseCommand):
    help = "THIS COMMAND HELP IN POSTING PLAYERS TO LOAN DISK"

    def handle(self, *args, **kwargs):
        # batch = LotteryBatch.objects.filter(batch_uuid="f842a1c9-1d85-4aaf-9d4b-8914bf2142a2").last()
        # batch = LotteryBatch.objects.filter(batch_uuid="Jun-f3eff5b1-4").last()
        # batch = LotteryBatch.objects.all().order_by("-id")[1]

        # get_all_players_on_disb_table = DisbursementTable.objects.filter(player_phone_num="*************")
        get_all_players_on_disb_table = DisbursementTable.objects.filter(id__in=range(126, 312))

        for i in get_all_players_on_disb_table:
            send_sms = sms_recollect_bank_account_nums(i.player_phone_num)
            print(send_sms)
            print(i.id)
