import json
import uuid
from time import sleep

from django.conf import settings
from django.core.management.base import BaseCommand
from django.db.models import Sum

from main.helpers.helper_functions import notify_admin_on_fund_balance_for_payout
from main.helpers.woven_manager import WovenHelper
from main.models import (
    DisbursementTable,
    LotteryBatch,
    PayoutTransactionTable,
    UserProfile,
)
from main.ussd.bankdb import filter_bank


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        get_batch = LotteryBatch.objects.filter(batch_uuid="Jul-a1202b45-7").last()
        _disbursement_table = DisbursementTable.objects.filter(stattus="NOT_INITIATED", lottery_batch=get_batch)

        total_amount_disbursable = 0.00

        for i in _disbursement_table:
            total_amount_disbursable += float(i.payout_amount)

        print("total disbursable amount", total_amount_disbursable)

        # _disbursement_table = DisbursementTable.objects.filter(Q(stattus = "NOT_INITIATED"))
        print(_disbursement_table)
        _woven_helper = WovenHelper()

        total_amount_needed = list(_disbursement_table.aggregate(Sum("payout_amount")).values())[0]
        total_disbursable_count = _disbursement_table.count()

        # Check Float Woven Balance
        woven_helper = WovenHelper()
        check_woven_balance = woven_helper.woven_balance()

        woven_balance = 0.00
        woven_charge = 10.00
        # sleep(30)

        if check_woven_balance.get("status") == "success":
            woven_balance = float(check_woven_balance.get("data").get("available_balance"))
            has_checked = True
        else:
            has_checked = False
            print(check_woven_balance)

        if has_checked is True:
            if woven_balance < (total_amount_needed + (total_disbursable_count * woven_charge)):
                notify_admin_on_fund_balance_for_payout(total_amount_needed=total_amount_needed)
                print(check_woven_balance)

            else:
                disbursed_list_id = []

                for disbursement in _disbursement_table:
                    print(disbursed_list_id)

                    sleep(5)

                    if disbursement.id in disbursed_list_id:
                        print("This transaction has been sent before, Cant go again")

                    else:
                        print(f"{disbursement.player_phone_num} - {disbursement.payout_amount}")
                        user_profile = UserProfile.objects.filter(phone_number=disbursement.player_phone_num).last()
                        if user_profile:
                            print(user_profile.bank_name)
                            if user_profile.account_num is not None and user_profile.bank_code is not None:
                                full_name = user_profile.first_name + " " + user_profile.last_name
                                reference = f"LLO-{str(uuid.uuid4())}-{disbursement.id}"
                                get_bank_details = filter_bank(cbn_code=user_profile.bank_code)

                                if get_bank_details is None:
                                    pass
                                    # get_bank_details = {
                                    #     "bank_code": "000012",
                                    #     "cbn_code": "039",
                                    #     "name": "Stanbic IBTC Plc",
                                    #     "bank_short_name": "Stanbic IBTC Plc",
                                    #     "disabled_for_vnuban": None,
                                    # }
                                else:
                                    payout = PayoutTransactionTable.objects.create(
                                        source="WOVEN",
                                        amount=disbursement.payout_amount,
                                        disbursement_unique_id=f"{disbursement.id}-{disbursement.player_phone_num}-{str(uuid.uuid4)[0:3]}",
                                        phone=user_profile.phone_number,
                                        payout_trans_ref=reference,
                                        bank_code=user_profile.bank_code,
                                        name=full_name,
                                        batch=disbursement.lottery_batch,
                                        disbursement=disbursement,
                                        balance_before=disbursement.payout_amount,
                                        balance_after=disbursement.payout_amount,
                                        joined_since=user_profile.get_duration(),
                                        name=full_name,
                                    )

                                    woven_payload = {
                                        "source_account": settings.WOVEN_DISBURSEMENT_SOURCE_ACCOUNT,
                                        "beneficiary_account_name": full_name,
                                        "beneficiary_nuban": user_profile.account_num,
                                        "beneficiary_bank_code": user_profile.bank_code,
                                        "bank_code_scheme": "NIP",
                                        "currency_code": "NGN",
                                        "narration": "LibPlay-Disbursement",
                                        # "callback_url": settings.WOVEN_CALL_BACK,
                                        "reference": reference,
                                        "amount": round(disbursement.payout_amount),
                                    }

                                    payout.payout_payload = json.dumps(woven_payload)
                                    payout.save()

                                    # update disbursement table
                                    disbursement.payout_account_num = user_profile.account_num
                                    disbursement.payout_bank_name = user_profile.bank_name
                                    disbursement.payout_bank_code = user_profile.bank_code
                                    disbursement.stattus = "PROCESSING"
                                    disbursement.disbursement_payload = json.dumps(woven_payload)

                                    # Add to list so it doesnt go again
                                    disbursed_list_id.append(disbursement.id)

                                    disbursement.save()

                                    # initiate disbursement
                                    woven_payload["PIN"] = settings.WOVEN_DISBURSEMENT_PAYMENT_PIN
                                    payout_response = _woven_helper.initaite_payout_to_winners(**woven_payload)
                                    print(payout_response)

                                    payout.source_response_payload = json.dumps(payout_response)
                                    payout.save()

                                    if payout_response is None:
                                        pass
                                    elif payout_response.get("status") == "success" and payout_response.get("data"):
                                        payout.source_unique_ref = payout_response.get("data").get("unique_reference")
                                        payout.save()
                                    # else:
                                    #     payout.source_unique_ref = payout_response.get("data").get("pay-")
                                    #     payout.save()

                                    disbursement.stattus = "INITIATED"
                                    disbursement.save()
                                    # disbursement.is_disbursed = True

                            else:
                                print("User has no bank details \n\n\n\n")
                        else:
                            print("\n\n\n User Not found \n\n\n")

                print("Done with the loop \n\n\n\n")

        else:
            print("Could not check balance \n\n\n\n")
