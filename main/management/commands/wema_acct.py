from django.core.management.base import BaseCommand

from main.tasks import send_email


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # create_wema_acct_for_existing_users.delay()
        # i = send_email(email="<EMAIL>", otp="123456")

        i = send_email(recipient="<EMAIL>", subject="Email Verification", template_dir="email_verification.html", otp="123456")
        print(i)
