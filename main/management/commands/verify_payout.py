from time import sleep

from django.core.management.base import BaseCommand
from django.db.models import Q

from main.helpers.woven_manager import WovenHelper
from main.models import LotteryBatch, PayoutTransactionTable


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # transactions = PayoutTransaction.objects.select_related('disbursment').filter(is_verified = False)
        # transactions = PayoutTransactionTable.objects.all()
        get_batch = LotteryBatch.objects.filter(batch_uuid="Jul-a1202b45-7").last()

        transactions = PayoutTransactionTable.objects.filter(Q(disbursed=False) & Q(is_verified=False) & Q(batch=get_batch))
        _woven = WovenHelper
        if transactions:
            for trans in transactions:
                sleep(3)

                # if trans.source_unique_ref is None or trans.source_unique_ref == "":
                unique_ref_response = _woven.get_transaction_with_ref(trans.payout_trans_ref)
                # print(unique_ref_response)

                if isinstance(unique_ref_response, dict) and unique_ref_response.get("data").get("payout_transactions"):
                    print("yesss it is")
                    trans.source_unique_ref = unique_ref_response.get("data")["payout_transactions"][0]["unique_reference"]
                    # trans.source_unique_ref =  unique_ref_response.get("data")["payout_transactions"][0]["unique_reference"]
                    trans.save()

                    if (
                        unique_ref_response["data"]["payout_transactions"][0]["status"] == "ACTIVE"
                        or unique_ref_response["data"]["payout_transactions"][0]["status"].casefold() == "active"
                    ):
                        print(unique_ref_response["data"]["payout_transactions"][0]["status"])
                        trans.is_verified = True
                        trans.disbursed = True
                        trans.payout_verified = True
                        trans.verification_response_payload = unique_ref_response
                        trans.disbursement.is_disbursed = True
                        trans.disbursement.payyment_verified = True
                        trans.disbursement.stattus = "DISBURSED"
                        trans.disbursement.save()
                        trans.save()

                    elif (
                        unique_ref_response["data"]["payout_transactions"][0]["status"] == "FAILED"
                        or unique_ref_response["data"]["payout_transactions"][0]["status"].casefold() == "failed"
                    ):
                        print(unique_ref_response["data"]["payout_transactions"][0]["status"])
                        trans.is_verified = True
                        trans.disbursed = False
                        trans.verification_response_payload = unique_ref_response
                        # trans.disbursement.stattus = "FAILED"
                        trans.disbursement.is_disbursed = False
                        trans.disbursement.payyment_verified = True
                        trans.disbursement.save()
                        trans.save()

                # elif len(unique_ref_response["data"]["payout_transactions"]) == 0:

                #         print(unique_ref_response["data"]["payout_transactions"])
                #         print("here nowwwww")
                #         trans.is_verified = True
                #         trans.disbursed = False
                #         trans.verification_response_payload = unique_ref_response
                #         trans.disbursement.stattus = "FAILED"
                #         trans.disbursement.is_disbursed = False
                #         trans.disbursement.payyment_verified = True
                #         trans.disbursement.save()
                #         trans.save()

                # else:
                #     payout_verification = WovenHelper().woven_payment_verification(trans.source_unique_ref)
                #     if isinstance(payout_verification, dict) and payout_verification["data"]["transactions"]:
                #         if payout_verification["data"]["transactions"][0]["status"] == "Successful" or payout_verification["data"]["transactions"][0]["status"] == "successful":
                #             print(payout_verification["data"]["transactions"][0]["status"])
                #             trans.is_verified = True
                #             trans.disbursed = True
                #             trans.disbursement.stattus = "DISBURSED"
                #             trans.disbursement.is_disbursed = True
                #             trans.disbursement.payyment_verified = True
                #             trans.save()

                #         elif payout_verification["data"]["transactions"][0]["status"] == "FAILED" or payout_verification["data"]["transactions"][0]["status"] == "failed":
                #             print(payout_verification["data"]["transactions"][0]["status"])
                #             trans.is_verified = True
                #             trans.disbursed = False
                #             trans.disbursement.stattus = "FAILED"
                #             trans.disbursement.is_disbursed = False
                #             trans.disbursement.payyment_verified = True
                #             trans.disbursement.save()
                #             trans.save()

                # if trans.source_unique_ref is None or trans.source_unique_ref == "":
                #     unique_ref_response = _woven.get_transaction_with_ref(trans.payout_trans_ref)
                #     print(unique_ref_response)

                #     if isinstance(unique_ref_response, dict) and unique_ref_response.get("data").get("payout_transactions"):

                #         trans.source_unique_ref =  unique_ref_response.get("data")["payout_transactions"][0]["unique_reference"]
                #         trans.source_unique_ref =  unique_ref_response.get("data")["payout_transactions"][0]["unique_reference"]
                #         trans.save()

                #         if unique_ref_response["data"]["payout_transactions"][0]["status"] == "ACTIVE" or unique_ref_response["data"]["payout_transactions"][0]["status"].casefold() == "active":
                #             print(unique_ref_response["data"]["payout_transactions"][0]["status"])
                #             trans.is_verified = True
                #             trans.disbursed = True
                #             trans.disbursement.is_disbursed = True
                #             trans.disbursement.payyment_verified = True
                #             trans.disbursement.stattus = "DISBURSED"
                #             trans.save()

                #         elif unique_ref_response["data"]["payout_transactions"][0]["status"] == "FAILED" or unique_ref_response["data"]["payout_transactions"][0]["status"].casefold() == "failed":
                #             print(unique_ref_response["data"]["payout_transactions"][0]["status"])
                #             trans.is_verified = True
                #             trans.disbursed = False
                #             trans.disbursement.stattus = "FAILED"
                #             trans.disbursement.is_disbursed = False
                #             trans.disbursement.payyment_verified = True
                #             trans.disbursement.save()
                #             trans.save()
                # else:
                #     payout_verification = WovenHelper().woven_payment_verification(trans.source_unique_ref)
                #     if isinstance(payout_verification, dict) and payout_verification["data"]["transactions"]:
                #         if payout_verification["data"]["transactions"][0]["status"] == "Successful" or payout_verification["data"]["transactions"][0]["status"] == "successful":
                #             print(payout_verification["data"]["transactions"][0]["status"])
                #             trans.is_verified = True
                #             trans.disbursed = True
                #             trans.disbursement.stattus = "DISBURSED"
                #             trans.disbursement.is_disbursed = True
                #             trans.disbursement.payyment_verified = True
                #             trans.save()

                #         elif payout_verification["data"]["transactions"][0]["status"] == "FAILED" or payout_verification["data"]["transactions"][0]["status"] == "failed":
                #             print(payout_verification["data"]["transactions"][0]["status"])
                #             trans.is_verified = True
                #             trans.disbursed = False
                #             trans.disbursement.stattus = "FAILED"
                #             trans.disbursement.is_disbursed = False
                #             trans.disbursement.payyment_verified = True
                #             trans.disbursement.save()
                #             trans.save()
