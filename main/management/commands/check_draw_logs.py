from django.core.management.base import BaseCommand
from django.utils.timezone import now
from main.models import DrawLog
from django.core.mail import send_mail

class Command(BaseCommand):
    help = 'Check draw logs and send email if any draw is missed'

    def handle(self, *args, **kwargs):
        missed_draws = []
        for log in DrawLog.objects.all():
            time_diff = (now() - log.last_run_time).total_seconds() / 60
            if time_diff > log.frequency_minutes:
                missed_draws.append(log.draw_name)

        if missed_draws:
            send_mail(
                'Missed Draws Alert',
                f'The following draws have missed their schedule: {", ".join(missed_draws)}',
                '<EMAIL>',
                ['<EMAIL>'],
                fail_silently=False,
            )
            self.stdout.write(self.style.ERROR(f"Missed draws: {', '.join(missed_draws)}"))
        else:
            self.stdout.write(self.style.SUCCESS("All draws are on schedule."))
