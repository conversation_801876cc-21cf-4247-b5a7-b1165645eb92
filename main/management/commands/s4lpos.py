from django.core.management.base import BaseCommand


def celery_salary_for_life_draw_pos():
    from main.models import LottoTicket

    try:
        LottoTicket.salary_for_life_draw_pos()
        print("DONE S4L")
        return "Salary for Life Draw Completed"
    except TypeError:
        print("FAILED THIS TIME. RETRYING")
        LottoTicket.salary_for_life_draw_pos()
        print("DONE S4L")
        return "Salary for Life Draw Completed"


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        celery_salary_for_life_draw_pos()
