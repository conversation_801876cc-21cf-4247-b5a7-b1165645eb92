import os
import subprocess

from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Install pre-commit hooks"

    def handle(self, *args, **kwargs):
        script_path = os.path.join(os.path.dirname(__file__), "../../../install_hooks.sh")
        result = subprocess.run(["bash", script_path], capture_output=True, text=True)
        self.stdout.write(self.style.SUCCESS(result.stdout))
        if result.stderr:
            self.stderr.write(self.style.ERROR(result.stderr))
