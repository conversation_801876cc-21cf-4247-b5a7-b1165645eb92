from django.core.management.base import BaseCommand

from main.helpers.loandisk import create_pool_on_loandisk
from main.models import LotteryModel


class Command(BaseCommand):
    help = "THIS COMMAND HELP IN CREATING PLAYERS POOL ON LOAN DISK"

    def handle(self, *args, **kwargs):
        lottery_queryset = LotteryModel.objects.filter(exist_on_loandisk=False).order_by("-id")[:500]

        if lottery_queryset:
            for lottery in lottery_queryset:
                _create_pool_response = create_pool_on_loandisk(
                    lottery.phone,
                    lottery.lucky_number,
                    lottery.id,
                    lottery.stake_amount,
                    lottery.band,
                    lottery.unique_id,
                )

            if isinstance(_create_pool_response, dict) and _create_pool_response["response"]["loan_id"]:
                lottery.loandisk_pool_id = _create_pool_response["response"]["]loan_id"]
                lottery.exist_on_loandisk = True
                lottery.save()
