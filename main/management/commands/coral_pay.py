from django.core.management.base import BaseCommand

from main.helpers.coral_pay import CoralpayUSSDWebHelper


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # coral_pay = CoralpayHelper()
        # kwargs = {
        #     "Username": "whispa",
        #     "Password": "BX@&Xx6bx6OWSs@5"
        # }
        # print(coral_pay.coral_pay_login())

        # print(coral_pay.initiate_payment())

        coral_pay = CoralpayUSSDWebHelper()
        kwargs = {"Username": "whispa", "Password": "BX@&Xx6bx6OWSs@5"}
        print(coral_pay.coral_pay_login(**kwargs))

        # print(coral_pay.initiate_payment())
