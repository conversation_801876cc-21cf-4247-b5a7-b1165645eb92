import os
from django.core.management.base import BaseCommand, CommandError
from main.models import LottoTicket, LotteryBatch

class Command(BaseCommand):
    help = 'Exports the "plays" variable content for a given batch_id to static/plays.txt'

    def add_arguments(self, parser):
        parser.add_argument('batch_id', type=str, help='The batch_uuid of the LotteryBatch to process')

    def handle(self, *args, **options):
        batch_id = options['batch_id']
        self.stdout.write(f"Processing batch_id: {batch_id}")

        try:
            match_batch = LotteryBatch.objects.get(batch_uuid=batch_id)
        except LotteryBatch.DoesNotExist:
            raise CommandError(f'LotteryBatch with batch_uuid "{batch_id}" does not exist.')
        except LotteryBatch.MultipleObjectsReturned:
            raise CommandError(f'Multiple LotteryBatch entries found for batch_uuid "{batch_id}". Please provide a unique batch_uuid.')


        # Get the relevant lottery tickets
        lottery_qs = LottoTicket.objects.filter(batch=match_batch, paid=True)

        if not lottery_qs.exists():
            self.stdout.write(self.style.WARNING(f"No paid LottoTickets found for batch_id: {batch_id}"))
            return

        # Construct the 'plays' variable
        plays = list(
            map(
                lambda x: (x.number_of_ticket, [int(i) for i in x.ticket.split(",")]),
                lottery_qs,
            )
        )

        # Write 'plays' to static/plays.txt
        static_dir = "static"
        if not os.path.exists(static_dir):
            os.makedirs(static_dir)
            self.stdout.write(f"Created directory: {static_dir}")
        
        file_path = os.path.join(static_dir, "plays.txt")
        try:
            with open(file_path, "w") as f:
                f.write(str(plays))
            self.stdout.write(self.style.SUCCESS(f"'plays' data written to {file_path}"))
        except IOError as e:
            raise CommandError(f'Error writing to file {file_path}: {e}')
