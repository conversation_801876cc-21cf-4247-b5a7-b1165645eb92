import datetime
import itertools
import multiprocessing

from django.core.management.base import BaseCommand
from django.db import connection

from main.models import LotteryBatch, LottoTicket


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        prices = {
            1: 5000.00 / 1 * 1,
            2: 15000.00 / 2 * 1,
            3: 50000.00 / 3 * 1,
            4: 150000.00 / 4 * 1,
            5: 250000.00 / 5 * 1,
            6: 500000.00 / 6 * 1,
            7: 750000.00 / 7 * 1,
            8: 900000.00 / 8 * 1,
            9: 1250000.00 / 9 * 1,
            10: 5000000.00 / 10 * 1,
        }

        def play(plays, prices, queries_list):
            print("RUNNING NOW.!@@@")

            random_combo = list(itertools.combinations(range(1, 50), 5))

            def search_number_occurences(numlist1: list, numlist2: list, number_of_match_limit=3) -> int:
                """COUNT COMMON NUMBERS"""
                match_count = 0
                # number_of_match_limit = 2 # random.choice([2, 1, 2, 1, 2, 1, 2])

                for number in numlist1:
                    if number in numlist2[1]:
                        match_count += 1

                return match_count if match_count > (number_of_match_limit - 1) else False

            def filter_winnings(combo, plays, prices, jackpot_amount):
                occurences = map(
                    lambda user_play: search_number_occurences(combo, user_play, 3), plays
                )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

                play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
                over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

                play_occurences_with_amount = map(
                    lambda played: get_potential_winning(played, prices, jackpot_amount),
                    over3ocurrences,
                )
                data = list(play_occurences_with_amount)

                return data

            def get_potential_winning(data, prices, jackpot):
                """
                data[1][0] : number_of_lines
                data[0] : number_of_matches
                """

                base_price = prices[data[1][0]]
                sharing = {5: 1, 4: 1, 3: 0.6, 2: 0.6 * 0.5}

                if data[0] == 5:
                    winning = jackpot

                else:
                    winning = (
                        (base_price * sharing[data[0]])
                        # (base_price / data[1][0] * sharing[data[0]])
                        if not sharing[data[0]] == 5
                        else jackpot
                    )

                response = [*data, int(winning)]

                return response

            then = datetime.datetime.now()
            combo_dict = {}
            queries = 0
            query = ""

            for index, combo in enumerate(random_combo):
                combo_dict[tuple(combo)] = 0

            for index, combo in enumerate(random_combo):
                winnings = filter_winnings(combo, plays, prices, 9999999999)
                num_wins = len(winnings)

                if combo == (19, 20, 33, 37, 49):
                    print(combo, ":", num_wins)

                if num_wins > 0:
                    _, __, amounts = zip(*winnings)
                    total_amount = sum(amounts)

                    queries += 1
                    query += f"""
                                UPDATE main_s4lcombos
                                SET value = value + {total_amount},
                                    winners = winners + {num_wins}
                                WHERE combo = '{str(combo)}';
                            """

                    combo_dict[tuple(combo)] += total_amount

            queries_list.append(query)
            print("BREAKING...Queries", queries)

            now = datetime.datetime.now()
            print((now - then).total_seconds())

        for i in range(2):
            # play(plays, prices)
            then = datetime.datetime.now()

            active_batch: LotteryBatch = LotteryBatch.objects.filter(
                lottery_type="SALARY_FOR_LIFE", batch_uuid="Mar-c2fb1e9b-be4b-4b5f-8f5b-349dabb9bac2"
            )

            if active_batch.count() == 1:
                active_batch = active_batch.last()
                print("Active batch->", active_batch.batch_uuid)

            # tickets = LottoTicket.objects.filter(lottery_type="SALARY_FOR_LIFE", s4l_drawn=False, batch=active_batch, paid=True)
            tickets = LottoTicket.objects.filter(lottery_type="SALARY_FOR_LIFE", s4l_drawn=False, batch=active_batch, paid=True)
            print("Active batch->", tickets.count())

            # tickets = tickets[:500]
            print("Target Tickets->", tickets.count())

            plays = list(map(lambda _object: [_object.number_of_ticket, list(map(lambda val: int(val), _object.ticket.split(",")))], tickets))
            then = datetime.datetime.now()

            manager = multiprocessing.Manager()
            queries_list = manager.list()

            processed_ticket_ = LottoTicket.objects.filter(id__in=[x.id for x in tickets])
            processed_ticket_.update(s4l_drawn=True)
            jobs = []
            step = 600

            for i in range(0, 4000, step):
                proccess = multiprocessing.Process(target=play, args=(plays[i : i + step], prices, queries_list))
                jobs.append(proccess)
                proccess.start()

            for proc in jobs:
                proc.join()

            # creating processes
            # t1 = multiprocessing.Process(target=play, args=(plays[:25], prices))
            # t2 = multiprocessing.Process(target=play, args=(plays[25:50], prices))
            # t3 = multiprocessing.Process(target=play, args=(plays[50:75], prices))
            # t4 = multiprocessing.Process(target=play, args=(plays[75:101], prices))
            # t5 = multiprocessing.Process(target=play, args=(plays[101:125], prices))
            # t6 = multiprocessing.Process(target=play, args=(plays[126:151], prices))
            # t7 = multiprocessing.Process(target=play, args=(plays[175:200], prices))
            # t8 = multiprocessing.Process(target=play, args=(plays[200:226], prices))

            # t1.start()
            # t2.start()
            # t3.start()
            # t4.start()
            # t5.start()
            # t6.start()
            # t7.start()
            # t8.start()

            # t1.join()
            # t2.join()
            # t3.join()
            # t4.join()
            # t5.join()
            # t6.join()
            # t7.join()
            # t8.join()

            print("Done!")
            now = datetime.datetime.now()
            print((now - then).total_seconds())

            with connection.cursor() as cursor:
                print("STARTING QUERIES TO DB")
                # print(queries_list)
                for query in queries_list:
                    if not query:
                        continue

                    cursor.execute(query)
                    print("Executing...Queries")
