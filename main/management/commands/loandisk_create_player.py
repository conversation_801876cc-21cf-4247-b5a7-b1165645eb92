from django.core.management.base import BaseCommand

from main.helpers.loandisk import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from main.models import UserProfile


class Command(BaseCommand):
    help = "THIS COMMAND HELP IN POSTING PLAYERS TO LOAN DISK"

    def handle(self, *args, **kwargs):
        player_queryset = UserProfile.objects.filter(on_loandisk=False).order_by("-id")[:500]
        print(player_queryset)
        if player_queryset:
            for player in player_queryset:
                loandisk_instance = LoandiskHelper()

                data = {
                    "phone": player.phone_number,
                    "account_num": player.account_num,
                    "account_name": player.account_name,
                    "bank_code": player.bank_code,
                    "id": player.id,
                    "loandisk_player_id": player.loandisk_player_id,
                }

                post_player = loandisk_instance.create_player(**data)

                if isinstance(post_player, dict) and post_player["response"]["borrower_id"]:
                    player.loandisk_player_id = post_player["response"]["borrower_id"]
                    player.on_loandisk = True
                    player.save()

        print("DONE")
