import base64
import random
import re
import string
import uuid
from datetime import datetime, time, timedelta
from random import randint

import requests
from django.conf import settings
from django.core.management.base import BaseCommand
from django.db import connection
from django.db.models import Sum

# from decisioning_engine.draw import InstantCashoutDraw, SalaryForLifeDraw, WyseCashDraw
# from decisioning_engine.game_final import draw, filter_winnings
from africa_lotto.tasks import process_winner_data
from main.api.api_lottery_helpers import generate_game_play_id
from main.helpers.redis_storage import RedisDictStorage, RedisStorage
from main.helpers.vfd_disbursement_helper import VfdDisbursementHelperFunc
from main.helpers.whisper_sms_managers import (
    send_congrats_sms_to_winners_without_account_num,
)
from main.models import (
    Jack<PERSON>,
    Jack<PERSON>Winner,
    LotteryBatch,
    LotteryGlobalJackPot,
    LotteryModel,
    LotteryTicketPrice,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    UserProfile,
)
from main.tasks import celery_sms_for_wyse_lost, celery_sms_for_wyse_winners
from main.ussd.helpers import Utility
from pos_app.models import Agent
from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry
from pos_app.utils import DommyName, serialize_ticket
from wallet_app.models import GeneralWithdrawableWallet, UserWallet
from wyse_ussd.models import (
    CoralpayTransactions,
    CoralpayUserCode,
    PendingAsyncTask,
    UssdPayment,
)
from wyse_ussd.tasks import (
    celery_deactivate_telco_subscription,
    celery_teclo_airtime_charge_datasync_handler,
    wyse_cash_lost_sms_on_telco,
)

number_of_plays = 5000
average_stake_perplay = 950

total_stake = number_of_plays * average_stake_perplay

rto = 0.3
rtp = (1 - rto) * total_stake

prices = {
    1: 5000.00,
    2: 15000.00,
    3: 50000.00,
    4: 150000.00,
    5: 250000.00,
    6: 500000.00,
    7: 750000.00,
    8: 900000.00,
    9: 1250000.00,
    10: 1500000.00,
}


def generate_random_name(length):
    VOWELS = "aeiou"
    CONSONANTS = "".join(set(string.ascii_lowercase) - set(VOWELS))

    word = ""
    for i in range(length):
        if i % 2 == 0:
            word += random.choice(CONSONANTS)
        else:
            word += random.choice(VOWELS)
    return word


def test_redis_lottery_payment_storage(batch_id):
    lottery_batch = LotteryBatch.objects.filter(batch_uuid=batch_id).last()

    if not lottery_batch:
        return "Batch not found"

    redis_db = RedisStorage(lottery_batch.batch_uuid)
    get_redis_item = redis_db.get_data()

    return ("redis response: ", get_redis_item)


def creat_dummy_users():
    for i in range(0, 500):
        try:
            UserProfile.objects.create(
                first_name=generate_random_name(5),
                last_name=generate_random_name(5),
                phone_number="234" + str(random.randint(7000000000, 7999999999)),
                email=generate_random_name(5) + "@gmail.com",
            )

        except Exception as e:
            print(e)

    # create dummy agents
    for i in range(0, 100):
        try:
            Agent.objects.create(
                first_name=generate_random_name(5),
                last_name=generate_random_name(5),
                phone="234" + str(random.randint(7000000000, 7999999999)),
                email=generate_random_name(5) + "@gmail.com",
            )

        except Exception as e:
            print(e)


def create_dommy_salary_for_life_data():
    if settings.DEBUG is False:
        return "Not allowed"

    users = [user for user in UserProfile.objects.all()]
    agents = [agent for agent in Agent.objects.all()]

    pontential_winning_choices = [
        5000,
        15000,
        50000,
        150000,
        250000,
        500000,
        750000,
        900000,
        1250000,
        1500000,
    ]

    for i in range(0, 500):
        user = random.choice(users)
        agent = random.choice(agents)

        get_current_batch = LotteryBatch.objects.filter(lottery_type="BANKER", is_active=True).last()

        if not get_current_batch:
            gloab_salary_for_life_jackpot = LotteryGlobalJackPot.objects.filter(lottery_type="SALARY_FOR_LIFE").last()
            if not gloab_salary_for_life_jackpot:
                gloab_salary_for_life_jackpot = LotteryGlobalJackPot.objects.create(lottery_type="SALARY_FOR_LIFE", threshold=1000000)

            get_current_batch = LotteryBatch.objects.create(
                lottery_type="BANKER",
                is_active=True,
                global_jackpot=None,
            )

        ticket = []
        for i in range(0, 5):
            ticket.append(random.randint(1, 10))

        # generate random stake amount
        stake_amount = random.randint(100, 1000)

        # generate random expected amount
        expected_amount = (random.randint(100, 1000)) * 2

        # generate random number_of_ticket
        random.randint(1, 10)

        # generate random channel
        channel = random.choice(["WEB", "MOBILE", "USSD", "POS_AGENT"])

        get_game_play_id = generate_game_play_id()

        identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        random_iterate_number = random.randint(1, 10)
        if channel == "POS_AGENT":
            lottery_instance = LottoTicket.objects.create(
                user_profile=user,
                agent_profile=agent,
                batch=get_current_batch,
                phone=user.phone_number,
                stake_amount=int(stake_amount),
                expected_amount=expected_amount,
                paid=False,
                number_of_ticket=random_iterate_number,
                channel=channel,
                game_play_id=get_game_play_id,
                lottery_type="BANKER",
                ticket=serialize_ticket(ticket),
                potential_winning=random.choice(pontential_winning_choices),
                identity_id=identity_id,
            )
        else:
            lottery_instance = LottoTicket.objects.create(
                user_profile=user,
                batch=get_current_batch,
                phone=user.phone_number,
                stake_amount=int(stake_amount),
                expected_amount=expected_amount,
                paid=False,
                number_of_ticket=random_iterate_number,
                channel=channel,
                game_play_id=get_game_play_id,
                lottery_type="BANKER",
                ticket=serialize_ticket(ticket),
                potential_winning=random.choice(pontential_winning_choices),
                identity_id=identity_id,
            )

        lottery_instance.paid = True
        lottery_instance.amount_paid = int(stake_amount)
        lottery_instance.save()

    return "Done"


def instant_cashout_dummy_data():
    users = UserProfile.objects.all()

    agents = Agent.objects.all()

    amount = 150

    stake_and_pont_choices = [
        {"stake_amount": amount, "total_winning_amount": 11250, "number_of_ticket": 1},
        {
            "stake_amount": amount * 2,
            "total_winning_amount": 13500,
            "number_of_ticket": 2,
        },
        {
            "stake_amount": amount * 3,
            "total_winning_amount": 15750,
            "number_of_ticket": 3,
        },
        {
            "stake_amount": amount * 4,
            "total_winning_amount": 18000,
            "number_of_ticket": 4,
        },
        {
            "stake_amount": amount * 5,
            "total_winning_amount": 18750,
            "number_of_ticket": 5,
        },
        {
            "stake_amount": amount * 6,
            "total_winning_amount": 22500,
            "number_of_ticket": 6,
        },
        {"stake_amount": 1000, "total_winning_amount": 25000, "number_of_ticket": 7},
    ]

    for i in range(0, 5000):
        user = random.choice(users)
        agent = random.choice(agents)

        get_current_batch = LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT", is_active=True).last()

        if not get_current_batch:
            get_current_batch = LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT", is_active=True)

        ticket = []
        for i in range(0, 4):
            ticket.append(random.randint(1, 40))

        # generate random stake amount
        random.randint(100, 1000)

        # generate random expected amount
        (random.randint(100, 1000)) * 2

        # generate random number_of_ticket
        random.randint(1, 10)

        # # generate random channel
        # channel = random.choice(["WEB", "MOBILE", "USSD", "POS_AGENT", "SYSTEM_BONUS"])
        channel = random.choice(["POS_AGENT", "SYSTEM_BONUS"])
        # channel = random.choice(["WEB", "MOBILE", "USSD"])

        get_game_play_id = generate_game_play_id()

        identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        random.randint(1, 10)

        stake_pont_pick = random.choice(stake_and_pont_choices)

        if channel == "POS_AGENT" or channel == "SYSTEM_BONUS":
            lottery_instance = LottoTicket.objects.create(
                user_profile=user,
                agent_profile=agent,
                batch=get_current_batch,
                phone=user.phone_number,
                stake_amount=stake_pont_pick["stake_amount"],
                expected_amount=stake_pont_pick["stake_amount"],
                potential_winning=stake_pont_pick["total_winning_amount"],
                paid=False,
                number_of_ticket=stake_pont_pick["number_of_ticket"],
                channel=channel,
                game_play_id=get_game_play_id,
                lottery_type="INSTANT_CASHOUT",
                ticket=serialize_ticket(ticket),
                identity_id=identity_id,
            )

            lottery_instance.paid = True
            lottery_instance.amount_paid = stake_pont_pick["stake_amount"]
            lottery_instance.save()

        else:
            lottery_instance = LottoTicket.objects.create(
                user_profile=user,
                batch=get_current_batch,
                phone=user.phone_number,
                stake_amount=stake_pont_pick["stake_amount"],
                expected_amount=stake_pont_pick["stake_amount"],
                potential_winning=stake_pont_pick["total_winning_amount"],
                paid=False,
                number_of_ticket=stake_pont_pick["number_of_ticket"],
                channel=channel,
                game_play_id=get_game_play_id,
                lottery_type="INSTANT_CASHOUT",
                ticket=serialize_ticket(ticket),
                identity_id=identity_id,
            )

            lottery_instance.paid = True
            lottery_instance.amount_paid = stake_pont_pick["stake_amount"]
            lottery_instance.save()


def update_lottery_payemnt():
    day = datetime.now().day

    query_Set = LottoTicket.objects.filter(lottery_type="SALARY_FOR_LIFE", date__day=day)

    # try:
    #     print("Total number of records: ", query_Set.count())
    #     for i in query_Set:
    #         LottoTicket.objects.filter(id=i.id).update(paid=True, amount_paid = i.expected_amount * 2)

    # except Exception:
    #     pass

    for i in query_Set:
        LottoTicket.objects.filter(id=i.id).update(paid=True, amount_paid=i.expected_amount * 2)

    # query_Set = LottoTicket.objects.filter(lottery_type="INSTANT_CASHOUT")

    # try:
    #     for i in query_Set:
    #         LottoTicket.objects.filter(id=i.id).update(paid=True, amount_paid = i.expected_amount * 2)
    # except Exception:
    #     pass

    # LottoTicket.salary_for_life_draw()

    return "Done"


def full_name_split(name):
    """
    This functions split and return user names in a dictonary
    """
    splited_names = name.split()
    names = {
        "first_name": splited_names[0] if len(splited_names) > 0 else "",
        "last_name": splited_names[1] if len(splited_names) > 1 else "",
        "middle_name": splited_names[2] if len(splited_names) > 2 else "",
        "full_name": name,
    }

    return names


def generate_random_lucky_number():
    n = 5
    range_start = 10 ** (n - 1)
    range_end = (10**n) - 1
    value = randint(range_start, range_end)
    return str(value)


def create_dommy_lottery_model():
    users = [user for user in UserProfile.objects.all()]

    band_choices = [10000, 50000, 100000, 200000]

    for i in range(0, 20):
        user = random.choice(users)

        get_current_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH", is_active=True).last()

        band = random.choice(band_choices)
        if band == 10000:
            pool = "TEN_THOUSAND"
            stake_amount = 100
        elif band == 50000:
            pool = "FIFTY_THOUSAND"
            stake_amount = 200
        elif band == 100000:
            pool = "ONE_HUNDRED_THOUSAND"
            stake_amount = 500
        elif band == 200000:
            pool = "TWO_HUNDRED_THOUSAND"
            stake_amount = 1000

        if get_current_batch is None:
            get_current_batch = LotteryBatch.objects.create(lottery_type="WYSE_CASH")

        ticket = []
        for i in range(0, 5):
            ticket.append(random.randint(1, 49))

        # generate random stake amount
        stake_amount = random.randint(100, 1000)

        # generate random expected amount
        (random.randint(100, 1000)) * 2

        # generate random number_of_ticket
        number_of_ticket = random.randint(1, 10)

        # generate random channel
        channel = random.choice(["WEB", "MOBILE", "USSD"])

        get_game_play_id = generate_game_play_id()

        if user.account_name is None:
            name = DommyName(6).generate_name()
        else:
            name = user.account_name

        names = full_name_split(name)

        lucky_number = f"{names.get('first_name')[0]}{names.get('last_name')[0]}-{generate_random_lucky_number()}"

        lottery_model_instance = LotteryModel.objects.create(
            user_profile=user,
            batch=get_current_batch,
            phone=user.phone_number,
            pool=pool,
            stake_amount=stake_amount,
            expected_amount=stake_amount,
            instance_number=number_of_ticket,
            channel=channel,
            game_play_id=get_game_play_id,
            lottery_type="WYSE_CASH",
            lucky_number=lucky_number,
            band=band,
        )

        lottery_model_instance.paid = True
        lottery_model_instance.amount_paid = lottery_model_instance.expected_amount
        lottery_model_instance.save()

    # get_current_batch.is_active = False
    # get_current_batch.save()

    return "Done"


def update_instant_cashout_system_pick():
    instant_cashout_tickets = LottoTicket.objects.filter(lottery_type="INSTANT_CASHOUT").distinct("game_play_id")
    if instant_cashout_tickets:
        for ticket in instant_cashout_tickets:
            winner_instance = LottoWinners.objects.filter(
                game_play_id=ticket.game_play_id,
                lottery__user_profile__phone_number=ticket.user_profile.phone_number,
            ).last()
            if winner_instance is None:
                generate_random_system_pick_number_for_instanct_cashout_tickets(ticket.game_play_id)
            else:
                LottoTicket().on_creating_instant_cashout_record_update_system_pick_num(
                    player_phone=ticket.user_profile.phone_number,
                    winning_ticket=ticket.ticket,
                    game_id=ticket.game_play_id,
                )


def generate_random_system_pick_number_for_instanct_cashout_tickets(game_id):
    """
    This method takes agme id and filter all instant cashout tickets with the game id,
    and generate 4 system pick number that will not match with the ticket number
    """

    instant_cashout_ticket = LottoTicket.objects.filter(game_play_id=game_id, lottery_type="INSTANT_CASHOUT")
    if instant_cashout_ticket.exists():
        user_picks = set([])
        system_pick = set([])
        for ticket in instant_cashout_ticket:
            ticket_number = ticket.ticket.split(",")
            user_picks.update(ticket_number)

        user_picks = list(user_picks)

        # generate system pick number that
        while len(system_pick) < 4:
            random_number = random.randint(1, 40)
            if str(random_number) not in user_picks:
                system_pick.add(str(random_number))

        system_pick = list(system_pick)

        instant_cashout_ticket.update(system_generated_num=",".join(system_pick))
        return user_picks


# def wyse_cash_draw():
#     active_lottery_batch = LotteryBatch.objects.filter(
#         lottery_type="WYSE_CASH", is_active=True
#     ).last()

#     active_lottery_batch_id = active_lottery_batch.id

#     batch_lottery_qs = LotteryModel.objects.filter(
#         batch__id=active_lottery_batch_id, lottery_type="WYSE_CASH", paid=True
#     )

#     aggregated_rtp = batch_lottery_qs.aggregate(Sum("rtp"))["rtp__sum"]

#     active_lottery_batch.is_active = False
#     active_lottery_batch.save()

#     print("batch_lottery_qs", batch_lottery_qs, "\n\n\n")
#     print("batch_lottery_qs.count()", batch_lottery_qs.count(), "\n\n\n")

#     wyse_cash_draw_response = WyseCashDraw(query_set=batch_lottery_qs).draw()

#     print("wyse_cash_draw_response", wyse_cash_draw_response, "\n\n")

#     total_amount_won = 0
#     for key in wyse_cash_draw_response:
#         if wyse_cash_draw_response[key].get("winners"):
#             for game_play_id in wyse_cash_draw_response[key].get("winners"):
#                 total_amount_won += wyse_cash_draw_response[key].get("reward")

#         elif wyse_cash_draw_response[key].get("jkpt_winners"):
#             for game_play_id in wyse_cash_draw_response[key].get("jkpt_winners"):
#                 total_amount_won += wyse_cash_draw_response[key].get("jackpot")

#     print("aggregated_rtp", aggregated_rtp, "\n\n")
#     print("total_amount_won", total_amount_won, "\n\n")
#     return wyse_cash_draw_response


def lotterywinners_dommy_data():
    lottery_model_qs = LotteryModel.objects.all()
    get_current_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH", is_active=True).last()

    win_type_choices = [
        "JACKPOT_WINNER",
        "ORDINARY_WINNER",
        "SUPER_WINNER",
        "CROWN_JUMBO_WINNER",
    ]

    for i in range(0, 30):
        lottery_model = random.choice(lottery_model_qs)

        win_type = random.choice(win_type_choices)

        try:
            LotteryWinnersTable.objects.create(
                batch=get_current_batch,
                phone_number=lottery_model.user_profile.phone_number,
                game_play_id=lottery_model.game_play_id,
                ticket=lottery_model.lucky_number,
                win_type=win_type,
                pool=lottery_model.pool,
                stake_amount=lottery_model.stake_amount,
                earning=lottery_model.stake_amount * 2,
                lottery_source_tag=lottery_model.channel,
            )
        except Exception as e:
            print(e)


def lotterywinners_jackpot_winners_reward():
    lottery_model_jackpot = LotteryWinnersTable.objects.filter(win_type="JACKPOT_WINNER")

    for i in lottery_model_jackpot:
        i.total_jackpot_amount = i.earning * 3
        i.save()


def dummy_jackpot_winners():
    crown_jumbo = Jackpot.objects.create(
        threshold=70000,
        contributed_amount=70000,
        is_active=False,
        jackpot_type="CROWN_JUMBO_JACKPOT",
    )

    super = Jackpot.objects.create(
        threshold=50000,
        contributed_amount=50000,
        is_active=False,
        jackpot_type="SUPER_JACKPOT",
    )

    mega_jackpot = Jackpot.objects.create(
        threshold=100000,
        contributed_amount=100000,
        is_active=False,
        jackpot_type="MEGA_JACKPOT",
    )

    users = [user for user in UserProfile.objects.all()]

    for i in range(0, 3):
        user = random.choice(users)

        JackpotWinner.objects.create(
            user=user,
            jackpot=crown_jumbo,
            amount=70000,
        )

        crown_jumbo.is_drawn = True
        crown_jumbo.save()

        break

    for i in range(0, 3):
        user = random.choice(users)

        JackpotWinner.objects.create(
            user=user,
            jackpot=super,
            amount=50000,
        )

        super.is_drawn = True
        super.save()

        break

    for i in range(0, 3):
        user = random.choice(users)

        JackpotWinner.objects.create(
            user=user,
            jackpot=mega_jackpot,
            amount=100000,
        )

        mega_jackpot.is_drawn = True
        mega_jackpot.save()

        break


# def instant_cashout_draw(re_run=False, new_rtp=0, batch_id=0):

#     instant_cashout_qs = LottoTicket.objects.filter(lottery_type="INSTANT_CASHOUT")

#     for i in range(0, 5):

#         instant_cashout_instance = random.choice(instant_cashout_qs)

#         LottoWinners.objects.create(
#             batch=instant_cashout_instance.batch,
#             phone_number=instant_cashout_instance.user_profile.phone_number,
#             ticket=instant_cashout_instance.ticket,
#             win_type="PERM_4",
#             lotto_type="INSTANT_CASHOUT",
#             game_play_id=instant_cashout_instance.game_play_id,
#             stake_amount=instant_cashout_instance.stake_amount,
#             earning=instant_cashout_instance.stake_amount * 5,
#             channel_played_from=instant_cashout_instance.channel,
#         )

#     if re_run is True:
#         # print("recursive function called")
#         batch = LotteryBatch.objects.filter(id=batch_id).last()
#         batch_db_id = batch.id
#     else:
#         batch = LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT").last()
#         batch_db_id = batch.id


def salaryForLifeJackpotWinners():
    salary_for_lfe_global_jackpot = LotteryGlobalJackPot.objects.filter(is_active=True).last()

    if salary_for_lfe_global_jackpot is not None:
        # select random ticket
        tickets = LottoTicket.objects.filter(lottery_type="SALARY_FOR_LIFE")
        random_ticket = random.choice(tickets)

        LottoWinners.objects.create(
            batch=random_ticket.batch,
            phone_number=random_ticket.user_profile.phone_number,
            game_play_id=random_ticket.game_play_id,
            ticket=random_ticket.ticket,
            win_type="JACKPOT_WINNER",
            lotto_type="SALARY_FOR_LIFE",
            stake_amount=random_ticket.stake_amount,
            earning=salary_for_lfe_global_jackpot.threshold,
            total_jackpot_amount=salary_for_lfe_global_jackpot.threshold,
            channel_played_from=random_ticket.channel,
            match_type="NAP_5",
        )

        salary_for_lfe_global_jackpot.is_active = False
        salary_for_lfe_global_jackpot.is_drawn = True
        salary_for_lfe_global_jackpot.save()

    for i in range(0, 5):
        # select random ticket
        tickets = LottoTicket.objects.filter(lottery_type="SALARY_FOR_LIFE")
        random_ticket = random.choice(tickets)

        try:
            salary_for_lfe_global_jackpot = LotteryGlobalJackPot.objects.create(
                threshold=100000,
                is_active=False,
                is_drawn=True,
            )

        except Exception:
            pass

        LottoWinners.objects.create(
            batch=random_ticket.batch,
            phone_number=random_ticket.user_profile.phone_number,
            game_play_id=random_ticket.game_play_id,
            ticket=random_ticket.ticket,
            win_type="JACKPOT_WINNER",
            lotto_type="SALARY_FOR_LIFE",
            stake_amount=random_ticket.stake_amount,
            earning=1000000.00,
            total_jackpot_amount=salary_for_lfe_global_jackpot.threshold,
            channel_played_from=random_ticket.channel,
            match_type="NAP_5",
        )

    salary_for_lfe_global_jackpot = LotteryGlobalJackPot.objects.create(
        threshold=100000,
        is_active=False,
        is_drawn=True,
    )


def lottery_model_update_pool():
    pools = [
        "TEN_THOUSAND",
        "FIFTY_THOUSAND",
        "ONE_HUNDRED_THOUSAND",
        "TWO_HUNDRED_THOUSAND",
    ]

    lottery_model_qs = LotteryModel.objects.all()

    for i in lottery_model_qs:
        pool_choice = random.choice(pools)
        i.pool = pool_choice
        i.save()


def create_coralpay_ussd_code():
    users = [user for user in UserProfile.objects.all()]

    for i in range(0, 20):
        code_instance = CoralpayUserCode.objects.create(
            user=random.choice(users),
        )

        # generate random amount
        amount = random.randint(100, 100000)

        CoralpayTransactions.objects.create(
            ussd_code_extension=code_instance,
            amount=amount,
            customer_name=code_instance.user.first_name,
            customer_phone=code_instance.user.phone_number,
        )

    return "Done generating coralpay ussd code"


def wyse_cash_draw_response_data():
    wyse_cash_draw_response = {
        "upper_tier": {
            "count": 2.59,
            "reward": 40000.0,
            "pool": "TWO_HUNDRED_THOUSAND",
            "jackpot": 34577.9,
            "jkpt_count": 0.17,
            "winners": ["400g3A9", "f100T19"],
            "jkpt_winners": [],
        },
        "middle_tier": {
            "count": 1.47,
            "reward": 80000.0,
            "pool": "ONE_HUNDRED_THOUSAND",
            "jackpot": 31397.24,
            "jkpt_count": 0.66,
            "winners": ["v8005W6"],
            "jkpt_winners": [],
        },
        "lower_tier": {
            "count": 4.55,
            "reward": 30000.0,
            "pool": "FIFTY_THOUSAND",
            "jackpot": 24649.61,
            "jkpt_count": 1.81,
            "winners": ["9H2n222", "439Wq69", "4L540P1", "V4622j8"],
            "jkpt_winners": ["5N01s79"],
        },
        "last_tier": {
            "count": 25.97,
            "reward": 5000.0,
            "pool": "TEN_THOUSAND",
            "jackpot": 20001.96,
            "jkpt_count": 6.06,
            "winners": [
                "r4L9699",
                "18J0b16",
                "J4K0057",
                "Z6116P5",
                "6E3g156",
                "56S29H7",
                "N534c72",
                "618J6G4",
                "996O6E8",
                "6He5201",
                "92NS392",
                "49I3963",
                "o672H63",
                "67C662u",
                "585gL58",
                "2069bC3",
                "634X13n",
                "F16u926",
                "H8926t7",
                "H61U771",
                "A68Y505",
                "2V78n63",
                "78409Ld",
                "7x45U96",
                "0467bD1",
            ],
            "jkpt_winners": [
                "0347E7w",
                "991G4N0",
                "2Ag9035",
                "196Yj85",
                "4949J8e",
                "S8695s0",
            ],
        },
    }

    for i, v in wyse_cash_draw_response:
        print(i)

        if wyse_cash_draw_response[i].get("winners"):
            for j in wyse_cash_draw_response[i].get("winners"):
                print(j)

        if wyse_cash_draw_response[i].get("jkpt_winners"):
            for j in wyse_cash_draw_response[i].get("jkpt_winners"):
                print(j)


def lottery_winner_sms():
    send_congrats_sms_to_winners_without_account_num("*************", 5000)


def redbiller_ussd_code():
    [user for user in UserProfile.objects.all()]
    # user = random.choice(users)
    user = UserProfile.objects.get(id=3018)
    ussd_payment = UssdPayment.initiate_transaction(
        user=user,
        amount=100,
        payment_for="LOTTERY_PAYMENT",
        source="REDBILLER",
    )

    return ussd_payment


def redbiller_verify_payment():
    # verify payment
    redbiller_url = f"{settings.LIBERTY_VAS_BASE_URL}/redbiller/merchant/verify/"

    payload = {
        "payment_reference": "wyse4112b8c6e09d188310f99f549a0308393018",
        "service": "USSD_PAYMENT",
    }
    username = settings.LIBERTY_VAS_AUTH_USERNAME
    password = settings.LIBERTY_VAS_AUTH_PASSWORD

    STRING_VALUE = f"{username}:{password}"
    AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

    headers = {
        "Authorization": f"Basic {AUTH_TOKEN}",
        "Content-Type": "application/json",
    }

    # redbiller_url = f"{redbiller_url}?payment_reference=wyse4112b8c6e09d188310f99f549a0308393018&service=USSD_PAYMENT"

    verify_response = requests.request("POST", redbiller_url, headers=headers, json=payload)
    verify_response = verify_response.text

    return verify_response


def update_lottery_to_unique():
    from main.models import LotteryBatch

    lottery_batch = LotteryBatch.objects.all()
    import uuid

    for batch in lottery_batch:
        if LotteryBatch.objects.filter(batch_uuid=batch.batch_uuid).exists():
            formatted_month = datetime.strftime(datetime.now(), "%b")
            batch.batch_uuid = f"{formatted_month}-{str(uuid.uuid4())}"
            batch.save()

    print("Done")


def redbiller_find_bank():
    url = "https://services.libertypayng.com/redbiller/find_bank/"

    username = settings.LIBERTY_VAS_AUTH_USERNAME
    password = settings.LIBERTY_VAS_AUTH_PASSWORD

    STRING_VALUE = f"{username}:{password}"
    AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Basic {AUTH_TOKEN}",
    }

    payload = {"account_no": "**********", "reference": "kajQWZXc"}

    response = requests.request("POST", url, headers=headers, json=payload)
    print(response.text)


def LottoWinnersSMSToWithdrawWinnings():
    winners = [
        {
            "phone_number": "*************",
            "game_play_id": "3m376T4",
            "amount": 3000,
        },
        {
            "phone_number": "*************",
            "game_play_id": "200Ib40",
            "amount": 4500,
        },
        {
            "phone_number": "*************",
            "game_play_id": "A461o94",
            "amount": 4500,
        },
        {
            "phone_number": "*************",
            "game_play_id": "KT06145",
            "amount": 10000,
        },
        {
            "phone_number": "*************",
            "game_play_id": "51Ro622",
            "amount": 22500,
        },
        {
            "phone_number": "*************",
            "game_play_id": "K1I4488",
            "amount": 3000,
        },
        {
            "phone_number": "*************",
            "game_play_id": "K1I4488",
            "amount": 3000,
        },
    ]

    for player in winners:
        whisper_url = "https://whispersms.xyz/transactional/send"
        whisper_payload = {
            "receiver": f"{player.get('phone_number')}",
            "template": "31e22cb9-31b0-46db-a9f6-5de61e6cc5b4",
            "place_holders": {
                "amount": f"{Utility.currency_formatter(player.get('amount'))}",
                "game_play_id": f"{player.get('game_play_id')}",
            },
        }

        whisper_headers = {
            "Authorization": f"Api_key {settings.WHISPER_KEY}",
            "Content-Type": "application/json",
        }

        whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

        try:
            response = whisper_resp.json()
        except Exception:
            response = whisper_resp.text
        print(response)


def wyse_cash_game_draw():
    from decisioning_engine.draw import WyseCashDraw
    from wyse_ussd.tasks import ussd_lottery_winner_reward

    # get wyse cash active game batch
    wyse_cash_batch = LotteryBatch.objects.filter(batch_uuid="Oct-3c8d5c25-950d-458e-8b1d-dabb32ed2505").last()

    batch_database_id = wyse_cash_batch.id

    active_lottery_batch_id = wyse_cash_batch.id

    batch_lottery_qs = LotteryModel.objects.filter(batch__id=active_lottery_batch_id, lottery_type="WYSE_CASH", paid=True)

    wyse_cash_batch.is_active = False
    wyse_cash_batch.save()

    # new_batch = LotteryBatch.objects.create(lottery_type="WYSE_CASH")

    wyse_cash_draw_response = WyseCashDraw(query_set=batch_lottery_qs).draw()

    print("wyse_cash_draw_response", wyse_cash_draw_response, "\n\n\n\n")

    """
    SAMPLE RESPONSE DATA
    {"upper_tier":{"count":2.59,"reward":40000.0,"pool":"TWO_HUNDRED_THOUSAND","jackpot":34577.9,"jkpt_count":0.17,"winners":["400g3A9","f100T19"],"jkpt_winners":[]},"middle_tier":{"count":1.47,"reward":80000.0,"pool":"ONE_HUNDRED_THOUSAND","jackpot":31397.24,"jkpt_count":0.66,"winners":["v8005W6"],"jkpt_winners":[]},"lower_tier":{"count":4.55,"reward":30000.0,"pool":"FIFTY_THOUSAND","jackpot":24649.61,"jkpt_count":1.81,"winners":["9H2n222","439Wq69","4L540P1","V4622j8"],"jkpt_winners":["5N01s79"]},"last_tier":{"count":25.97,"reward":5000.0,"pool":"TEN_THOUSAND","jackpot":20001.96,"jkpt_count":6.06,"winners":["r4L9699","18J0b16","J4K0057","Z6116P5","6E3g156","56S29H7","N534c72","618J6G4","996O6E8","6He5201","92NS392","49I3963","o672H63","67C662u","585gL58","2069bC3","634X13n","F16u926","H8926t7","H61U771","A68Y505","2V78n63","78409Ld","7x45U96","0467bD1"],"jkpt_winners":["0347E7w","991G4N0","2Ag9035","196Yj85","4949J8e","S8695s0"]}}
    """

    for key in wyse_cash_draw_response:
        if wyse_cash_draw_response[key].get("winners"):
            for game_play_id in wyse_cash_draw_response[key].get("winners"):
                lottery_model = LotteryModel.objects.filter(game_play_id=game_play_id).last()

                get_lottery_batch = LotteryBatch.objects.filter(id=lottery_model.batch.id).last()
                run_batch_id = generate_game_play_id()

                LotteryWinnersTable.objects.create(
                    batch=get_lottery_batch,
                    run_batch_id=run_batch_id,
                    phone_number=lottery_model.user_profile.phone_number,
                    playyer_id=lottery_model.user_profile.id,
                    unnique_id=lottery_model.unique_id,
                    game_play_id=lottery_model.game_play_id,
                    ticket=lottery_model.lucky_number,
                    win_type="ORDINARY_WINNER",
                    pool=lottery_model.pool,
                    stake_amount=lottery_model.stake_amount,
                    earning=wyse_cash_draw_response[key].get("reward"),
                    lottery_source_tag=lottery_model.channel,
                )

                # send sms to winner
                celery_sms_for_wyse_winners.delay(
                    lottery_model.batch.id,
                )

                # check if the play is ussd. if yes, # save amount to wallet
                if lottery_model.channel == "USSD":
                    ussd_lottery_winner_reward.delay(
                        user_id=lottery_model.user_profile.id,
                        amount=wyse_cash_draw_response[key].get("reward"),
                    )

        elif wyse_cash_draw_response[key].get("jkpt_winners"):
            for game_play_id in wyse_cash_draw_response[key].get("jkpt_winners"):
                lottery_model = LotteryModel.objects.filter(game_play_id=game_play_id).last()

                get_lottery_batch = LotteryBatch.objects.filter(id=lottery_model.batch.id).last()
                run_batch_id = generate_game_play_id()

                LotteryWinnersTable.objects.create(
                    batch=get_lottery_batch,
                    run_batch_id=run_batch_id,
                    phone_number=lottery_model.user_profile.phone_number,
                    playyer_id=lottery_model.user_profile.id,
                    unnique_id=lottery_model.unique_id,
                    game_play_id=lottery_model.game_play_id,
                    ticket=lottery_model.lucky_number,
                    win_type="JACKPOT_WINNER",
                    pool=lottery_model.pool,
                    stake_amount=lottery_model.stake_amount,
                    earning=wyse_cash_draw_response[key].get("reward"),
                    lottery_source_tag=lottery_model.channel,
                    total_jackpot_amount=wyse_cash_draw_response[key].get("jackpot"),
                )

                # send sms to winner
                celery_sms_for_wyse_winners.delay(
                    lottery_model.batch.id,
                )

                # check if the play is ussd. if yes, # save amount to wallet
                if lottery_model.channel == "USSD":
                    ussd_lottery_winner_reward.delay(
                        user_id=lottery_model.user_profile.id,
                        amount=wyse_cash_draw_response[key].get("jackpot"),
                    )

    # lost sms to wyse cash non winners
    celery_sms_for_wyse_lost.delay(batch_database_id)


def move_users_ussd_wallet_to_web_wallet():
    user_wallet = UserWallet.objects.filter(
        wallet_tag="USSD",
    )

    print(user_wallet)

    if user_wallet:
        for wallet in user_wallet:
            user_profile = UserProfile.objects.filter(phone_number=wallet.user.phone_number).last()
            web_wallet = UserWallet.objects.filter(wallet_tag="WEB", user=user_profile).last()

            if web_wallet:
                web_wallet.game_available_balance += wallet.game_available_balance
                web_wallet.save()

                wallet.game_available_balance = 0
                wallet.save()

            else:
                UserWallet.objects.create(
                    user=user_profile,
                    wallet_tag="WEB",
                    game_available_balance=wallet.game_available_balance,
                )

                wallet.game_available_balance = 0
                wallet.save()


def random_date():
    start = datetime.now() - timedelta(days=30)
    end = datetime.now()
    return start + (end - start) * random.random()


def update_lottery_batch_randaom_date():
    lottery_batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE")[::10]

    for batch in lottery_batch:
        batch.created_date = random_date()
        batch.save()


def update_lotto_winners_with_random_date():
    # get all lotto winners
    lotto_winners = LottoWinners.objects.filter(lotto_type="SALARY_FOR_LIFE").distinct("batch")

    if lotto_winners:
        for winner in lotto_winners:
            LottoWinners.objects.filter(batch=winner.batch).update(date_won=random_date())
            lottery_batch = LotteryBatch.objects.filter(id=winner.batch.id).last()
            if lottery_batch:
                lottery_batch.lottery_winner_ticket_number = ", (1, 5, 7, 8, 9), (4, 6, 7, 10, 47)"
                lottery_batch.save()


def jackpot_winner_for_wyse_games():
    # for salaray for life
    lottery_batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active=True).last()

    if lottery_batch:
        for i in range(0, 50):
            ticket = []
            for i in range(0, 5):
                ticket.append(random.randint(1, 10))

            stake_amount = random.randint(100, 1000)

            earning = random.randint(5000, 50000)

            channel = random.choice(["WEB", "USSD", "MOBILE", "POS_AGENT"])

            run_batch_id = random.randint(0, 100000000000000000000000000000000000000000000)

            user = random.choice([user for user in UserProfile.objects.all()])
            LottoWinners.objects.create(
                batch=lottery_batch,
                phone_number=user.phone_number,
                ticket=serialize_ticket(ticket),
                win_type="JACKPOT_WINNER",
                lotto_type="SALARY_FOR_LIFE",
                game_play_id=generate_game_play_id(),
                stake_amount=stake_amount,
                earning=earning,
                channel_played_from=channel,
                run_batch_id=run_batch_id,
            )

    # for instant cashout
    lottery_batch = LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT", is_active=True).last()

    if lottery_batch:
        for i in range(0, 50):
            ticket = []
            for i in range(0, 5):
                ticket.append(random.randint(1, 10))

            stake_amount = random.randint(100, 1000)

            earning = random.randint(5000, 50000)

            channel = random.choice(["WEB", "USSD", "MOBILE", "POS_AGENT"])

            run_batch_id = random.randint(0, 100000000000000000000000000000000000000000000)

            user = random.choice([user for user in UserProfile.objects.all()])
            LottoWinners.objects.create(
                batch=lottery_batch,
                phone_number=user.phone_number,
                ticket=serialize_ticket(ticket),
                win_type="JACKPOT_WINNER",
                lotto_type="INSTANT_CASHOUT",
                game_play_id=generate_game_play_id(),
                stake_amount=stake_amount,
                earning=earning,
                channel_played_from=channel,
                run_batch_id=run_batch_id,
            )

    # for wyse cash
    lottery_batch = LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT", is_active=True).last()

    if lottery_batch:
        for i in range(0, 50):
            ticket = []
            for i in range(0, 5):
                ticket.append(random.randint(1, 10))

            stake_amount = random.randint(100, 1000)

            earning = random.randint(5000, 50000)

            channel = random.choice(["WEB", "USSD", "MOBILE", "POS_AGENT"])

            run_batch_id = random.randint(0, 100000000000000000000000000000000000000000000)

            user = random.choice([user for user in UserProfile.objects.all()])

            pool = random.choice(
                [
                    "TEN_THOUSAND",
                    "FIFTY_THOUSAND",
                    "ONE_HUNDRED_THOUSAND",
                    "TWO_HUNDRED_THOUSAND",
                ]
            )

            LotteryWinnersTable.objects.create(
                pool=pool,
                earning=earning,
                phone_number=user.phone_number,
                batch=lottery_batch,
                stake_amount=stake_amount,
                win_type="JACKPOT_WINNER",
                lottery_source_tag=channel,
            )


def wyse_cash_game_draw_test(batch_id):
    from decisioning_engine.draw import WyseCashDraw
    from main.models import LotteryModel, LotteryWinnersTable
    from wyse_ussd.tasks import ussd_lottery_winner_reward

    # get wyse cash active game batch
    wyse_cash_batch = LotteryBatch.objects.filter(id=batch_id).last()

    batch_database_id = wyse_cash_batch.id

    active_lottery_batch_id = wyse_cash_batch.id

    batch_lottery_qs = LotteryModel.objects.filter(batch__id=active_lottery_batch_id, lottery_type="WYSE_CASH", paid=True)

    wyse_cash_batch.is_active = False
    wyse_cash_batch.save()

    if not batch_lottery_qs.exists():
        pass
    else:
        for band in batch_lottery_qs:
            print("band", band.band)

        print("end of band loop", "\n\n\n\n")
        wyse_cash_draw_response = WyseCashDraw(query_set=batch_lottery_qs).draw()

        """

        SAMPLE RESPONSE DATA
        {"upper_tier":{"count":2.59,"reward":40000.0,"pool":"TWO_HUNDRED_THOUSAND","jackpot":34577.9,"jkpt_count":0.17,"winners":["400g3A9","f100T19"],"jkpt_winners":[]},"middle_tier":{"count":1.47,"reward":80000.0,"pool":"ONE_HUNDRED_THOUSAND","jackpot":31397.24,"jkpt_count":0.66,"winners":["v8005W6"],"jkpt_winners":[]},"lower_tier":{"count":4.55,"reward":30000.0,"pool":"FIFTY_THOUSAND","jackpot":24649.61,"jkpt_count":1.81,"winners":["9H2n222","439Wq69","4L540P1","V4622j8"],"jkpt_winners":["5N01s79"]},"last_tier":{"count":25.97,"reward":5000.0,"pool":"TEN_THOUSAND","jackpot":20001.96,"jkpt_count":6.06,"winners":["r4L9699","18J0b16","J4K0057","Z6116P5","6E3g156","56S29H7","N534c72","618J6G4","996O6E8","6He5201","92NS392","49I3963","o672H63","67C662u","585gL58","2069bC3","634X13n","F16u926","H8926t7","H61U771","A68Y505","2V78n63","78409Ld","7x45U96","0467bD1"],"jkpt_winners":["0347E7w","991G4N0","2Ag9035","196Yj85","4949J8e","S8695s0"]}}

        """

        for key in wyse_cash_draw_response:
            if wyse_cash_draw_response[key].get("winners"):
                for game_play_id in wyse_cash_draw_response[key].get("winners"):
                    lottery_model = LotteryModel.objects.filter(game_play_id=game_play_id).last()

                    get_lottery_batch = LotteryBatch.objects.filter(id=lottery_model.batch.id).last()
                    run_batch_id = generate_game_play_id()

                    LotteryWinnersTable.objects.create(
                        batch=get_lottery_batch,
                        run_batch_id=run_batch_id,
                        phone_number=lottery_model.user_profile.phone_number,
                        playyer_id=lottery_model.user_profile.id,
                        unnique_id=lottery_model.unique_id,
                        game_play_id=lottery_model.game_play_id,
                        ticket=lottery_model.lucky_number,
                        win_type="ORDINARY_WINNER",
                        pool=lottery_model.pool,
                        stake_amount=lottery_model.stake_amount,
                        earning=wyse_cash_draw_response[key].get("reward"),
                        lottery_source_tag=lottery_model.channel,
                    )

                    # send sms to winner
                    celery_sms_for_wyse_winners.delay(
                        lottery_model.batch.id,
                    )

                    ussd_lottery_winner_reward.delay(
                        user_id=lottery_model.user_profile.id,
                        amount=wyse_cash_draw_response[key].get("reward"),
                    )

            elif wyse_cash_draw_response[key].get("jkpt_winners"):
                for game_play_id in wyse_cash_draw_response[key].get("jkpt_winners"):
                    lottery_model = LotteryModel.objects.filter(game_play_id=game_play_id).last()

                    get_lottery_batch = LotteryBatch.objects.filter(id=lottery_model.batch.id).last()
                    run_batch_id = generate_game_play_id()

                    LotteryWinnersTable.objects.create(
                        batch=get_lottery_batch,
                        run_batch_id=run_batch_id,
                        phone_number=lottery_model.user_profile.phone_number,
                        playyer_id=lottery_model.user_profile.id,
                        unnique_id=lottery_model.unique_id,
                        game_play_id=lottery_model.game_play_id,
                        ticket=lottery_model.lucky_number,
                        win_type="JACKPOT_WINNER",
                        pool=lottery_model.pool,
                        stake_amount=lottery_model.stake_amount,
                        earning=wyse_cash_draw_response[key].get("reward"),
                        lottery_source_tag=lottery_model.channel,
                        total_jackpot_amount=wyse_cash_draw_response[key].get("jackpot"),
                    )

                    # send sms to winner
                    celery_sms_for_wyse_winners.delay(
                        lottery_model.batch.id,
                    )

                    # check if the play is ussd. if yes, # save amount to wallet
                    ussd_lottery_winner_reward.delay(
                        user_id=lottery_model.user_profile.id,
                        amount=wyse_cash_draw_response[key].get("jackpot"),
                    )

        # lost sms to wyse cash non winners
        celery_sms_for_wyse_lost.delay(batch_database_id)


def re_run_wyse_cash_draw():
    lottery_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH", batch_uuid="Nov-7a2354a7-75d5-4de0-9328-a60f21b10e4b").last()

    if lottery_batch:
        wyse_cash_game_draw_test(lottery_batch.id)

        # for batch in lottery_batch:
        #     print("found", batch.id)
        #     wyse_cash_game_draw_test(batch.id)

    print("not found")
    return "done"


def game_play_base_on_batch_id(batch_uuid):
    lottery_batch = LotteryBatch.objects.filter(batch_uuid=batch_uuid, lottery_type="WYSE_CASH").last()

    if lottery_batch:
        lottery_qs = LotteryModel.objects.filter(batch=lottery_batch, paid=True)
        print("Count of lottery_qs", lottery_qs.count())
        if lottery_qs:
            for lottery in lottery_qs:
                print(
                    "Ticket",
                    lottery.lucky_number,
                    "Game play id",
                    lottery.game_play_id,
                    "stake_amount",
                    lottery.stake_amount,
                )

        print("Batch Found")

    else:
        print("Batch not found")


def resend_lost_and_win_sms_for_wyse_cash(batch_uuid):
    # send sms to winner

    lottery_batch = LotteryBatch.objects.filter(batch_uuid=batch_uuid, lottery_type="WYSE_CASH").last()

    lottery_qs = LotteryModel.objects.filter(batch=lottery_batch, paid=True).distinct("game_play_id")

    if lottery_qs:
        for ticket in lottery_qs:
            winner_instance = LotteryWinnersTable.objects.filter(game_play_id=ticket.game_play_id).last()
            if winner_instance:
                celery_sms_for_wyse_winners(
                    winner_instance.batch.id,
                )
                print("sms sent to winner")
            else:
                celery_sms_for_wyse_lost(ticket.batch.id)
                print("sms sent to lost")

    else:
        print("Batch not found")


def move_lottery_batch_rtp_to_general(wyse_cash_batch_id=0, salary_batch_id=0, instant_cash_batch_id=0):
    wyse_cash_query_set = LotteryModel.objects.filter(batch__id=wyse_cash_batch_id, paid=True)
    if wyse_cash_query_set:
        for lottery in wyse_cash_query_set:
            GeneralWithdrawableWallet.add_fund(lottery.rtp, lottery.user_profile.phone_number, "WYSE_CASH")

    salary_query_set = LottoTicket.objects.filter(batch__id=salary_batch_id, paid=True)
    if salary_query_set:
        for lottery in salary_query_set:
            GeneralWithdrawableWallet.add_fund(lottery.rtp, lottery.user_profile.phone_number, "SALARY_FOR_LIFE")

    instant_cash_query_set = LottoTicket.objects.filter(batch__id=instant_cash_batch_id, paid=True)
    if instant_cash_query_set:
        for lottery in instant_cash_query_set:
            GeneralWithdrawableWallet.add_fund(lottery.rtp, lottery.user_profile.phone_number, "INSTANT_CASHOUT")


def generate_system_pick(user_picks, winning_ticket):
    pass


def dummy_salary_for_life_winnings():
    # salary_for_life_data = LottoTicket.objects.filter(lottery_type="SALARY_FOR_LIFE")
    salary_for_life_data = LottoTicket.objects.filter(lottery_type="SALARY_FOR_LIFE", game_play_id="X56v289")

    for i in range(0, 50):
        sal_for_data = random.choice(salary_for_life_data)

        ticket = sal_for_data.ticket.split(",")

        if LottoWinners.objects.filter(game_play_id__iexact=sal_for_data.game_play_id).exists():
            pass
        else:
            LottoWinners.create_lotto_winner_obj(
                lottery=sal_for_data,
                batch=sal_for_data.batch,
                phone_number=sal_for_data.user_profile.phone_number,
                ticket=ticket,
                win_type="ORDINARY_WINNER",
                match_type="PERM_4",
                lotto_type="SALARY_FOR_LIFE",
                game_play_id=sal_for_data.game_play_id,
                stake_amount=sal_for_data.stake_amount,
                earning=random.randint(1000, 10000),
                channel_played_from=sal_for_data.channel,
                run_batch_id=random.randint(1, 100000),
            )

            if i == len(salary_for_life_data):
                break


def close_old_batch_and_create_new_batch_for_salary_for_life():
    old_batch = LotteryBatch.objects.get(id=2203)

    salary_for_life_jackpot = LotteryGlobalJackPot.get_jackpot_instance()

    old_batch.is_active = False
    old_batch.save()

    eight_pm = time(12, 1)

    lotto_ticket = LottoTicket.objects.filter(batch__id=old_batch.id, date__time__gt=eight_pm)

    sal_4_lif_batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE").last()
    if sal_4_lif_batch.is_active is True:
        new_batch = sal_4_lif_batch

    else:
        try:
            new_batch = LotteryBatch.objects.create(
                lottery_type="SALARY_FOR_LIFE",
                global_jackpot=salary_for_life_jackpot,
            )
        except Exception:
            new_batch = None

    if new_batch is not None:
        amount_paid = lotto_ticket.filter(paid=True).aggregate(Sum("amount_paid"))["amount_paid__sum"]
        total_unpaid = lotto_ticket.filter(paid=False).aggregate(Sum("stake_amount"))["stake_amount__sum"]
        lotto_ticket.update(batch=new_batch)

        new_batch.total_accumulated_paid = amount_paid
        new_batch.total_accumulated_unpaid = total_unpaid

        new_batch.save()

        print("New batch created")

    else:
        print("New batch not created")


def save_lottery_price_to_db():
    # salary for life price
    for i in range(1, 11):
        for j in range(1, 5):
            print("i", i, "j", j)
            if j == 1:
                continue

            ticket_price = LottoTicket.salary_for_life_stake_amount_pontential_winning_for_lotto_agents(ticket_count=i).get("stake_amount")
            print("ticket_price", ticket_price, "\n\n\n")
            pontential_winning = LottoTicket.salary_for_life_win_per_line(match=j, number_of_line=i)
            LotteryTicketPrice.objects.create(
                lottery_type="SALARY_FOR_LIFE",
                winning_type=f"PERM_{j}",
                number_of_tickets=i,
                ticket_price=ticket_price,
                band=ticket_price,
                pontential_winning=pontential_winning,
            )

    # instant cashout price
    for i in range(1, 8):
        for j in range(1, 5):
            if j == 1:
                continue

            ticket_price = LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(ticket_count=i).get("stake_amount")
            print("ticket_price", ticket_price, "\n\n\n")
            pontential_winning = LottoTicket.salary_for_life_win_per_line(match=j, number_of_line=i)


def adding_to_user_debt():
    sample_data = [
        [545, 6750.0],
        [1141, 700.0],
        [1596, 91100.0],
        [1651, 450.0],
        [1875, 700.0],
        [2376, 7270.0],
        [2509, 22750.0],
        [2619, 4500.0],
        [2773, 700.0],
        [2806, 500.0],
        [3051, 13650.0],
        [3225, 350.0],
        [3253, 150.0],
        [3576, 100.0],
        [6166, 150.0],
        [9403, 1500.0],
        [9678, 150.0],
        [10467, 1800.0],
        [11378, 23600.0],
        [11758, 100.0],
        [12143, 2450.0],
        [13260, 1500.0],
        [13725, 150.0],
        [15503, 1650.0],
        [16285, 52200.0],
        [16744, 1050.0],
        [16847, 1517.33],
        [17282, 3800.0],
        [17492, 100.0],
        [18674, 100.0],
        [18757, 2800.0],
        [20013, 900.0],
        [20652, 2950.0],
        [21850, 750.0],
        [21921, 600.0],
        [22083, 300.0],
        [22100, 200.0],
        [22270, 2000.0],
        [24584, 400.0],
        [24916, 3200.0],
        [31585, 3800.0],
        [32437, 1350.0],
        [33659, 200.0],
        [34777, 2100.0],
        [35316, 100800.0],
        [35439, 400.0],
        [35725, 107500.0],
        [35948, 2300.0],
        [36442, 100.0],
        [37097, 85050.0],
        [40180, 10600.0],
        [41289, 150.0],
        [41590, 400.0],
        [41678, 25000.0],
        [41860, 150.0],
        [42623, 300.0],
        [42833, 7350.0],
        [43210, 100.0],
        [43362, 9700.0],
        [44796, 300.0],
        [44859, 150.0],
        [44897, 600.0],
        [45140, 2100.0],
        [47844, 500.0],
        [48950, 100.0],
        [49033, 200.0],
        [49233, 1900.0],
        [50134, 1800.0],
        [50574, 400.0],
        [51086, 1000.0],
        [51889, 4994.0],
        [51893, 8170.0],
        [52036, 23250.0],
        [52173, 15500.0],
        [52281, 24900.0],
        [52930, 14250.0],
        [52934, 100.0],
        [52935, 6000.0],
        [52967, 2800.0],
        [53084, 6600.0],
        [53113, 7000.0],
        [53125, 70100.0],
        [53128, 41600.0],
        [53141, 1000.0],
        [53156, 550.0],
        [53188, 36050.0],
        [53252, 6600.0],
        [53265, 4150.0],
        [53333, 700.0],
        [53342, 2500.0],
        [53349, 14200.0],
        [53351, 1200.0],
        [53353, 55650.0],
        [53361, 5000.0],
        [53370, 150.0],
        [53420, 4750.0],
        [53556, 19300.0],
        [53984, 100.0],
        [54122, 150.0],
        [54338, 300.0],
        [54409, 150.0],
        [54637, 100.0],
        [55072, 200.0],
        [55201, 100.0],
        [55240, 3000.0],
        [55311, 100.0],
        [55461, 1000.0],
        [55521, 100.0],
        [55529, 2000.0],
        [55967, 1000.0],
        [56290, 400.0],
        [56379, 250.0],
        [56407, 1250.0],
        [56441, 800.0],
        [56515, 6800.0],
        [56521, 400.0],
        [56598, 1500.0],
        [56612, 400.0],
        [56636, 350.0],
        [56637, 300.0],
        [56759, 100.0],
        [56772, 300.0],
        [56789, 200.0],
        [56796, 3050.0],
        [56817, 12350.0],
        [56825, 2050.0],
        [56859, 200.0],
        [56866, 1050.0],
        [57024, 150.0],
        [57107, 200.0],
        [57141, 200.0],
        [57179, 1200.0],
        [57213, 100.0],
        [57249, 600.0],
        [57269, 600.0],
        [57280, 150.0],
        [57540, 1000.0],
        [58213, 100.0],
        [58328, 4500.0],
        [58503, 100.0],
        [58608, 350.0],
        [58953, 200.0],
        [59450, 150.0],
        [59537, 100.0],
        [59691, 600.0],
        [60428, 300.0],
        [60445, 100.0],
        [60548, 9960.0],
        [60944, 300.0],
        [61016, 300.0],
        [61083, 750.0],
        [61320, 300.0],
        [61367, 132900.0],
        [61742, 2000.0],
        [61970, 900.0],
        [62001, 200.0],
        [62166, 3500.0],
        [62314, 100.0],
        [62531, 100.0],
        [62704, 150.0],
        [62726, 1550.0],
        [62949, 300.0],
        [63451, 100.0],
        [63470, 3000.0],
        [63528, 8000.0],
        [63583, 29100.0],
        [63625, 5400.0],
        [63626, 1000.0],
        [63631, 3650.0],
        [63632, 6100.0],
        [63661, 59200.0],
        [63725, 2000.0],
        [63728, 2550.0],
        [63730, 59150.0],
        [63738, 150.0],
        [64018, 27100.0],
        [64019, 30450.0],
        [64021, 100.0],
        [64258, 200.0],
        [64263, 1300.0],
        [64276, 70550.0],
        [64453, 10150.0],
        [64503, 24300.0],
        [64504, 300.0],
        [64505, 18000.0],
        [64508, 14400.0],
        [64521, 300.0],
        [64524, 200.0],
        [64528, 39150.0],
        [64539, 32200.0],
        [64577, 24350.0],
        [64615, 3000.0],
        [64621, 500.0],
        [64625, 2000.0],
        [64711, 8000.0],
        [64844, 27400.0],
        [64948, 150.0],
        [64953, 32700.0],
        [64999, 1050.0],
        [65014, 450.0],
        [65037, 2000.0],
        [65122, 500.0],
        [65123, 13000.0],
        [65202, 400.0],
        [65341, 20200.0],
        [65539, 19400.0],
        [65566, 1200.0],
        [65595, 300.0],
        [65783, 1550.0],
        [65790, 150.0],
        [65815, 1700.0],
        [65824, 600.0],
        [65827, 200.0],
        [65829, 5650.0],
        [65833, 1700.0],
        [65864, 800.0],
        [65865, 800.0],
        [65866, 1100.0],
        [65871, 4100.0],
        [65883, 24150.0],
        [65890, 1350.0],
        [65917, 1000.0],
        [65919, 14500.0],
        [65926, 200.0],
        [65933, 4600.0],
        [65934, 1000.0],
        [65935, 5800.0],
        [65940, 600.0],
        [65945, 600.0],
        [65946, 350.0],
        [65947, 4200.0],
        [65953, 2550.0],
        [65972, 200.0],
        [65983, 750.0],
        [65998, 400.0],
        [66028, 1200.0],
        [66095, 3200.0],
        [66100, 1050.0],
        [66140, 4450.0],
        [66142, 200.0],
        [66173, 400.0],
        [66192, 600.0],
        [66206, 400.0],
        [66278, 200.0],
        [66290, 1200.0],
        [66328, 200.0],
        [66386, 5650.0],
        [66394, 200.0],
        [66406, 200.0],
        [66430, 1000.0],
        [66434, 200.0],
        [66449, 950.0],
        [66452, 950.0],
        [66476, 29500.0],
        [66494, 400.0],
        [66497, 550.0],
        [66531, 27500.0],
        [66536, 150.0],
        [66621, 6000.0],
        [66640, 200.0],
        [66662, 5800.0],
        [66725, 3800.0],
        [66728, 1000.0],
        [66734, 2000.0],
        [66735, 1000.0],
        [66736, 7000.0],
        [66755, 200.0],
        [66771, 4000.0],
        [66777, 6000.0],
    ]
    user_ids = []

    for data in sample_data:
        user_id = data[0]
        amount = data[1]

        user_profile = UserProfile.objects.filter(id=user_id).last()
        if user_profile:
            user_profile.debt_amount += amount
            user_profile.save()

            user_ids.append(user_profile.id)

        else:
            print("NO USER FOUND WITH THIS ID", user_id)


def bold_text(text):
    bold_start = "\033[1m"
    bold_end = "\033[0m"
    return bold_start + text + bold_end


def celery_sms_for_wyse_lost_fakes(batch_id):
    from main.models import LotteryBatch, LotteryModel, LotteryWinnersTable
    from wyse_ussd.tasks import salary_for_life_lost_sms_on_telco

    lottery_batch = LotteryBatch.objects.filter(id=batch_id).last()

    lottery_winners_qs = LotteryWinnersTable.objects.filter(
        batch=lottery_batch,
    )

    if lottery_winners_qs.exists():
        print("lottery_winners_qs.exists")
        # total amount
        total_amount = lottery_winners_qs.aggregate(Sum("earning"))["earning__sum"]

        # none winners
        non_winners = LotteryModel.objects.filter(
            paid=True,
            batch__is_active=False,
            batch__id=batch_id,
        ).distinct("game_play_id")
        print(lottery_winners_qs.values_list("phone_number", flat=True))

        print("non_winners", non_winners)

        for player in non_winners:
            if LotteryWinnersTable.objects.filter(game_play_id=player.game_play_id).exists():
                continue
            else:
                if player.played_via_telco_channel is False:
                    # send_sms_for_wyse_lost(
                    #     phone_number=player.user_profile.phone_number,
                    #     # phone_number="*************",
                    #     total_amount_won=total_amount,
                    #     num_of_winners=lottery_winners_qs.count(),
                    #     lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                    #     draw_date=f"{player.batch.last_updated.date()}",
                    # )
                    pass
                else:
                    print("played via telco channel")
                    for num in ["*************", "*************"]:
                        salary_for_life_lost_sms_on_telco(
                            phone_number=num,
                            total_amount_won=total_amount,
                            num_of_winners=lottery_winners_qs.count(),
                            lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                            draw_date=f"{player.batch.last_updated.date()}",
                        )
                    break

    else:
        pontentail_wiinings = [10000, 20000, 30000, 40000, 50000, 50000, 100000, 200000]
        winners_count = random.randint(1, 20)

        total_amount = random.choice(pontentail_wiinings)

        total_amount = random.randint(1000, 10000)
        non_winners = LotteryModel.objects.filter(paid=True, batch__is_active=False, batch__id=batch_id, channel="USSD").distinct("game_play_id")
        print(lottery_winners_qs.values_list("phone_number", flat=True))

        print("non_winners", non_winners)

        for player in non_winners:
            if LotteryWinnersTable.objects.filter(game_play_id=player.game_play_id).exists():
                continue
            else:
                if player.played_via_telco_channel is False:
                    # send_sms_for_wyse_lost(
                    #     phone_number=player.user_profile.phone_number,
                    #     # phone_number="*************",
                    #     total_amount_won=total_amount,
                    #     num_of_winners=lottery_winners_qs.count(),
                    #     lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                    #     draw_date=f"{player.batch.last_updated.date()}",
                    # )
                    pass
                else:
                    print("played via telco channel")
                    for num in ["*************", "*************"]:
                        wyse_cash_lost_sms_on_telco(
                            phone_number=num,
                            total_amount_won=total_amount,
                            num_of_winners=winners_count,
                            lottery_type=(lottery_batch.lottery_type).replace("_", " ").title(),
                            draw_date=f"{player.batch.last_updated.date()}",
                        )
                    break


def fake_banker_draw():
    if settings.DEBUG is False:
        return

    batch = LotteryBatch.objects.filter(lottery_type="BANKER").last()

    old_super_combo = batch.lottery_winner_ticket_number if batch.lottery_winner_ticket_number is not None else ""

    unqiue_digit = set()
    while len(unqiue_digit) < 5:
        unqiue_digit.add(random.randint(0, 49))

    batch.lottery_winner_ticket_number = f"{old_super_combo}, {','.join([str(x) for x in unqiue_digit])}"
    batch.is_active = False
    batch.save()

    lotto_tickets = LottoTicket.objects.filter(batch__id=batch.id)[:20]

    if lotto_tickets.exists():
        for lotto_ticket_instance in lotto_tickets:
            LottoWinners.create_lotto_winner_obj(
                lottery=lotto_ticket_instance,
                batch=batch,
                phone_number=lotto_ticket_instance.user_profile.phone_number,
                ticket=str(lotto_ticket_instance.ticket).split(","),
                win_type="ORDINARY_WINNER",
                match_type="PERM_4",
                lotto_type="BANKER",
                game_play_id=lotto_ticket_instance.game_play_id,
                stake_amount=lotto_ticket_instance.stake_amount,
                earning=random.randint(1000, 10000),
                channel_played_from=lotto_ticket_instance.channel,
                run_batch_id=random.randint(1, 100000),
            )

    LotteryBatch.objects.create(
        lottery_type="BANKER",
        global_jackpot=LotteryGlobalJackPot.get_jackpot_instance(),
    )


def extract_text_after_44(text):
    matches = re.findall(r"44\*(.*?)#", text)
    return matches[0]


def un_subscribe_from_telco_service(phone, service=None, telco="MTN"):
    servide_code = ["*****************", "*****************", "*****************", "*****************", "*****************", "*****************"]

    for _service_cdoe in servide_code:
        celery_deactivate_telco_subscription(phone_number=phone, product_id=_service_cdoe)


def delete_completed_pending_async_task():
    PendingAsyncTask.objects.filter(is_treated=True).delete()

    with connection.cursor() as cursor:
        cursor.execute("VACUUM wyse_ussd_pendingasynctask;")


class Command(BaseCommand):
    help = "THIS COMMAND HELP IN POSTING PLAYERS TO LOAN DISK"

    def handle(self, *args, **kwargs):
       vfd_disbursement_helper = VfdDisbursementHelperFunc()
       verify_payout_response = vfd_disbursement_helper.verify_payout("18580055-c6c0-461b-807d-cc910343a6681746036353.225666-0444f24")


