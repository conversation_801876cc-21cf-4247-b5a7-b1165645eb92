from django.core.management.base import BaseCommand
from main.models import ConstantVariableParam

DEFAULT_PARAMS = [
    ("icash_tickets_count", 0, 0.00, "Total tickets processed"),
]

class Command(BaseCommand):
    help = "Ensure all default parameters exist"

    def handle(self, *args, **kwargs):
        for param_name, default_int, default_float, description in DEFAULT_PARAMS:
            ConstantVariableParam.get_param(param_name, default_int, default_float, description)
        self.stdout.write(self.style.SUCCESS("Default parameters ensured."))