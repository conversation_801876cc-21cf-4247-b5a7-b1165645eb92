from django.core.management.base import BaseCommand

from liberty_lotto.celery import celery


def list_registered_tasks():
    inspector = celery.control.inspect()
    registered_tasks = inspector.registered()  # Get all registered tasks

    if not registered_tasks:
        return "No registered tasks."

    tasks = []
    for worker, task_list in registered_tasks.items():
        tasks.extend(task_list)
    return tasks


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # inspector = inspect()
        # active_tasks = inspector.active()

        # task_name =

        # if not active_tasks:
        #     print("No active tasks.")

        # for worker, tasks in active_tasks.items():
        #     for task in tasks:
        #         if task['name'] == task_name:
        #             return f"Task {task_name} is running on worker {worker}."
        # return f"No running task named {task_name}."

        tasks = list_registered_tasks()
        print(tasks)
