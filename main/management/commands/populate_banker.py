from django.core.management.base import BaseCommand


def celery_salary_for_life_draw_pos():
    pass

    from banker_lottery.models import SureBanker

    SureBanker.banker_draw()
    # try:
    #     LotteryBatch.objects.all().annotate(lotteryticket_set=Count('lottoticket'))

    #     salary_for_life_jackpot = LotteryGlobalJackPot.get_jackpot_instance()

    #     all_banker_batches = LotteryBatch.objects.filter(lottery_type="BANKER")
    #     all_banker_batches.update(is_active=False)

    #     # batch:LotteryBatch = LotteryBatch.objects.create(
    #     #     lottery_type="BANKER",
    #     #     global_jackpot=salary_for_life_jackpot,
    #     # )
    #     batch_uid = "Mar-3ef49406-0316-449d-9875-f1e0aa7fd07f"
    #     # new_batch_uid = batch.batch_uuid
    #     # print(batch)

    #     tickets = LottoTicket.objects.filter(batch__batch_uuid=batch_uid)

    #     target_batch_uid = "Mar-e084b0d3-9c68-4417-b2c7-9fbd78d42f83"
    #     target_batch = LotteryBatch.objects.get(batch_uuid=target_batch_uid)

    #     for ticket in tickets:

    #         excluded = ['_state', 'id', 'date', 'updated_at', 'lottery_type', 'salary_for_life_jackpot_per', 'salary_for_life_jackpot_amount', 'instant_cashout_drawn', 'pos_instant_cashout_drawn', 'icash_counted', 'icash_2_counted']
    #         tick_dict = ticket.__dict__

    #         for key in excluded:
    #             del tick_dict[key]

    #         # print(ticket)
    #         print(list(tick_dict.keys()))

    #         batch:ticket = SureBanker.objects.create(
    #             lottery_type="BANKER",
    #             batch=target_batch,
    #             **tick_dict
    #         )

    #     return "Salary for Life Draw Completed"
    # except SyntaxError:
    #     print("FAILED THIS TIME. RETRYING")
    #     LottoTicket.salary_for_life_draw_pos()
    #     return "Salary for Life Draw Completed"


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        celery_salary_for_life_draw_pos()


# x = {'_state': """<django.db.models.base.ModelState object at 0x7f652a0efeb0>""", 'id': 296672, 'user_profile_id': 56482, 'agent_profile_id': 42, 'batch_id': 13646, 'phone': '*************', 'stake_amount': 150.0, 'potential_winning': 15000.0, 'expected_amount': 150.0, 'amount_paid': 175.0, 'rto': 7.4375, 'rtp': 114.********, 'rtp_per': 95.0, 'commission_per': 0.15, 'commission_value': 26.25, 'salary_for_life_jackpot_per': 0.15, 'salary_for_life_jackpot_amount': 20.***************, 'win_commission_per': 0.0, 'win_commission_value': 0.0, 'paid': True,
#  'date': """datetime.datetime(2023, 3, 4, 17, 53, 55, 270667, tzinfo=datetime.timezone.utc)""",
#  'updated_at': """datetime.datetime(2023, 3, 4, 17, 54, 4, 581205, tzinfo=datetime.timezone.utc),""",
#  'number_of_ticket': 2, 'channel': 'POS_AGENT', 'game_play_id': 'f5H8398', 'lottery_type': 'SALARY_FOR_LIFE', 'lottery_source': 'NORMAL', 'has_interest': True, 'ticket': '10,9,5,7,2', 'system_generated_num': None, 'win_combo': None, 'is_agent': True, 'instant_cashout_drawn': False, 'pos_instant_cashout_drawn': False, 'icash_counted': False, 'icash_2_counted': False, 'is_duplicate': False, 'pin': '244653'}

# print(list(x.keys()))
