from django.core.management.base import BaseCommand
from django.db.models import Q

from main.helpers.whisper_sms_managers import demo_sms_compensate
from main.models import (
    DisbursementTable,
    LotteryBatch,
    LotteryWinnersTable,
    PaymentTransaction,
)


class Command(BaseCommand):
    help = "THIS COMMAND HELP IN POSTING PLAYERS TO LOAN DISK"

    def handle(self, *args, **kwargs):
        # batch = LotteryBatch.objects.filter(batch_uuid="f842a1c9-1d85-4aaf-9d4b-8914bf2142a2").last()
        # batch = LotteryBatch.objects.filter(batch_uuid="Jun-f3eff5b1-4").last()
        batch = LotteryBatch.objects.all().order_by("-id")[1]
        batch.lottery_players.all()

        get_all_winners_number = LotteryWinnersTable.objects.filter(batch=batch).values_list("phone_number", flat=True)

        # print(batch.lottery_players.all())
        get_non_winners = batch.lottery_players.filter(~Q(phone__in=get_all_winners_number)).values_list("phone").distinct("phone")
        print(get_non_winners)

        get_non_player_payment_amount = PaymentTransaction.objects.filter(Q(lottery_batch=batch) & Q(lottery_player__phone__in=get_non_winners))

        for player in get_non_player_payment_amount:
            phone_number = player.lottery_player.phone
            payout_amount = (player.amount) * 2
            print(phone_number)

            DisbursementTable.objects.create(
                lottery_batch=batch,
                player_phone_num=player.lottery_player.phone,
                payout_amount=payout_amount,
                is_demmo_player=True,
            )

            demo_sms_compensate(phone_number=phone_number, amount=payout_amount)

        # send_demo_sms = demo_sms_compensate(phone_number=player.lottery_player.phoner, amount=(player.amount)*2)

        # numbers = ["2349069090865", "2348077469471", "2347039516293"]
        # for i in numbers:
        #     notify = notfy_non_winners_on_raffle_end(phone_number=i, link="https://spectacular-arithmetic-d20107.netlify.app")
        #     payment_receipt = payment_receipt_sms(phone_number=i, amount="5000")
        #     print(notify)

        # # random.choice(["100", "200", "500", "1000", "2000"])
        # for lottery_player in LotteryWinnersTable.objects.all():

        #     if lottery_player.pool == "TEN_THOUSAND":
        #         # lottery_player.band = "10000"
        #         lottery_player.stake_amount = round(random.randint(100,500), -2)
        #         lottery_player.save()

        #     elif lottery_player.pool == "FIFTY_THOUSAND":
        #         # lottery_player.band = "50000"
        #         lottery_player.stake_amount =  round(random.randint(200,1000), -2)
        #         lottery_player.save()

        #     elif lottery_player.pool == "TWO_FIFTY_THOUSAND":
        #         # lottery_player.band = "250000"
        #         lottery_player.stake_amount = round(random.randint(500,2500), -2)
        #         lottery_player.save()

        #     elif lottery_player.pool == "FIVE_HUNDRED_THOUSAND":
        #         # lottery_player.band = "500000"
        #         lottery_player.stake_amount = round(random.randint(1000,5000), -2)
        #         lottery_player.save()

        #     elif lottery_player.pool == "ONE_MILLION":
        #         # lottery_player.band = "1000000"
        #         lottery_player.stake_amount = round(random.randint(2000,10000), -2)
        #         lottery_player.save()

        #     elif lottery_player.pool == "TWO_MILLION":
        #         # lottery_player.band = "2000000"
        #         lottery_player.stake_amount = round(random.randint(2000,20000), -2)
        #         lottery_player.save()

        # if lottery_player.pool == "TEN_THOUSAND":
        #     lottery_player.band = "10000"
        #     lottery_player.stake_amount = 100.00
        #     lottery_player.save()

        # elif lottery_player.pool == "FIFTY_THOUSAND":
        #     lottery_player.band = "50000"
        #     lottery_player.stake_amount = 200.00
        #     lottery_player.save()

        # elif lottery_player.pool == "TWO_FIFTY_THOUSAND":
        #     lottery_player.band = "250000"
        #     lottery_player.stake_amount = 500.00
        #     lottery_player.save()

        # elif lottery_player.pool == "FIVE_HUNDRED_THOUSAND":
        #     lottery_player.band = "500000"
        #     lottery_player.stake_amount = 1000.00
        #     lottery_player.save()

        # elif lottery_player.pool == "ONE_MILLION":
        #     lottery_player.band = "1000000"
        #     lottery_player.stake_amount = 2000.00
        #     lottery_player.save()

        # elif lottery_player.pool == "TWO_MILLION":
        #     lottery_player.band = "2000000"
        #     lottery_player.stake_amount = 4000.00
        #     lottery_player.save()

        # time_to_check = datetime(2022, 6, 27, 21, 30).replace(tzinfo=utc)
        # for get_old_woven in WovenAccountDetail.objects.all():
        #     if get_old_woven.date.replace(tzinfo=utc) < time_to_check:
        #         get_old_woven.is_active = False
        #         get_old_woven.save()
