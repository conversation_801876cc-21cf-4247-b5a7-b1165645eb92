from django.core.management.base import BaseCommand

# def similar(a, b):
#     _ratio = SequenceMatcher(None, a, b).ratio()
#     _len = len(a) + len(b)
#     return 100 * _ratio
#     return int(2.0 * _ratio * _len / _len)


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # paystack = PaymentGateway()
        # _verify_res = paystack.fetch_account_name("**********", "044")
        # print(_verify_res)

        # if isinstance(_verify_res, dict):
        #     if _verify_res.get("status") is True:
        #         account_name = (_verify_res.get("data").get("account_name")).casefold()
        #         name = "<PERSON>".casefold()

        #         print(similar("abcd", "bcde"))
        # percentage = JackpotConstantVariable.get_icash_jackpot_sharing_perc()[0]
        # win_amount = (percentage / 100) * 30000
        # print(win_amount)

        pass
