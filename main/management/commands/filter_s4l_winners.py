import os
import random
import requests  # Add this import for making HTTP requests
from django.core.management.base import BaseCommand, CommandError
from django.db import models
from main.models import LottoTicket, LotteryBatch, ConstantVariable, LotteryGlobalJackPot
from decisioning_engine.draw import SalaryForLifeDraw # Assuming SalaryForLifeDraw is here

class Command(BaseCommand):
    help = (
        'Filters and prints Salary For Life winners for a given batch_id and resolved_combo. '
        'The resolved_combo should be a comma-separated string of numbers (e.g., "1,2,3,4,5").'
    )

    def add_arguments(self, parser):
        parser.add_argument('batch_id', type=str, help='The batch_uuid of the LotteryBatch to process')
        parser.add_argument('rtp', type=float, help='The RTP (Return to Player) value to pass to the endpoint')

    def handle(self, *args, **options):
        batch_id = options['batch_id']
        rtp = options['rtp']

        self.stdout.write(f"Processing batch_id: {batch_id} with RTP: {rtp}")

        try:
            match_batch = LotteryBatch.objects.get(batch_uuid=batch_id)
        except LotteryBatch.DoesNotExist:
            raise CommandError(f'LotteryBatch with batch_uuid "{batch_id}" does not exist.')

        if match_batch.lottery_type != "SALARY_FOR_LIFE":
            self.stdout.write(self.style.WARNING(f"Warning: Batch {batch_id} is not of type SALARY_FOR_LIFE. It is {match_batch.lottery_type}."))

        lottery_qs = LottoTicket.objects.filter(batch=match_batch, paid=True, lottery_type="SALARY_FOR_LIFE")

        if not lottery_qs.exists():
            self.stdout.write(self.style.WARNING(f"No paid SALARY_FOR_LIFE LottoTickets found for batch_id: {batch_id}"))
            return

        # Calculate RTP from the sum of RTP on the ticket table
        total_rtp = lottery_qs.aggregate(total_rtp=models.Sum('rtp'))['total_rtp'] or 0
        self.stdout.write(f"Calculated RTP from tickets: {total_rtp}")

        # Use the calculated RTP if not explicitly provided
        rtp = rtp or total_rtp
        self.stdout.write(f"Using RTP: {rtp}")

        plays = list(
            map(
                lambda x: (x.number_of_ticket, [int(i) for i in x.ticket.split(",")]),
                lottery_qs,
            )
        )

        if not plays:
            self.stdout.write(self.style.WARNING(f"No plays constructed for batch_id: {batch_id}. This might mean tickets have invalid formats."))
            return

        endpoint_url = "http://localhost:3000/draw"  # Replace with the actual endpoint URL
        payload = {
            "plays": plays,
            "rtp": rtp
        }

        try:
            response = requests.post(endpoint_url, json=payload)
            response.raise_for_status()
            result = response.json()
        except requests.RequestException as e:
            raise CommandError(f"Error calling the draw endpoint: {e}")

        self.stdout.write(self.style.SUCCESS("Winning Combination and Amount:"))
        self.stdout.write(f"Best Match Combo: {result.get('best_match_combo')}")
        self.stdout.write(f"Best Match Percentage: {result.get('best_match')}%")

        resolved_combo = result.get('best_match_combo')

        # Run filter_winnings to show all winnings
        from flask_app.s4l_draw import filter_winnings, prices

        self.stdout.write("Running filter_winnings to show all winnings...")
        try:
            all_winnings = filter_winnings(resolved_combo, plays, prices, rtp)
            if all_winnings:
                self.stdout.write(self.style.SUCCESS("All Winnings:"))
                for winning in all_winnings:
                    self.stdout.write(f"Match Count: {winning[0]}, Ticket Details: {winning[1]}, Amount Won: {winning[2]}")
            else:
                self.stdout.write(self.style.WARNING("No winnings found for the given combo and plays."))
        except Exception as e:
            raise CommandError(f"Error during filter_winnings: {e}")
