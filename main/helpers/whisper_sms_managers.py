import requests
from django.conf import settings
from django.db.models import Sum

from main.helpers.helper_functions import currency_formatter
from main.ussd.helpers import Utility


def human_currency_format(num):
    magnitude = 0
    while abs(num) >= 1000:
        magnitude += 1
        num /= 1000.0
    # add more suffixes if you need them

    if len(str(num).split(".")) == 2 and int((str(num).split("."))[-1]) > 0:
        return Utility.currency_formatter(num * 1000)
    response = "%.2f%s" % (int(num), ["", "K", "M", "G", "T", "P"][magnitude])
    return f'N{response.replace(".00", "")}'


def send_sms_for_payment_collection(
    phone_number,
    amount,
    stake,
    watupay_ussd_code,
    account_number,
    bank_name,
    checkout_link,
    lottery_type=None,
):
    print(("send_sms_for_payment_collection", "amount", amount, "stake", stake))

    whisper_url = "https://whispersms.xyz/transactional/send"

    print("lottery_type", lottery_type, "\n\n\n\n\n\n")

    try:
        stake = f"{human_currency_format(stake)}"
    except Exception:
        stake = stake

    if lottery_type == "SALARY_FOR_LIFE" or lottery_type == "WYSE_CASH":
        whisper_payload = {
            "receiver": f"{phone_number}",
            "template": "69b42470-35de-45be-b3ba-aaab1aabcf1e",
            "place_holders": {
                "Amount": stake,
                "Win amount": stake,
                "ussd_code": watupay_ussd_code,
                "account_number": account_number,
                "bank_name": bank_name,
                "Checkout_link": checkout_link,
            },
        }

    elif lottery_type == "INSTANT_CASHOUT":
        whisper_payload = {
            "receiver": f"{phone_number}",
            "template": "55ee1a89-bfe3-4248-bbd6-a8609c7a2306",
            "place_holders": {
                "Amount": stake,
                "Win amount": stake,
                "ussd_code": watupay_ussd_code,
                "account_number": account_number,
                "bank_name": bank_name,
                "Checkout_link": checkout_link,
            },
        }

    elif lottery_type == "SOCCER_CASH":
        from sport_app.models import FootballTable
        from wyse_ussd.models import SoccerPrediction

        user_last_soccer_prediction_instance = SoccerPrediction.objects.filter(user_profile__phone_number=phone_number).last()

        if user_last_soccer_prediction_instance:
            soccer_obj = FootballTable.objects.filter(fixture_id=user_last_soccer_prediction_instance.game_fixture_id).last()

            if soccer_obj:
                whisper_payload = {
                    "receiver": f"{phone_number}",
                    "template": "16f49b1b-30c6-4451-a4d5-3f9dd9dcaf2c",
                    "place_holders": {
                        "Amount": stake,
                        "Win amount": stake,
                        "ussd_code": watupay_ussd_code,
                        "account_number": account_number,
                        "bank_name": bank_name,
                        "Checkout_link": checkout_link,
                        "Game": f"{soccer_obj.home_team} vs {soccer_obj.away_team}",
                        "Prediction": f"{user_last_soccer_prediction_instance.home_choice} - {user_last_soccer_prediction_instance.away_choice}",
                    },
                }

            else:
                whisper_payload = {
                    "receiver": f"{phone_number}",
                    "template": "2775cb95-f8c8-41c8-a402-9780be13027b",
                    "place_holders": {
                        "amount": stake,
                        "stake": stake,
                        "watupay_ussd_code": watupay_ussd_code,
                        "account_number": account_number,
                        "bank_name": bank_name,
                        "checkout_link": checkout_link,
                    },
                }

        else:
            whisper_payload = {
                "receiver": f"{phone_number}",
                "template": "2775cb95-f8c8-41c8-a402-9780be13027b",
                "place_holders": {
                    "amount": stake,
                    "stake": stake,
                    "watupay_ussd_code": watupay_ussd_code,
                    "account_number": account_number,
                    "bank_name": bank_name,
                    "checkout_link": checkout_link,
                },
            }

    elif lottery_type == "AWOOF":
        whisper_payload = {
            "receiver": f"{phone_number}",
            "template": "584b63fd-fc9c-4f8b-823a-47aad592097a",
            "place_holders": {
                "Amount": f"{human_currency_format(amount)}",
                "item": stake,
                "account_number": account_number,
                "bank_name": bank_name,
                "Checkout_link": checkout_link,
            },
        }
    else:
        whisper_payload = {
            "receiver": f"{phone_number}",
            "template": "2775cb95-f8c8-41c8-a402-9780be13027b",
            "place_holders": {
                "amount": stake,
                "stake": stake,
                "watupay_ussd_code": watupay_ussd_code,
                "account_number": account_number,
                "bank_name": bank_name,
                "checkout_link": checkout_link,
            },
        }
    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    print(
        f"""
        whisper_payload: {whisper_payload}
    """
    )
    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


def send_congrats_sms_to_winners_with_account_num(phone_number, share, account_num, bank_name):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": f"{settings.LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE_WITH_ACCOUNT}",
        "place_holders": {
            "share": f"{currency_formatter(share)}",
            "account_num": account_num,
            "bank_name": bank_name,
        },
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


def send_congrats_sms_to_winners_without_account_num(phone_number, share):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": f"{settings.LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE}",
        "place_holders": {"share": f"{currency_formatter(share)}"},
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


def notfy_non_winners_on_raffle_end(phone_number):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": f"{settings.LIBERTY_LOTTO_CHECK_WINNERS_LINK_LIST}",
        "place_holders": {},
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)

    return response


def payment_receipt_sms(phone_number, amount):
    from main.models import LotteryModel, LottoTicket, UserProfile
    from wyse_ussd.models import UssdLotteryPayment
    from wyse_ussd.tasks import (
        handle_telco_payemnt_receipt,
        possible_lottery_or_game_played,
    )

    whisper_url = "https://whispersms.xyz/transactional/send"

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    user_profile = UserProfile.objects.filter(phone_number=phone_number).last()

    game_play_id = UssdLotteryPayment.objects.filter(user__phone_number=user_profile.phone_number).last()

    print(f"  ::::::::::::::::::::::::::::::: START OF payment_receipt_sms {phone_number} :::::::::::::::::::")

    if game_play_id is None:
        whisper_payload = {
            "receiver": f"{phone_number}",
            "template": f"{settings.LIBERTY_LOTTO_PAYMENT_RECEIPT_TEMPLATE}",
            # "template": "66f8749a-b41f-4afe-92c6-3609f2fd5014",
            "place_holders": {"amount": str(amount)},
        }

        print("game_play_id is None")

    else:
        lotto_query_set = possible_lottery_or_game_played(game_play_id.game_play_id)

        if lotto_query_set:
            # print(
            #     """:
            # :::::::::::::::::
            # :::::::::::::::::
            # """
            # )
            ticket_instance = lotto_query_set.last()

            from datetime import datetime, timedelta

            now = datetime.now()
            week_name = str(now.strftime("%A"))[:3]
            get_current_date = now.strftime("%d/%m/%y")

            get_current_time = ((datetime.now() + timedelta(minutes=5)).time()).strftime("%H:%M")

            current_time = datetime.strptime(str(get_current_time), "%H:%M")
            current_time = current_time.strftime("%I:%M %p")

            if ticket_instance.lottery_type == "AWOOF" and ticket_instance.played_via_telco_channel is True:
                sms_payload = {
                    "receiver": f"{phone_number}",
                    "amount": Utility.currency_formatter(int(amount)),
                    "game_type": "AWOOF",
                }
                return handle_telco_payemnt_receipt("985da284-bf9c-435c-a40f-f79a947aaf41", **sms_payload)

            if (
                ticket_instance.lottery_type == "INSTANT_CASHOUT" or ticket_instance.lottery_type == "SALARY_FOR_LIFE"
            ) and ticket_instance.played_via_telco_channel is True:
                sms_payload = {
                    "receiver": f"{phone_number}",
                    "amount": Utility.currency_formatter(int(amount)),
                    "potential_win": Utility.currency_formatter(ticket_instance.potential_winning),
                    "game": (ticket_instance.lottery_type).replace("_", " "),
                    "next_game": f"{week_name} {get_current_date} {current_time}",
                    "game_play_id": game_play_id.game_play_id,
                    "game_type": ticket_instance.lottery_type,
                    "ticket_qs": lotto_query_set,
                    "lottery_type": ticket_instance.lottery_type,
                }

                if ticket_instance.lottery_type == "INSTANT_CASHOUT":
                    if ticket_instance.amount_paid == 150:
                        sms_payload["_send"] = False

                return handle_telco_payemnt_receipt("66f8749a-b41f-4afe-92c6-3609f2fd5014", **sms_payload)

            if ticket_instance.lottery_type == "WYSE_CASH" and ticket_instance.played_via_telco_channel is True:
                sms_payload = {
                    "receiver": f"{phone_number}",
                    "amount": Utility.currency_formatter(int(amount)),
                    "potential_win": Utility.currency_formatter(ticket_instance.band),
                    "game": (ticket_instance.lottery_type).replace("_", " "),
                    "next_game": f"{week_name} {get_current_date} {current_time}",
                    "game_play_id": game_play_id.game_play_id,
                    "lottery_type": ticket_instance.lottery_type,
                }

                return handle_telco_payemnt_receipt("66f8749a-b41f-4afe-92c6-3609f2fd5014", **sms_payload)

        lottery_qs = LottoTicket.objects.filter(game_play_id=game_play_id.game_play_id)
        if lottery_qs.exists():
            lottery_instance = lottery_qs.last()

            if lottery_instance.lottery_type == "INSTANT_CASHOUT":
                #
                #
                # get weekday name
                from datetime import datetime, timedelta

                now = datetime.now()
                week_name = str(now.strftime("%A"))[:3]
                get_current_date = now.strftime("%d/%m/%y")

                # get current time + 5 minutes
                get_current_time = ((datetime.now() + timedelta(minutes=5)).time()).strftime("%H:%M")
                current_time = datetime.strptime(str(get_current_time), "%H:%M")
                current_time = current_time.strftime("%I:%M %p")

                whisper_payload = {
                    "receiver": f"{phone_number}",
                    # "template": f"{settings.LIBERTY_LOTTO_PAYMENT_RECEIPT_TEMPLATE}",
                    "template": "66f8749a-b41f-4afe-92c6-3609f2fd5014",
                    "place_holders": {
                        "amount": Utility.currency_formatter(int(amount)),
                        "potential_win": Utility.currency_formatter(lottery_instance.potential_winning),
                        # "numbers": lottery_instance.ticket,
                        "game": (lottery_instance.lottery_type).replace("_", " "),
                        "next_game": f"{week_name} {get_current_date} {current_time}",
                        "game_play_id": game_play_id.game_play_id,
                    },
                }

                print("lottery type is INSTANT_CASHOUT")
            else:
                #
                #
                # get weekday name
                from datetime import datetime, timedelta

                now = datetime.now()
                week_name = str(now.strftime("%A"))[:3]
                get_current_date = now.strftime("%d/%m/%y")

                # get current time + 5 minutes
                get_current_time = ((datetime.now() + timedelta(minutes=5)).time()).strftime("%H:%M")
                current_time = datetime.strptime(str(get_current_time), "%H:%M")
                current_time = current_time.strftime("%I:%M %p")

                whisper_payload = {
                    "receiver": f"{phone_number}",
                    # "template": f"{settings.LIBERTY_LOTTO_PAYMENT_RECEIPT_TEMPLATE}",
                    "template": "66f8749a-b41f-4afe-92c6-3609f2fd5014",
                    "place_holders": {
                        "amount": str(amount),
                        "potential_win": Utility.currency_formatter(lottery_instance.potential_winning),
                        # "numbers": lottery_instance.ticket,
                        "game": (lottery_instance.lottery_type).replace("_", " "),
                        "next_game": f"{week_name} {get_current_date} 8 pm",
                        "game_play_id": game_play_id.game_play_id,
                    },
                }

                print("lottery type is SALARY_FOR_LIFE")
        else:
            #
            #
            # get weekday name
            from datetime import datetime, timedelta

            now = datetime.now()
            week_name = str(now.strftime("%A"))[:3]
            get_current_date = now.strftime("%d/%m/%y")

            # get current time + 5 minutes
            get_current_time = ((datetime.now() + timedelta(minutes=5)).time()).strftime("%H:%M")
            current_time = datetime.strptime(str(get_current_time), "%H:%M")
            current_time = current_time.strftime("%I:%M %p")

            lottery_instance = LotteryModel.objects.filter(game_play_id=game_play_id.game_play_id).last()
            if lottery_instance is None:
                whisper_payload = {
                    "receiver": f"{phone_number}",
                    "template": f"{settings.LIBERTY_LOTTO_PAYMENT_RECEIPT_TEMPLATE}",
                    # "template": "66f8749a-b41f-4afe-92c6-3609f2fd5014",
                    "place_holders": {"amount": str(amount)},
                }

                print("Invalid game_play_id")

            else:
                #
                #
                # get weekday name
                from datetime import datetime, timedelta

                now = datetime.now()
                week_name = str(now.strftime("%A"))[:3]
                get_current_date = now.strftime("%d/%m/%y")

                # get current time + 5 minutes
                get_current_time = ((datetime.now() + timedelta(minutes=5)).time()).strftime("%H:%M")
                current_time = datetime.strptime(str(get_current_time), "%H:%M")
                current_time = current_time.strftime("%I:%M %p")

                whisper_payload = {
                    "receiver": f"{phone_number}",
                    # "template": f"{settings.LIBERTY_LOTTO_PAYMENT_RECEIPT_TEMPLATE}",
                    "template": "66f8749a-b41f-4afe-92c6-3609f2fd5014",
                    "place_holders": {
                        "amount": str(amount),
                        "potential_win": Utility.currency_formatter(lottery_instance.band),
                        # "numbers": lottery_instance.lucky_number,
                        "game": (lottery_instance.lottery_type).replace("_", " "),
                        "next_game": f"{week_name} {get_current_date} {current_time}",
                        "game_play_id": game_play_id.game_play_id,
                    },
                }

                print("lottery typ is wyse cash")

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    print(whisper_resp.json())

    print("  ::::::::::::::::::::::::::::::: END OF payment_receipt_sms :::::::::::::::::::")

    return whisper_resp.json()


def collect_account_details(phone_number, share):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": f"{settings.LIBERTY_LOTTO_WINNERS_LIST}",
        "place_holders": {"share": f"{currency_formatter(share)}"},
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    return whisper_resp.json()


def demo_sms_compensate(phone_number, amount):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": f"{settings.LIBERTY_LOTTO_DEMO_SMS_FOR_NON_WINNERS}",
        "place_holders": {"amount": f"{currency_formatter(amount)}"},
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    print(whisper_resp.json())
    return whisper_resp.json()


def sms_recollect_bank_account_nums(phone_number):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": f"{settings.LIBERTY_LOTTO_RESEND_ACCOUNT_COLLECT_SMS}",
        "place_holders": {},
    }
    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    return whisper_resp.json()


def chnage_transaction_pin_otp_sms(phone_number, name, otp):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": f"{settings.TRANSACTION_OTP_TEMPLATE}",
        "place_holders": {"user": name, "otp": otp},
    }
    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    return whisper_resp.json()


def referral_campaign_sms(user, data):
    from main.models import LotteryModel, LotteryWinnersTable

    whisper_url = "https://whispersms.xyz/transactional/send"

    lottery_wins = LotteryWinnersTable.objects.filter(phone_number=user.phone_number)
    if not lottery_wins.exists():
        return None

    for key in data:
        _data = dict(*[key.items()])
        refer_friend = _data["first_name"]
        phone_no = _data["phone_no"]

        phone = LotteryModel.format_number_from_back_add_234(phone_no)

        user_phone = user.phone_number.replace(user.phone_number[0:3], "0", 1)

        amount_won = lottery_wins.aggregate(Sum("earning"))
        amount = Utility.currency_formatter(int(amount_won["amount_won"]))

        whisper_payload = {
            "receiver": f"{phone}",
            "template": f"{settings.REFERRAL_SMS_TEMPLATE}",
            "place_holders": {
                "user": refer_friend,
                "friends_name": user.first_name,
                "friends_number": user_phone,
                "amount": amount,
                "referral_link": user.referal_url,
            },
        }
        whisper_headers = {
            "Authorization": f"Api_key {settings.WHISPER_KEY}",
            "Content-Type": "application/json",
        }

        requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    return {"message": "success"}


def single_referral_campaign_sms(**data):
    whisper_url = "https://whispersms.xyz/transactional/send"

    whisper_payload = {
        "receiver": f"{data.get('phone')}",
        "template": f"{settings.REFERRAL_SMS_TEMPLATE}",
        "place_holders": {
            "user": data.get("refer_friend"),
            "friends_name": data.get("first_name"),
            "friends_number": data.get("friends_number"),
            "amount": data.get("amount"),
            "referral_link": data.get("referal_url"),
        },
    }
    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    res = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    print("res", res.text, "\n\n\n")

    return {"message": "success"}


def account_activation_otp_sms(phone_number, otp):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": f"{settings.ACCOUNT_ACTIVATION_OTP_TEMPLATE}",
        "place_holders": {"username": "customer", "otp": otp},
    }
    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    print(whisper_resp.text)
    # print("whisper_payload", whisper_payload)

    return whisper_resp.json()


# send_sms_for_payment_collection("*************", "100", "5000", "*737#", "**********", "GTB", "www.libertyng.com")


def send_sms_for_wyse_lost(phone_number, total_amount_won, num_of_winners, lottery_type, draw_date):
    if settings.DEBUG or settings.DEBUG is True:
        return {"message": "success"}

    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": "fad30d3e-c8bc-42f9-9ac4-08eeda536c88",
        "place_holders": {
            "Total Amount Won": f"{Utility.currency_formatter(total_amount_won)}",
            "no_of_Winners": f"{num_of_winners}",
            "Game": f"{lottery_type} game",
            "Draw_date": draw_date,
        },
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


def send_sms_for_wyse_winners(phone_number, amount_won, ticket, lottery_type, draw_date):
    if settings.DEBUG or settings.DEBUG is True:
        return {"message": "success"}

    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": "ebfd94d1-3c03-47a9-9d77-a8f5e1c074bd",
        "place_holders": {
            "Won Amount": f"{Utility.currency_formatter(amount_won)}",
            "Game": f"{lottery_type} game",
            "Draw_date": draw_date,
            "winning_no": f"{ticket}",
        },
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


def send_sms_for_s4fgame_lost(phone_number, total_amount_won, num_of_winners, lottery_type, draw_date, winning_no):
    if settings.DEBUG or settings.DEBUG is True:
        return {"message": "success"}

    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": "93c6a85f-d0e3-4030-ac79-baa5600b3788",
        "place_holders": {
            "Total Amount Won": f"{Utility.currency_formatter(total_amount_won)}",
            "no_of_Winners": f"{num_of_winners}",
            "Game": f"{lottery_type} game",
            "Draw_date": draw_date,
            "Winning_no": winning_no,
        },
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


def send_sms_for_ussd_topup(
    phone_number,
    amount_to_pay,
    game_type,
    ussd_payment_code,
    woven_account_number,
    woven_bank_name,
    bitly_checkout_link,
):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": "a4d9ef89-ef40-46f8-a37a-a57e9eb5815e",
        "place_holders": {
            "amount_to_pay": f"{Utility.currency_formatter(amount_to_pay)}",
            "game_type": f"{game_type.replace('_', ' ')}",
            "ussd_code": f"{ussd_payment_code}",
            "account_no": f"{woven_account_number}",
            "bank": f"{woven_bank_name}",
            "checkout": f"{bitly_checkout_link}",
        },
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


def general_withdraw_wallet_bal_notification(user_phone, amount_requested, wallet_balance):
    url = "https://whispersms.xyz/transactional/send"

    phones = ["*************", "*************", "*************"]
    # phones = [
    #     "*************",
    # ]

    for phone in phones:
        payload = {
            "receiver": f"{phone}",
            "template": "ce0fc3f2-a490-40ca-905c-db487dcf0dc6",
            "place_holders": {
                "withraw_amount": f"{Utility.currency_formatter(amount_requested)}",
                "wallet_balance": f"{Utility.currency_formatter(wallet_balance)}",
                "user_phone": user_phone,
            },
        }

        headers = {
            "Authorization": f"Api_key {settings.WHISPER_KEY}",
            "Content-Type": "application/json",
        }

        response = requests.request("POST", url, headers=headers, json=payload)

    return response.json()


def send_woven_callback(data):
    url = "https://whispersms.xyz/whisper/api/woven_call_back/"

    headers = {
        "content-type": "application/json",
    }

    response = requests.request("POST", url, headers=headers, json=data)

    return response.json()


def whisper_voice_otp(phone):
    payload = {
        "applicationId": "2cd9426d-2f7e-47f8-bcf0-31624a320798",
        "messageId": "f9509d89-d916-4191-bb88-6c03d36106f7",
        "message_type": "VOICE",
        "placeholders": {},
        "to": phone,
    }

    OTP_SEND_URL = "https://whispersms.xyz/2fa/message/"

    headers = {
        "content-type": "application/json",
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
    }

    response = requests.request("POST", OTP_SEND_URL, headers=headers, json=payload)
    # print(response.text)
    return response.json()


def whisper_verify_voice_otp(phone, otp):
    payload = {"receiver": phone, "otp": otp}

    print(payload)

    OTP_VERIFY_URL = "https://whispersms.xyz/2fa/verify/"

    headers = {
        "content-type": "application/json",
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
    }

    # print(headers)

    response = requests.request("POST", OTP_VERIFY_URL, headers=headers, json=payload)
    # print(response.text)
    return response.json(), response.status_code


def send_sms_winning_to_players_from_agent(phone_number, amount_won, game_play_id, agent_name, pin):
    if settings.DEBUG or settings.DEBUG is True:
        return {"message": "success"}

    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_payload = {
        "receiver": f"{phone_number}",
        "template": "a34de689-84fb-48c9-b3ce-a2575e861fa3",
        "place_holders": {
            "amount_won": f"{Utility.currency_formatter(amount_won)}",
            "game_play_id": f"{game_play_id}",
            "agent_name": f"{agent_name}",
            "pin": f"{pin}",
        },
    }

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=whisper_payload)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response


def send_simple_sms_campaign(**kwargs):
    if settings.DEBUG or settings.DEBUG is True:
        return {"message": "success"}

    whisper_url = "https://whispersms.xyz/transactional/send"

    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    whisper_resp = requests.request("POST", whisper_url, headers=whisper_headers, json=kwargs)

    try:
        response = whisper_resp.json()
    except Exception:
        response = whisper_resp.text
    print(response)
    return response
