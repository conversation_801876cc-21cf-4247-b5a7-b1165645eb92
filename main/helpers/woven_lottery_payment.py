from rest_framework import status
from rest_framework.response import Response

from main.helpers.woven_manager import woven_verify_transaction
from main.models import (
    LotteryBatch,
    LotteryModel,
    LottoTicket,
    PaymentCollectionDetail,
    PaymentTransaction,
    UserProfile,
    WovenCallBack,
)
from main.tasks import celery_send_whatsapp_payment_notification_admin
from wallet_app.models import DebitCreditRecord, UserWallet
from wallet_app.tasks import notify_admin_of_user_funding
from wyse_ussd.helper.woven_soccer_payment import handle_woven_soccer_prediction
from wyse_ussd.models import SoccerPrediction, UssdLotteryPayment
from wyse_ussd.tasks import share_ussd_payment_across_lottery_pool


def lottery_payment_via_woven(
    woven_data,
    from_wema=True,
    payment_channel="WOVEN_ACCOUNT",
    transaction_from="WOVEN_FUNDING",
):
    # print(
    #     f"""

    #       ===============================================
    #       LOTTERY PAYMENT VIA WOVEN, "\n\n\n

    #       """
    # )

    resp = woven_data

    account_ref = resp.get("account_reference")
    unique_ref = resp.get("unique_reference")
    amount = resp.get("amount")

    _check_for_transaction = PaymentTransaction.objects.filter(
        provider_transaction_reference=unique_ref,
    ).last()

    if _check_for_transaction:
        if _check_for_transaction.status == "SUCCESSFUL":
            return {"status": "SUCCESSFUL", "message": "transaction already processed"}

    if from_wema:
        get_wallet = UserWallet.objects.filter(wema_account__vnuban=account_ref).last()
    else:
        get_wallet = UserWallet.objects.filter(account_ref=account_ref).last()

    if not get_wallet:
        return {"status": "FAILED", "message": "can't find get_wallet"}

    query_lottery_player = PaymentCollectionDetail.objects.filter(woven_account_ref=account_ref).last()

    user_profile = UserProfile.objects.filter(phone_number=get_wallet.user.phone_number).last()

    if resp.get("status") == "ACTIVE":
        if _check_for_transaction:
            _check_for_transaction.status = "SUCCESSFUL"
            _check_for_transaction.save()

        else:
            PaymentTransaction.objects.create(
                lottery_player=user_profile,
                amount=int(amount),
                provider_transaction_reference=unique_ref,
                unique_provider_transaction_reference=unique_ref,
                status="SUCCESSFUL",
                payment_channel=payment_channel,
            )

        if unique_ref is None:
            return {"status": "FAILED", "message": "unique_ref is None"}

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=user_profile.phone_number,
            amount=int(amount),
            channel="WEB",
            reference=unique_ref,
            transaction_type="CREDIT",
        )

        wallet_payload = {
            "transaction_from": transaction_from,
        }

        UserWallet.fund_wallet(
            user=user_profile,
            amount=int(amount),
            channel="WEB",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="GAME_PLAY_WALLET",
            **wallet_payload,
        )

        # notify admin
        notify_admin_of_user_funding.delay(amount=amount, phone_number=user_profile.phone_number, channel="WOVEN")

    get_ussd_pending_lottery_payment_gameid = UssdLotteryPayment.objects.filter(
        game_play_id__isnull=False,
        user=user_profile,
        is_successful=False,
        is_verified=False,
        channel="USSD_WEB",
    ).last()

    if get_ussd_pending_lottery_payment_gameid is None:
        if resp.get("status") == "ACTIVE":
            if _check_for_transaction:
                _check_for_transaction.status = "SUCCESSFUL"
                _check_for_transaction.save()

            else:
                PaymentTransaction.objects.create(
                    lottery_player=user_profile,
                    amount=int(amount),
                    provider_transaction_reference=unique_ref,
                    unique_provider_transaction_reference=unique_ref,
                    status="SUCCESSFUL",
                    payment_channel="WOVEN_ACCOUNT",
                )

            if unique_ref is None:
                return {"status": "FAILED", "message": "unique_ref is None"}

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=user_profile.phone_number,
                amount=int(amount),
                channel="WEB",
                reference=unique_ref,
                transaction_type="CREDIT",
            )

            wallet_payload = {
                "transaction_from": "transaction_from",
            }

            UserWallet.fund_wallet(
                user=user_profile,
                amount=int(amount),
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="GAME_PLAY_WALLET",
                **wallet_payload,
            )

            # notify admin
            notify_admin_of_user_funding.delay(amount=amount, phone_number=user_profile.phone_number, channel="WOVEN")
        else:
            print("can't find get_ussd_pending_lottery_payment_gameid")
            return Response(
                {"data": "can't find get_ussd_pending_lottery_payment_gameid"},
                status=status.HTTP_200_OK,
            )

    # now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

    # time diff between now and time created of the ussd lottery payment
    # time_diff = now - get_ussd_pending_lottery_payment_gameid.created_at

    # if time_diff.min > 5:
    #     return {"status": "FAILED", "message": "transaction has expired"}

    lottery_instance = LottoTicket.objects.filter(game_play_id=get_ussd_pending_lottery_payment_gameid.game_play_id).last()

    if not lottery_instance:
        lottery_instance = LotteryModel.objects.filter(game_play_id=get_ussd_pending_lottery_payment_gameid.game_play_id).last()

        if not lottery_instance:
            # check soccer table
            lottery_instance = SoccerPrediction.objects.filter(game_id=get_ussd_pending_lottery_payment_gameid.game_play_id).last()

            if not lottery_instance:
                print("can't find lottery_instance")
                return (
                    share_ussd_payment_across_lottery_pool(
                        get_ussd_pending_lottery_payment_gameid.user.phone_number,
                        float(get_ussd_pending_lottery_payment_gameid.amount),
                        get_ussd_pending_lottery_payment_gameid.game_play_id,
                        transfrom="WEMA_ACCOUNT",
                        from_web=True,
                    ),
                )

            else:
                data = handle_woven_soccer_prediction(resp)
                print("data", data)

                return Response(data=data, status=status.HTTP_200_OK)

    game_current_batch = LotteryBatch.objects.filter(id=lottery_instance.batch.id).last()

    if not get_wallet:
        return Response(data={"data": "can't find get_wallet"}, status=status.HTTP_200_OK)

    elif get_wallet:
        get_lottery_player_user = UserProfile.objects.filter(phone_number=get_wallet.user.phone_number).last()
        get_lottery_player_user.phone_number

    elif query_lottery_player:
        payment_collection_detail = query_lottery_player.lottery_player
        payment_collection_detail.phone

    if resp.get("status") == "ACTIVE":
        # Check if transaction is in our database
        check_for_transaction = PaymentTransaction.objects.filter(provider_transaction_reference=unique_ref).last()

        if check_for_transaction:
            # Check woven to verify transaction
            verfiy_transaction = woven_verify_transaction(transaction_reference=unique_ref)

            if verfiy_transaction and from_wema is False:
                if check_for_transaction.status != "SUCCESSFUL":
                    check_for_transaction.status = "SUCCESSFUL"
                    check_for_transaction.has_paid = True
                    check_for_transaction.amount = float(amount)

                    check_for_transaction.save()

                    check_for_payload = WovenCallBack.objects.filter(transaction=check_for_transaction).last()

                    if check_for_payload:
                        check_for_payload.payload = resp
                        check_for_payload.save()
                    else:
                        # Create provider raw payload

                        WovenCallBack.objects.create(transaction=check_for_transaction, payload=resp)
            else:
                share_ussd_payment_across_lottery_pool(
                    get_ussd_pending_lottery_payment_gameid.user.phone_number,
                    float(get_ussd_pending_lottery_payment_gameid.amount),
                    get_ussd_pending_lottery_payment_gameid.game_play_id,
                    transfrom="WEMA_ACCOUNT",
                    from_web=True,
                ),
                print("transaction not verified")
                return Response(data={"data": "transaction not verified"}, status=status.HTTP_200_OK)
        else:
            # Check woven to verify transaction
            verfiy_transaction = woven_verify_transaction(transaction_reference=unique_ref)

            if verfiy_transaction:
                # Add transaction to database
                new_collection_transaction = PaymentTransaction.objects.create(
                    lottery_player=user_profile,
                    lottery_batch=game_current_batch,
                    payment_channel="WOVEN_ACCOUNT",
                    amount=amount,
                    provider_transaction_reference=unique_ref,
                    unique_provider_transaction_reference=unique_ref,
                    status="SUCCESSFUL",
                    has_paid=True,
                )

                check_for_payload = WovenCallBack.objects.filter(transaction=new_collection_transaction).last()

                if check_for_payload:
                    check_for_payload.payload = resp
                    check_for_payload.save()
                else:
                    # Create provider raw payload

                    WovenCallBack.objects.create(transaction=new_collection_transaction, payload=resp)

            else:
                return Response(data={"data": "Transaction not verified"}, status=status.HTTP_200_OK)

        get_ussd_pending_lottery_payment_gameid.is_successful = True
        get_ussd_pending_lottery_payment_gameid.is_verified = True
        get_ussd_pending_lottery_payment_gameid.save()
        game_play_id = get_ussd_pending_lottery_payment_gameid.game_play_id

        share_ussd_payment_across_lottery_pool(
            user_profile.phone_number,
            int(amount),
            game_play_id,
            transfrom="WOVEN_FUNDING",
            transaction_unique_id=unique_ref,
        )

        last_ussd_payment = UssdLotteryPayment.objects.filter(
            user=user_profile,
            game_play_id__isnull=False,
        ).last()

        lotto_qs = LottoTicket.objects.filter(game_play_id=last_ussd_payment.game_play_id)

        if lotto_qs.exists():
            lotto_qs = lotto_qs.last()

            celery_send_whatsapp_payment_notification_admin.delay(
                phone_number=user_profile.phone_number,
                batch_id=lotto_qs.batch.batch_uuid,
                amount=amount,
                paid_via="WOVEN",
            )

            return Response(data={"data": "success"}, status=status.HTTP_200_OK)

        else:
            lotto_qs = LotteryModel.objects.filter(game_play_id=game_play_id).last()

            if lotto_qs:
                celery_send_whatsapp_payment_notification_admin.delay(
                    phone_number=user_profile.phone_number,
                    batch_id=lotto_qs.batch.batch_uuid,
                    amount=amount,
                    paid_via="WOVEN",
                )

        return Response(data={"data": "success"}, status=status.HTTP_200_OK)
