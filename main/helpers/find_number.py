import collections
import random
from functools import reduce


def find_num(numbers, matches_required, log=False, game_play_id=None):
    from main.models import DrawData, LottoTicket

    print(
        f"""

    find_num
    log: {log}
    game_play_id: {game_play_id}

    \n\n\n\n\n\n


    """
    )

    """FORCE MATCHES REQUIRED TO 1 FOLLOWING INABILITY FOR FIND MATCHES WHEN USER PICKS ONY ONE VALUE AND HIGH WINING IS AVAILABLE"""
    for i in numbers:
        if 0 in i:
            matches_required = 1

    all_nums = list(range(1, 41))

    final = reduce(lambda x, y: x + y, numbers)
    final = list(final)

    set_form = [set(num) for num in numbers]
    non_occuring = set(all_nums).difference(set(final))

    occurrences = collections.Counter(final)
    sorted_occurences = sorted(occurrences.items(), key=lambda x: x[1])
    occurences = [key[0] for key in sorted_occurences]

    def line_matches(reference, target, match_len):
        line_matches = 0

        for line in reference:
            matches = line.intersection(target)

            if len(matches) == match_len:
                line_matches += 1

        return line_matches

    outliers_required = 4 - matches_required

    for _ in range(10000):
        target_combo = random.choices(list(occurences), k=matches_required)
        print()
        matches = line_matches(set_form, target_combo, matches_required)

        if matches == 1:
            winning_num = list(target_combo) + list(non_occuring)[:outliers_required]
            break
    else:
        winning_num = []

    print("WINNING NUMBER::", winning_num)

    if len(winning_num) > 0:
        # shuffle winning number
        random.shuffle(winning_num)
        random.shuffle(winning_num)

    # save record in draw meta data
    if game_play_id is None:
        pass
    else:
        if len(winning_num) > 0:
            system_pick_number = ",".join([str(i) for i in winning_num])

            print(
                f"""
            FIND_NUM: :::::::: system_pick_number: {system_pick_number}
            game_play_id: {game_play_id}
            \n\n\n\n\n
            """
            )
            qs = LottoTicket.objects.filter(game_play_id=game_play_id)
            qs.update(system_generated_num=system_pick_number)

            saved_system_pick_number = LottoTicket.objects.filter(game_play_id=game_play_id).values_list("system_generated_num", flat=True)

            print(
                f"""
            saved_system_pick_number: {saved_system_pick_number}
            \n\n\n\n
            """
            )

            if qs.exists():
                pass
                # qs.update(system_generated_num=system_pick_number)
                # cp_system_pick_number = system_pick_number
                # for i in qs:
                #     i.system_generated_num = cp_system_pick_number
                #     i.save()

                #     print(
                #         f"""
                #     cp_system_pick_number: {cp_system_pick_number}
                #     saved: cp_system_pick_number
                #     \n\n\n
                #     """
                #     )

                #     LottoTicket.objects.filter(game_play_id=game_play_id).update(
                #         system_generated_num=cp_system_pick_number
                #     )
                # saved_system_pick_number = LottoTicket.objects.filter(
                #     game_play_id=game_play_id
                # ).values_list("system_generated_num", flat=True)
                # print(
                #     f"""
                # saved_system_pick_number: {saved_system_pick_number}
                # \n\n\n\n
                # """
                # )
            else:
                pass

    if log:
        DrawData.objects.create(
            game_type="INSTANT_CASHOUT",
            factor1={"user_picks": numbers},
            factor2={"system_pick": winning_num},
        )

    return winning_num


# if __name__ == "__main__":
#     numbers = [random.choices(range(1,4), k=4) for i in range(7)]
#     sample_numbers = [
#                         [20, 8, 30, 4],
#                         [36, 35, 9, 3],
#                         [19, 37, 32, 26],
#                         [26, 15, 13, 24],
#                         [24, 1, 13, 17],
#                         [37, 3, 3, 30],
#                         [6, 27, 27, 19]
#                     ]
#     print(find_num(numbers, 2))
