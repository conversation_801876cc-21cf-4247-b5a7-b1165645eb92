from datetime import datetime

from main.helpers.redis_storage import RedisStore
from main.models import LotteryModel


class HopsRestrictor:
    @classmethod
    def is_restricted(cls, phone_number):
        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)
        now = datetime.now()

        hops, timestamp, pending_payment = cls.get_data(phone_number)

        # cls.increment_hops(phone_number)

        # print(get_create_data)
        start_time = datetime.fromtimestamp(float(timestamp))
        time_diff = (now - start_time).total_seconds() / 3600

        # print("\n")
        # print("TIME DIFF :::::::::::::::", time_diff, "TIME STAMP >>>>>>>>>>", float(timestamp))
        # print("HOPS >> ", hops ,"TIME DIFF ::", time_diff, "PENDING PAYMENT >>", pending_payment)

        if time_diff >= 1:
            RedisStore.set_data(phone_number, f"0,{now.timestamp()},0")
            return {"response": False, "reason": None}

        if int(hops) >= 20:
            return {"response": True, "reason": "OPPS"}

        if int(pending_payment) >= 2:
            return {"response": True, "reason": "PENDING_PAYMENT"}

        return {"response": False, "reason": None}

    @classmethod
    def increment_hops(cls, phone_number):
        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        hops, timestamp, pending_payment = cls.get_data(phone_number)
        RedisStore.set_data(phone_number, f"{int(hops) + 1},{timestamp},{pending_payment}")
        return True

    @classmethod
    def increment_pending_payment(cls, phone_number):
        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        # print(">>>>>>>>>>>>>>> I GOT TO PENDING PAYMENT <<<<<<<<<<<<<<<<<<")
        # print("PHONE NUMBER @ PENDING PAYMENT", phone_number, "=======")

        hops, timestamp, pending_payment = cls.get_data(phone_number)
        print(hops, timestamp, pending_payment)

        # print("INCREMENT", int(pending_payment) + 1)

        RedisStore.set_data(phone_number, f"{hops},{timestamp},{int(pending_payment) + 1}")

        print("AFTER EXECUTION", hops, timestamp, pending_payment)
        return True

    @staticmethod
    def get_data(phone_number):
        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)
        now = datetime.now()

        get_create_data = RedisStore.get_or_create_data(phone_number, f"0,{now.timestamp()},0")
        hops, timestamp, pending_payment = get_create_data.split(",")

        return hops, timestamp, pending_payment
