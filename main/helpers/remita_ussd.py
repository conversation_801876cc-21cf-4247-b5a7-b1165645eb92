import json

import requests


class RemitaUssd:
    def __init__(self):
        self.base_url = "https://libertyussd.com"
        self.headers = {"Content-Type": "application/json"}

    def get_borrower_details(self, phone):
        url = f"{self.base_url}/api/lottery/get_borrower"

        data = json.dumps({"phone": phone})
        response = requests.request("POST", url, headers=self.headers, data=data)
        return response.json()
