import json
import random
import uuid
from time import sleep

import requests
from django.conf import settings

_count_down = {}
_agent_id = {}


def generate_woven_collection_account_number(phone_number, func_count, tag, bank_code=None, agent_id=None):
    from main.models import UserProfile, WovenAccountDetail

    return None

    # sleep(10)

    if settings.DEBUG is True:
        # this is a test payload
        data = {
            "vnuban": "**********",
            "bank_name": "Wema Bank",
            "account_name": "Joe ventures",
            "account_reference": "Joe ventures-5e2b4a",
            "woven_payload": {
                "status": "success",
                "message": "The process was completed successfully",
                "data": {
                    "vnuban": "**********",
                    "account_name": "Joe ventures",
                    "bank_name": "Wema Bank",
                    "account_reference": "Joe ventures-5e2b4a",
                    "callback_url": "https://libertydraw.com/liberty/api/woven/call_back/",
                    "expires_on": "2022-07-10 16:24:54",
                    "use_frequency": "",
                    "min_amount": 100,
                    "max_amount": 200000,
                    "collection_bank": "000017",
                    "destination_nuban": "**********",
                    "customer_reference": "Joe ventures-5e2b4a",
                    "mobile_number": "***********",
                    "email": "",
                    "created_at": "2022-07-10 16:24:54",
                    "updated_at": "2022-07-10 16:24:54",
                    "id": 1386415,
                    "status": "ACTIVE",
                    "is_deleted": False,
                    "deleted_at": None,
                },
            },
        }
        return data

    if str(phone_number).isdigit() is False:
        return None

    count = _count_down.get(phone_number)
    get_agent_id = _agent_id.get(phone_number)

    if count is None:
        _count_down[phone_number] = int(func_count)
        count = _count_down[phone_number]

    if get_agent_id is None:
        _agent_id[phone_number] = agent_id

    agent_id = _agent_id[phone_number]

    uuid_for_woven = str(uuid.uuid4())[:6]
    func_count = 3
    get_user_profile = UserProfile.objects.filter(phone_number=phone_number).last()

    if agent_id is None:
        if get_user_profile and get_user_profile.first_name:
            first_name = get_user_profile.first_name
            last_name = get_user_profile.last_name
            customer_email = get_user_profile.email if get_user_profile.email else "<EMAIL>"

        else:
            first_name = phone_number
            last_name = "Whispawyse"
            customer_email = "<EMAIL>"
    else:
        from pos_app.models import Agent

        agent_obj = Agent.objects.filter(id=agent_id).last()

        first_name = agent_obj.first_name
        last_name = agent_obj.last_name
        customer_email = agent_obj.email if agent_obj.email else "<EMAIL>"

    # Create Customer with phone number
    base_url = "https://api.woven.finance"
    filter_str = "/v2/api/vnubans/create_customer"
    url = base_url + filter_str

    if settings.WOVEN_DEVELOPMENT_MODE:
        headers = {"api_secret": settings.WOVEN_API_KEY_TEST}
        destination_nuban = settings.TEST_DESTINATION_BANK
    else:
        headers = {"api_secret": settings.WOVEN_API_KEY_LIVE}
        destination_nuban = settings.LIVE_DESTINATION_BANK

    callback_url = "https://libertydraw.com/liberty/api/woven/call_back/"

    WEMA_COLLECTION_BANK = f"{settings.WEMA_BANK_CODE}"
    CMB_COLLECTION_BANK = f"{settings.CMB_BANK_CODE}"

    if bank_code is None:
        COLLECTION_BANK = WEMA_COLLECTION_BANK
    else:
        COLLECTION_BANK = bank_code

    payload = {
        "customer_reference": f"{first_name}-{uuid_for_woven}",
        "name": f"{first_name} {last_name}",
        "email": f"{customer_email}",
        "mobile_number": phone_number,
        # "expires_on": str_due_date,
        "use_frequency": "",
        "min_amount": 100,
        "max_amount": 200000,
        "collection_bank": COLLECTION_BANK,
        # "collection_bank": "060001", #CMB
        # "collection_bank": "000017", #WEMA
        "callback_url": callback_url,
        "destination_nuban": destination_nuban,
    }

    print((headers))

    try:
        response = requests.request("POST", url, headers=headers, json=payload, timeout=30)
    except requests.exceptions.ReadTimeout:
        print("ReadTimeout")

        get_count_down = _count_down.get(phone_number)

        if get_count_down < 1:
            del _count_down[phone_number]
            return None
        else:
            _count_down[phone_number] = get_count_down - 1
            return generate_woven_collection_account_number(phone_number, func_count - 1, tag, bank_code, agent_id)

    res = json.loads(response.text)

    if res.get("status", None) == "success":
        WovenAccountDetail.objects.create(
            phone_number=phone_number,
            vnuban=res["data"]["vnuban"],
            acct_name=res["data"]["account_name"],
            bank_name=res["data"]["bank_name"],
            account_ref=res["data"]["account_reference"],
            payload=res,
            wallet_tag=tag,
        )

        data = {
            "vnuban": res["data"]["vnuban"],
            "bank_name": res["data"]["bank_name"],
            "account_name": res["data"]["account_name"],
            "account_reference": res["data"]["account_reference"],
            "woven_payload": res,
        }

        try:
            del _count_down[phone_number]
            del _agent_id[phone_number]
        except KeyError:
            pass

        return data

    else:
        if count == 0:
            return None

        elif count == 1:
            COLLECTION_BANK = CMB_COLLECTION_BANK
            return generate_woven_collection_account_number(phone_number, func_count - 1, tag=tag, bank_code=COLLECTION_BANK)

        else:
            # Return a recursive function to retry woven account creation
            if str(phone_number).isdigit() is False:
                return None
            if "RETAIL" in phone_number:
                return None

            WovenAccountDetail.objects.create(
                phone_number=phone_number,
                is_active=False,
                payload=f"Retry Countdown - {func_count} | {res}",
            )
            sleep(5)
            _count = int(_count_down.get(phone_number)) - 1
            print(_count)

            COLLECTION_BANK = random.choice(
                [
                    settings.WEMA_BANK_CODE,
                    settings.CMB_BANK_CODE,
                    settings.SPARKLE_BANK_CODE,
                ]
            )[0]

            return generate_woven_collection_account_number(phone_number, _count - 1, tag=tag, bank_code=COLLECTION_BANK)


def woven_verify_transaction(transaction_reference):
    return False
    base_url = "https://api.woven.finance"
    filter_str = "/v2/api/transactions"
    url = base_url + filter_str

    if settings.DEBUG is True:
        return True

    headers = {"api_secret": settings.WOVEN_API_KEY}

    params = {"unique_reference": transaction_reference}

    response = requests.request("GET", url, headers=headers, params=params)
    res = response.json()

    if res["status"] == "success" and len(res["data"]["transactions"]) != 0:
        return True
    else:
        return False


class WovenHelper:
    def __init__(self):
        self.url = "https://api.woven.finance/v2/api"
        self.auth_key = settings.WOVEN_DISBURSEMENT_API_SECRET
        self.source_account = settings.WOVEN_DISBURSEMENT_SOURCE_ACCOUNT

        self.headers = {
            "api_secret": f"{settings.WOVEN_DISBURSEMENT_API_SECRET}",
            "requestId": str(uuid.uuid4()),
            "Content-Type": "application/json",
        }

    def initaite_payout_to_winners(self, **kwargs):
        """
        This method disburss payment using woven gateway

        """
        from wallet_app.models import FloatWallet

        return None

        if settings.DEBUG is True:
            return True

        data = json.dumps(kwargs)

        url = f"{self.url}/payouts/request?command=initiate"

        try:
            response = requests.request("POST", url, headers=self.headers, data=data)
            # update float wallet
            FloatWallet().update_float_wallet()
            return json.loads(response.text)
        except requests.exceptions.ConnectionError:
            return None
        except requests.exceptions.Timeout:
            return None

    def woven_payment_verification(self, ref_id):
        SINGLE_PAYOUT_URL = f"{self.url}/transactions"
        # PAYMENT_REFERENCE_KEY="payout_reference"

        return False

        if settings.DEBUG is True:
            return True

        # url = f"{SINGLE_PAYOUT_URL}{PAYMENT_REFERENCE_KEY}={ref_id}"

        params = {"unique_reference": f"{ref_id}"}

        headers = {"api_secret": f"{settings.WOVEN_DISBURSEMENT_API_SECRET}"}

        response = requests.request("GET", url=SINGLE_PAYOUT_URL, headers=headers, params=params)
        # print(response.json())
        return response.json()

    def woven_balance(self) -> dict:
        """The function returns details of woven balance"""

        url = f"{self.url}/reserved_vnuban/{self.source_account}"

        print(url)

        return False

        if settings.DEBUG is True:
            return True

        headers = {"api_secret": f"{self.auth_key}"}

        response = requests.request("GET", url, headers=headers)
        resp = json.loads(response.text)
        print(resp)

        """
        SAMPLE RESPONSE:
        {'status': 'success', 'message': 'The process was completed successfully', 'data': {'account_name': 'Joe ventures', 'nuban': '**********', 'vnuban_bank_code': '000017', 'vnuban_bank_name': 'Wema Bank', 'PIN': 'PIN EXISTS', 'available_balance': '*********.99', 'current_balance': '*********.99', 'pending_payout_and_fee': '0.00', 'total_funding_count': 1, 'funding_value': '*********.99', 'total_payout_count': 22, 'payout_value': '29431.00', 'total_transactions': 23}}
        """

        return resp

        # if resp.get("status") == "success":
        #     woven_balance = float(resp.get("data").get("available_balance"))
        # else:
        #     woven_balance = None

        # return woven_balance

    @staticmethod
    def get_transaction_with_ref(ref_id):
        PAYMENT_REFERENCE_KEY = "reference"
        LIST_PAYOUT_URL = "https://api.woven.finance/v2/api/merchant/payouts?"
        # PAYMENT_REFERENCE_KEY="payout_reference"

        return False

        url = f"{LIST_PAYOUT_URL}{PAYMENT_REFERENCE_KEY}={ref_id}"

        if settings.DEBUG is True:
            return True

        payload = {}
        headers = {"api_secret": f"{settings.WOVEN_DISBURSEMENT_API_SECRET}"}

        response = requests.request("GET", url, headers=headers, data=payload)

        """        
        Sample response
        
        {
            "status": "success",
            "message": "The process was completed successfully",
            "data": {
                "page_info": {
                    "total": 1,
                    "current_page": 1,
                    "total_pages": 1
                },
                "payout_transactions": [
                    {
                        "id": 298327,
                        "beneficiary_account_name": "LILIAN EJENGA",
                        "resolved_account_name": "EJENGA LILIAN IJEOMA",
                        "beneficiary_nuban": "**********",
                        "merchant_id": 297,
                        "vnubantx_id": 1386415,
                        "bank_code": "000007",
                        "bank_code_scheme": "000007",
                        "currency": "NGN",
                        "narration": "Loan disbursement",
                        "amount": 35340,
                        "transaction_date": "2022-07-10 16:24:55",
                        "payout_reference": "Libty-70610aa8006411ed8e153d44d100a9eb",
                        "status": "ACTIVE",
                        "callback_url": "https://libertyussd.com/api/vart_acc__callback",
                        "meta_data": "{\"is_notified\": false}",
                        "created_by": "297",
                        "updated_by": "297",
                        "payout_fee_id": 1386416,
                        "payout_fee": 10,
                        "fee_status": null,
                        "createdAt": "2022-07-10 16:24:55",
                        "updatedAt": "2022-07-10 16:25:11",
                        "merchant_name": "Liberty Assured Limited 2",
                        "source_nuban": "**********",
                        "source_bank_code": "090325",
                        "fee": 10,
                        "unique_reference": "wf_py_297_5aty5uwlx1s",
                        "nip_session_id": "000017220710162458057804267513",
                        "transaction_status": "ACTIVE",
                        "user_message": "Transaction successful",
                        "vnuban": "**********",
                        "payout_date": "2022-07-10 16:24:54",
                        "opening_balance": 0,
                        "closing_balance": 0,
                        "account_email": "<EMAIL>"
                    }
                ]
            }
        }
        
        """  # noqa

        return response.json()
