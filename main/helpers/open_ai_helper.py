
from django.conf import settings
import openai

class OpenAiHelper:
    @staticmethod
    def get_openai_api_key() -> str:
        """

        """
        api_key = settings.OPENAI_API_KEY
        
        return api_key
    
    @staticmethod
    def prompt_open_ai(prompt, model="gpt-4o"):
        
        openai.api_key = OpenAiHelper.get_openai_api_key()
        
        messages = [{"role": "user", "content": prompt}]
        
        response = openai.ChatCompletion.create(
            model=model,
            messages=messages,
            temperature=0,
        )
        
        return {"status": True, "message": response.choices[0].message["content"]}