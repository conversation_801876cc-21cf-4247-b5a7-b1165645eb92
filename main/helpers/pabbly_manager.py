import uuid

import requests
from django.conf import settings

PABBLY_AUTH_USER = settings.PABBLY_AUTH_USER
PABBLY_AUTH_PASS = settings.PABBLY_AUTH_PASS

auth = (PABBLY_AUTH_USER, PABBLY_AUTH_PASS)


def create_pabbly_plan(phone, amount):
    if amount is None:
        return None

    # sleep(20)

    # product_id = "628e3fe09dcb4a60765def3f" Liberty product id
    product_id = "634dbc6b564dd211f71d914d"  # WinWise product id
    url = "https://payments.pabbly.com/api/v1/plan/create"

    unique_id = f"{phone}-{str(uuid.uuid4())[:5]}"

    payload = {
        "product_id": product_id,
        "plan_name": "WinWise",
        "plan_code": unique_id,
        "price": float(int(amount)),
        "billing_cycle": "onetime",
        "plan_type": "flat_fee",
        "payment_gateway": "selected",
        "gateways_array": ["634ab3880f2f0269ca3da910"],
        "redirect_url": "https://www.wisewinn.com/",
    }

    headers = {"Content-Type": "application/json"}

    try:
        response = requests.request("POST", url, headers=headers, json=payload, auth=auth, timeout=10)

        res = response.json()

        if res.get("status", None) == "success":
            return {
                "plan_id": res["data"]["id"],
                "plan_checkout_link": res["data"]["checkout_page"],
            }
        else:
            return None
    # except requests.exceptions.Timeout

    except requests.exceptions.ConnectionError:
        return None
