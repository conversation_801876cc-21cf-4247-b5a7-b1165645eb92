from datetime import timedelta

import redis
import requests
from django.conf import settings
from decouple import config



class VfdDisbursementHelperFunc:
    @classmethod
    def account_enquiry(cls, account_number, bank_code):
        """
        This function is used to check if a user account_number is valid
        """
        url = f"{settings.AGENCY_BANKING_BASE_URL}/send/fetch_account_name/"

        token = (cls.login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        payload = {"account_number": account_number, "bank_code": bank_code}
        response = requests.post(url, json=payload, headers=headers)
        try:
            res = response.json()
        except Exception:
            res = None

        """
        SAMPLE RESPONSE
        
        {
            "message": "account name found",
            "data": {
                "account_number": "**********",
                "account_name": "NWAOMA CHUKWUEMEKA JOEL"
            }
        }
        """  # noqa

        status_code = response.status_code

        return res, status_code

    @classmethod
    def login(cls):
        """
        This function is used to login to the vfd api
        """
        from pos_app.models import AgencyBankingToken

        return {"access": AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")}

        

    @classmethod
    def initiate_payout(cls, **kwargs):
        from main.tasks import check_vfd_float_account_and_notify_admin
        from pos_app.models import AgencyBankingToken

        payload = kwargs

        token = AgencyBankingToken.retrieve_token("NON_RETAIL_WALLET")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_bank_account/"

        response = requests.post(url, json=payload, headers=headers)

        check_vfd_float_account_and_notify_admin.apply_async(
            queue="celery3",
        )

        print(
            f"""
              RESPONSE: {response.text}
              """
        )

        return response

    @classmethod
    def verify_payout(cls, reference):
        url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/fetch_transaction?reference={reference}"

        token = (cls.login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        response = requests.get(url, headers=headers)

        """
        SAMPLE RESPONSE:
        {
            "status": "error",
            "message": "No escrow id attached"
        }
        
        {
            "status":"error",
            "message":"transaction escrow ID is not a valid ID"
        }
        
        {
            "status":"error",
            "message":"transaction with escrow ID does not exist""            "
        }
        
        
        {
            "status":"PENDING",
            "transaction_type":"SEND_BANK_TRANSFER",
            "amount":200.0,
            "escrow_id":"7f01d27b-0bef-42e1-a2cc-464aeeb23a80",
            "transaction_leg":"TEMP_EXTERNAL",
            "narration":"Food",
            "liberty_commission":0.0,
            "sms_charge":0.0,
            "balance_before":10570.0,
            "balance_after":10370.0,
            "beneficiary_nuban":"**********",
            "beneficiary_account_name":"Chukwuemeka Nwaoma",
            "beneficiary_bank_code":"000013",
            "liberty_reference":"LGLP-VFD-bc81ac49-4ec2-4a8b-9f23-642e2dc59fa2",
            "is_reversed":"false"
        }
        
        
        {
            "status":"SUCCESSFUL",
            "transaction_type":"SEND_BANK_TRANSFER",
            "amount":50.0,
            "escrow_id":"09dd9434-bcc3-41e6-b6c3-a00899d44b0d",
            "transaction_leg":"EXTERNAL",
            "narration":"Food",
            "liberty_commission":0.0,
            "sms_charge":0.0,
            "balance_before":9350.0,
            "balance_after":9300.0,
            "beneficiary_nuban":"**********",
            "beneficiary_account_name":"Chukwuemeka Nwaoma",
            "beneficiary_bank_code":"000013",
            "liberty_reference":"LGLP-VFD-23cbccbe-d0d2-4de1-b0f2-9a3b40971c4b",
            "is_reversed":"false"
        }        
        """  # noqa

        try:
            return response.json()
        except Exception:
            return None

    @classmethod
    def fetch_liberty_agency_buddy_account(cls, phone):
        url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/{phone}/"

        token = (cls.login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        response = requests.get(url, headers=headers)

        """
        ERROR RESPONSE:
        {'status': 'error', 'message': 'Buddy Does Not Exist', 'invite_user': 'https://backend.libertypayng.com/invite_new_user/*************/'}
        
        
        CORRECT RESPONSE:
        {'message': 'buddy found', 'data': {'full_name': 'JOSEPH OGBU', 'phone_number': '*************'}}
        """  # noqa

        return response.json()

    @classmethod
    def liberty_agency_payout(cls, phone, **kwargs):
        url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

        from main.tasks import check_vfd_float_account_and_notify_admin

        token = (cls.login())["access"]

        # verify if the phone number is valid
        fetch_buddy = cls.fetch_liberty_agency_buddy_account(phone)
        if fetch_buddy.get("status") == "error":
            return fetch_buddy

        payload = kwargs

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        response = requests.post(url, headers=headers, json=payload)

        check_vfd_float_account_and_notify_admin.apply_async(
            queue="celery3",
        )

        """                
        SAMPLE RESPONSE:
        {'message': 'success', 'data': {'message': 'Transaction completed successfully', 'amount_sent': 10.0}, 'date_completed': '2022-12-07T16:03:27.661556'}
        """  # noqa

        try:
            res = response.json()
        except Exception:
            res = None

        return res
