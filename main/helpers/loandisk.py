import json
from datetime import datetime

import requests
from django.conf import settings


def full_name_split(name):
    """
    This functions split and return user names in a dictonary
    """
    splited_names = name.split()
    if len(splited_names) == 2:
        names = {
            "first_name": splited_names[0] if len(splited_names) > 0 else "",
            "middle_name": splited_names[2] if len(splited_names) > 2 else "",
            "last_name": splited_names[1] if len(splited_names) > 1 else "",
            "full_name": name,
        }

        return names
    elif len(splited_names) == 3:
        names = {
            "first_name": splited_names[0] if len(splited_names) > 0 else "",
            "last_name": splited_names[2] if len(splited_names) > 2 else "",
            "middle_name": splited_names[1] if len(splited_names) > 1 else "",
            "full_name": name,
        }
        return names

    elif len(splited_names) == 1:
        names = {
            "first_name": splited_names[0] if len(splited_names) > 0 else "",
            "middle_name": "",
            "last_name": "",
            "full_name": name,
        }
        return names

    else:
        names = {
            "first_name": "",
            "last_name": "",
            "middle_name": "",
            "full_name": name,
        }
        return names


def loan_disk_date_format(pass_date=None):
    """
    This function handles loan disk date format
    """

    if not pass_date:
        date_formate = datetime.now().date()
        date_formate = datetime.strptime(f"{date_formate}", "%Y-%m-%d").strftime("%d/%m/%Y")
        return date_formate

    if pass_date:
        date_formate = pass_date.date()
        date_formate = datetime.strptime(f"{date_formate}", "%Y-%m-%d").strftime("%d/%m/%Y")
        return date_formate


class LoandiskHelper:
    BRANCH_ID = "21569"

    def __init__(self) -> str:
        self.base_url = f"https://api-main.loandisk.com/{settings.LOAN_DISK_PUBLICK_KEY}/{settings.LOAN_DISK_BRANCH_ID}"
        self.headers = {
            "Authorization": f"Basic {settings.LOAN_DISK_SEC_KEY}",
            "Content-Type": "application/json",
        }

    def create_player(self, **kwargs):
        get_player_name = kwargs.get("account_name")
        phone = kwargs.get("phone")
        account_no = kwargs.get("account_num")
        bank_code = kwargs.get("bank_code")

        dommy_name = f"USSD {kwargs.get('id')} lotto"
        name = full_name_split(get_player_name if get_player_name is not None else dommy_name)

        payload = {
            "borrower_country": "NG",
            "borrower_fullname": name.get("full_name"),
            "borrower_firstname": name.get("first_name"),
            "borrower_lastname": name.get("last_name"),
            "custom_field_5854": name.get("middle_name"),
            "borrower_business_name": "",
            "borrower_gender": "",
            "borrower_title": "",
            "borrower_mobile": phone,
            "custom_field_5037": "",
            "custom_field_4220": account_no,
            "custom_field_4222": bank_code,
            "custom_field_4221": "",
            "custom_field_6362": "",
        }

        # payload = json.dumps(payload)

        url = f"{self.base_url}/borrower"
        response = requests.request("POST", url, headers=self.headers, json=payload)
        print(response.json())
        return response.json()

    def update_player(self, use_dommy_name=False, **kwargs):
        get_player_name = kwargs.get("account_name")
        phone = kwargs.get("phone")
        account_no = kwargs.get("account_num")
        bank_code = kwargs.get("bank_code")
        borrower_id = kwargs.get("borrower_id")

        if use_dommy_name is True:
            first_name = "USSD"
            middle_name = kwargs.get("id")
            last_name = "lotto"
            full_name = first_name + middle_name + last_name

        else:
            name = full_name_split(get_player_name)

            first_name = name.get("first_name")
            middle_name = name.get("middle_name")
            last_name = name.get("last_name")
            full_name = name.get("full_name")

        payload = {
            "borrower_country": "NG",
            "borrower_fullname": full_name,
            "borrower_firstname": first_name,
            "borrower_lastname": last_name,
            "custom_field_5854": middle_name,
            "borrower_business_name": "",
            "borrower_unique_number": "",
            "borrower_gender": "",
            "borrower_title": "",
            "borrower_mobile": phone,
            "custom_field_5037": "",
            "custom_field_4220": account_no,
            "custom_field_4222": bank_code,
            "custom_field_4221": "",
            "custom_field_6362": "",
            "borrower_id": borrower_id,
        }
        url = f"{self.base_url}/borrower"
        response = requests.request("PUT", url, headers=self.headers, data=payload)
        return response.json()

    def create_pool(self, **kwargs):
        # waiting draw 216234
        # awaiting payment 216235
        # lost 216233
        # open 1
        payload = json.dumps(
            {
                "loan_product_id": "140227",
                "borrower_id": kwargs.get("player_loandisk_id"),
                "loan_application_id": f"LLOT-{kwargs.get('lucky_num')}-{kwargs.get('id')}",
                "loan_disbursed_by_id": "91595",
                "loan_principal_amount": kwargs.get("stake_amount"),
                "loan_released_date": "26/05/2022",
                "loan_interest_method": "flat_rate",
                "loan_interest_type": "percentage",
                "loan_interest_period": "Year",
                "loan_interest": 0,
                "loan_duration_period": "Years",
                "loan_duration": 12,
                "loan_payment_scheme_id": "11",
                "loan_num_of_repayments": 1,
                "loan_decimal_places": "round_up_to_five",
                "loan_status_id": "216235" if kwargs.get("loan_status_id") is None else "1",
                "custom_field_4181": kwargs.get("bank_code"),
                "custom_field_4178": kwargs.get("account_num"),
                "custom_field_5261": "26/05/2022",
                "custom_field_5262": kwargs.get("unique_id"),
                "custom_field_4361": "",
                "loan_fee_id_2746": 0,
                "loan_fee_id_3915": 0,
                "loan_fee_id_4002": 0,
                "loan_fee_id_4003": 0,
                "loan_fee_id_4004": 0,
                "loan_fee_id_4005": 0,
                "loan_fee_id_4006": 0,
                "custom_field_5251": "",
                "custom_field_4385": "",
                "custom_field_6363": "",
                "custom_field_4219": kwargs.get("bank_name"),
                "custom_field_4221": "",
                "custom_field_7506": kwargs.get("band"),
            }
        )
        url = f"{self.base_url}/loan"
        response = requests.request("POST", url, headers=self.headers, data=payload)
        print(response.json())
        return response.json()

    def update_pool(self, **kwargs):
        payload = json.dumps(
            {
                "loan_product_id": "140227",
                "borrower_id": kwargs.get("player_loandisk_id"),
                "loan_application_id": f"LLOT-{kwargs.get('lucky_num')}-{kwargs.get('id')}",
                "loan_disbursed_by_id": "91595",
                "loan_principal_amount": kwargs.get("stake_amount"),
                "loan_released_date": "26/05/2022",
                "loan_interest_method": "flat_rate",
                "loan_interest_type": "percentage",
                "loan_interest_period": "Year",
                "loan_interest": 0,
                "loan_duration_period": "Years",
                "loan_duration": 12,
                "loan_payment_scheme_id": "11",
                "loan_num_of_repayments": 1,
                "loan_decimal_places": "round_up_to_five",
                "loan_status_id": kwargs.get("loan_status_id"),
                "custom_field_4181": kwargs.get("bank_code"),
                "custom_field_4178": kwargs.get("account_num"),
                "custom_field_5261": "26/05/2022",
                "custom_field_5262": kwargs.get("unique_id"),
                "custom_field_4361": "",
                "loan_fee_id_2746": 0,
                "loan_fee_id_3915": 0,
                "loan_fee_id_4002": 0,
                "loan_fee_id_4003": 0,
                "loan_fee_id_4004": 0,
                "loan_fee_id_4005": 0,
                "loan_fee_id_4006": 0,
                "custom_field_5251": "",
                "custom_field_4385": "",
                "custom_field_6363": "",
                "custom_field_4219": kwargs.get("bank_name"),
                "custom_field_4221": "",
                "custom_field_7506": kwargs.get("band"),
                "loan_id": kwargs.get("loan_id"),
            }
        )
        url = f"{self.base_url}/loan"
        response = requests.request("PUT", url, headers=self.headers, data=payload)
        return response.json()


def create_pool_on_loandisk(phone, lucky_num, lottery_instance_id, stake_amount, band, unique_id):
    from main.models import UserProfile

    loan_disk = LoandiskHelper()

    user_profile = UserProfile.objects.filter(phone_number=phone).last()

    if user_profile and user_profile.on_loandisk is True:
        data = {
            "player_loandisk_id": user_profile.loandisk_player_id,
            "lucky_num": lucky_num,
            "id": lottery_instance_id,
            "stake_amount": stake_amount,
            "bank_code": user_profile.bank_code,
            "account_num": user_profile.account_name,
            "bank_name": user_profile.bank_name,
            "band": band,
            "unique_id": unique_id,
        }
        _loandisk_res = loan_disk.create_pool(**data)
        return _loandisk_res

    elif user_profile and user_profile.on_loandisk is False:
        # create_borrower
        _create = {
            "phone": phone,
        }
        _create_loandisk_player = loan_disk.create_player(**_create)

        if isinstance(_create_loandisk_player, dict) and _create_loandisk_player["response"]["borrower_id"]:
            user_profile.loandisk_player_id = _create_loandisk_player["response"]["borrower_id"]
            user_profile.on_loandisk = True
            user_profile.save()

            # create_pool
            data = {
                "player_loandisk_id": user_profile.loandisk_player_id,
                "lucky_num": lucky_num,
                "id": lottery_instance_id,
                "stake_amount": stake_amount,
                "bank_code": user_profile.bank_code,
                "account_num": user_profile.account_name,
                "bank_name": user_profile.bank_name,
                "band": band,
                "unique_id": unique_id,
            }
            _loandisk_res = loan_disk.create_pool(**data)
            return _loandisk_res


def update_pool_on_loandisk(**kwargs):
    phone = kwargs.get("phone")
    lucky_num = kwargs.get("lucky_num")
    lottery_instance_id = kwargs.get("lottery_instance_id")
    stake_amount = kwargs.get("stake_amount")
    band = kwargs.get("band")
    unique_id = kwargs.get("unique_id")
    loan_status_id = kwargs.get("loan_status_id")
    exist_on_loandisk = kwargs.get("exist_on_loandisk")
    instance_number = int(kwargs.get("instance_number"))

    from main.models import LotteryModel, UserProfile

    loan_disk = LoandiskHelper()

    user_profile = UserProfile.objects.filter(phone_number=phone).last()

    if user_profile and user_profile.on_loandisk is True:
        # if exist_on_loandisk is False. create the pool on loandisk:
        if exist_on_loandisk is False or exist_on_loandisk == "False":
            lottery_model_queryset = LotteryModel.objects.filter(phone=phone).order_by("-id")[:instance_number]
            for i in lottery_model_queryset:
                data = {
                    "player_loandisk_id": user_profile.loandisk_player_id,
                    "lucky_num": lucky_num,
                    "id": lottery_instance_id,
                    "stake_amount": stake_amount,
                    "bank_code": user_profile.bank_code,
                    "account_num": user_profile.account_name,
                    "bank_name": user_profile.bank_name,
                    "band": band,
                    "unique_id": unique_id,
                    "loan_status_id": "216234",
                }
                _loandisk_res = loan_disk.create_pool(**data)
                if isinstance(_loandisk_res, dict) and _loandisk_res["response"]["loan_id"]:
                    i.loandisk_pool_id = _loandisk_res["response"]["loan_id"]
                    i.exist_on_loandisk = True
                    i.save()
        else:
            data = {
                "player_loandisk_id": user_profile.loandisk_player_id,
                "lucky_num": lucky_num,
                "id": lottery_instance_id,
                "stake_amount": stake_amount,
                "bank_code": user_profile.bank_code,
                "account_num": user_profile.account_name,
                "bank_name": user_profile.bank_name,
                "band": band,
                "unique_id": unique_id,
                "loan_status_id": loan_status_id,
            }
            loan_disk.update_pool(**data)

    elif user_profile and user_profile.on_loandisk is False:
        # create_borrower
        _create = {
            "phone": phone,
        }
        _create_loandisk_player = loan_disk.create_player(**_create)

        if isinstance(_create_loandisk_player, dict) and _create_loandisk_player["response"]["borrower_id"]:
            user_profile.loandisk_player_id = _create_loandisk_player["response"]["borrower_id"]
            user_profile.on_loandisk = True
            user_profile.save()

            data = {
                "player_loandisk_id": user_profile.loandisk_player_id,
                "lucky_num": lucky_num,
                "id": lottery_instance_id,
                "stake_amount": stake_amount,
                "bank_code": user_profile.bank_code,
                "account_num": user_profile.account_name,
                "bank_name": user_profile.bank_name,
                "band": band,
                "unique_id": unique_id,
                "loan_status_id": loan_status_id,
            }
            loan_disk.update_pool(**data)
