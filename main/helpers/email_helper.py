from dataclasses import dataclass

import requests
from django.conf import settings


@dataclass
class EmailHandler:
    email: str

    def otp_activation_email(self, otp):
        """
        Send OTP activation email to user.
        """
        html = """\
            <!DOCTYPE html
            PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
            <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
            xmlns:o="urn:schemas-microsoft-com:office:office">
            <head>
            <!--[if gte mso 9]>
                        <xml>
                            <o:OfficeDocumentSettings>
                            <o:AllowPNG/>
                            <o:PixelsPerInch>96</o:PixelsPerInch>
                            </o:OfficeDocumentSettings>
                        </xml>
                        <![endif]-->
            <meta http-equiv="Content-type" content="text/html; charset=utf-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="format-detection" content="date=no" />
            <meta name="format-detection" content="address=no" />
            <meta name="format-detection" content="telephone=no" />
            <meta name="x-apple-disable-message-reformatting" />
            <!--[if !mso]><!-->
            <link href="https://fonts.googleapis.com/css?family=Muli:400,400i,700,700i" rel="stylesheet" />
            <link rel="preconnect" href="https://fonts.googleapis.com">
            <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
            <link href="https://fonts.googleapis.com/css2?family=Nunito&display=swap" rel="stylesheet">
            <link href="http://fonts.cdnfonts.com/css/clash-display" rel="stylesheet">
            <link href="http://fonts.cdnfonts.com/css/montserrat" rel="stylesheet">
            <link href="http://fonts.cdnfonts.com/css/raleway-5" rel="stylesheet">
            <!--<![endif]-->
            <title>Whysecash</title>
            <!--[if gte mso 9]>
                        <style type="text/css" media="all">
                            sup { font-size: 100% !important; }
                        </style>
                        <![endif]-->
            <style type="text/css" media="screen">
                /* Linked Styles */
                body {
                padding: 0 !important;
                margin: 0 !important;
                display: block !important;
                min-width: 100% !important;
                width: 100% !important;
                background: #000000;
                -webkit-text-size-adjust: none
                }
                a {
                color: #66c7ff;
                text-decoration: none
                }
                p {
                padding: 0 !important;
                margin: 0 !important
                }
                img {
                -ms-interpolation-mode: bicubic;
                /* Allow smoother rendering of resized image in Internet Explorer */
                }
                .mcnPreviewText {
                display: none !important;
                }
                .cke_editable,
                .cke_editable a,
                .cke_editable span,
                .cke_editable a span {
                color: #000001 !important;
                }
                /* Mobile styles */
                @media only screen and (max-device-width: 480px),
                only screen and (max-width: 480px) {
                .mobile-shell {
                    width: 100% !important;
                    min-width: 100% !important;
                }
                .text-header,
                .m-center {
                    text-align: center !important;
                }
                .center {
                    margin: 0 auto !important;
                }
                .container {
                    padding: 20px 10px !important
                }
                .td {
                    width: 100% !important;
                    min-width: 100% !important;
                }
                .m-br-15 {
                    height: 15px !important;
                }
                .p30-15 {
                    padding: 30px 15px !important;
                }
                .m-block {
                    display: block !important;
                }
                .fluid-img img {
                    width: 100% !important;
                    max-width: 100% !important;
                    height: auto !important;
                }
                .column,
                .column-top,
                .column-empty,
                .column-empty2,
                .column-dir-top {
                    float: left !important;
                    width: 100% !important;
                    display: block !important;
                }
                .column-empty {
                    padding-bottom: 10px !important;
                }
                .column-empty2 {
                    padding-bottom: 30px !important;
                }
                .content-spacing {
                    width: 15px !important;
                }
                }
            </style>
            </head>
            <body class="body"
            style="padding:0 !important; margin:0 !important; display:block !important; min-width:100% !important; width:100% !important;  -webkit-text-size-adjust:none;">
            <!--|IF:MC_PREVIEW_TEXT|-->
            <!--[if !gte mso 9]><!-->
            <!--<![endif]-->
            <!--|END:IF|-->
            <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#000000">
                <tr>
                <td align="center" valign="top">
                    <span class="m-hide" style="font-size:42px; font-family: Clash Display; font-weight: 900; color: #ffffff;">
                    Whisper Wyse
                    </span> <br>
                    <table width="750" border="0" cellspacing="0" cellpadding="0" class="mobile-shell" bgcolor="#F0F0F0"
                    style="padding: 0px 5px;">
                    <tr>
                        <td class="td container"
                        style="width:650px; min-width:650px; font-size:0pt; line-height:0pt; margin:0; font-weight:normal; padding:5px 0px;">
                        <!-- HEADER -->
                        <div mc:repeatable="Select" mc:variant="Intro">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td style="padding-bottom: 10px;">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                    <td class="tbrr p30-15">
                                        <img
                                        src="https://res.cloudinary.com/djfz912mh/image/upload/v1663327559/Frame_7_1_giux49.png" />
                                    </td>
                                    </tr>
                                </table>
                                </td>
                            </tr>
                            </table>
                        </div>
                        <!-- END HEADER -->
                        <!-- Intro -->
                        <div mc:repeatable="Select" mc:variant="Intro" style="text-align:center; z-index: 999;">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0"
                            style="margin-top: -100px; position: relative; z-index: 999;">
                            <tr>
                                <td style="padding-bottom: 10px;">
                                <table width="90%" border="0" cellspacing="0" cellpadding="0" style="margin-left: 40px;">
                                    <tr>
                                    <td class="tbrr p30-15"
                                        style="padding: 30px 30px 20px 30px; border-radius:0px 0px 0px 0px; margin-top: -20px;"
                                        bgcolor="#ffffff">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td class="h1 pb25"
                                            style="color:#000000; font-family:'Muli', Arial,sans-serif; font-size:14px; line-height:35px; text-align:center; padding-bottom:25px;">
                                            <div mc:edit="text_2">
                                                <img
                                                src="https://res.cloudinary.com/djfz912mh/image/upload/v1663583500/Group_8420_1_zegr1p.png"
                                                alt="img">
                                            </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-left pb25"
                                            style="color:#272727; font-family:'Nunito', Arial,sans-serif; font-size:19px; line-height:30px; text-align:left; padding-bottom:5px;">
                                            <div mc:edit="text_3">
                                                <span class="m-hide"
                                                style="font-size:14px; font-family: Raleway; font-weight: 500;">
                                                Kindly copy and use the OTP Code displayed below to complete
                                                your account creation.
                                                </span>
                                                <br>
                                                <span class="m-hide"
                                                style="font-size:30px; font-family: Raleway; font-weight: 800;color: #4C1961;">
                                                $(otp)
                                                </span>
                                                <br><br>
                                            </div>
                                            </td>
                                        </tr>
                                        <br>
                                        <br>
                                        <tr bgcolor="#ffffff" style="margin-left: 10px;">
                                            <td class="text-center pb25" style=" font-family:'Nunito', Arial,sans-serif; font-size:14px; line-height:25px; text-align:left; padding-bottom:25px;
                                                        padding-left: 0px;">
                                            <div mc:edit="text_3">
                                                Best Regards, <br>
                                                WhisperWyse Support Team.
                                            </div>
                                            </td>
                                        </tr>
                                        </table>
                                    </td>
                                    </tr>
                                    <tr bgcolor="#ffffff">
                                    <td class="text-center pb25" style="color:#000000; font-family:'Nunito', Arial,sans-serif; font-size:14px; text-align:left; padding-bottom:25px;
                                                    padding-left: 20px;">
                                        <div mc:edit="text_3">Need help ? contact our customer
                                        services
                                        </div>
                                    </td>
                                    </tr>
                                    <tr bgcolor="#ffffff" style="margin-left: 20px;">
                                    <td class="text-center pb25" style="color:#4C1961; font-family:'Nunito', Arial,sans-serif; font-size:14px; line-height:25px; text-align:left; padding-bottom:25px;
                                            padding-left: 20px;">
                                        <div mc:edit="text_3">
                                        <EMAIL> <br />
                                        090 4000 1444
                                        </div>
                                    </td>
                                    </tr>
                                </table>
                                </td>
                            </tr>
                            </table>
                        </div>
                        <!-- END Intro -->
                        <!-- Footer -->
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                            <td class="p30-15 bbrr" style="padding: 50px 30px; border-radius:0px 0px 26px 26px;"
                                bgcolor="#f0f0f0">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                <tr>
                                    <td class="text-footer1 pb10"
                                    style="color:#4C1961; font-family:'Nunito', Arial,sans-serif; font-size:20px; line-height:20px; text-align:center; padding-bottom:30px;">
                                    <div mc:edit="text_36">Follow Us:
                                    </div>
                                    </td>
                                </tr>
                                <td align="center" style="padding-bottom: 30px;">
                                    <table border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td class="img" width="55" style="font-size:0pt; line-height:0pt; text-align:left;">
                                        <a href="#" target="_blank"><img
                                            src="https://res.cloudinary.com/djfz912mh/image/upload/v1663688843/Group_344_lqpgv8.png "
                                            width="38" height="38" mc:edit="image_14" style="max-width:38px;" border="0"
                                            alt="" /></a>
                                        </td>
                                        <td class="img" width="55" style="font-size:0pt; line-height:0pt; text-align:left;">
                                        <a href="#" target="_blank"><img
                                            src="https://res.cloudinary.com/djfz912mh/image/upload/v1663688843/Group_343_qjrxhs.png"
                                            width="38" height="38" mc:edit="image_15" style="max-width:38px;" border="0"
                                            alt="" /></a>
                                        </td>
                                        <td class="img" width="55" style="font-size:0pt; line-height:0pt; text-align:left;">
                                        <a href="#" target="_blank"><img
                                            src="https://res.cloudinary.com/djfz912mh/image/upload/v1663688842/Group_342_hyavoi.png"
                                            width="38" height="38" mc:edit="image_16" style="max-width:38px;" border="0"
                                            alt="" /></a>
                                        </td>
                                    </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                            <td class="text-footer2"
                                style="color:#4C1961; font-family:'Muli', Arial,sans-serif; font-size:12px; line-height:26px; text-align:center;">
                                <div mc:edit="text_37">© 2022, Whispa Konnect Limted .
                                <br />Terms | Privacy
                                </div>
                            </td>
                            </tr>
                        </table>
                        </td>
                    </tr>
                    <tr>
                        <td class="img" style="font-size:0pt; line-height:0pt; text-align:left;">
                        <div mc:edit="text_39">
                            <!--[if !mso]><!-->
                            |LIST:DESCRIPTION|
                            |LIST:ADDRESS|
                            |REWARDS_TEXT|
                            <!--<![endif]-->
                        </div>
                        </td>
                    </tr>
                    </table>
                    <!-- END Footer -->
                </td>
                </tr>
            </table>
            </td>
            </tr>
            </table>
            </body>
            </html>
        """

        html = html.replace("$(otp)", otp)
        response = requests.post(
            "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
            auth=("api", settings.MAILGUN_API_KEY),
            data={
                "from": "Wyse Cash <<EMAIL>>",
                "to": f"{self.email}",
                "subject": "Registration otp",
                "html": html,
            },
        )
        print(response.text)
        return response.text

    def forgot_password_email(self, token):
        html = """\
            <!DOCTYPE html
                PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
                <html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
                xmlns:o="urn:schemas-microsoft-com:office:office">
                <head>
                <!--[if gte mso 9]>
                            <xml>
                                <o:OfficeDocumentSettings>
                                <o:AllowPNG/>
                                <o:PixelsPerInch>96</o:PixelsPerInch>
                                </o:OfficeDocumentSettings>
                            </xml>
                            <![endif]-->
                <meta http-equiv="Content-type" content="text/html; charset=utf-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
                <meta http-equiv="X-UA-Compatible" content="IE=edge" />
                <meta name="format-detection" content="date=no" />
                <meta name="format-detection" content="address=no" />
                <meta name="format-detection" content="telephone=no" />
                <meta name="x-apple-disable-message-reformatting" />
                <!--[if !mso]><!-->
                <link href="https://fonts.googleapis.com/css?family=Muli:400,400i,700,700i" rel="stylesheet" />
                <link rel="preconnect" href="https://fonts.googleapis.com">
                <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
                <link href="https://fonts.googleapis.com/css2?family=Nunito&display=swap" rel="stylesheet">
                <link href="http://fonts.cdnfonts.com/css/clash-display" rel="stylesheet">
                <link href="http://fonts.cdnfonts.com/css/montserrat" rel="stylesheet">
                <link href="http://fonts.cdnfonts.com/css/raleway-5" rel="stylesheet">
                <!--<![endif]-->
                <title>|Liberty Email Template|</title>
                <!--[if gte mso 9]>
                            <style type="text/css" media="all">
                                sup { font-size: 100% !important; }
                            </style>
                            <![endif]-->
                <style type="text/css" media="screen">
                    /* Linked Styles */
                    body {
                    padding: 0 !important;
                    margin: 0 !important;
                    display: block !important;
                    min-width: 100% !important;
                    width: 100% !important;
                    background: #000000;
                    -webkit-text-size-adjust: none
                    }
                    a {
                    color: #66c7ff;
                    text-decoration: none
                    }
                    p {
                    padding: 0 !important;
                    margin: 0 !important
                    }
                    img {
                    -ms-interpolation-mode: bicubic;
                    /* Allow smoother rendering of resized image in Internet Explorer */
                    }
                    .mcnPreviewText {
                    display: none !important;
                    }
                    .cke_editable,
                    .cke_editable a,
                    .cke_editable span,
                    .cke_editable a span {
                    color: #000001 !important;
                    }
                    /* Mobile styles */
                    @media only screen and (max-device-width: 480px),
                    only screen and (max-width: 480px) {
                    .mobile-shell {
                        width: 100% !important;
                        min-width: 100% !important;
                    }
                    .text-header,
                    .m-center {
                        text-align: center !important;
                    }
                    .center {
                        margin: 0 auto !important;
                    }
                    .container {
                        padding: 20px 10px !important
                    }
                    .td {
                        width: 100% !important;
                        min-width: 100% !important;
                    }
                    .m-br-15 {
                        height: 15px !important;
                    }
                    .p30-15 {
                        padding: 30px 15px !important;
                    }
                    .m-block {
                        display: block !important;
                    }
                    .fluid-img img {
                        width: 100% !important;
                        max-width: 100% !important;
                        height: auto !important;
                    }
                    .column,
                    .column-top,
                    .column-empty,
                    .column-empty2,
                    .column-dir-top {
                        float: left !important;
                        width: 100% !important;
                        display: block !important;
                    }
                    .column-empty {
                        padding-bottom: 10px !important;
                    }
                    .column-empty2 {
                        padding-bottom: 30px !important;
                    }
                    .content-spacing {
                        width: 15px !important;
                    }
                    }
                </style>
                </head>
                <body class="body"
                style="padding:0 !important; margin:0 !important; display:block !important; min-width:100% !important; width:100% !important;  -webkit-text-size-adjust:none;">
                <!--|IF:MC_PREVIEW_TEXT|-->
                <!--[if !gte mso 9]><!-->
                <!--<![endif]-->
                <!--|END:IF|-->
                <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#000000">
                    <tr>
                    <td align="center" valign="top">
                        <span class="m-hide" style="font-size:42px; font-family: Clash Display; font-weight: 900; color: #ffffff;">
                        Whisper Wyse
                        </span> <br>
                        <table width="750" border="0" cellspacing="0" cellpadding="0" class="mobile-shell" bgcolor="#F0F0F0"
                        style="padding: 0px 5px;">
                        <tr>
                            <td class="td container"
                            style="width:650px; min-width:650px; font-size:0pt; line-height:0pt; margin:0; font-weight:normal; padding:5px 0px;">
                            <!-- HEADER -->
                            <div mc:repeatable="Select" mc:variant="Intro">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                    <td style="padding-bottom: 10px;">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                        <td class="tbrr p30-15">
                                            <img
                                            src="https://res.cloudinary.com/djfz912mh/image/upload/v1663327559/Frame_7_1_giux49.png" />
                                        </td>
                                        </tr>
                                    </table>
                                    </td>
                                </tr>
                                </table>
                            </div>
                            <!-- END HEADER -->
                            <!-- Intro -->
                            <div mc:repeatable="Select" mc:variant="Intro" style="text-align:center; z-index: 999;">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                                style="margin-top: -100px; position: relative; z-index: 999;">
                                <tr>
                                    <td style="padding-bottom: 10px;">
                                    <table width="90%" border="0" cellspacing="0" cellpadding="0" style="margin-left: 40px;">
                                        <tr>
                                        <td class="tbrr p30-15"
                                            style="padding: 30px 30px 20px 30px; border-radius:0px 0px 0px 0px; margin-top: -20px;"
                                            bgcolor="#ffffff">
                                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                            <tr>
                                                <td class="h1 pb25"
                                                style="color:#000000; font-family:'Muli', Arial,sans-serif; font-size:14px; line-height:35px; text-align:center; padding-bottom:25px;">
                                                <div mc:edit="text_2">
                                                    <img
                                                    src="https://res.cloudinary.com/djfz912mh/image/upload/v1663583500/Group_8424_bixql1.png"
                                                    alt="img">
                                                </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="text-left pb25"
                                                style="color:#272727; font-family:'Nunito', Arial,sans-serif; font-size:19px; line-height:30px; text-align:left; padding-bottom:5px;">
                                                <div mc:edit="text_3">
                                                    <span class="m-hide"
                                                    style="font-size:20px; font-family: Clash Display; font-weight: 500;">

                                                    </span> <br>
                                                    <span class="m-hide"
                                                    style="font-size:14px; font-family: Raleway; font-weight: 500;">
                                                    Click the link below to reset your Whisper Wyse account password
                                                    </span>
                                                    <br>
                                                    <a  href="https://whisper-wyze-games.vercel.app/user/reset-password2?$(token)"  style="background: linear-gradient(90deg, #4C1961 0%, #9602AD 242.97%);
                                                    border-radius: 6px; padding: 10px 20px; border: none; color: #ffffff;  font-family: Montserrat; font-weight: 500;">
                                                    Reset Password</a> <br><br>
                                                    <span class="m-hide"
                                                    style="font-size:14px; font-family: Raleway; font-weight: 400;">
                                                    If you did not request for this password reset kindly ignore
                                                    </span>
                                                </div>
                                                </td>
                                            </tr>
                                            <br>
                                            <br>
                                            </table>
                                        </td>
                                        </tr>
                                        <tr bgcolor="#ffffff">
                                        <td class="text-center pb25" style="color:#000000; font-family:'Nunito', Arial,sans-serif; font-size:14px; text-align:left; padding-bottom:25px;
                                                        padding-left: 20px;">
                                            <div mc:edit="text_3">Need help ? contact our customer
                                            services
                                            </div>
                                        </td>
                                        </tr>
                                        <tr bgcolor="#ffffff" style="margin-left: 20px;">
                                        <td class="text-center pb25" style="color:#4C1961; font-family:'Nunito', Arial,sans-serif; font-size:14px; line-height:25px; text-align:left; padding-bottom:25px;
                                                padding-left: 20px;">
                                            <div mc:edit="text_3">
                                            <EMAIL> <br />
                                            090 4000 1444
                                            </div>
                                        </td>
                                        </tr>
                                    </table>
                                    </td>
                                </tr>
                                </table>
                            </div>
                            <!-- END Intro -->
                            <!-- Footer -->
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tr>
                                <td class="p30-15 bbrr" style="padding: 50px 30px; border-radius:0px 0px 26px 26px;"
                                    bgcolor="#f0f0f0">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                    <tr>
                                        <td class="text-footer1 pb10"
                                        style="color:#4C1961; font-family:'Nunito', Arial,sans-serif; font-size:20px; line-height:20px; text-align:center; padding-bottom:30px;">
                                        <div mc:edit="text_36">Follow Us:
                                        </div>
                                        </td>
                                    </tr>
                                    <td align="center" style="padding-bottom: 30px;">
                                        <table border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td class="img" width="55" style="font-size:0pt; line-height:0pt; text-align:left;">
                                            <a href="#" target="_blank"><img
                                                src="https://res.cloudinary.com/eddiewurld/image/upload/v1650370220/Group_344_aaedtf.png"
                                                width="38" height="38" mc:edit="image_14" style="max-width:38px;" border="0"
                                                alt="" /></a>
                                            </td>
                                            <td class="img" width="55" style="font-size:0pt; line-height:0pt; text-align:left;">
                                            <a href="#" target="_blank"><img
                                                src="https://res.cloudinary.com/eddiewurld/image/upload/v1650370220/Group_342_gxzkt9.png"
                                                width="38" height="38" mc:edit="image_15" style="max-width:38px;" border="0"
                                                alt="" /></a>
                                            </td>
                                            <td class="img" width="55" style="font-size:0pt; line-height:0pt; text-align:left;">
                                            <a href="#" target="_blank"><img
                                                src="https://res.cloudinary.com/eddiewurld/image/upload/v1650370220/Group_343_llysyo.png"
                                                width="38" height="38" mc:edit="image_16" style="max-width:38px;" border="0"
                                                alt="" /></a>
                                            </td>
                                        </tr>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                <td class="text-footer2"
                                    style="color:#4C1961; font-family:'Muli', Arial,sans-serif; font-size:12px; line-height:26px; text-align:center;">
                                    <div mc:edit="text_37">© 2022, Whispa Konnect Limted .
                                    <br />Terms | Privacy
                                    </div>
                                </td>
                                </tr>
                            </table>
                            </td>
                        </tr>
                        <tr>
                            <td class="img" style="font-size:0pt; line-height:0pt; text-align:left;">
                            <div mc:edit="text_39">
                                <!--[if !mso]><!-->
                                |LIST:DESCRIPTION|
                                |LIST:ADDRESS|
                                |REWARDS_TEXT|
                                <!--<![endif]-->
                            </div>
                            </td>
                        </tr>
                        </table>
                        <!-- END Footer -->
                    </td>
                    </tr>
                </table>
                </td>
                </tr>
                </table>
                </body>
                </html>
            """
        html = html.replace("$(token)", token)

        if settings.DEBUG is False or settings.DEBUG == 0:
            html = html.replace(
                'href="https://whisper-wyze-games.vercel.app',
                'href="https://www.wisewinn.com',
            )

        response = requests.post(
            "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
            auth=("api", settings.MAILGUN_API_KEY),
            data={
                "from": "Whysecash <<EMAIL>>",
                "to": f"{self.email}",
                "subject": "Reset password",
                "html": html,
            },
        )

        print(response.text)
        return response.text

    def salary4life_rtp_check_email(self, rtp):
        html = f"""RTP:{rtp}

            """

        response = requests.post(
            "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
            auth=("api", settings.MAILGUN_API_KEY),
            data={
                "from": "Whysecash <<EMAIL>>",
                "to": f"{self.email}",
                "subject": "BAD SALARY FOR LIFE RTP",
                "html": html,
            },
        )

        print(response.text)
        return response.text


# if __name__ == "__main__":
#     EmailHandler("").otp_activation_email("Joseph", "123456")
