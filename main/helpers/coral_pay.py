import json
import time
from hashlib import sha256
from random import randint

import requests
from django.conf import settings

from main.helpers.redis_storage import RedisStorage


class CoralpayHelper:
    def __init__(self):
        self.url = "https://testdev.coralpay.com/VergeTest/api/v1"
        # self.headers = {'Content-Type': 'application/json', "Authorization":f'Bearer {settings.CORALPAY_USSD_WEB_TOKEN}'}

    def coral_pay_login(self):
        url = f"{self.url}/authentication"
        headers = {"Content-Type": "application/json"}

        payload = {"Username": settings.CORAL_PAY_USERNAME, "Password": settings.CORAL_PAY_PASSWORD}
        response = requests.request("POST", url, headers=headers, json=payload)
        res = response.json()

        # store token in redis
        redis_storage = RedisStorage("coral_pay_token")
        redis_storage.set_data(res["Token"])

        # store key in redis
        redis_storage = RedisStorage("coral_pay_key")
        redis_storage.set_data(res["Key"])

        return res["Token"], res["Key"]

    def random_with_N_digits(self, n=25):
        range_start = 10 ** (n - 1)
        range_end = (10**n) - 1
        code = randint(range_start, range_end)
        return code

    def initiate_payment(self):
        class SetEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, set):
                    return list(obj)
                return json.JSONEncoder.default(self, obj)

        url = f"{self.url}/Invokepayment"

        # login
        token = self.coral_pay_login()

        # get auth token from redis
        redis_storage = RedisStorage("coral_pay_token")
        token = redis_storage.get_data()
        if token is None:
            token = (self.coral_pay_login())["Token"]

        elif token is not None:
            token = token.decode("utf-8")

        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {token}"}
        trace_id = self.random_with_N_digits()

        time_stamp = f"{int(time.time())}"

        # get key from redis
        redis_storage = RedisStorage("coral_pay_key")
        coral_pay_redis_key = redis_storage.get_data()

        if coral_pay_redis_key is None:
            coral_pay_redis_key = (self.coral_pay_login())["Key"]

        elif coral_pay_redis_key is not None:
            coral_pay_redis_key = coral_pay_redis_key.decode("utf-8")

        hash_string = f"{settings.CORAL_PAY_MERCHANT_ID}{trace_id}{time_stamp}{coral_pay_redis_key}"

        payload = {
            "RequestHeader": {
                "MerchantId": settings.CORAL_PAY_MERCHANT_ID,
                "TimeStamp": f"{int(time.time())}",
                "Signature": sha256(hash_string.encode("utf-8")).hexdigest(),
            },
            "Customer": {"Email": "<EMAIL>", "Name": "Baba Voss", "Phone": "002200302092"},
            "Customization": {"LogoUrl": "http://sampleurl.com", "Title": "Watermarks Limited", "Description": "Service Payment"},
            "MetaData": {"Data1": "sample string 1", "Data2": "sample string 2", "Data3": "sample string 3"},
            "TraceId": trace_id,
            "Amount": 2000.00,
            "Currency": "NGN",
            "FeeBearer": "M",
            "ReturnUrl": "http://samplereturnurl.com/status",
        }

        response = requests.request("POST", url, headers=headers, json=payload)

        if response.status_code == 401:  # token expired
            self.coral_pay_login()
            return CoralpayHelper().initiate_payment()

        return response.json()

    def transaction_status(self, **kwargs):
        url = f"{self.url}/statusquery"
        response = requests.request("POST", url, headers=self.headers, data=json.dumps(kwargs))
        return response.json()

    def coral_bank():
        url = f"https://testdev.coralpay.com/cgateproxy/api/v2/getbanks"  # noqa
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {settings.CORALPAY_USSD_WEB_TOKEN}"}
        response = requests.request("GET", url, headers=headers)
        return response.json()


class CoralpayUSSDWebHelper:
    def __init__(self):
        self.url = "https://testdev.coralpay.com/cgateproxy/api/v2"
        # self.headers = {'Content-Type': 'application/json', "Authorization":f'Bearer {settings.CORALPAY_USSD_WEB_TOKEN}'}

    def coral_pay_login(self, **kwargs):
        url = f"{self.url}/authentication"
        headers = {
            "Content-Type": "application/json",
        }
        response = requests.request("POST", url, headers=headers, data=json.dumps(kwargs))
        return response.json()

    def invoke_reference(self, **kwargs):
        class SetEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, set):
                    return list(obj)
                return json.JSONEncoder.default(self, obj)

        url = f"{self.url}/invokereference"
        response = requests.request("POST", url, headers=self.headers, data=json.dumps(kwargs, cls=SetEncoder))
        return response.json()

    def transaction_status(self, **kwargs):
        url = f"{self.url}/statusquery"
        response = requests.request("POST", url, headers=self.headers, data=json.dumps(kwargs))
        return response.json()

    def coral_bank():
        url = "https://testdev.coralpay.com/cgateproxy/api/v2/getbanks"
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {settings.CORALPAY_USSD_WEB_TOKEN}"}
        response = requests.request("GET", url, headers=headers)
        return response.json()


# class CoralPayUSSDWebInvokeReference(APIView):
#     @swagger_auto_schema(request_body=CoralUSSDWebInvokeReferenceSerializer)
#     def post(self, request, format=None):
#         serializer = CoralUSSDWebInvokeReferenceSerializer(data=request.data)
#         if serializer.is_valid():
#             def random_with_N_digits(n):
#                     range_start = 10**(n-1)
#                     range_end = (10**n)-1
#                     code = randint(range_start, range_end)
#                     return code
#                     # exist = CoralpayUSSDTraceId.objects.filter(trace_id=code).exists()
#                     # if exist is True:
#                     #     random_with_N_digits(30)
#                     # else:
#                     #     return code

#             MerchantId = settings.CORALPAY_USSD_WEB_MERCHANT_ID
#             TerminalId = settings.CORALPAY_USSD_WEB_TERMINAL_ID
#             SubMerchantName = serializer.validated_data.get('SubMerchantName')
#             Amount = serializer.validated_data.get('Amount')
#             TraceId = random_with_N_digits(20)
#             BankCode = serializer.validated_data.get('BankCode')
#             KEY = settings.CORALPAY_USSD_WEB_KEY
#             TimeStamp = f'{int(time.time())}'
#             hash_string = MerchantId+TerminalId+TimeStamp+KEY
#             Signature = sha256(hash_string.encode('utf-8')).hexdigest()
#             data = CoralpayUSSDWebHelper().invoke_reference(MerchantId=MerchantId, TerminalId=TerminalId, SubMerchantName=SubMerchantName, Amount=Amount, TraceId=TraceId, BankCode=BankCode, TimeStamp=TimeStamp, Signature=Signature)
#             CoralpayUSSDWebReference.objects.create(subMerchantName=SubMerchantName, amount=Amount, traceId=TraceId, BankCode=BankCode, raw_data=data)
#             return Response(data=data, status=status.HTTP_200_OK)
#         else:
#             return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
