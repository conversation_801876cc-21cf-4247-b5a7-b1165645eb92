from time import sleep

import requests
from django.conf import settings


def watupay_format_number_from_back_add_234(phone) -> str:
    formatted_num = phone[-11:]
    # print(formatted_num)
    # # if formatted_num[0] == "0":
    # print(formatted_num)
    #     return None
    # else:
    return "234" + formatted_num


def generate_ussd_collection_code(bank_code, amount):
    sleep(1)
    url = "https://api.watu.global/v1/group/channel/ussd/NG"

    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": f"Bearer {settings.WATUPAY_PUBLIC_KEY}",
        # 'Authorization': 'Bearer WTP-L-PK-eb3e8ee71790400ebf148467f0a876e3'
    }

    # print("bank_code", bank_code, "\n\n\n\n")
    # print("amount", amount, "\n\n\n")

    response = requests.request("GET", url, headers=headers)
    # print("watupay response", response.text, "\n\n\n")
    resp = response.json().get("data", None)
    if resp is not None:
        ("watu pay response is not none")
        ussd_string = None
        for i in resp:
            if i["bank"]["id"] == bank_code:
                ussd_string = i["straight_string"]
                break

        if ussd_string is not None:
            return f"{ussd_string.replace('+', '*')}{int(amount)}#"
        else:
            return None
    else:
        return None


# ussd_string = generate_ussd_collection_code("058", "500")

# print(ussd_string)


def verify_watupay_ussd_transaction(transaction_reference):
    # url = "https://api.watu.global/v1/transaction/verify"
    url = "https://api.watu.global/v1/payment/verify-transaction-id"

    headers = {
        "Accept": "application/json",
        # 'Authorization': f'Bearer {settings.WATUPAY_PUBLIC_KEY}',
        "Authorization": "Bearer WTP-L-PK-eb3e8ee71790400ebf148467f0a876e3",
        "Content-Type": "application/json",
    }

    payload = {"transaction_id": transaction_reference}

    response = requests.request("POST", url, headers=headers, json=payload)
    res = response.json()

    if res.get("has_error") is False and res.get("status_code") == 200:
        return True
    else:
        return False


# verify_watupay_ussd_transaction("9cafafb406d84df684afa3abe62d8ca1s")
