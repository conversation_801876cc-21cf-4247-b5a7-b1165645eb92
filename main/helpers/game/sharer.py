# from settings import *
from main.helpers.game.settings import LOWER_MULTIPLIER, UPPER_MULTIPLIER

min_resolution = 2
max_resolution = 2


def clean_shares(shares, total_amount, band):
    min_share = band * LOWER_MULTIPLIER
    max_share = (
        band * 80 if band == 100 else band * UPPER_MULTIPLIER
    )  # ALSO VARY BTW 150 AND 200 WITH HIGHER BAND OCCURING MORE FREQUENTLY LIMIT THE 100NAIRA BAND TO 90X TO REMOVE LIKELYHOOD OF ORDINARY WINNERS GETTING EQUAL OR ABOVE THE BAND MAX VALUE

    sum_rand_shares = sum([int(sharer.number) for sharer in shares])

    while True:
        sum_rand_shares = sum([int(sharer.number) for sharer in shares])

        for player in shares:
            earning = round((int(player.number) / sum_rand_shares) * total_amount)

            if earning < min_share:
                shares = shares[:-min_resolution]
                sum_rand_shares = sum_rand_shares

                break

            if player.number == 10 and earning <= max_share:
                shares = shares[:-max_resolution]
                sum_rand_shares = sum_rand_shares

                break

        else:
            print("ENDING ::::")
            print("END SHARES : ", len(shares))
            print("\n\n")
            break

    return shares
