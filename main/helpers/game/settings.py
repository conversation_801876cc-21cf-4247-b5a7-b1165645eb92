import math

GAME_THRESHOLD = 5000000
WINNING_PERCENTAGE = 1  # noqa  #  0.15 results in about 2.3% wins while 0.3% results in about 4% wins whis value is to be fluctuated to alter the number of winners in every batch. Also this value has an effect on the worst case scenario take home of a winner, putting the value at about 4.9x of band's stake (BASED ON CHECKS AS AT 25-06-22 (0.15->0.3) APPEAR TO WORK FOR BEST RETURNS TO PLAYERS AT CLOSEST TO 4.9X OF STAKE

RTO = 0.1  # THIS VALUE FLUCTUATES THE NUMBER OF WINNERS ONLY WITHOUT ALTERING TAKE-HOME *****IF RTO IS AS HIGH AS 0.7 THEN WINNING PERCENTAGE MUST BE LOW TO 0.15 FORMING AN INVERSE RELATIONSHIP.
RTP = 1 - RTO
PLAYS = 5000  # NUMBER OF PLAYERS IN A SESSION

LOWER_MULTIPLIER = 12  # POSSIBLY VARY BTW 5 AND 8 WITH MORE OF THE HIGHER BANDS OCCURING MOST OF THE TIME
UPPER_MULTIPLIER = 150  # ALSO VARY BTW 150 AND 200 WITH HIGHER BAND OCCURING MORE FREQUENTLY

GAME_BANDS = {
    10: {
        "cost": 100,
        "jackpot": 0.005,
        "playability": 0.35,
        "no_of_plays": 0,
        "band": 10,
        "players": [],
        "band_random_sum": 0,
        "winner_selection": 0,
    },
    50: {
        "cost": 200,
        "jackpot": 0.005,
        "playability": 0.33,
        "no_of_plays": 0,
        "band": 50,
        "players": [],
        "band_random_sum": 0,
        "winner_selection": 0,
    },
    250: {
        "cost": 500,
        "jackpot": 0.005,
        "playability": 0.20,
        "no_of_plays": 0,
        "band": 250,
        "players": [],
        "band_random_sum": 0,
        "winner_selection": 0,
    },
    500: {
        "cost": 1000,
        "jackpot": 0.005,
        "playability": 0.09,
        "no_of_plays": 0,
        "band": 500,
        "players": [],
        "band_random_sum": 0,
        "winner_selection": 0,
    },
    1000: {
        "cost": 2000,
        "jackpot": 0.0025,
        "playability": 0.09,
        "no_of_plays": 0,
        "band": 1000,
        "players": [],
        "band_random_sum": 0,
        "winner_selection": 0,
    },
}

raw_pool = [[GAME_BANDS[key]["cost"]] * math.ceil(GAME_BANDS[key]["playability"] * 1000) for key in GAME_BANDS.keys()]

GAME_POOL = [*raw_pool[0], *raw_pool[1], *raw_pool[2], *raw_pool[3]]


if __name__ == "__main__":
    possible_plays = GAME_BANDS.keys()

    raw_pool = [[GAME_BANDS[key]["cost"]] * math.ceil(GAME_BANDS[key]["playability"] * 100) for key in GAME_BANDS.keys()]

    pool = [*raw_pool[0], *raw_pool[1], *raw_pool[2], *raw_pool[3]]
    print(pool)

"""
WINRATE - WINPCNT - RTO

2.313 -    0.15   - 0.3
4.610 -    0.30   - 0.3
7.696 -    0.50   - 0.3

"""


# import math

# from main.models import ConstantVariable

# # GAME_THRESHOLD = 5000000
# # WINNING_PERCENTAGE = 0.3 #  0.15 results in about 2.3% wins while 0.3% results in about 4% wins whis value is to be fluctuated to alter the number of winners in every batch. Also this value has an effect on the worst case scenario take home of a winner, putting the value at about 4.9x of band's stake (BASED ON CHECKS AS AT 25-06-22 (0.15->0.3) APPEAR TO WORK FOR BEST RETURNS TO PLAYERS AT CLOSEST TO 4.9X OF STAKE
# # RTO = 0.1 # THIS VALUE FLUCTUATES THE NUMBER OF WINNERS ONLY WITHOUT ALTERING TAKE-HOME *****IF RTO IS AS HIGH AS 0.7 THEN WINNING PERCENTAGE MUST BE LOW TO 0.15 FORMING AN INVERSE RELATIONSHIP.
# # RTP = 1-RTO
# # PLAYS = 46422 # NUMBER OF PLAYERS IN A SESSION

# GAME_THRESHOLD = ConstantVariable.get_constant_variable().get("game_threshold")
# WINNING_PERCENTAGE = ConstantVariable.get_constant_variable().get("winning_percentage")
# RTO = ConstantVariable.get_constant_variable().get("rto")
# RTP = float(int(ConstantVariable.get_constant_variable().get("rtp"))) - RTO
# PLAYS = ConstantVariable.get_constant_variable().get("plays")

# # print(GAME_THRESHOLD)
# # print(WINNING_PERCENTAGE)
# # print(RTO)
# # print(RTP)
# # print(PLAYS)

# GAME_BANDS = {
#                 10:{
#                     "cost": 100,
#                     "jackpot": 0.02,
#                     "playability": 0.35,
#                     "no_of_plays": 0,
#                     "band": 10,
#                     "players":[],
#                     "band_random_sum":0,
#                     "winner_selection":0,
#                     },
#                 50:{
#                     "cost": 200,
#                     "jackpot": 0.02,
#                     "playability": 0.33,
#                     "no_of_plays": 0,
#                     "band": 50,
#                     "players":[],
#                     "band_random_sum":0,
#                     "winner_selection":0,
#                     },
#                 250:{
#                     "cost": 500,
#                     "jackpot": 0.005,
#                     "playability": 0.20,
#                     "no_of_plays": 0,
#                     "band": 250,
#                     "players":[],
#                     "band_random_sum":0,
#                     "winner_selection":0,
#                     },
#                 500:{
#                     "cost": 1000,
#                     "jackpot": 0.0013,
#                     "playability": 0.09,
#                     "no_of_plays": 0,
#                     "band": 500,
#                     "players":[],
#                     "band_random_sum":0,
#                     "winner_selection":0,
#                     },
#                 1000:{
#                     "cost": 2000,
#                     "jackpot": 0.0013,
#                     "playability": 0.09,
#                     "no_of_plays": 0,
#                     "band": 1000,
#                     "players":[],
#                     "band_random_sum":0,
#                     "winner_selection":0,
#                     }
#             }

# raw_pool = [[GAME_BANDS[key]["cost"]]*\
#                                 math.ceil(GAME_BANDS[key]["playability"]*1000) \
#                                 for key in GAME_BANDS.keys()]

# GAME_POOL = [*raw_pool[0], *raw_pool[1], *raw_pool[2], *raw_pool[3]]


# if __name__ == "__main__":

#     possible_plays = GAME_BANDS.keys()

#     raw_pool = [[GAME_BANDS[key]["cost"]]*\
#                                 math.ceil(GAME_BANDS[key]["playability"]*100) \
#                                 for key in GAME_BANDS.keys()]

#     pool = [*raw_pool[0], *raw_pool[1], *raw_pool[2], *raw_pool[3]]
#     print(pool)

# """
# WINRATE - WINPCNT - RTO

# 2.313 -    0.15   - 0.3
# 4.610 -    0.30   - 0.3
# 7.696 -    0.50   - 0.3

# """
