import random

# from settings import *
from string import ascii_lowercase

from main.helpers.game.settings import GAME_BANDS, PLAYS, RTO, RTP, WINNING_PERCENTAGE

# from sharer import clean_shares
from main.helpers.game.sharer import clean_shares

# class LotteryModel():

#     """
#         JUST A TEST MODEL TO SIMULATE JOSEPH'S MODEL WHILE BUILDING
#         PLEASE IGNORE.
#     """

#     def __init__(self) -> None:

#         self.id    = random.randint(1,**********)
#         self.batch = "malabites epsillius"
#         self.phone = "***********"
#         self.band = random.choice(["10", "50", "250", "500", "1000"])
#         self.stake_amount = random.choice(["100", "200", "500", "1000", "2000"])
#         self.lucky_number = random.randint(1,10)
#         self.paid = True
#         self.account_no = ""
#         self.bank_name = ""
#         self.date = ""
#         self.paid_date = ""
#         self.bank_code = ""

# class Player:

#     # def __init__(self, id:int, lucky_number:int, phone:str, band_played:int ) -> None:

#     #     self.f_name:str = "".join([random.choice(list(ascii_lowercase + "aeiouaeiou" + "aeiouaeiou")) for _ in range(6)])
#     #     self.l_name:str = "".join([random.choice(list(ascii_lowercase + "aeiouaeiou" + "aeiouaeiou")) for _ in range(6)])
#     #     self.phone:str  = "".join([random.choice(list(ascii_lowercase + "aeiouaeiou" + "aeiouaeiou")) for _ in range(6)])
#     #     self.id:int = id
#     #     self.phone:str = phone
#     #     self.number:int  = random.randint(1,10)
#     #     self.balance:int = random.randint(100, 100000)
#     #     self.will_replay:int   = 3
#     #     self.band_played:int   = int(band_played)
#     #     self.feeling_happy:int = 0.1
#     #     self.num_winners:int = 0
#     #     self.earning:int


#     def __init__(self, id:int, lucky_number:int, phone:str, band_played:int, unique_id:str ) -> None:

#         self.f_name:str = "".join([random.choice(list(ascii_lowercase + "aeiouaeiou" + "aeiouaeiou")) for _ in range(6)])
#         self.l_name:str = "".join([random.choice(list(ascii_lowercase + "aeiouaeiou" + "aeiouaeiou")) for _ in range(6)])
#         self.phone:str  = "".join([random.choice(list(ascii_lowercase + "aeiouaeiou" + "aeiouaeiou")) for _ in range(6)])
#         self.id:int = id
#         self.unique_id:str = unique_id
#         self.phone:str = phone
#         self.number:int  = lucky_number
#         self.balance:int = random.randint(100, 100000)
#         self.will_replay:int   = 3
#         self.band_played:int   = int(band_played)
#         self.feeling_happy:int = 0.1
#         self.earning:int

#     def __str__(self)->str:

#         return f"{self.f_name} {self.l_name} {self.feeling_happy}"

#     def __repr__(self)->str:

#         return f"{self.f_name} {self.l_name} : {self.number}-{self.band_played} : {self.band_played}\n"

#     @property
#     def willing_to_play(self):

#         return self.feeling_happy * self.balance

#     @property
#     def earning(self):
#         return self.band_played * 1000


# class Game:

#     def __init__(self) -> None:

#         self.GAME_REVENUE = 0
#         self.winners = 0
#         self.PASSED = 0
#         self.players = []
#         self.game_result:dict = {}

#     def play(self, player:Player):

#         play = player.band_played

#         self.GAME_REVENUE += int(play)
#         play_band = self.filter_gameband_by_cost(play)

#         GAME_BANDS[play_band]["no_of_plays"] += 1
#         GAME_BANDS[play_band]["band_random_sum"] += player.number
#         GAME_BANDS[play_band]["players"].append(player)

#         self.players.append(player)

#     def filter_gameband_by_cost(self, cost:int)->int:

#         response = list(filter(lambda x:x["cost"] == cost, GAME_BANDS.values()))

#         return response[0]["band"]

#     def regularize_results(self, result:dict)->dict:

#         dict_items = list(result.items())
#         for key, value in dict_items:

#             result[key]["revenue"] = value["no_of_plays"] * value["cost"]

#             income_p = ((value["no_of_plays"] * value["cost"]) / self.GAME_REVENUE)*100
#             result[key]["income%"] = f'{income_p}%'

#             result[key]["play%"] = f'{((value["no_of_plays"] ) / PLAYS)*100}%'

#             result[key]["RTP"] = (value["no_of_plays"] * value["cost"])*RTP
#             result[key]["winners%"] = (RTP * income_p) * WINNING_PERCENTAGE
#             result[key]["winners"] = (result[key]["winners%"] * value["no_of_plays"])/100

#             result[key]["wins_jkpt"] = result[key]["jackpot"] * result[key]["winners"]

#             result[key]["if_equal_share"] = ((result[key]["RTP"] or 1) / (result[key]["winners"] or 1))

#             result[key]["if_equal_share%"] = ((result[key]["if_equal_share"] or 1) /(result[key]["RTP"] or 1)) * 100

#             result[key]["amount_aft_jkpt"] = result[key]["RTP"] - (result[key]["wins_jkpt"] * (result[key]["band"]*1000) if result[key]["wins_jkpt"] > 0.5 else 0)

#             result[key]["no_winners_aft_jkpt"] = result[key]["winners"] - result[key]["wins_jkpt"]

#             result[key]["equal_share_after_jkpt"] = ((result[key]["amount_aft_jkpt"] or 1)/  (result[key]["no_winners_aft_jkpt"] or 1))

#             self.winners += result[key]["winners"]

#         self.game_result = result

#         return result

#     def winner_selection(self):

#         band_winners = []
#         jkpts = []

#         for key, band in self.game_result.copy().items():

#             num_winners = int(band["winners"])
#             num_jkpts   = round(band["wins_jkpt"])

#             players:list = band["players"]

#             for _ in range(num_winners):

#                 winner = random.choice(players)
#                 band_winners.append(winner)

#                 players.remove(winner)

#             band_winners_copy = band_winners.copy()

#             jkpts = [] # RESET JACKPOT
#             for _ in range(num_jkpts):

#                 winner = random.choice(band_winners_copy)
#                 jkpts.append(winner)

#                 band_winners_copy.remove(winner)

#             self.game_result[key]["winner_selection"] = band_winners
#             self.game_result[key]["non_jkpts_selectns"] = band_winners_copy
#             self.game_result[key]["jkpts_selections"] = jkpts

#             band_winners.clear()


#     def share_winnings(self):

#         JKPT_TOTAL = 0

#         response = {}
#         all_winners = []

#         for key, band in self.game_result.items():

#             band["non_jkpts_selectns"] = clean_shares(
#                                                         band["non_jkpts_selectns"],
#                                                         band["amount_aft_jkpt"],
#                                                         band["cost"]
#                                                     )

#             player_random_nums = [player.number for player in band["non_jkpts_selectns"]]

#             sum_random_nums = sum(player_random_nums)

#             non_jkpt_selections = []

#             for player in band["non_jkpts_selectns"]:

#                 non_jkpt_selections.append(
#                                             {
#                                                 "phone":player.phone,
#                                                 "earning" :(player.number / sum_random_nums)*band["amount_aft_jkpt"],
#                                                 "id": player.id,
#                                                 "unique_id": player.unique_id
#                                                 }
#                                             )

#             jkpt_selections = []
#             for player in band["jkpts_selections"]:

#                 JKPT_TOTAL += key*1000

#                 jkpt_selections.append(
#                                             {
#                                                 "phone":player.phone,
#                                                 "earning" :key*1000,
#                                                 "id": player.id,
#                                                 "unique_id": player.unique_id
#                                                 }
#                                             )


#             all_winners += (jkpt_selections + non_jkpt_selections)

#             response[key] = {
#                                 "ordinary_winners"    : non_jkpt_selections,
#                                 "jackpot_winners"     : jkpt_selections,
#                                 "total_jackpot_amount": JKPT_TOTAL
#                             }

#         all_winners_phone = [winner["phone"] for winner in all_winners]
#         super_winner = list(set([x for x in all_winners_phone if all_winners_phone.count(x) > 1]))

#         response["totals"] = {
#                                 "total_revenue"       : self.GAME_REVENUE,
#                                 "super_winners"       : super_winner,
#                                 "RTO"                 : self.GAME_REVENUE * RTO,
#                                 "RTP"                 : self.GAME_REVENUE * RTP,
#                                 "WIN_RATIO"           : (len(band["non_jkpts_selectns"])/(PLAYS)*100)
#                                 }

#         return response


#     # def share_winnings(self):

#     #     JKPT_TOTAL = 0

#     #     response = {}
#     #     all_winners = []

#     #     for key, band in self.game_result.items():

#     #         band["non_jkpts_selectns"] = clean_shares(
#     #                                                     band["non_jkpts_selectns"],
#     #                                                     band["amount_aft_jkpt"],
#     #                                                     band["cost"]
#     #                                                 )

#     #         player_random_nums = [player.number for player in band["non_jkpts_selectns"]]

#     #         sum_random_nums = sum(player_random_nums)

#     #         non_jkpt_selections = []

#     #         for player in band["non_jkpts_selectns"]:

#     #             non_jkpt_selections.append(
#     #                                         {
#     #                                             "phone":player.phone,
#     #                                             "earning" :(player.number / sum_random_nums)*band["amount_aft_jkpt"]
#     #                                             }
#     #                                         )

#     #         jkpt_selections = []
#     #         for player in band["jkpts_selections"]:

#     #             JKPT_TOTAL += key*1000

#     #             jkpt_selections.append(
#     #                                     {
#     #                                         "phone":player.phone,
#     #                                         "earning" :key*1000
#     #                                         }
#     #                                     )

#     #         all_winners += (jkpt_selections + non_jkpt_selections)

#     #         response[key] = {
#     #                             "ordinary_winners"    : non_jkpt_selections,
#     #                             "jackpot_winners"     : jkpt_selections,
#     #                             "total_jackpot_amount": JKPT_TOTAL

#     #                             }

#     #     all_winners_phone = [winner["phone"] for winner in all_winners]
#     #     super_winner = list(set([x for x in all_winners_phone if all_winners_phone.count(x) > 1]))

#     #     response["totals"] = {
#     #                             "total_revenue"       : self.GAME_REVENUE,
#     #                             "super_winners"       : super_winner,
#     #                             "RTO"                 : self.GAME_REVENUE * RTO,
#     #                             "RTP"                 : self.GAME_REVENUE * RTP,
#     #                             "WIN_RATIO"           : (len(band["non_jkpts_selectns"])/(PLAYS)*100)
#     #                             }

#     #     return response


#     def load_players(self, raw_players:list)->bool:

#         """
#             EXPECTS LotteryModel QUERYSET FROM MAIN APP, LOOPS THROUGH AND CONVERTS THEM TO ACTUAL PLAYER OBJECTS TO FIT GAME REQUIREMENTS
#         """

#         for raw_player in raw_players:

#             player = Player( raw_player.id, raw_player.lucky_number, raw_player.phone, raw_player.stake_amount, raw_player.unique_id)
#             self.play(player)


#     def run(self, players:list):

#         """
#             MAIN ENTRY POINT FOR APPLICATION, EXPECTS LotteryModel QUERYSET FROM MAIN APP.
#         """
#         self.load_players(players) # LOAD PLAYERS INTO GAME CLASS

#         self.regularize_results(GAME_BANDS)
#         self.winner_selection()
#         data = self.share_winnings()

#         return data


# if __name__=="__main__":

#     game = Game()
#     pprint(game.run([LotteryModel() for _ in range(PLAYS)]))


# from datetime import datetime
# from functools import reduce
# from pprint import pprint
# import string
# from main.helpers.game.settings import *
# # from settings import *
# from string import ascii_lowercase
# import random


class LotteryModel:
    """
    JUST A TEST MODEL TO SIMULATE JOSEPH'S MODEL WHILE BUILDING
    PLEASE IGNORE.
    """

    def __init__(self) -> None:
        self.id = random.randint(1, **********)
        self.batch = "malabites epsillius"
        self.phone = "***********"
        self.unique_id = "Jun-df-as1"
        self.band = random.choice(["10", "50", "250", "500", "1000"])
        self.stake_amount = random.choice(["100", "200", "500", "1000", "2000"])
        self.lucky_number = random.randint(1, 100)
        self.paid = True
        self.account_no = ""
        self.bank_name = ""
        self.date = ""
        self.paid_date = ""
        self.bank_code = ""


class Player:
    def __init__(self, id: int, lucky_number: int, phone: str, band_played: int, unique_id: str) -> None:
        self.f_name: str = "".join([random.choice(list(ascii_lowercase + "aeiouaeiou" + "aeiouaeiou")) for _ in range(6)])
        self.l_name: str = "".join([random.choice(list(ascii_lowercase + "aeiouaeiou" + "aeiouaeiou")) for _ in range(6)])
        self.phone: str = "".join([random.choice(list(ascii_lowercase + "aeiouaeiou" + "aeiouaeiou")) for _ in range(6)])
        self.id: int = id
        self.unique_id: str = unique_id
        self.phone: str = phone
        self.number: int = lucky_number
        self.balance: int = random.randint(100, 100000)
        self.will_replay: int = 3
        self.band_played: int = int(band_played)
        self.feeling_happy: int = 0.1
        self.earning: int

    def __str__(self) -> str:
        return f"{self.f_name} {self.l_name} {self.feeling_happy}"

    def __repr__(self) -> str:
        return f"{self.f_name} {self.l_name} : {self.number}-{self.band_played} : {self.band_played}\n"

    @property
    def willing_to_play(self):
        return self.feeling_happy * self.balance

    @property
    def earning(self):
        return self.band_played * 1000


class Game:
    def __init__(self) -> None:
        self.GAME_REVENUE = 0
        self.winners = 0
        self.PASSED = 0
        self.players = []
        self.game_result: dict = {}

    def play(self, player: Player):
        play = player.band_played

        self.GAME_REVENUE += int(play)
        play_band = self.filter_gameband_by_cost(play)

        GAME_BANDS[play_band]["no_of_plays"] += 1
        GAME_BANDS[play_band]["band_random_sum"] += int(player.number)
        GAME_BANDS[play_band]["players"].append(player)

        self.players.append(player)

    def filter_gameband_by_cost(self, cost: int) -> int:
        response = list(filter(lambda x: x["cost"] == cost, GAME_BANDS.values()))

        return response[0]["band"]

    def regularize_results(self, result: dict) -> dict:
        dict_items = list(result.items())
        for key, value in dict_items:
            result[key]["revenue"] = value["no_of_plays"] * value["cost"]

            if self.GAME_REVENUE == 0:
                income_p = 0.00
            else:
                income_p = ((value["no_of_plays"] * value["cost"]) / self.GAME_REVENUE) * 100

            result[key]["income%"] = f"{income_p}%"

            result[key]["play%"] = f'{((value["no_of_plays"] ) / PLAYS)*100}%'

            result[key]["RTP"] = (value["no_of_plays"] * value["cost"]) * RTP
            result[key]["winners%"] = (RTP * income_p) * WINNING_PERCENTAGE
            result[key]["winners"] = (result[key]["winners%"] * value["no_of_plays"]) / 100

            result[key]["wins_jkpt"] = result[key]["jackpot"] * result[key]["winners"]

            result[key]["if_equal_share"] = (result[key]["RTP"] or 1) / (result[key]["winners"] or 1)

            result[key]["if_equal_share%"] = ((result[key]["if_equal_share"] or 1) / (result[key]["RTP"] or 1)) * 100

            result[key]["amount_aft_jkpt"] = result[key]["RTP"] - (
                result[key]["wins_jkpt"] * (result[key]["band"] * 1000) if result[key]["wins_jkpt"] > 0.5 else 0
            )

            result[key]["no_winners_aft_jkpt"] = result[key]["winners"] - result[key]["wins_jkpt"]

            result[key]["equal_share_after_jkpt"] = (result[key]["amount_aft_jkpt"] or 1) / (result[key]["no_winners_aft_jkpt"] or 1)

            self.winners += result[key]["winners"]

        self.game_result = result

        return result

    def winner_selection(self):
        band_winners = []
        jkpts = []

        for key, band in self.game_result.copy().items():
            num_winners = int(band["winners"])
            num_jkpts = round(band["wins_jkpt"])

            players: list = band["players"]

            for _ in range(num_winners):
                winner = random.choice(players)
                band_winners.append(winner)

                players.remove(winner)

            band_winners_copy = band_winners.copy()

            jkpts = []  # RESET JACKPOT
            for _ in range(num_jkpts):
                winner = random.choice(band_winners_copy)
                jkpts.append(winner)

                band_winners_copy.remove(winner)

            self.game_result[key]["winner_selection"] = band_winners
            self.game_result[key]["non_jkpts_selectns"] = band_winners_copy
            self.game_result[key]["jkpts_selections"] = jkpts

            band_winners.clear()

    # def share_winnings(self):

    #     JKPT_TOTAL = 0

    #     response = {}
    #     all_winners = []

    #     for key, band in self.game_result.items():

    #         band["non_jkpts_selectns"] = clean_shares(
    #                                                     band["non_jkpts_selectns"],
    #                                                     band["amount_aft_jkpt"],
    #                                                     band["cost"]
    #                                                 )

    #         player_random_nums = [player.number for player in band["non_jkpts_selectns"]]

    #         sum_random_nums = sum(player_random_nums)

    #         non_jkpt_selections = []

    #         for player in band["non_jkpts_selectns"]:

    #             non_jkpt_selections.append(
    #                                         {
    #                                             "phone":player.phone,
    #                                             "earning" :(player.number / sum_random_nums)*band["amount_aft_jkpt"],
    #                                             "id": player.id,
    #                                             "unique_id": player.unique_id
    #                                             }
    #                                         )

    #         jkpt_selections = []
    #         for player in band["jkpts_selections"]:

    #             JKPT_TOTAL += key*1000

    #             jkpt_selections.append(
    #                                         {
    #                                             "phone":player.phone,
    #                                             "earning" :key*1000,
    #                                             "id": player.id,
    #                                             "unique_id": player.unique_id
    #                                             }
    #                                         )

    #         all_winners += (jkpt_selections + non_jkpt_selections)

    #         response[key] = {
    #                             "ordinary_winners"    : non_jkpt_selections,
    #                             "jackpot_winners"     : jkpt_selections,
    #                             "total_jackpot_amount": JKPT_TOTAL
    #                         }

    #     all_winners_phone = [winner["phone"] for winner in all_winners]
    #     super_winner = list(set([x for x in all_winners_phone if all_winners_phone.count(x) > 1]))

    #     response["totals"] = {
    #                             "total_revenue"       : self.GAME_REVENUE,
    #                             "super_winners"       : super_winner,
    #                             "RTO"                 : self.GAME_REVENUE * RTO,
    #                             "RTP"                 : self.GAME_REVENUE * RTP,
    #                             "WIN_RATIO"           : (len(band["non_jkpts_selectns"])/(PLAYS)*100)
    #                             }

    #     return response

    def share_winnings(self):
        JKPT_TOTAL = 0

        response = {}
        all_winners = []

        for key, band in self.game_result.items():
            band["non_jkpts_selectns"] = clean_shares(band["non_jkpts_selectns"], band["amount_aft_jkpt"], band["cost"])

            player_random_nums = [int(player.number) for player in band["non_jkpts_selectns"]]

            sum_random_nums = sum(player_random_nums)

            non_jkpt_selections = []

            for player in band["non_jkpts_selectns"]:
                non_jkpt_selections.append(
                    {
                        "phone": player.phone,
                        "earning": (int(player.number) / sum_random_nums) * band["amount_aft_jkpt"],
                        "id": player.id,
                        "unique_id": player.unique_id,
                        "channel": player.channel,
                        "lottery_type": player.lottery_type,
                    }
                )

            jkpt_selections = []
            for player in band["jkpts_selections"]:
                JKPT_TOTAL += key * 1000

                jkpt_selections.append(
                    {
                        "phone": player.phone,
                        "earning": key * 1000,
                        "id": player.id,
                        "unique_id": player.unique_id,
                        "channel": player.channel,
                        "lottery_type": player.lottery_type,
                    }
                )

            all_winners += jkpt_selections + non_jkpt_selections

            print("all winners", all_winners)

            response[key] = {
                "ordinary_winners": non_jkpt_selections,
                "jackpot_winners": jkpt_selections,
                "total_jackpot_amount": JKPT_TOTAL,
            }

        all_winners_phone = [winner["phone"] for winner in all_winners]
        super_winner = list(set([x for x in all_winners_phone if all_winners_phone.count(x) > 1]))

        response["totals"] = {
            "total_revenue": self.GAME_REVENUE,
            "super_winners": super_winner,
            "RTO": self.GAME_REVENUE * RTO,
            "RTP": self.GAME_REVENUE * RTP,
            "WIN_RATIO": (len(all_winners) / (PLAYS) * 100),
            # "WIN_RATIO"           : (len(band["non_jkpts_selectns"])/(PLAYS))
        }
        print("-------------------------------------")
        print("-------------------------------------")
        print("-------------------------------------")
        print("-------------------------------------")
        # print(band["non_jkpts_selectns"])
        print(all_winners_phone)
        print("-------------------------------------")
        print("-------------------------------------")
        print("-------------------------------------")
        print("-------------------------------------")

        return response

    def load_players(self, raw_players: list) -> bool:
        """
        EXPECTS LotteryModel QUERYSET FROM MAIN APP, LOOPS THROUGH AND CONVERTS THEM TO ACTUAL PLAYER OBJECTS TO FIT GAME REQUIREMENTS
        """

        for raw_player in raw_players:
            player = Player(
                raw_player.id,
                raw_player.lucky_number[3:],
                raw_player.phone,
                raw_player.stake_amount,
                raw_player.unique_id,
            )
            self.play(player)

    def run(self, players: list):
        """
        MAIN ENTRY POINT FOR APPLICATION, EXPECTS LotteryModel QUERYSET FROM MAIN APP.
        """
        self.load_players(players)  # LOAD PLAYERS INTO GAME CLASS

        self.regularize_results(GAME_BANDS)
        self.winner_selection()
        data = self.share_winnings()

        return data


if __name__ == "__main__":
    game = Game()
    game.run([LotteryModel() for _ in range(10000)])
