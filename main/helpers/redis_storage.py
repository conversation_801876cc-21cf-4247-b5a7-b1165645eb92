from dataclasses import dataclass

import redis
from decouple import config


@dataclass
class RedisStorage:
    """
    Class for storing data in Redis.
    """

    redis_key: str
    redis_client: object = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0)

    def set_data(self, data):
        """
        Set data in Redis.
        """
        self.redis_client.set(self.redis_key, data, ex=10800)

    def get_data(self):
        """
        Get data from Redis.
        """
        return self.redis_client.get(self.redis_key)

    def delete_data(self):
        """
        Delete data from Redis.
        """
        self.redis_client.delete(self.redis_key)

    def clear_data(self):
        """
        Clear data from Redis.
        """
        self.redis_client.flushdb()


class RedisStore:
    """
    Class for storing data in Redis.
    """

    redis_client: object = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0)

    @classmethod
    def set_data(cls, key, value):
        """
        Set data in Redis.
        """
        cls.redis_client.set(key, value, ex=10800)

    @classmethod
    def get_data(cls, key):
        """
        Get data from Redis.
        """
        return cls.redis_client.get(key)

    @classmethod
    def get_or_create_data(cls, key, default=0):
        """
        Get data from Redis.
        """
        data = cls.redis_client.get(key)
        if data is None:
            cls.set_data(key, default)
            return default

        return data.decode()

    @classmethod
    def delete_data(cls, key):
        """
        Delete data from Redis.
        """
        cls.redis_client.delete(key)

    def clear_data(cls):
        """
        Clear data from Redis.
        """
        cls.redis_client.flushdb()


@dataclass
class RedisDictStorage:
    """
    Class for storing data in Redis.
    """

    redis_key: str
    redis_client: object = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")

    def set_data(self, data: dict):
        """
        Set dict data in Redis.
        """

        self.redis_client.hset(self.redis_key, mapping=data)
        self.redis_client.expire(self.redis_key, 600)

    def set_string_data(self, data: dict):
        """
        Set dict data in Redis.
        """

        self.redis_client.set(self.redis_key, data, ex=3600)

    def get_string_data(self):
        """
        Get dict data from Redis.
        """
        return self.redis_client.get(self.redis_key)

    def get_data(self):
        """
        Get dict data from Redis.
        """
        return self.redis_client.hgetall(self.redis_key)

    def get_all_hash_keys(self):
        """
        Get dict data from Redis.
        """
        return self.redis_client.hkeys(self.redis_key)

    def get_value_of_a_key(self, key):
        """
        Get dict data from Redis.
        """
        return self.redis_client.hget(self.redis_key, key)

    def get_value_of_multiple_keys(self, *required_keys):
        """
        Get dict data from Redis.
        """
        return self.redis_client.hmget(self.redis_key, required_keys)
