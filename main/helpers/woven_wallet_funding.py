from rest_framework import status
from rest_framework.response import Response

from main.helpers.woven_manager import woven_verify_transaction
from main.models import PaymentTransaction, UserProfile, WovenCallBack
from wallet_app.models import BankTransferFunding, DebitCreditRecord, UserWallet
from wallet_app.tasks import notify_admin_of_user_funding


def handle_woven_wallet_funding(woven_data):
    resp = woven_data

    account_ref = resp.get("account_reference")
    unique_ref = resp.get("unique_reference")
    amount = resp.get("amount")

    _check_for_transaction = PaymentTransaction.objects.filter(provider_transaction_reference=unique_ref).last()

    if _check_for_transaction:
        return {"status": "SUCCESSFUL", "message": "transaction already processed"}

    get_wallet = UserWallet.objects.filter(account_ref=account_ref).last()

    # Check if transaction is in our database
    check_for_transaction = PaymentTransaction.objects.filter(
        provider_transaction_reference=unique_ref,
    ).last()

    user_profile = UserProfile.objects.filter(phone_number=get_wallet.user.phone_number).last()

    if user_profile is None:
        return Response(data={"message": "User not found"}, status=status.HTTP_404_NOT_FOUND)

    if check_for_transaction:
        if check_for_transaction.status == "SUCCESSFUL":
            return {"status": "SUCCESSFUL", "message": "transaction already processed"}

    if check_for_transaction:
        # Check woven to verify transaction
        verfiy_transaction = woven_verify_transaction(transaction_reference=unique_ref)

        if verfiy_transaction:
            if check_for_transaction.status != "SUCCESSFUL" and check_for_transaction.has_paid is False:
                check_for_transaction.status = "SUCCESSFUL"
                check_for_transaction.has_paid = True
                check_for_transaction.amount = float(amount)

                check_for_transaction.save()

                check_for_payload = WovenCallBack.objects.filter(transaction=check_for_transaction).last()

                if check_for_payload:
                    check_for_payload.payload = resp
                    check_for_payload.save()
                else:
                    # Create provider raw payload

                    WovenCallBack.objects.create(transaction=check_for_transaction, payload=resp)

                # fund users wallet
                debit_credit_record = DebitCreditRecord.create_record(
                    phone_number=user_profile.phone_number,
                    amount=int(amount),
                    channel="WEB",
                    reference=unique_ref,
                    transaction_type="CREDIT",
                )

                wallet_payload = {
                    "transaction_from": "WOVEN_FUNDING",
                }

                UserWallet.fund_wallet(
                    user=user_profile,
                    amount=int(amount),
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="GAME_PLAY_WALLET",
                    **wallet_payload,
                )

                # notify admin
                notify_admin_of_user_funding.delay(amount=int(amount), phone_number=user_profile.phone_number, channel="WOVEN")

                return Response(
                    data={"message": "Transaction successful"},
                    status=status.HTTP_200_OK,
                )
        else:
            try:
                BankTransferFunding().update_bank_transfer_status(user_profile.phone_number, "SUCCESSFUL")
            except Exception as e:
                print(
                    f"""Error updating bank transfer status: {e}

                ===============================================
                FAILED TO UPDATE BANK TRANSFER FUNDING STATUS, "\n\n\n

                """
                )

            return Response(data={"data": "transaction not verified"}, status=status.HTTP_200_OK)
    else:
        # Check woven to verify transaction
        verfiy_transaction = woven_verify_transaction(transaction_reference=unique_ref)

        if verfiy_transaction:
            # Add transaction to database
            new_collection_transaction = PaymentTransaction.objects.create(
                lottery_player=user_profile,
                payment_channel="WOVEN_ACCOUNT",
                amount=amount,
                provider_transaction_reference=unique_ref,
                unique_provider_transaction_reference=unique_ref,
                status="SUCCESSFUL",
                has_paid=True,
            )

            check_for_payload = WovenCallBack.objects.filter(transaction=new_collection_transaction).last()

            if check_for_payload:
                check_for_payload.payload = resp
                check_for_payload.save()
            else:
                # Create provider raw payload

                WovenCallBack.objects.create(transaction=new_collection_transaction, payload=resp)

            # fund users wallet

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=user_profile.phone_number,
                amount=int(amount),
                channel="WEB",
                reference=f"{unique_ref}",
                transaction_type="CREDIT",
            )

            wallet_payload = {
                "transaction_from": "WOVEN_FUNDING",
            }

            UserWallet.fund_wallet(
                user=user_profile,
                amount=int(amount),
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="GAME_PLAY_WALLET",
                **wallet_payload,
            )

            # notify admin
            notify_admin_of_user_funding.delay(amount=int(amount), phone_number=user_profile.phone_number, channel="WOVEN")

            return Response(data={"message": "Transaction successful"}, status=status.HTTP_200_OK)

        else:
            try:
                BankTransferFunding().update_bank_transfer_status(user_profile.phone_number, "SUCCESSFUL")
            except Exception:
                pass

            return Response(data={"data": "Transaction not verified"}, status=status.HTTP_200_OK)

    try:
        BankTransferFunding().update_bank_transfer_status(user_profile.phone_number, "SUCCESSFUL")
    except Exception:
        pass

    return Response(data={"data": "success"}, status=status.HTTP_200_OK)
