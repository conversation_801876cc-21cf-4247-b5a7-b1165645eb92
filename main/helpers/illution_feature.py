from dataclasses import dataclass

import redis

from main.models import ConstantVariable, InstantCashoutPendingWinning
from wallet_app.models import IllusionWallet
from decouple import config


@dataclass
class IllusionDatabase:
    """
    Class for storing data in Redis.
    """

    redis_key: str
    redis_client: object = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")

    def set_data(self, data: dict):
        """
        Set dict data in Redis.
        """
        self.redis_client.hset(self.redis_key, mapping=data)
        self.redis_client.expire(self.redis_key, 172800)

    def get_data(self):
        """
        Get dict data from Redis.
        """
        return self.redis_client.hgetall(self.redis_key)

    def get_all_hash_keys(self):
        """
        Get dict data from Redis.
        """
        return self.redis_client.hkeys(self.redis_key)

    def get_value_of_a_key(self, key):
        """
        Get dict data from Redis.
        """
        return self.redis_client.hget(self.redis_key, key)

    def get_value_of_multiple_keys(self, *required_keys):
        """
        Get dict data from Redis.
        """
        return self.redis_client.hmget(self.redis_key, required_keys)


@dataclass
class GameIllusion:
    """
    CLASS FOR DECIDING ILLUSION WINNING

    """

    def get_illusion(self, key):
        """
        GET ILLUSION WINNING
        """
        illusion_data = IllusionDatabase(redis_key=key).get_data()
        return illusion_data

    def set_illusion_data(self, data, game_type, is_global=False):
        """
        SET ILLUSION DATA
        """
        if is_global:
            key = "global_illusion"

        else:
            key = f"{game_type}_illusion"

        # print("setting illusion data", data, "=====================")

        IllusionDatabase(redis_key=key).set_data(data=data)

    def set_excess_amount(self, amount, game_type):
        illusion_level = ConstantVariable().get_game_illusion_level()

        is_global = False

        if illusion_level == "GLOBAL":
            is_global = True
        else:
            is_global = False

        if is_global is True:
            key = "global_illusion"

        else:
            key = f"{game_type}_illusion"

        illusion_data = self.get_illusion(key=key)
        if illusion_data.get("amount") is None:
            _set_illusion_data = {"amount": amount, "count": 0}

            IllusionWallet().add_illusion_wallet(amount=amount)

            self.set_illusion_data(data=_set_illusion_data, game_type=game_type, is_global=is_global)
        else:
            # print(
            #     f"""
            # illusion_data: {illusion_data}
            # \n\n\n\n\n
            # """
            # )
            _amount = float(str(illusion_data.get("amount").replace(".0", ""))) + amount
            _set_illusion_data = {
                "amount": _amount,
                "count": int(illusion_data.get("count")),
            }

            IllusionWallet().add_illusion_wallet(amount=amount)

            self.set_illusion_data(data=_set_illusion_data, game_type=game_type, is_global=is_global)

    def decide_illusion(self, game_type, game_play_id, phone_number, ticket_instance, total_stake_amount):
        """
        DECIDE ILLUSION WINNING

        RETURN:
            - Status: True or False
            - Amount: Amount to be won
            - Count: Number of times illusion has happened

            return True, 0, 0 or None, None, None


        """
        # get

        illusion_level = ConstantVariable().get_game_illusion_level()

        if illusion_level == "GLOBAL":
            key = "global_illusion"
            is_global = True
        elif illusion_level == "OFF":
            return False, 0, 0, 0
        else:
            key = f"{game_type}_illusion"
            is_global = False

        illusion_data = self.get_illusion(key)

        # print("illusion_data ================", illusion_data, "=====================")

        if illusion_data:
            # print(
            #     f"""
            # illusion_data: {illusion_data}
            # """
            # )

            game_play_ids_in_redis = illusion_data.get("game_play_ids")
            if game_play_ids_in_redis is None:
                pass
            else:
                game_play_ids_in_redis = game_play_ids_in_redis.split(",")
                if str(game_play_id) in game_play_ids_in_redis:
                    """
                    SEES IF THE GAME PLAY ID IS ALREADY IN THE REDIS
                    THAT MEANS THE GAME HAS ALREADY BEEN DRAWN AND THE ILLUSION HAS ALREADY BEEN DECIDED
                    """
                    return None, None, None, None

            if int(illusion_data.get("count")) == 0:
                amount_won = int(str(illusion_data.get("amount")).replace(",", "").replace(".0", ""))

                if IllusionWallet().deduct_illusion_wallet(amount=amount_won, phone=phone_number, game_type=game_type) is False:
                    return None, None, None, None

                _set_illusion_data = {
                    "amount": 0,
                    "count": 1,
                    "game_play_ids": ",".join([str(game_play_id)]),
                }

                self.set_illusion_data(data=_set_illusion_data, game_type=game_type, is_global=is_global)

                if amount_won < 1:
                    return None, None, None, None

                pending_winning_instance = InstantCashoutPendingWinning.objects.create(
                    batch=f"illusion_{int(illusion_data.get('count')) + 1}",
                    amount=amount_won,
                    win_flavour="CASHBACK",
                    tier="least_win",
                    stake_amount=total_stake_amount,
                    pending=False,
                    claimant=ticket_instance,
                    is_illusion=True,
                    is_avialable=False,
                    illusion_level="GLOBAL" if is_global else "LOCAL",
                )

                return (
                    True,
                    amount_won,
                    int(illusion_data.get("count")) + 1,
                    pending_winning_instance,
                )

            elif (int(illusion_data.get("count")) + 1) % 2 == 0:
                _get_game_ids_in_redis = illusion_data.get("game_play_ids")
                _get_game_ids_in_redis = _get_game_ids_in_redis.split(",")
                _get_game_ids_in_redis.append(str(game_play_id))

                _set_illusion_data = {
                    "amount": int(str(illusion_data.get("amount")).replace(",", "").replace(".0", "")),
                    "count": int(illusion_data.get("count")) + 1,
                    "game_play_ids": ",".join(_get_game_ids_in_redis),
                }

                self.set_illusion_data(data=_set_illusion_data, game_type=game_type, is_global=is_global)

                return False, 0, (int(illusion_data.get("count")) + 1), None

            elif (int(illusion_data.get("count")) + 1) == 5:
                amount_won = int(str(illusion_data.get("amount")).replace(",", "").replace(".0", ""))

                if IllusionWallet().deduct_illusion_wallet(amount=amount_won, phone=phone_number, game_type=game_type) is False:
                    return None, None, None, None

                _get_game_ids_in_redis = illusion_data.get("game_play_ids")
                _get_game_ids_in_redis = _get_game_ids_in_redis.split(",")
                _get_game_ids_in_redis.append(str(game_play_id))

                _set_illusion_data = {
                    "amount": 0,
                    "count": 1,
                    "game_play_ids": ",".join(_get_game_ids_in_redis),
                }

                self.set_illusion_data(data=_set_illusion_data, game_type=game_type, is_global=is_global)

                if amount_won < 1:
                    return None, None, None, None

                pending_winning_instance = InstantCashoutPendingWinning.objects.create(
                    batch=f"illusion_{int(illusion_data.get('count')) + 1}",
                    amount=amount_won,
                    win_flavour="CASHBACK",
                    tier="least_win",
                    stake_amount=total_stake_amount,
                    pending=False,
                    claimant=ticket_instance,
                    is_illusion=True,
                    is_avialable=False,
                    illusion_level="GLOBAL" if is_global else "LOCAL",
                )

                return (
                    True,
                    amount_won,
                    (int(illusion_data.get("count")) + 1),
                    pending_winning_instance,
                )

            else:
                amount_won = int(str(illusion_data.get("amount")).replace(",", "").replace(".0", ""))

                if IllusionWallet().deduct_illusion_wallet(amount=amount_won, phone=phone_number, game_type=game_type) is False:
                    return None, None, None, None

                _get_game_ids_in_redis = illusion_data.get("game_play_ids")
                _get_game_ids_in_redis = _get_game_ids_in_redis.split(",")
                _get_game_ids_in_redis.append(str(game_play_id))

                if (int(illusion_data.get("count")) + 1) == 3:
                    _set_illusion_data = {
                        "amount": amount_won,
                        "count": int(illusion_data.get("count")) + 1,
                        "game_play_ids": ",".join(_get_game_ids_in_redis),
                    }

                    self.set_illusion_data(
                        data=_set_illusion_data,
                        game_type=game_type,
                        is_global=is_global,
                    )

                    return None, None, None, None
                else:
                    if IllusionWallet().deduct_illusion_wallet(amount=amount_won, phone=phone_number, game_type=game_type) is False:
                        return None, None, None, None

                    _set_illusion_data = {
                        "amount": 0,
                        "count": int(illusion_data.get("count")) + 1,
                        "game_play_ids": ",".join(_get_game_ids_in_redis),
                    }

                    self.set_illusion_data(
                        data=_set_illusion_data,
                        game_type=game_type,
                        is_global=is_global,
                    )

                    if amount_won < 1:
                        return None, None, None, None

                    pending_winning_instance = InstantCashoutPendingWinning.objects.create(
                        batch=f"illusion_{int(illusion_data.get('count')) + 1}",
                        amount=amount_won,
                        win_flavour="CASHBACK",
                        tier="least_win",
                        stake_amount=total_stake_amount,
                        pending=False,
                        claimant=ticket_instance,
                        is_illusion=True,
                        is_avialable=False,
                        illusion_level="GLOBAL" if is_global else "LOCAL",
                    )

                    return (
                        True,
                        amount_won,
                        (int(illusion_data.get("count")) + 1),
                        pending_winning_instance,
                    )

        else:
            amount_won = ticket_instance.amount_paid

            if float(amount_won) < 1:
                return None, None, None, None

            if IllusionWallet().deduct_illusion_wallet(amount=amount_won, phone=phone_number, game_type=game_type) is False:
                return None, None, None, None

            _get_game_ids_in_redis = illusion_data.get("game_play_ids")
            _get_game_ids_in_redis = _get_game_ids_in_redis.split(",") if _get_game_ids_in_redis else []
            _get_game_ids_in_redis.append(str(game_play_id))

            _set_illusion_data = {
                "amount": 0,
                "count": 1,
                "game_play_ids": ",".join(_get_game_ids_in_redis),
            }

            self.set_illusion_data(data=_set_illusion_data, game_type=game_type, is_global=is_global)

            if amount_won < 1:
                return None, None, None, None

            pending_winning_instance = InstantCashoutPendingWinning.objects.create(
                batch=f"illusion_{int(illusion_data.get('count')) + 1}",
                amount=amount_won,
                win_flavour="CASHBACK",
                tier="least_win",
                stake_amount=total_stake_amount,
                pending=False,
                claimant=ticket_instance,
                is_illusion=True,
                is_avialable=False,
                illusion_level="GLOBAL" if is_global else "LOCAL",
            )

            return True, amount_won, 1, pending_winning_instance
