import random
from datetime import datetime

from django.db.models import F, Q, Sum
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.db.models import <PERSON>loat<PERSON>ield
from django.db.models.functions import Cast

from decisioning_engine.global_jackpot import JackpotWinning  # jackpot_winning
from main.api.api_lottery_helpers import generate_game_play_id
from main.helpers.find_number import find_num
from main.helpers.redis_storage import RedisStore
from main.models import (
    ConstantVariable,
    InstantCashoutPendingWinning,
    Jackpot,
    JackpotConstantVariable,
    JackpotWinner,
    LotteryBatch,
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
)
from main.tasks import (
    celery_sms_for_instant_cash_lost_ticket,
    celery_sms_for_instant_cash_winners,
    notify_agents_on_lottery_batch_draw,
    switch_rtp,
)
from overide_print import print
from pos_app.models import Agent
from wyse_ussd.tasks import (
    instant_cashout_lost_sms_on_telco,
    ussd_lottery_winner_reward,
)


@receiver(post_save, sender=LotteryModel)
def calculate_total_accumulated_on_batch(sender, instance: LotteryModel, created, **kwargs):
    # generate ticket pin
    # print("Lottery Model generate_ticket_pin generate ticket pin", "\n\n\n\n\n")
    # LotteryModel().generate_ticket_pin(instance.game_play_id)

    get_batch = instance.batch
    # get_batch.total_accumulated_paid = list(LotteryModel.objects.filter(Q(paid=True) & Q(
    # batch=get_batch)).aggregate(Sum('stake_amount')).values())[0]
    get_batch.total_accumulated_paid = LotteryBatch.batch_paid_amount(batch=get_batch)
    get_batch.total_accumulated_unpaid = list(
        LotteryModel.objects.filter(Q(paid=False) & Q(batch=get_batch)).aggregate(Sum("stake_amount")).values()
    )[0]
    get_batch.total_accumulated_all = list(LotteryModel.objects.filter(batch=get_batch).aggregate(Sum("stake_amount")).values())[0]
    get_batch.total_players_in_pool = get_batch.lottery_players.count()
    # get_batch.total_unique_players_in_pool = LotteryModel.objects.filter(Q(paid=True) & Q(
    # unique_id__isnull=False)).distinct("unique_id").count()
    get_batch.total_unique_paid_playyers_in_pool = (
        get_batch.lottery_players.filter(Q(paid=True) & Q(unique_id__isnull=False)).distinct("unique_id").count()
    )
    get_batch.total_unique_unpaid_players_in_pool = get_batch.lottery_players.filter(unique_id__isnull=False).distinct("unique_id").count()
    get_batch.save()

    # ---------------------------Jackpot draw --------------------------------

    if instance.paid is True:
        print("running jackpot draw................................................................")

        jackpot = Jackpot.is_available()
        available_to = ConstantVariable.jackpot_available_channel()

        print(available_to, instance.channel)
        # print("INSTANCE DATE:", instance.paid_date.date())

        if jackpot is not None and instance.paid_date is not None and available_to == instance.channel:
            # print("JACKPOT INSTANCE DATE:", jackpot.updated_at.date())

            lotto_winner_ins = LotteryWinnersTable.objects.filter(
                agent=instance.agent_profile, won_jackpot=True, jackpot=jackpot
            )  # to get existing winner

            jackpot_winning = JackpotWinning(game_instance=instance, lotto_winner_ins=lotto_winner_ins)
            run_jackpot_decisioning = jackpot_winning.draw()

            if run_jackpot_decisioning.get("jackpot") is True:
                print("jackpot winner ----------------------------------------------------------------")
                # print(available_to, instance.paid_date)

                batch = LotteryBatch.objects.filter(id=instance.batch.id).last()
                amount_won = run_jackpot_decisioning.get("amount")

                run_batch_id = generate_game_play_id()

                LotteryWinnersTable.objects.create(
                    lottery=instance,
                    batch=batch,
                    jackpot=jackpot,
                    run_batch_id=run_batch_id,
                    phone_number=instance.user_profile.phone_number,
                    playyer_id=instance.user_profile.id,
                    unnique_id=instance.unique_id,
                    game_play_id=instance.game_play_id,
                    ticket=instance.lucky_number,
                    win_type="JACKPOT_WINNER",
                    pool=instance.pool,
                    stake_amount=instance.stake_amount,
                    earning=amount_won,
                    lottery_source_tag=instance.channel,
                    won_jackpot=True,
                )

                # check if the play is ussd. if yes, # save amount to wallet
                if instance.channel != "POS_AGENT":
                    ussd_lottery_winner_reward.delay(
                        user_id=instance.user_profile.id,
                        amount=amount_won,
                        trans_from="WYSE_CASH_GAME_WIN",
                        played_via_telco_channel=instance.played_via_telco_channel,
                    )
                JackpotWinner.objects.create(user=instance.user_profile, jackpot=jackpot, amount=amount_won)

                print(run_jackpot_decisioning, "-------------------")
                jackpot_winning.toggle_drawn_status(
                    run_jackpot_decisioning=run_jackpot_decisioning,
                    jackpot_instance=jackpot,
                    amount_won=amount_won,
                )

                # add event to engage
                # winner_engange_event.delay(
                #     user_id=instance.user_profile.id,
                #     event_name=f"INSTANT CASHOUT GAME WINNER",
                #     is_user_profile_id=True,
                # )

            else:
                print("jackpot available")

        else:
            print("jackpot not available")


# @receiver(post_save, sender=InstantCashoutPendingWinning)
# def house_play_switch_rtp_threshold(
#     sender, instance: InstantCashoutPendingWinning, created, **kwargs
# ):
#     """HERE THE SYSTEM CALCULATES THE WINNING AND USES THAT VALUE TO VARY THE RTP"""

#     house_play_switch_rtp.apply_async(queue="icash_new")

#     return "DEPLOYED"


@receiver(post_save, sender=LottoTicket)
def calculate_total_accumulated_on_batch(sender, instance: LottoTicket, created, **kwargs):  # noqa
    get_batch = LotteryBatch.objects.get(id=instance.batch.id)

    LottoTicket().generate_random_system_pick_number_for_instanct_cashout_tickets(instance.game_play_id)

    # generate ticket pin
    LottoTicket().generate_ticket_pin(instance.game_play_id)

    # check if this lotto ticket  is instant_cashout and has been paid for
    if (instance.paid is True) and (
        (instance.lottery_type == "INSTANT_CASHOUT") or (instance.lottery_type == "VIRTUAL_SOCCER") or (instance.lottery_type == "QUIKA")
    ):
        # check if this lotto ticket won

        stake_amount = LottoTicket.objects.filter(
            game_play_id=instance.game_play_id,
        ).aggregate(
            Sum("stake_amount")
        )["stake_amount__sum"]

        # make_bonus_winning_available_for current play
        # make_bonus_winning_available_for current play
        bonus_winnings = InstantCashoutPendingWinning.objects.filter(stake_amount=999).first()
        if bonus_winnings:
            bonus_winnings.stake_amount = instance.stake_amount
            bonus_winnings.save()
        # make_bonus_winning_available_for current play
        # make_bonus_winning_available_for current play

        instant_cashout_pending_winning_object = InstantCashoutPendingWinning.objects.filter(
            stake_amount=stake_amount,
            is_avialable=True,
            bonus_target_agent=instance.agent_profile,
            is_for_telco_only=False,
            pending=False,
        ).last()

        print("#")
        print("AT AGENT WINNING:::", instant_cashout_pending_winning_object)
        print("#")
        instance.amount_paid = int(instance.amount_paid)

        if not instant_cashout_pending_winning_object:
            instant_cashout_pending_winning_object = InstantCashoutPendingWinning.objects.filter(
                stake_amount=stake_amount,
                is_avialable=True,
                bonus_target_agent__isnull=True,
                is_for_telco_only=False,
                pending=False,
            ).last()
            print("#")
            print("AT OPEN WINNING:::", instant_cashout_pending_winning_object)
            print("#")
        elif (instance.played_via_telco_channel) and (not instant_cashout_pending_winning_object):
            instant_cashout_pending_winning_object = InstantCashoutPendingWinning.objects.filter(
                stake_amount=stake_amount,
                is_avialable=True,
                bonus_target_agent=instance.agent_profile,
                pending=False,
            ).last()
            print("#")
            print("AT TELCO WINNING:::", instant_cashout_pending_winning_object)
            print("#")
        elif (instance.played_via_telco_channel) and (not instant_cashout_pending_winning_object):
            instant_cashout_pending_winning_object = InstantCashoutPendingWinning.objects.filter(
                stake_amount=stake_amount,
                is_avialable=True,
                is_for_telco_only=True,
                pending=False,
            ).last()

        if ((instance.amount_paid == 50) or (instance.amount_paid == 100)) and (not instant_cashout_pending_winning_object):
            instant_cashout_pending_winning_object = InstantCashoutPendingWinning.objects.filter(
                stake_amount=instance.stake_amount,
                bonus_target_agent__isnull=True,
                is_avialable=True,
                is_avail_50_n_100=True,
                pending=False,
            ).last()

        if instant_cashout_pending_winning_object:
            InstantCashoutPendingWinning.objects.filter(id=instant_cashout_pending_winning_object.id).update(is_avialable=False)
            if LottoWinners.objects.filter(
                game_play_id=instance.game_play_id,
                phone_number=instance.user_profile.phone_number,
            ).exists():
                print("PASSED BECAUSE IT EXISTS.!!!!!!!!!!!!!!!!")
                print("PASSED BECAUSE IT EXISTS.!!!!!!!!!!!!!!!!")
                print("PASSED BECAUSE IT EXISTS.!!!!!!!!!!!!!!!!")
                print("PASSED BECAUSE IT EXISTS.!!!!!!!!!!!!!!!!")
            else:
                print("ENTERING BECAUSE IT DOESN'T EXIST.!!!!!!!!!!!!!!!!")
                print("ENTERING BECAUSE IT DOESN'T EXIST.!!!!!!!!!!!!!!!!")
                instant_cashout_pending_winning_object.is_avialable = False
                instant_cashout_pending_winning_object.is_available_for_pos = False
                instant_cashout_pending_winning_object.claimant = instance
                instant_cashout_pending_winning_object.claimant_channel = instance.channel
                instant_cashout_pending_winning_object.is_avail_50_n_100 = False
                instant_cashout_pending_winning_object.save()

                win_type = "PERM_4"

                # print(f"""

                #       instant_cashout_pending_winning_object.tier: {instant_cashout_pending_winning_object.tier}

                #       """)

                if instant_cashout_pending_winning_object.tier == "least_win":
                    win_type = "PERM_0"
                    matches_required = 0

                elif instant_cashout_pending_winning_object.tier == "minor_win":
                    win_type = "PERM_1"
                    matches_required = 1

                elif instant_cashout_pending_winning_object.tier == "mid_win":
                    win_type = "PERM_2"
                    matches_required = 2

                elif instant_cashout_pending_winning_object.tier == "min_win":
                    win_type = "PERM_3"
                    matches_required = 3

                elif instant_cashout_pending_winning_object.tier == "max_win":
                    win_type = "PERM_4"
                    matches_required = 4

                # print(f"""

                #       win_type: {win_type}
                #       \n\n\n\n\n\n\n

                #       """)

                print("CHECKING IF PENDING WINNING IS AVAILABLE.!!!!!!!!!!!!!!!!")
                instant_cashout_pending_winning_object.refresh_from_db()
                if instant_cashout_pending_winning_object.is_avialable is False:
                    # GENERATING SYSTEM PICK FOR INSTANT CASHOUT TICKETS #
                    print("PENDING WINNING IS AVAILABLE.!!!!!!!!!!!!!!!!")
                    ticket_qs = LottoTicket.objects.filter(
                        user_profile__phone_number=instance.user_profile.phone_number,
                        game_play_id=instance.game_play_id,
                        lottery_type=instance.lottery_type,
                    )
                    ticket_data = []
                    if ticket_qs:
                        for ticket in ticket_qs:
                            _ticket = [int(i) for i in ticket.ticket.split(",")]
                            ticket_data.append(_ticket)

                    generated_number = find_num(
                        numbers=ticket_data,
                        matches_required=matches_required,
                        log=False,
                    )

                    # TICKET DATA:: [[27, 0, 0, 0]]
                    # MATCHES REQUIRED:: 4

                    print("TICKET DATA::", ticket_data)
                    print("MATCHES REQUIRED::", matches_required)
                    print("GENERATED NUMBER::", generated_number)

                    # GENERATING SYSTEM PICK FOR INSTANT CASHOUT TICKETS #

                    if len(generated_number) > 0:
                        ",".join([str(i) for i in generated_number])

                        # ticket_qs.update(system_generated_num=system_pick_number)

                        get_batch = LotteryBatch.objects.filter(id=instance.batch.id).last()

                        if LottoWinners.objects.filter(game_play_id__iexact=instance.game_play_id).exists():
                            pass
                        else:
                            LottoWinners.create_lotto_winner_obj(
                                batch=get_batch,
                                phone_number=instance.phone,
                                ticket=instance.ticket.split(","),
                                win_type="ORDINARY_WINNER",
                                match_type=win_type,
                                lotto_type=instance.lottery_type,
                                game_play_id=instance.game_play_id,
                                stake_amount=instance.stake_amount,
                                earning=instant_cashout_pending_winning_object.amount,
                                channel_played_from=instance.channel,
                                run_batch_id=instant_cashout_pending_winning_object.batch,
                                lottery=instance,
                                win_flavour=instant_cashout_pending_winning_object.win_flavour,
                                instant_cashout_pending_instance=instant_cashout_pending_winning_object,
                                played_via_telco_channel=instance.played_via_telco_channel,
                            )

                            # notify agents
                            try:
                                notify_agents_on_lottery_batch_draw(
                                    batch_id=instance.batch.id,
                                    lottery_type="INSTANT_CASHOUT",
                                    phone_number=instance.user_profile.phone_number,
                                )
                            except Exception:
                                pass

                            # close old instant cashout batch and create a new one
                            old_batch = LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT", is_active=True).last()

                            if old_batch:
                                old_batch.is_active = False
                                old_batch.draw_date = datetime.now()
                                old_batch.save()

                            LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT", is_active=True)
                    else:
                        print("CHECK IF PENDING WINNING IS AVAILABLE FAILED.!!!!!!!!!!!!!!!!")
                        if instance.channel == "USSD":
                            # send sms to player
                            if instance.played_via_telco_channel is False:
                                celery_sms_for_instant_cash_lost_ticket(
                                    phone_number=instance.user_profile.phone_number,
                                    ticket=instance.system_generated_num,
                                    game_play_id=instance.game_play_id,
                                )

                            else:
                                pass
        else:
            if instance.channel == "USSD":
                if instance.played_via_telco_channel is False:
                    celery_sms_for_instant_cash_lost_ticket.apply_async(
                        kwargs={
                            "phone_number": instance.user_profile.phone_number,
                            "ticket": instance.system_generated_num,
                            "game_play_id": instance.game_play_id,
                        },
                        countdown=190,
                    )
                else:
                    instant_cashout_lost_sms_on_telco.apply_async(
                        queue="telcocharge",
                        kwargs={
                            "phone": instance.user_profile.phone_number,
                            "ticket_num": instance.system_generated_num,
                            "game_play_id": instance.game_play_id,
                            "instance_id": instance.id,
                        },
                        countdown=190,
                    )

    # --------------------------------------------------------------------------------------------------------------
    # --------------------------------------------------------------------------------------------------------------
    # ----------------------------------INSTANT CASH SUPER JACKPOT--------------------------------------------------
    # --------------------------------------------------------------------------------------------------------------
    # --------------------------------------------------------------------------------------------------------------

    if instance.paid is True and instance.lottery_type == "INSTANT_CASHOUT":  # super jackpot draw
        jackpot_instance = Jackpot.is_available()
        available_to = ConstantVariable.jackpot_available_channel()

        print("JACKPOT VARIABLES")
        print(jackpot_instance, instance.updated_at, available_to)
        print("JACKPOT VARIABLES")

        if jackpot_instance is not None and instance.updated_at is not None and available_to == instance.channel:
            # print("JACKPOT AVAILABLE")
            # print("JACKPOT AVAILABLE")
            print("JACKPOT AVAILABLE")

            # lotto_jackpot_winner = LottoWinners.objects.filter(
            #     agent=instance.agent_profile,
            #     won_jackpot=True,
            #     win_type="JACKPOT_WINNER",
            #     jackpot=jackpot_instance,
            # )

            # print("EXISTING WINNING COUNT", lotto_jackpot_winner)
            # print("EXISTING WINNING COUNT", lotto_jackpot_winner.count())

            # if not lotto_jackpot_winner.exists():
            if True:  # NOW ALLOW ALL TO GET THE JACKPOTS IF OR NOT GOTTEN BEFORE.
                const_var_instance = ConstantVariable.objects.last()
                jackpot_amount = const_var_instance.i_cash_jackpot_winning_amount

                new_jackpot_instance = Jackpot.objects.annotate(
                    contributed_amount_float=Cast('contributed_amount', FloatField())
                ).get(id=jackpot_instance.id)

                jackpot_instance = new_jackpot_instance

                super_jackpot_winning_balance = jackpot_instance.contributed_amount

                if super_jackpot_winning_balance >= jackpot_amount:
                    jackpot_query_set = Jackpot.objects.filter(id=jackpot_instance.id)
                    is_drawn = True if (jackpot_instance.contributed_amount - jackpot_amount) < jackpot_amount else False
                    print(is_drawn, "--------------------\n")
                    # jackpot_query_set.update(
                    #     contributed_amount=F("contributed_amount") - jackpot_amount,
                    #     total_amount_given_out=F("total_amount_given_out") + jackpot_amount,
                    #     is_drawn=is_drawn,
                    #     give_out_count=F("give_out_count") - 1,
                    # )
                    batch = LotteryBatch.objects.filter(id=instance.batch.id).last()

                    LottoWinners.create_lotto_winner_obj(
                        batch=batch,
                        lottery=instance,
                        agent=instance.agent_profile,
                        jackpot_instance=jackpot_instance,
                        run_batch_id=None,
                        phone_number=instance.phone,
                        game_play_id=instance.game_play_id,
                        ticket=instance.ticket.split(","),
                        win_type="JACKPOT_WINNER",
                        lotto_type=instance.lottery_type,
                        stake_amount=instance.stake_amount,
                        earning=jackpot_amount,
                        channel_played_from=instance.channel,
                        jackpot=True,
                        match_type="JACKPOT",
                    )

                    print("JACKPOT AMOUNT WON:", jackpot_amount)
                    # notify agents
                    notify_agents_on_lottery_batch_draw.delay(
                        batch_id=instance.batch.id,
                        lottery_type=instance.lottery_type,
                        phone_number=instance.user_profile.phone_number,
                    )

                    # send sms to winner
                    if instance.channel == "USSD":
                        celery_sms_for_instant_cash_winners.apply_async(
                            kwargs={
                                "phone_number": instance.user_profile.phone_number,
                                "amount_Won": jackpot_amount,
                                "winning_no": instance.ticket,
                                "game_play_id": instance.game_play_id,
                            },
                            countdown=190,
                        )
                    JackpotWinner.objects.create(
                        user=instance.user_profile,
                        jackpot=jackpot_instance,
                        game_id=instance.game_play_id,
                        amount=jackpot_amount,
                    )

        # --------------------------------------------------------------------------------------------------------------
        # --------------------------------------------------------------------------------------------------------------
        # --------------------------------INSTANT CASH SUPER JACKPOT TO ALL CHANNELS------------------------------------
        # --------------------------------------------------------------------------------------------------------------
        # --------------------------------------------------------------------------------------------------------------

        if jackpot_instance is not None and instance.updated_at is not None and available_to == "ALL":
            # print("JACKPOT AVAILABLE")
            # print("JACKPOT AVAILABLE")
            # print("JACKPOT AVAILABLE")

            lotto_jackpot_winner = LottoWinners.objects.filter(
                agent=instance.agent_profile,
                won_jackpot=True,
                win_type="JACKPOT_WINNER",
                jackpot=jackpot_instance,
            )

            # print("EXISTING WINNING COUNT", lotto_jackpot_winner.count())
            # print("EXISTING WINNING COUNT", lotto_jackpot_winner.count())

            if not lotto_jackpot_winner.exists():
                const_var_instance = ConstantVariable.objects.last()
                jackpot_amount = const_var_instance.i_cash_jackpot_winning_amount

                super_jackpot_winning_balance = jackpot_instance.contributed_amount

                if super_jackpot_winning_balance >= jackpot_amount:
                    jackpot_query_set = Jackpot.objects.filter(id=jackpot_instance.id)
                    is_drawn = True if (jackpot_instance.contributed_amount - jackpot_amount) < jackpot_amount else False
                    # print(is_drawn,"--------------------\n")
                    jackpot_query_set.update(
                        contributed_amount=F("contributed_amount") - jackpot_amount,
                        total_amount_given_out=F("total_amount_given_out") + jackpot_amount,
                        is_drawn=is_drawn,
                        give_out_count=F("give_out_count") - 1,
                    )
                    batch = LotteryBatch.objects.filter(id=instance.batch.id).last()

                    LottoWinners.create_lotto_winner_obj(
                        batch=batch,
                        lottery=instance,
                        agent=instance.agent_profile,
                        jackpot_instance=jackpot_instance,
                        run_batch_id=None,
                        phone_number=instance.phone,
                        game_play_id=instance.game_play_id,
                        ticket=instance.ticket.split(","),
                        win_type="JACKPOT_WINNER",
                        lotto_type=instance.lottery_type,
                        stake_amount=instance.stake_amount,
                        earning=jackpot_amount,
                        channel_played_from=instance.channel,
                        jackpot=True,
                        match_type="JACKPOT",
                    )

                    # print("JACKPOT AMOUNT WON:", jackpot_amount)
                    # notify agents
                    notify_agents_on_lottery_batch_draw.delay(
                        batch_id=instance.batch.id,
                        lottery_type=instance.lottery_type,
                        phone_number=instance.user_profile.phone_number,
                    )

                    # send sms to winner
                    if instance.channel == "USSD":
                        celery_sms_for_instant_cash_winners.apply_async(
                            kwargs={
                                "phone_number": instance.user_profile.phone_number,
                                "amount_Won": jackpot_amount,
                                "winning_no": instance.ticket,
                                "game_play_id": instance.game_play_id,
                            },
                            countdown=190,
                        )
                    JackpotWinner.objects.create(
                        user=instance.user_profile,
                        jackpot=jackpot_instance,
                        game_id=instance.game_play_id,
                        amount=jackpot_amount,
                    )


# @receiver(post_save, sender=UserProfile)
# def create_referral_code(sender, instance: UserProfile, created, **kwargs):
#     if created:
#         ReferralCode.create_referal_code(instance)

#         # create wallet for user
#         create_woven_virtual_wallet.delay(instance.id)


# @receiver(post_save, sender=LottoTicket)
# def draw_instant_cash(sender, instance: LottoTicket, created, **kwargs):
#     if instance.seeder_status == "COMPLETE":
#         if (
#             instance.lottery_type in ["INSTANT_CASHOUT", "QUIKA"]
#             and instance.paid is True
#             and not instance.icash_2_counted
#             and not instance.icash_counted
#             and instance.instant_cashout_drawn is False
#         ):
#             # if instance.played_via_telco_channel:
#             #     quika_new_icash_local.apply_async(args=(instance.id,), queue="icash_new")
#             #     pass
#             # else:
#             #     quika_new_icash_local.apply_async(args=(instance.id,), queue="icash_new")

#             quika_new_icash_local.apply_async(args=(instance.id,), queue="icash_new")

#             # quika_new_icash_local(instance.id)
#             return "DEPLOYED"

#     print("FINISHECALLED")
#     return "DUPLICATE"


# @receiver(post_save, sender=LottoTicket)
def update_switch_rtp_threshold(sender, instance: LottoTicket, created, **kwargs):
    if instance.paid:
        switch_rtp.delay(
            log=True,
            amount=instance.amount_paid,
            rto=instance.rto,
        )

        return "DEPLOYED"

    print("FINISHECALLED")
    return "DUPLICATE"


# @receiver(post_save, sender=LottoTicket)
def handle_new_icash(sender, instance: LottoTicket, created, **kwargs):
    const_obj = ConstantVariable.objects.all().last()

    WINNINGS = {
        150: {
            "min_win": 4500 / const_obj.icash_winnings_divisor,
            "mid_win": 5400 / const_obj.icash_winnings_divisor,
            "max_win": 11250 / const_obj.icash_winnings_divisor,
        },
        300: {
            "min_win": 7500 / const_obj.icash_winnings_divisor,
            "mid_win": 9000 / const_obj.icash_winnings_divisor,
            "max_win": 13500 / const_obj.icash_winnings_divisor,
        },
        450: {
            "min_win": 9000 / const_obj.icash_winnings_divisor,
            "mid_win": 10800 / const_obj.icash_winnings_divisor,
            "max_win": 15750 / const_obj.icash_winnings_divisor,
        },
        600: {
            "min_win": 10800 / const_obj.icash_winnings_divisor,
            "mid_win": 13000 / const_obj.icash_winnings_divisor,
            "max_win": 18000 / const_obj.icash_winnings_divisor,
        },
        750: {
            "min_win": 12000 / const_obj.icash_winnings_divisor,
            "mid_win": 14000 / const_obj.icash_winnings_divisor,
            "max_win": 18750 / const_obj.icash_winnings_divisor,
        },
        900: {
            "min_win": 13500 / const_obj.icash_winnings_divisor,
            "mid_win": 16000 / const_obj.icash_winnings_divisor,
            "max_win": 22500 / const_obj.icash_winnings_divisor,
        },
        1000: {
            "min_win": 15000 / const_obj.icash_winnings_divisor,
            "mid_win": 18000 / const_obj.icash_winnings_divisor,
            "max_win": 25000 / const_obj.icash_winnings_divisor,
        },
    }
    const_obj = ConstantVariable.objects.all().first()
    averages = {
        150: 1360.00,
        300: 2033.33,
        450: 2420.00,
        600: 2866.67,
        750: 3100.00,
        900: 3544.44,
        1000: 3977.78,
    }

    if instance.lottery_type == "INSTANT_CASHOUT":
        if instance.paid and instance.rtp > 0 and instance.is_agent:
            agent = instance.agent_profile
            agent.icash_sold = agent.icash_sold + 1
            instance.icash_2_counted = True
            instance.icash_counted = True

            actual_stake_amount = instance.stake_amount * instance.number_of_ticket
            tickets_per_teir = instance.number_of_ticket

            if averages.get(actual_stake_amount) is None:
                return None
            band_average = averages[actual_stake_amount]
            rtp = instance.rtp * instance.number_of_ticket

            num_win_tickets = rtp / band_average
            print(
                "NUM WINNING TICKS ::::",
                num_win_tickets,
                instance.number_of_ticket,
                rtp,
                band_average,
            )
            percent_win_ticket = num_win_tickets / instance.number_of_ticket

            count_before = (1 / percent_win_ticket) / instance.number_of_ticket

            print("BAND RTP ::::", rtp, "BAND RTP ::::", count_before)
            print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

            if not agent.icash_sold_dict.get(str(actual_stake_amount)):
                agent.icash_sold_dict[str(actual_stake_amount)] = 0

            if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
                const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

            agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) + 1
            agent.save()

            const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + 1
            const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + 1
            )

            win_rank_ratio = ["min_win"] + ["mid_win"] + ["max_win"]
            win_rank_choice = random.choice(win_rank_ratio)

            if const_obj.icash_to_use == "NEW ICASH":
                const_obj.save()

                if (
                    (const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir) >= count_before
                ) and const_obj.icash2_draw_mode == "GLOBAL":
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            WINNINGS[actual_stake_amount][win_rank_choice],
                        ),
                        count_before=count_before,
                    )
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

                    const_obj.save()

                if ((agent.icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir) >= count_before) and const_obj.icash2_draw_mode == "LOCAL":
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            WINNINGS[actual_stake_amount][win_rank_choice],
                        ),
                        count_before=count_before,
                    )
                    agent.icash_sold_dict[str(actual_stake_amount)] = 0

                    agent.save()

            ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
            number_of_single_tickets_in_largest_ticket = random.randint(int(ticket_min), int(ticket_max))

            if (agent.icash_sold / number_of_single_tickets_in_largest_ticket) >= const_obj.icash2_local_bonus_threshold:
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")

                if agent_tickets.exists():
                    real_play_bands = [int(amount * qty) for amount, qty in list(agent_tickets)]
                    bonus_band = int(random.choice(real_play_bands))

                    if WINNINGS.get(bonus_band) is None:
                        pass
                    else:
                        const_obj = ConstantVariable.objects.all().first()
                        const_obj.icash2_local_bonus_available -= WINNINGS[bonus_band]["min_win"]

                        if not const_obj.icash2_local_bonus_available <= 0:
                            agent.icash_sold = 0
                            agent.save()

                            InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                                f"BONUS-{datetime.now()}",
                                (
                                    "min_win",
                                    bonus_band,
                                    WINNINGS[bonus_band]["min_win"],
                                ),
                                agent,
                                count_before=number_of_single_tickets_in_largest_ticket,
                            )

            if (const_obj.global_agent_icash_sold / number_of_single_tickets_in_largest_ticket) >= const_obj.icash2_local_bonus_threshold:
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")
                if agent_tickets.exists():
                    real_play_bands = [int(amount * qty) for amount, qty in list(agent_tickets)]

                    bonus_band = int(random.choice(real_play_bands))

                    if WINNINGS.get(bonus_band) is None:
                        return None

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_global_bonus_available -= WINNINGS[bonus_band]["min_win"]
                    const_obj.global_agent_icash_sold = 0

                    if not const_obj.icash2_global_bonus_available < 0:
                        const_obj.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            f"GLOB-BONUS-{datetime.now()}",
                            ("min_win", bonus_band, WINNINGS[bonus_band]["min_win"]),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                        )


def round_down(num):
    return num // 100 * 100


def winnings(const_obj):
    data = {
        200: {
            "least_win": 250,
            "minor_win": 650,
            "min_win": round_down(4500 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(5400 / const_obj.icash_winnings_divisor),
            "max_win": round_down(11250 / const_obj.icash_winnings_divisor),
        },
        500: {
            "least_win": 1000,
            "minor_win": 1250,
            "min_win": round_down(7000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(9000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(15200 / const_obj.icash_winnings_divisor),
        },
        750: {
            "least_win": 1400,
            "minor_win": 1700,
            "min_win": round_down(9000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(10800 / const_obj.icash_winnings_divisor),
            "max_win": round_down(15750 / const_obj.icash_winnings_divisor),
        },
        800: {
            "least_win": 2000,
            "minor_win": 2250,
            "min_win": round_down(11400 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(13500 / const_obj.icash_winnings_divisor),
            "max_win": round_down(18000 / const_obj.icash_winnings_divisor),
        },
        1250: {
            "least_win": 1250,
            "minor_win": 1950,
            "min_win": round_down(12000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(14000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(18750 / const_obj.icash_winnings_divisor),
        },
        1300: {
            "least_win": 1300,
            "minor_win": 1700,
            "min_win": round_down(13500 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(15000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(25000 / const_obj.icash_winnings_divisor),
        },
        1400: {
            "least_win": 1500,
            "minor_win": 1800,
            "min_win": round_down(15000 / const_obj.icash_winnings_divisor),
            "mid_win": round_down(18000 / const_obj.icash_winnings_divisor),
            "max_win": round_down(27000 / const_obj.icash_winnings_divisor),
        },
    }
    return data


# # @receiver(post_save, sender=LottoTicket)
# def quika_icash(sender, instance: LottoTicket, created, **kwargs):
#     fields_to_exclude = {'system_generated_num', }
#     # automatically populate a list with all fields, except the ones you want to exclude
#     fields_to_update = [f.name for f in LottoTicket._meta.get_fields() if
#                         f.name not in fields_to_exclude and not f.auto_created]

#     from math import ceil
#     from django.conf import settings

#     if settings.DEBUG:
#         # DEFAULT_AGENT_FOR_WEB = 230
#         DEFAULT_AGENT_FOR_WEB = 1

#     else:
#         DEFAULT_AGENT_FOR_WEB = settings.DEFAULT_AGENT_ID

#     if not instance.paid: return

#     if not instance.agent_profile:
#         instance.is_agent = True
#         agent_profile = Agent.objects.filter(id=DEFAULT_AGENT_FOR_WEB)
#         instance.agent_profile = agent_profile.last()
#         instance.save()

#     if instance.icash_2_counted or not instance.is_agent: return

#     if instance.lottery_type not in ["INSTANT_CASHOUT", "QUIKA"]: return

#     WIN_GIVEN_OUT = False
#     const_obj: ConstantVariable = ConstantVariable.objects.all().last()

#     if const_obj.icash_to_use != "NEW ICASH":
#         return

#     # def round_down(num):
#     #     return num//100 * 100

#     WHITE_WINNINGS = winnings(const_obj=const_obj)

#     BLACK_WINNINGS = {
#         200: {
#             "least_win": 250,
#             "minor_win": 450,
#             "min_win": round_down(4500 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "mid_win": round_down(5400 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "max_win": 1400,
#         },
#         500: {
#             "least_win": 600,
#             "minor_win": 750,
#             "min_win": round_down(7500 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "mid_win": round_down(9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "max_win": round_down(13500 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#         },
#         750: {
#             "least_win": 1125,
#             "minor_win": 1200,
#             "min_win": round_down(9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "mid_win": round_down(10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "max_win": round_down(15750 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#         },
#         1000: {
#             "least_win": 500,
#             "minor_win": 1000,
#             "min_win": round_down(10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "mid_win": round_down(13000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "max_win": round_down(18000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#         },
#         1250: {
#             "least_win": 1250,
#             "minor_win": 1450,
#             "min_win": round_down(12000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "mid_win": round_down(14000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "max_win": round_down(18750 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#         },
#         1300: {
#             "least_win": 1300,
#             "minor_win": 1500,
#             "min_win": round_down(13500 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "mid_win": round_down(16000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#             "max_win": round_down(22500 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#         },
#         1500: {
#             "least_win": 1300,
#             "minor_win": 1700,
#             "min_win": 2100,
#             "mid_win": 2600,
#             "max_win": round_down(25000 / (const_obj.icash_winnings_divisor * 1.666666666666667)),
#         },
#     }

#     # {"200": 0, "500": 0, "750": 0, "1000": 0, "1250": 0, "1300": 0, "1500": 0}
#     actual_stake_amount = str(int(instance.stake_amount * instance.number_of_ticket))

#     agent = instance.agent_profile

#     agent_flavour_list = agent.icash_flavour_dict.get(str((actual_stake_amount)), [])
#     global_flavour_list = const_obj.icash_flavour_dict.get(
#         str((actual_stake_amount)), []
#     )

#     const_obj = ConstantVariable.objects.all().last()

#     if not len(agent_flavour_list):
#         agent.icash_flavour_dict[
#             str((actual_stake_amount))
#         ] = const_obj.build_flavour_list()
#         agent.save()

#     if not len(global_flavour_list):
#         const_obj.icash_flavour_dict[
#             str((actual_stake_amount))
#         ] = const_obj.build_flavour_list()
#         const_obj.save()

#     agent_current_flavour = agent.icash_flavour_dict[str((actual_stake_amount))][0]
#     global_current_flavour = const_obj.icash_flavour_dict[str((actual_stake_amount))][0]

#     agent.icash_flavour_dict[str((actual_stake_amount))] = agent.icash_flavour_dict[
#                                                                str((actual_stake_amount))
#                                                            ][1:]
#     const_obj.icash_flavour_dict[
#         str((actual_stake_amount))
#     ] = const_obj.icash_flavour_dict[str((actual_stake_amount))][1:]

#     new_agent_dict = dict(agent.icash_flavour_dict)
#     new_global_dict = dict(const_obj.icash_flavour_dict)

#     GIVER_RANGE1, GIVER_RANGE2 = const_obj.giver_threshold.split(",")

#     # giver_threshold = InstantCashGiverThresholds.objects.first()
#     # giver_threshold.GIVER_THRESHOLD_DICT[actual_stake_amount] = F(GIVER_THRESHOLD_DICT[actual_stake_amount]) + 1
#     # giver_threshold.save()

#     const_obj = ConstantVariable.objects.all().last()
#     global_count_to_giver = const_obj.new_quika_icash_count_to_giver[str(actual_stake_amount)]
#     agent_count_to_giver = agent.new_quika_icash_count_to_giver[str(actual_stake_amount)]

#     GIVER_THRESHOLD = random.randint(int(GIVER_RANGE1), int(GIVER_RANGE2))

#     # GIVER_THRESHOLD = GIVER_THRESHOLD_DICT[str(actual_stake_amount)]

#     WINNINGS_DICT = {"WHITE": WHITE_WINNINGS, "BLACK": BLACK_WINNINGS}

#     GLOBAL_WINNINGS = WINNINGS_DICT[global_current_flavour]
#     AGENT_LOCAL_WINNINGS = WINNINGS_DICT[agent_current_flavour]

#     BONUS_FLAVOUR = random.choice(const_obj.icash_bonus_bias_dict["flavours"])
#     BONUS_WINNING = WINNINGS_DICT[BONUS_FLAVOUR]
#     BONUS_TIER = random.choice(const_obj.icash_bonus_bias_dict["tiers"])

#     print(BONUS_FLAVOUR, BONUS_TIER, const_obj.icash_bonus_bias_dict["tiers"])

#     const_obj = ConstantVariable.objects.all().first()

#     def get_averages(band):
#         values = (
#                 [band["least_win"]] * 5
#                 + [band["minor_win"]] * 4
#                 + [band["min_win"]] * 4
#                 + [band["mid_win"]] * 4
#                 + [band["max_win"]] * 3
#         )
#         return sum(values)

#     if instance.lottery_type == "INSTANT_CASHOUT" or instance.lottery_type == "QUIKA":
#         if instance.paid and instance.rtp > 0 and instance.is_agent:
#             print("HERE TOO..!!!!!!")

#             agent = instance.agent_profile
#             agent.icash_sold = agent.icash_sold + 1
#             instance.icash_2_counted = True
#             instance.icash_counted = True
#             instance.save(update_fields=fields_to_update)

#             tickets_per_teir = instance.number_of_ticket

#             band_average = get_averages(GLOBAL_WINNINGS[int(actual_stake_amount)])
#             rtp = instance.rtp * instance.number_of_ticket

#             print('\n BAND Average +++++++++++++++++++++++++++++++++++++++++++++++++>', band_average,
#                   '<+++++++++++++++++++++++++++++++++++++++++++++++++\n')

#             num_win_tickets = rtp / band_average
#             print(
#                 "NUM WINNING TICKS ::::",
#                 num_win_tickets,
#                 "\n",
#                 f"Inst-RTP::{instance.rtp}\n",
#                 f"Num tiks{instance.number_of_ticket}\n",
#                 f"Tot RTP{rtp}\n",
#                 band_average,
#                 "\n",
#                 GLOBAL_WINNINGS[int(actual_stake_amount)],
#             )
#             percent_win_ticket = num_win_tickets / instance.number_of_ticket

#             count_before = (1 / percent_win_ticket) / instance.number_of_ticket
#             icash_excesses_from_count_before = (
#                                                        instance.number_of_ticket * instance.rtp
#                                                ) * (ceil(count_before) - count_before)
#             print("EXCESSES FROM CB4 ::::", icash_excesses_from_count_before)

#             print("BAND RTP ::::", rtp, "COUNT BEF ::::", count_before)
#             print(
#                 "A-FLAVOUR ::::",
#                 agent_current_flavour,
#                 "G-FLAVOUR BEF ::::",
#                 global_current_flavour,
#             )
#             print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

#             if not agent.icash_sold_dict.get(str(actual_stake_amount)):
#                 agent.icash_sold_dict[str(actual_stake_amount)] = 0

#             if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
#                 const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

#             agent.icash_sold_dict[str(actual_stake_amount)] = (
#                     agent.icash_sold_dict.get(str(actual_stake_amount)) + 1
#             )

#             const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + 1
#             const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                     const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + 1
#             )

#             win_rank_ratio = ["least_win"] * 5 + ["minor_win"] * 4 + ["min_win"] * 4 + ["mid_win"] * 4 + ["max_win"] * 3
#             win_rank_choice = random.choice(win_rank_ratio)

#             if const_obj.icash_to_use == "NEW ICASH":
#                 const_obj.save()
#                 agent.save()

#                 if (
#                         global_count_to_giver >= GIVER_THRESHOLD
#                         and const_obj.icash2_draw_mode == "GLOBAL"
#                 ):
#                     # Handle giver for GLOBAL
#                     print("HERE 1..!!!")

#                     const_obj.global_agent_icash_sold = (
#                             const_obj.global_agent_icash_sold - 1
#                     )
#                     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                             const_obj.global_agent_icash_sold_dict.get(
#                                 str(actual_stake_amount)
#                             )
#                             - 1
#                     )
#                     const_obj.save()
#                     WIN_GIVEN_OUT = True

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     # const_obj.count_to_giver = 0
#                     # const_obj.save()
#                     ConstantVariable.update_giver(actual_stake_amount, reset=True)
#                     return

#                 if (
#                         agent_count_to_giver >= GIVER_THRESHOLD
#                         and const_obj.icash2_draw_mode == "LOCAL"
#                 ):
#                     # Handle giver for LOCAL
#                     print("HERE 2..!!!")

#                     agent.icash_sold_dict[str(actual_stake_amount)] = (
#                             agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
#                     )
#                     agent.save()
#                     WIN_GIVEN_OUT = True
#                     # Offset the number of tickets sold because of the giver

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         agent=agent,
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     # agent.count_to_giver = 0
#                     # agent.save()
#                     agent.update_giver(actual_stake_amount, reset=True)
#                     return

#                 if (
#                         global_count_to_giver >= GIVER_THRESHOLD
#                         and const_obj.icash2_draw_mode == "GLOBAL"
#                 ):
#                     # Handle giver for GLOBAL
#                     print("HERE 3..!!!")

#                     const_obj.global_agent_icash_sold = (
#                             const_obj.global_agent_icash_sold - 1
#                     )
#                     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                             const_obj.global_agent_icash_sold_dict.get(
#                                 str(actual_stake_amount)
#                             )
#                             - 1
#                     )
#                     const_obj.save()
#                     WIN_GIVEN_OUT = True

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     # const_obj.count_to_giver = 0
#                     # const_obj.save()
#                     ConstantVariable.update_giver(actual_stake_amount, reset=True)
#                     return

#                 if (
#                         agent_count_to_giver >= GIVER_THRESHOLD
#                         and const_obj.icash2_draw_mode == "LOCAL"
#                 ):
#                     # Handle giver for LOCAL
#                     print("HERE 4..!!!")

#                     agent.icash_sold_dict[str(actual_stake_amount)] = (
#                             agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
#                     )
#                     agent.save()
#                     WIN_GIVEN_OUT = True
#                     # Offset the number of tickets sold because of the giver

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         agent=agent,
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     agent.update_giver(actual_stake_amount, reset=True)
#                     agent.save()
#                     return

#                 if (
#                         (
#                                 const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)]
#                                 / tickets_per_teir
#                         )
#                         >= count_before
#                 ) and const_obj.icash2_draw_mode == "GLOBAL":
#                     print("HERE 5..!!!")
#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             win_rank_choice,
#                             actual_stake_amount,
#                             GLOBAL_WINNINGS[int(actual_stake_amount)][win_rank_choice],
#                         ),
#                         count_before=count_before,
#                         flavour=global_current_flavour,
#                     )
#                     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0
#                     const_obj.icash_excesses_from_count_before = const_obj.icash_excesses_from_count_before + icash_excesses_from_count_before
#                     const_obj.save()

#                     # const_obj.count_to_giver = const_obj.count_to_giver + 1
#                     ConstantVariable.update_giver(actual_stake_amount)
#                     WIN_GIVEN_OUT = True

#                 if (
#                         (agent.icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir)
#                         >= count_before
#                 ) and const_obj.icash2_draw_mode == "LOCAL":
#                     print("HERE 6..!!!")
#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             win_rank_choice,
#                             actual_stake_amount,
#                             AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][
#                                 win_rank_choice
#                             ],
#                         ),
#                         agent=agent,
#                         count_before=count_before,
#                         flavour=agent_current_flavour,
#                     )
#                     agent.icash_sold_dict[str(actual_stake_amount)] = 0
#                     # agent.count_to_giver = agent.count_to_giver + 1
#                     # agent.save()

#                     agent.icash_excesses_from_count_before = agent.icash_excesses_from_count_before + icash_excesses_from_count_before
#                     agent.save()
#                     agent.update_giver(actual_stake_amount)
#                     WIN_GIVEN_OUT = True

#             ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
#             number_of_single_tickets_in_largest_ticket = random.randint(
#                 int(ticket_min), int(ticket_max)
#             )

#             if WIN_GIVEN_OUT:
#                 ConstantVariable.objects.all().update(icash_flavour_dict=new_agent_dict)
#                 Agent.objects.filter().update(icash_flavour_dict=new_global_dict)

#             (
#                 thresholdx,
#                 thresholdy,
#                 thresholdz,
#             ) = const_obj.excesses_giveout_threshold.split(",")
#             excess_giveout_amount = random.choice(
#                 list(range(int(thresholdx), int(thresholdy), int(thresholdz)))
#             )
#             print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
#             print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
#             print("EXCESS AMOUNT :::", excess_giveout_amount)
#             BALANCE_AFTER_EXCESS_GIVEOUT = 0

#             if (
#                     const_obj.icash_excesses_from_count_before >= excess_giveout_amount
#                     and const_obj.icash2_draw_mode == "GLOBAL"
#             ):
#                 InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                     f"EXCESS-{datetime.now()}",
#                     ("min_win", 150, excess_giveout_amount),
#                     count_before=number_of_single_tickets_in_largest_ticket,
#                     flavour="CASHBACK",
#                 )

#                 BALANCE_AFTER_EXCESS_GIVEOUT = (
#                         const_obj.icash_excesses_from_count_before - excess_giveout_amount
#                 )
#                 ConstantVariable.objects.all().update(
#                     icash_excesses_from_count_before=BALANCE_AFTER_EXCESS_GIVEOUT
#                 )

#             if (
#                     agent.icash_excesses_from_count_before >= excess_giveout_amount
#                     and const_obj.icash2_draw_mode == "LOCAL"
#             ):
#                 InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                     f"EXCESS-{datetime.now()}",
#                     ("min_win", 150, excess_giveout_amount),
#                     agent,
#                     count_before=number_of_single_tickets_in_largest_ticket,
#                     flavour="CASHBACK",
#                 )

#                 BALANCE_AFTER_EXCESS_GIVEOUT = (
#                         agent.icash_excesses_from_count_before - excess_giveout_amount
#                 )
#                 agent.icash_excesses_from_count_before = BALANCE_AFTER_EXCESS_GIVEOUT
#                 agent.save()

#             tickets_to_bonus = (
#                     agent.icash_sold / number_of_single_tickets_in_largest_ticket
#             )

#             if tickets_to_bonus >= const_obj.icash2_local_bonus_threshold:
#                 agent_tickets = agent.lottoticket_set.filter(
#                     lottery_type="INSTANT_CASHOUT",
#                     paid=True,
#                     date__date=datetime.now().date(),
#                 ).values_list("stake_amount", "number_of_ticket")

#                 if agent_tickets.exists():
#                     real_play_bands = [
#                         int(amount * qty) for amount, qty in list(agent_tickets)
#                     ]
#                     bonus_band = int(random.choice(real_play_bands))

#                     const_obj = ConstantVariable.objects.all().first()
#                     const_obj.icash2_local_bonus_available = (
#                             const_obj.icash2_local_bonus_available
#                             - BLACK_WINNINGS[bonus_band][BONUS_TIER]
#                     )

#                     if not const_obj.icash2_local_bonus_available <= 0:
#                         const_obj.save()
#                         agent.icash_sold = 0
#                         agent.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"BONUS-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )

#                     agent.icash2_bonus_left = agent.icash2_bonus_left - BLACK_WINNINGS[bonus_band][BONUS_TIER]

#                     if not agent.icash2_bonus_left <= 0:
#                         agent.icash_sold = 0
#                         agent.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"BONUS-LOC-{datetime.now()}",
#                             (BONUS_TIER, bonus_band, BONUS_WINNING[bonus_band][BONUS_TIER]),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR
#                         )

#             if (
#                     const_obj.global_agent_icash_sold
#                     / number_of_single_tickets_in_largest_ticket
#             ) >= const_obj.icash2_local_bonus_threshold:
#                 print("HERE 9..!!!")
#                 agent_tickets = agent.lottoticket_set.filter(
#                     lottery_type="INSTANT_CASHOUT",
#                     paid=True,
#                     date__date=datetime.now().date(),
#                 ).values_list("stake_amount", "number_of_ticket")
#                 if agent_tickets.exists():
#                     real_play_bands = [
#                         int(amount * qty) for amount, qty in list(agent_tickets)
#                     ]

#                     bonus_band = int(random.choice(real_play_bands))

#                     const_obj = ConstantVariable.objects.all().first()
#                     const_obj.icash2_global_bonus_available = (
#                             const_obj.icash2_global_bonus_available
#                             - BONUS_WINNING[bonus_band][BONUS_TIER]
#                     )
#                     const_obj.global_agent_icash_sold = 0

#                     if not const_obj.icash2_global_bonus_available < 0:
#                         const_obj.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"GLOB-BONUS-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )


# @receiver(post_save, sender=LottoTicket)
# def quika_icash(sender, instance: LottoTicket, created, **kwargs):
#     fields_to_exclude = {
#         "system_generated_num",
#     }
#     # automatically populate a list with all fields, except the ones you want to exclude
#     fields_to_update = [
#         f.name
#         for f in LottoTicket._meta.get_fields()
#         if f.name not in fields_to_exclude and not f.auto_created
#     ]

#     from math import ceil

#     from django.conf import settings

#     if settings.DEBUG:
#         # DEFAULT_AGENT_FOR_WEB = 230
#         DEFAULT_AGENT_FOR_WEB = 1

#     else:
#         DEFAULT_AGENT_FOR_WEB = settings.DEFAULT_AGENT_ID

#     if not instance.paid:
#         return

#     if not instance.agent_profile:
#         instance.is_agent = True
#         agent_profile = Agent.objects.filter(id=DEFAULT_AGENT_FOR_WEB)
#         instance.agent_profile = agent_profile.last()
#         instance.save()

#     if instance.icash_2_counted or not instance.is_agent:
#         return

#     if instance.lottery_type not in ["INSTANT_CASHOUT", "QUIKA"]:
#         return

#     WIN_GIVEN_OUT = False
#     const_obj: ConstantVariable = ConstantVariable.objects.all().last()

#     if const_obj.icash_to_use != "NEW ICASH":
#         return

#     # def round_down(num):
#     #     return num//100 * 100

#     WHITE_WINNINGS = winnings(const_obj=const_obj)

#     BLACK_WINNINGS = {
#         200: {
#             "least_win": 250,
#             "minor_win": 450,
#             "min_win": round_down(
#                 4500 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 5400 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": 1400,
#         },
#         500: {
#             "least_win": 600,
#             "minor_win": 750,
#             "min_win": round_down(
#                 7500 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 13500 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         750: {
#             "least_win": 1125,
#             "minor_win": 1200,
#             "min_win": round_down(
#                 9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 15750 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         1000: {
#             "least_win": 500,
#             "minor_win": 1000,
#             "min_win": round_down(
#                 10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 13000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 18000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         1250: {
#             "least_win": 1250,
#             "minor_win": 1450,
#             "min_win": round_down(
#                 12000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 14000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 18750 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         1300: {
#             "least_win": 1300,
#             "minor_win": 1500,
#             "min_win": round_down(
#                 13500 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 16000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 22500 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         1500: {
#             "least_win": 1300,
#             "minor_win": 1700,
#             "min_win": 2100,
#             "mid_win": 2600,
#             "max_win": round_down(
#                 25000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#     }

#     # {"200": 0, "500": 0, "750": 0, "1000": 0, "1250": 0, "1300": 0, "1500": 0}
#     actual_stake_amount = str(int(instance.stake_amount * instance.number_of_ticket))

#     agent = instance.agent_profile

#     agent_flavour_list = agent.icash_flavour_dict.get(str((actual_stake_amount)), [])
#     global_flavour_list = const_obj.icash_flavour_dict.get(
#         str((actual_stake_amount)), []
#     )

#     const_obj = ConstantVariable.objects.all().last()

#     if not len(agent_flavour_list):
#         agent.icash_flavour_dict[
#             str((actual_stake_amount))
#         ] = const_obj.build_flavour_list()
#         agent.save()

#     if not len(global_flavour_list):
#         const_obj.icash_flavour_dict[
#             str((actual_stake_amount))
#         ] = const_obj.build_flavour_list()
#         const_obj.save()

#     agent_current_flavour = agent.icash_flavour_dict[str((actual_stake_amount))][0]
#     global_current_flavour = const_obj.icash_flavour_dict[str((actual_stake_amount))][0]

#     agent.icash_flavour_dict[str((actual_stake_amount))] = agent.icash_flavour_dict[
#         str((actual_stake_amount))
#     ][1:]
#     const_obj.icash_flavour_dict[
#         str((actual_stake_amount))
#     ] = const_obj.icash_flavour_dict[str((actual_stake_amount))][1:]

#     new_agent_dict = dict(agent.icash_flavour_dict)
#     new_global_dict = dict(const_obj.icash_flavour_dict)

#     GIVER_RANGE1, GIVER_RANGE2 = const_obj.giver_threshold.split(",")

#     # giver_threshold = InstantCashGiverThresholds.objects.first()
#     # giver_threshold.GIVER_THRESHOLD_DICT[actual_stake_amount] = F(GIVER_THRESHOLD_DICT[actual_stake_amount]) + 1
#     # giver_threshold.save()

#     const_obj = ConstantVariable.objects.all().last()
#     global_count_to_giver = const_obj.new_quika_icash_count_to_giver[
#         str(actual_stake_amount)
#     ]
#     agent_count_to_giver = agent.new_quika_icash_count_to_giver[
#         str(actual_stake_amount)
#     ]

#     GIVER_THRESHOLD = random.randint(int(GIVER_RANGE1), int(GIVER_RANGE2))

#     # GIVER_THRESHOLD = GIVER_THRESHOLD_DICT[str(actual_stake_amount)]

#     WINNINGS_DICT = {"WHITE": WHITE_WINNINGS, "BLACK": BLACK_WINNINGS}

#     GLOBAL_WINNINGS = WINNINGS_DICT[global_current_flavour]
#     AGENT_LOCAL_WINNINGS = WINNINGS_DICT[agent_current_flavour]

#     BONUS_FLAVOUR = random.choice(const_obj.icash_bonus_bias_dict["flavours"])
#     BONUS_WINNING = WINNINGS_DICT[BONUS_FLAVOUR]
#     BONUS_TIER = random.choice(const_obj.icash_bonus_bias_dict["tiers"])

#     print(BONUS_FLAVOUR, BONUS_TIER, const_obj.icash_bonus_bias_dict["tiers"])

#     const_obj = ConstantVariable.objects.all().first()

#     def get_averages(band):
#         values = (
#             [band["least_win"]] * 5
#             + [band["minor_win"]] * 4
#             + [band["min_win"]] * 4
#             + [band["mid_win"]] * 4
#             + [band["max_win"]] * 3
#         )
#         return sum(values)

#     if instance.lottery_type == "INSTANT_CASHOUT" or instance.lottery_type == "QUIKA":
#         if instance.paid and instance.rtp > 0 and instance.is_agent:
#             print("HERE TOO..!!!!!!")

#             agent = instance.agent_profile
#             agent.icash_sold = agent.icash_sold + 1
#             instance.icash_2_counted = True
#             instance.icash_counted = True
#             instance.save(update_fields=fields_to_update)

#             tickets_per_teir = instance.number_of_ticket

#             band_average = get_averages(GLOBAL_WINNINGS[int(actual_stake_amount)])
#             rtp = instance.rtp * instance.number_of_ticket

#             print(
#                 "\n BAND Average +++++++++++++++++++++++++++++++++++++++++++++++++>",
#                 band_average,
#                 "<+++++++++++++++++++++++++++++++++++++++++++++++++\n",
#             )

#             num_win_tickets = rtp / band_average
#             print(
#                 "NUM WINNING TICKS ::::",
#                 num_win_tickets,
#                 "\n",
#                 f"Inst-RTP::{instance.rtp}\n",
#                 f"Num tiks{instance.number_of_ticket}\n",
#                 f"Tot RTP{rtp}\n",
#                 band_average,
#                 "\n",
#                 GLOBAL_WINNINGS[int(actual_stake_amount)],
#             )
#             percent_win_ticket = num_win_tickets / instance.number_of_ticket

#             count_before = (1 / percent_win_ticket) / instance.number_of_ticket
#             icash_excesses_from_count_before = (
#                 instance.number_of_ticket * instance.rtp
#             ) * (ceil(count_before) - count_before)
#             print("EXCESSES FROM CB4 ::::", icash_excesses_from_count_before)

#             print("BAND RTP ::::", rtp, "COUNT BEF ::::", count_before)
#             print(
#                 "A-FLAVOUR ::::",
#                 agent_current_flavour,
#                 "G-FLAVOUR BEF ::::",
#                 global_current_flavour,
#             )
#             print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

#             if not agent.icash_sold_dict.get(str(actual_stake_amount)):
#                 agent.icash_sold_dict[str(actual_stake_amount)] = 0

#             if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
#                 const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

#             agent.icash_sold_dict[str(actual_stake_amount)] = (
#                 agent.icash_sold_dict.get(str(actual_stake_amount)) + 1
#             )

#             const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + 1
#             const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                 const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + 1
#             )

#             win_rank_ratio = (
#                 ["least_win"] * 5
#                 + ["minor_win"] * 4
#                 + ["min_win"] * 4
#                 + ["mid_win"] * 4
#                 + ["max_win"] * 3
#             )
#             win_rank_choice = random.choice(win_rank_ratio)

#             if const_obj.icash_to_use == "NEW ICASH":
#                 const_obj.save()
#                 agent.save()

#                 if (
#                     global_count_to_giver >= GIVER_THRESHOLD
#                     and const_obj.icash2_draw_mode == "GLOBAL"
#                 ):
#                     # Handle giver for GLOBAL
#                     print("HERE 1..!!!")

#                     const_obj.global_agent_icash_sold = (
#                         const_obj.global_agent_icash_sold - 1
#                     )
#                     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                         const_obj.global_agent_icash_sold_dict.get(
#                             str(actual_stake_amount)
#                         )
#                         - 1
#                     )
#                     const_obj.save()
#                     WIN_GIVEN_OUT = True

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     # const_obj.count_to_giver = 0
#                     # const_obj.save()
#                     ConstantVariable.update_giver(actual_stake_amount, reset=True)
#                     return

#                 if (
#                     agent_count_to_giver >= GIVER_THRESHOLD
#                     and const_obj.icash2_draw_mode == "LOCAL"
#                 ):
#                     # Handle giver for LOCAL
#                     print("HERE 2..!!!")

#                     agent.icash_sold_dict[str(actual_stake_amount)] = (
#                         agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
#                     )
#                     agent.save()
#                     WIN_GIVEN_OUT = True
#                     # Offset the number of tickets sold because of the giver

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         agent=agent,
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     # agent.count_to_giver = 0
#                     # agent.save()
#                     agent.update_giver(actual_stake_amount, reset=True)
#                     return

#                 if (
#                     global_count_to_giver >= GIVER_THRESHOLD
#                     and const_obj.icash2_draw_mode == "GLOBAL"
#                 ):
#                     # Handle giver for GLOBAL
#                     print("HERE 3..!!!")

#                     const_obj.global_agent_icash_sold = (
#                         const_obj.global_agent_icash_sold - 1
#                     )
#                     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                         const_obj.global_agent_icash_sold_dict.get(
#                             str(actual_stake_amount)
#                         )
#                         - 1
#                     )
#                     const_obj.save()
#                     WIN_GIVEN_OUT = True

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     # const_obj.count_to_giver = 0
#                     # const_obj.save()
#                     ConstantVariable.update_giver(actual_stake_amount, reset=True)
#                     return

#                 if (
#                     agent_count_to_giver >= GIVER_THRESHOLD
#                     and const_obj.icash2_draw_mode == "LOCAL"
#                 ):
#                     # Handle giver for LOCAL
#                     print("HERE 4..!!!")

#                     agent.icash_sold_dict[str(actual_stake_amount)] = (
#                         agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
#                     )
#                     agent.save()
#                     WIN_GIVEN_OUT = True
#                     # Offset the number of tickets sold because of the giver

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         agent=agent,
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     agent.update_giver(actual_stake_amount, reset=True)
#                     agent.save()
#                     return

#                 if (
#                     (
#                         const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)]
#                         / tickets_per_teir
#                     )
#                     >= count_before
#                 ) and const_obj.icash2_draw_mode == "GLOBAL":
#                     print("HERE 5..!!!")
#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             win_rank_choice,
#                             actual_stake_amount,
#                             GLOBAL_WINNINGS[int(actual_stake_amount)][win_rank_choice],
#                         ),
#                         count_before=count_before,
#                         flavour=global_current_flavour,
#                     )
#                     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0
#                     const_obj.icash_excesses_from_count_before = (
#                         const_obj.icash_excesses_from_count_before
#                         + icash_excesses_from_count_before
#                     )
#                     const_obj.save()

#                     # const_obj.count_to_giver = const_obj.count_to_giver + 1
#                     ConstantVariable.update_giver(actual_stake_amount)
#                     WIN_GIVEN_OUT = True

#                 if (
#                     (agent.icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir)
#                     >= count_before
#                 ) and const_obj.icash2_draw_mode == "LOCAL":
#                     print("HERE 6..!!!")
#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             win_rank_choice,
#                             actual_stake_amount,
#                             AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][
#                                 win_rank_choice
#                             ],
#                         ),
#                         agent=agent,
#                         count_before=count_before,
#                         flavour=agent_current_flavour,
#                     )
#                     agent.icash_sold_dict[str(actual_stake_amount)] = 0
#                     # agent.count_to_giver = agent.count_to_giver + 1
#                     # agent.save()

#                     agent.icash_excesses_from_count_before = (
#                         agent.icash_excesses_from_count_before
#                         + icash_excesses_from_count_before
#                     )
#                     agent.save()
#                     agent.update_giver(actual_stake_amount)
#                     WIN_GIVEN_OUT = True

#             ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
#             number_of_single_tickets_in_largest_ticket = random.randint(
#                 int(ticket_min), int(ticket_max)
#             )

#             if WIN_GIVEN_OUT:
#                 ConstantVariable.objects.all().update(icash_flavour_dict=new_agent_dict)
#                 Agent.objects.filter().update(icash_flavour_dict=new_global_dict)

#             (
#                 thresholdx,
#                 thresholdy,
#                 thresholdz,
#             ) = const_obj.excesses_giveout_threshold.split(",")
#             excess_giveout_amount = random.choice(
#                 list(range(int(thresholdx), int(thresholdy), int(thresholdz)))
#             )
#             print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
#             print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
#             print("EXCESS AMOUNT :::", excess_giveout_amount)
#             BALANCE_AFTER_EXCESS_GIVEOUT = 0

#             if (
#                 const_obj.icash_excesses_from_count_before >= excess_giveout_amount
#                 and const_obj.icash2_draw_mode == "GLOBAL"
#             ):
#                 InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                     f"EXCESS-{datetime.now()}",
#                     ("min_win", 150, excess_giveout_amount),
#                     count_before=number_of_single_tickets_in_largest_ticket,
#                     flavour="CASHBACK",
#                 )

#                 BALANCE_AFTER_EXCESS_GIVEOUT = (
#                     const_obj.icash_excesses_from_count_before - excess_giveout_amount
#                 )
#                 ConstantVariable.objects.all().update(
#                     icash_excesses_from_count_before=BALANCE_AFTER_EXCESS_GIVEOUT
#                 )

#             if (
#                 agent.icash_excesses_from_count_before >= excess_giveout_amount
#                 and const_obj.icash2_draw_mode == "LOCAL"
#             ):
#                 InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                     f"EXCESS-{datetime.now()}",
#                     ("min_win", 150, excess_giveout_amount),
#                     agent,
#                     count_before=number_of_single_tickets_in_largest_ticket,
#                     flavour="CASHBACK",
#                 )

#                 BALANCE_AFTER_EXCESS_GIVEOUT = (
#                     agent.icash_excesses_from_count_before - excess_giveout_amount
#                 )
#                 agent.icash_excesses_from_count_before = BALANCE_AFTER_EXCESS_GIVEOUT
#                 agent.save()

#             tickets_to_bonus = (
#                 agent.icash_sold / number_of_single_tickets_in_largest_ticket
#             )

#             if tickets_to_bonus >= const_obj.icash2_local_bonus_threshold:
#                 agent_tickets = agent.lottoticket_set.filter(
#                     lottery_type="INSTANT_CASHOUT",
#                     paid=True,
#                     date__date=datetime.now().date(),
#                 ).values_list("stake_amount", "number_of_ticket")

#                 if agent_tickets.exists():
#                     real_play_bands = [
#                         int(amount * qty) for amount, qty in list(agent_tickets)
#                     ]
#                     bonus_band = int(random.choice(real_play_bands))

#                     const_obj = ConstantVariable.objects.all().first()
#                     const_obj.icash2_local_bonus_available = (
#                         const_obj.icash2_local_bonus_available
#                         - BLACK_WINNINGS[bonus_band][BONUS_TIER]
#                     )

#                     if not const_obj.icash2_local_bonus_available <= 0:
#                         const_obj.save()
#                         agent.icash_sold = 0
#                         agent.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"BONUS-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )

#                     agent.icash2_bonus_left = (
#                         agent.icash2_bonus_left - BLACK_WINNINGS[bonus_band][BONUS_TIER]
#                     )

#                     if not agent.icash2_bonus_left <= 0:
#                         agent.icash_sold = 0
#                         agent.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"BONUS-LOC-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )

#             if (
#                 const_obj.global_agent_icash_sold
#                 / number_of_single_tickets_in_largest_ticket
#             ) >= const_obj.icash2_local_bonus_threshold:
#                 print("HERE 9..!!!")
#                 agent_tickets = agent.lottoticket_set.filter(
#                     lottery_type="INSTANT_CASHOUT",
#                     paid=True,
#                     date__date=datetime.now().date(),
#                 ).values_list("stake_amount", "number_of_ticket")
#                 if agent_tickets.exists():
#                     real_play_bands = [
#                         int(amount * qty) for amount, qty in list(agent_tickets)
#                     ]

#                     bonus_band = int(random.choice(real_play_bands))

#                     const_obj = ConstantVariable.objects.all().first()
#                     const_obj.icash2_global_bonus_available = (
#                         const_obj.icash2_global_bonus_available
#                         - BONUS_WINNING[bonus_band][BONUS_TIER]
#                     )
#                     const_obj.global_agent_icash_sold = 0

#                     if not const_obj.icash2_global_bonus_available < 0:
#                         const_obj.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"GLOB-BONUS-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )


# # @receiver(post_save, sender=LottoTicket)
# def quika_new_icash_local(sender, instance: LottoTicket, created, **kwargs):
#     import redis

#     conn = redis.Redis("localhost")
#     user = {
#         "Name": "Pradeep",
#         "Company": "SCTL",
#         "Address": "Mumbai",
#         "Location": "RCP",
#     }
#     conn.hmset("pythonDict", user)
#     print(conn.hgetall("pythonDict"))
#     print("#")
#     print("#")
#     print("#")
#     print("              ")
#     print("              ")
#     print("              ")
#     print(conn.hgetall("pythonDict"))
#     print("              ")
#     print("              ")
#     print("#")
#     print("#")
#     print("#")
#     print("#")
#     print("#")

#     fields_to_exclude = {
#         "system_generated_num",
#     }
#     # automatically populate a list with all fields, except the ones you want to exclude
#     fields_to_update = [
#         f.name
#         for f in LottoTicket._meta.get_fields()
#         if f.name not in fields_to_exclude and not f.auto_created
#     ]

#     from math import ceil

#     from django.conf import settings

#     if settings.DEBUG:
#         DEFAULT_AGENT_FOR_WEB = 43
#         # DEFAULT_AGENT_FOR_WEB = 1

#     else:
#         # DEFAULT_AGENT_FOR_WEB = settings.DEFAULT_AGENT_ID
#         DEFAULT_AGENT_FOR_WEB = 43

#     if not instance.paid:
#         return

#     if not instance.agent_profile:
#         instance.is_agent = True
#         agent_profile = Agent.objects.filter(id=DEFAULT_AGENT_FOR_WEB)
#         instance.agent_profile = agent_profile.last()
#         instance.save()

#     print("::::::::::::::1::::::::::::::")
#     if instance.icash_2_counted or not instance.is_agent:
#         return

#     print("::::::::::::::2::::::::::::::")
#     print("LOTTERY TYPE", instance.lottery_type)
#     if instance.lottery_type not in ["INSTANT_CASHOUT", "QUIKA"]:
#         return
#     print("::::::::::::::3::::::::::::::")

#     WIN_GIVEN_OUT = False
#     const_obj: ConstantVariable = ConstantVariable.objects.all().last()

#     if const_obj.icash_to_use != "NEW ICASH":
#         return

#     print("::::::::::::::4::::::::::::::")
#     # def round_down(num):
#     #     return num//100 * 100

#     WHITE_WINNINGS = winnings(const_obj=const_obj)

#     BLACK_WINNINGS = {
#         200: {
#             "least_win": 300,
#             "minor_win": 500,
#             "min_win": 800,
#             "mid_win": 1000,
#             "max_win": 1000,
#         },
#         500: {
#             "least_win": 1000,
#             "minor_win": 1200,
#             "min_win": 1700,
#             "mid_win": 1500,
#             "max_win": 1800,
#         },
#         750: {
#             "least_win": 1125,
#             "minor_win": 1200,
#             "min_win": round_down(
#                 9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 15750 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         800: {
#             "least_win": 1200,
#             "minor_win": 1500,
#             "min_win": 2000,
#             "mid_win": 1800,
#             "max_win": 2000,
#         },
#         1250: {
#             "least_win": 2500,
#             "minor_win": 2350,
#             "min_win": 2200,
#             "mid_win": 2500,
#             "max_win": 3500,
#         },
#         1300: {
#             "least_win": 2750,
#             "minor_win": 1800,
#             "min_win": 2000,
#             "mid_win": 3000,
#             "max_win": 4000,
#         },
#         1400: {
#             "least_win": 2800,
#             "minor_win": 2000,
#             "min_win": 2500,
#             "mid_win": 3500,
#             "max_win": 4000,
#         },
#     }

#     # {"200": 0, "500": 0, "750": 0, "1000": 0, "1250": 0, "1300": 0, "1500": 0}
#     print("           |||              ")
#     print("           |||              ")
#     print(
#         "Stake Amount :::",
#         instance.stake_amount,
#         instance.number_of_ticket,
#         instance.game_play_id,
#     )
#     print("           |||              ")
#     print("           |||              ")
#     actual_stake_amount = str(int(instance.stake_amount * instance.number_of_ticket))

#     agent = instance.agent_profile

#     agent_flavour_list = agent.icash_flavour_dict.get(str((actual_stake_amount)), [])
#     global_flavour_list = const_obj.icash_flavour_dict.get(
#         str((actual_stake_amount)), []
#     )

#     const_obj = ConstantVariable.objects.all().last()

#     if not len(agent_flavour_list):
#         agent.icash_flavour_dict[
#             str((actual_stake_amount))
#         ] = const_obj.build_flavour_list()
#         agent.save()

#     if not len(global_flavour_list):
#         const_obj.icash_flavour_dict[
#             str((actual_stake_amount))
#         ] = const_obj.build_flavour_list()
#         const_obj.save()

#     agent_current_flavour = agent.icash_flavour_dict[str((actual_stake_amount))][0]
#     global_current_flavour = const_obj.icash_flavour_dict[str((actual_stake_amount))][0]

#     agent.icash_flavour_dict[str((actual_stake_amount))] = agent.icash_flavour_dict[
#         str((actual_stake_amount))
#     ][1:]
#     const_obj.icash_flavour_dict[
#         str((actual_stake_amount))
#     ] = const_obj.icash_flavour_dict[str((actual_stake_amount))][1:]

#     new_agent_dict = dict(agent.icash_flavour_dict)
#     new_global_dict = dict(const_obj.icash_flavour_dict)

#     GIVER_RANGE1, GIVER_RANGE2 = const_obj.giver_threshold.split(",")

#     # giver_threshold = InstantCashGiverThresholds.objects.first()
#     # giver_threshold.GIVER_THRESHOLD_DICT[actual_stake_amount] = F(GIVER_THRESHOLD_DICT[actual_stake_amount]) + 1
#     # giver_threshold.save()

#     const_obj = ConstantVariable.objects.all().last()

#     try:
#         global_count_to_giver = const_obj.new_quika_icash_count_to_giver[
#             str(actual_stake_amount)
#         ]
#         agent_count_to_giver = agent.new_quika_icash_count_to_giver[
#             str(actual_stake_amount)
#         ]
#     except Exception:
#         base_global_giver_dict = const_obj.new_quika_icash_count_to_giver
#         base_global_giver_dict[str(actual_stake_amount)] = 0

#         base_agent_giver_dict = agent.new_quika_icash_count_to_giver
#         base_agent_giver_dict[str(actual_stake_amount)] = 0

#         ConstantVariable.objects.all().update(
#             new_quika_icash_count_to_giver=base_global_giver_dict
#         )
#         agent.new_quika_icash_count_to_giver = base_agent_giver_dict
#         agent.save()

#         global_count_to_giver = -999
#         agent_count_to_giver = -999

#     const_obj = ConstantVariable.objects.all().last()
#     GIVER_THRESHOLD = random.randint(int(GIVER_RANGE1), int(GIVER_RANGE2))

#     # GIVER_THRESHOLD = GIVER_THRESHOLD_DICT[str(actual_stake_amount)]

#     WINNINGS_DICT = {"WHITE": WHITE_WINNINGS, "BLACK": BLACK_WINNINGS}

#     GLOBAL_WINNINGS = WINNINGS_DICT[global_current_flavour]
#     AGENT_LOCAL_WINNINGS = WINNINGS_DICT[agent_current_flavour]

#     BONUS_FLAVOUR = random.choice(const_obj.icash_bonus_bias_dict["flavours"])
#     BONUS_WINNING = WINNINGS_DICT[BONUS_FLAVOUR]
#     BONUS_TIER = random.choice(const_obj.icash_bonus_bias_dict["tiers"])

#     print(BONUS_FLAVOUR, BONUS_TIER, const_obj.icash_bonus_bias_dict["tiers"])

#     const_obj = ConstantVariable.objects.all().first()

#     def get_averages(band):
#         values = (
#             [band["least_win"]]
#             + [band["minor_win"]]
#             + [band["min_win"]]
#             + [band["mid_win"]]
#             + [band["max_win"]]
#         )
#         return sum(values) / 5

#     if instance.lottery_type == "INSTANT_CASHOUT" or instance.lottery_type == "QUIKA":
#         if instance.paid and instance.rtp > 0 and instance.is_agent:
#             print("HERE TOO..!!!!!!")

#             agent = instance.agent_profile
#             agent.icash_sold = agent.icash_sold + 1
#             instance.icash_2_counted = True
#             instance.icash_counted = True
#             instance.save(update_fields=fields_to_update)

#             tickets_per_teir = instance.number_of_ticket

#             band_average = get_averages(GLOBAL_WINNINGS[int(actual_stake_amount)])
#             rtp = instance.rtp * instance.number_of_ticket

#             print(
#                 "\n BAND Average +++++++++++++++++++++++++++++++++++++++++++++++++>",
#                 band_average,
#                 "<+++++++++++++++++++++++++++++++++++++++++++++++++\n",
#             )

#             num_win_tickets = rtp / band_average
#             print(
#                 "NUM WINNING TICKS ::::",
#                 num_win_tickets,
#                 "\n",
#                 f"Inst-RTP::{instance.rtp}\n",
#                 f"Num tiks{instance.number_of_ticket}\n",
#                 f"Tot RTP{rtp}\n",
#                 band_average,
#                 "\n",
#                 GLOBAL_WINNINGS[int(actual_stake_amount)],
#             )
#             percent_win_ticket = num_win_tickets / instance.number_of_ticket

#             count_before = (1 / percent_win_ticket) / instance.number_of_ticket
#             icash_excesses_from_count_before = (
#                 instance.number_of_ticket * instance.rtp
#             ) * (ceil(count_before) - count_before)
#             print("EXCESSES FROM CB4 ::::", icash_excesses_from_count_before)

#             print("BAND RTP ::::", rtp, "COUNT BEF ::::", count_before)
#             print(
#                 "A-FLAVOUR ::::",
#                 agent_current_flavour,
#                 "G-FLAVOUR BEF ::::",
#                 global_current_flavour,
#             )
#             print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

#             if not agent.icash_sold_dict.get(str(actual_stake_amount)):
#                 agent.icash_sold_dict[str(actual_stake_amount)] = 0

#             if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
#                 const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

#             agent.icash_sold_dict[str(actual_stake_amount)] = (
#                 agent.icash_sold_dict.get(str(actual_stake_amount)) + 1
#             )

#             const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + 1
#             const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                 const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + 1
#             )

#             win_rank_ratio = (
#                 ["least_win"] + ["minor_win"] + ["min_win"] + ["mid_win"] + ["max_win"]
#             )
#             win_rank_choice = random.choice(win_rank_ratio)

#             print("                                      ")
#             print("                                      ")
#             print("                                      ")
#             print("                                      ")
#             print("JUST BEFORE THE FIRST RETURN STATEMENT")
#             print("JUST BEFORE THE FIRST RETURN STATEMENT")
#             print("JUST BEFORE THE FIRST RETURN STATEMENT")
#             print("JUST BEFORE THE FIRST RETURN STATEMENT")
#             print("JUST BEFORE THE FIRST RETURN STATEMENT")
#             print("JUST BEFORE THE FIRST RETURN STATEMENT")
#             print("JINSTANCE GAME ID", instance.game_play_id)
#             print("COUNT TO GIVER", global_count_to_giver, "GTHRES", GIVER_THRESHOLD)
#             print("                                      ")
#             print("                                      ")
#             print("                                      ")
#             print("                                      ")

#             if const_obj.icash_to_use == "NEW ICASH":
#                 const_obj.save()
#                 agent.save()

#                 if (
#                     global_count_to_giver >= GIVER_THRESHOLD
#                     and const_obj.icash2_draw_mode == "GLOBAL"
#                 ):
#                     # Handle giver for GLOBAL
#                     print("HERE 1..!!!")
#                     print("RELEASING GIVER")
#                     print(
#                         "COUNT TO GIVER",
#                         global_count_to_giver,
#                         "GTHRES",
#                         GIVER_THRESHOLD,
#                     )
#                     print("                                      ")
#                     print("                                      ")
#                     print("                                      ")
#                     print("                                      ")

#                     const_obj.global_agent_icash_sold = (
#                         const_obj.global_agent_icash_sold - 1
#                     )
#                     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                         const_obj.global_agent_icash_sold_dict.get(
#                             str(actual_stake_amount)
#                         )
#                         - 1
#                     )
#                     const_obj.save()
#                     WIN_GIVEN_OUT = True

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"GIVER-G-{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     # const_obj.count_to_giver = 0
#                     # const_obj.save()
#                     ConstantVariable.update_giver(actual_stake_amount, reset=True)
#                     return

#                 if (
#                     agent_count_to_giver >= GIVER_THRESHOLD
#                     and const_obj.icash2_draw_mode == "LOCAL"
#                 ):
#                     # Handle giver for LOCAL
#                     print("HERE 2..!!!")

#                     agent.icash_sold_dict[str(actual_stake_amount)] = (
#                         agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
#                     )
#                     agent.save()
#                     WIN_GIVEN_OUT = True
#                     # Offset the number of tickets sold because of the giver

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"GIVER-L-{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         agent=agent,
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     # agent.count_to_giver = 0
#                     # agent.save()
#                     agent.update_giver(actual_stake_amount, reset=True)
#                     return

#                 if (
#                     (
#                         const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)]
#                         / tickets_per_teir
#                     )
#                     >= count_before
#                 ) and const_obj.icash2_draw_mode == "GLOBAL":
#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             win_rank_choice,
#                             actual_stake_amount,
#                             GLOBAL_WINNINGS[int(actual_stake_amount)][win_rank_choice],
#                         ),
#                         count_before=count_before,
#                         flavour=global_current_flavour,
#                     )
#                     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0
#                     const_obj.icash_excesses_from_count_before = (
#                         const_obj.icash_excesses_from_count_before
#                         + icash_excesses_from_count_before
#                     )
#                     const_obj.save()

#                     # const_obj.count_to_giver = const_obj.count_to_giver + 1
#                     ConstantVariable.update_giver(actual_stake_amount)
#                     WIN_GIVEN_OUT = True

#                 if (
#                     (agent.icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir)
#                     >= count_before
#                 ) and const_obj.icash2_draw_mode == "LOCAL":
#                     print("HERE 6..!!!")
#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             win_rank_choice,
#                             actual_stake_amount,
#                             AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][
#                                 win_rank_choice
#                             ],
#                         ),
#                         agent=agent,
#                         count_before=count_before,
#                         flavour=agent_current_flavour,
#                     )
#                     agent.icash_sold_dict[str(actual_stake_amount)] = 0
#                     # agent.count_to_giver = agent.count_to_giver + 1
#                     # agent.save()

#                     agent.icash_excesses_from_count_before = (
#                         agent.icash_excesses_from_count_before
#                         + icash_excesses_from_count_before
#                     )
#                     agent.save()
#                     agent.update_giver(actual_stake_amount)
#                     WIN_GIVEN_OUT = True

#                 if (
#                     agent.icash2_draw_mode == "LOCAL"
#                     and const_obj.icash2_draw_mode == "GLOBAL"
#                 ):
#                     """New implementation for local winning"""

#                     icash_sold_game_ids = list(agent.icash_sold_game_ids) + [
#                         instance.game_play_id
#                     ]
#                     Agent.objects.filter(id=instance.agent_profile.id).update(
#                         icash_sold_game_ids=icash_sold_game_ids,
#                         icash_sold_amount=F("icash_sold_amount") + instance.rtp,
#                     )

#                     const_obj = ConstantVariable.objects.all().last()
#                     win_tiers = const_obj.icash2_quika_giveout_tier

#                     if win_tiers:
#                         win_rank_choice = win_tiers[0]
#                     else:
#                         win_tiers = [
#                             "least_win",
#                             "minor_win",
#                             "min_win",
#                             "mid_win",
#                             "least_win",
#                             "minor_win",
#                             "min_win",
#                             "mid_win",
#                             "max_win",
#                         ]
#                         win_rank_choice = win_tiers[0]

#                     winning = AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][
#                         win_rank_choice
#                     ]

#                     agent = Agent.objects.get(id=instance.agent_profile.id)

#                     tickets_sold_amount = agent.icash_sold_amount
#                     tickets_sold_qty = agent.unique_tickets_sold

#                     difference_after_payout = tickets_sold_amount - winning

#                     print("**************")
#                     print("**************")
#                     print("**************")
#                     print("              ")
#                     print("              ")
#                     print("AGENT ::,", instance.agent_profile)
#                     print("POSSIBLE WINNING::::", winning)
#                     print("WINNING RANK::::", win_rank_choice)
#                     print("ACTIVE FLAVOUR::::", agent_current_flavour)
#                     print("ACTUAL STAKE AMOUNT::::", actual_stake_amount)
#                     print("AMOUNT SOLD::::", agent.icash_sold_amount)
#                     print("I GOT HERE NOW..!!!")
#                     print("              ")
#                     print("              ")
#                     print("**************")
#                     print("**************")
#                     print("**************")

#                     if difference_after_payout >= 0:
#                         if (
#                             len(set(agent.icash_sold_game_ids)) <= 7
#                             and actual_stake_amount == 200
#                         ):
#                             print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")
#                             print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")
#                             print(
#                                 "SALES :::",
#                                 len(set(agent.icash_sold_game_ids)),
#                                 "@@@@@@@@@@@",
#                             )
#                             print("@@@@@@@@@                    @@@@@@@@@@@")
#                             print("@@@@@@@@@ CAN PAYOUT 200 YET @@@@@@@@@@@")

#                         else:
#                             ConstantVariable.objects.all().update(
#                                 icash2_quika_giveout_tier=win_tiers[1:]
#                             )

#                             Agent.objects.filter(id=instance.agent_profile.id).update(
#                                 icash_sold_game_ids=[],
#                                 icash_sold_amount=difference_after_payout,
#                             )
#                             agent = Agent.objects.get(id=instance.agent_profile.id)

#                             InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                                 f"{datetime.now()}",
#                                 (
#                                     win_rank_choice,
#                                     actual_stake_amount,
#                                     winning,
#                                 ),
#                                 agent=agent,
#                                 count_before=count_before,
#                                 flavour=agent_current_flavour,
#                             )
#                             agent.icash_sold_dict[str(actual_stake_amount)] = 0

#                             agent.icash_excesses_from_count_before = (
#                                 agent.icash_excesses_from_count_before
#                                 + icash_excesses_from_count_before
#                             )
#                             agent.save()
#                             agent.update_giver(actual_stake_amount)
#                             WIN_GIVEN_OUT = True

#                     else:
#                         print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")
#                         print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")
#                         print("@@@@@@@@@CAN NOT PAYOUT@@@@@@@@@@@")

#             ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
#             number_of_single_tickets_in_largest_ticket = random.randint(
#                 int(ticket_min), int(ticket_max)
#             )

#             if WIN_GIVEN_OUT:
#                 ConstantVariable.objects.all().update(icash_flavour_dict=new_agent_dict)
#                 Agent.objects.filter().update(icash_flavour_dict=new_global_dict)

#             (
#                 thresholdx,
#                 thresholdy,
#                 thresholdz,
#             ) = const_obj.excesses_giveout_threshold.split(",")
#             excess_giveout_amount = random.choice(
#                 list(range(int(thresholdx), int(thresholdy), int(thresholdz)))
#             )
#             print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
#             print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
#             print("EXCESS AMOUNT :::", excess_giveout_amount)
#             BALANCE_AFTER_EXCESS_GIVEOUT = 0

#             if (
#                 const_obj.icash_excesses_from_count_before >= excess_giveout_amount
#                 and const_obj.icash2_draw_mode == "GLOBAL"
#             ):
#                 InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                     f"EXCESS-{datetime.now()}",
#                     ("min_win", 200, excess_giveout_amount),
#                     count_before=number_of_single_tickets_in_largest_ticket,
#                     flavour="CASHBACK",
#                 )

#                 BALANCE_AFTER_EXCESS_GIVEOUT = (
#                     const_obj.icash_excesses_from_count_before - excess_giveout_amount
#                 )
#                 ConstantVariable.objects.all().update(
#                     icash_excesses_from_count_before=BALANCE_AFTER_EXCESS_GIVEOUT
#                 )

#             if (
#                 agent.icash_excesses_from_count_before >= excess_giveout_amount
#                 and const_obj.icash2_draw_mode == "LOCAL"
#             ):
#                 InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                     f"EXCESS-{datetime.now()}",
#                     ("min_win", 200, excess_giveout_amount),
#                     agent,
#                     count_before=number_of_single_tickets_in_largest_ticket,
#                     flavour="CASHBACK",
#                 )

#                 BALANCE_AFTER_EXCESS_GIVEOUT = (
#                     agent.icash_excesses_from_count_before - excess_giveout_amount
#                 )
#                 agent.icash_excesses_from_count_before = BALANCE_AFTER_EXCESS_GIVEOUT
#                 agent.save()

#             tickets_to_bonus = (
#                 agent.icash_sold / number_of_single_tickets_in_largest_ticket
#             )

#             if (
#                 tickets_to_bonus >= const_obj.icash2_local_bonus_threshold
#                 and instance.lottery_type == "INSTANT_CASHOUT"
#             ):
#                 agent_tickets = agent.lottoticket_set.filter(
#                     lottery_type="INSTANT_CASHOUT",
#                     paid=True,
#                     date__date=datetime.now().date(),
#                 ).values_list("stake_amount", "number_of_ticket")

#                 if agent_tickets.exists():
#                     real_play_bands = [
#                         int(amount * qty) for amount, qty in list(agent_tickets)
#                     ]
#                     bonus_band = 200  # int(random.choice(real_play_bands))

#                     const_obj = ConstantVariable.objects.all().first()
#                     const_obj.icash2_local_bonus_available = (
#                         const_obj.icash2_local_bonus_available
#                         - BLACK_WINNINGS[bonus_band][BONUS_TIER]
#                     )

#                     if not const_obj.icash2_local_bonus_available <= 0:
#                         const_obj.save()
#                         agent.icash_sold = 0
#                         agent.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"BONUS-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )

#                     agent.icash2_bonus_left = (
#                         agent.icash2_bonus_left - BLACK_WINNINGS[bonus_band][BONUS_TIER]
#                     )

#                     if not agent.icash2_bonus_left <= 0:
#                         agent.icash_sold = 0
#                         agent.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"BONUS-LOC-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )

#             if (
#                 (
#                     const_obj.global_agent_icash_sold
#                     / number_of_single_tickets_in_largest_ticket
#                 )
#                 >= const_obj.icash2_local_bonus_threshold
#                 and instance.lottery_type == "INSTANT_CASHOUT"
#             ):
#                 print("HERE 9..!!!")
#                 agent_tickets = agent.lottoticket_set.filter(
#                     lottery_type="INSTANT_CASHOUT",
#                     paid=True,
#                     date__date=datetime.now().date(),
#                 ).values_list("stake_amount", "number_of_ticket")
#                 if agent_tickets.exists():
#                     real_play_bands = [
#                         int(amount * qty) for amount, qty in list(agent_tickets)
#                     ]

#                     bonus_band = 200  # int(random.choice(real_play_bands))

#                     const_obj = ConstantVariable.objects.all().first()
#                     const_obj.icash2_global_bonus_available = (
#                         const_obj.icash2_global_bonus_available
#                         - BONUS_WINNING[bonus_band][BONUS_TIER]
#                     )
#                     const_obj.global_agent_icash_sold = 0

#                     if not const_obj.icash2_global_bonus_available < 0:
#                         const_obj.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"GLOB-BONUS-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )


# @receiver(post_save, sender=LottoTicket)
# def quika_icash_different_local(sender, instance: LottoTicket, created, **kwargs):

#     import redis
#     conn = redis.Redis('localhost')

#     user = {"Name":"Pradeep", "Company":"SCTL", "Address":"Mumbai", "Location":"RCP"}

#     conn.hmset("pythonDict", user)

#     print(conn.hgetall("pythonDict"))


#     print("#")
#     print("#")
#     print("#")
#     print("              ")
#     print("              ")
#     print("              ")
#     print(conn.hgetall("pythonDict"))
#     print("              ")
#     print("              ")
#     print("#")
#     print("#")
#     print("#")
#     print("#")
#     print("#")

#     from math import ceil

#     DEFAULT_AGENT_FOR_WEB = 274

#     if not instance.paid:
#         return

#     if not instance.agent_profile:
#         instance.is_agent = True
#         agent_profile = Agent.objects.filter(id=DEFAULT_AGENT_FOR_WEB)
#         instance.agent_profile = agent_profile.last()
#         instance.save()

#     if instance.icash_2_counted or not instance.is_agent:
#         return

#     if instance.lottery_type not in ["INSTANT_CASHOUT", "QUIKA"]:
#         return

#     WIN_GIVEN_OUT = False
#     const_obj: ConstantVariable = ConstantVariable.objects.all().last()

#     if const_obj.icash_to_use != "NEW ICASH":
#         return

#     print("              ")
#     print("GOT PASST HERE3")
#     print("              ")
#     print("              ")
#     print("#")

#     # def round_down(num):
#     #     return num//100 * 100

#     WHITE_WINNINGS = winnings(const_obj=const_obj)

#     BLACK_WINNINGS = {
#         200: {
#             "least_win": 100,
#             "minor_win": 350,
#             "min_win": round_down(
#                 4500 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 5400 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": 1400,
#         },
#         500: {
#             "least_win": 700,
#             "minor_win": 850,
#             "min_win": round_down(
#                 7500 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 13500 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         750: {
#             "least_win": 1125,
#             "minor_win": 1200,
#             "min_win": round_down(
#                 9000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 15750 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         1000: {
#             "least_win": 500,
#             "minor_win": 1000,
#             "min_win": round_down(
#                 10800 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 13000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 18000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         1250: {
#             "least_win": 1250,
#             "minor_win": 1450,
#             "min_win": round_down(
#                 12000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 14000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 18750 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         1300: {
#             "least_win": 1300,
#             "minor_win": 1550,
#             "min_win": round_down(
#                 13500 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "mid_win": round_down(
#                 16000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#             "max_win": round_down(
#                 22500 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#         1500: {
#             "least_win": 1500,
#             "minor_win": 1750,
#             "min_win": 2100,
#             "mid_win": 2600,
#             "max_win": round_down(
#                 25000 / (const_obj.icash_winnings_divisor * 1.666666666666667)
#             ),
#         },
#     }

#     # {"200": 0, "500": 0, "750": 0, "1000": 0, "1250": 0, "1300": 0, "1500": 0}
#     actual_stake_amount = str(int(instance.stake_amount * instance.number_of_ticket))

#     agent = instance.agent_profile

#     agent_flavour_list = agent.icash_flavour_dict.get(str((actual_stake_amount)), [])
#     global_flavour_list = const_obj.icash_flavour_dict.get(
#         str((actual_stake_amount)), []
#     )

#     const_obj = ConstantVariable.objects.all().last()

#     if not len(agent_flavour_list):
#         agent.icash_flavour_dict[
#             str((actual_stake_amount))
#         ] = const_obj.build_flavour_list()
#         agent.save()

#     if not len(global_flavour_list):
#         const_obj.icash_flavour_dict[
#             str((actual_stake_amount))
#         ] = const_obj.build_flavour_list()
#         const_obj.save()

#     agent_current_flavour = agent.icash_flavour_dict[str((actual_stake_amount))][0]
#     global_current_flavour = const_obj.icash_flavour_dict[str((actual_stake_amount))][0]

#     agent.icash_flavour_dict[str((actual_stake_amount))] = agent.icash_flavour_dict[
#         str((actual_stake_amount))
#     ][1:]
#     const_obj.icash_flavour_dict[
#         str((actual_stake_amount))
#     ] = const_obj.icash_flavour_dict[str((actual_stake_amount))][1:]

#     new_agent_dict = dict(agent.icash_flavour_dict)
#     new_global_dict = dict(const_obj.icash_flavour_dict)

#     new_agent_dict = dict(agent.icash_flavour_dict)
#     new_global_dict = dict(const_obj.icash_flavour_dict)

#     GIVER_RANGE1, GIVER_RANGE2 = const_obj.giver_threshold.split(",")

#     # giver_threshold = InstantCashGiverThresholds.objects.first()
#     # giver_threshold.GIVER_THRESHOLD_DICT[actual_stake_amount] = F(GIVER_THRESHOLD_DICT[actual_stake_amount]) + 1
#     # giver_threshold.save()


#     global_count_to_giver = const_obj.new_quika_icash_count_to_giver[
#         str(actual_stake_amount)
#     ]

#     agent_count_to_giver = agent.new_quika_icash_count_to_giver[
#         str(actual_stake_amount)
#     ]


#     GIVER_THRESHOLD = random.randint(int(GIVER_RANGE1), int(GIVER_RANGE2))

#     # GIVER_THRESHOLD = GIVER_THRESHOLD_DICT[str(actual_stake_amount)]

#     WINNINGS_DICT = {"WHITE": WHITE_WINNINGS, "BLACK": BLACK_WINNINGS}

#     GLOBAL_WINNINGS = WINNINGS_DICT[global_current_flavour]
#     AGENT_LOCAL_WINNINGS = WINNINGS_DICT[agent_current_flavour]

#     BONUS_FLAVOUR = random.choice(const_obj.icash_bonus_bias_dict["flavours"])
#     BONUS_WINNING = WINNINGS_DICT[BONUS_FLAVOUR]
#     BONUS_TIER = random.choice(const_obj.icash_bonus_bias_dict["tiers"])

#     print(BONUS_FLAVOUR, BONUS_TIER, const_obj.icash_bonus_bias_dict["tiers"])

#     const_obj = ConstantVariable.objects.all().first()

#     def get_averages(band):
#         values = (
#             [band["least_win"]]
#             + [band["minor_win"]]
#             + [band["min_win"]]
#             + [band["mid_win"]]
#             + [band["max_win"]]
#         )

#         return sum(values) / len(values)

#     if instance.lottery_type == "INSTANT_CASHOUT":
#         if instance.paid and instance.rtp > 0 and instance.is_agent:
#             print("HERE TOO..!!!!!!")

#             agent = instance.agent_profile
#             agent.icash_sold = agent.icash_sold + 1
#             instance.icash_2_counted = True
#             instance.icash_counted = True
#             instance.save()

#             tickets_per_teir = instance.number_of_ticket

#             band_average = get_averages(GLOBAL_WINNINGS[int(actual_stake_amount)])
#             rtp = instance.rtp * instance.number_of_ticket

#             num_win_tickets = rtp / band_average
#             print(
#                 "NUM WINNING TICKS ::::",
#                 num_win_tickets,
#                 "\n",
#                 f"Inst-RTP::{instance.rtp}\n",
#                 f"Num tiks{instance.number_of_ticket}\n",
#                 f"Tot RTP{rtp}\n",
#                 band_average,
#                 "\n",
#                 GLOBAL_WINNINGS[int(actual_stake_amount)],
#             )
#             percent_win_ticket = num_win_tickets / instance.number_of_ticket

#             count_before = (1 / percent_win_ticket) / instance.number_of_ticket
#             icash_excesses_from_count_before = (
#                 instance.number_of_ticket * instance.rtp
#             ) * (ceil(count_before) - count_before)
#             print("EXCESSES FROM CB4 ::::", icash_excesses_from_count_before)

#             print("BAND RTP ::::", rtp, "COUNT BEF ::::", count_before)
#             print(
#                 "A-FLAVOUR ::::",
#                 agent_current_flavour,
#                 "G-FLAVOUR BEF ::::",
#                 global_current_flavour,
#             )
#             print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

#             if not agent.icash_sold_dict.get(str(actual_stake_amount)):
#                 agent.icash_sold_dict[str(actual_stake_amount)] = 0

#             if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
#                 const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

#             agent.icash_sold_dict[str(actual_stake_amount)] = (
#                 agent.icash_sold_dict.get(str(actual_stake_amount)) + 1
#             )

#             const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + 1
#             const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                 const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + 1
#             )

#             win_rank_ratio = (
#                 ["least_win"]
#                 + ["minor_win"]
#                 + ["min_win"]
#                 + ["mid_win"]
#                 + ["max_win"]
#             )
#             win_rank_choice = random.choice(win_rank_ratio)

#             if const_obj.icash_to_use == "NEW ICASH":
#                 const_obj.save()
#                 agent.save()

#                 if (
#                     global_count_to_giver >= GIVER_THRESHOLD
#                     and const_obj.icash2_draw_mode == "GLOBAL"
#                 ):
#                     # Handle giver for GLOBAL
#                     print("HERE 1..!!!")

#                     const_obj.global_agent_icash_sold = (
#                         const_obj.global_agent_icash_sold - 1
#                     )
#                     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                         const_obj.global_agent_icash_sold_dict.get(
#                             str(actual_stake_amount)
#                         )
#                         - 1
#                     )
#                     const_obj.save()
#                     WIN_GIVEN_OUT = True

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     # const_obj.count_to_giver = 0
#                     # const_obj.save()
#                     ConstantVariable.update_giver(actual_stake_amount, reset=True)
#                     return

#                 if (
#                     agent_count_to_giver >= GIVER_THRESHOLD
#                     and const_obj.icash2_draw_mode == "LOCAL"
#                 ):
#                     # Handle giver for LOCAL

#                     agent.icash_sold_dict[str(actual_stake_amount)] = (
#                         agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
#                     )
#                     agent.save()
#                     WIN_GIVEN_OUT = True
#                     # Offset the number of tickets sold because of the giver

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             "min_win",
#                             actual_stake_amount,
#                             actual_stake_amount,
#                         ),
#                         agent=agent,
#                         count_before=GIVER_THRESHOLD * 10,
#                         flavour="CASHBACK",
#                     )
#                     # agent.count_to_giver = 0
#                     # agent.save()
#                     agent.update_giver(actual_stake_amount, reset=True)
#                     return

#                 # if (
#                 #     global_count_to_giver >= GIVER_THRESHOLD
#                 #     and const_obj.icash2_draw_mode == "GLOBAL"
#                 # ):
#                 #     # Handle giver for GLOBAL

#                 #     const_obj.global_agent_icash_sold = (
#                 #         const_obj.global_agent_icash_sold - 1
#                 #     )
#                 #     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
#                 #         const_obj.global_agent_icash_sold_dict.get(
#                 #             str(actual_stake_amount)
#                 #         )
#                 #         - 1
#                 #     )
#                 #     const_obj.save()
#                 #     WIN_GIVEN_OUT = True

#                 #     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                 #         f"{datetime.now()}",
#                 #         (
#                 #             "min_win",
#                 #             actual_stake_amount,
#                 #             actual_stake_amount,
#                 #         ),
#                 #         count_before=GIVER_THRESHOLD * 10,
#                 #         flavour="CASHBACK",
#                 #     )
#                 #     # const_obj.count_to_giver = 0
#                 #     # const_obj.save()
#                 #     ConstantVariable.update_giver(actual_stake_amount, reset=True)
#                 #     return

#                 # if (
#                 #     agent_count_to_giver >= GIVER_THRESHOLD
#                 #     and const_obj.icash2_draw_mode == "LOCAL"
#                 # ):
#                 #     # Handle giver for LOCAL
#                 #     print("HERE 4..!!!")

#                 #     agent.icash_sold_dict[str(actual_stake_amount)] = (
#                 #         agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
#                 #     )
#                 #     agent.save()
#                 #     WIN_GIVEN_OUT = True
#                 #     # Offset the number of tickets sold because of the giver

#                 #     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                 #         f"{datetime.now()}",
#                 #         (
#                 #             "min_win",
#                 #             actual_stake_amount,
#                 #             actual_stake_amount,
#                 #         ),
#                 #         agent=agent,
#                 #         count_before=GIVER_THRESHOLD * 10,
#                 #         flavour="CASHBACK",
#                 #     )
#                 #     agent.update_giver(actual_stake_amount, reset=True)
#                 #     agent.save()
#                 #     return

#                 if (
#                     (
#                         const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)]
#                         / tickets_per_teir
#                     )
#                     >= count_before
#                 ) and const_obj.icash2_draw_mode == "GLOBAL":

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             win_rank_choice,
#                             actual_stake_amount,
#                             GLOBAL_WINNINGS[int(actual_stake_amount)][win_rank_choice],
#                         ),
#                         count_before=count_before,
#                         flavour=global_current_flavour,
#                     )
#                     const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0
#                     const_obj.icash_excesses_from_count_before = (
#                         const_obj.icash_excesses_from_count_before
#                         + icash_excesses_from_count_before
#                     )
#                     const_obj.save()

#                     # const_obj.count_to_giver = const_obj.count_to_giver + 1
#                     ConstantVariable.update_giver(actual_stake_amount)
#                     WIN_GIVEN_OUT = True

#                 print("              ")
#                 print("GOT PASST HERE4")
#                 print("              ")
#                 print("              ")
#                 print("#")

#                 if instance.agent_profile.icash2_draw_mode == "LOCAL":
#                     print("              ")
#                     print("GOT PASST HERE4")
#                     print("              ")
#                     print("              ")
#                     print("#")

#                     """ New implementation for local winning """

#                     const_obj = ConstantVariable.objects.all().last()

#                     win_tiers = const_obj.icash2_quika_giveout_tier

#                     if win_tiers:
#                         win_rank_choice = win_tiers[0]
#                     else:
#                         win_tiers = ["least_win", "minor_win", "min_win", "mid_win", "least_win", "minor_win", "min_win", "mid_win", "max_win"]
#                         win_rank_choice = win_tiers[0]

#                     ConstantVariable.objects.all().update(icash2_quika_giveout_tier=win_tiers[1:])

#                     winning = AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][
#                                     win_rank_choice
#                                 ]

#                     Agent.objects.filter(id = instance.agent_profile.id).\
#                                                 update(icash_sold_amount=F("icash_sold_amount")+instance.rtp)

#                     agent = Agent.objects.get(id = instance.agent_profile.id)
#                     total_tickets_sold = agent.icash_sold_amount

#                     InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                         f"{datetime.now()}",
#                         (
#                             win_rank_choice,
#                             actual_stake_amount,
#                             AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][
#                                 win_rank_choice
#                             ],
#                         ),
#                         agent=agent,
#                         count_before=count_before,
#                         flavour=agent_current_flavour,
#                     )
#                     agent.icash_sold_dict[str(actual_stake_amount)] = 0

#                     agent.icash_excesses_from_count_before = (
#                         agent.icash_excesses_from_count_before
#                         + icash_excesses_from_count_before
#                     )
#                     agent.save()
#                     agent.update_giver(actual_stake_amount)
#                     WIN_GIVEN_OUT = True

#             ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
#             number_of_single_tickets_in_largest_ticket = random.randint(
#                 int(ticket_min), int(ticket_max)
#             )

#             if WIN_GIVEN_OUT:
#                 ConstantVariable.objects.all().update(icash_flavour_dict=new_agent_dict)
#                 Agent.objects.filter().update(icash_flavour_dict=new_global_dict)

#             (
#                 thresholdx,
#                 thresholdy,
#                 thresholdz,
#             ) = const_obj.excesses_giveout_threshold.split(",")
#             excess_giveout_amount = random.choice(
#                 list(range(int(thresholdx), int(thresholdy), int(thresholdz)))
#             )
#             print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
#             print("$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$")
#             print("EXCESS AMOUNT :::", excess_giveout_amount)
#             BALANCE_AFTER_EXCESS_GIVEOUT = 0

#             if (
#                 const_obj.icash_excesses_from_count_before >= excess_giveout_amount
#                 and const_obj.icash2_draw_mode == "GLOBAL"
#             ):
#                 InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                     f"EXCESS-{datetime.now()}",
#                     ("min_win", 150, excess_giveout_amount),
#                     count_before=number_of_single_tickets_in_largest_ticket,
#                     flavour=BONUS_FLAVOUR,
#                 )

#                 BALANCE_AFTER_EXCESS_GIVEOUT = (
#                     const_obj.icash_excesses_from_count_before - excess_giveout_amount
#                 )
#                 ConstantVariable.objects.all().update(
#                     icash_excesses_from_count_before=BALANCE_AFTER_EXCESS_GIVEOUT
#                 )

#             if (
#                 agent.icash_excesses_from_count_before >= excess_giveout_amount
#                 and const_obj.icash2_draw_mode == "LOCAL"
#             ):
#                 InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                     f"EXCESS-{datetime.now()}",
#                     ("min_win", 150, excess_giveout_amount),
#                     agent,
#                     count_before=number_of_single_tickets_in_largest_ticket,
#                     flavour=BONUS_FLAVOUR,
#                 )

#                 BALANCE_AFTER_EXCESS_GIVEOUT = (
#                     agent.icash_excesses_from_count_before - excess_giveout_amount
#                 )
#                 agent.icash_excesses_from_count_before = BALANCE_AFTER_EXCESS_GIVEOUT
#                 agent.save()

#             tickets_to_bonus = (
#                 agent.icash_sold / number_of_single_tickets_in_largest_ticket
#             )

#             if tickets_to_bonus >= const_obj.icash2_local_bonus_threshold:
#                 agent_tickets = agent.lottoticket_set.filter(
#                     lottery_type="INSTANT_CASHOUT",
#                     paid=True,
#                     date__date=datetime.now().date(),
#                 ).values_list("stake_amount", "number_of_ticket")

#                 if agent_tickets.exists():
#                     real_play_bands = [
#                         int(amount * qty) for amount, qty in list(agent_tickets)
#                     ]
#                     bonus_band = int(random.choice(real_play_bands))

#                     const_obj = ConstantVariable.objects.all().first()
#                     const_obj.icash2_local_bonus_available = (
#                         const_obj.icash2_local_bonus_available
#                         - BLACK_WINNINGS[bonus_band][BONUS_TIER]
#                     )

#                     if not const_obj.icash2_local_bonus_available <= 0:
#                         const_obj.save()
#                         agent.icash_sold = 0
#                         agent.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"BONUS-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )

#                     agent.icash2_bonus_left = (
#                         agent.icash2_bonus_left - BLACK_WINNINGS[bonus_band][BONUS_TIER]
#                     )

#                     if not agent.icash2_bonus_left <= 0:
#                         agent.icash_sold = 0
#                         agent.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"BONUS-LOC-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )

#             if (
#                 const_obj.global_agent_icash_sold
#                 / number_of_single_tickets_in_largest_ticket
#             ) >= const_obj.icash2_local_bonus_threshold:
#                 print("HERE 9..!!!")
#                 agent_tickets = agent.lottoticket_set.filter(
#                     lottery_type="INSTANT_CASHOUT",
#                     paid=True,
#                     date__date=datetime.now().date(),
#                 ).values_list("stake_amount", "number_of_ticket")
#                 if agent_tickets.exists():
#                     real_play_bands = [
#                         int(amount * qty) for amount, qty in list(agent_tickets)
#                     ]

#                     bonus_band = int(random.choice(real_play_bands))

#                     const_obj = ConstantVariable.objects.all().first()
#                     const_obj.icash2_global_bonus_available = (
#                         const_obj.icash2_global_bonus_available
#                         - BONUS_WINNING[bonus_band][BONUS_TIER]
#                     )
#                     const_obj.global_agent_icash_sold = 0

#                     if not const_obj.icash2_global_bonus_available < 0:
#                         const_obj.save()

#                         InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
#                             f"GLOB-BONUS-{datetime.now()}",
#                             (
#                                 BONUS_TIER,
#                                 bonus_band,
#                                 BONUS_WINNING[bonus_band][BONUS_TIER],
#                             ),
#                             agent,
#                             count_before=number_of_single_tickets_in_largest_ticket,
#                             flavour=BONUS_FLAVOUR,
#                         )


# @receiver(post_save, sender=LottoTicket)
def handle_new_icash_flavoured(sender, instance: LottoTicket, created, **kwargs):
    print("HERE!!!!!!!!!!!!!!!!!!")

    fields_to_exclude = {
        "system_generated_num",
    }
    # automatically populate a list with all fields, except the ones you want to exclude
    [f.name for f in LottoTicket._meta.get_fields() if f.name not in fields_to_exclude and not f.auto_created]

    if not instance.paid or instance.icash_2_counted or not instance.is_agent:
        return
    if instance.lottery_type != "INSTANT_CASHOUT":
        return

    print("THERE THERE!!!!!!!!!!!!!!!!!!")
    WIN_GIVEN_OUT = False
    const_obj: ConstantVariable = ConstantVariable.objects.all().last()

    if const_obj.icash_to_use != "NEW ICASH":
        return

    WHITE_WINNINGS = {
        150: {
            "min_win": 4500 / const_obj.icash_winnings_divisor,
            "mid_win": 5400 / const_obj.icash_winnings_divisor,
            "max_win": 11250 / const_obj.icash_winnings_divisor,
        },
        300: {
            "min_win": 7500 / const_obj.icash_winnings_divisor,
            "mid_win": 9000 / const_obj.icash_winnings_divisor,
            "max_win": 13500 / const_obj.icash_winnings_divisor,
        },
        450: {
            "min_win": 9000 / const_obj.icash_winnings_divisor,
            "mid_win": 10800 / const_obj.icash_winnings_divisor,
            "max_win": 15750 / const_obj.icash_winnings_divisor,
        },
        600: {
            "min_win": 10800 / const_obj.icash_winnings_divisor,
            "mid_win": 13000 / const_obj.icash_winnings_divisor,
            "max_win": 18000 / const_obj.icash_winnings_divisor,
        },
        750: {
            "min_win": 12000 / const_obj.icash_winnings_divisor,
            "mid_win": 14000 / const_obj.icash_winnings_divisor,
            "max_win": 18750 / const_obj.icash_winnings_divisor,
        },
        900: {
            "min_win": 13500 / const_obj.icash_winnings_divisor,
            "mid_win": 16000 / const_obj.icash_winnings_divisor,
            "max_win": 22500 / const_obj.icash_winnings_divisor,
        },
        1000: {
            "min_win": 15000 / const_obj.icash_winnings_divisor,
            "mid_win": 18000 / const_obj.icash_winnings_divisor,
            "max_win": 25000 / const_obj.icash_winnings_divisor,
        },
    }

    BLACK_WINNINGS = {
        150: {
            "min_win": 4500 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 5400 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 11250 / (const_obj.icash_winnings_divisor * 2),
        },
        300: {
            "min_win": 7500 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 9000 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 13500 / (const_obj.icash_winnings_divisor * 2),
        },
        450: {
            "min_win": 9000 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 10800 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 15750 / (const_obj.icash_winnings_divisor * 2),
        },
        600: {
            "min_win": 10800 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 13000 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 18000 / (const_obj.icash_winnings_divisor * 2),
        },
        750: {
            "min_win": 12000 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 14000 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 18750 / (const_obj.icash_winnings_divisor * 2),
        },
        900: {
            "min_win": 13500 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 16000 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 22500 / (const_obj.icash_winnings_divisor * 2),
        },
        1000: {
            "min_win": 15000 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 18000 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 25000 / (const_obj.icash_winnings_divisor * 2),
        },
    }

    actual_stake_amount = str(int(instance.stake_amount * instance.number_of_ticket))

    agent = instance.agent_profile

    agent_flavour_list = agent.icash_flavour_dict.get(str((actual_stake_amount)), [])
    global_flavour_list = const_obj.icash_flavour_dict.get(str((actual_stake_amount)), [])

    const_obj = ConstantVariable.objects.all().last()

    if not len(agent_flavour_list):
        agent.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        agent.save()

    if not len(global_flavour_list):
        const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        const_obj.save()

    agent_current_flavour = agent.icash_flavour_dict[str((actual_stake_amount))][0]
    global_current_flavour = const_obj.icash_flavour_dict[str((actual_stake_amount))][0]

    agent.icash_flavour_dict[str((actual_stake_amount))] = agent.icash_flavour_dict[str((actual_stake_amount))][1:]
    const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.icash_flavour_dict[str((actual_stake_amount))][1:]

    # new_agent_dict = dict(agent.icash_flavour_dict)
    # new_global_dict = dict(const_obj.icash_flavour_dict)

    new_agent_dict = dict(agent.icash_flavour_dict)
    new_global_dict = dict(const_obj.icash_flavour_dict)

    GIVER_RANGE1, GIVER_RANGE2 = const_obj.giver_threshold.split(",")
    GIVER_THRESHOLD = random.randint(int(GIVER_RANGE1), int(GIVER_RANGE2))

    WINNINGS_DICT = {"WHITE": WHITE_WINNINGS, "BLACK": BLACK_WINNINGS}

    GLOBAL_WINNINGS = WINNINGS_DICT[global_current_flavour]
    AGENT_LOCAL_WINNINGS = WINNINGS_DICT[agent_current_flavour]

    BONUS_FLAVOUR = random.choice(const_obj.icash_bonus_bias_dict["flavours"])
    BONUS_WINNING = WINNINGS_DICT[BONUS_FLAVOUR]
    BONUS_TIER = random.choice(const_obj.icash_bonus_bias_dict["tiers"])

    print(BONUS_FLAVOUR, BONUS_TIER, const_obj.icash_bonus_bias_dict["tiers"])

    const_obj = ConstantVariable.objects.all().first()

    get_averages = lambda vals: sum(vals.values()) / 3  # noqa

    if instance.lottery_type == "INSTANT_CASHOUT":
        if instance.paid and instance.rtp > 0 and instance.is_agent:
            print("HERE TOO..!!!!!!")

            agent = instance.agent_profile
            agent.icash_sold = agent.icash_sold + 1
            instance.icash_2_counted = True
            instance.icash_counted = True
            instance.save()

            tickets_per_teir = instance.number_of_ticket

            band_average = get_averages(GLOBAL_WINNINGS[int(actual_stake_amount)])
            rtp = instance.rtp * instance.number_of_ticket

            num_win_tickets = rtp / band_average
            print(
                "NUM WINNING TICKS ::::",
                num_win_tickets,
                "\n",
                f"Inst-RTP::{instance.rtp}\n",
                f"Num tiks{instance.number_of_ticket}\n",
                f"Tot RTP{rtp}\n",
                band_average,
                "\n",
                GLOBAL_WINNINGS[int(actual_stake_amount)],
            )
            percent_win_ticket = num_win_tickets / instance.number_of_ticket

            count_before = (1 / percent_win_ticket) / instance.number_of_ticket

            print("BAND RTP ::::", rtp, "COUNT BEF ::::", count_before)
            print(
                "A-FLAVOUR ::::",
                agent_current_flavour,
                "G-FLAVOUR BEF ::::",
                global_current_flavour,
            )
            print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

            if not agent.icash_sold_dict.get(str(actual_stake_amount)):
                agent.icash_sold_dict[str(actual_stake_amount)] = 0

            if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
                const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

            agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) + 1

            const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + 1
            const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + 1
            )

            win_rank_ratio = ["min_win"] + ["mid_win"] + ["max_win"]
            win_rank_choice = random.choice(win_rank_ratio)

            if const_obj.icash_to_use == "NEW ICASH":
                const_obj.save()
                agent.save()

                if const_obj.count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "GLOBAL":
                    # Handle giver for GLOBAL

                    const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold - 1
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                        const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) - 1
                    )
                    const_obj.save()
                    WIN_GIVEN_OUT = True

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    const_obj.count_to_giver = 0
                    const_obj.save()
                    return

                if agent.count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "LOCAL":
                    # Handle giver for LOCAL

                    agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
                    agent.save()
                    WIN_GIVEN_OUT = True
                    # Offset the number of tickets sold because of the giver

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        agent=agent,
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    agent.count_to_giver = 0
                    agent.save()
                    return

                if const_obj.count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "GLOBAL":
                    # Handle giver for GLOBAL

                    const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold - 1
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                        const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) - 1
                    )
                    const_obj.save()
                    WIN_GIVEN_OUT = True

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    const_obj.count_to_giver = 0
                    const_obj.save()
                    return

                if agent.count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "LOCAL":
                    # Handle giver for LOCAL

                    agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
                    agent.save()
                    WIN_GIVEN_OUT = True
                    # Offset the number of tickets sold because of the giver

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        agent=agent,
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    agent.count_to_giver = 0
                    agent.save()
                    return

                if (
                    (const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir) >= count_before
                ) and const_obj.icash2_draw_mode == "GLOBAL":
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            GLOBAL_WINNINGS[int(actual_stake_amount)][win_rank_choice],
                        ),
                        count_before=count_before,
                        flavour=global_current_flavour,
                    )
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0
                    const_obj.count_to_giver = const_obj.count_to_giver + 1
                    const_obj.save()
                    WIN_GIVEN_OUT = True

                if ((agent.icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir) >= count_before) and const_obj.icash2_draw_mode == "LOCAL":
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][win_rank_choice],
                        ),
                        agent=agent,
                        count_before=count_before,
                        flavour=agent_current_flavour,
                    )
                    agent.icash_sold_dict[str(actual_stake_amount)] = 0
                    agent.count_to_giver = agent.count_to_giver + 1
                    agent.save()
                    WIN_GIVEN_OUT = True

            ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
            number_of_single_tickets_in_largest_ticket = random.randint(int(ticket_min), int(ticket_max))

            if WIN_GIVEN_OUT:
                ConstantVariable.objects.all().update(icash_flavour_dict=new_agent_dict)
                Agent.objects.filter().update(icash_flavour_dict=new_global_dict)

            if (agent.icash_sold / number_of_single_tickets_in_largest_ticket) >= const_obj.icash2_local_bonus_threshold:
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")

                if agent_tickets.exists():
                    real_play_bands = [int(amount * qty) for amount, qty in list(agent_tickets)]
                    bonus_band = int(random.choice(real_play_bands))

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_local_bonus_available = const_obj.icash2_local_bonus_available - BLACK_WINNINGS[bonus_band][BONUS_TIER]

                    if not const_obj.icash2_local_bonus_available <= 0:
                        const_obj.save()
                        agent.icash_sold = 0
                        agent.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            f"BONUS-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )

            if (const_obj.global_agent_icash_sold / number_of_single_tickets_in_largest_ticket) >= const_obj.icash2_local_bonus_threshold:
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")
                if agent_tickets.exists():
                    real_play_bands = [int(amount * qty) for amount, qty in list(agent_tickets)]

                    bonus_band = int(random.choice(real_play_bands))

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_global_bonus_available = const_obj.icash2_global_bonus_available - BONUS_WINNING[bonus_band][BONUS_TIER]
                    const_obj.global_agent_icash_sold = 0

                    if not const_obj.icash2_global_bonus_available < 0:
                        const_obj.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            f"GLOB-BONUS-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )


# @receiver(post_save, sender=LottoTicket)
def handle_new_icash_flavoured(sender, instance: LottoTicket, created, **kwargs):  # noqa
    # print("HERE!!!!!!!!!!!!!!!!!!")

    fields_to_exclude = {
        "system_generated_num",
    }
    # automatically populate a list with all fields, except the ones you want to exclude
    fields_to_update = [f.name for f in LottoTicket._meta.get_fields() if f.name not in fields_to_exclude and not f.auto_created]

    if not instance.paid or instance.icash_2_counted or not instance.is_agent:
        return
    if instance.lottery_type != "INSTANT_CASHOUT":
        return

    print("THERE THERE!!!!!!!!!!!!!!!!!!")
    WIN_GIVEN_OUT = False
    const_obj: ConstantVariable = ConstantVariable.objects.all().last()

    if const_obj.icash_to_use != "NEW ICASH":
        return

    WHITE_WINNINGS = {
        150: {
            "min_win": 4500 / const_obj.icash_winnings_divisor,
            "mid_win": 5400 / const_obj.icash_winnings_divisor,
            "max_win": 11250 / const_obj.icash_winnings_divisor,
        },
        300: {
            "min_win": 7500 / const_obj.icash_winnings_divisor,
            "mid_win": 9000 / const_obj.icash_winnings_divisor,
            "max_win": 13500 / const_obj.icash_winnings_divisor,
        },
        450: {
            "min_win": 9000 / const_obj.icash_winnings_divisor,
            "mid_win": 10800 / const_obj.icash_winnings_divisor,
            "max_win": 15750 / const_obj.icash_winnings_divisor,
        },
        600: {
            "min_win": 10800 / const_obj.icash_winnings_divisor,
            "mid_win": 13000 / const_obj.icash_winnings_divisor,
            "max_win": 18000 / const_obj.icash_winnings_divisor,
        },
        750: {
            "min_win": 12000 / const_obj.icash_winnings_divisor,
            "mid_win": 14000 / const_obj.icash_winnings_divisor,
            "max_win": 18750 / const_obj.icash_winnings_divisor,
        },
        900: {
            "min_win": 13500 / const_obj.icash_winnings_divisor,
            "mid_win": 16000 / const_obj.icash_winnings_divisor,
            "max_win": 22500 / const_obj.icash_winnings_divisor,
        },
        1000: {
            "min_win": 15000 / const_obj.icash_winnings_divisor,
            "mid_win": 18000 / const_obj.icash_winnings_divisor,
            "max_win": 25000 / const_obj.icash_winnings_divisor,
        },
    }

    BLACK_WINNINGS = {
        150: {
            "min_win": 4500 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 5400 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 11250 / (const_obj.icash_winnings_divisor * 2),
        },
        300: {
            "min_win": 7500 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 9000 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 13500 / (const_obj.icash_winnings_divisor * 2),
        },
        450: {
            "min_win": 9000 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 10800 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 15750 / (const_obj.icash_winnings_divisor * 2),
        },
        600: {
            "min_win": 10800 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 13000 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 18000 / (const_obj.icash_winnings_divisor * 2),
        },
        750: {
            "min_win": 12000 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 14000 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 18750 / (const_obj.icash_winnings_divisor * 2),
        },
        900: {
            "min_win": 13500 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 16000 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 22500 / (const_obj.icash_winnings_divisor * 2),
        },
        1000: {
            "min_win": 15000 / (const_obj.icash_winnings_divisor * 2),
            "mid_win": 18000 / (const_obj.icash_winnings_divisor * 2),
            "max_win": 25000 / (const_obj.icash_winnings_divisor * 2),
        },
    }

    actual_stake_amount = str(int(instance.stake_amount * instance.number_of_ticket))

    agent = instance.agent_profile

    agent_flavour_list = agent.icash_flavour_dict.get(str((actual_stake_amount)), [])
    global_flavour_list = const_obj.icash_flavour_dict.get(str((actual_stake_amount)), [])

    const_obj = ConstantVariable.objects.all().last()

    if not len(agent_flavour_list):
        agent.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        agent.save()

    if not len(global_flavour_list):
        const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.build_flavour_list()
        const_obj.save()

    agent_current_flavour = agent.icash_flavour_dict[str((actual_stake_amount))][0]
    global_current_flavour = const_obj.icash_flavour_dict[str((actual_stake_amount))][0]

    agent.icash_flavour_dict[str((actual_stake_amount))] = agent.icash_flavour_dict[str((actual_stake_amount))][1:]
    const_obj.icash_flavour_dict[str((actual_stake_amount))] = const_obj.icash_flavour_dict[str((actual_stake_amount))][1:]

    # new_agent_dict = dict(agent.icash_flavour_dict)
    # new_global_dict = dict(const_obj.icash_flavour_dict)

    new_agent_dict = dict(agent.icash_flavour_dict)
    new_global_dict = dict(const_obj.icash_flavour_dict)

    GIVER_RANGE1, GIVER_RANGE2 = const_obj.giver_threshold.split(",")
    GIVER_THRESHOLD = random.randint(int(GIVER_RANGE1), int(GIVER_RANGE2))

    WINNINGS_DICT = {"WHITE": WHITE_WINNINGS, "BLACK": BLACK_WINNINGS}

    GLOBAL_WINNINGS = WINNINGS_DICT[global_current_flavour]
    AGENT_LOCAL_WINNINGS = WINNINGS_DICT[agent_current_flavour]

    BONUS_FLAVOUR = random.choice(const_obj.icash_bonus_bias_dict["flavours"])
    BONUS_WINNING = WINNINGS_DICT[BONUS_FLAVOUR]
    BONUS_TIER = random.choice(const_obj.icash_bonus_bias_dict["tiers"])

    print(BONUS_FLAVOUR, BONUS_TIER, const_obj.icash_bonus_bias_dict["tiers"])

    const_obj = ConstantVariable.objects.all().first()

    get_averages = lambda vals: sum(vals.values()) / 3  # noqa

    if instance.lottery_type == "INSTANT_CASHOUT":
        if instance.paid and instance.rtp > 0 and instance.is_agent:
            print("HERE TOO..!!!!!!")

            agent = instance.agent_profile
            agent.icash_sold = agent.icash_sold + 1
            instance.icash_2_counted = True
            instance.icash_counted = True
            instance.save(update_fields=fields_to_update)
            # instance.save()

            tickets_per_teir = instance.number_of_ticket

            band_average = get_averages(GLOBAL_WINNINGS[int(actual_stake_amount)])
            rtp = instance.rtp * instance.number_of_ticket

            num_win_tickets = rtp / band_average
            print(
                "NUM WINNING TICKS ::::",
                num_win_tickets,
                "\n",
                f"Inst-RTP::{instance.rtp}\n",
                f"Num tiks{instance.number_of_ticket}\n",
                f"Tot RTP{rtp}\n",
                band_average,
                "\n",
                GLOBAL_WINNINGS[int(actual_stake_amount)],
            )
            percent_win_ticket = num_win_tickets / instance.number_of_ticket

            count_before = (1 / percent_win_ticket) / instance.number_of_ticket

            print("BAND RTP ::::", rtp, "COUNT BEF ::::", count_before)
            print(
                "A-FLAVOUR ::::",
                agent_current_flavour,
                "G-FLAVOUR BEF ::::",
                global_current_flavour,
            )
            print("BAND RTP ::::", 1, percent_win_ticket, instance.number_of_ticket)

            if not agent.icash_sold_dict.get(str(actual_stake_amount)):
                agent.icash_sold_dict[str(actual_stake_amount)] = 0

            if not const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)):
                const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0

            agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) + 1

            const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold + 1
            const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) + 1
            )

            win_rank_ratio = ["min_win"] + ["mid_win"] + ["max_win"]
            win_rank_choice = random.choice(win_rank_ratio)

            if const_obj.icash_to_use == "NEW ICASH":
                const_obj.save()
                agent.save()

                if const_obj.count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "GLOBAL":
                    # Handle giver for GLOBAL

                    const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold - 1
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                        const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) - 1
                    )
                    const_obj.save()
                    WIN_GIVEN_OUT = True

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    const_obj.count_to_giver = 0
                    const_obj.save()
                    return

                if agent.count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "LOCAL":
                    # Handle giver for LOCAL

                    agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
                    agent.save()
                    WIN_GIVEN_OUT = True
                    # Offset the number of tickets sold because of the giver

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        agent=agent,
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    agent.count_to_giver = 0
                    agent.save()
                    return

                if const_obj.count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "GLOBAL":
                    # Handle giver for GLOBAL

                    const_obj.global_agent_icash_sold = const_obj.global_agent_icash_sold - 1
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = (
                        const_obj.global_agent_icash_sold_dict.get(str(actual_stake_amount)) - 1
                    )
                    const_obj.save()
                    WIN_GIVEN_OUT = True

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    const_obj.count_to_giver = 0
                    const_obj.save()
                    return

                if agent.count_to_giver >= GIVER_THRESHOLD and const_obj.icash2_draw_mode == "LOCAL":
                    # Handle giver for LOCAL

                    agent.icash_sold_dict[str(actual_stake_amount)] = agent.icash_sold_dict.get(str(actual_stake_amount)) - 1
                    agent.save()
                    WIN_GIVEN_OUT = True
                    # Offset the number of tickets sold because of the giver

                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            "min_win",
                            actual_stake_amount,
                            actual_stake_amount,
                        ),
                        agent=agent,
                        count_before=GIVER_THRESHOLD * 10,
                        flavour="CASHBACK",
                    )
                    agent.count_to_giver = 0
                    agent.save()
                    return

                if (
                    (const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir) >= count_before
                ) and const_obj.icash2_draw_mode == "GLOBAL":
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            GLOBAL_WINNINGS[int(actual_stake_amount)][win_rank_choice],
                        ),
                        count_before=count_before,
                        flavour=global_current_flavour,
                    )
                    const_obj.global_agent_icash_sold_dict[str(actual_stake_amount)] = 0
                    const_obj.count_to_giver = const_obj.count_to_giver + 1
                    const_obj.save()
                    WIN_GIVEN_OUT = True

                if ((agent.icash_sold_dict[str(actual_stake_amount)] / tickets_per_teir) >= count_before) and const_obj.icash2_draw_mode == "LOCAL":
                    InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                        f"{datetime.now()}",
                        (
                            win_rank_choice,
                            actual_stake_amount,
                            AGENT_LOCAL_WINNINGS[int(actual_stake_amount)][win_rank_choice],
                        ),
                        agent=agent,
                        count_before=count_before,
                        flavour=agent_current_flavour,
                    )
                    agent.icash_sold_dict[str(actual_stake_amount)] = 0
                    agent.count_to_giver = agent.count_to_giver + 1
                    agent.save()
                    WIN_GIVEN_OUT = True

            ticket_min, ticket_max = const_obj.ratio_for_icash2_bonus.split(",")
            number_of_single_tickets_in_largest_ticket = random.randint(int(ticket_min), int(ticket_max))

            if WIN_GIVEN_OUT:
                ConstantVariable.objects.all().update(icash_flavour_dict=new_agent_dict)
                Agent.objects.filter().update(icash_flavour_dict=new_global_dict)

            if (agent.icash_sold / number_of_single_tickets_in_largest_ticket) >= const_obj.icash2_local_bonus_threshold:
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")

                if agent_tickets.exists():
                    real_play_bands = [int(amount * qty) for amount, qty in list(agent_tickets)]
                    bonus_band = int(random.choice(real_play_bands))

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_local_bonus_available = const_obj.icash2_local_bonus_available - BLACK_WINNINGS[bonus_band][BONUS_TIER]

                    if not const_obj.icash2_local_bonus_available <= 0:
                        const_obj.save()
                        agent.icash_sold = 0
                        agent.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            f"BONUS-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )

            if (const_obj.global_agent_icash_sold / number_of_single_tickets_in_largest_ticket) >= const_obj.icash2_local_bonus_threshold:
                agent_tickets = agent.lottoticket_set.filter(
                    lottery_type="INSTANT_CASHOUT",
                    paid=True,
                    date__date=datetime.now().date(),
                ).values_list("stake_amount", "number_of_ticket")
                if agent_tickets.exists():
                    real_play_bands = [int(amount * qty) for amount, qty in list(agent_tickets)]

                    bonus_band = int(random.choice(real_play_bands))

                    const_obj = ConstantVariable.objects.all().first()
                    const_obj.icash2_global_bonus_available = const_obj.icash2_global_bonus_available - BONUS_WINNING[bonus_band][BONUS_TIER]
                    const_obj.global_agent_icash_sold = 0

                    if not const_obj.icash2_global_bonus_available < 0:
                        const_obj.save()

                        InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                            f"GLOB-BONUS-{datetime.now()}",
                            (
                                BONUS_TIER,
                                bonus_band,
                                BONUS_WINNING[bonus_band][BONUS_TIER],
                            ),
                            agent,
                            count_before=number_of_single_tickets_in_largest_ticket,
                            flavour=BONUS_FLAVOUR,
                        )


# @receiver(post_save, sender=LottoTicket)
# def increament_count_unpaid_tickets(sender, instance: LottoTicket, created, **kwargs):

#     if created:
#         HopsRestrictor.pending_payment(instance.phone)


# @receiver(post_save, sender=LotteryBatch)
# def update_wallet_status(sender, instance, created, **kwargs):
#     if created:
#         if instance.lottery_type == "BANKER":
#             # Get the current time in UTC timezone
#             now = timezone.now()
#
#             # Add 3 hours and 30 minutes to the current time
#             count_down_time = now + timedelta(hours=3, minutes=30)
#
#             instance.draw_count_down_time = count_down_time
#             instance.save()
#     else:
#         pass


@receiver(post_save, sender=JackpotConstantVariable)
def start_i_cash_200_jackpot_winning_count(sender, instance: JackpotConstantVariable, created, **kwargs):
    if instance.count_icash_jkpt is True:
        # get updated time
        i_cash__filter_date_string = "i_cash__filter_date_string"
        print(instance.updated_at)
        RedisStore.set_data(key=i_cash__filter_date_string, value=str(instance.updated_at))
        # print()
        # set updated time to redis


# @receiver(post_save, sender=LottoTicket)
# def mobid_ad_tracker_renewals_tracker(sender, instance, created, *args, **kwargs):
#     if instance.channel == "USSD" and instance.paid is True:
#         player_phone = instance.phone

#         ad_tracker_instance = MobidTracker.objects.filter(
#             phone_number=player_phone
#         ).last()

#         if ad_tracker_instance is None:
#             return "DONE"

#         # Trigger Postback
#         ad_tracker_instance.number_of_renewals += 1
#         ad_tracker_instance.save()

#         return "DONE"
