import logging
import random
from main.models import LottoTicket, Seeder, ConstantVariable, RapidFireWinning, InstantCashoutPendingWinning
from pos_app.models import AgentConstantVariables
from django.db.models import Sum
from django.utils import timezone
from africa_lotto.models import AfricaLottoConstants, AfricaLotto
from datetime import datetime

# Configure logger for use in SeederService
logger = logging.getLogger(__name__)


def turn_rapid_fire_to_winnings():
    seeder = SeederService("GLOBAL")
    batch = seeder.agent_constant.active_rapid_fire_batch
    rapid_fire = Seeder.objects.get(batch=batch, wave__icontains="rapid fire")
    if not rapid_fire.is_on_break:
        available_winning = RapidFireWinning.objects.filter(batch=batch, is_used=False).first()
        if available_winning:
            prev_winning = RapidFireWinning.objects.filter(id__lt=available_winning.id).order_by("id").last()
            available_winning.release_at += 1
            if prev_winning and available_winning.games_count == 0:
                available_winning.games_count = prev_winning.games_count + 1
            else:
                available_winning.games_count += 1
            available_winning.save()
            available_winning.refresh_from_db()

            if available_winning.release_at == rapid_fire.release_after_event:

                win_rank_choice = "min_win"
                actual_stake_amount = 200
                winning = available_winning.amount
                InstantCashoutPendingWinning.handle_game_winning_list_pos_icash2(
                    batch=f"RAPID-FIRE-{seeder.batch}",
                    data=[win_rank_choice, actual_stake_amount, winning],
                    agent=None,
                    count_before=0,
                    flavour="BLACK",
                    is_for_pos_only=True,
                    is_avail_50_n_100=False,
                )
                available_winning.is_used = True
                available_winning.save()
                rapid_fire.used_winning_count += 1
                rapid_fire.save()
                rapid_fire.refresh_from_db()
                
                if rapid_fire.used_winning_count >= seeder.agent_constant.break_after:
                    rapid_fire.is_on_break = True
                    rapid_fire.save()
    else:
        rapid_fire.count_after_break += 1
        rapid_fire.save()
        rapid_fire.refresh_from_db()
        stop_break_threshold = rapid_fire.release_after_event * seeder.agent_constant.break_after
        if rapid_fire.count_after_break >= stop_break_threshold:
            rapid_fire.is_on_break = False
            rapid_fire.count_after_break = 0
            rapid_fire.save()


def create_seeder_batch(date, seed_time):
    """
    Create a batch number from the given date and seed time.

    Args:
        date (datetime.date): The date for the batch.
        seed_time (datetime.time): The seed time for the batch.

    Returns:
        str: A string representing the batch number in the format YYMMDDHHMMSS.
    """
    return date.strftime("%y%m%d") + seed_time.strftime("%H%M%S")


class SeederService:
    """
    Service class for managing seeders and their calculations.
    """

    def __init__(self, level, game_type="INSTANT_GAMES") -> None:
        self.level = level
        if game_type in ["INSTANT_GAMES", "BANKER", "KENYA", "KENYA_30"]:
            self.game_type = game_type
        else:
            raise ValueError("game_type must be either 'INSTANT_GAMES', 'KENYA', 'KENYA_30' or 'BANKER'")

    @property
    def seeder_instance(self):
        """
        Get the seeder instance for the current batch.
        """
        instance = Seeder.objects.filter(batch=self.batch, wave__icontains="seeder", seeder_level=self.level, is_seeder_active=True).first()
        if instance:
            instance.refresh_from_db()
        return instance

    @property
    def rapid_fire_instance(self):
        """
        Get the rapid fire instance for the current batch.
        """
        instance = Seeder.objects.filter(batch=self.batch, wave__icontains="rapid fire", seeder_level=self.level, is_seeder_active=True).first()
        if instance:
            instance.refresh_from_db()
        return instance

    @property
    def constant(self) -> ConstantVariable:
        """
        Get the constant variable instance.
        """
        instance = ConstantVariable.objects.all().first()
        if instance:
            instance.refresh_from_db()
        return instance

    @property
    def agent_constant(self) -> AgentConstantVariables:
        """
        Get the agent constant variable instance.
        """
        instance = AgentConstantVariables.objects.all().first()
        if instance:
            instance.refresh_from_db()
        return instance

    @property
    def africa_lotto_constant(self) -> AfricaLottoConstants:
        instance = AfricaLottoConstants.objects.all().first()
        if instance:
            instance.refresh_from_db()
        return instance

    @property
    def batch(self):
        """
        Get the active seeder batch.
        """
        data = self.agent_constant.active_seeder_batch_data
        return data.get(self.game_type)

    def calculate_gross_sales(self):
        """
        Calculate the gross sales based on the seeder instance created_at.
        """

        if self.game_type == "INSTANT_GAMES":
            start_datetime = (
                timezone.make_aware(datetime.combine(self.seeder_instance.date, self.seeder_instance.seed_time))
                if self.seeder_instance
                else timezone.make_aware(datetime.combine(self.rapid_fire_instance.date, self.rapid_fire_instance.seed_time))
            )
            gross_sales = (
                LottoTicket.objects.filter(
                    played_via_telco_channel=False,
                    lottery_type__in=["INSTANT_CASHOUT", "QUIKA", "VIRTUAL_SOCCER"],
                    date__gte=start_datetime,
                    drawn_for=self.level,
                ).aggregate(total_sales=Sum("amount_paid"))["total_sales"]
                or 200
            )
        elif self.game_type == "BANKER":
            start_datetime = timezone.make_aware(datetime.combine(self.seeder_instance.date, self.seeder_instance.seed_time))
            gross_sales = (
                LottoTicket.objects.filter(
                    played_via_telco_channel=False,
                    lottery_type__in=["BANKER"],
                    date__gte=start_datetime,
                    drawn_for=self.level,
                ).aggregate(total_sales=Sum("amount_paid"))["total_sales"]
                or 200
            )
        elif self.game_type == "KENYA":
            start_datetime = timezone.make_aware(datetime.combine(self.seeder_instance.date, self.seeder_instance.seed_time))
            gross_sales = (
                AfricaLotto.objects.filter(
                    game_type__in=["KENYA_LOTTO"],
                    created_at__gte=start_datetime,
                    drawn_for=self.level,
                ).aggregate(
                    total_sales=Sum("purchase_amount")
                )["total_sales"]
                or 200
            )
        elif self.game_type == "KENYA_30":
            start_datetime = timezone.make_aware(datetime.combine(self.seeder_instance.date, self.seeder_instance.seed_time))
            gross_sales = (
                AfricaLotto.objects.filter(
                    game_type__in=["KENYA_30_LOTTO"],
                    created_at__gte=start_datetime,
                    drawn_for=self.level,
                ).aggregate(
                    total_sales=Sum("purchase_amount")
                )["total_sales"]
                or 200
            )
        return gross_sales

    def get_gross_sales(self):
        """
        Get the gross sales from the seeder instance.
        """
        return self.seeder_instance.gross_sales if self.seeder_instance else self.rapid_fire_instance.gross_sales

    def total_seed_value(self):
        """
        Calculate the total seed value for the current batch.
        """
        value = Seeder.objects.filter(batch=self.batch, seeder_level=self.level).aggregate(total_seed=Sum("seed_value"))["total_seed"] or 0
        return value

    def calculate_rto(self):
        """
        Calculate the Return to Operator (RTO) based on the seeder instance and gross sales.
        """
        rto_percent = self.seeder_instance.rto_percent if self.seeder_instance else self.rapid_fire_instance.rto_percent
        return rto_percent * self.get_gross_sales()

    def calculate_rtp(self):
        """
        Calculate the Return to Player (RTP) based on the gross sales, RTO, and agent commission.
        """
        return self.get_gross_sales() - self.calculate_rto() - self.cal_agent_comission()

    def cal_agent_comission(self):
        """
        Calculate the agent commission based on the agent constant variable and gross sales.
        """

        if self.game_type == "INSTANT_GAMES":
            commission = self.agent_constant.agent_sales_commission
        elif self.game_type in ["KENYA", "BANKER", "KENYA_30"]:
            commission = self.africa_lotto_constant.agent_commission_percentage

        return commission * self.get_gross_sales()

    def calculate_seed_ratio(self, rapid_fire_only=False):
        """
        Computes the ratio of Return to Operator (RTO) to the total seed value. This ratio is a measure of the
        operational return relative to the seeding investment. If the 'rapid_fire_only' flag is set, the calculation
        is based solely on the seed value of the rapid fire instance; otherwise, it encompasses the total seed value
        across all instances.
        """
        seeder_amount = self.rapid_fire_instance.seed_value if rapid_fire_only else self.total_seed_value()
        return self.calculate_rto() / seeder_amount if seeder_amount != 0 else 0

    def calculate_gain_loss(self, rapid_fire_only=False):
        """
        Determines the financial outcome as either a gain or a loss by subtracting the total seed value from
        the Return to Operator (RTO). This outcome indicates the operational profit or deficit. When the
        'rapid_fire_only' flag is true, the calculation considers only the seed value of the rapid fire instance;
        otherwise, it includes the total seed value from all instances.
        """
        total_value = self.rapid_fire_instance.seed_value if rapid_fire_only else self.total_seed_value()
        return self.calculate_rto() - total_value

    def cal_rtp_plus_seeder(self):
        """
        Calculate the RTP plus seeder value based on the calculated RTP and seeder instance seed value.
        """
        value = self.seeder_instance.seed_value if self.seeder_instance else self.rapid_fire_instance.seed_value
        rtp_plus_seeder = self.calculate_rtp() + value
        return rtp_plus_seeder

    def calculate_effective_rtp(self):
        """
        Calculate the effective RTP based on the calculated RTP plus seeder and gross sales.
        """
        gross_sale = self.get_gross_sales()
        return ((self.cal_rtp_plus_seeder() / gross_sale) * 100) if gross_sale != 0 else 0

    def cal_rtp_multiplier(self):
        """
        Calculate the RTP multiplier based on the effective RTP and seeder instance effective RTP before.
        """
        effective_rtp_before = self.seeder_instance.effective_rtp_before if self.seeder_instance else self.rapid_fire_instance.effective_rtp_before
        return self.calculate_effective_rtp() / effective_rtp_before

    def get_effective_multiplier(self):
        """
        Get the effective multiplier based on the calculated RTP multiplier.
        """
        rtp_multiplier = self.cal_rtp_multiplier()

        if self.game_type == "INSTANT_GAMES":
            if rtp_multiplier > 5:
                effective_multiplier = random.randint(2, 5)
            else:
                effective_multiplier = rtp_multiplier
            return effective_multiplier
        elif self.game_type in ["BANKER", "KENYA", "KENYA_30"]:
            if rtp_multiplier > 7:
                effective_multiplier = random.randint(5, 7)
            elif rtp_multiplier > 6:
                effective_multiplier = random.randint(5, 6)
            elif rtp_multiplier > 5:
                effective_multiplier = random.randint(4, 5)
            elif rtp_multiplier > 4:
                effective_multiplier = random.randint(3, 4)
            elif rtp_multiplier > 3:
                effective_multiplier = random.randint(2, 3)
            elif rtp_multiplier > 2:
                effective_multiplier = random.randint(1, 2)
            else:
                effective_multiplier = rtp_multiplier
            return effective_multiplier

    def get_seeder_rtp_trigger(self):
        """
        Get the seeder RTP trigger based on the calculated RTP plus seeder and rapid fire instance seed value.
        """
        if self.rapid_fire_instance:
            rapid_fire_value = self.rapid_fire_instance.seed_value
        else:
            rapid_fire_value = 0
        return (self.cal_rtp_plus_seeder() + rapid_fire_value) / self.get_gross_sales()

    def update_seeders_in_batch(self):
        """
        Update the seeders in the current batch with new calculated values and handle seeder and rapid fire status.
        """
        logger.info("Updating seeders in batch: %s", self.batch)
        self.seeder_instance.rto_percent = self.constant.rto
        self.seeder_instance.save()
        batch = self.batch
        seeders = Seeder.objects.filter(batch=self.batch, seeder_level=self.level)[:2]
        new_rto = self.calculate_rto()
        new_gross_sale = self.calculate_gross_sales()
        new_rtp = self.calculate_rtp()
        new_seed_ratio = self.calculate_seed_ratio()
        new_gain_loss = self.calculate_gain_loss()
        new_effective_rtp = self.calculate_effective_rtp()
        new_effective_multiplier = self.get_effective_multiplier()
        new_rtp_plus_seeder = self.cal_rtp_plus_seeder()
        new_agent_commission = self.cal_agent_comission()
        new_rtp_multiplier = self.cal_rtp_multiplier()
        new_seeder_rtp_trigger = self.get_seeder_rtp_trigger()

        # Update each seeder with the new calculated values
        for seeder in seeders:
            seeder.gross_sales = new_gross_sale
            seeder.rtp = new_rtp
            seeder.rto = new_rto
            seeder.seed_ratio = new_seed_ratio
            seeder.gain_loss = new_gain_loss
            seeder.effective_rtp = new_effective_rtp
            seeder.effective_multiplier = new_effective_multiplier
            seeder.rtp_seeder = new_rtp_plus_seeder
            seeder.agent_commission = new_agent_commission
            seeder.rtp_multiplier = new_rtp_multiplier
            seeder.seeder_rtp_trigger = new_seeder_rtp_trigger
            seeder.save()

        if new_seed_ratio > self.agent_constant.seed_ratio_constant:
            seeder = self.seeder_instance
            rapid_fire = self.rapid_fire_instance

            seeder.refresh_from_db()
            seeder.is_seeder_active = False
            seeder.is_seeder_close = True
            seeder.save()

            if rapid_fire:
                rapid_fire.is_seeder_active = False
                rapid_fire.save()

            if self.agent_constant.rapid_fire_only and self.game_type in ["INSTANT_GAMES"]:
                self.create_rapid_fire_only()
            else:
                self.create_new_seeders()

        logger.info("Seeder batch %s update completed.", self.batch)
        logger.info("I am ignore threshold, %s", self.agent_constant.ignore_rapid_fire_threshold)
        if self.game_type == "INSTANT_GAMES":
            if self.agent_constant.ignore_rapid_fire_threshold:
                self.create_rapid_fire_winnings(batch)
            elif new_effective_rtp <= self.agent_constant.rapid_fire_release_threshold and self.level == "GLOBAL":
                self.create_rapid_fire_winnings(batch)

        return

    def create_rapid_fire_winnings(self, batch):
        rapid_fire = self.rapid_fire_instance
        if not rapid_fire.is_seeder_close:
            rapid_fire.refresh_from_db()
            rapid_fire.is_seeder_close = True
            rapid_fire.save()
            total = int(rapid_fire.seed_value / rapid_fire.burst_amount)
            available_winnings = [RapidFireWinning(amount=rapid_fire.burst_amount, batch=rapid_fire.batch) for _ in range(total)]
            RapidFireWinning.objects.bulk_create(available_winnings)

            constant: AgentConstantVariables = self.agent_constant
            constant.active_rapid_fire_batch = batch
            constant.save()

    def _check_rapid_fire_without_seeder(self):
        """
        Check if there is an active rapid fire without a seeder pair in the current batch.
        """
        seeders = Seeder.objects.filter(
            batch=self.batch,
            is_seeder_active=True,
            seeder_level=self.level,
            rtp_game_type="Instant Games",
        )

        rapid_fire_exists = seeders.filter(wave__icontains="rapid fire").exists()
        seeder_exists = seeders.filter(wave__icontains="seeder").exists()

        return bool(rapid_fire_exists and not seeder_exists)

    def recalculate_seedratio_and_grossprofit(self, create=True):
        rapid_fire = self.rapid_fire_instance
        logger.info(
            "Rapid Fire Instance - Batch: %s, Seed Value: %s, Is Active: %s, Is Closed: %s",
            rapid_fire.batch,
            rapid_fire.seed_value,
            rapid_fire.is_seeder_active,
            rapid_fire.is_seeder_close,
        )
        seed_ratio = self.calculate_seed_ratio(rapid_fire_only=True)
        rapid_fire.seed_ratio = seed_ratio
        rapid_fire.gain_loss = self.calculate_gain_loss(rapid_fire_only=True)
        rapid_fire.rto = self.calculate_rto()
        rapid_fire.gross_sales = self.calculate_gross_sales()
        rapid_fire.rtp = self.calculate_rtp()
        rapid_fire.effective_rtp = self.calculate_effective_rtp()
        rapid_fire.effective_multiplier = self.get_effective_multiplier()
        rapid_fire.rtp_seeder = self.cal_rtp_plus_seeder()
        rapid_fire.agent_commission = self.cal_agent_comission()
        rapid_fire.rtp_multiplier = self.cal_rtp_multiplier()
        rapid_fire.seeder_rtp_trigger = self.get_seeder_rtp_trigger()
        rapid_fire.save()
        active = True

        rapid_fire.refresh_from_db()
        logger.info(
            "Updated Rapid Fire - Batch: %s, Seed Value: %s, Seed Ratio: %s, Gain/Loss: %s, RTO: %s, Gross Sale: %s, RTP: %s, Effective RTP: %s, Effective Multiplier: %s, RTP Plus Seeder: %s, Agent Commission: %s, RTP Multiplier: %s, Seeder RTP Trigger: %s",
            rapid_fire.batch,
            rapid_fire.seed_value,
            rapid_fire.seed_ratio,
            rapid_fire.gain_loss,
            rapid_fire.rto,
            rapid_fire.gross_sales,
            rapid_fire.rtp,
            rapid_fire.effective_rtp,
            rapid_fire.effective_multiplier,
            rapid_fire.rtp_seeder,
            rapid_fire.agent_commission,
            rapid_fire.rtp_multiplier,
            rapid_fire.seeder_rtp_trigger,
        )

        self.create_rapid_fire_winnings(rapid_fire.batch)
        if seed_ratio > self.agent_constant.seed_ratio_constant:
            active = rapid_fire.is_seeder_active = False
            rapid_fire.save()
            if create:
                self.create_rapid_fire_only()
        return not active

    def calculate_effective_rtp_for_ticket(self, ticket):
        """
        Calculate the effective RTP for a given lotto ticket and update seeders in the batch.
        """
        rapid_fire_only = self.agent_constant.rapid_fire_only
        if rapid_fire_only and self.game_type in ["INSTANT_GAMES"]:
            self.recalculate_seedratio_and_grossprofit()
            ticket.seeder_status = "COMPLETE"
            ticket.effective_rtp = ticket.rtp
            ticket.save()
        elif self.game_type in ["INSTANT_GAMES"] and self._check_rapid_fire_without_seeder() and not rapid_fire_only:
            closed = self.recalculate_seedratio_and_grossprofit(create=False)
            ticket.seeder_status = "COMPLETE"
            ticket.effective_rtp = ticket.rtp
            ticket.save()
            if closed:
                self.create_new_seeders()
        else:
            effective_rtp_multiplier = self.get_effective_multiplier()
            self.update_seeders_in_batch()
            ticket.effective_rtp = effective_rtp_multiplier * ticket.rtp
            ticket.seeder_status = "COMPLETE"
            ticket.save()

        if self.game_type == "INSTANT_GAMES":
            turn_rapid_fire_to_winnings()

    def create_new_seeders(self):
        """
        Create new seeders for the current batch and update the agent constant with the active seeder batch.
        """

        seedtime = timezone.now().time()
        seeddate = timezone.now().date()
        batch = create_seeder_batch(date=seeddate, seed_time=seedtime)

        if self.game_type == "INSTANT_GAMES":
            rtp_game_type = "Instant Games"
            rtp_before = self.constant.rtp
            rto = self.constant.rto

            Seeder.objects.create(
                wave=f"rapid fire-{batch}",
                is_seeder_active=True,
                is_seeder_close=False,
                seed_time=seedtime,
                date=seeddate,
                batch=batch,
                seed_value=self.agent_constant.rapid_fire_amount,
                rtp_game_type=rtp_game_type,
                release_after_event=self.agent_constant.rapid_fire_release_after,
                burst_amount=self.agent_constant.seeder_burst_amount,
                seeder_level=self.level,
                effective_rtp_before=rtp_before,
                gross_sales=200,
                rto_percent=rto,
            )

        elif self.game_type == "BANKER":
            rtp_game_type = "Banker"
            rtp_before = self.constant.rtp_s4l_bank_wysecash
            rto = self.africa_lotto_constant.rto_percentage
        elif self.game_type == "KENYA":
            rtp_game_type = "Kenya"
            rtp_before = self.africa_lotto_constant.rtp_percentage * 100
            rto = self.africa_lotto_constant.rto_percentage
        elif self.game_type == "KENYA_30":
            rtp_game_type = "Kenya 30"
            rtp_before = self.africa_lotto_constant.rtp_percentage * 100
            rto = self.africa_lotto_constant.rto_percentage

        Seeder.objects.create(
            wave=f"seeder-{batch}",
            is_seeder_active=True,
            is_seeder_close=False,
            seed_time=seedtime,
            date=seeddate,
            batch=batch,
            seed_value=self.agent_constant.seed_amount,
            rtp_game_type=rtp_game_type,
            seeder_level=self.level,
            effective_rtp_before=rtp_before,
            gross_sales=200,
            rto_percent=rto,
        )

        constant: AgentConstantVariables = self.agent_constant
        data = constant.active_seeder_batch_data
        data[self.game_type] = batch
        constant.active_seeder_batch_data = data
        constant.save()

    def create_rapid_fire_only(self):
        """
        Create new seeders for the current batch and update the agent constant with the active seeder batch.
        """

        seedtime = timezone.now().time()
        seeddate = timezone.now().date()
        batch = create_seeder_batch(date=seeddate, seed_time=seedtime)

        if self.game_type == "INSTANT_GAMES":
            rtp_game_type = "Instant Games"
            rtp_before = self.constant.rtp

            Seeder.objects.create(
                wave=f"rapid fire-{batch}",
                is_seeder_active=True,
                is_seeder_close=False,
                seed_time=seedtime,
                date=seeddate,
                batch=batch,
                seed_value=self.agent_constant.rapid_fire_amount,
                rtp_game_type=rtp_game_type,
                release_after_event=self.agent_constant.rapid_fire_release_after,
                burst_amount=self.agent_constant.seeder_burst_amount,
                seeder_level=self.level,
                effective_rtp_before=rtp_before,
                gross_sales=200,
                rto_percent=self.constant.rto,
            )

        constant: AgentConstantVariables = self.agent_constant
        data = constant.active_seeder_batch_data
        data[self.game_type] = batch
        constant.active_seeder_batch_data = data
        constant.save()
