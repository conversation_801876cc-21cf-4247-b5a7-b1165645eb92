import os
import random
import re
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from time import sleep

from django.conf import settings
from django.contrib.admin.views.decorators import staff_member_required
from django.core.exceptions import ValidationError
from django.db.models import Q
from django.http import FileResponse, Http404, HttpResponse
from django.utils.datastructures import MultiValueDictKeyError
from django.utils.decorators import method_decorator
from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.views import APIView

from account.authentication import PabblyCustomBasicAuthentication, SuperUser2Permission
from account.helpers import log_save_operation
from account.models import User
from broad_base_communication.bbc_helper import BBCTelcoAggregator
from main.helpers.helper_functions import fetch_account_name, match_masked_phone_number
from main.helpers.redis_storage import RedisStorage
from main.helpers.watupay_manager import (
    verify_watupay_ussd_transaction,
    watupay_format_number_from_back_add_234,
)
from main.helpers.whisper_sms_managers import send_woven_callback
from main.helpers.woven_lottery_payment import lottery_payment_via_woven
from main.helpers.woven_wallet_funding import handle_woven_wallet_funding
from main.models import (
    ContactUsForm,
    ErroneousTransferRefundLog,
    LotteryBatch,
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    PabblyCallBack,
    PaymentCollectionDetail,
    PaymentTransaction,
    PayoutTransactionTable,
    SalaryForLifeParticipant,
    SalaryForLifeSponsor,
    ScratchCardPartnership,
    UserProfile,
    WatuPayCallBack,
    WemaInstantPaymentAccounts,
    WovenAccountDetail,
    WovenCallBack,
)
from main.serializers import (
    AllVFDFloatBalanceSerializer,
    ContactUsFormSerializer,
    CoreBankingFundWalletCallBackSerializer,
    ErroneousTransferRefundLogSerializer,
    FetchLottoTicketDetailsSerializer,
    FileUploadSerializer,
    LotteryBatchUUIDSerializer,
    MainLottoTicketSerializer,
    ManuallyCreateARetailWinningSerializer,
    MtnSMSCampaignSerializer,
    NonWinnersListSerializer,
    RunBankerDrawManuallySerializer,
    S4LParticipantSerializer,
    S4LSponsorSerializer,
    ScratchCardPartnershipSerializer,
    SendMtnSmsSerializer,
    VerifyAccountNumberSerializer,
    WaitlistSerializer,
    WebFundDebitPlayWinningWalletSerializer,
)
from main.tasks import (
    celery_mtn_batch_campaign2,
    celery_send_whatsapp_payment_notification_admin,
    celery_send_whatsapp_payment_notification_admin_for_soccer_payment,
    notify_agents_on_lottery_batch_draw,
    send_email,
)
# from overide_print import print
from pos_app.models import PosLotteryWinners
from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry
from pos_app.serializers import GameHistorySerializer, LottoTicketGameResultSerializer, PosLottoFilterPendingDrawSerializer
from retail_metrics.models import DailyFundingWalletActivity
from wallet_app.models import DebitCreditRecord, FloatWallet, UserWallet
from wallet_app.tasks import notify_admin_of_user_funding
from wyse_ussd.models import SoccerPrediction, UssdLotteryPayment
from wyse_ussd.tasks import share_ussd_payment_across_lottery_pool

# Create your views here.


@api_view(["POST"])
def waitlist_signup(request):
    """
    Handle waitlist signup submissions
    """
    serializer = WaitlistSerializer(data=request.data)

    if serializer.is_valid():
        try:
            serializer.save()
            return Response(
                {"status": "success", "message": "Thank you for joining our waitlist! We'll notify you when we launch.", "data": serializer.data},
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            return Response(
                {"status": "error", "message": "An error occurred while processing your request.", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    return Response({"status": "error", "message": "Invalid data provided.", "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)


class WatuPayRecievePaymentAPIView(APIView):
    def post(self, request):
        response = request.data

        print("WATUPAY CALLBACK DATA", response, "\n\n\n\n\n\n")

        sleep(5)

        # Transaction Details
        status_code = response.get("transaction_parameters")["status_code"]  # 200
        status = response["status"]
        response_message = response["message"]
        merchant_reference = response["merchant_reference"]
        transaction_reference = response["transaction_reference"]

        # Customer
        phone_number = response.get("customer")["phone_number"]

        customer_phone_number = watupay_format_number_from_back_add_234(phone=phone_number)

        # Payment Data
        amount = int(float(response.get("payment_data")["amount"]))
        response.get("payment_data")["fees"]

        # Check watupay to confirm if transaction exists
        verify_transaction = verify_watupay_ussd_transaction(transaction_reference=transaction_reference)
        if verify_transaction is False:
            pass
        else:
            # LOGIC

            # Check current batch and update
            current_batch = LotteryBatch.get_current_batch()

            # Get player by matching phone number algorithm
            phone_matcher = match_masked_phone_number(customer_phone_number)
            phone_start = phone_matcher["phone_start"]
            phone_end = phone_matcher["phone_end"]

            lottery_player = LotteryModel.objects.filter(Q(phone__startswith=phone_start) & Q(phone__endswith=phone_end)).last()
            if not lottery_player:
                pass
            else:
                number_of_instances = lottery_player.instance_number
                if (
                    status_code == 200
                    and status == "completed"
                    and response_message == "Payment Completed Successfully"
                    and merchant_reference == settings.WATUPAY_MERCHANT_REFERENCE
                ):
                    # Check if transaction is in our database
                    print(number_of_instances)
                    check_for_transaction = PaymentTransaction.objects.filter(provider_transaction_reference=transaction_reference).last()

                    if check_for_transaction:
                        if check_for_transaction.status != "SUCCESSFUL" and check_for_transaction.has_paid is False:
                            check_for_transaction.amount = float(amount)
                            check_for_transaction.has_paid = True
                            check_for_transaction.save()

                            check_for_payload = WatuPayCallBack.objects.filter(transaction=check_for_transaction).last()

                            if check_for_payload:
                                check_for_payload.payload = response
                                check_for_payload.save()
                            else:
                                # Create provider raw payload

                                WatuPayCallBack.objects.create(transaction=check_for_transaction, payload=response)

                            # share_payment_accross_pool = (
                            #     LotteryModel.share_payment_accross_pool(
                            #         amount=amount,
                            #         player=lottery_player,
                            #         instance_number=number_of_instances,
                            #     )
                            # )

                            # assign_player_to_batch = (
                            #     LotteryModel.assign_player_to_batch(
                            #         player=lottery_player
                            #     )
                            # )

                            #
                            # devjoe's code start here
                            # share_ussd_payment_across_lottery_pool
                            user_profile = UserProfile.objects.filter(
                                Q(phone_number__startswith=phone_start) & Q(phone_number__endswith=phone_end)
                            ).last()

                            # get the lottery game play id that this payment was made for
                            game_play_id = UssdLotteryPayment.objects.filter(
                                game_play_id__isnull=False,
                                user=user_profile,
                                is_successful=False,
                                is_verified=False,
                            ).last()

                            if game_play_id is None:
                                # update user wallet
                                user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                                if user_wallet is None:
                                    user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                                # fund user wallet

                                debit_credit_record = DebitCreditRecord.create_record(
                                    phone_number=user_wallet.user.phone_number,
                                    amount=int(amount),
                                    channel="WEB",
                                    reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                                    transaction_type="CREDIT",
                                )

                                wallet_payload = {
                                    "transaction_from": "WATU_PAY",
                                }

                                UserWallet.fund_wallet(
                                    user=user_wallet.user,
                                    amount=int(amount),
                                    channel="WEB",
                                    transaction_id=debit_credit_record.reference,
                                    user_wallet_type="GAME_PLAY_WALLET",
                                    **wallet_payload,
                                )

                                return Response(
                                    {
                                        "message": "Transaction successful",
                                        "responseCode": "00",
                                    },
                                    status=status.HTTP_200_OK,
                                )

                            game_play_id.is_successful = True
                            game_play_id.is_verified = True
                            game_play_id.save()
                            game_play_id = game_play_id.game_play_id
                            # get the lottery game play instance
                            lottoticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id)
                            if not lottoticket_qs.exists():
                                lottoticket_qs = LotteryModel.objects.filter(game_play_id=game_play_id)

                                if not lottoticket_qs.exists():
                                    # update user wallet
                                    user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                                    if user_wallet is None:
                                        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                                    # fund user wallet

                                    debit_credit_record = DebitCreditRecord.create_record(
                                        phone_number=user_wallet.user.phone_number,
                                        amount=int(amount),
                                        channel="WEB",
                                        reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                                        transaction_type="CREDIT",
                                    )

                                    wallet_payload = {
                                        "transaction_from": "WATU_PAY",
                                    }

                                    UserWallet.fund_wallet(
                                        user=user_wallet.user,
                                        amount=int(amount),
                                        channel="WEB",
                                        transaction_id=debit_credit_record.reference,
                                        user_wallet_type="GAME_PLAY_WALLET",
                                        **wallet_payload,
                                    )

                                    print("no lottery ticket found")

                                    return Response(
                                        {
                                            "message": "Transaction successful",
                                            "responseCode": "00",
                                        },
                                        status=status.HTTP_200_OK,
                                    )

                            share_ussd_payment_across_lottery_pool(
                                user_profile.phone_number,
                                int(amount),
                                game_play_id,
                                transfrom="WATU_PAY",
                                transaction_unique_id=transaction_reference,
                            ),

                            # Send Notification
                            celery_send_whatsapp_payment_notification_admin.delay(
                                phone_number=lottery_player.phone,
                                batch_id=lottery_player.batch.batch_uuid,
                                amount=amount,
                                paid_via="WATU PAY",
                            )

                            # devjoe's code ends here

                        else:
                            # Transaction is successful, so pass
                            pass
                    else:
                        # Add transaction to database
                        new_collection_transaction = PaymentTransaction.objects.create(
                            lottery_player=lottery_player,
                            lottery_batch=current_batch,
                            payment_channel="WATUPAY_USSD",
                            amount=amount,
                            provider_transaction_reference=transaction_reference,
                            unique_provider_transaction_reference=transaction_reference,
                            status="SUCCESSFUL",
                            has_paid=True,
                        )

                        # share_payment_accross_pool = (
                        #     LotteryModel.share_payment_accross_pool(
                        #         amount=amount,
                        #         player=lottery_player,
                        #         instance_number=number_of_instances,
                        #     )
                        # )

                        # assign_player_to_batch = LotteryModel.assign_player_to_batch(
                        #     player=lottery_player
                        # )

                        #
                        # devjoe's code start here
                        # share_ussd_payment_across_lottery_pool
                        user_profile = UserProfile.objects.filter(
                            Q(phone_number__startswith=phone_start) & Q(phone_number__endswith=phone_end)
                        ).last()

                        # get the lottery game play id that this payment was made for
                        game_play_id = UssdLotteryPayment.objects.filter(
                            game_play_id__isnull=False,
                            user=user_profile,
                            is_successful=False,
                            is_verified=False,
                        ).last()

                        if game_play_id is None:
                            # update user wallet
                            user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="USSD").last()
                            if user_wallet is None:
                                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="USSD")

                            # fund user wallet
                            user_wallet.game_available_balance += int(amount)
                            user_wallet.transaction_from = "WATU_PAY"
                            user_wallet.save()

                            return Response(
                                {
                                    "message": "Transaction successful",
                                    "responseCode": "00",
                                },
                                status=status.HTTP_200_OK,
                            )

                        game_play_id.is_successful = True
                        game_play_id.is_verified = True
                        game_play_id.save()
                        game_play_id = game_play_id.game_play_id
                        # get the lottery game play instance
                        lottoticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id)
                        if not lottoticket_qs.exists():
                            lottoticket_qs = LotteryModel.objects.filter(game_play_id=game_play_id)

                            if not lottoticket_qs.exists():
                                # check soccer table
                                lottoticket_qs = SoccerPrediction.objects.filter(game_id=game_play_id).last()
                                if not lottoticket_qs:  # no lottery ticket found
                                    # update user wallet
                                    user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                                    if user_wallet is None:
                                        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                                    # fund user wallet
                                    debit_credit_record = DebitCreditRecord.create_record(
                                        phone_number=user_wallet.user.phone_number,
                                        amount=int(amount),
                                        channel="WEB",
                                        reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                                        transaction_type="CREDIT",
                                    )

                                    wallet_payload = {
                                        "transaction_from": "WATU_PAY",
                                    }

                                    UserWallet.fund_wallet(
                                        user=user_wallet.user,
                                        amount=int(amount),
                                        channel="WEB",
                                        transaction_id=debit_credit_record.reference,
                                        user_wallet_type="GAME_PLAY_WALLET",
                                        **wallet_payload,
                                    )

                                    print("no lottery ticket found")

                                    return Response(
                                        {
                                            "message": "Transaction successful",
                                            "responseCode": "00",
                                        },
                                        status=status.HTTP_200_OK,
                                    )

                                else:  # soccer prediction found
                                    share_ussd_payment_across_lottery_pool(
                                        user_profile.phone_number,
                                        int(amount),
                                        game_play_id,
                                        transfrom="WATU_PAY",
                                        transaction_unique_id=transaction_reference,
                                    )

                                    celery_send_whatsapp_payment_notification_admin_for_soccer_payment.delay(
                                        phone_number=user_profile.phone_number,
                                        amount=amount,
                                        paid_via="WATU PAY",
                                    )

                            else:  # Lottery model data found
                                share_ussd_payment_across_lottery_pool(
                                    user_profile.phone_number,
                                    int(amount),
                                    game_play_id,
                                    transfrom="WATU_PAY",
                                    transaction_unique_id=transaction_reference,
                                ),

                                # Send Notification
                                celery_send_whatsapp_payment_notification_admin.delay(
                                    phone_number=lottery_player.phone,
                                    batch_id=lottery_player.batch.batch_uuid,
                                    amount=amount,
                                    paid_via="WATU PAY",
                                )
                        else:  # lottoticket table game id found
                            share_ussd_payment_across_lottery_pool(
                                user_profile.phone_number,
                                int(amount),
                                game_play_id,
                                transfrom="WATU_PAY",
                                transaction_unique_id=transaction_reference,
                            ),

                            # Send Notification
                            celery_send_whatsapp_payment_notification_admin.delay(
                                phone_number=lottery_player.phone,
                                batch_id=lottery_player.batch.batch_uuid,
                                amount=amount,
                                paid_via="WATU PAY",
                            )

                        # devjoe's code ends here

                    WatuPayCallBack.objects.create(transaction=new_collection_transaction, payload=response)
                else:
                    # failed_transaction
                    pass

        return Response(
            {"message": "callback received"},
            # status=status.HTTP_200_OK
        )


# Webhook from pabbly


class PabblyRecievePaymentAPIView(APIView):
    authentication_classes = [PabblyCustomBasicAuthentication]

    """
    PABBLY WEBHOOK SAMPLE WEBHOOK PAYLOAD
            {
        "event_type":"payment_success",
        "event_source":"api",
        "data":{
            "subscription_id":"634adf650f2f0269ca3db461",
            "plan_id":"634aca1c0f2f0269ca3dae36",
            "product_id":"628e3fe09dcb4a60765def3f",
            "type_formated":"Payment",
            "status_formatted":"Success",
            "reference_id":"INV-2537",
            "transaction":"Transaction ID: 634adf650f2f0269ca3db462 &lt;br />PSB Subscription Id: 634adf650f2f0269ca3db461",
            "gateway_type":"connect",
            "gateway_id":"634ab3880f2f0269ca3da910",
            "currency_code":"NGN",
            "currency_symbol":"₦",
            "failed_data":"",
            "payment_mode":"api",
            "subscription":{
                "customer":{
                    "phone":"23470391152",
                    "billing_address":{
                    "street1":"",
                    "city":"",
                    "state":"Lagos",
                    "state_code":"LA",
                    "zip_code":"",
                    "country":"NG"
                    },
                    "shipping_address":{

                    },
                    "other_detail":{

                    },
                    "role":"",
                    "credit":{
                    "remaining":0
                    },
                    "api":"",
                    "createdAt":"2022-07-22T11:29:21.362Z",
                    "updatedAt":"2022-10-15T16:27:17.273Z",
                    "id":"62da8a11c049495f89dda447",
                    "user_id":"623c7cdfd6d55061dfcf61f8",
                    "first_name":"Joseph",
                    "last_name":"chinedu",
                    "user_name":"<EMAIL>",
                    "email_id":"<EMAIL>"
                },
                "plan":{
                    "plan_type":"flat_fee",
                    "redirect_url":"https://www.libertypay-agent.online/",
                    "specific_keep_live":false,
                    "trial_amount":0,
                    "createdAt":"2022-10-15T14:56:28.575Z",
                    "updatedAt":"2022-10-15T14:56:28.575Z",
                    "id":"634aca1c0f2f0269ca3dae36",
                    "product_id":"628e3fe09dcb4a60765def3f",
                    "user_id":"623c7cdfd6d55061dfcf61f8",
                    "plan_name":"TOTAL PAYABLE AMOUNT",
                    "plan_code":"2348031346306-ac7db",
                    "price":100,
                    "billing_period":"None",
                    "billing_period_num":"None",
                    "billing_cycle":"onetime",
                    "billing_cycle_num":"None",
                    "trial_period":0,
                    "setup_fee":0,
                    "plan_description":""
                },
                "setup_fee":0,
                "currency_symbol":"₦",
                "payment_method":"connect",
                "taxable":true,
                "tax_apply":{
                    "country":"NG",
                    "tax_id":"",
                    "exempt_tax":[

                    ],
                    "total_amount":100,
                    "total_tax":0,
                    "total_amount_before_tax":100
                },
                "gateway_type":"connect",
                "payment_terms":"net0",
                "gateway_id":"634ab3880f2f0269ca3da910",
                "gateway_name":"WhispaKonnect",
                "custom_fields":[

                ],
                "requested_ip":"**************",
                "cron_process":"done",
                "createdAt":"2022-10-15T16:27:17.282Z",
                "updatedAt":"2022-10-15T16:30:11.457Z",
                "id":"634adf650f2f0269ca3db461",
                "customer_id":"62da8a11c049495f89dda447",
                "product_id":"628e3fe09dcb4a60765def3f",
                "plan_id":"634aca1c0f2f0269ca3dae36",
                "amount":100,
                "user_id":"623c7cdfd6d55061dfcf61f8",
                "email_id":"<EMAIL>",
                "status":"live",
                "quantity":1,
                "starts_at":"2022-10-15T16:27:17.171Z",
                "activation_date":"2022-10-15T16:30:11.111Z",
                "expiry_date":"2122-10-15T16:27:17.171Z",
                "trial_days":0,
                "trial_expiry_date":"",
                "next_billing_date":"",
                "last_billing_date":"2022-10-15T16:30:11.111Z",
                "canceled_date":"None"
            },
            "user":{
                "verified":"1",
                "project":[
                    {
                    "name":"connect",
                    "createdAt":"2022-03-24T14:15:04.282Z"
                    },
                    {
                    "name":"pabbly-subscriptions",
                    "createdAt":"2022-03-25T08:34:09.112Z"
                    },
                    {
                    "name":"mailget",
                    "createdAt":"2022-03-25T08:34:11.220Z"
                    },
                    {
                    "name":"formget",
                    "createdAt":"2022-03-25T08:34:12.116Z"
                    },
                    {
                    "name":"emailverify",
                    "createdAt":"2022-03-25T08:34:13.671Z"
                    }
                ],
                "currency":"NGN",
                "lockout_count":0,
                "status":"active",
                "createdAt":"2022-03-24T14:14:55.815Z",
                "updatedAt":"2022-10-15T13:06:36.378Z",
                "id":"623c7cdfd6d55061dfcf61f8",
                "parent":"5a1cfb08e5866110121ebbf5",
                "first_name":"Liberty",
                "last_name":"Assured",
                "email":"<EMAIL>",
                "address_line1":"",
                "address_line2":"",
                "city":"",
                "state":"",
                "country":"",
                "zip_code":"",
                "phone":"012770331",
                "mobile":"",
                "facebook_url":"",
                "twitter_url":"",
                "time_zone":"",
                "date_format":"MMM DD, YYYY hh:mm A",
                "ip_address":"**************",
                "currency_symbol":"₦"
            },
            "createdAt":"2022-10-15T16:27:17.305Z",
            "updatedAt":"2022-10-15T16:27:17.305Z",
            "id":"634adf650f2f0269ca3db464",
            "user_id":"623c7cdfd6d55061dfcf61f8",
            "customer_id":"62da8a11c049495f89dda447",
            "invoice_id":"634adf650f2f0269ca3db462",
            "type":"payment",
            "status":"success",
            "amount":100,
            "description":"Payment success",
            "plan_code":"",
            "invoice":{
                "quantity":1,
                "product_id":"628e3fe09dcb4a60765def3f",
                "setup_fee":0,
                "currency_symbol":"₦",
                "credit_note":{
                    "total_tax":"0.00",
                    "status":"success",
                    "new_plan_total":100,
                    "total_credit_amount":0,
                    "charge_amount":100,
                    "credit_applied":[

                    ]
                },
                "tax_apply":{
                    "country":"NG",
                    "tax_id":"",
                    "exempt_tax":[

                    ],
                    "total_amount":100,
                    "total_tax":0,
                    "total_amount_before_tax":100
                },
                "createdAt":"2022-10-15T16:27:17.295Z",
                "updatedAt":"2022-10-15T16:27:17.299Z",
                "id":"634adf650f2f0269ca3db462",
                "customer_id":"62da8a11c049495f89dda447",
                "subscription_id":"634adf650f2f0269ca3db461",
                "status":"paid",
                "invoice_id":"INV-2537",
                "payment_term":"net0",
                "amount":100,
                "due_amount":0,
                "due_date":"2022-10-15T16:27:17.171Z",
                "subscription":{
                    "plan":{
                    "plan_type":"flat_fee",
                    "createdAt":"2022-10-15T14:56:28.575Z",
                    "updatedAt":"2022-10-15T14:56:28.575Z",
                    "id":"634aca1c0f2f0269ca3dae36",
                    "product_id":"628e3fe09dcb4a60765def3f",
                    "plan_name":"TOTAL PAYABLE AMOUNT",
                    "plan_code":"2348031346306-ac7db",
                    "price":100,
                    "billing_period":"None",
                    "billing_period_num":"None",
                    "billing_cycle":"onetime",
                    "billing_cycle_num":"None",
                    "trial_period":0,
                    "setup_fee":0,
                    "plan_description":""
                    },
                    "setup_fee":0,
                    "currency_symbol":"₦",
                    "payment_method":"connect",
                    "taxable":true,
                    "tax_apply":{
                    "country":"NG",
                    "tax_id":"",
                    "exempt_tax":[

                    ],
                    "total_amount":100,
                    "total_tax":0,
                    "total_amount_before_tax":100
                    },
                    "gateway_type":"connect",
                    "payment_terms":"net0",
                    "gateway_id":"634ab3880f2f0269ca3da910",
                    "gateway_name":"WhispaKonnect",
                    "custom_fields":[

                    ],
                    "requested_ip":"**************",
                    "createdAt":"2022-10-15T16:27:17.282Z",
                    "updatedAt":"2022-10-15T16:30:11.457Z",
                    "id":"634adf650f2f0269ca3db461",
                    "customer_id":"62da8a11c049495f89dda447",
                    "product_id":"628e3fe09dcb4a60765def3f",
                    "plan_id":"634aca1c0f2f0269ca3dae36",
                    "amount":100,
                    "email_id":"<EMAIL>",
                    "status":"live",
                    "quantity":1,
                    "starts_at":"2022-10-15T16:27:17.171Z",
                    "activation_date":"2022-10-15T16:30:11.111Z",
                    "expiry_date":"2122-10-15T16:27:17.171Z",
                    "trial_days":0,
                    "trial_expiry_date":"",
                    "next_billing_date":"",
                    "last_billing_date":"2022-10-15T16:30:11.111Z",
                    "canceled_date":"None"
                },
                "product":{
                    "createdAt":"2022-05-25T14:40:32.364Z",
                    "updatedAt":"2022-05-25T14:40:32.364Z",
                    "id":"628e3fe09dcb4a60765def3f",
                    "product_name":"liberty"
                },
                "invoice_link":"https://payments.pabbly.com/secureinvoice/623d7eec1d2bbb12cc2bee60/?cinvoice_id=ba6eae1f1078939c72aedb84426c31f9:890378540f3f688f464e981845c0f2e4a052b18c2c1441621ad73cc310c0999ba1b0f49ff4996fcb3892ce4d1e2bded21f7f703301e145b0933c1549a4fa0dcca18ee8bc3e3807ae62edbe8b510886b8"
            },
            "customer":{
                "phone":"23470391152",
                "billing_address":{
                    "street1":"",
                    "city":"",
                    "state":"Lagos",
                    "state_code":"LA",
                    "zip_code":"",
                    "country":"NG"
                },
                "shipping_address":{

                },
                "credit":{
                    "remaining":0
                },
                "createdAt":"2022-07-22T11:29:21.362Z",
                "updatedAt":"2022-10-15T16:27:17.273Z",
                "id":"62da8a11c049495f89dda447",
                "first_name":"Joseph",
                "last_name":"chinedu",
                "email_id":"<EMAIL>"
            },
            "product":{
                "createdAt":"2022-05-25T14:40:32.364Z",
                "updatedAt":"2022-05-25T14:40:32.364Z",
                "id":"628e3fe09dcb4a60765def3f",
                "product_name":"liberty"
            }
        },
        "create_time":"2022-10-15T16:30:11.1111+00:00"
        }
    """

    def post(self, request):
        webhook_response = request.data
        # print("webhook_response", webhook_response, "\n\n")
        resp = webhook_response.get("data", None)

        # print("pabbly webhook response", resp, "\n\n\n\n")

        sleep(5)

        if resp:
            print("pabbly transction found")
            # print(webhook_response)

            event_type = webhook_response.get("event_type", None)
            plan_id = resp.get("plan_id")
            email_id = resp.get("customer").get("email_id")
            # customer_id = resp.get("customer_id")
            invoice_id = resp.get("invoice_id")
            amount = resp.get("amount")

            # LOGIC

            # Check current batch and update
            current_batch = LotteryBatch.get_current_batch()

            get_lottery_player = PaymentCollectionDetail.objects.filter(pabbly_plan_id=plan_id).last()
            if not get_lottery_player:
                pass
            else:
                print("get_lottery_player found")
                transaction_lottery_player = get_lottery_player.lottery_player.phone_number

                # lottery_player = LotteryModel.objects.filter(
                #     phone=transaction_lottery_player
                # ).last()

                # update user profile email
                user_profile = UserProfile.objects.filter(phone_number=transaction_lottery_player).last()

                if user_profile.email is None or user_profile.email == "":
                    if email_id:
                        user_profile.email = email_id
                        user_profile.save()
                    else:
                        pass

                # number_of_instances = lottery_player.instance_number
                # if event_type == "payment_failure":
                if event_type == "payment_success":
                    print("event_type: payment_success")

                    # Check if transaction is in our database
                    check_for_transaction = PaymentTransaction.objects.filter(
                        Q(provider_transaction_reference=plan_id) & Q(pabbly_reference=invoice_id)
                    ).last()

                    # check_for_transaction = PaymentTransaction.objects.filter(provider_transaction_reference=plan_id).last()

                    if check_for_transaction:
                        print("check_for_transaction found")
                        print(
                            "check_for_transaction status",
                        )

                        if check_for_transaction.status != "SUCCESSFUL" and check_for_transaction.has_paid is False:
                            check_for_transaction.status = "SUCCESSFUL"
                            check_for_transaction.has_paid = True
                            check_for_transaction.amount = float(amount)
                            check_for_transaction.save()

                            check_for_payload = PabblyCallBack.objects.filter(transaction=check_for_transaction).last()

                            if check_for_payload:
                                check_for_payload.payload = resp
                                check_for_payload.save()
                            else:
                                # Create provider raw payload

                                PabblyCallBack.objects.create(transaction=check_for_transaction, payload=resp)

                            # share_payment_accross_pool = (
                            #     LotteryModel.share_payment_accross_pool(
                            #         amount=amount,
                            #         player=lottery_player,
                            #         instance_number=number_of_instances,
                            #     )
                            # )

                            # assign_player_to_batch = (
                            #     LotteryModel.assign_player_to_batch(
                            #         player=lottery_player
                            #     )
                            # )

                            #
                            # devjoe's code start here
                            # share_ussd_payment_across_lottery_pool
                            user_profile = UserProfile.objects.filter(phone_number=user_profile.phone_number).last()

                            # get the lottery game play id that this payment was made for
                            game_play_id = UssdLotteryPayment.objects.filter(
                                game_play_id__isnull=False,
                                user=user_profile,
                                is_successful=False,
                                is_verified=False,
                            ).last()

                            if game_play_id is None:
                                # update user wallet
                                user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                                if user_wallet is None:
                                    user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                                # fund user wallet
                                user_wallet.game_available_balance += int(amount)
                                user_wallet.transaction_from = "PABBLY_PAYSTACK"
                                user_wallet.save()

                                # notify admin
                                notify_admin_of_user_funding.delay(
                                    amount=int(amount),
                                    phone_number=user_profile.phone_number,
                                    channel="PABBLY",
                                )

                                return Response(
                                    {
                                        "message": "Transaction successful",
                                        "responseCode": "00",
                                    },
                                    status=status.HTTP_200_OK,
                                )

                            game_play_id.is_successful = True
                            game_play_id.is_verified = True
                            game_play_id.save()
                            game_play_id = game_play_id.game_play_id
                            # get the lottery game play instance
                            lottoticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id)
                            if not lottoticket_qs.exists():
                                lottoticket_qs = LotteryModel.objects.filter(game_play_id=game_play_id)

                                if not lottoticket_qs.exists():
                                    lottoticket_qs = SoccerPrediction.objects.filter(game_id=game_play_id)

                                    if not lottoticket_qs.exists():
                                        # update user wallet
                                        user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                                        if user_wallet is None:
                                            user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                                        # fund user wallet
                                        user_wallet.game_available_balance += int(amount)
                                        user_wallet.transaction_from = "PABBLY_PAYSTACK"
                                        user_wallet.save()

                                        # notify admin
                                        notify_admin_of_user_funding.delay(
                                            amount=int(amount),
                                            phone_number=user_profile.phone_number,
                                            channel="PABBLY",
                                        )

                                        print("no lottery ticket found")

                                        return Response(
                                            {
                                                "message": "Transaction successful",
                                                "responseCode": "00",
                                            },
                                            status=status.HTTP_200_OK,
                                        )

                            share_ussd_payment_across_lottery_pool(
                                user_profile.phone_number,
                                int(amount),
                                game_play_id,
                                transfrom="PABBLY_PAYSTACK",
                                transaction_unique_id=invoice_id,
                            ),

                            # Send Notification

                            lotto_qs = LottoTicket.objects.filter(game_play_id=game_play_id)

                            if lotto_qs.exists():
                                lotto_qs = lotto_qs.last()

                                celery_send_whatsapp_payment_notification_admin.delay(
                                    phone_number=user_profile.phone_number,
                                    batch_id=lotto_qs.batch.batch_uuid,
                                    amount=amount,
                                    paid_via="PABBLY",
                                )

                            else:
                                lotto_qs = LotteryModel.objects.filter(game_play_id=game_play_id).last()

                                if lotto_qs:
                                    celery_send_whatsapp_payment_notification_admin.delay(
                                        phone_number=user_profile.phone_number,
                                        batch_id=lotto_qs.batch.batch_uuid,
                                        amount=amount,
                                        paid_via="PABBLY",
                                    )

                                else:
                                    # check soccer table
                                    lottoticket_instance = SoccerPrediction.objects.filter(game_id=game_play_id).last()

                                    if lottoticket_instance:
                                        celery_send_whatsapp_payment_notification_admin_for_soccer_payment.delay(
                                            phone_number=user_profile.phone_number,
                                            amount=amount,
                                            paid_via="PABBLY",
                                        )

                            # devjoe's code ends here

                        else:
                            # Transaction is successful, so pass
                            pass
                    else:
                        print("check_for_transaction not found")

                        # Add transaction to database
                        new_collection_transaction = PaymentTransaction.objects.create(
                            lottery_player=user_profile,
                            lottery_batch=current_batch,
                            payment_channel="PABBLY_PAYSTACK",
                            amount=amount,
                            provider_transaction_reference=plan_id,
                            unique_provider_transaction_reference=plan_id,
                            pabbly_reference=invoice_id,
                            status="SUCCESSFUL",
                            has_paid=True,
                        )

                        # share_payment_accross_pool = (
                        #     LotteryModel.share_payment_accross_pool(
                        #         amount=amount,
                        #         player=lottery_player,
                        #         instance_number=number_of_instances,
                        #     )
                        # )

                        # assign_player_to_batch = LotteryModel.assign_player_to_batch(
                        #     player=lottery_player
                        # )

                        #
                        # devjoe's code start here
                        # share_ussd_payment_across_lottery_pool
                        user_profile = UserProfile.objects.filter(phone_number=user_profile.phone_number).last()

                        # get the lottery game play id that this payment was made for
                        game_play_id = UssdLotteryPayment.objects.filter(
                            game_play_id__isnull=False,
                            user=user_profile,
                            is_successful=False,
                            is_verified=False,
                        ).last()

                        if game_play_id is None:
                            # update user wallet
                            user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="USSD").last()
                            if user_wallet is None:
                                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="USSD")

                            # fund user wallet
                            user_wallet.game_available_balance += int(amount)
                            user_wallet.transaction_from = "PABBLY_PAYSTACK"
                            user_wallet.save()

                            # notify admin
                            notify_admin_of_user_funding.delay(
                                amount=int(amount),
                                phone_number=user_profile.phone_number,
                                channel="PABBLY",
                            )

                            return Response(
                                {
                                    "message": "Transaction successful",
                                    "responseCode": "00",
                                },
                                status=status.HTTP_200_OK,
                            )

                        game_play_id.is_successful = True
                        game_play_id.is_verified = True
                        game_play_id.save()
                        game_play_id = game_play_id.game_play_id
                        # get the lottery game play instance
                        lottoticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id)
                        if not lottoticket_qs.exists():
                            lottoticket_qs = LotteryModel.objects.filter(game_play_id=game_play_id)

                            if not lottoticket_qs.exists():
                                # update user wallet
                                user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="USSD").last()
                                if user_wallet is None:
                                    user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="USSD")

                                # fund user wallet
                                user_wallet.game_available_balance += int(amount)
                                user_wallet.transaction_from = "PABBLY_PAYSTACK"
                                user_wallet.save()

                                # notify admin
                                notify_admin_of_user_funding.delay(
                                    amount=int(amount),
                                    phone_number=user_profile.phone_number,
                                    channel="PABBLY",
                                )

                                print("no lottery ticket found")

                                return Response(
                                    {
                                        "message": "Transaction successful",
                                        "responseCode": "00",
                                    },
                                    status=status.HTTP_200_OK,
                                )

                        share_ussd_payment_across_lottery_pool(
                            user_profile.phone_number,
                            int(amount),
                            game_play_id,
                            transfrom="PABBLY_PAYSTACK",
                            transaction_unique_id=invoice_id,
                        ),

                        # devjoe's code ends here

                        PabblyCallBack.objects.create(
                            transaction=new_collection_transaction,
                            payload=webhook_response,
                        )

                        # Send Notification
                        lotto_qs = LottoTicket.objects.filter(game_play_id=game_play_id)

                        if lotto_qs.exists():
                            lotto_qs = lotto_qs.last()

                            celery_send_whatsapp_payment_notification_admin.delay(
                                phone_number=user_profile.phone_number,
                                batch_id=lotto_qs.batch.batch_uuid,
                                amount=amount,
                                paid_via="PABBLY",
                            )

                        else:
                            lotto_qs = LotteryModel.objects.filter(game_play_id=game_play_id).last()

                            if lotto_qs:
                                celery_send_whatsapp_payment_notification_admin.delay(
                                    phone_number=user_profile.phone_number,
                                    batch_id=lotto_qs.batch.batch_uuid,
                                    amount=amount,
                                    paid_via="PABBLY",
                                )

                            else:
                                celery_send_whatsapp_payment_notification_admin.delay(
                                    phone_number=user_profile.phone_number,
                                    batch_id=current_batch.batch_uuid,
                                    amount=amount,
                                    paid_via="PABBLY",
                                )
                else:
                    # failed_transaction
                    pass

        return Response(
            {"message": "callback received"},
            # status=status.HTTP_200_OK
        )


# WOVEN CallBack


class WovenRecievePaymentAPIView(APIView):
    def post(self, request):
        from pos_app.models import AgentWallet

        """
        SAMPLE WEBHOOK RESPONSE
        {
            "fee":50,
            "hash":"2c1e19630b88545c45c2e51e6f6ab457a2a9687e6d54fb84d441267a1fd02fdb2c8f9b02e7a0697cb4a30fe01dfa19e04ad4a8007846c105e3554f1f1152124d",
            "meta":"{}",
            "event":"payment.success",
            "nuban":"**********",
            "amount":300,
            "status":"ACTIVE",
            "is_test":0,
            "currency":"NGN",
            "vnubanId":4357873,
            "bank_code":"000017",
            "narration":null,
            "requestId":"822ba7eb-2dc5-4cdf-a418-951c51995dbc",
            "merchantId":441,
            "account_name":"************* Whispawyse",
            "failure_type":null,
            "source_nuban":"**********",
            "sweep_status":null,
            "user_message":null,
            "account_email":"<EMAIL>",
            "bank_response":null,
            "amount_payable":250,
            "bank_reference":null,
            "settled_status":"UNSETTLED",
            "source_bank_code":"090110",
            "transaction_time":"22:13:8",
            "transaction_type":"TRANSFER",
            "unique_reference":"090110221014231217398691723575",
            "account_reference":"*************-e8389f",
            "service_bank_code":null,
            "is_posted_to_mifos":null,
            "payout_user_message":null,
            "source_account_name":"Liberty",
            "account_mobile_number":"*************"
        }

        """

        resp = request.data

        # send woven callback to whispersms
        try:
            send_woven_callback(resp)
        except Exception:
            pass

        print("woven callback received", resp, "\n\n\n\n\n")

        if resp.get("transaction_type") is None or resp.get("event") != "payment.success":
            pass
        else:
            account_ref = resp.get("account_reference")
            # unique_ref = resp.get("unique_reference")
            # amount = resp.get("amount")

            print("account_ref", account_ref, "\n\n\n")

            # LOGIC

            sleep(2)
            # Check current batch and update
            # current_batch = LotteryBatch.get_current_batch()

            # update float wallet
            FloatWallet().update_float_wallet()

            # check if transaction is for mobile agent funding
            agent_wallet = AgentWallet.objects.filter(account_ref=account_ref).last()

            if agent_wallet:
                if agent_wallet.woven_account:
                    AgentWallet.wallet_funding_via_woven_virtual_account(resp)
                    return Response({"message": "callback received pos"}, status=status.HTTP_200_OK)

            get_wallet = UserWallet.objects.filter(account_ref=account_ref).last()
            if get_wallet is None:
                woven_account_detail = WovenAccountDetail.objects.filter(account_ref=account_ref).last()
                if woven_account_detail:
                    user_profile = UserProfile.objects.filter(phone_number=woven_account_detail.phone_number).last()

                    get_wallet = UserWallet.objects.create(
                        user=user_profile,
                        woven_account=woven_account_detail,
                        account_ref=account_ref,
                        wallet_tag=woven_account_detail.wallet_tag,
                    )

            print("get_wallet.wallet_tag", get_wallet.wallet_tag, "\n\n\n")

            # if get_wallet and get_wallet.wallet_tag == "USSD":

            if get_wallet and get_wallet.wallet_tag == "USSD":
                lottery_payment_via_woven(resp)
            elif get_wallet and get_wallet.wallet_tag == "WEB":
                handle_woven_wallet_funding(resp)

        return Response({"message": "callback received"}, status=status.HTTP_200_OK)


class NonWinnerListAPIView(APIView):
    serializer_class = NonWinnersListSerializer

    def get(self, request):
        # Send back last batch winners
        # winners_qs = LotteryWinnersTable.objects.filter(batch__is_active=True)

        batch = LotteryBatch.objects.all().order_by("-id")[1]
        winners_qs = LotteryWinnersTable.objects.filter(batch=batch)

        serializer = self.serializer_class(winners_qs, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)


class VerifyAccountNumber(APIView):
    permission_classes = []
    permission_classes = []

    def post(self, request):
        serializer = VerifyAccountNumberSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        account_number = serializer.validated_data.get("account_number")
        bank_code = serializer.validated_data.get("bank_code")

        bank_details = fetch_account_name(account_number, bank_code)

        if bank_details.get("status") is True:
            bank_details.get("data")["bank_code"] = bank_code
            return Response(bank_details.get("data"), status=status.HTTP_200_OK)

        return Response(bank_details, status=status.HTTP_400_BAD_REQUEST)


class WinWiseLogUserAPIView(APIView):
    def get(self, request):
        try:
            start_date = request.query_params["start_date"]
            end_date = request.query_params["end_date"]
        except MultiValueDictKeyError:
            return Response(
                {"param": "you must pass in the start_date and end_date"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            base_api = User.objects.filter(created_at__date__range=(start_date, end_date))
        except ValidationError:
            return Response(
                {"param": "value has an invalid date format. It must be in YYYY-MM-DD format."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        new_sign_up_web = base_api.filter(channel="WEB").count()
        new_sign_up_pos = base_api.filter(channel="APP/POS").count()

        completed_signups_web = base_api.filter(channel="WEB", is_active=True).count()
        completed_signups_pos = base_api.filter(channel="APP/POS", is_active=True).count()

        incomplete_signups_web = base_api.filter(channel="WEB", is_active=False).count()
        incomplete_signups_pos = base_api.filter(channel="APP/POS", is_active=False).count()

        data = {
            "data": "SIGNUPS",
            "new_sign_up_web": new_sign_up_web,
            "new_sign_up_pos": new_sign_up_pos,
            "completed_signups_web": completed_signups_web,
            "completed_signups_pos": completed_signups_pos,
            "incomplete_signups_web": incomplete_signups_web,
            "incomplete_signups_pos": incomplete_signups_pos,
        }
        return Response(data, status=status.HTTP_200_OK)


class WinWiseGamePlayedWebAPIView(APIView):
    def get(self, request):
        try:
            start_date = request.query_params["start_date"]
            end_date = request.query_params["end_date"]
        except MultiValueDictKeyError:
            return Response(
                {"param": "you must pass in the start_date and end_date"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            base_api_wyse = LotteryModel.objects.filter(date__date__range=(start_date, end_date), channel="WEB")
            base_api_salary_instant = LottoTicket.objects.filter(date__date__range=(start_date, end_date), channel="WEB")
        except ValidationError:
            return Response(
                {"param": "value has an invalid date format. It must be in YYYY-MM-DD format."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        wyse_cash = base_api_wyse.filter(lottery_type="WYSE_CASH").count()
        salary_for_life = base_api_salary_instant.filter(lottery_type="SALARY_FOR_LIFE").count()
        instant_cashout = base_api_salary_instant.filter(lottery_type="INSTANT_CASHOUT").count()
        data = {
            "game_channel": "WEB",
            "wyse_cash": wyse_cash,
            "salary_for_life": salary_for_life,
            "instant_cashout": instant_cashout,
        }
        return Response(data, status=status.HTTP_200_OK)


class WinWiseGamePlayedPOSAPIView(APIView):
    def get(self, request):
        try:
            start_date = request.query_params["start_date"]
            end_date = request.query_params["end_date"]
        except MultiValueDictKeyError:
            return Response(
                {"param": "you must pass in the start_date and end_date"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            base_api = LotteryModel.objects.filter(date__date__range=(start_date, end_date), channel="POS_AGENT")
            base_api_salary_instant = LottoTicket.objects.filter(date__date__range=(start_date, end_date), channel="POS_AGENT")
        except ValidationError:
            return Response(
                {"param": "value has an invalid date format. It must be in YYYY-MM-DD format."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        wyse_cash = base_api.filter(lottery_type="WYSE_CASH").count()
        salary_for_life = base_api_salary_instant.filter(lottery_type="SALARY_FOR_LIFE").count()
        instant_cashout = base_api_salary_instant.filter(lottery_type="INSTANT_CASHOUT").count()
        data = {
            "game_channel": "POS",
            "wyse_cash": wyse_cash,
            "salary_for_life": salary_for_life,
            "instant_cashout": instant_cashout,
        }
        return Response(data, status=status.HTTP_200_OK)


class WinWiseGamePlayedUSSDAPIView(APIView):
    def get(self, request):
        try:
            start_date = request.query_params["start_date"]
            end_date = request.query_params["end_date"]
        except MultiValueDictKeyError:
            return Response(
                {"param": "you must pass in the start_date and end_date"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            base_api = LotteryModel.objects.filter(date__date__range=(start_date, end_date), channel="USSD")
            base_api_salary_instant = LottoTicket.objects.filter(date__date__range=(start_date, end_date), channel="USSD")
        except ValidationError:
            return Response(
                {"param": "value has an invalid date format. It must be in YYYY-MM-DD format."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        wyse_cash = base_api.filter(lottery_type="WYSE_CASH").count()
        salary_for_life = base_api_salary_instant.filter(lottery_type="SALARY_FOR_LIFE").count()
        instant_cashout = base_api_salary_instant.filter(lottery_type="INSTANT_CASHOUT").count()
        data = {
            "game_channel": "USSD",
            "wyse_cash": wyse_cash,
            "salary_for_life": salary_for_life,
            "instant_cashout": instant_cashout,
        }
        return Response(data, status=status.HTTP_200_OK)


class WinWiseGamePayOutAPIView(APIView):
    def get(self, request):
        try:
            start_date = request.query_params["start_date"]
            end_date = request.query_params["end_date"]
        except MultiValueDictKeyError:
            return Response(
                {"param": "you must pass in the start_date and end_date"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            base_api = PayoutTransactionTable.objects.filter(date_added__date__range=(start_date, end_date))
        except ValidationError:
            return Response(
                {"param": "value has an invalid date format. It must be in YYYY-MM-DD format."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        successful_payout = base_api.filter(payment_initiated=True, is_verified=True, disbursed=True).count()
        unsuccessful_payout = base_api.filter(payment_initiated=True, is_verified=True, disbursed=False).count()
        data = {
            "data": "PAYOUTS",
            "successful_payout": successful_payout,
            "unsuccessful_payout": unsuccessful_payout,
        }
        return Response(data, status=status.HTTP_200_OK)


class WinWiseGameWonUSSDAPIView(APIView):
    def get(self, request):
        try:
            start_date = request.query_params["start_date"]
            end_date = request.query_params["end_date"]
        except MultiValueDictKeyError:
            return Response(
                {"param": "you must pass in the start_date and end_date"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            base_api = LotteryWinnersTable.objects.filter(date_won__date__range=(start_date, end_date), lottery_source_tag="USSD")
            base_api_salary_instant = LottoWinners.objects.filter(date_won__date__range=(start_date, end_date), channel_played_from="USSD")
        except ValidationError:
            return Response(
                {"param": "value has an invalid date format. It must be in YYYY-MM-DD format."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        wyse_cash = base_api.count()
        salary_for_life = base_api_salary_instant.filter(lotto_type="SALARY_FOR_LIFE").count()
        instant_cashout = base_api_salary_instant.filter(lotto_type="INSTANT_CASHOUT").count()
        data = {
            "game_channel": "USSD",
            "wyse_cash": wyse_cash,
            "salary_for_life": salary_for_life,
            "instant_cashout": instant_cashout,
        }
        return Response(data, status=status.HTTP_200_OK)


class WinWiseGameWonWEBAPIView(APIView):
    def get(self, request):
        try:
            start_date = request.query_params["start_date"]
            end_date = request.query_params["end_date"]
        except MultiValueDictKeyError:
            return Response(
                {"param": "you must pass in the start_date and end_date"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            base_api = LotteryWinnersTable.objects.filter(date_won__date__range=(start_date, end_date), lottery_source_tag="WEB")
            base_api_salary_instant = LottoWinners.objects.filter(date_won__date__range=(start_date, end_date), channel_played_from="WEB")
        except ValidationError:
            return Response(
                {"param": "value has an invalid date format. It must be in YYYY-MM-DD format."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        wyse_cash = base_api.count()
        salary_for_life = base_api_salary_instant.filter(lotto_type="SALARY_FOR_LIFE").count()
        instant_cashout = base_api_salary_instant.filter(lotto_type="INSTANT_CASHOUT").count()
        data = {
            "game_channel": "WEB",
            "wyse_cash": wyse_cash,
            "salary_for_life": salary_for_life,
            "instant_cashout": instant_cashout,
        }
        return Response(data, status=status.HTTP_200_OK)


class WinWiseGameWonPOSAPIView(APIView):
    def get(self, request):
        try:
            start_date = request.query_params["start_date"]
            end_date = request.query_params["end_date"]
        except MultiValueDictKeyError:
            return Response(
                {"param": "you must pass in the start_date and end_date"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            base_api = LotteryWinnersTable.objects.filter(
                date_won__date__range=(start_date, end_date),
                lottery_source_tag="POS_AGENT",
            )
            base_api_salary_instant = LottoWinners.objects.filter(
                date_won__date__range=(start_date, end_date),
                channel_played_from="POS_AGENT",
            )
        except ValidationError:
            return Response(
                {"param": "value has an invalid date format. It must be in YYYY-MM-DD format."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        wyse_cash = base_api.count()
        salary_for_life = base_api_salary_instant.filter(lotto_type="SALARY_FOR_LIFE").count()
        instant_cashout = base_api_salary_instant.filter(lotto_type="INSTANT_CASHOUT").count()
        data = {
            "game_channel": "POS",
            "wyse_cash": wyse_cash,
            "salary_for_life": salary_for_life,
            "instant_cashout": instant_cashout,
        }
        return Response(data, status=status.HTTP_200_OK)


class VFDFloatBalance(APIView):
    def get(self, request):
        data = []
        vfd_enquiries = liberty_pay_vfd_account_enquiry()

        vfd_float_wallet_instance = FloatWallet().get_float_wallet("AGENT_FUNDING_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)
            vfd_float_wallet_instance.amount = vfd_enquiries
            vfd_float_wallet_instance.save()

        data.append({"retail_funding_wallet": vfd_enquiries})

        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="NON_RETAIL_WALLET")

        vfd_float_wallet_instance = FloatWallet().get_float_wallet("NON_RETAIL_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)
            vfd_float_wallet_instance.amount = vfd_enquiries
            vfd_float_wallet_instance.save()

        data.append({"non_retail_wallet": vfd_enquiries})

        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_RTP_WALLET")

        vfd_float_wallet_instance = FloatWallet().get_float_wallet("RETAIL_RTP_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)
            vfd_float_wallet_instance.amount = vfd_enquiries
            vfd_float_wallet_instance.save()

        data.append({"retail_rtp_wallet": vfd_enquiries})

        return Response(data=data, status=status.HTTP_200_OK)


class AllVFDFloatBalance(APIView):

    permission_classes = [SuperUser2Permission]

    serializer_class = AllVFDFloatBalanceSerializer


    def get(self, request):
        
        data = []

       

        vfd_enquiries = liberty_pay_vfd_account_enquiry()

        vfd_float_wallet_instance = FloatWallet().get_float_wallet("AGENT_FUNDING_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)
            vfd_float_wallet_instance.amount = vfd_enquiries
            vfd_float_wallet_instance.save()

        data.append({"retail_funding_wallet": vfd_enquiries})

        funding_wallet_activities = DailyFundingWalletActivity.get_instance()

        data.append({"funding_wallet_activities_summary": 
                     {
                        "total_funding": funding_wallet_activities.total_funding,
                        "total_due_remittance": funding_wallet_activities.total_due_remittance,
                        "total_expected_remittance_today": funding_wallet_activities.total_pending_remittance,
                        "total_rtp_deduction": funding_wallet_activities.total_rtp_deduction,
                        "total_rto_deduction": funding_wallet_activities.total_rto_deduction,
                        "total_commission_deduction": funding_wallet_activities.total_commission_deduction,
                        "uncliamed_winning_count": funding_wallet_activities.uncliamed_winning_count,
                        "uncliamed_winning_value": funding_wallet_activities.uncliamed_winning_value
                     }
                     })

        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="NON_RETAIL_WALLET")

        vfd_float_wallet_instance = FloatWallet().get_float_wallet("NON_RETAIL_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)
            vfd_float_wallet_instance.amount = vfd_enquiries
            vfd_float_wallet_instance.save()

        data.append({"non_retail_wallet": vfd_enquiries})

        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_RTP_WALLET")

        vfd_float_wallet_instance = FloatWallet().get_float_wallet("RETAIL_RTP_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)
            vfd_float_wallet_instance.amount = vfd_enquiries
            vfd_float_wallet_instance.save()

        data.append({"retail_rtp_wallet": vfd_enquiries})

        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="GHANA_RTP_WALLET")

        vfd_float_wallet_instance = FloatWallet().get_float_wallet("GHANA_RTP_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)
            vfd_float_wallet_instance.amount = vfd_enquiries
            vfd_float_wallet_instance.save()

        data.append({"ghana_rtp_wallet": vfd_enquiries})

        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RTO_WALLET")

        vfd_float_wallet_instance = FloatWallet().get_float_wallet("RTO_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)
            vfd_float_wallet_instance.amount = vfd_enquiries
            vfd_float_wallet_instance.save()

        data.append({"rto_wallet": vfd_enquiries})


        commission_wallet_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_COMMISSION_WALLET").get("available_balance", 0)
        data.append({"retail_commission_wallet": commission_wallet_enquiries})


        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="PENDING_LATE_WITHDRAWAL_WALLET")

        vfd_float_wallet_instance = FloatWallet().get_float_wallet("PENDING_LATE_WITHDRAWAL_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)
            vfd_float_wallet_instance.amount = vfd_enquiries
            vfd_float_wallet_instance.save()

        data.append({"pending_late_withdrawal_wallet": vfd_enquiries})


        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="EXCESS_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)

        data.append({"excess_wallet": vfd_enquiries})



        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="PRE_FUNDING_WALLET")

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)

        data.append({"pre_funding_wallet": vfd_enquiries})

        return Response(data=data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        source = serializer.validated_data.get("source")

        # print("source", source, "\n\n")

        vfd_enquiries = liberty_pay_vfd_account_enquiry(source = source)
        main_vfd_enquiries = vfd_enquiries

        

        vfd_float_wallet_instance = FloatWallet().get_float_wallet(source)

        if isinstance(vfd_enquiries, dict):
            vfd_enquiries = vfd_enquiries.get("available_balance", 0)
            vfd_float_wallet_instance.amount = vfd_enquiries
            vfd_float_wallet_instance.save()

        return Response(data = {"data": main_vfd_enquiries}, status=status.HTTP_200_OK)
    
    

        


class GenerateFailedSystemPickNumberForALotteryBatch(APIView):
    serializer_class = LotteryBatchUUIDSerializer
    """
    GENERATE SYSTEM PICK NUMBER FOR SALARY FOR LIFE /  BANKER LOTTERY
    """

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        lottery_batch_uuid = serializer.validated_data.get("batch_uuid")

        lottery_batch = LotteryBatch.objects.filter(batch_uuid=lottery_batch_uuid).last()
        if lottery_batch is None:
            return Response(
                {"message": "lottery batch does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # print(
        #     "lottery_batch.lottery_winner_ticket_number",
        #     lottery_batch.lottery_winner_ticket_number,
        # )

        # print(
        #     f"""
        # lottery_batch.lottery_winner_ticket_number: {lottery_batch.lottery_winner_ticket_number is not None}
        # """
        # )

        if lottery_batch.lottery_winner_ticket_number is None or lottery_batch.lottery_winner_ticket_number == "":
            pass
        else:
            return Response(
                {"message": "lottery batch already has a winner"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if lottery_batch.lottery_type not in ["SALARY_FOR_LIFE", "BANKER"]:
            return Response(
                {"message": "lottery batch is not a salary for life or banker lottery"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if lottery_batch.is_active is True:
            return Response(
                {"message": "lottery batch is still active"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        lottery_tickets = LottoTicket.objects.filter(batch__batch_uuid=lottery_batch, paid=True)
        if lottery_tickets.exists():
            user_ticket_numbers = []
            for ticket in lottery_tickets:
                user_ticket_pick = str(ticket.ticket).split(",")
                _pick = [int(i) for i in user_ticket_pick]
                user_ticket_numbers.append(_pick)

            # user_num_picks = list(user_ticket_numbers)

            random_system_pick_num = LottoTicket.generate_random_system_pick_number_for_sal_4_life_and_banker(
                numbers=user_ticket_numbers, matches_required=1
            )

            lottery_batch.lottery_winner_ticket_number = f",{random_system_pick_num}"
            lottery_batch.save()

            draw_date = lottery_batch.draw_date
            if draw_date is None:
                draw_date = lottery_batch.last_updated
                if draw_date is None:
                    draw_date = datetime.now()

            get_current_time = ((datetime.now() + timedelta(minutes=0)).time()).strftime("%H:%M")
            current_time = datetime.strptime(str(get_current_time), "%H:%M")
            current_time = current_time.strftime("%I:%M %p")

            get_current_time = ((datetime.now() + timedelta(minutes=0)).time()).strftime("%H:%M")
            current_time = datetime.strptime(str(get_current_time), "%H:%M")
            current_time = current_time.strftime("%I:%M %p")

            try:
                notify_agents_on_lottery_batch_draw(
                    batch_id=lottery_batch.id,
                    lottery_type=lottery_batch.lottery_type,
                    draw_date=f"{current_time}",
                )
            except Exception:
                # print("error sending notification to agents on lottery batch draw", e)
                pass

            return Response(
                {"message": "lottery batch system pick number generated"},
                status=status.HTTP_200_OK,
            )

        else:
            # generate random numbers between 1 and 50
            random_system_pick_num = []
            while len(random_system_pick_num) < 5:
                random_system_pick_num.append(random.randint(1, 50))

            lottery_ticket_numbers = ",".join([str(num) for num in random_system_pick_num])

            lottery_batch.lottery_winner_ticket_number = f",{lottery_ticket_numbers}"
            lottery_batch.save()

            draw_date = lottery_batch.draw_date
            if draw_date is None:
                draw_date = lottery_batch.last_updated
                if draw_date is None:
                    draw_date = datetime.now()

            get_current_time = ((datetime.now() + timedelta(minutes=0)).time()).strftime("%H:%M")
            current_time = datetime.strptime(str(get_current_time), "%H:%M")
            current_time = current_time.strftime("%I:%M %p")

            get_current_time = ((datetime.now() + timedelta(minutes=0)).time()).strftime("%H:%M")
            current_time = datetime.strptime(str(get_current_time), "%H:%M")
            current_time = current_time.strftime("%I:%M %p")

            try:
                notify_agents_on_lottery_batch_draw(
                    batch_id=lottery_batch.id,
                    lottery_type=lottery_batch.lottery_type,
                    draw_date=f"{current_time}",
                )
            except Exception:
                # print("error sending notification to agents on lottery batch draw", e)
                pass

            return Response(
                {"message": "lottery batch system pick number generated"},
                status=status.HTTP_200_OK,
            )


class PickyAssistMediaMediaUniqueUrl(APIView):
    """
    This endpoint takes a file and return a unique url for the file
    """

    serializer_class = FileUploadSerializer

    def get(self, request, file_name):
        # file_path = settings.MEDIA_ROOT / 'pickyfolder/myfile.xlsx'
        # # return FileResponse(open(f"{file_path}/file_name.xls", 'rb'), as_attachment=True, content_type='	application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

        BASE_DIR_SET = Path(__file__).resolve().parent.parent
        file_path = os.path.join(BASE_DIR_SET, "pickyfolder")
        if not os.path.join(file_path, file_name):
            raise Http404()

        new_file_path = os.path.join(file_path, file_name)
        print(new_file_path)
        return FileResponse(
            open(new_file_path, "rb"),
            as_attachment=True,
            content_type="text/csv",
        )


class WebFundDebitPlayWinningWallet(APIView):
    permission_classes = [SuperUser2Permission]

    serializer_class = WebFundDebitPlayWinningWalletSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone = serializer.validated_data["phone"]
        amount = serializer.validated_data["amount"]
        transaction_from = serializer.validated_data["transaction_from"]
        transaction_id = serializer.validated_data["transaction_id"]
        wallet_type = serializer.validated_data["wallet_type"]
        transaction_type = serializer.validated_data["transaction_type"]

        formated_phone = LotteryModel.format_number_from_back_add_234(phone)

        try:
            user_profile = UserProfile.objects.get(phone_number=formated_phone)
        except UserProfile.DoesNotExist:
            user_profile = UserProfile.objects.filter(phone_number=formated_phone).first()

        if not user_profile:
            return Response(
                {"message": "user profile does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=user_profile.phone_number,
                amount=amount,
                channel="WEB",
                reference=transaction_id,
                transaction_type=transaction_type,
            )
        except Exception as e:
            return Response(
                {"message": f"error creating debit credit record {e}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        wallet_payload = {
            "transaction_from": transaction_from,
        }

        if transaction_type == "CREDIT":
            res = UserWallet.fund_wallet(
                user=user_profile,
                amount=amount,
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type=wallet_type,
                **wallet_payload,
            )

            print(
                f"""
            res: {res}
            \n\n\n
            """
            )

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").first()

            try:
                log_save_operation(request.user, user_wallet)
            except Exception:
                pass

            return Response({"data": res}, status=status.HTTP_200_OK)

        elif transaction_type == "DEBIT":
            res = UserWallet.deduct_wallet(
                user=user_profile,
                amount=amount,
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type=wallet_type,
                **wallet_payload,
            )

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").first()

            try:
                log_save_operation(request.user, user_wallet)
            except Exception:
                pass

            return Response(data={"data": res}, status=status.HTTP_200_OK)

        else:
            return Response(
                {"message": "transaction type not supported"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class CoreBankingFundWalletCallBack(APIView):
    # permission_classes = [IpWhiteListPermission]

    def post(self, request):
        WovenCallBack.objects.create(payload=request.data)

        serializer = CoreBankingFundWalletCallBackSerializer(data=request.data)

        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        account_number = validated_data.get("recipient_account_number")
        amount = validated_data.get("amount")
        reference = validated_data.get("reference")

        redis_helper = RedisStorage(redis_key=account_number)
        redis_helper.set_data(account_number)

        # Check if this request is for instant account used for playing games
        try:
            WemaInstantPaymentAccounts.objects.get(account_number=account_number)

            reference = request.data.get("request_reference")

            WemaInstantPaymentAccounts.receive_payment(account_number=account_number, amount=amount, reference=reference, raw_payload=validated_data)
            data = {"status": "success", "status_code": 200, "message": "Ok"}
            return Response(data=data, status=status.HTTP_200_OK)
        except WemaInstantPaymentAccounts.DoesNotExist:
            pass

        get_wallet = UserWallet.objects.filter(wema_account__vnuban=account_number).last()
        if get_wallet is not None:
            if get_wallet.wallet_tag == "USSD":
                res = {
                    "account_reference": account_number,
                    "amount": float(amount) - 50,
                    "unique_reference": reference,
                    "status": "ACTIVE",
                }
                lottery_payment_via_woven(res, from_wema=True, payment_channel="WEMA_ACCOUNT", transaction_from="WEMA_FUNDING")
                data = {"status": "success", "status_code": 200, "message": "Ok"}
                return Response(data=data, status=status.HTTP_200_OK)

        fund_wallet = UserWallet.fund_wallet_with_wema_call_back(account_number=account_number, amount=amount, reference=reference)

        if fund_wallet is None:
            data = {
                "status": "failed",
                "status_code": 403,
                "message": "Invalid account number",
            }
            return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        elif fund_wallet is False:
            data = {
                "status": "failed",
                "status_code": 400,
                "message": "Transaction Already exists",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        elif fund_wallet is True:
            data = {"status": "success", "status_code": 200, "message": "Ok"}
            return Response(data=data, status=status.HTTP_200_OK)


class MtnSMSCampaignAPIView(APIView):
    serializer_class = MtnSMSCampaignSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        file = serializer.validated_data["file"]
        phone_number = serializer.validated_data["initiator_phone_number"]
        sender_id = serializer.validated_data["sender_id"]
        message_body = serializer.validated_data["message_body"]

        file_type = None

        if file.name.endswith(".csv"):
            file_type = "csv"
        elif file.name.endswith(".xlsx"):
            file_type = "xlsx"

        if (file.name.endswith(".csv") is False) and (file.name.endswith(".xlsx") is False):
            return Response(
                data={"message": "file format not supported"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        decoded_file = file.read().decode()

        celery_mtn_batch_campaign2.apply_async(queue="mtn_campaign", args=(decoded_file, sender_id, phone_number, file_type, message_body))

        # celery_mtn_batch_campaign(decoded_file, sender_id, phone_number, file_type, message_body)

        return Response(
            data={"message": "file uploaded successfully, processing started. you'll get first sms and another one when the campaign is done"},
            status=status.HTTP_200_OK,
        )


class SendMtnSMSAPIView(APIView):
    serializer_class = SendMtnSmsSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data.get("phone_number")
        message = serializer.validated_data.get("message")

        phone_number = f"234{phone_number[-10:]}"

        sms_payload = {"phone_number": phone_number, "sender_name": "20144", "message": message}

        broad_base_helper = BBCTelcoAggregator()

        sms_payload["use_json_format"] = True

        res = broad_base_helper.bbc_send_sms_2(**sms_payload)

        return Response(data=res, status=status.HTTP_200_OK)


class ConfirmWemaTransferPayment(APIView):
    permission_classes = []

    def post(self, request):
        account_number = request.data.get("account_number")
        if account_number is None:
            return Response(
                data={
                    "message_code": "003",
                    "message": "account number is required",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        redis_data = RedisStorage(redis_key=account_number).get_data()
        if redis_data is None:
            return Response(
                data={
                    "message_code": "002",
                    "message": "pending",
                },
                status=status.HTTP_200_OK,
            )

        else:
            return Response(
                data={
                    "message_code": "001",
                    "message": "successful",
                },
                status=status.HTTP_200_OK,
            )


class SalaryForLifeSponsorView(APIView):
    def post(self, request):
        serializer = S4LSponsorSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        brand_name = serializer_data.get("brand_name")
        designation = serializer_data.get("designation")
        email = serializer_data.get("email")
        phone_number = serializer_data.get("phone_number")

        try:
            sponsor = SalaryForLifeSponsor.objects.create(
                brand_name=brand_name,
                designation=designation,
                email=email,
                phone_number=phone_number,
            )
            if sponsor:
                send_email(
                    email=email,
                    brand_name=brand_name,
                    subject="Brand Profile Creation Successful!",
                    template_dir="sponsor_confirmation_email.html",
                )

            serialized_sponsor = S4LSponsorSerializer(sponsor)
            
            return Response(
                {
                    "message": "Salary 4 Life Sponsor Profile Created Successfully!",
                    "sponsor_details": serialized_sponsor.data,
                },
                status=status.HTTP_201_CREATED,
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while attempting to create Salary 4 Life Sponsor",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class SalaryForLifeParticipantView(APIView):
    def post(self, request):
        serializer = S4LParticipantSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        name = serializer_data.get("name")
        email = serializer_data.get("email")
        phone_number = serializer_data.get("phone_number")
        location = serializer_data.get("location")
        referrer = serializer_data.get("referrer")

        try:
            participant = SalaryForLifeParticipant.objects.create(
                name=name,
                email=email,
                phone_number=phone_number,
                location=location,
                referrer=referrer,
            )

            serialized_participants = S4LParticipantSerializer(participant)

            if participant:
                send_email(
                    email=email,
                    name=name,
                    subject="Participant Profile Created Successfully!",
                    template_dir="participant_confirmation_email.html",
                )
            return Response(
                {
                    "message": "Salary For Life Participant created successfully!",
                    "participant_details": serialized_participants.data,
                },
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            return Response(
                {"message": "An error occurred while attempting to create Salary 4 Life participant!", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@staff_member_required
def view_logs(
    request,
):
    """View logs dynamically based on the file name with color highlighting."""
    logs_dir = os.path.join(settings.BASE_DIR, "logs")  # Fixed logs directory

    file_name = request.GET.get("file_name")
    if not file_name:

        file_name = f"errors_{datetime.now().strftime('%Y-%m-%d')}.log"

    log_file = os.path.join(logs_dir, file_name)

    if not os.path.exists(log_file):
        return HttpResponse(
            "<h2 style='color: red; text-align: center;'>Log file not found</h2>",
            status=404,
        )

    with open(log_file, "r") as file:
        log_content = file.read()

    # Apply color highlighting
    def colorize_logs(log_text):
        log_text = re.sub(
            r"(ERROR)",
            r"<span style='color: red; font-weight: bold;'>\1</span>",
            log_text,
        )
        log_text = re.sub(
            r"(WARNING)",
            r"<span style='color: yellow; font-weight: bold;'>\1</span>",
            log_text,
        )
        log_text = re.sub(
            r"(INFO)",
            r"<span style='color: limegreen; font-weight: bold;'>\1</span>",
            log_text,
        )
        return log_text

    log_content = colorize_logs(log_content)

    # HTML template with Dark Mode and wide layout
    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Logs - {file_name}</title>
        <style>
            body {{
                background-color: #1e1e1e;
                color: #c7c7c7;
                font-family: Arial, sans-serif;
                padding: 20px;
            }}
            .container {{
                max-width: 90%;
                margin: auto;
                background: #2e2e2e;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }}
            pre {{
                background: #333;
                color: #e6e6e6;
                padding: 15px;
                overflow-x: auto;
                border-radius: 5px;
                font-size: 14px;
                white-space: pre-wrap;
            }}
            h1 {{
                text-align: center;
                color: #fff;
            }}
            .back-link {{
                display: block;
                text-align: center;
                margin-top: 20px;
                color: #00aaff;
                text-decoration: none;
            }}
            .back-link:hover {{
                text-decoration: underline;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>📜 Logs - {file_name}</h1>
            <pre>{log_content}</pre>
            <a href="/admin/" class="back-link">🔙 Back to Admin</a>
        </div>
    </body>
    </html>
    """

    return HttpResponse(html_template)


@method_decorator(staff_member_required, name="dispatch")
class RunBankerDrawManuallyView(APIView):

    serializer_class = RunBankerDrawManuallySerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        batch_id = serializer.validated_data.get("batch_id")
        LottoTicket.banker_draw(batch_id=batch_id, run_manually=True)

        return Response(
            {"message": "Banker draw has been run manually"},
            status=status.HTTP_200_OK,
        )


class ServerHealthCheckEndpoint(APIView):
    permission_classes = []

    def get(self, request):
        lotto_ticket = LottoTicket.objects.last()
        return Response(
            {"message": "Server is up and running", "last_game_play_id": lotto_ticket.game_play_id},
            status=status.HTTP_200_OK,
        )


class ErroneousTransferRefundLogView(APIView):
    permission_classes = [SuperUser2Permission]

    serializer_class = ErroneousTransferRefundLogSerializer

    def post(self, request):

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        response = ErroneousTransferRefundLog.create_log(
            phone_number=serializer_data.get("phone_number"), requested_by=serializer_data.get("requested_by"), amount=serializer_data.get("amount")
        )

        return Response(data=response, status=status.HTTP_200_OK)


class ContactUsFormView(APIView):
    def post(self, request):
        serializer = ContactUsFormSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        full_name = serializer_data.get("full_name")
        email = serializer_data.get("email")
        phone_no = serializer_data.get("phone_no")
        message = serializer_data.get("message")
        try:
            message = ContactUsForm.objects.create(
                full_name=full_name,
                email=email,
                phone_no=phone_no,
                message=message,
            )

            serialized_message = ContactUsFormSerializer(message)

            if serialized_message:
                return Response(
                    {
                        "message": "Salary for Life contact form submitted successfully!",
                        "participant_details": serialized_message.data,
                    },
                    status=status.HTTP_201_CREATED,
                )
        except Exception as e:
            return Response(
                {
                    "message": "Salary for Life contact form submission failed!",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class ScratchCardPartnershipView(APIView):
    def post(self, request):
        serializer = ScratchCardPartnershipSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data

        name = serializer_data.get("name")
        email = serializer_data.get("email")
        phone_number = serializer_data.get("phone_number")
        location = serializer_data.get("location")

        try:
            partnership_instance = ScratchCardPartnership.objects.create(
                name=name,
                email=email,
                phone_number=phone_number,
                location=location,
            )

            serialized_partnership = ScratchCardPartnershipSerializer(partnership_instance)

            if partnership_instance:
                send_email(
                    email=email,
                    name=name,
                    subject="Scratch Card Partnership Profile Created Successfully!",
                    template_dir="scratch_card_confirmation_email.html",
                )

            return Response(
                {
                    "message": "Scratch Card Partnership created successfully!",
                    "partnership": serialized_partnership.data
                }, status=status.HTTP_201_CREATED
            )
        except Exception as e:
            return Response(
                {
                    "message": "An error occurred while attempting to create Scratch Card Partnership!",
                    "error": str(e),
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        


class ManuallyCreateARetailWinningView(APIView):
    permission_classes = [SuperUser2Permission]

    serializer_class = ManuallyCreateARetailWinningSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        game_play_id = serializer.validated_data["game_play_id"]
        amount_won = serializer.validated_data["amount_won"]
        ticket = serializer.validated_data["ticket"]

        lotto = LottoTicket.objects.filter(game_play_id = game_play_id, paid = True, ticket = ticket, agent_profile__isnull = False)
        if len(lotto) == 0:
            return Response(
                {"message": "lotto ticket not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        lotto_ticket_instance = lotto.last()

        if LottoWinners.objects.filter(game_play_id__iexact=lotto_ticket_instance.game_play_id).exists():
            return Response(
                {"message": "lotto ticket winner already exists"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        else:
            LottoWinners.create_lotto_winner_obj(
                lottery=lotto_ticket_instance,
                batch=lotto_ticket_instance.batch,
                phone_number=lotto_ticket_instance.user_profile.phone_number,
                ticket=[int(i) for i in lotto_ticket_instance.ticket.split(",")],
                win_type="ORDINARY_WINNER",
                match_type="PERM_2",
                lotto_type="BANKER",
                game_play_id=lotto_ticket_instance.game_play_id,
                stake_amount=lotto_ticket_instance.stake_amount,
                earning=amount_won,
                channel_played_from=lotto_ticket_instance.channel,
                run_batch_id=str(lotto_ticket_instance.batch.batch_uuid),
            )

        return Response(
            {"message": "lotto ticket winner created"},
            status=status.HTTP_200_OK,
        )


class FetchLottoTicketDetailsView(APIView):
    serializer_class = FetchLottoTicketDetailsSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        game_play_id = serializer.validated_data["game_play_id"]


        lotto_ticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id)
        if lotto_ticket_qs.exists():
            last_instance = lotto_ticket_qs.last()
            lotto_ticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id, batch = last_instance.batch)
        winnings_qs = LottoWinners.objects.filter(game_play_id=game_play_id)

        winners_serializer = LottoTicketGameResultSerializer(winnings_qs, many=True).data
        lotto_ticket_serializer = MainLottoTicketSerializer(lotto_ticket_qs, many=True).data

        data = {
            "lotto_ticket": lotto_ticket_serializer,
            "winners": winners_serializer,
        }

        return Response(data, status=status.HTTP_200_OK)








