from django.contrib.admin.filters import SimpleListFilter


class StakeAmountFilter(SimpleListFilter):
    title = "Stake amount"  # display name of the filter
    parameter_name = "stake_amount"  # query string parameter name

    def lookups(self, request, model_admin):
        """Return a list of tuples for filtering options."""
        amounts = model_admin.get_queryset(request).values_list("stake_amount", flat=True).distinct()
        return [(a, a) for a in amounts]

    def queryset(self, request, queryset):
        """Return a filtered queryset based on the selected filter option."""
        if self.value():
            return queryset.filter(stake_amount=self.value())
        else:
            return queryset


class AmountFilter(SimpleListFilter):
    title = "Winning Amount"  # display name of the filter
    parameter_name = "amount"  # query string parameter name

    def lookups(self, request, model_admin):
        """Return a list of tuples for filtering options."""
        amounts = model_admin.get_queryset(request).values_list("amount", flat=True).distinct()
        return [(a, a) for a in amounts]

    def queryset(self, request, queryset):
        """Return a filtered queryset based on the selected filter option."""
        if self.value():
            return queryset.filter(amount=self.value())
        else:
            return queryset


class NitroswitchActionFilter(SimpleListFilter):
    title = "Action"
    parameter_name = "action"

    def lookups(self, request, model_admin):
        """Return a list of tuples for filtering options."""
        actions = model_admin.get_queryset(request).values_list("action", flat=True).distinct()
        return [(a, a) for a in actions]

    def queryset(self, request, queryset):
        """Return a filtered queryset based on the selected filter option."""
        if self.value():
            return queryset.filter(action=self.value())
        else:
            return queryset


class NitroswitchSericeType(SimpleListFilter):
    title = "Service Type"
    parameter_name = "service_type"

    def lookups(self, request, model_admin):
        """Return a list of tuples for filtering options."""
        service_types = model_admin.get_queryset(request).values_list("service_type", flat=True).distinct()
        return [(a, a) for a in service_types]

    def queryset(self, request, queryset):
        """Return a filtered queryset based on the selected filter option."""
        if self.value():
            return queryset.filter(service_type=self.value())
        else:
            return queryset
