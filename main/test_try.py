data = {
    "status": "success",
    "message": "The process was completed successfully",
    "data": {
        "page_info": {"total": 1, "current_page": 1, "total_pages": 1},
        "transactions": [
            {
                "nuban": "**********",
                "bank_code": "000017",
                "account_name": "Liberty Assured Limited 2",
                "account_email": "<EMAIL>",
                "account_mobile_number": "+*************",
                "is_test": 0,
                "account_reference": "297",
                "source_bank_code": "000017",
                "source_nuban": "**********",
                "source_account_name": "Liberty Special Loans",
                "amount": 100,
                "fee": 10,
                "currency": "NGN",
                "transaction_time": "**********",
                "unique_reference": "wf_py_297_qe0jal7lju9",
                "bank_reference": "None",
                "transaction_type": "PAYOUT",
                "status": "FAILED",
                "settled_status": "LOCKED",
                "failure_type": "None",
                "user_message": "Insufficient balance",
                "bank_response": "None",
                "failure_type": "None",
                "user_message": "Insufficient balance",
                "bank_response": "None",
                "service_bank_code": "060001",
                "sweep_status": "None",
                "is_posted_to_mifos": "None",
                "payout_user_message": "None",
                "narration": "None",
                "meta": "None",
                "reconciliation_status": "UNRECONCILED",
                "merchantId": 297,
                "destination_nuban": "None",
                "merchant_email": "<EMAIL>",
                "merchant_business_name": "Liberty Assured Limited 2",
                "created_at": "2022-06-30 09:00:47",
                "updated_at": "2022-06-30 09:00:47",
            }
        ],
    },
}

print(data.get("data").get("transactions")[0]["unique_reference"])
