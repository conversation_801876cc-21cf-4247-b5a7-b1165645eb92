from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied

from main.models import IPWhitelist


class IpWhiteListPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")

        try:
            IPWhitelist.objects.get(ip_address=x_forwarded_for, allowed=True)
        except IPWhitelist.DoesNotExist:
            raise PermissionDenied()
        return True
