from django.urls import path

from admin_dashboard import views

urlpatterns = [
    path("dash-wallets-metrics", views.MainDashboardWalletsView.as_view()),
    path("dash-total-tickets-sold-amount", views.MainDashboardTicketsSoldAmountView.as_view()),
    path("dash-total-tickets-sold-count", views.MainDashboardTicketsSoldCountView.as_view()),
    path("dash-payouts-data", views.MainDashboardPayoutDatatView.as_view()),
    path("dash-remittance-data", views.MainDashboardRemittanceDatatView.as_view()),
    path("dash-terminals-count", views.MainDashboardTerminalsCountView.as_view()),
    path("dash-inflows-data", views.MainDashboardInflowsDataView.as_view()),
    path("dash-transactions-metrics", views.MainDashboardTransactionsView.as_view()),
    path("dash-failed-transactions-metrics", views.MainDashboardFailedTransactionsView.as_view()),
    path("dash-transactions-comparatives", views.MainDashboardTransactionsComparativesView.as_view()),
    path("dash-transactions-count", views.MainDashboardTransactionsCountsView.as_view()),
    path("dash-sales-metrics", views.MainDashboardSalesView.as_view()),
    path("dash-tickets-sales-range", views.MainDashboardTicketsSalesRangeView.as_view()),
    path("dash-average-sales", views.MainDashboardAverageSalesView.as_view()),
    path("dash-commission-data", views.MainDashboardCommissionsView.as_view()),
    path("dash-sales-winning-comparative", views.MainDashboardSalesWinningsView.as_view()),
    path("dash-chart-ticket-sales", views.DashboardChartTicketSalesView.as_view()),
    path("dash-chart-commission-salary", views.DashboardChartCommissionSalaryView.as_view()),
    path("dash-chart-transaction-comparative", views.DashboardChartTransactionComparativeView.as_view()),
    path("dash-chart-game-comparative", views.DashboardChartsGameComparative.as_view()),
    path("dash-agents", views.DashboardAgentView.as_view()),
    path("inactive-agents", views.AllInActiveAgentsView.as_view()),
    path("active-agents", views.AllActiveAgentsView.as_view()),
    path("supervisor-analytics", views.SupervisorAnalyticsView.as_view()),
    path("supervisor-data", views.SupervisorDataView.as_view()),
    path("supervisor-agents-list-table", views.SupervisorAgentsListView.as_view()),
    path("supervisor-no-performing-agents-list", views.SupervisorNoPerformingAgentsListView.as_view()),
    path("supervisor-absent-agents-list", views.SupervisorAbsentAgentsListView.as_view()),
    path("location-agents-list/<int:id>", views.LocationAgentsListView.as_view()),
    path("location-no-performing-agents-list/<int:id>", views.LocationNoPerformingAgentsListView.as_view()),
    path("location-absent-agents-list/<int:id>", views.LocationAbsentAgentsListView.as_view()),
    path("location-remittance-list/<int:id>", views.LocationRemittanceListView.as_view()),
    path("supervisor-location_details/<int:id>", views.SupervisorLocationDetailView.as_view()),
    path("single-agent-details/<int:id>", views.SingleAgentDetailView.as_view()),
    path("single-agent-game-play-details/<int:id>", views.SingleAgentGamePlayDetailView.as_view()),
    path("single-agent-remittance-details/<int:id>", views.SingleAgentRemittanceDetailsView.as_view()),
    path("overall-agent-performance", views.OverallAgentsPerformanceView.as_view()),
    path("agency-banking-transactions-metrics", views.LottoAgentsTransactionsAnalyticsView.as_view()),
    path("get_supervisor_user_role", views.UserDetailsView.as_view()),
    path("lotto_tickets_table", views.LottoTicketDataAnalyticsDataExposureView.as_view()),
    path("lottery_model_table", views.LotteryModelTicketDataAnalyticsDataExposureView.as_view()),
    path("awoofgame_model_table", views.AwoofGameTableDataAnalyticsDataExposureView.as_view()),
    path("soccer_prediction_model_table", views.SoccerPredictionDataAnalyticsDataExposureView.as_view()),
    path("lotto_winners_model_table", views.LottoWinnersDataAnalyticsDataExposureView.as_view()),
    path("lottery_winners_model_table", views.LotteryWinnersDataAnalyticsDataExposureView.as_view()),
    path("awoof_draw_model_table", views.AwoofDrawTableDataAnalyticsDataExposureView.as_view()),
    path("soccercash_winners_model_table", views.SoccerCashWinnersDataAnalyticsDataExposureView.as_view()),
    path("agent_funding_table_model_table", views.AgentFundingTableDataAnalyticsDataExposureView.as_view()),
    path("payment_transaction_model_table", views.PaymentTransactionTableDataAnalyticsDataExposureView.as_view()),
    path("failed_remittance_agency_wallet_charge_table", views.FailedRemittanceAgencyWalletChargeDataAnalyticsDataExposureView.as_view()),
    path("sales_target_graph", views.SalesTargetGraphView.as_view()),
    path("retrieve_terminal", views.SupervisorTerminalRetrievalView.as_view()),
]
