from rest_framework import serializers
from pos_app.models import SupervisorTerminalRetrieval

class TerminalRetrievalSerializer(serializers.Serializer):
    retrieved = serializers.CharField()
    agent_id = serializers.CharField()

class SupervisorTerminalRetrievalSerializer(serializers.ModelSerializer):
    class Meta:
        model = SupervisorTerminalRetrieval
        fields = [
            "retrieved",
            "supervisor_name",
            "supervisor_phone",
            "supervisor_email",
            "terminal_id",
            "agent_name",
            "agent_phone",
            "agent_email",
        ]