from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from admin_dashboard.models import ConstantTable


class ContantTableResource(resources.ModelResource):
    class Meta:
        model = ConstantTable


class ConstantTableResourceAdmin(ImportExportModelAdmin):
    resource_class = ContantTableResource
    search_fields = ["sales_target"]
    list_filter = ("sales_target", "created_at", "updated_at")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.fields]


admin.site.register(ConstantTable, ConstantTableResourceAdmin)
