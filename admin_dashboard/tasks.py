import ast
from datetime import datetime, timedelta
from math import floor

from celery import shared_task
from django.conf import settings
from django.db.models import F, Q, Sum
from django.utils import timezone

from admin_dashboard.helpers.helpers import date_utility, send_rtp_sales_winnings_email
from awoof_app.models import AwoofGameTable
from main.models import (
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    PayoutTransactionTable,
)
from pos_app.models import (
    Agent,
    AgentWallet,
    AgentWalletTransaction,
    LottoAgentRemittanceTable,
    Supervisor,
)
from sport_app.models import DecisioningResult, SoccerCashWinner
from wyse_ussd.models import SoccerPrediction

filter_by_date = date_utility(datetime)
date_today = timezone.now()


@shared_task
def lotto_agents_set_activity():
    date_today = timezone.now()
    all_agents_qs = Agent.objects.all()
    ticket_sales_qs = AgentWalletTransaction.objects.filter(
        status="SUCCESSFUL",
        transaction_from="GAME_PLAY",
        agent_wallet__agent__agent_type="LOTTO_AGENT",
        agent_wallet__agent__terminal_id__isnull=False,
    )

    for agent in all_agents_qs:
        agent_wallet = AgentWallet.objects.filter(agent=agent).last()

        if not agent_wallet:
            continue

        # Days Computations
        date_joined = agent.created_date
        days_since_joined = (date_today - date_joined).days
        last_active = agent_wallet.updated_at
        days_since_last_updated = (date_today - last_active).days

        if days_since_joined > 7:
            number_of_weeks = floor(days_since_joined / 7)
            days_since_joined_less_weekends = floor(days_since_joined - number_of_weeks) + days_since_joined % 7
        else:
            days_since_joined_less_weekends = days_since_joined

        agent_tickets_sales_qs = ticket_sales_qs.filter(agent_wallet__agent=agent)
        total_sales_amount = list(agent_tickets_sales_qs.aggregate(Sum("amount")).values())[0]

        average_sales_per_day = (total_sales_amount if total_sales_amount else 0.00) / (
            days_since_joined_less_weekends if days_since_joined_less_weekends else 1
        )

        days_active = 3
        days_active_percentage = (days_active / days_since_joined_less_weekends if days_since_joined_less_weekends else 1) * 100

        if average_sales_per_day < 1000 or days_since_last_updated >= 1:
            agent.performance_status = "ABSENT"
            break
        elif average_sales_per_day < 7000:
            agent.performance_status = "INACTIVE"
        elif average_sales_per_day < 7000 and days_active_percentage > 60:
            agent.performance_status = "UNDERPERFORMING"
        elif average_sales_per_day > 7000 and average_sales_per_day < 10000:
            agent.performance_status = "PERFORMING"
        elif average_sales_per_day > 10000 and average_sales_per_day < 20000:
            agent.performance_status = "TOP_PERFORMING"
        elif average_sales_per_day > 20000 and average_sales_per_day < 30000:
            agent.performance_status = "HIGH_PERFORMING"
        elif average_sales_per_day > 30000:
            agent.performance_status = "SUPER_PERFORMING"
        else:
            agent.performance_status = "ACTIVE"

        agent.save()


@shared_task
def set_supervisors_performance_status():
    supervisors_qs = Supervisor.objects.all()

    for supervisor in supervisors_qs:
        supervisor_agents = Agent.objects.filter(supervisor=supervisor)

        supervisor_agents_count = supervisor_agents.count()
        absent_agents_count = supervisor_agents.filter(performance_status="PERFORMING").count()
        performing_agents_count = supervisor_agents.filter(performance_status="ABSENT").count()
        inactive_agents_count = supervisor_agents.filter(performance_status="INACTIVE").count()
        super_performing_agents_count = supervisor_agents.filter(performance_status="SUPER_PERFORMING").count()
        high_performing_agents_count = supervisor_agents.filter(performance_status="HIGH_PERFORMING").count()
        top_performing_agents_count = supervisor_agents.filter(performance_status="TOP_PERFORMING").count()
        under_performing_agents_count = supervisor_agents.filter(performance_status="UNDER_PERFORMING").count()

        supervisor_agent_transactions = AgentWalletTransaction.objects.filter(
            agent_wallet__agent__supervisor=supervisor, transaction_from__in=["GAME_PLAY"], status="SUCCESSFUL"
        )

        supervisor_agents_tickets_sales_amt = list(supervisor_agent_transactions.aggregate(Sum("amount")).values())[0]

        average_sales_amount = (supervisor_agents_tickets_sales_amt if supervisor_agents_tickets_sales_amt else 0) / (
            supervisor_agents_count if supervisor_agents_count else 1
        )

        (absent_agents_count / supervisor_agents_count if supervisor_agents_count else 1) * 100
        performing_agents_count_percentage = (performing_agents_count / supervisor_agents_count if supervisor_agents_count else 1) * 100
        inactive_agents_count_percentage = (inactive_agents_count / supervisor_agents_count if supervisor_agents_count else 1) * 100
        (super_performing_agents_count / supervisor_agents_count if supervisor_agents_count else 1) * 100
        high_performing_agents_count_percentage = (high_performing_agents_count / supervisor_agents_count if supervisor_agents_count else 1) * 100
        top_performing_agents_count_percentage = (top_performing_agents_count / supervisor_agents_count if supervisor_agents_count else 1) * 100
        under_performing_agents_count_percentage = (under_performing_agents_count / supervisor_agents_count if supervisor_agents_count else 1) * 100

        if performing_agents_count_percentage >= 70 and under_performing_agents_count_percentage <= 10:
            supervisor.performance_status = "PERFORMING"
        elif inactive_agents_count_percentage <= 60 and average_sales_amount > 7000:
            supervisor.performance_status = "INACTIVE"
        elif high_performing_agents_count_percentage >= 50:
            supervisor.performance_status = "HIGH_PERFORMING"
        elif top_performing_agents_count_percentage >= 70:
            supervisor.performance_status = "TOP_PERFORMING"
        elif under_performing_agents_count_percentage >= 50 or performing_agents_count_percentage <= 50:
            supervisor.performance_status = "UNDER_PERFORMING"
        else:
            supervisor.performance_status = "PERFORMING"

        supervisor.save()


@shared_task
def daily_rtp_winnings_sales_monitoring():
    """
    Celery task to monitor the business performance of lotto games

    Metrics:
        - rtp:
            - stands for 'return to player.'
            - It is the amount that is returned to players as bonus

        - winnings:
            - This is the total value of winnings from game plays
            - A greater winnings amount than sales amount signifies a loss and vice-versa

        - Payouts:
            - This is the withdrawals made by game players
            - In ideal situation payout should never exceed winnings amount

    Queries:
        - Queries are performed across the following models:
            - LottoTicket: Holds ticket sales for INSTANT_CASHOUT, SALARY_FOR_LIFE, BANKER and QUICKA
            - LotteryModel: Holds ticket sales for WYSE_CASH
            - SoccerPrediction: Holds ticket sales for soccer games prediction
            - Payout table: Holds withdrawals made by players
    """
    # Date
    date_today = timezone.now().date()
    _14_days_ago = date_today - timedelta(days=14)
    month_start = date_today.replace(day=1)
    yesterday = date_today - timedelta(days=1)

    # Tickets Sales
    lottery_model_sales_qs = LotteryModel.objects.filter(paid=True)  # For WYSE_CASH game plays
    soccer_prediction_sales_qs = SoccerPrediction.objects.filter(paid=True)  # For SOCCER_CASH game plays
    lotto_ticket_sales_qs = LottoTicket.objects.filter(paid=True)  # For all other game plays
    awoof_sales_qs = AwoofGameTable.objects.filter(paid=True)  # For awoof game plays

    lottery_model_sales_amount_today = list(lottery_model_sales_qs.filter(date__date=date_today).aggregate(Sum("amount_paid")).values())[0]
    soccer_prediction_sales_amount_today = list(soccer_prediction_sales_qs.filter(date__date=date_today).aggregate(Sum("amount_paid")).values())[0]
    lotto_ticket_sales_amount_today = list(lotto_ticket_sales_qs.filter(date__date=date_today).aggregate(Sum("amount_paid")).values())[0]
    awoof_ticket_sales_amount_today = list(awoof_sales_qs.filter(date_created__date=date_today).aggregate(Sum("amount_paid")).values())[0]

    total_sales_amount = (
        (lottery_model_sales_amount_today if lottery_model_sales_amount_today else 0.00)
        + (soccer_prediction_sales_amount_today if soccer_prediction_sales_amount_today else 0.00)
        + (lotto_ticket_sales_amount_today if lotto_ticket_sales_amount_today else 0.00)
        + (awoof_ticket_sales_amount_today if awoof_ticket_sales_amount_today else 0.00)
    )

    # Winnings
    lottery_winners_table_qs = LotteryWinnersTable.objects.filter()  # For WYSE_CASH winners
    soccercash_winners_qs = SoccerCashWinner.objects.filter()  # For soccercash winners
    lotto_winners_qs = LottoWinners.objects.filter()  # For all other game winnings
    # awoof_winnings_qs = LifeStyleTable.objects.filter(item_given=True) # For awoof draws

    lotto_winners_amount_today = list(lotto_winners_qs.filter(date_won__date=date_today).aggregate(Sum("earning")).values())[0]
    lottery_winners_table_amount_today = list(lottery_winners_table_qs.filter(date_won__date=date_today).aggregate(Sum("earning")).values())[0]
    soccercash_winners_amount_today = list(soccercash_winners_qs.filter(date_won__date=date_today).aggregate(Sum("earning")).values())[0]
    # awoof_winners_amount_today = list(awoof_winnings_qs.filter(
    #                             date_created__date=date_today
    #                             ).aggregate(Sum("item_amount")).values())[0]

    total_winnings_amount = (
        (lotto_winners_amount_today if lotto_winners_amount_today else 0.00)
        + (lottery_winners_table_amount_today if lottery_winners_table_amount_today else 0.00)
        + (soccercash_winners_amount_today if soccercash_winners_amount_today else 0.00)  # +
        # (awoof_winners_amount_today if awoof_winners_amount_today else 0.00)
    )

    # Total Winnings - This Month
    lotto_winners_amount_this_month = list(lotto_winners_qs.filter(date_won__date__gte=month_start).aggregate(Sum("earning")).values())[0]
    lottery_winners_table_amount_this_month = list(
        lottery_winners_table_qs.filter(date_won__date__gte=month_start).aggregate(Sum("earning")).values()
    )[0]
    soccercash_winners_amount_this_month = list(soccercash_winners_qs.filter(date_won__date__gte=month_start).aggregate(Sum("earning")).values())[0]

    total_winnings_amount_this_month = (
        (lotto_winners_amount_this_month if lotto_winners_amount_this_month else 0.00)
        + (lottery_winners_table_amount_this_month if lottery_winners_table_amount_this_month else 0.00)
        + (soccercash_winners_amount_this_month if soccercash_winners_amount_this_month else 0.00)  # +
        # (awoof_winners_amount_today if awoof_winners_amount_today else 0.00)
    )

    # Telco Winnings
    telco_lottery_winners_table_qs = lottery_winners_table_qs.filter(lottery_source_tag="USSD")  # For WYSE_CASH winners
    telco_soccercash_winners_qs = soccercash_winners_qs.filter(channel_played_from="USSD")  # For soccercash winners
    telco_lotto_winners_qs = lotto_winners_qs.filter(channel_played_from="USSD")  # For all other game winnings
    # telco_awoof_winnings_qs = awoof_winnings_qs.filter(item_given=True) # For awoof draws

    telco_lotto_winners_amount_today = list(telco_lotto_winners_qs.filter(date_won__date=date_today).aggregate(Sum("earning")).values())[0]
    telco_lottery_winners_table_amount_today = list(
        telco_lottery_winners_table_qs.filter(date_won__date=date_today).aggregate(Sum("earning")).values()
    )[0]
    telco_soccercash_winners_amount_today = list(telco_soccercash_winners_qs.filter(date_won__date=date_today).aggregate(Sum("earning")).values())[0]
    # awoof_winners_amount_today = list(telco_awoof_winnings_qs.filter(
    #                             date_created__date=date_today
    #                             ).aggregate(Sum("item_amount")).values())[0]

    telco_total_winnings_amount = (
        (telco_lotto_winners_amount_today if telco_lotto_winners_amount_today else 0.00)
        + (telco_lottery_winners_table_amount_today if telco_lottery_winners_table_amount_today else 0.00)
        + (telco_soccercash_winners_amount_today if telco_soccercash_winners_amount_today else 0.00)  # +
        # (telco_awoof_winners_amount_today if telco_awoof_winners_amount_today else 0.00)
    )

    # Telco Winnings - This Month
    telco_lotto_winners_amount_this_month = list(telco_lotto_winners_qs.filter(date_won__date__gte=month_start).aggregate(Sum("earning")).values())[0]
    telco_lottery_winners_table_amount_this_month = list(
        telco_lottery_winners_table_qs.filter(date_won__date__gte=month_start).aggregate(Sum("earning")).values()
    )[0]
    telco_soccercash_winners_amount_this_month = list(
        telco_soccercash_winners_qs.filter(date_won__date__gte=month_start).aggregate(Sum("earning")).values()
    )[0]

    telco_total_winnings_amount_this_month = (
        (telco_lotto_winners_amount_this_month if telco_lotto_winners_amount_this_month else 0.00)
        + (telco_lottery_winners_table_amount_this_month if telco_lottery_winners_table_amount_this_month else 0.00)
        + (telco_soccercash_winners_amount_this_month if telco_soccercash_winners_amount_this_month else 0.00)  # +
        # (telco_awoof_winners_amount_today if telco_awoof_winners_amount_today else 0.00)
    )

    # Other Winnings
    other_lottery_winners_table_qs = lottery_winners_table_qs.filter(~Q(lottery_source_tag="USSD"))  # For WYSE_CASH winners
    other_soccercash_winners_qs = soccercash_winners_qs.filter(~Q(channel_played_from="USSD"))  # For soccercash winners
    other_lotto_winners_qs = lotto_winners_qs.filter(~Q(channel_played_from="USSD"))  # For all other game winnings
    # other_awoof_winnings_qs = awoof_winnings_qs.filter(item_given=True) # For awoof draws

    other_lotto_winners_amount_today = list(other_lotto_winners_qs.filter(date_won__date=date_today).aggregate(Sum("earning")).values())[0]
    other_lottery_winners_table_amount_today = list(
        other_lottery_winners_table_qs.filter(date_won__date=date_today).aggregate(Sum("earning")).values()
    )[0]
    other_soccercash_winners_amount_today = list(other_soccercash_winners_qs.filter(date_won__date=date_today).aggregate(Sum("earning")).values())[0]
    # other_awoof_winners_amount_today = list(other_awoof_winnings_qs.filter(
    #                             date_created__date=date_today
    #                             ).aggregate(Sum("item_amount")).values())[0]

    other_total_winnings_amount = (
        (other_lotto_winners_amount_today if other_lotto_winners_amount_today else 0.00)
        + (other_lottery_winners_table_amount_today if other_lottery_winners_table_amount_today else 0.00)
        + (other_soccercash_winners_amount_today if other_soccercash_winners_amount_today else 0.00)  # +
        # (other_awoof_winners_amount_today if other_awoof_winners_amount_today else 0.00)
    )

    # Other Winnings - This Month
    other_lotto_winners_amount_this_month = list(other_lotto_winners_qs.filter(date_won__date__gte=month_start).aggregate(Sum("earning")).values())[0]
    other_lottery_winners_table_amount_this_month = list(
        other_lottery_winners_table_qs.filter(date_won__date__gte=month_start).aggregate(Sum("earning")).values()
    )[0]
    other_soccercash_winners_amount_this_month = list(
        other_soccercash_winners_qs.filter(date_won__date__gte=month_start).aggregate(Sum("earning")).values()
    )[0]

    other_total_winnings_amount_this_month = (
        (other_lotto_winners_amount_this_month if other_lotto_winners_amount_this_month else 0.00)
        + (other_lottery_winners_table_amount_this_month if other_lottery_winners_table_amount_this_month else 0.00)
        + (other_soccercash_winners_amount_this_month if other_soccercash_winners_amount_this_month else 0.00)  # +
        # (other_awoof_winners_amount_this_month if other_awoof_winners_amount_this_month else 0.00)
    )

    # Payouts
    payouts_qs = PayoutTransactionTable.objects.all().filter(payment_initiated=True, disbursed=True, is_verified=True)
    payouts_amount_today = list(payouts_qs.filter(date_added__date=date_today).aggregate(Sum("amount")).values())[0]
    payouts_amount_today_winnings = list(payouts_qs.filter(date_added__date=date_today, date_won__date=date_today).aggregate(Sum("amount")).values())[
        0
    ]
    payouts_amount_yesterday_winnings = list(
        payouts_qs.filter(date_added__date=date_today, date_won__date=yesterday).aggregate(Sum("amount")).values()
    )[0]

    payout_telco_qs = payouts_qs.filter(date_added__date=date_today, channel="USSD")
    payout_others_qs = payouts_qs.filter(Q(date_added__date=date_today) & ~Q(channel="USSD"))
    payouts_amount_telco_today = list(payout_telco_qs.filter(date_added__date=date_today).aggregate(Sum("amount")).values())[0]
    payouts_amount_others_today = list(payout_others_qs.filter(date_added__date=date_today).aggregate(Sum("amount")).values())[0]

    # RTP
    lottery_model_rtp_amount_today = list(lottery_model_sales_qs.filter(paid_date__date=date_today).aggregate(Sum("rtp")).values())[0]
    lotto_ticket_rtp_amount_today = list(lotto_ticket_sales_qs.filter(date__date=date_today).aggregate(Sum("rtp")).values())[0]
    soccer_prediction_dump_qs = DecisioningResult.objects.filter(date_created__date=date_today).values_list("result_payload", flat=True)
    soccer_prediction_rtp_amount_today = sum([ast.literal_eval(data)["rtp"] for data in soccer_prediction_dump_qs])
    # awoof_rtp_amount_today = list(awoof_sales_qs.filter(
    #                             date_created__date=date_today
    #                             ).aggregate(Sum("rtp")).values())[0]

    total_rtp_amount = (
        (lottery_model_rtp_amount_today if lottery_model_rtp_amount_today else 0.00)
        + (soccer_prediction_rtp_amount_today if soccer_prediction_rtp_amount_today else 0.00)
        + (lotto_ticket_rtp_amount_today if lotto_ticket_rtp_amount_today else 0.00)  # +
        # (awoof_rtp_amount_today if awoof_rtp_amount_today else 0.00)
    )

    # Remittances
    agents_remittance_qs = LottoAgentRemittanceTable.objects.filter()
    # print("::::::::::::::::::::::::::::::")
    # print(agents_remittance_qs)
    # print("::::::::::::::::::::::::::::::")
    # remittance_qs = agents_remittance_qs.filter(**self.date_filter_three).order_by("-updated_at")
    _14_days_due_remittance_amount = list(
        agents_remittance_qs.filter(remitted=False, due=True, created_at__gte=_14_days_ago)
        .annotate(amount_owed=F("amount") - F("amount_paid"))
        .aggregate(Sum("amount_owed"))
        .values()
    )[0]

    if settings.DEBUG is True:
        admin_email_list = ["<EMAIL>"]
    else:
        admin_email_list = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]

    for email in admin_email_list:
        send_rtp_sales_winnings_email(
            message="Kindly find the report for the Sales, RTP, Winnings and Payout for the day as at this hour.",
            file="",
            file_name="",
            email_subject="Sales, Winnings, RTP & Payouts Monitoring Report",
            email=email,
            total_ticket_sales_amount=f"{round(total_sales_amount, 2):,}",
            total_winnings_amount=f"{round(total_winnings_amount, 2):,}",
            telco_total_winnings_amount=f"{round(telco_total_winnings_amount, 2):,}",
            other_total_winnings_amount=f"{round(other_total_winnings_amount, 2):,}",
            total_payout_amount=f"{round(payouts_amount_today, 2):,}" if payouts_amount_today else 0.00,
            total_payout_amount_today_winnings=f"{round(payouts_amount_today_winnings, 2):,}" if payouts_amount_today_winnings else 0.00,
            total_payout_amount_yesterday_winnings=f"{round(payouts_amount_yesterday_winnings, 2):,}" if payouts_amount_yesterday_winnings else 0.00,
            telco_payouts=f"{round(payouts_amount_telco_today, 2):,}" if payouts_amount_telco_today else 0.00,
            other_payouts=f"{round(payouts_amount_others_today, 2):,}" if payouts_amount_others_today else 0.00,
            total_rtp_amount=f"{round(total_rtp_amount, 2):,}" if total_rtp_amount else 0.00,
            due_remittance_since_14_days=f"{round(_14_days_due_remittance_amount, 2):,}" if _14_days_due_remittance_amount else 0.00,
            total_winnings_amount_this_month=f"{round(total_winnings_amount_this_month, 2):,}" if total_winnings_amount_this_month else 0.00,
            telco_total_winnings_amount_this_month=(
                f"{round(telco_total_winnings_amount_this_month, 2):,}" if telco_total_winnings_amount_this_month else 0.00
            ),
            other_total_winnings_amount_this_month=(
                f"{round(other_total_winnings_amount_this_month, 2):,}" if other_total_winnings_amount_this_month else 0.00
            ),
        )
