from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from account.models import User
from admin_dashboard.permissions import SupervisorAgentPermission, SupervisorPermission, AdminOnlyPermission
from admin_dashboard.serializers import SupervisorTerminalRetrievalSerializer, TerminalRetrievalSerializer
from admin_dashboard.services import (
    Agents,
    DashboardChart,
    DataAnalyticsDataExposure,
    LottoAgentsTransactionsAnalytics,
    MainDashboard,
    SupervisorAnalytics,
)

from admin_dashboard.helpers.helpers import MetricsRedis
from pos_app.models import Agent, Supervisor, SupervisorTerminalRetrieval
from pos_app.pos_helpers import unsuspend_account_on_agency_banking


class MainDashboardWalletsView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_wallet") is None:
                response = MainDashboard(request).get_wallet_metrics()
                MetricsRedis.set_data("main_dash_wallet", response)
            else:
                response = MetricsRedis.get_data("main_dash_wallet")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardTicketsSoldAmountView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_tickets_sold_amount") is None:
                response = MainDashboard(request).get_total_tickets_sold_amount()
                MetricsRedis.set_data("main_dash_tickets_sold_amount", response)
            else:
                response = MetricsRedis.get_data("main_dash_tickets_sold_amount")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardTicketsSoldCountView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_tickets_sold_count") is None:
                response = MainDashboard(request).get_total_tickets_sold_count()
                MetricsRedis.set_data("main_dash_tickets_sold_count", response)
            else:
                response = MetricsRedis.get_data("main_dash_tickets_sold_count")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardPayoutDatatView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_payout") is None:
                response = MainDashboard(request).get_payout_data()
                MetricsRedis.set_data("main_dash_payout", response)
            else:
                response = MetricsRedis.get_data("main_dash_payout")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardRemittanceDatatView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_remittance_data") is None:
                response = MainDashboard(request).get_remittance_data()
                MetricsRedis.set_data("main_dash_remittance_data", response)
            else:
                response = MetricsRedis.get_data("main_dash_remittance_data")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardTerminalsCountView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_terminal_count") is None:
                response = MainDashboard(request).get_terminals_count()
                MetricsRedis.set_data("main_dash_terminal_count", response)
            else:
                response = MetricsRedis.get_data("main_dash_terminal_count")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardInflowsDataView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_inflow_data") is None:
                response = MainDashboard(request).get_inflows_data()
                MetricsRedis.set_data("main_dash_inflow_data", response)
            else:
                response = MetricsRedis.get_data("main_dash_inflow_data")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardTransactionsView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_transactions") is None:
                response = MainDashboard(request).get_transactions_metrics()
                MetricsRedis.set_data("main_dash_transactions", response)
            else:
                response = MetricsRedis.get_data("main_dash_transactions")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardFailedTransactionsView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_failed_transactions") is None:
                response = MainDashboard(request).get_failed_transactions_amount()
                MetricsRedis.set_data("main_dash_failed_transactions", response)
            else:
                response = MetricsRedis.get_data("main_dash_failed_transactions")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardTransactionsComparativesView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_transactions_comparative") is None:
                response = MainDashboard(request).get_transaction_comparatives()
                MetricsRedis.set_data("main_transactions_comparative", response)
            else:
                response = MetricsRedis.get_data("main_transactions_comparative")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardTransactionsCountsView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_transactions_count") is None:
                response = MainDashboard(request).get_transactions_count()
                MetricsRedis.set_data("main_dash_transactions_count", response)
            else:
                response = MetricsRedis.get_data("main_dash_transactions_count")
            return Response({"data": response}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardSalesView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_sales_data") is None:
                sales_data = MainDashboard(request).get_sales_metrics()
                MetricsRedis.set_data(key="main_dash_sales_data", data=sales_data)
            else:
                sales_data = MetricsRedis.get_data("main_dash_sales_data")
            response = {"sales_data": sales_data}
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardTicketsSalesRangeView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_ticket_sales_range") is None:
                tickets_bought_range = MainDashboard(request).get_tickets_bought_range()
                MetricsRedis.set_data("main_dash_ticket_sales_range", tickets_bought_range)
            else:
                tickets_bought_range = MetricsRedis.get_data("main_dash_ticket_sales_range")
            response = {"ticket_sales_range": tickets_bought_range}
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardAverageSalesView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_average_sales") is None:
                response = MainDashboard(request).get_average_sales()
                MetricsRedis.set_data("main_dash_average_sales", response)
            else:
                response = MetricsRedis.get_data("main_dash_average_sales")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardCommissionsView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_commissions") is None:
                response = MainDashboard(request).get_commission_metrics()
                MetricsRedis.set_data("main_dash_commissions", response)
            else:
                response = MetricsRedis.get_data("main_dash_commissions")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class MainDashboardSalesWinningsView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_sales_winnings") is None:
                response = MainDashboard(request).get_sales_winnings_comparison()
                MetricsRedis.set_data("main_dash_sales_winnings", response)
            else:
                response = MetricsRedis.get_data("main_dash_sales_winnings")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardChartTicketSalesView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_chart_ticket_sales") is None:
                response = DashboardChart(request).get_ticket_sales_count_chart()
                MetricsRedis.set_data("main_dash_chart_ticket_sales", response)
            else:
                response = MetricsRedis.get_data("main_dash_chart_ticket_sales")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardChartCommissionSalaryView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_chart_commission_salary") is None:
                response = DashboardChart(request).get_commissions_salary_chart()
                MetricsRedis.set_data("main_dash_chart_commission_salary", response)
            else:
                response = MetricsRedis.get_data("main_dash_chart_commission_salary")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class DashboardChartTransactionComparativeView(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("main_dash_chart_transaction_comp") is None:
                response = DashboardChart(request).get_transation_comparative_chart()
                MetricsRedis.set_data("main_dash_chart_transaction_comp", response)
            else:
                response = MetricsRedis.get_data("main_dash_chart_transaction_comp")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardChartsGameComparative(APIView):
    permission_classes = [IsAuthenticated, AdminOnlyPermission]

    def get(self, request):
        update = request.query_params.get("update")

        try:
            if update == "true" or MetricsRedis.get_data("dash_chart_games_comp_count") is None:
                game_count = DashboardChart(request).get_games_comparatives_count_graph()
                game_amount = DashboardChart(request).get_games_comparatives_amount_graph()
                MetricsRedis.set_data("dash_chart_games_comp_count", game_count)
                MetricsRedis.set_data("dash_chart_games_comp_amount", game_amount)
            else:
                game_count = MetricsRedis.get_data("dash_chart_games_comp_count")
                game_amount = MetricsRedis.get_data("dash_chart_games_comp_amount")
            response = {"game_count": game_count, "game_amount": game_amount}
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class DashboardAgentView(APIView):
    # permission_classes = [IsAuthenticated, AdminOnlyPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request):
        update = request.query_params.get("update")
        update = "true"

        try:
            if update == "true" or MetricsRedis.get_data("dash_agents_overview") is None:
                agents_overview = Agents(request).get_agents_overview_metrics()
                all_agents_top10 = Agents(request).get_all_agents_tickets()
                all_agents_details_table = Agents(request).get_all_agents_details_table()
                MetricsRedis.set_data("dash_agents_overview", agents_overview)
                MetricsRedis.set_data("dash_agents_top10", all_agents_top10)
                MetricsRedis.set_data("dash_agents_details_table", all_agents_details_table)
            else:
                agents_overview = MetricsRedis.get_data("dash_agents_overview")
                all_agents_top10 = MetricsRedis.get_data("dash_agents_top10")
                all_agents_details_table = MetricsRedis.get_data("dash_agents_details_table")
            response = {"overview": agents_overview, "agents_top10": all_agents_top10, "agents_details_table": all_agents_details_table,}
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AllActiveAgentsView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]
    
    def get(self, request):
        update = request.query_params.get("update")
        update = "true"

        user = request.user
        try:
            supervisor = Supervisor.objects.get(email=request.user.email)
            if user.email == supervisor.email:
                base_queryset = Agent.objects.filter(
                    supervisor=supervisor,
                    agent_type="LOTTO_AGENT",
                    terminal_id__isnull=False,
                    terminal_retrieved=False
                )
                if update == "true" or MetricsRedis.get_data("all_active_agents") is None:
                    agents_helper = Agents(request)
                    agents_helper.agents_qs = base_queryset
                    response = agents_helper.get_all_active_agents_details_table()
                    MetricsRedis.set_data("all_active_agents", response)
                else:
                    response = MetricsRedis.get_data("all_active_agents")
                return Response(response, status=status.HTTP_200_OK)
        except Supervisor.DoesNotExist:
            if user.is_staff:
                all_agents = Agent.objects.filter(
                    agent_type="LOTTO_AGENT",
                    terminal_id__isnull=False,
                    terminal_retrieved=False
                )
                if update == "true" or MetricsRedis.get_data("admin_active_agents") is None:
                    agents_helper = Agents(request)
                    agents_helper.agents_qs = all_agents
                    response = agents_helper.get_all_active_agents_details_table()
                    MetricsRedis.set_data("admin_active_agents", response)
                else:
                    response = MetricsRedis.get_data("admin_active_agents")
                return Response(response, status=status.HTTP_200_OK)  
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class AllInActiveAgentsView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request):
        update = request.query_params.get("update")
        update = "true"

        user = request.user
        try:
            MetricsRedis.delete_data("all_inactive_agents")
            supervisor = Supervisor.objects.get(email=request.user.email)
            if user.email == supervisor.email:
                base_queryset = Agent.objects.filter(
                    supervisor=supervisor,
                    agent_type="LOTTO_AGENT",
                    is_suspended=True,
                )
                MetricsRedis.delete_data("all_inactive_agents")
                if update == "true" or MetricsRedis.get_data("all_inactive_agents") is None:
                    agents_helper = Agents(request)
                    agents_helper.agents_qs = base_queryset
                    response = agents_helper.get_all_inactive_agents_details_table(request)
                    MetricsRedis.set_data("all_inactive_agents", response)
                else:
                    response = MetricsRedis.get_data("all_inactive_agents")
                return Response(response, status=status.HTTP_200_OK)
        except Supervisor.DoesNotExist:
            if user.is_staff:
                all_agents = Agent.objects.filter(
                    agent_type="LOTTO_AGENT",
                    is_suspended=True,
                )
                if update == "true" or MetricsRedis.get_data("admin_inactive_agents") is None:
                    agents_helper = Agents(request)
                    agents_helper.agents_qs = all_agents
                    response = agents_helper.get_all_inactive_agents_details_table()
                    MetricsRedis.set_data("admin_inactive_agents", response)
                else:
                    response = MetricsRedis.get_data("admin_inactive_agents")
                return Response(response, status=status.HTTP_200_OK)  
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class SupervisorAnalyticsView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request):
        update = request.query_params.get("update")
        update = "true"

        try:
            if update == "true" or MetricsRedis.get_data("supervisor_analytics") is None:
                response = SupervisorAnalytics(request).get_supervisor_analytics()
                MetricsRedis.set_data("supervisor_analytics", response)
            else:
                response = MetricsRedis.get_data("supervisor_analytics")
                return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorDataView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request):
        update = request.query_params.get("update")
        update = "true"

        try:
            if update == "true" or MetricsRedis.get_data("supervisor_data_view") is None:
                response = SupervisorAnalytics(request).get_supervisor_data()
                MetricsRedis.set_data("supervisor_data_view", response)
            else:
                response = MetricsRedis.get_data("supervisor_data_view")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorAgentsListView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request):
        update = request.query_params.get("update")
        update = "true"

        try:
            if update == "true" or MetricsRedis.get_data("supervisor_agents_list") is None:
                response = SupervisorAnalytics(request).get_supervisors_agents_list()
                MetricsRedis.set_data("supervisor_agents_list", response)
            else:
                response = MetricsRedis.get_data("supervisor_agents_list")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorNoPerformingAgentsListView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request):
        update = request.query_params.get("update")
        update = "true"

        try:
            if update == "true" or MetricsRedis.get_data("supervisor_performing_agents_list") is None:
                response = SupervisorAnalytics(request).get_supervisors_no_performing_agents_list()
                MetricsRedis.set_data("supervisor_performing_agents_list", response)
            else:
                response = MetricsRedis.get_data("supervisor_performing_agents_list")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorAbsentAgentsListView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request):
        update = request.query_params.get("update")
        update = "true"

        try:
            if update == "true" or MetricsRedis.get_data("supervisor_absent_agents_list") is None:
                response = SupervisorAnalytics(request).get_supervisors_absent_agents_list()
                MetricsRedis.set_data("supervisor_absent_agents_list", response)
            else:
                response = MetricsRedis.get_data("supervisor_absent_agents_list")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorLocationDetailView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request, id):
        update = request.query_params.get("update")
        update = "true"

        try:
            if update == "true" or MetricsRedis.get_data("supervisor_location_detail") is None:
                response = SupervisorAnalytics(request).get_location_details(id=id)
                MetricsRedis.set_data("supervisor_location_detail", response)
            else:
                response = MetricsRedis.get_data("supervisor_location_detail")
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LocationAgentsListView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request, id):
        try:
            response = SupervisorAnalytics(request).get_location_agents_list(id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LocationNoPerformingAgentsListView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request, id):
        try:
            response = SupervisorAnalytics(request).get_location_no_performing_agents_list(id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LocationAbsentAgentsListView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request, id):
        try:
            response = SupervisorAnalytics(request).get_location_absent_agents_list(id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LocationRemittanceListView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request, id):
        try:
            response = SupervisorAnalytics(request).get_location_remittance_list(id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SingleAgentDetailView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request, id):
        try:
            response = Agents(request).get_single_agent_details(id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SingleAgentGamePlayDetailView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request, id):
        try:
            response = Agents(request).get_game_play_details(id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SingleAgentRemittanceDetailsView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request, id):
        try:
            response = Agents(request).get_remittance_details(request, id=id)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class OverallAgentsPerformanceView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request):
        try:
            response = Agents(request).get_agents_performance()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LottoAgentsTransactionsAnalyticsView(APIView):
    # permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = LottoAgentsTransactionsAnalytics().get_agent_transaction_details(request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class UserDetailsView(APIView):
    # permission_classes = [IsAuthenticated, SupervisorPermission]
    permission_classes = [IsAuthenticated, SupervisorAgentPermission]

    def get(self, request):
        try:
            response = SupervisorAnalytics(request).get_supervisor_user_role(request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LottoTicketDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_lotto_ticket_table(request=request)
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LotteryModelTicketDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_lottery_model_table()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AwoofGameTableDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_awoof_game_table_model_table()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SoccerPredictionDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_soccer_prediction_model_table()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LottoWinnersDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_lotto_winners_model_table()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class LotteryWinnersDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_lottery_winners_model_table()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AwoofDrawTableDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_awoof_draw_table_model_table()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SoccerCashWinnersDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_soccer_winners_model_table()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AgentFundingTableDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_agent_fundingtable_model()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class PaymentTransactionTableDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_payment_transaction_model()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class FailedRemittanceAgencyWalletChargeDataAnalyticsDataExposureView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_failed_remittance_agency_wallet_charge_table_model()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SalesTargetGraphView(APIView):
    def get(self, request):
        try:
            response = DataAnalyticsDataExposure.get_failed_remittance_agency_wallet_charge_table_model()
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class SupervisorTerminalRetrievalView(APIView):
    permission_classes = [SupervisorAgentPermission]

    def post(self, request):
        serializer = TerminalRetrievalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer_data = serializer.validated_data
        retrieved = serializer_data.get("retrieved")
        agent_id = serializer_data.get("agent_id")
        supervisor_id = request.user.id

        try:
            supervisor = Supervisor.objects.get(user_id=supervisor_id)
            supervisor_name = f"{supervisor.first_name} {supervisor.last_name}"
            supervisor_phone = supervisor.phone
            supervisor_email = supervisor.email
        except Supervisor.DoesNotExist:
            return Response(
                {
                    "error": "Supervisor does not exist!"
                }, status=status.HTTP_404_NOT_FOUND
            )

        try:
            agent = Agent.objects.get(pk=agent_id)
            agent_name = f"{agent.first_name} {agent.last_name}"
            agent_phone = agent.phone
            agent_email = agent.email
            terminal_id = agent.terminal_id
            agent.terminal_retrieved = retrieved
            agent.performance_status = "INACTIVE" if retrieved == "True" else agent.performance_status
            agent.save()
        except Agent.DoesNotExist:
            return Response(
                {
                    "error": "Agent does not exist!"
                }, status=status.HTTP_404_NOT_FOUND
            )
        
        
        terminal_retrieval = SupervisorTerminalRetrieval.objects.create(
                retrieved=retrieved,
                supervisor_name=supervisor_name,
                supervisor_phone=supervisor_phone,
                supervisor_email=supervisor_email,
                terminal_id=terminal_id,
                agent_name=agent_name,
                agent_phone=agent_phone,
                agent_email=agent_email,
        )

        agent.terminal_retrieved = True
        agent.save()
        unsuspend_account_on_agency_banking(user_id=agent.user_id)

        serialized_retrieval = SupervisorTerminalRetrievalSerializer(terminal_retrieval)
        return Response(
            {
                "details": serialized_retrieval.data,
            }, status=status.HTTP_200_OK
        )