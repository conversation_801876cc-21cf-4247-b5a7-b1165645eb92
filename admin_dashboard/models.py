from django.db import models


class ConstantTable(models.Model):
    sales_target = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    winwise_agents_salary_amount = models.FloatField(default=30000.00, help_text="This represents the monthly salary amount for Winwise Staff agents")
    postback_count = models.IntegerField(default=0)
    postback_skip_count = models.IntegerField(default=20)
    postback_to_send = models.IntegerField(default=15)
    postback_to_skip = models.IntegerField(default=5)
    

    @classmethod
    def get_sales_target(cls):
        sales_target = cls.objects.last()
        return sales_target
