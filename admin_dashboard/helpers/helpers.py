import calendar
import os
import json
from datetime import date, timedelta
from string import Template

import requests
from django.conf import settings
from django.core.paginator import Paginator as django_core_paginator
from django.utils import timezone
from decouple import config

def date_utility(datetime):
    start_of_all_agents = datetime(2021, 1, 1)

    previous_day = timezone.now() - timedelta(days=1) #previousday

    date_today = timezone.now() #currentday

    if date_today.month > 1:
        previous_month_start = datetime(date_today.year, date_today.month-1, 1) #previousmonth
    else:
        previous_month_start = datetime(date_today.year, 12, 1) #previousmonthbeginning
    current_month_start = datetime(date_today.year, date_today.month, 1) #argument
    previous_month_end = current_month_start + timedelta(days=-1) #previousmonthend

    current_date = date.today() #currentdate
    month_start = datetime(current_date.year, current_date.month, 1)  #currentmonthbeginning
    year_start = datetime(current_date.year, 1, 1) #currentyearbeginning
    year_end = datetime(current_date.year, 12, 31) #currentyearending

    week_start = date_today - timedelta(days=current_date.weekday())
    previous_week_end = week_start - timedelta(days=1)
    previous_week_start = previous_week_end - timedelta(days=previous_week_end.weekday())

    previous_year_start = datetime(date_today.year-1, 1, 1) #previousyearbeginning
    previous_year_end = datetime(date_today.year-1, 12, 31) #previousyearending

    first_day, last_day = calendar.monthrange(date_today.year - 1, date_today.month)
    previous_year_current_month_start = datetime(date_today.year - 1, current_date.month, 1)
    previous_year_current_month_end = datetime(date_today.year - 1, current_date.month, last_day)
    previous_year_current_following_month = current_date.month + 1
    date_from = datetime.now() - timedelta(days=1)
    month_ago = timezone.now() - timedelta(days=30)
    datetime_today_6am = datetime(date_today.year, date_today.month, date_today.day, 6, 0)
    previous_day_six_am = datetime_today_6am - timedelta(days=1)
    date_today_date = datetime.now().date()

    data = {
        "start_of_all_agents": start_of_all_agents,
        "start_of_all_transactions": start_of_all_agents,
        "previous_day": previous_day,
        "today": date_today + timedelta(days=1),
        "previous_month_end": previous_month_end,
        "previous_month_start": previous_month_start,
        "init_start": start_of_all_agents,
        "month_start": month_start,
        "year_start": year_start,
        "year_end": year_end,
        "week_start": week_start.date(),
        "previous_week_start": previous_week_start.date(),
        "previous_week_end": previous_week_end.date(),
        "previous_year_start": previous_year_start,
        "previous_year_end": previous_year_end,
        "previous_year_current_month_start": previous_year_current_month_start,
        "previous_year_current_month_end": previous_year_current_month_end,
        "previous_year_current_following_month": previous_year_current_following_month,
        "date_from": date_from,
        "month_ago": month_ago,
        "date_today": date_today,
        "previous_day_six_am": previous_day_six_am,
        "datetime_today_6am": datetime_today_6am,
        "date_today_date": date_today_date,
    }

    return data


def get_percentage_change(previous_amount, current_amount):
    try:
        difference = abs(current_amount - previous_amount)

        percentage = (difference / max(previous_amount, current_amount)) * 100

        if current_amount > previous_amount:
            change = "up"
        elif current_amount == previous_amount:
            change = "No change"
        else:
            change = "down"

        return {"percentage": percentage, "change": change}
    except Exception:
        return {"percentage": 0, "change": "No change"}


class Paginator:
    @staticmethod
    def paginate(request, queryset, page):
        request_get_data = request.GET
        if page is None or page == "":
            page = 1
        elif int(page) < 0:
            page = 1
        else:
            page = page

        paginator = django_core_paginator(queryset, int(request_get_data.get("size", 30)))
        requested_page = int(request_get_data.get("page", page))

        verified_page = requested_page if requested_page < paginator.num_pages else paginator.num_pages

        page = paginator.page(verified_page)

        return page


def send_rtp_sales_winnings_email(
    message,
    file,
    file_name,
    email_subject,
    email,
    total_ticket_sales_amount,
    total_winnings_amount,
    total_payout_amount,
    total_rtp_amount,
    total_payout_amount_today_winnings,
    total_payout_amount_yesterday_winnings,
    telco_payouts,
    other_payouts,
    due_remittance_since_14_days,
    telco_total_winnings_amount,
    other_total_winnings_amount,
    total_winnings_amount_this_month,
    telco_total_winnings_amount_this_month,
    other_total_winnings_amount_this_month,
):
    """For sending template emails"""

    template_dir = os.path.join(settings.BASE_DIR, "../templates/sales_winnings_rtp_tracking.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
        message=message,
        # email_subject=email_subject,
        file=file,
        file_name=file_name,
        email=email,
        total_ticket_sales_amount=total_ticket_sales_amount,
        total_winnings_amount=total_winnings_amount,
        total_payout_amount=total_payout_amount,
        total_rtp_amount=total_rtp_amount,
        total_payout_amount_today_winnings=total_payout_amount_today_winnings,
        total_payout_amount_yesterday_winnings=total_payout_amount_yesterday_winnings,
        telco_payouts=telco_payouts,
        other_payouts=other_payouts,
        due_remittance_since_14_days=due_remittance_since_14_days,
        telco_total_winnings_amount=telco_total_winnings_amount,
        other_total_winnings_amount=other_total_winnings_amount,
        total_winnings_amount_this_month=total_winnings_amount_this_month,
        telco_total_winnings_amount_this_month=telco_total_winnings_amount_this_month,
        other_total_winnings_amount_this_month=other_total_winnings_amount_this_month,
    )

    data = {"from": "Wyse Cash <<EMAIL>>", "to": email, "subject": email_subject, "html": template}
    message = requests.post(
        "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
        auth=("api", f"{settings.MAILGUN_API_KEY}"),
        data=data,
        files=[("attachment", (f"{file_name}", file))],
    )

    return message.text


class MetricsRedis:
    import redis

    # import json

    redis_client = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0)

    @classmethod
    def set_data(cls, key, data, timeout=108000):
        data = json.dumps(data)
        cls.redis_client.set(key, data, ex=timeout)

    @classmethod
    def get_data(cls, key):
        import ast

        data = cls.redis_client.get(key)

        if isinstance(data, bytes) or isinstance(data, dict):
            try:
                return json.loads(data)
            except Exception:
                try:
                    return ast.literal_eval(data)
                except Exception:
                    return None
        else:
            return None

    @classmethod
    def delete_data(cls, key):
        """
        Delete data from Redis.
        """
        cls.redis_client.delete(key)

    @classmethod
    def clear_data(cls):
        """
        Clear data from Redis.
        """
        cls.redis_client.flushdb()
