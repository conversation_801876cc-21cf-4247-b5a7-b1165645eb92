import datetime
import itertools
import random
from pprint import pprint

# from .utils import get_banker_potential_winning, get_potential_winning, search_number_occurences
from overide_print import print

# import math
# import functools
# import time
# from dataclasses import dataclass
# from functools import reduce

# import pandas as pd

# from main.models import ConstantVariable


class ConstantVariable:
    """
    Defines a class ConstantVariable with class attributes:
    restrict_s4l_to_lower_wins, max_s4l_single_win_to_rtp,
    and s4l_number_of_match_limit.
    """

    restrict_s4l_to_lower_wins = False
    max_s4l_single_win_to_rtp = 4
    s4l_number_of_match_limit = 2

    @classmethod
    @property
    def objects(cls):
        return cls

    @classmethod
    def all(cls):
        return cls

    @classmethod
    def last(cls):
        return cls


def get_potential_winning(data, prices, jackpot):
    """
    data[1][0] : number_of_lines
    data[0] : number_of_matches
    """

    base_price = prices[data[1][0]]
    sharing = {5: 1, 4: 1, 3: 0.6, 2: 0.6 * 0.5}

    if data[0] == 5:
        winning = jackpot

    else:
        winning = (
            (base_price * sharing[data[0]])
            # (base_price / data[1][0] * sharing[data[0]])
            if not sharing[data[0]] == 5
            else jackpot
        )

    response = [*data, int(winning)]

    return response


def search_number_occurences(numlist1: list, numlist2: list, number_of_match_limit=3) -> int:
    """COUNT COMMON NUMBERS"""
    match_count = 0
    # number_of_match_limit = 2 # random.choice([2, 1, 2, 1, 2, 1, 2])

    for number in numlist1:
        if number in numlist2[1]:
            match_count += 1

    return match_count if match_count > (number_of_match_limit - 1) else False


class SalaryForLifeDraw:
    @staticmethod
    def draw(plays, rtp, line_prices, jackpot_amount, disburse_jackpot=False) -> dict:
        then2 = datetime.datetime.now()
        random_combo = list(itertools.combinations(range(1, 50), 5))
        random.shuffle(random_combo)
        const_obj = ConstantVariable.objects.all().last()
        best_match = 0
        best_winners = 0
        best_match_combo = []

        restrict_or_not = const_obj.restrict_s4l_to_lower_wins
        max_win_lim_ratio = const_obj.max_s4l_single_win_to_rtp

        best_match_with_jkpt = 0
        best_match_with_jkpt_combo = []

        best_match_witho_jkpt = 0
        best_match_witho_jkpt_combo = []

        for index, combo in enumerate(random_combo):
            occurences = map(
                lambda user_play: search_number_occurences(combo, user_play, const_obj.s4l_number_of_match_limit), plays
            )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS
            # print(list(occurences))
            play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
            over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

            # NOW FILTER HAS BEEN ALTERED TO ALLOW 2 AND ABOVE WIN COMBOs

            play_occurences_with_amount = map(
                lambda played: get_potential_winning(played, line_prices, jackpot_amount),
                over3ocurrences,
            )

            total_sum = 0
            play_occurences_with_amount = list(play_occurences_with_amount)
            # CALCULATE THE TOTAL WINNING AMOUNT
            for index, ocurrence in enumerate(play_occurences_with_amount):
                total_sum += ocurrence[-1]

                if rtp / ocurrence[-1] < max_win_lim_ratio:
                    total_sum = 99999999999
                    break

            if total_sum >= 99999999999:
                continue

            has_jkpt = bool(list(filter(lambda x: x[0] == 5, play_occurences_with_amount)))
            match = total_sum / rtp * 100
            winners = len(over3ocurrences)

            if (winners >= best_winners or match > best_match) and match < 100:
                if winners == best_winners and match < best_match and restrict_or_not:
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo

                elif winners > best_winners:
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo

                elif best_winners == 0:
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo

                if match > best_match and winners >= best_winners:
                    best_match = match
                    best_winners = winners
                    best_match_combo = combo

            if match > best_match_with_jkpt and match < 20 and has_jkpt:
                best_match_with_jkpt = match
                best_match_with_jkpt_combo = combo
            if match > best_match_witho_jkpt and match < 20 and (not has_jkpt):
                best_match_witho_jkpt = match
                best_match_witho_jkpt_combo = combo

        now2 = datetime.datetime.now()
        tt2 = (now2 - then2).total_seconds()

        print("!!!!!!!!!!!..FULLY DONE..!!!!!!!!!!!")
        print(tt2)

        return dict(
            best_match=best_match,
            best_match_combo=best_match_combo,
            best_match_with_jkpt=best_match_with_jkpt,
            best_match_with_jkpt_combo=best_match_with_jkpt_combo,
            best_match_witho_jkpt=best_match_witho_jkpt,
            best_match_witho_jkpt_combo=best_match_witho_jkpt_combo,
        )

    @staticmethod
    def filter_winnings(combo, plays, prices, jackpot_amount):
        ConstantVariable.objects.all().last()
        occurences = map(
            lambda user_play: search_number_occurences(combo, user_play, 3), plays
        )  # CHECK FOR HOW MANY NUMBERS MATCH FOR EVERY COMBINATION IN LIST OF NUMBERS

        play_occurences = zip(occurences, plays)  # Match number of occurences to number of matches found in selected combination
        over3ocurrences = list(filter(lambda x: x[0], play_occurences))  # FILTER ALL VALUES THAT ARE NOT 3 AND ABOVE (FILTER WITH FALSE)

        play_occurences_with_amount = map(
            lambda played: get_potential_winning(played, prices, jackpot_amount),
            over3ocurrences,
        )
        data = list(play_occurences_with_amount)

        return data


prices = {
    1: 5000.00 / 1 * 0.2,
    2: 15000.00 / 2 * 0.2,
    3: 50000.00 / 3 * 0.2,
    4: 150000.00 / 4 * 0.2,
    5: 250000.00 / 5 * 0.2,
    6: 500000.00 / 6 * 0.2,
    7: 750000.00 / 7 * 0.2,
    8: 900000.00 / 8 * 0.2,
    9: 1250000.00 / 9 * 0.2,
    10: 1500000.00 / 10 * 0.2,
}

plays = [
    [2, [3, 22, 1, 4, 15]],
    [2, [3, 13, 1, 4, 5]],
    [2, [3, 40, 1, 38, 5]],
    [2, [3, 20, 1, 4, 5]],
    [2, [3, 2, 12, 4, 5]],
    [2, [3, 2, 12, 4, 5]],
    [2, [3, 2, 12, 34, 5]],
    [2, [3, 2, 12, 4, 25]],
] * 1200

then = datetime.datetime.now()
x = SalaryForLifeDraw.draw(plays, 20000, prices, 20000)
pprint(x)
y = SalaryForLifeDraw.filter_winnings(x["best_match_combo"], plays, prices, 320000000)
print(y)

now = datetime.datetime.now()
print((now - then).total_seconds())
