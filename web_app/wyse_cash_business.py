from datetime import timed<PERSON><PERSON>

from django.utils import timezone
from rest_framework import status
from rest_framework.generics import ListAPIView
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework.views import APIView

from main.api.serializer import UserProfileSerializer
from main.models import LotteryModel, LotteryWinnersTable, UserProfile
from web_app.serializers import (
    LotteryWinnersTableWithMaskNumberSerializer,
    WyseCashBusinessGamePlaySerializer,
)


class CustomPageNumberPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = "page_size"  # Allow clients to override page size
    max_page_size = 100  # Prevents excessive page sizes


class GenerateWyseCashBusinessTicketApiView(APIView):
    def get(self, request):
        tickets = LotteryModel.generate_wyse_cash_business()
        return Response(data=tickets, status=status.HTTP_200_OK)


class WyseCashBusinessGetUserDetailsApiView(APIView):
    def get(self, request, phone_number):
        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)
        try:
            user_profile = UserProfile.objects.get(phone_number=phone_number)
        except Exception:
            return Response(data={"message": "User does not exist"}, status=status.HTTP_404_NOT_FOUND)

        serialized_data = UserProfileSerializer(user_profile).data

        return Response(serialized_data, status=status.HTTP_200_OK)


class WyseCashBusinessGamePlayApiView(APIView):
    serializer_class = WyseCashBusinessGamePlaySerializer

    def post(self, request):
        # print("YYYY")

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        # print("QQQQ")

        data = LotteryModel.create_lottery_ticket_for_wyse_cash_businness(**serializer.validated_data)

        return Response(data, status=status.HTTP_200_OK)


class WyseCashBuinessGameLeaderBoardApiView(ListAPIView):
    serializer_class = LotteryWinnersTableWithMaskNumberSerializer
    pagination_class = CustomPageNumberPagination

    def get(self, request):
        filter_this_week = request.GET.get("filter_this_week", False)

        if str(filter_this_week).lower() == "true":
            start_of_week = timezone.now() - timedelta(days=timezone.now().weekday())  # Start of the week (Monday)
            end_of_week = start_of_week + timedelta(days=7)  # End of the week (next Monday)
            queryset = LotteryWinnersTable.objects.filter(date_won__range=(start_of_week, end_of_week)).order_by("-date_won")
        else:
            queryset = LotteryWinnersTable.objects.all().order_by("-date_won")

        paginated_data = self.paginate_queryset(queryset)  # Use pagination
        serialized_data = self.get_serializer(paginated_data, many=True).data  # Serialize data

        return self.get_paginated_response(serialized_data)  # Return paginated response


class GetWyseCashBusinessDrawCountDownApiView(APIView):
    def get(self, request):
        data = LotteryModel.get_wyse_cash_business_draw_count_down()
        return Response(data, status=status.HTTP_200_OK)
        





        




