from django.urls import include, path

from web_app.wyse_cash_business import (
    GenerateWyseCashBusinessTicketApiView,
    WyseCashBuinessGameLeaderBoardApiView,
    WyseCashBusinessGamePlayApiView,
    WyseCashBusinessGetUserDetailsApiView,
)

from .views import (
    ActivateUsersPhoneNumberView,
    ActivateUserView,
    ActiveGamesApiView,
    ApiConstant,
    BusinessActiveTickets,
    BusinessDashboardDetails,
    BusinessGameHistory,
    BusinessTopWinners,
    BusinessWalletBalance,
    ChangePasswordView,
    CreateUserView,
    DashboardAnalyticsView,
    EmailLoginView,
    FundWalletFundWalletView,
    GamePlayStatistics,
    GetInstantCashOutResultAPIView,
    GetSalaryForLiveResultAPIView,
    GoogleConnectAPIView,
    IndividualGamePlayStatistics,
    InstantCashoutGameResult,
    JackpotApiView,
    LoginView,
    LotteryGameResultApiView,
    LotteryHistoryView,
    LotteryModelGamePlayStatistics,
    LotteryTopJackpotWinner,
    LotteryWinnersApiView,
    NewsletterAPIView,
    NewsletterUnsubcribeAPIView,
    PayForLottoTicket,
    PhoneLoginView,
    PhoneNumberActivationSMS,
    PhoneNumberOnboardingResendOtp,
    PhoneNumberOnboardingView,
    PhoneNumberOtpVerificationView,
    QuikaMultiplePlay,
    RegisterLottery,
    ResendActivationCodeView,
    ResetPasswordConfirmView,
    ResetPasswordView,
    SalaryForLifeWyseCashJackpotApiView,
    TopFiveWinners,
    UpdatePhoneNumberView,
    UserApiview,
    UserGameHistoryView,
    UserReferralDetails,
    UserReferralsView,
    VerifyBankTransferWalletFunding,
    VerifyPaystackPayment,
    VerifyPhoneAndCreatePassword,
    VerifyPhoneNumberOtp,
    VerifyUssdWalletFunding,
    WebSalaryForLifeNumberPick,
    WebUserTestimonalView,
    WyseCashGameApiView,
)

PHONE_NUMBER_ONBOARDING = [
    path("onboard_via_phone/", PhoneNumberOnboardingView.as_view()),
    path("verify_phone_number_onboarding/", VerifyPhoneAndCreatePassword.as_view()),
    path("update_phone_number/", UpdatePhoneNumberView.as_view()),
    path("verify_phone_number_update/", PhoneNumberOtpVerificationView.as_view()),
    path("phone_number_onboarding_resend_otp/", PhoneNumberOnboardingResendOtp.as_view()),
    path("phone_number_activation_sms/", PhoneNumberActivationSMS.as_view()),
    path("activate_phone_number/", ActivateUsersPhoneNumberView.as_view()),
    path("verify_phone_number_otp/", VerifyPhoneNumberOtp.as_view()),
]

NEWSLETTER = [
    path("newsletter/", NewsletterAPIView.as_view()),
    path(
        "newsletter/<str:ref>/",
        NewsletterUnsubcribeAPIView.as_view(),
        name="unsubscribe_newsletter",
    ),
]

DASHBOARDANALYTICS = [path("dashboard_analytics", DashboardAnalyticsView.as_view())]

WALLET_URLS = [
    path("fund_wallet/", FundWalletFundWalletView.as_view()),
    path("transaction/", include("wallet_app.urls")),
]

REFERRAL_SYSTEM_URL = [
    path("user_testimonial/", WebUserTestimonalView.as_view()),
    path("user_referral_details/", UserReferralDetails.as_view()),
    path("user_referrals/", UserReferralsView.as_view()),
]

WALLET_URL = [
    path("lottery_payment/", PayForLottoTicket.as_view()),
    path("fund_wallet/", FundWalletFundWalletView.as_view()),
    path("transaction/", include("wallet_app.urls")),
    path("verify_bank_transfer_funding/", VerifyBankTransferWalletFunding.as_view()),
    path("verify_paystack_payment/", VerifyPaystackPayment.as_view()),
    path("verify_ussd_wallet_funding/", VerifyUssdWalletFunding.as_view()),
]

# WIN_WISE_EMPLOYEE_TABLE_URL = [
#     path("wisewinagentform/", WinWiseEmployeeView.as_view()),
# ]

WYSE_CASH_BUSINESS_DASHBOARD_URL = [
    path("business_dashboard_details/", BusinessDashboardDetails.as_view(), name="business_dashboard_details"),
    path("business_game_history/", BusinessGameHistory.as_view(), name="business_game_history"),
    path("business_top_winners/", BusinessTopWinners.as_view(), name="business_top_winners"),
    path("business_active_tickets/", BusinessActiveTickets.as_view(), name="business_active_tickets"),
    path("business_wallet_balance/", BusinessWalletBalance.as_view(), name="business_wallet_balance"),
]


WYSE_CASH_BUSINESS_URLS = [
    path("wyse_cash_business_generate_ticket/", GenerateWyseCashBusinessTicketApiView.as_view()),
    path("wyse_cash_business_get_user_details/<str:phone_number>/", WyseCashBusinessGetUserDetailsApiView.as_view()),
    path("wyse_cash_business_game_play/", WyseCashBusinessGamePlayApiView.as_view()),
    path("wyse_cash_business_leader_board/", WyseCashBuinessGameLeaderBoardApiView.as_view()),
]

urlpatterns = [
    path("login/", LoginView.as_view()),
    path("email-login/", EmailLoginView.as_view(), name="email-login"),
    path("phone-login/", PhoneLoginView.as_view()),
    path("create-account/", CreateUserView.as_view()),
    path("google/", GoogleConnectAPIView.as_view()),
    path("activate_user/", ActivateUserView.as_view()),
    path("change_password/", ChangePasswordView.as_view()),
    path("password_reset/", ResetPasswordView.as_view()),
    path("password_reset/confirm/", ResetPasswordConfirmView.as_view()),
    path("user/", UserApiview.as_view()),
    path("dynamic_data/", ApiConstant.as_view()),
    path("resend_activation_code/", ResendActivationCodeView.as_view()),
    path("top_five_winners/", TopFiveWinners.as_view()),
    path("jackpot/", JackpotApiView.as_view()),
    path("active_games/", ActiveGamesApiView.as_view()),
    path("lottery_games_result/", LotteryGameResultApiView.as_view()),
    path("game_play_statistics/", GamePlayStatistics.as_view()),
    path("individual_game_play_statistics/", IndividualGamePlayStatistics.as_view()),
    path("lottery_winners/", LotteryWinnersApiView.as_view()),
    path("lottery_jackpot/", LotteryTopJackpotWinner.as_view()),
    path("wyse_cash_statistics/", LotteryModelGamePlayStatistics.as_view()),
    path("play_lottery/", RegisterLottery.as_view()),
    # path("quika_lottery_play/", QuikaLottoView.as_view()),
    path("quika/multiple/", QuikaMultiplePlay.as_view()),  # quika multiple
    path("lottery_history/", LotteryHistoryView.as_view()),
    path("salary_for_life_game_result/", WebSalaryForLifeNumberPick.as_view()),
    path("salary_wyse_jackpot_winners/", SalaryForLifeWyseCashJackpotApiView.as_view()),
    path("instant_cashout_game_result/", InstantCashoutGameResult.as_view()),
    path("generate_wyse_cash_game/", WyseCashGameApiView.as_view()),
    path("game_history/", UserGameHistoryView.as_view()),
    path("instant_cash_out_result/", GetInstantCashOutResultAPIView.as_view()),
    path("salary_for_live_result/", GetSalaryForLiveResultAPIView.as_view()),
    # ----------- PHONE NUMBER ONBOARDING URLS -------------
    *PHONE_NUMBER_ONBOARDING,
    # ------- NEWSLETTER SUBSCRIBE & UNSUBSCRIBE URLS -------
    *NEWSLETTER,
    *DASHBOARDANALYTICS,
    *WALLET_URL,
    *WYSE_CASH_BUSINESS_URLS,
    *WYSE_CASH_BUSINESS_DASHBOARD_URL,
    # *WIN_WISE_EMPLOYEE_TABLE_URL
]
