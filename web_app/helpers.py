import json
import os

import requests
from django.conf import settings
from django.db.models import Sum

from main.models import LotteryModel, LottoTicket
from main.tasks import (
    celery_send_whatsapp_payment_notification_admin,
    celery_send_whatsapp_payment_notification_admin_for_soccer_payment,
)


def sort_winners_serialized_data(list_of_winners_1, list_of_winners_2):
    """
    This helper function sorts the two list

    Args: list_of_winners_1, list_of_winners_2
    """

    winners = list_of_winners_1 + list_of_winners_2

    winners = sorted(winners, key=lambda x: x["earning"], reverse=True)

    return winners[:5]


def jackpot_data_restructure(data):
    """
    This helper function restructures the data

    Args: data

    Sample data:
            [
                {
                    "phone_number": "23414*****536",
                    "earning": "N2,436",
                    "total_jackpot_amount": "N9,746",
                    "unnique_id": null,
                    "win_type": "SUPER_WINNER",
                    "pool": "TWO_MILLION",
                    "date_won": "2022-09-14T15:01:57.538792+01:00"
                },
                {
                    "phone_number": "23472*****613",
                    "earning": "N2,416",
                    "total_jackpot_amount": "N9,666",
                    "unnique_id": null,
                    "win_type": "SUPER_WINNER",
                    "pool": "FIVE_HUNDRED_THOUSAND",
                    "date_won": "2022-09-14T15:01:57.525657+01:00"
                },
                {
                    "phone_number": "23482*****099",
                    "earning": "N2,415",
                    "total_jackpot_amount": "N9,660",
                    "game_play_id": "",
                    "win_type": "SUPER_WINNER",
                    "lotto_type": "SALARY_FOR_LIFE",
                    "date_won": "2022-09-14T15:16:31.743905+01:00"
                },
                {
                    "phone_number": "23422*****012",
                    "earning": "N2,377",
                    "total_jackpot_amount": "N19,016",
                    "unnique_id": null,
                    "win_type": "JACKPOT_WINNER",
                    "pool": "TWO_FIFTY_THOUSAND",
                    "date_won": "2022-09-14T15:01:57.502576+01:00"
                },
                {
                    "phone_number": "23442*****277",
                    "earning": "N2,338",
                    "total_jackpot_amount": "N18,708",
                    "unnique_id": null,
                    "win_type": "JACKPOT_WINNER",
                    "pool": "TEN_THOUSAND",
                    "date_won": "2022-09-14T15:01:57.548791+01:00"
                }
            ]


    Output:
        {
            "SUPER_WINNER": [
                {
                    "phone_number": "23414*****536",
                    "earning": "N2,436",
                    "total_jackpot_amount": "N9,746",
                    "unnique_id": null,
                    "win_type": "SUPER_WINNER",
                    "pool": "TWO_MILLION",
                    "date_won": "2022-09-14T15:01:57.538792+01:00"
                },
                {
                    "phone_number": "23472*****613",
                    "earning": "N2,416",
                    "total_jackpot_amount": "N9,666",
                    "unnique_id": null,
                    "win_type": "SUPER_WINNER",
                    "pool": "FIVE_HUNDRED_THOUSAND",
                    "date_won": "2022-09-14T15:01:57.525657+01:00"
                },
                {
                    "phone_number": "23482*****099",
                    "earning": "N2,415",
                    "total_jackpot_amount": "N9,660",
                    "game_play_id": "",
                    "win_type": "SUPER_WINNER",
                    "lotto_type": "SALARY_FOR_LIFE",
                    "date_won": "2022-09-14T15:16:31.743905+01:00"
                }
            ],
            "JACKPOT_WINNER": [
                {
                    "phone_number": "23422*****012",
                    "earning": "N2,377",
                    "total_jackpot_amount": "N19,016",
                    "unnique_id": null,
                    "win_type": "JACKPOT_WINNER",
                    "pool": "TWO_FIFTY_THOUSAND",
                    "date_won": "2022-09-14T15:01:57.502576+01:00"
                },
                {
                    "phone_number": "23442*****277",
                    "earning": "N2,338",
                    "total_jackpot_amount": "N18,708",
                    "unnique_id": null,
                    "win_type": "JACKPOT_WINNER",
                    "pool": "TEN_THOUSAND",
                    "date_won": "2022-09-14T15:01:57.548791+01:00"
                }
            ]
        }

    """

    type_of_wins = {i.get("win_type"): [] for i in data}

    for i in data:
        type_of_wins.get(i.get("win_type")).append(i)

    return type_of_wins


def lottery_model_band_average(qs):
    """
    This helper function calculates the average of the band

    Args: qs
    """

    sum_of_band = 0
    for i in qs:
        sum_of_band += float(i.band)

    return sum_of_band / len(qs)


def system_generated_number_helper(batch_obj):
    """
    This helper function picks the salary for life batch instance and return system generated number in a list
    """
    # print("batch_obj", batch_obj)
    item = []

    # print(batch_obj.split(",")[1:])

    item = batch_obj.split(",")[1:]
    formated_number_pick = []

    for num_pick in item:
        formated_number_pick.append((num_pick.replace("(", "").replace(")", "").replace("'", "")).strip())

    # if item in a list is more than 5, split the list

    # print("formated_number_pick", formated_number_pick)
    item = []
    if len(formated_number_pick) > 5:
        for i in range(0, len(formated_number_pick), 5):
            item.append(formated_number_pick[i : i + 5])
    else:
        return [int(i) for i in formated_number_pick]

    return item


def initiate_admin_whatsapp_notification(game_play_id, amount, paid_via):
    """
    This function initiates the admin notification for the game play
    """
    from wyse_ussd.models import SoccerPrediction

    # lottoticket models
    lotto_instance = LottoTicket.objects.filter(game_play_id=game_play_id).last()
    if lotto_instance:
        celery_send_whatsapp_payment_notification_admin.delay(
            phone_number=lotto_instance.user_profile.phone_number,
            batch_id=lotto_instance.batch.batch_uuid,
            amount=amount,
            paid_via=paid_via,
        )

    else:
        # lottery model
        lottery_instance = LotteryModel.objects.filter(game_play_id=game_play_id).last()
        if lottery_instance:
            celery_send_whatsapp_payment_notification_admin.delay(
                phone_number=lottery_instance.user_profile.phone_number,
                batch_id=lottery_instance.batch.batch_uuid,
                amount=amount,
                paid_via=paid_via,
            )

        else:
            # soccer isntance
            soccer_instance = SoccerPrediction.objects.filter(game_id=game_play_id).last()

            if soccer_instance:
                celery_send_whatsapp_payment_notification_admin_for_soccer_payment.delay(
                    phone_number=soccer_instance.user_profile.phone_number,
                    amount=amount,
                    paid_via=paid_via,
                )

            else:
                data = {
                    "message": "No game play instance found for this game play id",
                }
                return data


def generate_lottery_payment_receipt(game_play_id):
    """
    This function generates the lottery payment receipt
    """
    from wyse_ussd.models import SoccerPrediction

    # lottoticket models
    lotto_qs = LottoTicket.objects.filter(game_play_id=game_play_id, paid=True)
    lotto_instance = lotto_qs.last()
    if lotto_instance:
        data = {
            "game_status": "Accepted",
            "game_type": str(lotto_instance.lottery_type).replace("_", " "),
            "game_id": lotto_instance.game_play_id,
            # "stake_per_line": lotto_instance.expected_amount,
            "stake_per_line": lotto_qs.aggregate(sum=Sum("expected_amount")).get("sum"),
            "total_lines": lotto_qs.count(),
            # "total_stake": lotto_instance.expected_amount * lotto_qs.count(),
            "total_stake": lotto_qs.aggregate(sum=Sum("expected_amount")).get("sum"),
        }

        if lotto_instance.lottery_type == "SALARY_FOR_LIFE":
            data["pontential_winning"] = LottoTicket.salary_for_life_stake_amount_pontential_winning(lotto_qs.count()).get("total_winning_amount")

        else:
            data["pontential_winning"] = LottoTicket.instant_stake_amount_pontential_winning(lotto_qs.count()).get("total_winning_amount")

        return data

    else:
        # lottery model
        lottery_qs = LotteryModel.objects.filter(game_play_id=game_play_id)
        lottery_instance = lottery_qs.last()
        if lottery_instance:
            total_stake = lottery_qs.aggregate(sum=Sum("expected_amount")).get("sum")
            data = {
                "game_status": "Accepted",
                "game_type": str(lottery_instance.lottery_type).replace("_", " "),
                "game_id": lottery_instance.game_play_id,
                "stake_per_line": lottery_instance.stake_amount,
                "total_lines": lottery_qs.count(),
                "total_stake": total_stake,
                "pontential_winning": lottery_instance.band,
            }
            return data

        else:
            # soccer isntance
            soccer_qs = SoccerPrediction.objects.filter(game_id=game_play_id)

            if soccer_qs.exists():
                soccer_instance = soccer_qs.last()
                pontential_winning = soccer_qs.aggregate(sum=Sum("potential_winning")).get("sum")
                total_stake = soccer_qs.aggregate(sum=Sum("stake_amount")).get("sum")

                data = {
                    "game_status": "Accepted",
                    "game_type": "Soccer cash",
                    "game_id": soccer_instance.game_id,
                    "stake_per_line": soccer_instance.stake_amount,
                    "total_predictions": soccer_qs.count(),
                    "total_stake": total_stake,
                    "pontential_winning": pontential_winning,
                }
                return data

            else:
                data = []
                return data


def file_upload_path(instance, filename):
    """
    Set file upload path per user.
    Identifies the file type and stores accordingly.
    """
    # Get file extension and determine type
    filename, ext = os.path.splitext(filename)
    filename = f"{filename}{ext}"

    if ext == ".jpg" or ext == ".jpeg" or ext == ".png" or ext == ".gif":
        return f"files/{instance.user.first_name}_{instance.user.id}/images/{filename}"
    else:
        return f"files/{instance.user.first_name}_{instance.user.id}/documents/{filename}"


def file_upload_fortune_ai_path(instance, filename):
    print(instance)
    """
    Set file upload path per user.
    Identifies the file type and stores accordingly.
    """
    # Get file extension and determine type
    filename, ext = os.path.splitext(filename)
    filename = f"{filename}{ext}"

    if ext == ".jpg" or ext == ".jpeg" or ext == ".png" or ext == ".gif":
        return f"files/fortune_ai/images/{filename}"
    else:
        return f"files/fortune_ai/documents/{filename}"


def verify_bvn_with_uverify(bvn_number):
    url = "http://api.youverify.co/v2/api/addresses/candidates/identity"
    headers = {"token": settings.UVERIFY_TOKEN, "Content-Type": "application/json"}
    payload = json.dumps({"type": "bvn", "idNumber": bvn_number, "subjectConsent": True})
    # retry = Retry(connect=3, backoff_factor=0.5)
    response = requests.request("POST", url=url, headers=headers, data=payload)
    resp = json.loads(response.text)

    return resp
