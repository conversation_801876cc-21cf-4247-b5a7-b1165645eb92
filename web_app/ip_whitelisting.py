from django.conf import settings
from rest_framework import status
from rest_framework.exceptions import APIException

from overide_print import print


def ip_whitelist_middleware():
    def decorator(func):
        def wrapper(request, *args, **kwargs):
            # print(
            #     "request.META['REMOTE_ADDR']: ",
            #     request.META.get("HTTP_ORIGIN", "0.0.0.0"),
            #     "\n\n\n",
            # )

            print("request.headers['Origin']", request.headers.get("Origin"))

            if "HTTP_X_FORWARDED_FOR" in request.META:
                request.META["HTTP_X_PROXY_REMOTE_ADDR"] = request.META["REMOTE_ADDR"]
                parts = request.META["HTTP_X_FORWARDED_FOR"].split(",", 1)
                request.META["REMOTE_ADDR"] = parts[0]
                # print("first if proxy")
                # print(
                #     "request.META['REMOTE_ADDR']: ",
                #     request.META.get("REMOTE_ADDR", "0.0.0.0"),
                #     "\n\n\n",
                # )

            # else:
            #     # print(
            #     #     "request.META['REMOTE_ADDR']: ",
            #     #     request.META.get("REMOTE_ADDR", "0.0.0.0"),
            #     #     "\n\n\n",
            #     # )

            if settings.DEBUG is True or settings.DEBUG:
                return func(request, *args, **kwargs)

            else:
                return func(request, *args, **kwargs)

            # else:
            #     # return func(request, *args, **kwargs) # TEMPORARY ADDED
            #     if settings.WHITELISTING_TO_USE == "DOMAIN_WHITELIST":
            #         domain_name_list = settings.DOMAIN_WHITELIST.split(",")

            #         if request.META.get("HTTP_ORIGIN") in domain_name_list:
            #             return func(request, *args, **kwargs)
            #         else:
            #             raise HttpResponseForbidden

            #     raise HttpResponseForbidden

            #     # if request.META["REMOTE_ADDR"] in settings.IP_WHITELIST:
            #     #     return func(request, *args, **kwargs)
            #     # else:
            #     #     return HttpResponseForbidden

            return func(request, *args, **kwargs)

        return wrapper

    return decorator

    # if request.META.get("REMOTE_ADDR", '0.0.0.0') in settings.IP_WHITELIST:
    #     return func(request, *args, **kwargs)
    # else:
    #     return HttpResponseForbidden()


# HttpResponseForbidden
class HttpResponseForbidden(APIException):
    status_code = status.HTTP_403_FORBIDDEN

    default_detail = {
        "message": "Forbidden access",
    }
    default_code = "Not permitted"
