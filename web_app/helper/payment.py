from main.models import LottoTicket
from pos_app.prices.structure import quika_i_cash_lines_amount
from wallet_app.models import UserWallet
from web_app.helpers import initiate_admin_whatsapp_notification


class WebPayment:
    def __init__(self, player, amount, game_play_id):
        self.game_play_id = game_play_id
        self.amount = amount
        self.player = player

    def referral_wallet(self):
        from wyse_ussd.models import UssdLotteryPayment

        pending_lottery_payment = UssdLotteryPayment.objects.filter(
            game_play_id__isnull=False,
            game_play_id=self.game_play_id,
            user=self.player,
            is_successful=False,
            is_verified=False,
        ).last()

        user_wallet = UserWallet.objects.filter(user=self.player, wallet_tag="WEB").last()
        if user_wallet is None:
            user_wallet = UserWallet.objects.create(user=self.player, wallet_tag="WEB")

        user_wallet.game_available_balance += self.amount
        user_wallet.transaction_from = "REFERRAL_BONUS"
        user_wallet.save()

        from wyse_ussd.tasks import share_ussd_payment_across_lottery_pool

        game_ins_qs = LottoTicket.objects.filter(game_play_id=self.game_play_id)

        if game_ins_qs.count() > 0:
            if game_ins_qs.last().lottery_type == "QUIKA":
                illusion = quika_i_cash_lines_amount[game_ins_qs.count()]["illusion"]
                from main.helpers.illution_feature import GameIllusion

                GameIllusion().set_excess_amount(amount=illusion, game_type="QUIKA")
        else:
            pass

        share_ussd_payment_across_lottery_pool(
            self.player.phone_number,
            int(self.amount),
            self.game_play_id,
            from_web=True,
            transfrom="REFERRAL_BONUS",
        )

        initiate_admin_whatsapp_notification(game_play_id=self.game_play_id, amount=int(self.amount), paid_via="WALLET")

        pending_lottery_payment.is_successful = True
        pending_lottery_payment.is_verified = True
        pending_lottery_payment.save()

    def play_wallet(self, transfrom="PLAY_WALLET"):
        from wyse_ussd.tasks import share_ussd_payment_across_lottery_pool

        game_ins_qs = LottoTicket.objects.only("id").filter(game_play_id=self.game_play_id)
        game_count = game_ins_qs.count()

        if game_count > 0:
            if game_ins_qs.last().lottery_type == "QUIKA":
                illusion = quika_i_cash_lines_amount[game_count]["illusion"]
                from main.helpers.illution_feature import GameIllusion

                GameIllusion().set_excess_amount(amount=illusion, game_type="QUIKA")
        else:
            pass

        share_ussd_payment_across_lottery_pool(
            self.player.phone_number,
            int(self.amount),
            self.game_play_id,
            from_web=True,
            transfrom=transfrom,
        )

        initiate_admin_whatsapp_notification(game_play_id=self.game_play_id, amount=int(self.amount), paid_via="WALLET")
