import uuid
from collections import OrderedDict
from datetime import datetime

from main.api.api_lottery_helpers import generate_game_play_id
from main.models import ConstantVariable, LottoTicket
from main.tasks import lottery_play_engange_event
from pos_app.prices.structure import quika_i_cash_lines_amount
from wyse_ussd.models import UssdLotteryPayment


class QuikaReg:  # multiple handler
    def __init__(
        self,
        lottery_play,
        phone_number,
        stake_winning_amount,
        serialize_ticket,
        lotto_type,
        player,
        channel,
        current_batch,
        agent_instance=None,
    ):
        self.current_batch = current_batch
        self.channel = channel
        self.player = player
        self.agent_instance = agent_instance
        self.lotto_type = lotto_type
        self.serialize_ticket = serialize_ticket
        self.stake_winning_amount = stake_winning_amount
        self.phone_number = phone_number
        self.lottery_play = lottery_play
        self.reg_source = self._game_route()

    def _game_route(self):
        if self.channel == "pos":
            return "POS_AGENT"
        elif self.channel == "WEB":
            return "WEB"
        else:
            return "MOBILE"

    def register(self):
        game_play_ids_generated = []
        game_amount = []

        count = 0
        for game in self.lottery_play:
            stake_amount = game["amount"]
            amount_reg_helper = self.stake_winning_amount(stake_amount)

            ticket_reg_count = amount_reg_helper["reg_count"]
            potential_winning = amount_reg_helper["potential_winning"]

            stake_illusion_amount = quika_i_cash_lines_amount[ticket_reg_count]
            if ConstantVariable.objects.last().game_illusion_feature == "OFF":
                has_illusion = False
                illusion_amount = 0
            else:
                illusion_amount = stake_illusion_amount["illusion"]
                has_illusion = True

            game_play_id = generate_game_play_id()

            identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"

            for ticket_count in range(ticket_reg_count):
                lottery = game["lottery"][0]

                LottoTicket.objects.create(
                    user_profile=self.player,
                    agent_profile=self.agent_instance,
                    batch=self.current_batch,
                    phone=self.phone_number,
                    stake_amount=stake_illusion_amount["reg_amount"] / ticket_reg_count,
                    expected_amount=stake_amount / ticket_reg_count,
                    illusion=illusion_amount / ticket_reg_count,
                    potential_winning=potential_winning,
                    paid=False,
                    number_of_ticket=ticket_reg_count,
                    channel=self.reg_source,
                    game_play_id=game_play_id,
                    lottery_type=self.lotto_type,
                    ticket=self.serialize_ticket(lottery["ticket"]),
                    identity_id=identity_id,
                )
                game_play_ids_generated.append(game_play_id)

                # check if duplicate ticket for POS channel is turned on
                # create_pos_duplicate_lottery = (
                #     ConstantVariable.get_constant_variable().get(
                #         "create_pos_duplicate_lottery"
                #     )
                # )

            game_amount.append(stake_amount)

            UssdLotteryPayment.objects.create(
                user=self.player,
                amount=int(stake_amount),
                game_play_id=game_play_id,
                channel=self.reg_source,
                lottery_type="QUIKA",
                illusion_amount=illusion_amount,
                has_illusion=has_illusion,
            )

            engage_event_payload = {
                "event": "QUIKA LOTTERY PLAY",
                "properties": {
                    "game_id": game_play_id,
                },
            }
            lottery_play_engange_event.delay(
                user_id=self.player.id,
                is_user_profile_id=True,
                **engage_event_payload,
            )

            count += 1
            self.lottery_play[count - 1]["game_play_id"] = game_play_id

        sorted_game_id = list(OrderedDict.fromkeys(game_play_ids_generated))
        reg_result = {"sorted_game_id": sorted_game_id, "game_amount": game_amount}
        return reg_result
