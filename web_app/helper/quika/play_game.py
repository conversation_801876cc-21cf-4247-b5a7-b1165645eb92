from main.models import LotteryBatch, LottoTicket, LottoWinners
from pos_app.prices.structure import quika_pricing_and_reg_count
from web_app.helper.payment import WebPayment
from web_app.helper.quika.reg import QuikaReg


class QuikaWebManager:
    def __init__(
        self,
        lottery_play,
        lotto_type,
        player,
        channel,
        from_play_wallet,
        from_referral_wallet,
        paystack,
        agent_instance,
    ):
        self.lottery_play = lottery_play
        self.from_referral_wallet = from_referral_wallet
        self.paystack = paystack
        self.from_play_wallet = from_play_wallet
        self.lotto_type = lotto_type
        self.agent_instance = agent_instance
        self.channel = channel
        self.player = player

    def fetch_total_stake_amount_from_payload(self) -> int:
        amount = sum(tuple([req["amount"] for req in self.lottery_play]))
        return amount

    @staticmethod
    def _serialize_ticket(number_list):
        return ",".join(map(str, map(int, number_list)))

    def _current_batch(self):
        batch = LotteryBatch.objects.filter(is_active=True, lottery_type="INSTANT_CASHOUT")
        if batch.count() > 0:
            return batch.last()

        else:
            return LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT")

    def stake_winning_amount(
        self,
        stake_amount,
        ticket_count=None,
    ):
        if self.lotto_type == "QUIKA":
            # quika_amount_instance = (
            #     ConstantVariable().quika_admin_stake_and_winning_amount()[
            #         int(ticket_count)
            #     ]
            # )
            #
            # amounts = {
            #     "stake_amount": quika_amount_instance["stake_amount"],
            #     "winning_amount": quika_amount_instance["total_winning_amount"],
            # }
            # print(amounts)

            return quika_pricing_and_reg_count(stake_amount=stake_amount, channel=self.channel)

        elif self.lotto_type == "INSTANT_CASHOUT":
            instant_cash_out_amount_instance = LottoTicket.instant_stake_amount_pontential_winning(ticket_count=ticket_count)
            amounts = {
                "stake_amount": instant_cash_out_amount_instance["stake_amount"],
                "winning_amount": instant_cash_out_amount_instance["total_winning_amount"],
            }
            return amounts

    def registrations(self):
        return QuikaReg(
            lottery_play=self.lottery_play,
            phone_number=self.player.phone_number,
            stake_winning_amount=self.stake_winning_amount,
            serialize_ticket=self._serialize_ticket,
            lotto_type=self.lotto_type,
            player=self.player,
            channel=self.channel,
            current_batch=self._current_batch(),
            agent_instance=self.agent_instance,
        ).register()


class PaymentGameResult:
    def __init__(
        self,
        from_play_wallet,
        from_referral_wallet,
        reg_result,
        lottery_play,
        player,
        lotto_type,
        stake_winning_amount,
    ):
        self.from_play_wallet = from_play_wallet
        self.from_referral_wallet = from_referral_wallet
        self.reg_result = reg_result
        self.lottery_play = lottery_play
        self.player = player
        self.lotto_type = lotto_type
        self.stake_winning_amount = stake_winning_amount

    def payment_result_check(self):
        # reg_result = {"sorted_game_id": [], "game_amount": int} ::: sample dict

        sorted_game_id = self.reg_result["sorted_game_id"]
        game_amount = self.reg_result["game_amount"]

        for game_count in range(len(sorted_game_id)):
            game_id = sorted_game_id[game_count]
            amount = game_amount[game_count]

            print(amount, "AMOUNT +++++++++++++++++++++++++++++++++++>>>")

            # lottery_ticket_qs = LottoTicket.objects.filter(
            #     game_play_id=game_id,
            #     paid=False,
            #     is_duplicate=False,
            # )

            payment = WebPayment(player=self.player, amount=amount, game_play_id=game_id)

            if self.from_play_wallet:
                payment.play_wallet()
            if self.from_referral_wallet:
                payment.referral_wallet()

            lotto_queryset = LottoTicket.objects.filter(
                game_play_id=game_id,
                lottery_type=self.lotto_type,
                paid=True,
            )

            ticket_count = lotto_queryset.count()
            stake_amount = lotto_queryset.last().expected_amount * ticket_count
            # print(stake_amount)
            amounts = self.stake_winning_amount(stake_amount=stake_amount)
            lotto_instance = lotto_queryset.last()

            self.lottery_play[game_count]["ticket_owner"] = lotto_instance.user_profile.phone_number
            self.lottery_play[game_count]["stake_per_pick"] = amounts["main_stake_amount"] / ticket_count
            self.lottery_play[game_count]["total_stake"] = amounts["main_stake_amount"]
            self.lottery_play[game_count]["total_ticket"] = lotto_queryset.count()
            self.lottery_play[game_count]["date"] = lotto_instance.date

            winning_tickets = []
            winning_instance = LottoWinners.objects.filter(
                game_play_id=game_id,
                lottery__user_profile__phone_number=lotto_instance.user_profile.phone_number,
            )
            from django.db.models import Sum

            if winning_instance.exists():
                winning_ins = winning_instance.last()
                if (winning_ins.win_flavour == "WHITE") and (winning_ins.match_type != "PERM_0"):
                    self.lottery_play[game_count]["status"] = "won"
                    self.lottery_play[game_count]["amount_won"] = winning_instance.aggregate(Sum("earning"))["earning__sum"]
                    won_tickets = [ticket.ticket for ticket in winning_instance]
                    for x in won_tickets:
                        winning_tickets.append(x)

                if (winning_ins.win_flavour == "BLACK") or (winning_ins.win_flavour == "CASHBACK"):
                    cash_back_perc = winning_ins.get_cashback_percentage
                    self.lottery_play[game_count]["status"] = f"{cash_back_perc}% cashback"

                    self.lottery_play[game_count]["cashback_amount"] = winning_instance.aggregate(Sum("earning"))["earning__sum"]
                    won_tickets = [ticket.ticket for ticket in winning_instance]
                    for x in won_tickets:
                        winning_tickets.append(x)

            else:
                self.lottery_play[game_count]["status"] = "lost"
                self.lottery_play[game_count]["amount_won"] = 0.0

            lottery = self.lottery_play[game_count]["lottery"]

            for ticket_count in range(len(lottery)):
                ticket = lottery[ticket_count]["ticket"]
                ticket_string = ",".join(str(x) for x in ticket)

                if ticket_string in winning_tickets:
                    lottery[ticket_count]["status"] = "won"

                else:
                    lottery[ticket_count]["status"] = "lost"

            self.lottery_play[game_count]["system_pick"] = (
                [] if lotto_instance.system_generated_num is None else [int(x) for x in lotto_instance.system_generated_num.split(",")]
            )
            self.lottery_play[game_count]["pin"] = lotto_instance.pin if lotto_instance is not None else None

        return {"lottery_play": self.lottery_play}
