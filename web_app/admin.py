from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from web_app.models import Newsletter, UserFileUpload

# WinWiseEmployeeTable


# Register your model(s) here.
class NewsletterResource(resources.ModelResource):
    class Meta:
        model = Newsletter


class UserFileUploadResource(resources.ModelResource):
    class Meta:
        model = UserFileUpload


# class WinWiseEmployeeTableResource(resources.ModelResource):

#     class Meta:
#         model = WinWiseEmployeeTable


class NewsletterResourceAdmin(ImportExportModelAdmin):
    resource_class = NewsletterResource
    search_fields = ["email"]
    list_filter = ("email", "subscribe", "unsubscribe")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserFileUploadResourceAdmin(ImportExportModelAdmin):
    resource_class = UserFileUploadResource
    list_filter = ("date_created",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


# class WinWiseEmployeeTableResourceAdmin(ImportExportModelAdmin):
#     resource_class = WinWiseEmployeeTableResource
#     search_fields = ["mobile_number", "bank_verification_no", "email", "business_address"]
#     list_filter = ("agent_first_name", "mobile_number", "email", "bank_verification_no")

#     def get_list_display(self, request):
#         return ["agent_first_name", "email", "bank_verification_no", "business_address"]

admin.site.register(Newsletter, NewsletterResourceAdmin)
admin.site.register(UserFileUpload, UserFileUploadResourceAdmin)
# admin.site.register(WinWiseEmployeeTable, WinWiseEmployeeTableResourceAdmin)
