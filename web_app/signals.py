# from web_app.models import WinWiseEmployeeTable

# @receiver(post_save, sender=WinWiseEmployeeTable)
# def validate_bvn(sender, instance, created, **kwargs):
#     if created:
#         print("validate_bvn\n\n\n")
# response = verify_bvn_with_uverify(instance.bank_verification_no)
# print(response.json())
# # self.bvn_data = response.text
# if response.get("success") is True and response.get("statusCode") == 201:
#     data = response.get("data")["data"]

#     bvn_phone_number = data.get("mobile")
#     if bvn_phone_number:
#         formatted_bvn_phone_number = User.format_number_from_back_add_234(
#             bvn_phone_number
#         )
#         return {
#             "status": True,
#             "bvn_phone_number": formatted_bvn_phone_number,
#             "data": response
#         }
#     else:
#         return {
#             "status": False,
#             "bvn_phone_number": None,
#             "data": response
#     }
