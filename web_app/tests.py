# Create your tests here.
from urllib.parse import urlencode

from django.test import Client, TestCase
from django.urls import reverse

from main.models import LotteryBatch, LotteryModel, LotteryWinnersTable, UserProfile
from pos_app.models import Agent
from wallet_app.models import UserWallet


class TestDashboard(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = UserProfile.objects.create(phone_number="***********")
        self.agent = Agent.objects.create(first_name="a", last_name="b", phone="***********", email="<EMAIL>", user_id=1)
        self.batch = LotteryBatch.objects.create()
        self.lottery_model = LotteryModel.objects.create(
            user_profile=self.user,
            agent_profile=self.agent,
            batch=self.batch,
            phone="***********",
            pool="TEN_THOUSAND",
            lucky_number="1234567",
            amount_paid=2000,
            game_play_id="7654321",
        )
        self.lottery_winner = LotteryWinnersTable.objects.create(
            lottery=self.lottery_model,
            agent=self.agent,
            batch=self.batch,
            phone_number="***********",
            pool="TEN_THOUSAND",
            win_type="JACKPOT_WINNER",
            earning=5000,
            game_play_id="7654321",
        )
        self.wallet = UserWallet.objects.create(user=self.user, game_available_balance=2000, withdrawable_available_balance=1000)

    def test_get_dashboard_details(self):
        url = reverse("business_dashboard_details")
        query_params = {"user_id": 1}
        url_with_params = f"{url}?{urlencode(query_params)}"
        response = self.client.get(url_with_params)
        self.assertEqual(response.status_code, 200)

    def test_get_game_history(self):
        url = reverse("business_game_history")
        query_params = {"user_id": 1}
        url_with_params = f"{url}?{urlencode(query_params)}"
        response = self.client.get(url_with_params)
        self.assertEqual(response.status_code, 200)

    def test_get_top_winners(self):
        url = reverse("business_top_winners")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_get_my_active_tickets(self):
        url = reverse("business_active_tickets")
        query_params = {"user_id": 1}
        url_with_params = f"{url}?{urlencode(query_params)}"
        response = self.client.get(url_with_params)
        self.assertEqual(response.status_code, 200)

    def test_get_wallet_balances(self):
        url = reverse("business_wallet_balance")
        query_params = {"user_id": 1}
        url_with_params = f"{url}?{urlencode(query_params)}"
        response = self.client.get(url_with_params)
        self.assertEqual(response.status_code, 200)
