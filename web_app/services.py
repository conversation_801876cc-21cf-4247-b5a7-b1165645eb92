from datetime import date, timedelta

from django.db.models import Sum
from django.utils import timezone

from main.models import LotteryModel, LotteryWinnersTable, LottoTicket, UserProfile
from pos_app.models import BoughtLotteryTickets, PosLotteryWinners
from django.utils import timezone
from datetime import timedelta, date
from django.db.models import Sum, Case, When, Value, CharField


from wallet_app.models import UserWallet
from wyse_ussd.models import UssdLotteryPayment


# date analysis
today = timezone.now().date()
yesterday = today - timedelta(days=1)
week_start = today - timedelta(days=today.weekday())
month_start = date(today.year, today.month, 1)
year_start = date(today.year, 1, 1)


class DashboardAnalytics:
    # returns the response for dashboard analytics

    def tickets_analytics():
        # Tickets Bought
        ticket_bought_qs = BoughtLotteryTickets.objects.all()
        ticket_bought_today_qs = ticket_bought_qs.filter(created_at=today, paid=True)
        ticket_bought_yesterday_qs = ticket_bought_qs.filter(created_at=yesterday)
        ticket_bought_week_qs = ticket_bought_qs.filter(created_at__gte=week_start)
        ticket_bought_month_qs = ticket_bought_qs.filter(created_at__gte=month_start)
        ticket_bought_year_qs = ticket_bought_qs.filter(created_at__gte=year_start)

        ticket_bought_today_amt = ticket_bought_today_qs.aggregate(Sum("amount"))["amount__sum"]
        ticket_bought_yesterday_amt = ticket_bought_yesterday_qs.aggregate(Sum("amount"))["amount__sum"]
        ticket_bought_week_amt = ticket_bought_week_qs.aggregate(Sum("amount"))["amount__sum"]
        ticket_bought_month_amt = ticket_bought_month_qs.aggregate(Sum("amount"))["amount__sum"]
        ticket_bought_year_amt = ticket_bought_year_qs.aggregate(Sum("amount"))["amount__sum"]

        # Game Winnings
        ticket_winnings_qs = PosLotteryWinners.objects.all()
        ticket_winnings_today_qs = ticket_winnings_qs.filter(date_created=today)
        ticket_winnings_yesterday_qs = ticket_winnings_qs.filter(date_created=yesterday)
        ticket_winnings_week_qs = ticket_winnings_qs.filter(date_created__gte=week_start)
        ticket_winnings_month_qs = ticket_winnings_qs.filter(date_created__gte=month_start)
        ticket_winnings_year_qs = ticket_winnings_qs.filter(date_created__gte=year_start)

        ticket_winnings_today_amt = ticket_winnings_today_qs.aggregate(Sum("amount_won"))["amount_won__sum"]
        ticket_winnings_yesterday_amt = ticket_winnings_yesterday_qs.aggregate(Sum("amount_won"))["amount_won__sum"]
        ticket_winnings_week_amt = ticket_winnings_week_qs.aggregate(Sum("amount_won"))["amount_won__sum"]
        ticket_winnings_month_amt = ticket_winnings_month_qs.aggregate(Sum("amount_won"))["amount_won__sum"]
        ticket_winnings_year_amt = ticket_winnings_year_qs.aggregate(Sum("amount_won"))["amount_won__sum"]

        queryset = {
            "tickets_sold_amount": {
                "today": ticket_bought_today_amt if ticket_bought_today_amt else 0,
                "yesterday": ticket_bought_yesterday_amt if ticket_bought_yesterday_amt else 0,
                "week": ticket_bought_week_amt if ticket_bought_week_amt else 0,
                "month": ticket_bought_month_amt if ticket_bought_month_amt else 0,
                "year": ticket_bought_year_amt if ticket_bought_year_amt else 0,
            },
            "total_winnings_amount": {
                "today": ticket_winnings_today_amt if ticket_winnings_today_amt else 0,
                "yesterday": ticket_winnings_yesterday_amt if ticket_winnings_yesterday_amt else 0,
                "week": ticket_winnings_week_amt if ticket_winnings_week_amt else 0,
                "month": ticket_winnings_month_amt if ticket_winnings_month_amt else 0,
                "year": ticket_winnings_year_amt if ticket_winnings_year_amt else 0,
            },
        }

        return queryset
    

class BusinessEditionDashBoard:
    def __init__(self, request):
        user_id = request.query_params.get('user_id')
        self.user = UserProfile.objects.filter(id=user_id).first()
        
    def get_dashboard_details(self):
        queryset = UssdLotteryPayment.objects.filter(user=self.user).values(
            "created_at", "game_play_id", "lottery_type", "amount", "is_successful"
        ).annotate(success = Case(
            When(is_successful=True, then=Value('Successful')),
            default=Value('Not Successful'),
            output_field=CharField(),
            )
        )
        return queryset
    
    def get_game_history(self):         
        tickets = LottoTicket.objects.filter(user_profile = self.user).values("updated_at", "game_play_id", "number_of_ticket", "stake_amount", "potential_winning")
        return tickets
    
    def get_top_winners(self):
        queryset = LotteryWinnersTable.objects.all().values('earning', 'agent__email').order_by('-earning')[:5]
        return queryset
    
    def get_my_active_tickets(self):
        active_tickets = LotteryModel.objects.filter(user_profile = self.user).values("amount_paid", "game_play_id")
        return active_tickets
    
    def get_wallet_balances(self):         
        wallet = UserWallet.objects.filter(user = self.user).values("game_available_balance", "withdrawable_available_balance").first()
        print(wallet)
        return wallet
    

