from dataclasses import dataclass

from django.db.models import Sum
from rest_framework import serializers, status
from rest_framework.exceptions import APIException

from account.models import User
from account.validator import validate_password_strength
from banker_lottery.helpers.func import add_1_hour_to_date
from main.helpers.helper_functions import mask_winners_phone_number
from main.models import (
    ConstantVariable,
    JackpotWinner,
    LotteryBatch,
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
)
from main.ussd.helpers import Utility
from pos_app.pos_helpers import machine_number_serializer
from prices.game_price import (
    InstantCashOutPriceModel,
    QuikaPriceModel,
    SalaryForLifePriceModel,
)
from referral_system.models import ReferralTable
from web_app.models import Newsletter
from wyse_ussd.models import SoccerPrediction


class LotteryWinnersTableSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryWinnersTable
        fields = (
            "phone_number",
            "earning",
            "total_jackpot_amount",
            "ticket",
            "unnique_id",
            "win_type",
            "pool",
            "date_won",
            "stake_amount",
        )

    def to_representation(self, obj):
        serialized_data = super(LotteryWinnersTableSerializer, self).to_representation(obj)
        serialized_data["phone_number"] = obj.phone_number
        serialized_data["earning"] = obj.earning
        serialized_data["total_jackpot_amount"] = Utility.currency_formatter(obj.total_jackpot_amount)
        serialized_data["band"] = LotteryModel.band_integer(obj.pool)
        serialized_data["lotto_type"] = "WYSE_CASH"
        return serialized_data


class TopLotteryWinnersTableSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryWinnersTable
        fields = (
            "phone_number",
            "earning",
            "total_jackpot_amount",
            "ticket",
            "unnique_id",
            "win_type",
            "pool",
            "date_won",
        )

    def to_representation(self, obj):
        serialized_data = super(TopLotteryWinnersTableSerializer, self).to_representation(obj)
        serialized_data["phone_number"] = obj.phone_number
        serialized_data["earning"] = obj.earning
        serialized_data["total_jackpot_amount"] = Utility.currency_formatter(obj.total_jackpot_amount)
        serialized_data["band"] = LotteryModel.band_integer(obj.pool)
        serialized_data["win_type"] = "WYSE_CASH"

        # del serialized_data["win_type"]
        return serialized_data


class LottoWinnersSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LottoWinners
        fields = (
            "phone_number",
            "earning",
            "total_jackpot_amount",
            "ticket",
            "game_play_id",
            "win_type",
            # "match_type",
            "lotto_type",
            "date_won",
            "stake_amount",
        )

    def to_representation(self, obj):
        serialized_data = super(LottoWinnersSerializer, self).to_representation(obj)
        serialized_data["phone_number"] = obj.phone_number
        serialized_data["earning"] = obj.earning
        serialized_data["total_jackpot_amount"] = Utility.currency_formatter(obj.total_jackpot_amount)
        serialized_data["ticket"] = obj.ticket.split(",") if obj.ticket else []
        serialized_data["ticket"] = [int(i) for i in serialized_data["ticket"]]
        return serialized_data


class JackpotWinnerSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = JackpotWinner
        fields = ("user", "jackpot", "jackpot", "amount", "created_at")

    def to_representation(self, obj):
        serialized_data = super(JackpotWinnerSerializer, self).to_representation(obj)
        serialized_data["earning"] = "0"
        serialized_data["win_type"] = obj.jackpot.jackpot_type
        serialized_data["phone_number"] = obj.user.phone_number
        serialized_data["total_jackpot_amount"] = Utility.currency_formatter(obj.amount)
        serialized_data["date"] = obj.created_at

        del serialized_data["created_at"]

        return serialized_data


class LotteryModelActiveGamesSerializer(serializers.ModelSerializer):
    class Meta:
        model = LotteryModel
        fields = [
            "user_profile",
            "agent_profile",
            "batch",
            "unique_id",
            "phone",
            "instance_number",
            "pool",
            "band",
            "stake_amount",
            "lucky_number",
            "paid",
            "account_no",
            "has_interest",
            "channel",
            "game_play_id",
            "date",
        ]


class LottoTicketActiveGamesSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LottoTicket
        fields = [
            "user_profile",
            "agent_profile",
            "batch",
            "phone",
            "stake_amount",
            "lottery_type",
            "has_interest",
            "ticket",
            "date",
        ]

    def to_representation(self, obj):
        serialized_data = super(LottoTicketActiveGamesSerializer, self).to_representation(obj)
        serialized_data["ticket"] = obj.ticket.split(",") if obj.ticket else []

        return serialized_data


class LottoApiSerializer(serializers.Serializer):
    ticket = serializers.CharField()


class RegisterOtherLotterySerializer(serializers.Serializer):
    stake_amount = serializers.IntegerField()
    ticket = serializers.ListSerializer(child=LottoApiSerializer())


@dataclass
class WebLotteryPlaySerializer:
    data: dict

    def is_valid(self, lottery_type, raise_exception=False):
        if raise_exception:
            _data_keys = self.data.keys()
            fields = self.serilaizing_fields()

            error_response = {}

            for key in fields.keys():
                if key not in _data_keys:
                    error_response[key] = "This field is required"

                if self.data.get(key) is None or self.data.get(key) == "":
                    error_response[key] = "This field is required"

                if not isinstance(self.data.get("lottery"), list):
                    error_response["lottery"] = "This field is required as list field"

                if isinstance(self.data.get("lottery"), list):
                    for i in self.data.get("lottery"):
                        if i.get("ticket") is None or i.get("ticket") == "":
                            error_response["lottery"] = "a ticket field is required in the lottery field as child field"

            if error_response:
                raise APIException(error_response, code=status.HTTP_400_BAD_REQUEST)

        _validated_data = self.validated_data(lottery_type)
        if _validated_data.get("status") == "error":
            raise APIException(_validated_data.get("message"), code=status.HTTP_400_BAD_REQUEST)

    def serilaizing_fields(self):
        fields = {
            "stake_amount": "",
            "lottery": [{"ticket_number": ""}],
        }

        return fields

    def validated_data(self, lottery_type):
        if lottery_type == "SALARY_FOR_LIFE":
            if len(self.data.get("lottery")) > 10:
                return {
                    "status": "error",
                    "message": "You can only select 10 tickets for salary for life lottery",
                }

            for i in self.data.get("lottery"):
                if len(i.get("ticket")) != 5:
                    return {
                        "status": "error",
                        "message": "ticket field must be 5 characters long",
                    }

                if not i.get("ticket"):
                    return {"status": "error", "message": "ticket field is required"}

                for j in i.get("ticket"):
                    if not isinstance(j, int):
                        return {
                            "status": "error",
                            "message": f"please change this `{j}` to a digit",
                        }
                    elif isinstance(j, int) and int(j) > 49:
                        return {
                            "status": "error",
                            "message": f"please change this `{j}` to a digit less than 50",
                        }

            # check for stake amount
            amount = int(self.data.get("stake_amount"))
            if amount is None:
                return {"status": "error", "message": "stake amount is required"}

            # if (
            #         LottoTicket.salary_for_life_stake_amount_pontential_winning_for_lotto_agents(
            #             len(self.data.get("lottery"))
            #         ).get("stake_amount")
            #         != amount
            # ):
            #     return {"status": "error", "message": "stake amount is invalid"}

            sal4_life_price_model = SalaryForLifePriceModel()

            valid_amount = sal4_life_price_model.get_ticket_price_details_with_stake_amount(channel="WEB", stake_amount=amount)

            if valid_amount is None:
                return {"status": "error", "message": "stake amount is invalid"}

        elif lottery_type == "INSTANT_CASHOUT":
            if len(self.data.get("lottery")) > 7:
                return {
                    "status": "error",
                    "message": "You can only select 7 tickets for instant cashout lottery",
                }

            for i in self.data.get("lottery"):
                if len(i.get("ticket")) != 4:
                    return {
                        "status": "error",
                        "message": "ticket field must be 4 characters long",
                    }

                if not i.get("ticket"):
                    return {"status": "error", "message": "ticket field is required"}

                for j in i.get("ticket"):
                    if not isinstance(j, int):
                        return {
                            "status": "error",
                            "message": f"please change this `{j}` to a digit",
                        }

                    elif isinstance(j, int) and int(j) > 40:
                        return {
                            "status": "error",
                            "message": f"please change this `{j}` to a digit less than 40",
                        }

            # # check for stake amount
            amount = int(self.data.get("stake_amount"))
            # print("amount", amount)
            # if (
            #         LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
            #             len(self.data.get("lottery"))
            #         ).get("stake_amount")
            #         != amount
            # ):
            #     return {"status": "error", "message": "stake amount is invalid"}

            i_cash_price_model = InstantCashOutPriceModel()

            # print(
            #     f"""
            # i_cash_price_model.get_ticket_price_details_with_stake_amount(channel="WEB", stake_amount=amount): {i_cash_price_model.get_ticket_price_details_with_stake_amount(channel="WEB", stake_amount=amount)}
            # """
            # )

            valid_amount = i_cash_price_model.get_ticket_price_details_with_stake_amount(channel="WEB", stake_amount=amount)

            if valid_amount is None:
                return {"status": "error", "message": "stake amount is invalid"}

        return self.data


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)
    confirm_password = serializers.CharField(required=True)

    def validate(self, data):
        if data["new_password"] != data["confirm_password"]:
            raise serializers.ValidationError("Password mismatch")
        return data


class EmailSerializer(serializers.Serializer):
    email = serializers.EmailField()


class PasswordTokenSerializer(serializers.Serializer):
    password = serializers.CharField()
    new_password = serializers.CharField()
    token = serializers.CharField()

    def validate(self, data):
        if data["password"] != data["new_password"]:
            raise serializers.ValidationError("Password mismatch")
        return data


class AllLottoTicketLotteryHistorySerializer(serializers.ModelSerializer):
    depth = 1

    total_stake = serializers.SerializerMethodField()

    def get_total_stake(self, inst: LottoTicket):
        return inst.number_of_ticket * inst.stake_amount

    class Meta:
        model = LottoTicket
        fields = [
            "game_play_id",
            "lottery_type",
            "potential_winning",
            "date",
            "number_of_ticket",
            "total_stake",
        ]

    def to_representation(self, obj):
        serialized_data = super(AllLottoTicketLotteryHistorySerializer, self).to_representation(obj)
        # serialized_data["ticket"] = obj.ticket.split(",") if obj.ticket else []
        if obj.batch.is_active is False:
            if LottoWinners.objects.filter(game_play_id=obj.game_play_id).exists():
                serialized_data["status"] = "won"

            else:
                serialized_data["status"] = "lost"
        else:
            if obj.paid is True:
                serialized_data["status"] = "pending_draw"
            else:
                serialized_data["status"] = "pending_payment"

        return serialized_data


class AllLottoTicketLotteryModelHistorySerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryModel
        fields = [
            "game_play_id",
            "lottery_type",
            "band",
            "date",
            "instance_number",
            "stake_amount",
        ]

    def to_representation(self, obj):
        serialized_data = super(AllLottoTicketLotteryModelHistorySerializer, self).to_representation(obj)
        # serialized_data["ticket"] = obj.ticket.split(",") if obj.ticket else []

        serialized_data["potential_winning"] = obj.band
        serialized_data["number_of_ticket"] = obj.instance_number

        del serialized_data["band"]
        del serialized_data["instance_number"]

        if obj.batch.is_active is False:
            winning_check = LotteryWinnersTable.objects.filter(game_play_id=obj.game_play_id)
            if winning_check.exists():
                serialized_data["status"] = "won"
                serialized_data["amount_won"] = winning_check.last().earning

            else:
                serialized_data["status"] = "lost"
        else:
            if obj.paid is True:
                serialized_data["status"] = "pending_draw"
            else:
                serialized_data["status"] = "pending_payment"

        return serialized_data


class GamePlayIdSerializer(serializers.Serializer):
    game_play_id = serializers.CharField()


class LottoTicketDetailSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LottoTicket
        fields = [
            "user_profile",
            "agent_profile",
            "batch",
            "phone",
            "stake_amount",
            "lottery_type",
            "has_interest",
            "ticket",
            "date",
        ]

    def to_representation(self, obj):
        serialized_data = super(LottoTicketDetailSerializer, self).to_representation(obj)
        serialized_data["ticket"] = obj.ticket.split(",") if obj.ticket else []

        amount_won = 0

        if obj.batch.is_active is False:
            lottery_winning_qs = LottoWinners.objects.filter(
                ticket=obj.ticket,
                phone_number=obj.user_profile.phone_number,
                batch=obj.batch,
            )
            if lottery_winning_qs.exists():
                serialized_data["status"] = "won"
                amount_won += lottery_winning_qs.last().earning

            else:
                serialized_data["status"] = "lost"
        else:
            if obj.paid is True:
                serialized_data["status"] = "pending_draw"
            else:
                serialized_data["status"] = "pending_payment"

        serialized_data["amount_won"] = amount_won
        return serialized_data


class LotteryModelDetailSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryModel
        fields = [
            "game_play_id",
            "lottery_type",
            "stake_amount",
            "band",
            "date",
            "instance_number",
            "lucky_number",
        ]

    def to_representation(self, obj):
        serialized_data = super(LotteryModelDetailSerializer, self).to_representation(obj)
        # serialized_data["ticket"] = obj.ticket.split(",") if obj.ticket else []

        serialized_data["potential_winning"] = obj.band
        serialized_data["number_of_ticket"] = obj.instance_number

        del serialized_data["band"]
        del serialized_data["instance_number"]

        amount_won = 0

        if obj.batch.is_active is False:
            lottery_winning_qs = LotteryWinnersTable.objects.filter(ticket=obj.lucky_number)
            if lottery_winning_qs.exists():
                serialized_data["status"] = "won"
                amount_won += lottery_winning_qs.last().earning

            else:
                serialized_data["status"] = "lost"
        else:
            if obj.paid is True:
                serialized_data["status"] = "pending_draw"
            else:
                serialized_data["status"] = "pending_payment"

        serialized_data["amount_won"] = amount_won
        serialized_data["ticket"] = obj.lucky_number

        del serialized_data["lucky_number"]
        return serialized_data


class FundWalletFundWalletSerializer(serializers.Serializer):
    fund_wallet_choices = (
        ("PLAY_WALLET", "PLAY_WALLET"),
        ("PAYSTACK", "PAYSTACK"),
        ("USSD", "USSD"),
        ("BANK_TRANSFER", "BANK_TRANSFER"),
    )
    amount = serializers.FloatField()
    fund_wallet_type = serializers.ChoiceField(choices=fund_wallet_choices)
    bank_code = serializers.CharField(required=False, allow_blank=True)
    pin = serializers.CharField(required=False, allow_blank=True)


class GoogleAuthSerializer(serializers.Serializer):
    user_id_token = serializers.CharField()


# class SalaryForLifeJackpotWinnerSerializer(serializers.ModelSerializer):
#     depth = 1
#     class Meta:
#         model = SalaryForLifeJackpotWinner
#         fields = ["phone_number", "batch", "amount", "ticket", "created_at"]

#     def to_representation(self, obj):
#         serialized_data = super(SalaryForLifeJackpotWinnerSerializer, self).to_representation(obj)
#         serialized_data["ticket"] = obj.ticket.split(",") if obj.ticket else []
#         return serialized_data


class LotteryTicketGameResultSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LottoWinners
        fields = ["phone_number", "batch", "earning", "ticket", "date_won"]

    def to_representation(self, obj):
        serialized_data = super(LotteryTicketGameResultSerializer, self).to_representation(obj)
        serialized_data["ticket"] = obj.ticket.split(",") if obj.ticket else []
        serialized_data["created_at"] = obj.date_won
        serialized_data["amount"] = obj.earning

        del serialized_data["date_won"]
        del serialized_data["earning"]
        return serialized_data


class WhyseCashPermFourSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryWinnersTable
        fields = ["phone_number", "batch", "earning", "ticket", "date_won"]

    def to_representation(self, obj):
        serialized_data = super(WhyseCashPermFourSerializer, self).to_representation(obj)
        serialized_data["created_at"] = obj.date_won
        serialized_data["amount"] = obj.earning

        del serialized_data["date_won"]
        del serialized_data["earning"]
        return serialized_data


class SalaryForLifeJackpotSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LottoWinners
        fields = ["date_won", "phone_number", "stake_amount", "earning"]

    def to_representation(self, obj):
        serialized_data = super(SalaryForLifeJackpotSerializer, self).to_representation(obj)
        serialized_data["phone"] = obj.phone_number
        serialized_data["created_at"] = obj.date_won
        serialized_data["amount"] = obj.earning
        serialized_data["jackpot_type"] = "mega"
        serialized_data["game_played"] = "salary for life"

        del serialized_data["date_won"]
        del serialized_data["earning"]
        del serialized_data["phone_number"]
        return serialized_data


class WyseCashJackpotSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryWinnersTable
        fields = ["date_won", "phone_number", "stake_amount", "earning"]

    def to_representation(self, obj):
        serialized_data = super(WyseCashJackpotSerializer, self).to_representation(obj)
        serialized_data["phone"] = obj.phone_number
        serialized_data["created_at"] = obj.date_won
        serialized_data["amount"] = obj.earning
        serialized_data["jackpot_type"] = "crown jumbo"
        serialized_data["game_played"] = "wyse cash"

        del serialized_data["date_won"]
        del serialized_data["earning"]
        del serialized_data["phone_number"]
        return serialized_data


class LotteryModelGameHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = LotteryModel
        fields = [
            "user_profile",
            "unique_id",
            "pool",
            "stake_amount",
            "lucky_number",
            "paid_date",
            "expected_amount",
            "lottery_type",
        ]


class LottoTicketGameHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = LottoTicket
        fields = [
            "user_profile",
            "stake_amount",
            "potential_winning",
            "number_of_ticket",
            "game_play_id",
            "lottery_type",
        ]


class SoccerPredictionGameHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = SoccerPrediction
        fields = [
            "user_profile",
            "game_id",
            "date",
        ]

    def to_representation(self, obj):
        serialized_data = super(SoccerPredictionGameHistorySerializer, self).to_representation(obj)
        number_of_predictions = SoccerPrediction.objects.filter(game_id=serialized_data["game_id"])
        total_stake_amount = number_of_predictions.aggregate(Sum("stake_amount"))
        potential_winning = number_of_predictions.aggregate(Sum("potential_winning"))
        game_statuses = [
            "accepted" if not game.paid or not game.is_drawn else "won" if game.is_drawn and game.won else "lost" for game in number_of_predictions
        ]
        game_status = "won" if "won" in game_statuses else "accepted" if "accepted" in game_statuses else "lost"

        serialized_data["game_name"] = "SOCCER_CASH"
        serialized_data["number_of_predictions"] = len(number_of_predictions)
        serialized_data["total_stake_amount"] = total_stake_amount["stake_amount__sum"]
        serialized_data["potential_winning"] = potential_winning["potential_winning__sum"]
        serialized_data["game_status"] = game_status
        return serialized_data


class PhoneNumberOnboardingSerializer(serializers.Serializer):
    phone_number = serializers.CharField()

    def validate(self, data):
        if not User.check_if_phone_number_is_valid(data["phone_number"]):
            raise serializers.ValidationError("Please check the length of the phone number or content of the phone number")

        return data


class PhoneNumberOtpVerificationSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    code = serializers.CharField()

    def validate(self, data):
        if not User.check_if_phone_number_is_valid(data["phone_number"]):
            raise serializers.ValidationError("Please check the length of the phone number or content of the phone number")

        return data


class VerifyPhoneAndCreatePasswordSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    # code = serializers.CharField()
    password = serializers.CharField()
    confirm_password = serializers.CharField()

    def validate(self, data):
        if not User.check_if_phone_number_is_valid(data["phone_number"]):
            raise serializers.ValidationError("Please check the length of the phone number or content of the phone number")

        if data["password"] != data["confirm_password"]:
            raise serializers.ValidationError("Password and confirm password do not match")

        validate_password_strength(data["password"])

        return data


class PhoneNumberSerializer(serializers.Serializer):
    phone_number = serializers.CharField()

    def validate(self, data):
        if not User.check_if_phone_number_is_valid(data["phone_number"]):
            raise serializers.ValidationError("Please check the length of the phone number or content of the phone number")

        return data


class ResendPhoneNumberOtpSerializer(serializers.Serializer):
    SMS_OTP_OPTION = (
        ("SMS", "SMS"),
        ("VOICE", "VOICE"),
    )
    phone_number = serializers.CharField()
    otp_option = serializers.ChoiceField(choices=SMS_OTP_OPTION)

    def validate(self, data):
        if not User.check_if_phone_number_is_valid(data["phone_number"]):
            raise serializers.ValidationError("Please check the length of the phone number or content of the phone number")

        return data


class ActivateUsersPhoneNumberserializer(serializers.Serializer):
    code = serializers.CharField()


class VerifyPhoneNumberOtpSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    code = serializers.CharField()

    def validate(self, data):
        if not User.check_if_phone_number_is_valid(data["phone_number"]):
            raise serializers.ValidationError("Please check the length of the phone number or content of the phone number")

        return data


class NewsletterSerializer(serializers.ModelSerializer):
    class Meta:
        model = Newsletter
        fields = ["email"]
        read_only_fields = ["reference"]


class InstantCashOutResultSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LottoTicket
        fields = ["date", "system_generated_num"]

    def to_representation(self, instance):
        serialized_data = super(InstantCashOutResultSerializer, self).to_representation(instance)

        serialized_data["date_won"] = instance.date
        this_ticket = instance.system_generated_num.split(",")
        serialized_data["ticket"] = [int(x) for x in this_ticket]
        del serialized_data["date"]
        del serialized_data["system_generated_num"]
        return serialized_data


class SalaryForLiveResultSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryBatch
        fields = ["draw_date", "lottery_winner_ticket_number"]

    def get_index(list_of_dicts, target_key):
        for index, dictionary in enumerate(list_of_dicts):
            if target_key in dictionary:
                return index
        return -1

    def to_representation(self, instance):
        serialized_data = super(SalaryForLiveResultSerializer, self).to_representation(instance)
        salary_live = (
            LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", draw_date__date=instance)
            .exclude(lottery_winner_ticket_number__isnull=True, draw_date__isnull=True)
            .exclude(lottery_winner_ticket_number__exact="")
            .order_by("-draw_date")
        )
        web_data = []
        pos_data = []

        for i in salary_live:
            winnings = []
            winnings = machine_number_serializer(i.lottery_winner_ticket_number)

            if len(winnings) == 1:
                web = {"system_pick": winnings[0], "machine_pick": []}
                if i.is_pos_batch is False:
                    date = f"{i.draw_date.hour + 1}:00"
                    result = SalaryForLiveResultSerializer.get_index(web_data, date)
                    print(result, "index")
                    if result == -1:
                        web_data.append({f"{i.draw_date.hour + 1}:00": [{"system_pick": winnings[0], "machine_pick": []}]})
                    else:
                        web_data[result][f"{i.draw_date.hour + 1}:00"].append(web)
                else:
                    date = f"{i.draw_date.hour + 1}:00"
                    result = SalaryForLiveResultSerializer.get_index(pos_data, date)
                    print(result, "index")
                    if result == -1:
                        pos_data.append({f"{i.draw_date.hour + 1}:00": [{"system_pick": winnings[0], "machine_pick": []}]})
                    else:
                        pos_data[result][f"{i.draw_date.hour + 1}:00"].append(web)

            elif len(winnings) > 1:
                web = {
                    "system_pick": winnings[0],
                    "machine_pick": winnings[1:],
                }
                if i.is_pos_batch is False:
                    date = f"{i.draw_date.hour + 1}:00"
                    result = SalaryForLiveResultSerializer.get_index(web_data, date)
                    print(result, "index")
                    if result == -1:
                        web_data.append(
                            {
                                f"{i.draw_date.hour + 1}:00": [
                                    {
                                        "system_pick": winnings[0],
                                        "machine_pick": winnings[1:],
                                    }
                                ]
                            }
                        )
                    else:
                        web_data[result][f"{i.draw_date.hour + 1}:00"].append(web)

                else:
                    date = f"{i.draw_date.hour + 1}:00"
                    result = SalaryForLiveResultSerializer.get_index(pos_data, date)
                    print(result, "index")
                    if result == -1:
                        pos_data.append(
                            {
                                f"{i.draw_date.hour + 1}:00": [
                                    {
                                        "system_pick": winnings[0],
                                        "machine_pick": winnings[1:],
                                    }
                                ]
                            }
                        )
                    else:
                        pos_data[result][f"{i.draw_date.hour + 1}:00"].append(web)

        serialized_data["date_won"] = instance
        serialized_data["ticket"] = {"pos": pos_data, "web": web_data}
        del serialized_data["draw_date"]
        del serialized_data["lottery_winner_ticket_number"]
        return serialized_data


class BankerResultSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryBatch
        fields = ["draw_date", "lottery_winner_ticket_number"]

    def get_index(list_of_dicts, target_key):
        for index, dictionary in enumerate(list_of_dicts):
            if target_key in dictionary:
                return index
        return -1

    def to_representation(self, instance):
        serialized_data = super(BankerResultSerializer, self).to_representation(instance)
        salary_live = (
            LotteryBatch.objects.filter(lottery_type="BANKER", draw_date__date=instance)
            .exclude(lottery_winner_ticket_number__isnull=True, draw_date__isnull=True)
            .exclude(lottery_winner_ticket_number__exact="")
            .order_by("-draw_date")
        )
        web_data = []
        pos_data = []

        for i in salary_live:
            winnings = []
            winnings = machine_number_serializer(i.lottery_winner_ticket_number)

            # for j in winning_ticket:
            #     print(j, "::::::::::::::::::::::::::::::::::::::::")
            #     j_list = j.split(",")

            #     to_add_numbers = []
            #     for x in j_list:
            #         if x == "[]":
            #             continue
            #         else:
            #             to_add_numbers.append(int(x))

            #     winnings.append(to_add_numbers)

            if len(winnings) == 1:
                web = {"system_pick": winnings[0], "machine_pick": []}
                if i.is_pos_batch is False:
                    date = add_1_hour_to_date(i)
                    result = BankerResultSerializer.get_index(web_data, date)
                    print(result, "index")
                    if result == -1:
                        web_data.append({add_1_hour_to_date(i): [{"system_pick": winnings[0], "machine_pick": []}]})
                    else:
                        web_data[result][add_1_hour_to_date(i)].append(web)
                else:
                    date = add_1_hour_to_date(i)
                    result = BankerResultSerializer.get_index(pos_data, date)
                    print(result, "index")
                    if result == -1:
                        pos_data.append({add_1_hour_to_date(i): [{"system_pick": winnings[0], "machine_pick": []}]})
                    else:
                        pos_data[result][add_1_hour_to_date(i)].append(web)

            elif len(winnings) > 1:
                web = {
                    "system_pick": winnings[0],
                    "machine_pick": winnings[1:],
                }
                if i.is_pos_batch is False:
                    date = add_1_hour_to_date(i)
                    result = BankerResultSerializer.get_index(web_data, date)
                    print(result, "index")
                    if result == -1:
                        web_data.append(
                            {
                                add_1_hour_to_date(i): [
                                    {
                                        "system_pick": winnings[0],
                                        "machine_pick": winnings[1:],
                                    }
                                ]
                            }
                        )
                    else:
                        web_data[result][add_1_hour_to_date(i)].append(web)

                else:
                    date = add_1_hour_to_date(i)
                    result = BankerResultSerializer.get_index(pos_data, date)
                    print(result, "index")
                    if result == -1:
                        pos_data.append(
                            {
                                add_1_hour_to_date(i): [
                                    {
                                        "system_pick": winnings[0],
                                        "machine_pick": winnings[1:],
                                    }
                                ]
                            }
                        )
                    else:
                        pos_data[result][add_1_hour_to_date(i)].append(web)

        serialized_data["date_won"] = instance
        serialized_data["ticket"] = {"pos": pos_data, "web": web_data}
        del serialized_data["draw_date"]
        del serialized_data["lottery_winner_ticket_number"]
        return serialized_data


class QuikaLottoSerializer(serializers.Serializer):
    stake_amount = serializers.IntegerField()
    lottery = serializers.ListField(child=serializers.JSONField())

    def validate(self, data):
        if len(data.get("lottery")) > 7:
            raise serializers.ValidationError({"lottery": "You can only select 7 numbers"})

        ticket_count = int(len(data.get("lottery")))

        stake_amount = ConstantVariable().quika_admin_stake_and_winning_amount()[ticket_count].get("stake_amount")
        if data.get("stake_amount") < stake_amount:
            raise serializers.ValidationError({"amount": "Amount is less than the minimum stake amount"})

        print(data.get("lottery"), "Stake")

        for line in data.get("lottery"):
            print(line["ticket"], "-----------------------")

            if len(line["ticket"]) > 4 or len(line["ticket"]) < 4:
                raise serializers.ValidationError({"ticket": "Ticket must be between 4 and 4 numbers"})

        return data


class ReferFriendsDetails(serializers.Serializer):
    first_name = serializers.CharField()
    phone_number = serializers.CharField()


class WebPostTestimonialSerializer(serializers.Serializer):
    message = serializers.CharField()
    user_details = serializers.ListSerializer(child=ReferFriendsDetails())


class UserReferralsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReferralTable
        fields = [
            "id",
            "date_created",
            "user_name",
            "user_phone",
            "amount",
            "played_percentage",
            "status",
        ]


class QuikaSerializer(serializers.Serializer):
    from_referral_wallet = serializers.BooleanField()
    paystack = serializers.BooleanField()
    from_play_wallet = serializers.BooleanField()
    lottery_play = serializers.ListField(child=serializers.JSONField())

    def validate(self, attrs):
        lottery_play = attrs.get("lottery_play")
        if len(lottery_play) <= 0:
            raise serializers.ValidationError({"lottery_play": "You must select at least one lottery."})
        # total_amount = 0
        for lottery in lottery_play:
            # print(lottery)
            try:
                if len(lottery["lottery"]) <= 0:
                    raise serializers.ValidationError({"lottery": "Lottery cannot be empty"})

                stake_amount = lottery["amount"]
                # amount = LOTT0_AMOUNT.get(stake_amount, None)

                # if amount is None:
                #     raise serializers.ValidationError(
                #         {"amount": "Invalid stake amount"}
                #     )

                quicka_price_model = QuikaPriceModel()

                valid_amount = quicka_price_model.get_ticket_price_details_with_stake_amount(channel="WEB", stake_amount=stake_amount)

                if valid_amount is None:
                    raise serializers.ValidationError({"amount": "Invalid stake amount"})
                if len(lottery["lottery"]) != 1:
                    raise serializers.ValidationError({"tickets": "you can only have one line"})

            except KeyError as key:
                print(key, "---------------Invalid KeyError")
                raise serializers.ValidationError({"lottery_play": "Invalid key"})

        return attrs


class WyseCashBusinessGamePlayChildTicket(serializers.Serializer):
    lucky_number = serializers.CharField()


class WyseCashBusinessGamePlaySerializer(serializers.Serializer):
    full_name = serializers.CharField()
    phone_number = serializers.CharField()
    ticket = serializers.ListSerializer(child=WyseCashBusinessGamePlayChildTicket())
    business_name = serializers.CharField(allow_blank=True, required=False, allow_null=True)
    business_description = serializers.CharField(allow_blank=True, required=False, allow_null=True)
    business_file = serializers.CharField(allow_blank=True, required=False, allow_null=True)


class LotteryWinnersTableWithMaskNumberSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryWinnersTable
        fields = ("phone_number", "earning", "total_jackpot_amount", "ticket", "unnique_id", "win_type", "pool", "date_won", "lottery")

    def to_representation(self, obj):
        serialized_data = super(LotteryWinnersTableWithMaskNumberSerializer, self).to_representation(obj)

        lottery = serialized_data["lottery"]

        serialized_data["win_type"] = "micro"

        if lottery is not None:
            lottery_instance = LotteryModel.objects.get(id=lottery)
            if lottery_instance.lottery_type == "WHYSE_CASH_BUSINESS":
                serialized_data["win_type"] = "business"

        serialized_data["phone_number"] = mask_winners_phone_number(obj.phone_number)
        serialized_data["earning"] = obj.earning
        serialized_data["total_jackpot_amount"] = Utility.currency_formatter(obj.total_jackpot_amount)

        del serialized_data["lottery"]
        return serialized_data


class PosBankerResultSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryBatch
        fields = ["id", "batch_uuid", "draw_date", "lottery_winner_ticket_number"]

    def to_representation(self, instance):
        # Get the base serialized data
        super(PosBankerResultSerializer, self).to_representation(instance)

        # Query for lottery batch data
        lottery_batches = (
            LotteryBatch.objects.filter(lottery_type="BANKER", draw_date__date=instance)
            .exclude(lottery_winner_ticket_number__isnull=True, draw_date__isnull=True)
            .exclude(lottery_winner_ticket_number__exact="")
            .order_by("-draw_date")
        )

        result = []

        for batch in lottery_batches:
            # Parse the lottery winner ticket numbers
            ticket_numbers = machine_number_serializer(batch.lottery_winner_ticket_number)

            # Format the date to YYYY-MM-DD
            formatted_date = batch.draw_date.strftime("%Y-%m-%d") if batch.draw_date else None
            if batch.draw_date is None:
                draw_date = formatted_date
            else:
                draw_date = f"{formatted_date} {add_1_hour_to_date(batch)}"

            # Create the value object with the required structure
            value = {
                "id": batch.id,
                "batch_uuid": batch.batch_uuid,
                "game_play_id": "",
                "earning": 0,  # Assuming there's an earning field, or default to 0
                "date": draw_date,
                "ticket_number": ticket_numbers if ticket_numbers else [],
            }

            # Add to the result list
            result.append({"date": formatted_date, "value": value})

        return result
