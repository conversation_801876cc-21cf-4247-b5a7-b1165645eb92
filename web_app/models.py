from django.db import models

from main.models import UserProfile
from web_app.helpers import file_upload_path


class Newsletter(models.Model):
    email = models.EmailField(max_length=225, unique=True)
    user_reference = models.CharField(max_length=225, unique=True)
    subscribe = models.BooleanField(default=False)
    unsubscribe = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.email

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "EMAIL NEWSLETTER"
        verbose_name_plural = "EMAIL NEWSLETTERS"


class UserFileUpload(models.Model):
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    file = models.FileField(upload_to=file_upload_path)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.user.__str__()

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "USER FILE"
        verbose_name_plural = "USER FILES"

    @property
    def get_url(self):
        return self.file.url
