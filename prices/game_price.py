from dataclasses import dataclass


from main.helpers.helper_functions import (
    big_digit_currency_formatter,
    currency_formatter,
)



def round_down(num):
    return num // 100 * 100


@dataclass
class LottoPrice:
    @classmethod
    def instant_cash_out(cls):
        pass


@dataclass
class InstantCashOutPriceModel:
    @classmethod
    def ticket_price_with_other_service_price(cls, channel, ticket_line=None):
        from main.models import ConstantVariable

        try:
            const_obj = ConstantVariable.objects.last()
            divisor = const_obj.icash_winnings_divisor
        except Exception:
            divisor = 1

        if channel == "POS":
            return None

        elif channel == "WEB":
            price = {
                1: {
                    "ticket_price": 200,
                    "potential_winning": 11250 / divisor,
                    "line_number": 1,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 300,
                    "amount_to_register": 200,
                },
                2: {
                    "ticket_price": 500,
                    "potential_winning": 15200 / divisor,
                    "line_number": 2,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 600,
                    "amount_to_register": 500,
                },
                3: {
                    "ticket_price": 750,
                    "potential_winning": 15750 / divisor,
                    "line_number": 3,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 850,
                    "amount_to_register": 750,
                },
                4: {
                    "ticket_price": 800,
                    "potential_winning": 18000 / divisor,
                    "line_number": 4,
                    "illusion_price": 400,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1200,
                    "amount_to_register": 800,
                },
                5: {
                    "ticket_price": 1250,
                    "potential_winning": 18750 / divisor,
                    "line_number": 5,
                    "illusion_price": 200,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1450,
                    "amount_to_register": 1250,
                },
                6: {
                    "ticket_price": 1300,
                    "potential_winning": 25000 / divisor,
                    "line_number": 6,
                    "illusion_price": 200,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1500,
                    "amount_to_register": 1300,
                },
                7: {
                    "ticket_price": 1400,
                    "potential_winning": 27000 / divisor,
                    "line_number": 7,
                    "illusion_price": 300,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1700,
                    "amount_to_register": 1400,
                },
            }

            if ticket_line is None:
                return price

            return price[ticket_line]

        elif channel == "USSD":
            price = {
                1: {
                    "ticket_price": 200,
                    "potential_winning": 11250 / divisor,
                    "line_number": 1,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 200,
                    "amount_to_register": 200,
                },
                2: {
                    "ticket_price": 500,
                    "potential_winning": 15200 / divisor,
                    "line_number": 2,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 500,
                    "amount_to_register": 500,
                },
                3: {
                    "ticket_price": 750,
                    "potential_winning": 15750 / divisor,
                    "line_number": 3,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 750,
                    "amount_to_register": 750,
                },
                4: {
                    "ticket_price": 800,
                    "potential_winning": 18000 / divisor,
                    "line_number": 4,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 800,
                    "amount_to_register": 800,
                },
                5: {
                    "ticket_price": 1250,
                    "potential_winning": 18750 / divisor,
                    "line_number": 5,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1250,
                    "amount_to_register": 1250,
                },
                6: {
                    "ticket_price": 1300,
                    "potential_winning": 25000 / divisor,
                    "line_number": 6,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1300,
                    "amount_to_register": 1300,
                },
                7: {
                    "ticket_price": 1400,
                    "potential_winning": 27000 / divisor,
                    "line_number": 7,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1400,
                    "amount_to_register": 1400,
                },
            }

            if ticket_line is None:
                return price

            return price[ticket_line]

        return None

    def ticket_price(cls, channel, ticket_line=None):
        from main.models import ConstantVariable

        try:
            const_obj = ConstantVariable.objects.last()
            divisor = const_obj.icash_winnings_divisor
        except Exception:
            divisor = 1

        if channel == "POS":
            price = {
                1: {
                    "ticket_price": 200,  # 200,
                    "potential_winning": 11250 / divisor,
                    "line_number": 1,
                    "total_amount": 200,  # 200,
                    "amount_to_register": 200,
                },
                2: {
                    "ticket_price": 400,  # 500,
                    "potential_winning": 15200 / divisor,
                    "line_number": 2,
                    "total_amount": 400,  # 500,
                    "amount_to_register": 400,
                },
                3: {
                    "ticket_price": 600,
                    "potential_winning": 15750 / divisor,
                    "line_number": 3,
                    "total_amount": 600,
                    "amount_to_register": 600,
                },
                4: {
                    "ticket_price": 800,  # 800,
                    "potential_winning": 18000 / divisor,
                    "line_number": 4,
                    "total_amount": 800,  # 800,
                    "amount_to_register": 800,
                },
                5: {
                    "ticket_price": 1200,
                    "potential_winning": 18750 / divisor,
                    "line_number": 5,
                    "total_amount": 1200,
                    "amount_to_register": 1200,
                },
                6: {
                    "ticket_price": 1400,
                    "potential_winning": 25000 / divisor,
                    "line_number": 6,
                    "total_amount": 1400,
                    "amount_to_register": 1400,
                },
                7: {
                    "ticket_price": 1600,
                    "potential_winning": 27000 / divisor,
                    "line_number": 7,
                    "total_amount": 1600,
                    "amount_to_register": 1600,
                },
            }

            if ticket_line is None:
                return price

            return price[ticket_line]

        if channel == "WEB":
            price = {
                1: {
                    "ticket_price": 300,
                    "potential_winning": 11250 / divisor,
                    "line_number": 1,
                    "total_amount": 300,
                    "amount_to_register": 200,
                },
                2: {
                    "ticket_price": 500,
                    "potential_winning": 15200 / divisor,
                    "line_number": 2,
                    "total_amount": 500,
                    "amount_to_register": 500,
                },
                3: {
                    "ticket_price": 750,
                    "potential_winning": 15750 / divisor,
                    "line_number": 3,
                    "total_amount": 750,
                    "amount_to_register": 750,
                },
                4: {
                    "ticket_price": 800,
                    "potential_winning": 18000 / divisor,
                    "line_number": 4,
                    "total_amount": 800,
                    "amount_to_register": 800,
                },
                5: {
                    "ticket_price": 1250,
                    "potential_winning": 18750 / divisor,
                    "line_number": 5,
                    "total_amount": 1250,
                    "amount_to_register": 1250,
                },
                6: {
                    "ticket_price": 1300,
                    "potential_winning": 25000 / divisor,
                    "line_number": 6,
                    "total_amount": 1300,
                    "amount_to_register": 1300,
                },
                7: {
                    "ticket_price": 1400,
                    "potential_winning": 27000 / divisor,
                    "line_number": 7,
                    "total_amount": 1400,
                    "amount_to_register": 1400,
                },
            }

            if ticket_line is None:
                return price

            return price[ticket_line]

        if channel == "USSD":
            price = {
                1: {
                    "ticket_price": 200,
                    "potential_winning": 11250 / divisor,
                    "line_number": 1,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 200,
                    "amount_to_register": 200,
                },
                2: {
                    "ticket_price": 500,
                    "potential_winning": 15200 / divisor,
                    "line_number": 2,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 500,
                    "amount_to_register": 500,
                },
                3: {
                    "ticket_price": 750,
                    "potential_winning": 15750 / divisor,
                    "line_number": 3,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 750,
                    "amount_to_register": 750,
                },
                4: {
                    "ticket_price": 800,
                    "potential_winning": 18000 / divisor,
                    "line_number": 4,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 800,
                    "amount_to_register": 800,
                },
                5: {
                    "ticket_price": 1250,
                    "potential_winning": 18750 / divisor,
                    "line_number": 5,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1250,
                    "amount_to_register": 1250,
                },
                6: {
                    "ticket_price": 1300,
                    "potential_winning": 25000 / divisor,
                    "line_number": 6,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1300,
                    "amount_to_register": 1300,
                },
                7: {
                    "ticket_price": 1400,
                    "potential_winning": 27000 / divisor,
                    "line_number": 7,
                    "illusion_price": 0,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1400,
                    "amount_to_register": 1400,
                },
            }

            if ticket_line is None:
                return price
            return price[ticket_line]

        return None

    def get_ticket_price_details_with_stake_amount(cls, channel, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)

        if channel == "POS":
            ticket_p = cls.ticket_price(channel)
        else:
            ticket_p = cls.ticket_price_with_other_service_price(channel)

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[
                float(value["ticket_price"])
                + float(value.get("illusion_price", 0))
                + float(value.get("woven_service_charge", 0))
                + float(value.get("africastalking_charge", 0))
            ] = value

        # print(
        #     f"""
        # price_re_structure: {price_re_structure}
        # _stake_amount: {_stake_amount}
        # """
        # )

        return price_re_structure.get(_stake_amount)

    def ussd_top_prices_in_threes(cls, index_range: list):
        ticket_price = cls.ticket_price_with_other_service_price(channel="USSD")
        price = {}
        for i in index_range:
            if i == 5:
                pass
            else:
                price[i] = ticket_price[i]

        return price

    @classmethod
    def white_winning(cls, const_table_instance=None) -> dict:
        amount_data = {
            200: {
                "least_win": 250,
                "minor_win": 650,
                # "min_win": round_down(4500 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(5400 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(11250 / const_table_instance.icash_winnings_divisor),
            },
            500: {
                "least_win": 1000,
                "minor_win": 1250,
                # "min_win": round_down(7000 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(9000 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(15200 / const_table_instance.icash_winnings_divisor),
            },
            750: {
                "least_win": 1400,
                "minor_win": 1700,
                # "min_win": round_down(9000 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(10800 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(15750 / const_table_instance.icash_winnings_divisor),
            },
            800: {
                "least_win": 2000,
                "minor_win": 2250,
                # "min_win": round_down(11400 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(13500 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(18000 / const_table_instance.icash_winnings_divisor),
            },
            1250: {
                "least_win": 1250,
                "minor_win": 1950,
                # "min_win": round_down(12000 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(14000 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(18750 / const_table_instance.icash_winnings_divisor),
            },
            1300: {
                "least_win": 1300,
                "minor_win": 1700,
                # "min_win": round_down(13500 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(15000 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(25000 / const_table_instance.icash_winnings_divisor),
            },
            1400: {
                "least_win": 1500,
                "minor_win": 1800,
                # "min_win": round_down(15000 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(18000 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(27000 / const_table_instance.icash_winnings_divisor),
            },
        }
        return amount_data

    @classmethod
    def amount(cls, const_table_instance=None):
        amount_data = {
            200: {
                "least_win": 300,
                "minor_win": 500,
                "min_win": 800,
                "mid_win": 1000,
                "max_win": 1000,
            },
            500: {
                "least_win": 1000,
                "minor_win": 1200,
                "min_win": 1700,
                "mid_win": 1500,
                "max_win": 1800,
            },
            750: {
                "least_win": 1125,
                "minor_win": 1200,
                # "min_win": round_down(9000 / (const_table_instance.icash_winnings_divisor * 1.666666666666667)),
                # "mid_win": round_down(10800 / (const_table_instance.icash_winnings_divisor * 1.666666666666667)),
                # "max_win": round_down(15750 / (const_table_instance.icash_winnings_divisor * 1.666666666666667)),
            },
            800: {
                "least_win": 1200,
                "minor_win": 1500,
                "min_win": 2000,
                "mid_win": 1800,
                "max_win": 2000,
            },
            1250: {
                "least_win": 2500,
                "minor_win": 2350,
                "min_win": 2200,
                "mid_win": 2500,
                "max_win": 3500,
            },
            1300: {
                "least_win": 2750,
                "minor_win": 1800,
                "min_win": 2000,
                "mid_win": 3000,
                "max_win": 4000,
            },
            1400: {
                "least_win": 2800,
                "minor_win": 2000,
                "min_win": 2500,
                "mid_win": 3500,
                "max_win": 4000,
            },
        }
        return amount_data

    def lotto_amount_line_reg(self) -> dict:
        """
        Stake amount is the amount received from the player
        Reg amount is the amount registered on the lotto table
        illusion amount still remains the illusion amount if the game is expected to have the illusion feature
        """
        lines_amounts = {
            1: {"stake_amount": 300, "reg_amount": 200, "illusion": 100},
            2: {"stake_amount": 600, "reg_amount": 500, "illusion": 100},
            3: {"stake_amount": 850, "reg_amount": 750, "illusion": 100},
            4: {"stake_amount": 1200, "reg_amount": 800, "illusion": 400},
            5: {"stake_amount": 1450, "reg_amount": 1250, "illusion": 200},
            6: {"stake_amount": 1500, "reg_amount": 1300, "illusion": 200},
            7: {"stake_amount": 1700, "reg_amount": 1400, "illusion": 300},
        }

        return lines_amounts


@dataclass
class SalaryForLifePriceModel:
    @classmethod
    def ticket_price(cls, channel, ticket_line=None):
        from pos_app.models import AgentConstantVariables

        if channel == "POS":
            base_price = 300

            retail_price_mutiplier = AgentConstantVariables.get_salary_for_life_price_multiplier()
            
            price = {}

            potential_winning = {
                1: 5000,
                2: 15000,
                3: 50000,
                4: 150000,
                5: 250000,
                6: 500000,
                7: 750000,
                8: 900000,
                9: 1250000,
                10: 1500000
            }

            for p, v in retail_price_mutiplier.items():
                if p == 1:
                    price[1] = {
                        "ticket_price": base_price,
                        "potential_winning": potential_winning[int(p)],
                        "line_number": 1,
                        "woven_service_charge": 0,
                        "africastalking_charge": 0,
                        "total_amount": 300,
                        "amount_to_register": 300,
                    }
                else:
                    price[int(p)] = {
                        "ticket_price": base_price * v,
                        "potential_winning": potential_winning[int(p)],
                        "line_number": int(p),
                        "woven_service_charge": 0,
                        "africastalking_charge": 0,
                        "total_amount": 300 * v,
                        "amount_to_register": 300 * v,
                    }

            # price = {
            #     1: {
            #         "ticket_price": 300,
            #         "potential_winning": 5000,
            #         "line_number": 1,
            #         "woven_service_charge": 0,
            #         "africastalking_charge": 0,
            #         "total_amount": 300,
            #         "amount_to_register": 300,
            #     },
            #     2: {
            #         "ticket_price": 450,
            #         "potential_winning": 15000,
            #         "line_number": 2,
            #         "woven_service_charge": 0,
            #         "africastalking_charge": 0,
            #         "total_amount": 450,
            #         "amount_to_register": 450,
            #     },
            #     3: {
            #         "ticket_price": 700,
            #         "potential_winning": 50000,
            #         "line_number": 3,
            #         "woven_service_charge": 0,
            #         "africastalking_charge": 0,
            #         "total_amount": 700,
            #         "amount_to_register": 700,
            #     },
            #     4: {
            #         "ticket_price": 1400,
            #         "potential_winning": 150000,
            #         "line_number": 4,
            #         "woven_service_charge": 0,
            #         "africastalking_charge": 0,
            #         "total_amount": 1400,
            #         "amount_to_register": 1400,
            #     },
            #     5: {
            #         "ticket_price": 2030,
            #         "potential_winning": 250000,
            #         "line_number": 5,
            #         "woven_service_charge": 0,
            #         "africastalking_charge": 0,
            #         "total_amount": 2030,
            #         "amount_to_register": 2030,
            #     },
            #     6: {
            #         "ticket_price": 2800,
            #         "potential_winning": 500000,
            #         "line_number": 6,
            #         "woven_service_charge": 0,
            #         "africastalking_charge": 0,
            #         "total_amount": 2800,
            #         "amount_to_register": 2800,
            #     },
            #     7: {
            #         "ticket_price": 3300,
            #         "potential_winning": 750000,
            #         "line_number": 7,
            #         "woven_service_charge": 0,
            #         "africastalking_charge": 0,
            #         "total_amount": 3300,
            #         "amount_to_register": 3300,
            #     },
            #     8: {
            #         "ticket_price": 3700,
            #         "potential_winning": 900000,
            #         "line_number": 8,
            #         "woven_service_charge": 0,
            #         "africastalking_charge": 0,
            #         "total_amount": 3700,
            #         "amount_to_register": 3700,
            #     },
            #     9: {
            #         "ticket_price": 4200,
            #         "potential_winning": 1250000,
            #         "line_number": 9,
            #         "woven_service_charge": 0,
            #         "africastalking_charge": 0,
            #         "total_amount": 4200,
            #         "amount_to_register": 4200,
            #     },
            #     10: {
            #         "ticket_price": 5800,
            #         "potential_winning": 1500000,
            #         "line_number": 10,
            #         "woven_service_charge": 0,
            #         "africastalking_charge": 0,
            #         "total_amount": 5800,
            #         "amount_to_register": 5800,
            #     },
            # }

            if ticket_line is None:
                return price

            return price[ticket_line]

        elif channel == "WEB":
            price = {
                1: {
                    "ticket_price": 300,
                    "potential_winning": 5000,
                    "line_number": 1,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 300,
                    "amount_to_register": 300,
                },
                2: {
                    "ticket_price": 450,
                    "potential_winning": 15000,
                    "line_number": 2,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 450,
                    "amount_to_register": 450,
                },
                3: {
                    "ticket_price": 700,
                    "potential_winning": 50000,
                    "line_number": 3,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 700,
                    "amount_to_register": 700,
                },
                4: {
                    "ticket_price": 1400,
                    "potential_winning": 150000,
                    "line_number": 4,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1400,
                    "amount_to_register": 1400,
                },
                5: {
                    "ticket_price": 2030,
                    "potential_winning": 250000,
                    "line_number": 5,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 2030,
                    "amount_to_register": 2030,
                },
                6: {
                    "ticket_price": 2800,
                    "potential_winning": 500000,
                    "line_number": 6,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 2800,
                    "amount_to_register": 2800,
                },
                7: {
                    "ticket_price": 3300,
                    "potential_winning": 750000,
                    "line_number": 7,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 3300,
                    "amount_to_register": 3300,
                },
                8: {
                    "ticket_price": 3700,
                    "potential_winning": 900000,
                    "line_number": 8,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 3700,
                    "amount_to_register": 3700,
                },
                9: {
                    "ticket_price": 4200,
                    "potential_winning": 1250000,
                    "line_number": 9,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 4200,
                    "amount_to_register": 4200,
                },
                10: {
                    "ticket_price": 5800,
                    "potential_winning": 1500000,
                    "line_number": 10,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 5800,
                    "amount_to_register": 5800,
                },
            }

            if ticket_line is None:
                return price

            return price[ticket_line]

        elif channel == "USSD":
            price = {
                1: {
                    "ticket_price": 150,
                    "potential_winning": 5000,
                    "line_number": 1,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 150,
                    "amount_to_register": 150,
                },
                2: {
                    "ticket_price": 350,
                    "potential_winning": 15000,
                    "line_number": 2,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 350,
                    "amount_to_register": 350,
                },
                3: {
                    "ticket_price": 700,
                    "potential_winning": 50000,
                    "line_number": 3,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 700,
                    "amount_to_register": 700,
                },
                4: {
                    "ticket_price": 1400,
                    "potential_winning": 150000,
                    "line_number": 4,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1400,
                    "amount_to_register": 1400,
                },
                5: {
                    "ticket_price": 2030,
                    "potential_winning": 250000,
                    "line_number": 5,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 2030,
                    "amount_to_register": 2030,
                },
                6: {
                    "ticket_price": 2800,
                    "potential_winning": 500000,
                    "line_number": 6,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 2800,
                    "amount_to_register": 2800,
                },
                7: {
                    "ticket_price": 3300,
                    "potential_winning": 750000,
                    "line_number": 7,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 3300,
                    "amount_to_register": 3300,
                },
                8: {
                    "ticket_price": 3700,
                    "potential_winning": 900000,
                    "line_number": 8,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 3700,
                    "amount_to_register": 3700,
                },
                9: {
                    "ticket_price": 4200,
                    "potential_winning": 1250000,
                    "line_number": 9,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 4200,
                    "amount_to_register": 4200,
                },
                10: {
                    "ticket_price": 5800,
                    "potential_winning": 1500000,
                    "line_number": 10,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 5800,
                    "amount_to_register": 5800,
                },
            }

            if ticket_line is None:
                return price

            return price[ticket_line]

        else:
            return None

    @classmethod
    def get_ticket_price_details_with_stake_amount(cls, channel, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)
        # if channel == "POS":
        #     ticket_p = cls.ticket_price(channel)
        # else:
        #     ticket_p = cls.ticket_price_with_other_service_price(channel)

        ticket_p = cls.ticket_price(channel)

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[
                float(value["ticket_price"])
                + float(value.get("illusion_price", 0))
                + float(value.get("woven_service_charge", 0))
                + float(value.get("africastalking_charge", 0))
            ] = value

        return price_re_structure.get(_stake_amount)

    @classmethod
    def ussd_top_three_pontential_winnings(cls):
        sal_4_life_ticket_prices = cls.ticket_price("USSD")
        top_three = []
        top_three_index = [3, 6, 9]
        for index in top_three_index:
            top_three.append(currency_formatter(int(sal_4_life_ticket_prices[index]["potential_winning"])))

        return top_three

    @classmethod
    def ussd_top_prices_in_threes(cls, index_range: list):
        sal_4_life_ticket_prices = cls.ticket_price("USSD")

        data = {}
        for index in index_range:
            data[index] = {
                "ticket_price": currency_formatter(
                    sal_4_life_ticket_prices.get(index).get("ticket_price")
                    + sal_4_life_ticket_prices.get(index).get("woven_service_charge", 0)
                    + sal_4_life_ticket_prices.get(index).get("africastalking_charge", 0)
                ),
                "potential_winning": big_digit_currency_formatter(sal_4_life_ticket_prices.get(index).get("potential_winning")),
            }

        return data


@dataclass
class WyseCashPriceModel:
    @classmethod
    def ticket_price(cls, channel, band=None, no_of_line=None):
        if channel == "POS":
            if band is not None and no_of_line is not None:
                if band == 10000:
                    stake_amount = 300

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)

                elif band == 50000:
                    stake_amount = 350

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)

                elif band == 100000:
                    stake_amount = 700

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)
                elif band == 200000:
                    stake_amount = 1200

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)

                else:
                    return None

            else:
                data = {
                    10000: {
                        1: {
                            "stake_amount": 300 * 1,
                            "total_winning_amount": 10000 * 1,
                            "total_amount": 300 * 1,
                            "amount_to_register": 300 * 1,
                        },
                        2: {
                            "stake_amount": 300 * 2,
                            "total_winning_amount": 10000 * 2,
                            "total_amount": 300 * 2,
                            "amount_to_register": 300 * 2,
                        },
                        3: {
                            "stake_amount": 300 * 3,
                            "total_winning_amount": 10000 * 3,
                            "total_amount": 300 * 3,
                            "amount_to_register": 300 * 3,
                        },
                        4: {
                            "stake_amount": 300 * 4,
                            "total_winning_amount": 10000 * 4,
                            "total_amount": 300 * 4,
                            "amount_to_register": 300 * 4,
                        },
                        5: {
                            "stake_amount": 300 * 5,
                            "total_winning_amount": 10000 * 5,
                            "total_amount": 300 * 5,
                            "amount_to_register": 300 * 5,
                        },
                    },
                    50000: {
                        1: {
                            "stake_amount": 350 * 1,
                            "total_winning_amount": 50000 * 1,
                            "total_amount": 350 * 1,
                            "amount_to_register": 350 * 1,
                        },
                        2: {
                            "stake_amount": 350 * 2,
                            "total_winning_amount": 50000 * 2,
                            "total_amount": 350 * 2,
                            "amount_to_register": 350 * 2,
                        },
                        3: {
                            "stake_amount": 350 * 3,
                            "total_winning_amount": 50000 * 3,
                            "total_amount": 350 * 3,
                            "amount_to_register": 350 * 3,
                        },
                        4: {
                            "stake_amount": 350 * 4,
                            "total_winning_amount": 50000 * 4,
                            "total_amount": 350 * 4,
                            "amount_to_register": 350 * 4,
                        },
                        5: {
                            "stake_amount": 350 * 5,
                            "total_winning_amount": 50000 * 5,
                            "total_amount": 350 * 5,
                            "amount_to_register": 350 * 5,
                        },
                    },
                    100000: {
                        1: {
                            "stake_amount": 700 * 1,
                            "total_winning_amount": 100000 * 1,
                            "total_amount": 700 * 1,
                            "amount_to_register": 700 * 1,
                        },
                        2: {
                            "stake_amount": 700 * 2,
                            "total_winning_amount": 100000 * 2,
                            "total_amount": 700 * 2,
                            "amount_to_register": 700 * 2,
                        },
                        3: {
                            "stake_amount": 700 * 3,
                            "total_winning_amount": 100000 * 3,
                            "total_amount": 700 * 3,
                            "amount_to_register": 700 * 3,
                        },
                        4: {
                            "stake_amount": 700 * 4,
                            "total_winning_amount": 100000 * 4,
                            "total_amount": 700 * 4,
                            "amount_to_register": 700 * 4,
                        },
                        5: {
                            "stake_amount": 700 * 5,
                            "total_winning_amount": 100000 * 5,
                            "total_amount": 700 * 5,
                            "amount_to_register": 700 * 5,
                        },
                    },
                    200000: {
                        1: {
                            "stake_amount": 1200 * 1,
                            "total_winning_amount": 200000 * 1,
                            "total_amount": 1200 * 1,
                            "amount_to_register": 1200 * 1,
                        },
                        2: {
                            "stake_amount": 1200 * 2,
                            "total_winning_amount": 200000 * 2,
                            "total_amount": 1200 * 2,
                            "amount_to_register": 1200 * 2,
                        },
                        3: {
                            "stake_amount": 1200 * 3,
                            "total_winning_amount": 200000 * 3,
                            "total_amount": 1200 * 3,
                            "amount_to_register": 1200 * 3,
                        },
                        4: {
                            "stake_amount": 1200 * 4,
                            "total_winning_amount": 200000 * 4,
                            "total_amount": 1200 * 4,
                            "amount_to_register": 1200 * 4,
                        },
                        5: {
                            "stake_amount": 1200 * 5,
                            "total_winning_amount": 200000 * 5,
                            "total_amount": 1200 * 5,
                            "amount_to_register": 1200 * 5,
                        },
                    },
                }

                return data

        elif channel == "WEB":
            if band is not None and no_of_line is not None:
                if band == 10000:
                    stake_amount = 150

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)

                elif band == 50000:
                    stake_amount = 250

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)

                elif band == 100000:
                    stake_amount = 600

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)
                elif band == 200000:
                    stake_amount = 1200

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)

                else:
                    return None

            else:
                data = {
                    10000: {
                        1: {
                            "stake_amount": 150 * 1,
                            "total_winning_amount": 10000 * 1,
                            "total_amount": 150 * 1,
                            "amount_to_register": 150 * 1,
                        },
                        2: {
                            "stake_amount": 150 * 2,
                            "total_winning_amount": 10000 * 2,
                            "total_amount": 150 * 2,
                            "amount_to_register": 150 * 2,
                        },
                        3: {
                            "stake_amount": 150 * 3,
                            "total_winning_amount": 10000 * 3,
                            "total_amount": 150 * 3,
                            "amount_to_register": 150 * 3,
                        },
                        4: {
                            "stake_amount": 150 * 4,
                            "total_winning_amount": 10000 * 4,
                            "total_amount": 150 * 4,
                            "amount_to_register": 150 * 4,
                        },
                        5: {
                            "stake_amount": 150 * 5,
                            "total_winning_amount": 10000 * 5,
                            "total_amount": 150 * 5,
                            "amount_to_register": 150 * 5,
                        },
                    },
                    50000: {
                        1: {
                            "stake_amount": 250 * 1,
                            "total_winning_amount": 50000 * 1,
                            "total_amount": 250 * 1,
                            "amount_to_register": 250 * 1,
                        },
                        2: {
                            "stake_amount": 250 * 2,
                            "total_winning_amount": 50000 * 2,
                            "total_amount": 250 * 2,
                            "amount_to_register": 250 * 2,
                        },
                        3: {
                            "stake_amount": 250 * 3,
                            "total_winning_amount": 50000 * 3,
                            "total_amount": 250 * 3,
                            "amount_to_register": 250 * 3,
                        },
                        4: {
                            "stake_amount": 250 * 4,
                            "total_winning_amount": 50000 * 4,
                            "total_amount": 250 * 4,
                            "amount_to_register": 250 * 4,
                        },
                        5: {
                            "stake_amount": 250 * 5,
                            "total_winning_amount": 50000 * 5,
                            "total_amount": 250 * 5,
                            "amount_to_register": 250 * 5,
                        },
                    },
                    100000: {
                        1: {
                            "stake_amount": 600 * 1,
                            "total_winning_amount": 100000 * 1,
                            "total_amount": 600 * 1,
                            "amount_to_register": 600 * 1,
                        },
                        2: {
                            "stake_amount": 600 * 2,
                            "total_winning_amount": 100000 * 2,
                            "total_amount": 600 * 2,
                            "amount_to_register": 600 * 2,
                        },
                        3: {
                            "stake_amount": 600 * 3,
                            "total_winning_amount": 100000 * 3,
                            "total_amount": 600 * 3,
                            "amount_to_register": 600 * 3,
                        },
                        4: {
                            "stake_amount": 600 * 4,
                            "total_winning_amount": 100000 * 4,
                            "total_amount": 600 * 4,
                            "amount_to_register": 600 * 4,
                        },
                        5: {
                            "stake_amount": 600 * 5,
                            "total_winning_amount": 100000 * 5,
                            "total_amount": 600 * 5,
                            "amount_to_register": 600 * 5,
                        },
                    },
                    200000: {
                        1: {
                            "stake_amount": 1200 * 1,
                            "total_winning_amount": 200000 * 1,
                            "total_amount": 1200 * 1,
                            "amount_to_register": 1200 * 1,
                        },
                        2: {
                            "stake_amount": 1200 * 2,
                            "total_winning_amount": 200000 * 2,
                            "total_amount": 1200 * 2,
                            "amount_to_register": 1200 * 2,
                        },
                        3: {
                            "stake_amount": 1200 * 3,
                            "total_winning_amount": 200000 * 3,
                            "total_amount": 1200 * 3,
                            "amount_to_register": 1200 * 3,
                        },
                        4: {
                            "stake_amount": 1200 * 4,
                            "total_winning_amount": 200000 * 4,
                            "total_amount": 1200 * 4,
                            "amount_to_register": 1200 * 4,
                        },
                        5: {
                            "stake_amount": 1200 * 5,
                            "total_winning_amount": 200000 * 5,
                            "total_amount": 1200 * 5,
                            "amount_to_register": 1200 * 5,
                        },
                    },
                }

                return data

        elif channel == "USSD":
            if band is not None and no_of_line is not None:
                if band == 10000:
                    stake_amount = 150

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)

                elif band == 50000:
                    stake_amount = 250

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)

                elif band == 100000:
                    stake_amount = 600

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)
                elif band == 200000:
                    stake_amount = 1200

                    prices = {
                        1: {
                            "stake_amount": stake_amount * 1,
                            "total_winning_amount": band * 1,
                            "amount_to_register": stake_amount * 1,
                        },
                        2: {
                            "stake_amount": stake_amount * 2,
                            "total_winning_amount": band * 2,
                            "amount_to_register": stake_amount * 2,
                        },
                        3: {
                            "stake_amount": stake_amount * 3,
                            "total_winning_amount": band * 3,
                            "amount_to_register": stake_amount * 3,
                        },
                        4: {
                            "stake_amount": stake_amount * 4,
                            "total_winning_amount": band * 4,
                            "amount_to_register": stake_amount * 4,
                        },
                        5: {
                            "stake_amount": stake_amount * 5,
                            "total_winning_amount": band * 5,
                            "amount_to_register": stake_amount * 5,
                        },
                    }

                    return prices.get(no_of_line)

                else:
                    return None

            else:
                data = {
                    10000: {
                        1: {
                            "stake_amount": 150 * 1,
                            "total_winning_amount": 10000 * 1,
                            "total_amount": 150 * 1,
                            "amount_to_register": 150 * 1,
                        },
                        2: {
                            "stake_amount": 150 * 2,
                            "total_winning_amount": 10000 * 2,
                            "total_amount": 150 * 2,
                            "amount_to_register": 150 * 2,
                        },
                        3: {
                            "stake_amount": 150 * 3,
                            "total_winning_amount": 10000 * 3,
                            "total_amount": 150 * 3,
                            "amount_to_register": 150 * 3,
                        },
                        4: {
                            "stake_amount": 150 * 4,
                            "total_winning_amount": 10000 * 4,
                            "total_amount": 150 * 4,
                            "amount_to_register": 150 * 4,
                        },
                        5: {
                            "stake_amount": 150 * 5,
                            "total_winning_amount": 10000 * 5,
                            "total_amount": 150 * 5,
                            "amount_to_register": 150 * 5,
                        },
                    },
                    50000: {
                        1: {
                            "stake_amount": 250 * 1,
                            "total_winning_amount": 50000 * 1,
                            "total_amount": 250 * 1,
                            "amount_to_register": 250 * 1,
                        },
                        2: {
                            "stake_amount": 250 * 2,
                            "total_winning_amount": 50000 * 2,
                            "total_amount": 250 * 2,
                            "amount_to_register": 250 * 2,
                        },
                        3: {
                            "stake_amount": 250 * 3,
                            "total_winning_amount": 50000 * 3,
                            "total_amount": 250 * 3,
                            "amount_to_register": 250 * 3,
                        },
                        4: {
                            "stake_amount": 250 * 4,
                            "total_winning_amount": 50000 * 4,
                            "total_amount": 250 * 4,
                            "amount_to_register": 250 * 4,
                        },
                        5: {
                            "stake_amount": 250 * 5,
                            "total_winning_amount": 50000 * 5,
                            "total_amount": 250 * 5,
                            "amount_to_register": 250 * 5,
                        },
                    },
                    100000: {
                        1: {
                            "stake_amount": 600 * 1,
                            "total_winning_amount": 100000 * 1,
                            "total_amount": 600 * 1,
                            "amount_to_register": 600 * 1,
                        },
                        2: {
                            "stake_amount": 600 * 2,
                            "total_winning_amount": 100000 * 2,
                            "total_amount": 600 * 2,
                            "amount_to_register": 600 * 2,
                        },
                        3: {
                            "stake_amount": 600 * 3,
                            "total_winning_amount": 100000 * 3,
                            "total_amount": 600 * 3,
                            "amount_to_register": 600 * 3,
                        },
                        4: {
                            "stake_amount": 600 * 4,
                            "total_winning_amount": 100000 * 4,
                            "total_amount": 600 * 4,
                            "amount_to_register": 600 * 4,
                        },
                        5: {
                            "stake_amount": 600 * 5,
                            "total_winning_amount": 100000 * 5,
                            "total_amount": 600 * 5,
                            "amount_to_register": 600 * 5,
                        },
                    },
                    200000: {
                        1: {
                            "stake_amount": 1200 * 1,
                            "total_winning_amount": 200000 * 1,
                            "total_amount": 1200 * 1,
                            "amount_to_register": 1200 * 1,
                        },
                        2: {
                            "stake_amount": 1200 * 2,
                            "total_winning_amount": 200000 * 2,
                            "total_amount": 1200 * 2,
                            "amount_to_register": 1200 * 2,
                        },
                        3: {
                            "stake_amount": 1200 * 3,
                            "total_winning_amount": 200000 * 3,
                            "total_amount": 1200 * 3,
                            "amount_to_register": 1200 * 3,
                        },
                        4: {
                            "stake_amount": 1200 * 4,
                            "total_winning_amount": 200000 * 4,
                            "total_amount": 1200 * 4,
                            "amount_to_register": 1200 * 4,
                        },
                        5: {
                            "stake_amount": 1200 * 5,
                            "total_winning_amount": 200000 * 5,
                            "total_amount": 1200 * 5,
                            "amount_to_register": 1200 * 5,
                        },
                    },
                }

                return data

        else:
            return None

    @classmethod
    def bands_base_amount(cls, channel):
        # if channel == "POS":

        wyse_cash_ticket_price = cls.ticket_price(channel=channel)

        data = {}
        for item in wyse_cash_ticket_price.values():
            data[f'{int(item[1].get("total_amount"))}'] = []

        return data

    @classmethod
    def ussd_top_pontential_winnings_amount(cls):
        wyse_cash_prices = cls.ticket_price(channel="USSD")
        top_pontential_winnings_amount = []
        for item in wyse_cash_prices.values():
            top_pontential_winnings_amount.append(currency_formatter(int(item[5].get("total_winning_amount"))))

        return top_pontential_winnings_amount

    @classmethod
    def ussd_wyse_cash_ticket_price(cls, band, index_range: list):
        wyse_cash_prices = cls.ticket_price(channel="USSD")

        # print("index_range", index_range, "band", band)
        data = {}
        ticket_item = wyse_cash_prices.get(band)
        # print("ticket_item", ticket_item)
        if ticket_item is None:
            return None

        for index in index_range:
            data[index] = {
                "ticket_price": currency_formatter(ticket_item[index].get("total_amount")),
                "potential_winning": big_digit_currency_formatter(ticket_item[index].get("total_winning_amount")),
            }

        return data

    @classmethod
    def wyse_cash_bands(cls, channel):
        wyse_cash_ticket_price = cls.ticket_price(channel=channel)

        data = []
        for key, value in wyse_cash_ticket_price.items():
            data.append(key)

        return data

    @classmethod
    def wyse_cash_base_stake_amount_with_bands(cls, channel):
        wyse_cash_ticket_price = cls.ticket_price(channel=channel)

        data = {}
        for key, item in wyse_cash_ticket_price.items():
            data[f'{item[1].get("total_amount")}'] = key

        return data

    @classmethod
    def wyse_cash_bands_with_stake_amoun_as_value(cls, channel):
        wyse_cash_ticket_price = cls.ticket_price(channel=channel)

        data = {}
        for key, item in wyse_cash_ticket_price.items():
            # data[f'{item[1].get("total_amount")}'] = key

            data[key] = f'{item[1].get("total_amount")}'

        return data


@dataclass
class VirtualSoccerPriceModel:
    def ticket_price(cls, channel, ticket_line=None):
        from main.models import ConstantVariable

        try:
            const_obj = ConstantVariable.objects.last()
            divisor = const_obj.icash_winnings_divisor
        except Exception:
            divisor = 1

        if channel == "POS":
            # instant_cashout_price_model = InstantCashOutPriceModel()
            # instany_cashout_price = instant_cashout_price_model.ticket_price(
            #     channel="POS", const_obj=cons_obj
            # )

            # price = {k: instany_cashout_price[k] for k in range(1, 4)}

            price = {
                1: {
                    "ticket_price": 200,
                    "potential_winning": 11250 / divisor,
                    "line_number": 1,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 300,
                    "amount_to_register": 200,
                },
                2: {
                    "ticket_price": 500,
                    "potential_winning": 15200 / divisor,
                    "line_number": 2,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 600,
                    "amount_to_register": 500,
                },
                3: {
                    "ticket_price": 800,
                    "potential_winning": 18000 / divisor,
                    "line_number": 4,
                    "illusion_price": 400,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1200,
                    "amount_to_register": 800,
                },
            }

            if ticket_line is None:
                return price

            return price.get(ticket_line)

        elif channel == "WEB":
            # instant_cashout_price_model = InstantCashOutPriceModel()
            # instany_cashout_price = (
            #     instant_cashout_price_model.ticket_price_with_other_service_price(
            #         channel="WEB", const_obj=cons_obj
            #     )
            # )

            # price = {k: instany_cashout_price[k] for k in range(1, 4)}
            try:
                const_obj = ConstantVariable.objects.last()
                divisor = const_obj.icash_winnings_divisor
            except Exception:
                divisor = 1

            price = {
                1: {
                    "ticket_price": 200,
                    "potential_winning": 11250 / divisor,
                    "line_number": 1,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 300,
                    "amount_to_register": 200,
                },
                2: {
                    "ticket_price": 500,
                    "potential_winning": 15200 / divisor,
                    "line_number": 2,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 600,
                    "amount_to_register": 500,
                },
                3: {
                    "ticket_price": 800,
                    "potential_winning": 18000 / divisor,
                    "line_number": 4,
                    "illusion_price": 400,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1200,
                    "amount_to_register": 800,
                },
            }

            if ticket_line is None:
                return price

            return price.get(ticket_line)

        else:
            return None

    def get_ticket_price_details_with_stake_amount(cls, channel, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)

        # if channel == "POS":
        #     ticket_p = cls.ticket_price(channel)
        # else:
        #     ticket_p = cls.ticket_price_with_other_service_price(channel)

        ticket_p = cls.ticket_price(channel)

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[
                float(value["ticket_price"])
                + float(value.get("illusion_price", 0))
                + float(value.get("woven_service_charge", 0))
                + float(value.get("africastalking_charge", 0))
            ] = value

        return price_re_structure.get(_stake_amount)


@dataclass
class QuikaPriceModel:
    def ticket_price(cls, channel, ticket_line=None):
        from main.models import ConstantVariable

        try:
            const_obj = ConstantVariable.objects.last()
            divisor = const_obj.icash_winnings_divisor
        except Exception:
            divisor = 1

        if channel == "POS":
            # instant_cashout_price_model = InstantCashOutPriceModel()
            # instany_cashout_price = instant_cashout_price_model.ticket_price(
            #     channel="POS", const_obj=cons_obj
            # )

            # price = {k: instany_cashout_price[k] for k in range(1, 4)}

            price = {
                1: {
                    "ticket_price": 200,
                    "potential_winning": 11250 / divisor,
                    "line_number": 1,
                    "ticket_line": 1,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 300,
                    "amount_to_register": 200,
                },
                2: {
                    "ticket_price": 200,
                    "potential_winning": 15200 / divisor,
                    "line_number": 1,
                    "ticket_line": 1,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 600,
                    "amount_to_register": 500,
                },
                4: {
                    "ticket_price": 800,
                    "potential_winning": 18000 / divisor,
                    "line_number": 4,
                    "ticket_line": 1,
                    "illusion_price": 400,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1200,
                    "amount_to_register": 800,
                },
            }

            if ticket_line is None:
                return price

            return price.get(ticket_line)

        elif channel == "WEB":
            # instant_cashout_price_model = InstantCashOutPriceModel()
            # instany_cashout_price = (
            #     instant_cashout_price_model.ticket_price_with_other_service_price(
            #         channel="WEB", const_obj=cons_obj
            #     )
            # )

            # price = {k: instany_cashout_price[k] for k in range(1, 4)}

            price = {
                1: {
                    "ticket_price": 200,
                    "potential_winning": 11250 / divisor,
                    "line_number": 1,
                    "ticket_line": 1,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 300,
                    "amount_to_register": 200,
                },
                2: {
                    "ticket_price": 500,
                    "potential_winning": 15200 / divisor,
                    "line_number": 2,
                    "ticket_line": 1,
                    "illusion_price": 100,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 600,
                    "amount_to_register": 500,
                },
                3: {
                    "ticket_price": 800,
                    "potential_winning": 18000 / divisor,
                    "line_number": 4,
                    "ticket_line": 1,
                    "illusion_price": 400,
                    "woven_service_charge": 0,
                    "africastalking_charge": 0,
                    "total_amount": 1200,
                    "amount_to_register": 800,
                },
            }

            if ticket_line is None:
                return price

            return price.get(ticket_line)

        else:
            return None

    def get_ticket_price_details_with_stake_amount(cls, channel, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)

        # if channel == "POS":
        #     ticket_p = cls.ticket_price(channel)
        # else:
        #     ticket_p = cls.ticket_price_with_other_service_price(channel)

        ticket_p = cls.ticket_price(channel)

        print("_stake_amount", _stake_amount, "\n\n\n\n")

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[value["total_amount"]] = value

        return price_re_structure.get(_stake_amount)


@dataclass
class TelcoSalaryForLifePriceModel:
    @classmethod
    def ticket_price(cls, ticket_line=None):
        price = {
            1: {
                "ticket_price": 200,
                "potential_winning": 5000,
                "line_number": 1,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 200,
                "amount_to_register": 150,
            },
            2: {
                "ticket_price": 500,
                "potential_winning": 15000,
                "line_number": 2,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 500,
                "amount_to_register": 350,
            },
            3: {
                "ticket_price": 800,
                "potential_winning": 50000,
                "line_number": 3,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 800,
                "amount_to_register": 700,
            },
            4: {
                "ticket_price": 1200,
                "potential_winning": 150000,
                "line_number": 7,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 1200,
                "amount_to_register": 1400,
            },
        }

        if ticket_line is None:
            return price

        return price[ticket_line]

    @classmethod
    def get_ticket_price_details_with_stake_amount(cls, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)
        # if channel == "POS":
        #     ticket_p = cls.ticket_price(channel)
        # else:
        #     ticket_p = cls.ticket_price_with_other_service_price(channel)

        ticket_p = cls.ticket_price()

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[float(value["total_amount"])] = value

        return price_re_structure.get(_stake_amount)

    @classmethod
    def ussd_pontential_winnings(cls):
        sal_4_life_ticket_prices = cls.ticket_price()
        top_price = []
        top_three_index = [1, 2, 3, 4]
        for index in top_three_index:
            top_price.append(currency_formatter(int(sal_4_life_ticket_prices[index]["potential_winning"])))

        return top_price

    @classmethod
    def ussd_top_prices_in_threes(cls, index_range: list):
        sal_4_life_ticket_prices = cls.ticket_price()

        data = {}
        for index in index_range:
            data[index] = {
                "ticket_price": currency_formatter(
                    sal_4_life_ticket_prices.get(index).get("ticket_price")
                    + sal_4_life_ticket_prices.get(index).get("woven_service_charge", 0)
                    + sal_4_life_ticket_prices.get(index).get("africastalking_charge", 0)
                ),
                "potential_winning": big_digit_currency_formatter(sal_4_life_ticket_prices.get(index).get("potential_winning")),
            }

        return data

    @classmethod
    def get_bbc_service_and_prodct_details(cls, stake_amount):
        data = {
            200: [234102200006770, 23410220000024648],
            500: [234102200006770, 23410220000024649],
            800: [234102200006770, 23410220000024650],
            1200: [234102200006770, 23410220000024651],
        }

        return data[stake_amount]

    @classmethod
    def subscription_plan_service_and_plan_id(cls, amount):
        # # [service_id, plan_id]
        # data = {
        #     50: [234102200006962, 23410220000027464],
        #     75: [234102200006962, 23410220000027465],
        # }
        data = {
            50: [234102200006962, 23410220000027464],
            75: [234102200006962, 23410220000027464],
            100: [234102200006962, 23410220000027465],
        }

        return data[amount]

    @classmethod
    def subscription_ticket_prices(cls):
        price = {
            1: {
                "ticket_price": 50,
                "potential_winning": 2000,
                "line_number": 1,
                "total_amount": 50,
                "amount_to_register": 150,
                "type": "subscription",
            },
            2: {
                "ticket_price": 100,
                "potential_winning": 3000,
                "line_number": 2,
                "total_amount": 100,
                "amount_to_register": 150,
                "type": "subscription",
            },
            3: {
                "ticket_price": 200,
                "potential_winning": 5000,
                "line_number": 1,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 200,
                "amount_to_register": 150,
                "type": "on-demand",
            },
        }

        return price

    @classmethod
    def get_subscription_ticket_price_details_with_stake_amount(cls, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)

        ticket_p = cls.subscription_ticket_prices()

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[
                float(value["ticket_price"])
                + float(value.get("illusion_price", 0))
                + float(value.get("woven_service_charge", 0))
                + float(value.get("africastalking_charge", 0))
            ] = value

        return price_re_structure.get(_stake_amount)

    @classmethod
    def ussd_top_subscription_prices_in_threes(cls, index_range: list):
        """
        This method is used to get the top three subscription prices for the ussd

        :param index_range: list [1,2,3]
        :return: dict
        """
        sal_4_life_ticket_prices = cls.subscription_ticket_prices()

        data = {}
        for index in index_range:
            data[index] = {
                "ticket_price": currency_formatter(sal_4_life_ticket_prices.get(index).get("ticket_price")),
                "potential_winning": big_digit_currency_formatter(sal_4_life_ticket_prices.get(index).get("potential_winning")),
                "type": sal_4_life_ticket_prices.get(index).get("type"),
            }

        return data


@dataclass
class TelcoInstantCashOutPriceModel:
    @classmethod
    def ticket_price_with_other_service_price(cls, ticket_line=None):
        from main.models import ConstantVariable

        try:
            const_obj = ConstantVariable.objects.last()
            divisor = const_obj.icash_winnings_divisor
        except Exception:
            divisor = 1

        # price = {
        # 1: {
        #     "ticket_price": 300,
        #     "potential_winning": 11250 / divisor,
        #     "line_number": 1,
        #     "illusion_price": 0,
        #     "woven_service_charge": 0,
        #     "africastalking_charge": 0,
        #     "total_amount": 300,
        #     "amount_to_register": 200,
        # },
        # 2: {
        #     "ticket_price": 750,
        #     "potential_winning": 15750 / divisor,
        #     "line_number": 3,
        #     "illusion_price": 0,
        #     "woven_service_charge": 0,
        #     "africastalking_charge": 0,
        #     "total_amount": 750,
        #     "amount_to_register": 500,
        # },
        # 3: {
        #     "ticket_price": 900,
        #     "potential_winning": 18000 / divisor,
        #     "line_number": 4,
        #     "illusion_price": 0,
        #     "woven_service_charge": 0,
        #     "africastalking_charge": 0,
        #     "total_amount": 900,
        #     "amount_to_register": 800,
        # },
        #     4: {
        #         "ticket_price": 1000,
        #         "potential_winning": 27000 / divisor,
        #         "line_number": 7,
        #         "illusion_price": 0,
        #         "woven_service_charge": 0,
        #         "africastalking_charge": 0,
        #         "total_amount": 1000,
        #         "amount_to_register": 1400,
        #     },
        # }

        price = {
            1: {
                "ticket_price": 150,
                "potential_winning": 12500,
                "line_number": 1,
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 150,
                "amount_to_register": 200,
            },
            2: {
                "ticket_price": 300,
                "potential_winning": 11250 / divisor,
                "line_number": 1,
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 300,
                "amount_to_register": 200,
            },
            3: {
                "ticket_price": 750,
                "potential_winning": 15750 / divisor,
                "line_number": 3,
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 750,
                "amount_to_register": 500,
            },
            4: {
                "ticket_price": 900,
                "potential_winning": 18000 / divisor,
                "line_number": 4,
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 900,
                "amount_to_register": 800,
            },
        }

        if ticket_line is None:
            return price

        return price[ticket_line]

        return None

    @classmethod
    def ticket_price(cls, ticket_line=None):
        from main.models import ConstantVariable

        try:
            const_obj = ConstantVariable.objects.last()
            divisor = const_obj.icash_winnings_divisor
        except Exception:
            divisor = 1

        price = {
            1: {
                "ticket_price": 150,
                "potential_winning": 11250 / divisor,
                "line_number": 1,
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 150,
                "amount_to_register": 150,
            },
            2: {
                "ticket_price": 300,
                "potential_winning": 15200 / divisor,
                "line_number": 2,
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 300,
                "amount_to_register": 200,
            },
            3: {
                "ticket_price": 500,
                "potential_winning": 15750 / divisor,
                "line_number": 3,
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 500,
                "amount_to_register": 350,
            },
            4: {
                "ticket_price": 750,
                "potential_winning": 18000 / divisor,
                "line_number": 4,
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 750,
                "amount_to_register": 500,
            },
            5: {
                "ticket_price": 900,
                "potential_winning": 18750 / divisor,
                "line_number": 5,
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 900,
                "amount_to_register": 800,
            },
            6: {
                "ticket_price": 1000,
                "potential_winning": 25000 / divisor,
                "line_number": 6,
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 1400,
            },
        }

        if ticket_line is None:
            return price
        return price[ticket_line]

        return None

    @classmethod
    def get_ticket_price_details_with_stake_amount(cls, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)

        ticket_p = cls.ticket_price_with_other_service_price()

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[
                float(value["ticket_price"])
                + float(value.get("illusion_price", 0))
                + float(value.get("woven_service_charge", 0))
                + float(value.get("africastalking_charge", 0))
            ] = value

        # print(
        #     f"""
        # price_re_structure: {price_re_structure}
        # _stake_amount: {_stake_amount}
        # """
        # )

        return price_re_structure.get(_stake_amount)

    @classmethod
    def ussd_top_prices_in_threes(cls, index_range: list):
        ticket_price = cls.ticket_price_with_other_service_price()
        price = {}
        for i in index_range:
            if i == 5:
                pass
            else:
                price[i] = ticket_price[i]

        return price

    @classmethod
    def get_pontential_winnings(cls):
        ticket_price = cls.ticket_price_with_other_service_price()
        price = []
        for i in range(1, 5):
            price.append(currency_formatter(int(ticket_price[i]["potential_winning"])))

        return price

    @classmethod
    def white_winning(cls, const_table_instance=None) -> dict:
        amount_data = {
            200: {
                "least_win": 250,
                "minor_win": 650,
                # "min_win": round_down(4500 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(5400 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(11250 / const_table_instance.icash_winnings_divisor),
            },
            500: {
                "least_win": 1000,
                "minor_win": 1250,
                # "min_win": round_down(7000 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(9000 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(15200 / const_table_instance.icash_winnings_divisor),
            },
            750: {
                "least_win": 1400,
                "minor_win": 1700,
                # "min_win": round_down(9000 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(10800 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(15750 / const_table_instance.icash_winnings_divisor),
            },
            800: {
                "least_win": 2000,
                "minor_win": 2250,
                # "min_win": round_down(11400 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(13500 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(18000 / const_table_instance.icash_winnings_divisor),
            },
            1250: {
                "least_win": 1250,
                "minor_win": 1950,
                # "min_win": round_down(12000 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(14000 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(18750 / const_table_instance.icash_winnings_divisor),
            },
            1300: {
                "least_win": 1300,
                "minor_win": 1700,
                # "min_win": round_down(13500 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(15000 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(25000 / const_table_instance.icash_winnings_divisor),
            },
            1400: {
                "least_win": 1500,
                "minor_win": 1800,
                # "min_win": round_down(15000 / const_table_instance.icash_winnings_divisor),
                # "mid_win": round_down(18000 / const_table_instance.icash_winnings_divisor),
                # "max_win": round_down(27000 / const_table_instance.icash_winnings_divisor),
            },
        }
        return amount_data

    @classmethod
    def amount(cls, const_table_instance=None):
        amount_data = {
            200: {
                "least_win": 300,
                "minor_win": 500,
                "min_win": 800,
                "mid_win": 1000,
                "max_win": 1000,
            },
            500: {
                "least_win": 1000,
                "minor_win": 1200,
                "min_win": 1700,
                "mid_win": 1500,
                "max_win": 1800,
            },
            750: {
                "least_win": 1125,
                "minor_win": 1200,
                # "min_win": round_down(9000 / (const_table_instance.icash_winnings_divisor * 1.666666666666667)),
                # "mid_win": round_down(10800 / (const_table_instance.icash_winnings_divisor * 1.666666666666667)),
                # "max_win": round_down(15750 / (const_table_instance.icash_winnings_divisor * 1.666666666666667)),
            },
            800: {
                "least_win": 1200,
                "minor_win": 1500,
                "min_win": 2000,
                "mid_win": 1800,
                "max_win": 2000,
            },
            1250: {
                "least_win": 2500,
                "minor_win": 2350,
                "min_win": 2200,
                "mid_win": 2500,
                "max_win": 3500,
            },
            1300: {
                "least_win": 2750,
                "minor_win": 1800,
                "min_win": 2000,
                "mid_win": 3000,
                "max_win": 4000,
            },
            1400: {
                "least_win": 2800,
                "minor_win": 2000,
                "min_win": 2500,
                "mid_win": 3500,
                "max_win": 4000,
            },
        }
        return amount_data

    def lotto_amount_line_reg(self) -> dict:
        """
        Stake amount is the amount received from the player
        Reg amount is the amount registered on the lotto table
        illusion amount still remains the illusion amount if the game is expected to have the illusion feature
        """
        lines_amounts = {
            1: {"stake_amount": 300, "reg_amount": 200, "illusion": 100},
            2: {"stake_amount": 600, "reg_amount": 500, "illusion": 100},
            3: {"stake_amount": 850, "reg_amount": 750, "illusion": 100},
            4: {"stake_amount": 1200, "reg_amount": 800, "illusion": 400},
            5: {"stake_amount": 1450, "reg_amount": 1250, "illusion": 200},
            6: {"stake_amount": 1500, "reg_amount": 1300, "illusion": 200},
            7: {"stake_amount": 1700, "reg_amount": 1400, "illusion": 300},
        }

        return lines_amounts

    @classmethod
    def get_bbc_service_and_prodct_details(cls, stake_amount):
        data = {
            150: [234102200006767, 23410220000024635],
            200: [234102200006767, 23410220000024635],
            300: [234102200006767, 23410220000024636],
            500: [234102200006767, 23410220000024637],
            750: [234102200006767, 23410220000024638],
            900: [234102200006767, 23410220000024639],
            1000: [234102200006767, 23410220000024640],
        }

        return data[stake_amount]

    @classmethod
    def subscription_plan_service_and_plan_id(cls, amount):
        # [service_id, plan_id]
        data = {
            # 50: [234102200006961, 23410220000027462],
            # 75: [234102200006961, 23410220000027463],
            75: [234102200006961, 23410220000027462],
            100: [234102200006961, 23410220000027463],
        }

        try:
            return data[amount]
        except Exception:
            data = {
                50: [234102200006961, 23410220000027462],
                75: [234102200006961, 23410220000027463],
            }
            return data[amount]

    @classmethod
    def subscription_ticket_prices(cls):
        price = {
            1: {
                "ticket_price": 50,
                "potential_winning": 4100,
                "line_number": 1,
                "total_amount": 50,
                "type": "subscription",
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "amount_to_register": 200,
            },
            2: {
                "ticket_price": 100,
                "potential_winning": 6250,
                "line_number": 2,
                "total_amount": 100,
                "type": "subscription",
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "amount_to_register": 200,
            },
            3: {
                "ticket_price": 150,
                "potential_winning": 11250,
                "line_number": 3,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 200,
                "type": "on-demand",
                "illusion_price": 0,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "amount_to_register": 200,
            },
        }

        return price

    @classmethod
    def ussd_top_subscription_prices_in_threes(cls, index_range: list):
        """
        This method is used to get the top three subscription prices for the ussd

        :param index_range: list [1,2,3]
        :return: dict
        """
        sal_4_life_ticket_prices = cls.subscription_ticket_prices()

        data = {}
        for index in index_range:
            data[index] = {
                "ticket_price": currency_formatter(sal_4_life_ticket_prices.get(index).get("ticket_price")),
                "potential_winning": big_digit_currency_formatter(sal_4_life_ticket_prices.get(index).get("potential_winning")),
                "type": sal_4_life_ticket_prices.get(index).get("type"),
            }

        return data

    @classmethod
    def get_subscription_ticket_price_details_with_stake_amount(cls, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)

        ticket_p = cls.subscription_ticket_prices()

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[
                float(value["ticket_price"])
                + float(value.get("illusion_price", 0))
                + float(value.get("woven_service_charge", 0))
                + float(value.get("africastalking_charge", 0))
            ] = value

        return price_re_structure.get(_stake_amount)


@dataclass
class TelcoWyseCashPriceModel:
    @classmethod
    def ticket_price(cls, band=None, no_of_line=None):
        if band is not None and no_of_line is not None:
            if band == 10000:
                stake_amount = 300

                prices = {
                    1: {
                        "stake_amount": stake_amount * 1,
                        "total_winning_amount": 5000 * 1,
                        "amount_to_register": 150,
                    },
                    2: {
                        "stake_amount": stake_amount * 2,
                        "total_winning_amount": 5000 * 2,
                        "amount_to_register": 150,
                    },
                    3: {
                        "stake_amount": stake_amount * 3,
                        "total_winning_amount": 5000 * 3,
                        "amount_to_register": 150,
                    },
                    4: {
                        "stake_amount": stake_amount * 4,
                        "total_winning_amount": 5000 * 4,
                        "amount_to_register": 150,
                    },
                    5: {
                        "stake_amount": stake_amount * 5,
                        "total_winning_amount": 5000 * 5,
                        "amount_to_register": 150,
                    },
                }

                return prices.get(no_of_line)

            elif band == 50000:
                stake_amount = 500

                prices = {
                    1: {
                        "stake_amount": stake_amount * 1,
                        "total_winning_amount": 25000 * 1,
                        "amount_to_register": 250,
                    },
                    2: {
                        "stake_amount": stake_amount * 2,
                        "total_winning_amount": 25000 * 2,
                        "amount_to_register": 250,
                    },
                    3: {
                        "stake_amount": stake_amount * 3,
                        "total_winning_amount": 25000 * 3,
                        "amount_to_register": 250,
                    },
                    4: {
                        "stake_amount": stake_amount * 4,
                        "total_winning_amount": 25000 * 4,
                        "amount_to_register": 250,
                    },
                    5: {
                        "stake_amount": stake_amount * 5,
                        "total_winning_amount": 25000 * 5,
                        "amount_to_register": 250,
                    },
                }

                return prices.get(no_of_line)

            elif band == 100000:
                stake_amount = 700

                prices = {
                    1: {
                        "stake_amount": stake_amount * 1,
                        "total_winning_amount": 50000 * 1,
                        "amount_to_register": 600,
                    },
                    2: {
                        "stake_amount": stake_amount * 2,
                        "total_winning_amount": 50000 * 2,
                        "amount_to_register": 600,
                    },
                    3: {
                        "stake_amount": stake_amount * 3,
                        "total_winning_amount": 50000 * 3,
                        "amount_to_register": 600,
                    },
                    4: {
                        "stake_amount": stake_amount * 4,
                        "total_winning_amount": 50000 * 4,
                        "amount_to_register": 600,
                    },
                    5: {
                        "stake_amount": stake_amount * 5,
                        "total_winning_amount": 50000 * 5,
                        "amount_to_register": 600,
                    },
                }

                return prices.get(no_of_line)
            elif band == 200000:
                stake_amount = 1000

                prices = {
                    1: {
                        "stake_amount": stake_amount * 1,
                        "total_winning_amount": 100000 * 1,
                        "amount_to_register": 1200,
                    },
                    2: {
                        "stake_amount": stake_amount * 2,
                        "total_winning_amount": 100000 * 2,
                        "amount_to_register": 1200,
                    },
                    3: {
                        "stake_amount": stake_amount * 3,
                        "total_winning_amount": 100000 * 3,
                        "amount_to_register": 1200,
                    },
                    4: {
                        "stake_amount": stake_amount * 4,
                        "total_winning_amount": 100000 * 4,
                        "amount_to_register": 1200,
                    },
                    5: {
                        "stake_amount": stake_amount * 5,
                        "total_winning_amount": 100000 * 5,
                        "amount_to_register": 1200,
                    },
                }

                return prices.get(no_of_line)

            else:
                return None

        else:
            data = {
                10000: {
                    1: {
                        "stake_amount": 300 * 1,
                        "total_winning_amount": 5000 * 1,
                        "total_amount": 300 * 1,
                        "amount_to_register": 150,
                    },
                    2: {
                        "stake_amount": 300 * 2,
                        "total_winning_amount": 5000 * 2,
                        "total_amount": 300 * 2,
                        "amount_to_register": 150,
                    },
                    3: {
                        "stake_amount": 300 * 3,
                        "total_winning_amount": 5000 * 3,
                        "total_amount": 300 * 3,
                        "amount_to_register": 150,
                    },
                    4: {
                        "stake_amount": 300 * 4,
                        "total_winning_amount": 5000 * 4,
                        "total_amount": 300 * 4,
                        "amount_to_register": 150,
                    },
                    5: {
                        "stake_amount": 300 * 5,
                        "total_winning_amount": 5000 * 5,
                        "total_amount": 300 * 5,
                        "amount_to_register": 150,
                    },
                },
                50000: {
                    1: {
                        "stake_amount": 500 * 1,
                        "total_winning_amount": 25000 * 1,
                        "total_amount": 500 * 1,
                        "amount_to_register": 250,
                    },
                    2: {
                        "stake_amount": 500 * 2,
                        "total_winning_amount": 25000 * 2,
                        "total_amount": 500 * 2,
                        "amount_to_register": 250,
                    },
                    3: {
                        "stake_amount": 500 * 3,
                        "total_winning_amount": 25000 * 3,
                        "total_amount": 500 * 3,
                        "amount_to_register": 250,
                    },
                    4: {
                        "stake_amount": 500 * 4,
                        "total_winning_amount": 25000 * 4,
                        "total_amount": 500 * 4,
                        "amount_to_register": 250,
                    },
                    5: {
                        "stake_amount": 500 * 5,
                        "total_winning_amount": 25000 * 5,
                        "total_amount": 500 * 5,
                        "amount_to_register": 250,
                    },
                },
                100000: {
                    1: {
                        "stake_amount": 700 * 1,
                        "total_winning_amount": 50000 * 1,
                        "total_amount": 700 * 1,
                        "amount_to_register": 600,
                    },
                    2: {
                        "stake_amount": 700 * 2,
                        "total_winning_amount": 50000 * 2,
                        "total_amount": 700 * 2,
                        "amount_to_register": 600,
                    },
                    3: {
                        "stake_amount": 700 * 3,
                        "total_winning_amount": 50000 * 3,
                        "total_amount": 700 * 3,
                        "amount_to_register": 600,
                    },
                    4: {
                        "stake_amount": 700 * 4,
                        "total_winning_amount": 50000 * 4,
                        "total_amount": 700 * 4,
                        "amount_to_register": 600,
                    },
                    5: {
                        "stake_amount": 700 * 5,
                        "total_winning_amount": 50000 * 5,
                        "total_amount": 700 * 5,
                        "amount_to_register": 600,
                    },
                },
                200000: {
                    1: {
                        "stake_amount": 1000 * 1,
                        "total_winning_amount": 100000 * 1,
                        "total_amount": 1000 * 1,
                        "amount_to_register": 1200,
                    },
                    2: {
                        "stake_amount": 1000 * 2,
                        "total_winning_amount": 100000 * 2,
                        "total_amount": 1000 * 2,
                        "amount_to_register": 1200,
                    },
                    3: {
                        "stake_amount": 1000 * 3,
                        "total_winning_amount": 100000 * 3,
                        "total_amount": 1000 * 3,
                        "amount_to_register": 1200,
                    },
                    4: {
                        "stake_amount": 1000 * 4,
                        "total_winning_amount": 100000 * 4,
                        "total_amount": 1000 * 4,
                        "amount_to_register": 1200,
                    },
                    5: {
                        "stake_amount": 1000 * 5,
                        "total_winning_amount": 100000 * 5,
                        "total_amount": 1000 * 5,
                        "amount_to_register": 1200,
                    },
                },
            }

            return data

    @classmethod
    def bands_base_amount(cls):
        # if channel == "POS":

        wyse_cash_ticket_price = cls.ticket_price()

        data = {}
        for item in wyse_cash_ticket_price.values():
            data[f'{int(item[1].get("total_amount"))}'] = []

        return data

    @classmethod
    def ussd_top_pontential_winnings_amount(cls):
        wyse_cash_prices = cls.ticket_price()
        top_pontential_winnings_amount = []
        for item in wyse_cash_prices.values():
            top_pontential_winnings_amount.append(currency_formatter(int(item[5].get("total_winning_amount"))))

        return top_pontential_winnings_amount

    @classmethod
    def ussd_wyse_cash_ticket_price(cls, band, index_range: list):
        wyse_cash_prices = cls.ticket_price()

        # print("index_range", index_range, "band", band)
        data = {}
        ticket_item = wyse_cash_prices.get(band)
        # print("ticket_item", ticket_item)
        if ticket_item is None:
            return None

        for index in index_range:
            data[index] = {
                "ticket_price": currency_formatter(ticket_item[index].get("total_amount")),
                "potential_winning": big_digit_currency_formatter(ticket_item[index].get("total_winning_amount")),
            }

        return data

    @classmethod
    def wyse_cash_bands(cls):
        wyse_cash_ticket_price = cls.ticket_price()

        data = []
        for key, value in wyse_cash_ticket_price.items():
            data.append(key)

        return data

    @classmethod
    def wyse_cash_base_stake_amount_with_bands(cls):
        wyse_cash_ticket_price = cls.ticket_price()

        data = {}
        for key, item in wyse_cash_ticket_price.items():
            data[f'{item[1].get("total_amount")}'] = key

        return data

    @classmethod
    def wyse_cash_bands_with_stake_amoun_as_value(cls):
        wyse_cash_ticket_price = cls.ticket_price()

        data = {}
        for key, item in wyse_cash_ticket_price.items():
            # data[f'{item[1].get("total_amount")}'] = key

            data[key] = f'{item[1].get("total_amount")}'

        return data

    @classmethod
    def get_bbc_service_and_prodct_details(cls, stake_amount):
        data = {
            300: [234102200006769, 23410220000024643],
            500: [234102200006769, 23410220000024645],
            700: [234102200006769, 23410220000024646],
            1000: [234102200006769, 23410220000024647],
        }

        return data[stake_amount]

    @classmethod
    def subscription_plan_service_and_plan_id(cls, amount):
        # [service_id, plan_id]
        data = {
            50: [234102200006965, 23410220000027470],
            75: [234102200006965, 23410220000027471],
        }

        return data[amount]

    @classmethod
    def subscription_ticket_prices(cls):
        stake_amount = 300
        price = {
            1: {
                "ticket_price": 50,
                "potential_winning": 5000 * 1,
                "line_number": 1,
                "total_amount": 50,
                "amount_to_register": 150,
                "type": "subscription",
            },
            2: {
                "ticket_price": 100,
                "potential_winning": 5000 * 2,
                "line_number": 2,
                "total_amount": 100,
                "amount_to_register": 150,
                "type": "subscription",
            },
            3: {
                "ticket_price": stake_amount * 1,
                "potential_winning": 5000 * 3,
                "line_number": 1,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 200,
                "amount_to_register": 150,
                "type": "on-demand",
            },
            4: {
                "ticket_price": stake_amount * 2,
                "potential_winning": 5000 * 4,
                "line_number": 1,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 200,
                "amount_to_register": 150,
                "type": "on-demand",
            },
            5: {
                "ticket_price": stake_amount * 3,
                "potential_winning": 5000 * 5,
                "line_number": 1,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 200,
                "amount_to_register": 150,
                "type": "on-demand",
            },
        }

        return price

    @classmethod
    def ussd_top_subscription_prices_in_threes(cls, index_range: list):
        """
        This method is used to get the top three subscription prices for the ussd

        :param index_range: list [1,2,3]
        :return: dict
        """
        sal_4_life_ticket_prices = cls.subscription_ticket_prices()

        data = {}
        for index in index_range:
            data[index] = {
                "ticket_price": currency_formatter(sal_4_life_ticket_prices.get(index).get("ticket_price")),
                "potential_winning": big_digit_currency_formatter(sal_4_life_ticket_prices.get(index).get("potential_winning")),
                "type": sal_4_life_ticket_prices.get(index).get("type"),
            }

        return data

    @classmethod
    def ussd_top_prices_in_threes(cls, index_range: list):
        ticket_price = cls.subscription_ticket_prices()
        price = {}
        for i in index_range:
            if i == 5:
                pass
            else:
                price[i] = ticket_price[i]

        return price


@dataclass
class TelcoAwoofPriceModel:
    @classmethod
    def ticket_price(cls, item_price):
        price = {
            15000: 200,
            30000: 200,
            50000: 200,
            100000: 200,
            150000: 200,
            300000: 200,
            500000: 200,
        }

        return price[int(item_price)]

    @classmethod
    def get_bbc_service_and_prodct_details(cls, stake_amount):
        data = {
            200: [234102200006771, 23410220000024652],
            500: [234102200006771, 23410220000024653],
            800: [234102200006771, 23410220000024654],
            1200: [234102200006771, 23410220000024655],
        }

        return data[stake_amount]

    def get_bbc_service_and_prodct_details_for_subscription(cls, stake_amount):
        data = {
            50: [234102200006963, 23410220000027466],
            75: [234102200006963, 23410220000027467],
        }

        try:
            return data[stake_amount]
        except KeyError:
            return [234102200006963, 23410220000027466]

    @classmethod
    def subscription_ticket_prices(cls):
        price = {
            1: {
                "ticket_price": 50,
                "line_number": 1,
                "type": "subscription",
            },
            2: {
                "ticket_price": 100,
                "line_number": 1,
                "type": "subscription",
            },
            3: {
                "ticket_price": 200,
                "line_number": 1,
                "type": "on-demand",
            },
        }

        return price

    @classmethod
    def ticket_price_and_price_type(cls, number):
        prices = cls.subscription_ticket_prices()

        item = prices.get(number)
        if item is None:
            return None, None

        return (
            item.get("ticket_price"),
            True if item.get("type") == "on-demand" else False,
        )


def secure_d_and_upstream_service_and_prodct_details(product_id):
    data = {
        "23410220000024641": {
            "service_name": "WYSE_CASH_150",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 150,
            "product_id": "23410220000024641",
        },
        "23410220000024642": {
            "service_name": "WYSE_CASH_200",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 200,
            "product_id": "23410220000024642",
        },
        "23410220000024643": {
            "service_name": "WYSE_CASH_300",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 300,
            "product_id": "23410220000024643",
            "band": 10000,
        },
        "23410220000024644": {
            "service_name": "WYSE_CASH_400",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 400,
            "product_id": "23410220000024644",
        },
        "23410220000024645": {
            "service_name": "WYSE_CASH_500",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 500,
            "product_id": "23410220000024645",
        },
        "23410220000024646": {
            "service_name": "WYSE_CASH_700",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 700,
            "product_id": "23410220000024646",
        },
        "23410220000024647": {
            "service_name": "WYSE_CASH_1000",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 1000,
            "product_id": "23410220000024647",
        },
        "23410220000024635": {
            "service_name": "INSTANT_CASH_150",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 150,
            "product_id": "23410220000024635",
            "line_number": 1,
        },
        "23410220000024636": {
            "service_name": "INSTANT_CASH_300",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 300,
            "product_id": "23410220000024636",
            "line_number": 1,
        },
        "23410220000024637": {
            "service_name": "INSTANT_CASH_500",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 500,
            "product_id": "23410220000024637",
            "line_number": 2,
        },
        "23410220000024638": {
            "service_name": "INSTANT_CASH_750",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 750,
            "product_id": "23410220000024638",
            "line_number": 3,
        },
        "23410220000024639": {
            "service_name": "INSTANT_CASH_900",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 900,
            "product_id": "23410220000024639",
            "line_number": 4,
        },
        "23410220000024640": {
            "service_name": "INSTANT_CASH_1000",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 1000,
            "product_id": "23410220000024640",
            "line_number": 5,
        },
        "23410220000024656": {
            "service_name": "SOCCER_CASH_100",
            "service_id": "234102200006772",
            "product_name": "SOCCER_CASH",
            "amount": 100,
            "product_id": "23410220000024656",
            "line_number": 1,
        },
        "23410220000024657": {
            "service_name": "SOCCER_CASH_500",
            "service_id": "234102200006772",
            "product_name": "SOCCER_CASH",
            "amount": 500,
            "product_id": "23410220000024657",
            "line_number": 2,
        },
        "23410220000024658": {
            "service_name": "SOCCER_CASH_800",
            "service_id": "234102200006772",
            "product_name": "SOCCER_CASH",
            "amount": 800,
            "product_id": "23410220000024658",
            "line_number": 3,
        },
        "23410220000024659": {
            "service_name": "SOCCER_CASH_1200",
            "service_id": "234102200006772",
            "product_name": "SOCCER_CASH",
            "amount": 1200,
            "product_id": "23410220000024659",
            "line_number": 4,
        },
        "23410220000024648": {
            "service_name": "SALARY_FOR_LIFE_200",
            "service_id": "234102200006770",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 200,
            "product_id": "23410220000024648",
            "line_number": 1,
        },
        "23410220000024649": {
            "service_name": "SALARY_FOR_LIFE_500",
            "service_id": "234102200006770",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 500,
            "product_id": "23410220000024649",
            "line_number": 2,
        },
        "23410220000024650": {
            "service_name": "SALARY_FOR_LIFE_800",
            "service_id": "234102200006770",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 800,
            "product_id": "23410220000024650",
            "line_number": 3,
        },
        "23410220000024651": {
            "service_name": "SALARY_FOR_LIFE_1200",
            "service_id": "234102200006770",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 1200,
            "product_id": "23410220000024651",
            "line_number": 7,
        },
        "23410220000027462": {
            "service_name": "INSTANT_CASHOUT_50",
            "service_id": "234102200006961",
            "product_name": "INSTANT_CASHOUT",
            "amount": 50,
            "product_id": "23410220000027462",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027463": {
            "service_name": "INSTANT_CASHOUT_75",
            "service_id": "234102200006961",
            "product_name": "INSTANT_CASHOUT",
            "amount": 75,
            "product_id": "23410220000027463",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027470": {
            "service_name": "WYSE_CASH_50",
            "service_id": "234102200006965",
            "product_name": "WYSE_CASH",
            "amount": 50,
            "product_id": "23410220000027470",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027471": {
            "service_name": "WYSE_CASH_75",
            "service_id": "234102200006965",
            "product_name": "WYSE_CASH",
            "amount": 75,
            "product_id": "23410220000027471",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027464": {
            "service_name": "SALARY_FOR_LIFE_50",
            "service_id": "234102200006962",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 50,
            "product_id": "23410220000027464",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027465": {
            "service_name": "SALARY_FOR_LIFE_75",
            "service_id": "234102200006962",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 75,
            "product_id": "23410220000027465",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027466": {
            "service_name": "FAST_FINGER_50",
            "service_id": "234102200006963",
            "product_name": "FAST_FINGER",
            "amount": 50,
            "product_id": "23410220000027466",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027467": {
            "service_name": "FAST_FINGER_75",
            "service_id": "234102200006963",
            "product_name": "FAST_FINGER",
            "amount": 75,
            "product_id": "23410220000027467",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027468": {
            "service_name": "SOCCER_CASH_50",
            "service_id": "234102200006964",
            "product_name": "SOCCER_CASH",
            "amount": 75,
            "product_id": "23410220000027468",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027469": {
            "service_name": "SOCCER_CASH_75",
            "service_id": "234102200006964",
            "product_name": "SOCCER_CASH",
            "amount": 100,
            "product_id": "23410220000027469",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "0017182000001707": {
            "service_name": "INSTANT_CASH_50",
            "service_id": "234102200006964",
            "product_name": "INSTANT_CASH",
            "amount": 50,
            "product_id": "0017182000001707",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "GLO",
        },
        "0017182000003867": {
            "service_name": "FAST_FINGER_50",
            "service_id": "234102200006964",
            "product_name": "FAST_FINGER",
            "amount": 50,
            "product_id": "0017182000003867",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "GLO",
        },
        "0017182000003868": {
            "service_name": "SALARY_FOR_LIFE_50",
            "service_id": "234102200006964",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 50,
            "product_id": "0017182000003868",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "GLO",
        },
        "0017182000003869": {
            "service_name": "WYSE_CASH_50",
            "service_id": "234102200006964",
            "product_name": "WYSE_CASH",
            "amount": 50,
            "product_id": "0017182000003869",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "GLO",
        },
        "0017182000003870": {
            "service_name": "SOCCER_CASH_50",
            "service_id": "234102200006964",
            "product_name": "SOCCER_CASH",
            "amount": 50,
            "product_id": "0017182000003870",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "GLO",
        },
    }

    return data.get(product_id)


def new_secure_d_and_upstream_service_and_prodct_details(product_id):
    data = {
        "23410220000024641": {
            "service_name": "WYSE_CASH_150",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 150,
            "product_id": "23410220000024641",
        },
        "23410220000024642": {
            "service_name": "WYSE_CASH_200",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 200,
            "product_id": "23410220000024642",
        },
        "23410220000024643": {
            "service_name": "WYSE_CASH_300",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 300,
            "product_id": "23410220000024643",
            "band": 10000,
        },
        "23410220000024644": {
            "service_name": "WYSE_CASH_400",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 400,
            "product_id": "23410220000024644",
        },
        "23410220000024645": {
            "service_name": "WYSE_CASH_500",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 500,
            "product_id": "23410220000024645",
        },
        "23410220000024646": {
            "service_name": "WYSE_CASH_700",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 700,
            "product_id": "23410220000024646",
        },
        "23410220000024647": {
            "service_name": "WYSE_CASH_1000",
            "service_id": "234102200006769",
            "product_name": "WYSE_CASH",
            "amount": 1000,
            "product_id": "23410220000024647",
        },
        "23410220000024635": {
            "service_name": "INSTANT_CASH_150",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 150,
            "product_id": "23410220000024635",
            "line_number": 1,
        },
        "23410220000024636": {
            "service_name": "INSTANT_CASH_300",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 300,
            "product_id": "23410220000024636",
            "line_number": 1,
        },
        "23410220000024637": {
            "service_name": "INSTANT_CASH_500",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 500,
            "product_id": "23410220000024637",
            "line_number": 2,
        },
        "23410220000024638": {
            "service_name": "INSTANT_CASH_750",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 750,
            "product_id": "23410220000024638",
            "line_number": 3,
        },
        "23410220000024639": {
            "service_name": "INSTANT_CASH_900",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 900,
            "product_id": "23410220000024639",
            "line_number": 4,
        },
        "23410220000024640": {
            "service_name": "INSTANT_CASH_1000",
            "service_id": "234102200006767",
            "product_name": "INSTANT_CASH",
            "amount": 1000,
            "product_id": "23410220000024640",
            "line_number": 5,
        },
        "23410220000024656": {
            "service_name": "SOCCER_CASH_100",
            "service_id": "234102200006772",
            "product_name": "SOCCER_CASH",
            "amount": 100,
            "product_id": "23410220000024656",
            "line_number": 1,
        },
        "23410220000024657": {
            "service_name": "SOCCER_CASH_500",
            "service_id": "234102200006772",
            "product_name": "SOCCER_CASH",
            "amount": 500,
            "product_id": "23410220000024657",
            "line_number": 2,
        },
        "23410220000024658": {
            "service_name": "SOCCER_CASH_800",
            "service_id": "234102200006772",
            "product_name": "SOCCER_CASH",
            "amount": 800,
            "product_id": "23410220000024658",
            "line_number": 3,
        },
        "23410220000024659": {
            "service_name": "SOCCER_CASH_1200",
            "service_id": "234102200006772",
            "product_name": "SOCCER_CASH",
            "amount": 1200,
            "product_id": "23410220000024659",
            "line_number": 4,
        },
        "23410220000024648": {
            "service_name": "SALARY_FOR_LIFE_200",
            "service_id": "234102200006770",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 200,
            "product_id": "23410220000024648",
            "line_number": 1,
        },
        "23410220000024649": {
            "service_name": "SALARY_FOR_LIFE_500",
            "service_id": "234102200006770",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 500,
            "product_id": "23410220000024649",
            "line_number": 2,
        },
        "23410220000024650": {
            "service_name": "SALARY_FOR_LIFE_800",
            "service_id": "234102200006770",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 800,
            "product_id": "23410220000024650",
            "line_number": 3,
        },
        "23410220000024651": {
            "service_name": "SALARY_FOR_LIFE_1200",
            "service_id": "234102200006770",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 1200,
            "product_id": "23410220000024651",
            "line_number": 7,
        },
        "23410220000027462": {
            "service_name": "INSTANT_CASHOUT_50",
            "service_id": "234102200006961",
            "product_name": "INSTANT_CASHOUT",
            "amount": 50,
            "product_id": "23410220000027462",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027463": {
            "service_name": "INSTANT_CASHOUT_75",
            "service_id": "234102200006961",
            "product_name": "INSTANT_CASHOUT",
            "amount": 75,
            "product_id": "23410220000027463",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027470": {
            "service_name": "WYSE_CASH_50",
            "service_id": "234102200006965",
            "product_name": "WYSE_CASH",
            "amount": 50,
            "product_id": "23410220000027470",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027471": {
            "service_name": "WYSE_CASH_75",
            "service_id": "234102200006965",
            "product_name": "WYSE_CASH",
            "amount": 75,
            "product_id": "23410220000027471",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027464": {
            "service_name": "SALARY_FOR_LIFE_50",
            "service_id": "234102200006962",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 50,
            "product_id": "23410220000027464",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027465": {
            "service_name": "SALARY_FOR_LIFE_75",
            "service_id": "234102200006962",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 75,
            "product_id": "23410220000027465",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027466": {
            "service_name": "FAST_FINGER_50",
            "service_id": "234102200006963",
            "product_name": "FAST_FINGER",
            "amount": 50,
            "product_id": "23410220000027466",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027467": {
            "service_name": "FAST_FINGER_75",
            "service_id": "234102200006963",
            "product_name": "FAST_FINGER",
            "amount": 75,
            "product_id": "23410220000027467",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027468": {
            "service_name": "SOCCER_CASH_50",
            "service_id": "234102200006964",
            "product_name": "SOCCER_CASH",
            "amount": 50,
            "product_id": "23410220000027468",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "23410220000027469": {
            "service_name": "SOCCER_CASH_75",
            "service_id": "234102200006964",
            "product_name": "SOCCER_CASH",
            "amount": 75,
            "product_id": "23410220000027469",
            "line_number": 1,
            "is_daily_subscription": True,
        },
        "0017182000001707": {
            "service_name": "INSTANT_CASH_50",
            "service_id": "234102200006964",
            "product_name": "INSTANT_CASH",
            "amount": 50,
            "product_id": "0017182000001707",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "GLO",
        },
        "0017182000003867": {
            "service_name": "FAST_FINGER_50",
            "service_id": "234102200006964",
            "product_name": "FAST_FINGER",
            "amount": 50,
            "product_id": "0017182000003867",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "GLO",
        },
        "0017182000003868": {
            "service_name": "SALARY_FOR_LIFE_50",
            "service_id": "234102200006964",
            "product_name": "SALARY_FOR_LIFE",
            "amount": 50,
            "product_id": "0017182000003868",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "GLO",
        },
        "0017182000003869": {
            "service_name": "WYSE_CASH_50",
            "service_id": "234102200006964",
            "product_name": "WYSE_CASH",
            "amount": 50,
            "product_id": "0017182000003869",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "GLO",
        },
        "0017182000003870": {
            "service_name": "SOCCER_CASH_50",
            "service_id": "234102200006964",
            "product_name": "SOCCER_CASH",
            "amount": 50,
            "product_id": "0017182000003870",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "GLO",
        },
        "23410220000030965": {
            "service_name": "ASK_AI_75",
            "service_id": "234102200007318",
            "product_name": "ASK_AI",
            "amount": 75,
            "product_id": "23410220000030965",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "MTN",
        },
        "23410220000030956": {
            "service_name": "ASK_AI_30",
            "service_id": "234102200007315",
            "product_name": "ASK_AI",
            "amount": 30,
            "product_id": "23410220000030956",
            "line_number": 1,
            "is_daily_subscription": False,
            "network": "MTN",
        },
        "23410220000030957": {
            "service_name": "ASK_AI_50",
            "service_id": "234102200007315",
            "product_name": "ASK_AI",
            "amount": 50,
            "product_id": "23410220000030957",
            "line_number": 1,
            "is_daily_subscription": False,
            "network": "MTN",
        },
        "23410220000030958": {
            "service_name": "MONETIZE_AI_100",
            "service_id": "234102200007316",
            "product_name": "MONETIZE_AI",
            "amount": 100,
            "product_id": "23410220000030958",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "MTN",
        },
        "23410220000030959": {
            "service_name": "MONETIZE_AI_150",
            "service_id": "234102200007316",
            "product_name": "MONETIZE_AI",
            "amount": 150,
            "product_id": "23410220000030959",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "MTN",
        },
        "23410220000030966": {
            "service_name": "MONETIZE_AI_100",
            "service_id": "234102200007319",
            "product_name": "MONETIZE_AI",
            "amount": 100,
            "product_id": "23410220000030966",
            "line_number": 1,
            "is_daily_subscription": False,
            "network": "MTN",
        },
        "23410220000030967": {
            "service_name": "MONETIZE_AI_150",
            "service_id": "234102200007319",
            "product_name": "MONETIZE_AI",
            "amount": 150,
            "product_id": "23410220000030967",
            "line_number": 1,
            "is_daily_subscription": False,
            "network": "MTN",
        },
        "23410220000030960": {
            "service_name": "LIBERTY_LIFE_50",
            "service_id": "234102200007317",
            "product_name": "LIBERTY_LIFE",
            "amount": 50,
            "product_id": "23410220000030960",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "MTN",
        },
        "234102200000309611": {
            "service_name": "LIBERTY_LIFE_75",
            "service_id": "234102200007317",
            "product_name": "LIBERTY_LIFE",
            "amount": 75,
            "product_id": "234102200000309611",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "MTN",
        },
        "23410220000030962": {
            "service_name": "LIBERTY_LIFE_100",
            "service_id": "234102200007317",
            "product_name": "LIBERTY_LIFE",
            "amount": 100,
            "product_id": "23410220000030962",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "MTN",
        },
        "23410220000030963": {
            "service_name": "LIBERTY_LIFE_200",
            "service_id": "234102200007317",
            "product_name": "LIBERTY_LIFE",
            "amount": 200,
            "product_id": "23410220000030963",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "MTN",
        },
        "23410220000030964": {
            "service_name": "LIBERTY_LIFE_300",
            "service_id": "234102200007317",
            "product_name": "LIBERTY_LIFE",
            "amount": 300,
            "product_id": "23410220000030964",
            "line_number": 1,
            "is_daily_subscription": True,
            "network": "MTN",
        },
    }

    return data.get(product_id)


# if __name__ == "__main__":
#     print()


@dataclass
class TelcoSoccerCashPriceModel:
    @classmethod
    def get_bbc_service_and_prodct_details(cls, amount):
        data = {
            100: [234102200006772, 23410220000024656],
            500: [234102200006772, 23410220000024657],
            800: [234102200006772, 23410220000024658],
            1200: [234102200006772, 23410220000024659],
        }

        return data.get(amount)

    @classmethod
    def subscription_plan_service_and_plan_id(cls, amount):
        # [service_id, plan_id]
        data = {
            75: [234102200006964, 23410220000027468],
            100: [234102200006964, 23410220000027469],
        }

        return data[amount]


def telco_subscription_service_id_game_identification(service_id=None, product_id=None):
    if product_id is not None:
        data = {
            "1000005218": "INSTANT_CASHOUT",
            "1000005251": "INSTANT_CASHOUT",
            "1000005234": "WYSE_CASH",
            "1000005235": "WYSE_CASH",
            "1000005229": "SALARY_FOR_LIFE",
            "1000005230": "SALARY_FOR_LIFE",
            "1000005225": "FAST_FINGERS/AWOOF",
            "1000005226": "FAST_FINGERS/AWOOF",
            "1000005239": "SOCCER_CASH",
            "1000005240": "SOCCER_CASH",
            "1000005218": "INSTANT_CASHOUT",
            "1000005251": "INSTANT_CASHOUT",
            "1000005397": "WYSE_CASH",
            "1000005399": "WYSE_CASH",
            "1000005392": "SALARY_FOR_LIFE",
            "1000005394": "SALARY_FOR_LIFE",
            "1000005386": "FAST_FINGERS/AWOOF",
            "1000005388": "FAST_FINGERS/AWOOF",
            "1000005402": "SOCCER_CASH",
            "1000005404": "SOCCER_CASH",
            "234102200006962": "SALARY_FOR_LIFE",
            "234102200006965": "WYSE_CASH",
            "234102200006961": "INSTANT_CASH",
            "234102200006963": "AWOOF",
            "234102200006964": "SOCCER_CASH",
            "0017182000001707": "INSTANT_CASH",
            "0017182000003867": "AWOOF",
            "0017182000003868": "SALARY_FOR_LIFE",
            "0017182000003869": "WYSE_CASH",
            "0017182000003870": "SOCCER_CASH",
            "23410220000030965": "ASK_AI",
            "23410220000030956": "ASK_AI",
            "23410220000030957": "ASK_AI",
            "23410220000030958": "MONETIZE_AI",
            "23410220000030959": "MONETIZE_AI",
            "23410220000030966": "MONETIZE_AI",
            "23410220000030967": "MONETIZE_AI",
            "23410220000030960": "LIBERTY_LIFE",
            "234102200000309611": "LIBERTY_LIFE",
            "23410220000030962": "LIBERTY_LIFE",
            "23410220000030963": "LIBERTY_LIFE",
            "23410220000030964": "LIBERTY_LIFE",
            "23410220000027465": "SALARY_FOR_LIFE",
            "23410220000027464": "SALARY_FOR_LIFE",
            "23410220000027471": "WYSE_CASH",
            "23410220000027470": "WYSE_CASH",
            "23410220000027463": "INSTANT_CASH",
            "23410220000027462": "INSTANT_CASH",
            "23410220000027467": "AWOOF",
            "23410220000027466": "AWOOF",
            "23410220000027469": "SOCCER_CASH",
        }

        return data.get(product_id)

    else:
        return None


@dataclass
class PosNewQuikaPriceModel:
    def ticket_price(cls, ticket_line=None):
        price = {
            1: {
                "ticket_price": 200,
                "potential_winning": 100000,
                "line_number": 1,
                "total_amount": 200,
                "amount_to_register": 200,
            },
            2: {
                "ticket_price": 400,
                "potential_winning": 100000,
                "line_number": 2,
                "total_amount": 200,
                "amount_to_register": 200,
            },
            3: {
                "ticket_price": 600,
                "potential_winning": 100000,
                "line_number": 3,
                "total_amount": 200,
                "amount_to_register": 200,
            },
            4: {
                "ticket_price": 800,
                "potential_winning": 100000,
                "line_number": 4,
                "total_amount": 200,
                "amount_to_register": 200,
            },
        }

        if ticket_line is None:
            return price
        return price[ticket_line]


@dataclass
class TelcoAskAiPriceModel:
    @classmethod
    def ticket_price(cls, ticket_line=None):
        price = {
            1: {
                "ticket_price": 200,
                "potential_winning": 5000,
                "line_number": 1,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 200,
                "amount_to_register": 150,
            },
            2: {
                "ticket_price": 500,
                "potential_winning": 15000,
                "line_number": 2,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 500,
                "amount_to_register": 350,
            },
            3: {
                "ticket_price": 800,
                "potential_winning": 50000,
                "line_number": 3,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 800,
                "amount_to_register": 700,
            },
            4: {
                "ticket_price": 1200,
                "potential_winning": 150000,
                "line_number": 7,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 1200,
                "amount_to_register": 1400,
            },
        }

        if ticket_line is None:
            return price

        return price[ticket_line]

    @classmethod
    def get_ticket_price_details_with_stake_amount(cls, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)
        # if channel == "POS":
        #     ticket_p = cls.ticket_price(channel)
        # else:
        #     ticket_p = cls.ticket_price_with_other_service_price(channel)

        ticket_p = cls.ticket_price()

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[float(value["total_amount"])] = value

        return price_re_structure.get(_stake_amount)

    @classmethod
    def ussd_pontential_winnings(cls):
        sal_4_life_ticket_prices = cls.ticket_price()
        top_price = []
        top_three_index = [1, 2, 3, 4]
        for index in top_three_index:
            top_price.append(currency_formatter(int(sal_4_life_ticket_prices[index]["potential_winning"])))

        return top_price

    @classmethod
    def ussd_top_prices_in_threes(cls, index_range: list):
        ticket_price = cls.subscription_ticket_prices()

        price = {}
        for i in index_range:
            # print(i)
            if i == 5:
                pass
            else:
                price[i] = ticket_price[i]

        return price

    @classmethod
    def get_bbc_service_and_prodct_details(cls, stake_amount):
        # [service_id, plan_id]
        data = {
            30: [234102200007315, 23410220000030956],
            50: [234102200007315, 23410220000030957],
        }

        return data[stake_amount]

    @classmethod
    def subscription_plan_service_and_plan_id(cls, amount):
        # [service_id, plan_id]

        data = {
            75: [234102200007318, 23410220000030965],
        }

        return data[amount]

    @classmethod
    def subscription_ticket_prices(cls):
        price = {
            1: {
                "ticket_price": 100,
                "potential_winning": 2000,
                "line_number": 1,
                "total_amount": 100,
                "amount_to_register": 100,
                "type": "subscription",
                "service_id": 234102200007318,
                "product_id": 23410220000030965,
            },
            2: {
                "ticket_price": 30,
                "potential_winning": 3000,
                "line_number": 2,
                "total_amount": 30,
                "amount_to_register": 30,
                "type": "on-demand",
                "service_id": 234102200007315,
                "product_id": 23410220000030956,
            },
            3: {
                "ticket_price": 50,
                "potential_winning": 5000,
                "line_number": 1,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 50,
                "amount_to_register": 50,
                "type": "on-demand",
                "service_id": 234102200007315,
                "product_id": 23410220000030957,
            },
        }

        return price

    @classmethod
    def get_subscription_ticket_price_details_with_stake_amount(cls, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)

        ticket_p = cls.subscription_ticket_prices()

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[
                float(value["ticket_price"])
                + float(value.get("illusion_price", 0))
                + float(value.get("woven_service_charge", 0))
                + float(value.get("africastalking_charge", 0))
            ] = value

        return price_re_structure.get(_stake_amount)

    @classmethod
    def ussd_top_subscription_prices_in_threes(cls, index_range: list):
        """
        This method is used to get the top three subscription prices for the ussd

        :param index_range: list [1,2,3]
        :return: dict
        """
        sal_4_life_ticket_prices = cls.subscription_ticket_prices()

        data = {}
        for index in index_range:
            data[index] = {
                "ticket_price": currency_formatter(sal_4_life_ticket_prices.get(index).get("ticket_price")),
                "potential_winning": big_digit_currency_formatter(sal_4_life_ticket_prices.get(index).get("potential_winning")),
                "type": sal_4_life_ticket_prices.get(index).get("type"),
            }

        return data


@dataclass
class TelcoMonetizeAiPriceModel:
    @classmethod
    def ticket_price(cls, ticket_line=None):
        price = {
            1: {
                "ticket_price": 200,
                "potential_winning": 5000,
                "line_number": 1,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 200,
                "amount_to_register": 150,
            },
            2: {
                "ticket_price": 500,
                "potential_winning": 15000,
                "line_number": 2,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 500,
                "amount_to_register": 350,
            },
            3: {
                "ticket_price": 800,
                "potential_winning": 50000,
                "line_number": 3,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 800,
                "amount_to_register": 700,
            },
            4: {
                "ticket_price": 1200,
                "potential_winning": 150000,
                "line_number": 7,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 1200,
                "amount_to_register": 1400,
            },
        }

        if ticket_line is None:
            return price

        return price[ticket_line]

    @classmethod
    def get_ticket_price_details_with_stake_amount(cls, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)
        # if channel == "POS":
        #     ticket_p = cls.ticket_price(channel)
        # else:
        #     ticket_p = cls.ticket_price_with_other_service_price(channel)

        ticket_p = cls.ticket_price()

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[float(value["total_amount"])] = value

        return price_re_structure.get(_stake_amount)

    @classmethod
    def ussd_pontential_winnings(cls):
        monetize_ticket_prices = cls.ticket_price()
        top_price = []
        top_three_index = [1, 2, 3, 4]
        for index in top_three_index:
            top_price.append(currency_formatter(int(monetize_ticket_prices[index]["potential_winning"])))

        return top_price

    @classmethod
    def ussd_top_prices_in_threes(cls, index_range: list):
        ticket_price = cls.subscription_ticket_prices()

        price = {}
        for i in index_range:
            # print(i)
            if i == 5:
                pass
            else:
                price[i] = ticket_price[i]

        return price

    @classmethod
    def get_bbc_service_and_prodct_details(cls, stake_amount):
        # [service_id, plan_id]
        data = {
            100: [234102200007316, 23410220000030958],
            150: [234102200007316, 23410220000030959],
        }

        return data[stake_amount]

    @classmethod
    def subscription_plan_service_and_plan_id(cls, amount):
        # [service_id, plan_id]

        data = {
            100: [234102200007319, 23410220000030966],
            150: [234102200007319, 23410220000030966],
        }

        return data[amount]

    @classmethod
    def subscription_ticket_prices(cls):
        price = {
            1: {
                "ticket_price": 100,
                "potential_winning": 2000,
                "line_number": 1,
                "total_amount": 100,
                "amount_to_register": 100,
                "type": "subscription",
                "service_id": 234102200007316,
                "product_id": 23410220000030958,
            },
            2: {
                "ticket_price": 150,
                "potential_winning": 2000,
                "line_number": 1,
                "total_amount": 150,
                "amount_to_register": 150,
                "type": "subscription",
                "service_id": 234102200007316,
                "product_id": 23410220000030959,
            },
            3: {
                "ticket_price": 100,
                "potential_winning": 3000,
                "line_number": 2,
                "total_amount": 100,
                "amount_to_register": 100,
                "type": "on-demand",
                "service_id": 234102200007319,
                "product_id": 23410220000030966,
            },
            4: {
                "ticket_price": 150,
                "potential_winning": 5000,
                "line_number": 1,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 150,
                "amount_to_register": 150,
                "type": "on-demand",
                "service_id": 234102200007319,
                "product_id": 23410220000030967,
            },
        }

        return price

    @classmethod
    def get_subscription_ticket_price_details_with_stake_amount(cls, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)

        ticket_p = cls.subscription_ticket_prices()

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[
                float(value["ticket_price"])
                + float(value.get("illusion_price", 0))
                + float(value.get("woven_service_charge", 0))
                + float(value.get("africastalking_charge", 0))
            ] = value

        return price_re_structure.get(_stake_amount)

    @classmethod
    def ussd_top_subscription_prices_in_threes(cls, index_range: list):
        """
        This method is used to get the top three subscription prices for the ussd

        :param index_range: list [1,2,3]
        :return: dict
        """
        sal_4_life_ticket_prices = cls.subscription_ticket_prices()

        data = {}
        for index in index_range:
            data[index] = {
                "ticket_price": currency_formatter(sal_4_life_ticket_prices.get(index).get("ticket_price")),
                "potential_winning": big_digit_currency_formatter(sal_4_life_ticket_prices.get(index).get("potential_winning")),
                "type": sal_4_life_ticket_prices.get(index).get("type"),
            }

        return data


@dataclass
class TelcoLibertyLifePriceModel:
    @classmethod
    def ticket_price(cls, ticket_line=None):
        price = {
            1: {
                "ticket_price": 200,
                "potential_winning": 5000,
                "line_number": 1,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 200,
                "amount_to_register": 150,
            },
            2: {
                "ticket_price": 500,
                "potential_winning": 15000,
                "line_number": 2,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 500,
                "amount_to_register": 350,
            },
            3: {
                "ticket_price": 800,
                "potential_winning": 50000,
                "line_number": 3,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 800,
                "amount_to_register": 700,
            },
            4: {
                "ticket_price": 1200,
                "potential_winning": 150000,
                "line_number": 7,
                "woven_service_charge": 0,
                "africastalking_charge": 0,
                "total_amount": 1200,
                "amount_to_register": 1400,
            },
        }

        if ticket_line is None:
            return price

        return price[ticket_line]

    @classmethod
    def get_ticket_price_details_with_stake_amount(cls, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)
        # if channel == "POS":
        #     ticket_p = cls.ticket_price(channel)
        # else:
        #     ticket_p = cls.ticket_price_with_other_service_price(channel)

        ticket_p = cls.ticket_price()

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[float(value["total_amount"])] = value

        return price_re_structure.get(_stake_amount)

    @classmethod
    def ussd_pontential_winnings(cls):
        monetize_ticket_prices = cls.ticket_price()
        top_price = []
        top_three_index = [1, 2, 3, 4]
        for index in top_three_index:
            top_price.append(currency_formatter(int(monetize_ticket_prices[index]["potential_winning"])))

        return top_price

    @classmethod
    def ussd_top_prices_in_threes(cls, index_range: list):
        ticket_price = cls.subscription_ticket_prices()

        price = {}
        for i in index_range:
            # print(i)
            if i == 5:
                pass
            else:
                price[i] = ticket_price[i]

        return price

    @classmethod
    def get_bbc_service_and_prodct_details(cls, stake_amount):
        # [service_id, plan_id]
        data = {}

        return data[stake_amount]

    @classmethod
    def subscription_plan_service_and_plan_id(cls, amount):
        # [service_id, plan_id]

        data = {
            50: [234102200007317, 23410220000030960],
            75: [234102200007317, 234102200000309611],
            100: [234102200007317, 23410220000030962],
            200: [234102200007317, 23410220000030963],
            300: [234102200007317, 23410220000030964],
        }

        return data[amount]

    @classmethod
    def subscription_ticket_prices(cls):
        price = {
            1: {
                "ticket_price": 50,
                "potential_winning": 2000,
                "line_number": 1,
                "total_amount": 50,
                "amount_to_register": 50,
                "type": "subscription",
                "service_id": 234102200007317,
                "product_id": 23410220000030960,
            },
            2: {
                "ticket_price": 75,
                "potential_winning": 2000,
                "line_number": 1,
                "total_amount": 75,
                "amount_to_register": 75,
                "type": "subscription",
                "service_id": 234102200007317,
                "product_id": 234102200000309611,
            },
            3: {
                "ticket_price": 100,
                "potential_winning": 2000,
                "line_number": 1,
                "total_amount": 100,
                "amount_to_register": 100,
                "type": "subscription",
                "service_id": 234102200007317,
                "product_id": 23410220000030962,
            },
            4: {
                "ticket_price": 200,
                "potential_winning": 2000,
                "line_number": 1,
                "total_amount": 200,
                "amount_to_register": 200,
                "type": "subscription",
                "service_id": 234102200007317,
                "product_id": 23410220000030963,
            },
            5: {
                "ticket_price": 300,
                "potential_winning": 2000,
                "line_number": 1,
                "total_amount": 300,
                "amount_to_register": 300,
                "type": "subscription",
                "service_id": 234102200007317,
                "product_id": 23410220000030964,
            },
        }

        return price

    @classmethod
    def get_subscription_ticket_price_details_with_stake_amount(cls, stake_amount):
        # const_obj = ConstantVariable.objects.last()

        _stake_amount = float(stake_amount)

        ticket_p = cls.subscription_ticket_prices()

        price_re_structure = {}
        for key, value in ticket_p.items():
            price_re_structure[
                float(value["ticket_price"])
                + float(value.get("illusion_price", 0))
                + float(value.get("woven_service_charge", 0))
                + float(value.get("africastalking_charge", 0))
            ] = value

        return price_re_structure.get(_stake_amount)

    @classmethod
    def ussd_top_subscription_prices_in_threes(cls, index_range: list):
        """
        This method is used to get the top three subscription prices for the ussd

        :param index_range: list [1,2,3]
        :return: dict
        """
        sal_4_life_ticket_prices = cls.subscription_ticket_prices()

        data = {}
        for index in index_range:
            data[index] = {
                "ticket_price": currency_formatter(sal_4_life_ticket_prices.get(index).get("ticket_price")),
                "potential_winning": big_digit_currency_formatter(sal_4_life_ticket_prices.get(index).get("potential_winning")),
                "type": sal_4_life_ticket_prices.get(index).get("type"),
            }

        return data


ON_DEMAND_SERVICE_IDS = [
    "234102200006771",
    "234102200006769",
    "234102200006767",
    "234102200006770",
    "234102200007315",
    "2341022000D7316",
    "234102200007316",
]

SUBSCRIPTION_SERVICE_IDS = [
    "234102200006963",
    "234102200006965",
    "234102200006961",
    "234102200006962",
    "234102200007318",
    "234102200007319",
    "234102200007317",
    "23410220000027469",
    "234102200006964",
]
