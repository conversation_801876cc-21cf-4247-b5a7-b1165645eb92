from django.db import models
from django.utils.translation import gettext_lazy as _


class TrackingSourceChoices(models.TextChoices):
    MORBIDTEK_MEDIA = "MORBIDTEK_MEDIA", _("MORBIDTEK_MEDIA")
    MOBPLUS = "MOBPLUS", _("MOBPLUS")
    ANGEL_MANCALA = "ANGEL_MANCALA", _("ANGEL_MANCALA")
    AD_MAVEN = "AD_MAVEN", _("AD_MAVEN")
    ANGEL_MEDIA = "ANGEL_MEDIA", _("ANGEL_MEDIA")
    TRAFFIC_COMPANY = "TRAFFIC_COMPANY", _("TRAFFIC_COMPANY")
    GOLDEN_GOOSE = "GOLDEN_GOOSE", _("GOLDEN_GOOSE")
    Phoenix_MTN = "Phoenix_MTN", _("Phoenix_MTN")
    VICTORY_ADS = "VICTORY_ADS", _("VICTORY_ADS")
    UPSTREAM = "UPSTREAM", _("UPSTREAM")
    ORGANIC = "ORGANIC", _("ORGANIC")
    WINWISE_UPSELLING = "WINWISE_UPSELLING", _("WINWISE_UPSELLING")
    CLICK_STREAM = "CLICK_STREAM", _("CLICK_STREAM")
    MOBIPIUM = "MOBIPIUM", _("MOBIPIUM")
    CLICKBYTE_MEDIA = "CLICKBYTE_MEDIA", _("CLICKBYTE_MEDIA")
    SIGMA = "SIGMA", _("SIGMA")
    SHINE_DIGITAL = "SHINE_DIGITAL", _("SHINE_DIGITAL")
