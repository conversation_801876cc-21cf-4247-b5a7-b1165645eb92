from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from ads_tracker.models import ClickId, MobidTracker
from ads_tracker.serializers import (
    ClickIdSerializer,
    MobidTrackerSerializer,
    WinwiseUpsellingSerializer,
)


class MobidTrackerClickIdView(generics.CreateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = MobidTrackerSerializer

    # def post(self, request):
    #     serializer = self.serializer_class(data=request.data)

    #     if serializer.is_valid(raise_exception=True):
    #         serializer.save()
    #         return Response({"message": "response received successfully"}, status=status.HTTP_200_OK)
    #     else:
    #         return Response({"error": "An error occurred"}, status=status.HTTP_400_BAD_REQUEST)


class CreateClickIdRecordView(generics.CreateAPIView):
    permission_classes = [IsAuthenticated]
    """Creates a record for a click_id when a new user is routed to our web url.
    The click_id must be unique."""
    serializer_class = ClickIdSerializer


class UpdateClickIdView(APIView):
    permission_classes = [IsAuthenticated]

    """
        Tracks the drop-off point of users on the web interface.
        Especially users attracted via ads campaigns and promotions with
        third parties.
        """

    def get(self, request):
        """
        - Args:
            - click_id: The unique identifier of the user as generated by our ads
            campaign partner company.
            - phone_number_screeen_seen: A boolean that tells whether the user was shown
                phone number input screen on the lotto web flow.
            - loaded_mancala_screen: A boolean that tells whether a
                user attempts to load a mancala game on our web flow.
        """

        click_id = request.query_params.get("click_id")
        phone_number_screeen_seen = request.query_params.get("phone_number_screeen_seen")
        loaded_mancala_screen = request.query_params.get("loaded_mancala_screen")

        if click_id and phone_number_screeen_seen == "true":  # Request with info on which screen the user interracts with
            try:
                click_id_instance = ClickId.objects.get(click_id=click_id)  # Get the click id record
                click_id_instance.phone_number_input_seen = True  # Update the instance
                click_id_instance.save()  # save the update
            except ClickId.DoesNotExist:
                return Response({"error": "User with this click id does not exist"}, status=status.HTTP_400_BAD_REQUEST)
            return Response({"message": "This user has seen the phone number input screen"}, status=status.HTTP_200_OK)
        elif click_id and loaded_mancala_screen == "true":
            try:
                click_id_instance = ClickId.objects.get(click_id=click_id)  # Get the click id record
                click_id_instance.loaded_mancala_screen = True  # Update the instance
                click_id_instance.save()  # save the update
                return Response({"message": "This user loaded the mancala games screen"}, status=status.HTTP_200_OK)
            except ClickId.DoesNotExist:
                return Response({"error": "User with this click id does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        elif click_id is None:
            return Response({"error": "Click id is required"}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({"error": "User page dropoff is required"}, status=status.HTTP_400_BAD_REQUEST)


class UpdateMorbidTrackerView(generics.UpdateAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = MobidTrackerSerializer
    queryset = MobidTracker


class CreateWinwiseUpsellingRecordView(generics.CreateAPIView):
    # permission_classes = [IsAuthenticated]
    """Creates a record for a click_id when a new user is routed to our web url.
    The click_id must be unique."""
    serializer_class = WinwiseUpsellingSerializer


# class GetWinwiseUpsellingRecordView(generics.RetrieveAPIView):
#     permission_classes = [IsAuthenticated]
#     """Creates a record for a click_id when a new user is routed to our web url.
#     The click_id must be unique."""
#     serializer_class = WinwiseUpsellingSerializer


class ReleasePendingPostbacksView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        from ads_tracker.tasks import release_pending_postbacks
        task_id = release_pending_postbacks.apply_async(queue="celery_ads_postback")

        return Response({"task_id": str(task_id)}, status=status.HTTP_200_OK)