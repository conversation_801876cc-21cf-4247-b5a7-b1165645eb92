from django.core.management.base import BaseCommand
from wyse_ussd.models import SecureDDataDump
from ads_tracker.helpers.process_postback import process_in_batches
from django.utils import timezone
from datetime import timedelta


class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        # import random
        a_days_ago = timezone.now() - timedelta(days=1)
        items_qs = SecureDDataDump.objects.filter(created_at__gte=a_days_ago, postback_processed=False)

        for i in range(1, 11):
            print(f"CYCLE NUMBER: {i}")
            # Fetch items to be processed

            try:
                items = items_qs[:500]
            except IndexError:
                items = items_qs[: items.count()]
            # Start processing
            process_result = process_in_batches(items)
        items_qs.update(postback_processed=True)

        

        # return process_result
        # for i in range(0, 600):
        #     msisdn = ''.join(str(random.randint(0, 9)) for _ in range(11))
        #     productID = ''.join(str(random.randint(0, 9)) for _ in range(11))
        #     trxId = ''.join(str(random.randint(0, 9)) for _ in range(11))
        #     data = {
        #         "msisdn": msisdn,
        #         "activation": "1",
        #         "productID":productID,
        #         "description": "success",
        #         "timestamp": "1741276749573",
        #         "trxId": trxId
        #         }

        #     SecureDDataDump.objects.create(
        #         data=data, source="MTN"
        #     )
