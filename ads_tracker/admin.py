from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from ads_tracker.helpers.helpers import (
    get_lotto_amount_paid_for_subscriber,
    get_lotto_subscribers_phone_list,
    send_ads_tracker_postback_url,
)
from ads_tracker.models import (
    ClickId,
    MarketingCampaignAnalytics,
    MobidTracker,
    Unsubscription,
    WinwiseUpselling,
)
from wyse_ussd.models import SecureDTransaction

class MobidTrackerResource(resources.ModelResource):
    class Meta:
        model = MobidTracker


class MarketingCampaignAnalyticsResource(resources.Resource):
    class Meta:
        model = MarketingCampaignAnalytics


class UnsubscriptionResource(resources.ModelResource):
    class Meta:
        model = Unsubscription


class MobidTranckerResourceAdmin(ImportExportModelAdmin):
    resource_class = MobidTrackerResource
    search_fields = ["click_id", "phone_number", "number_of_renewals", "game_type"]
    list_filter = ["source", "telco_network", "date_created", "amount_played", "converted", "game_type", "controled", "unsubscribed"]
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return self.readonly_fields + ("amount_played", "click_id", "number_of_renewals", "controled")
        else:
            return self.readonly_fields

    def resolve_secure_d_amount(self, request, queryset):
        for record in queryset:
            secure_d_transaction = SecureDTransaction.objects.filter(reference=record.click_id).last()
            if secure_d_transaction:
                record.amount_played = secure_d_transaction.subscription_amount
                record.save()
            else:
                pass

        self.message_user(request, "Successfully Resolved Amounts")

    def update_renewals_count(self, request, queryset):
        for query in queryset:
            query: MobidTracker
            phone_number = query.phone_number
            all_subscribers_phones_list = get_lotto_subscribers_phone_list(phone_number=phone_number)
            renewals_count = all_subscribers_phones_list.count(phone_number)
            query.number_of_renewals = renewals_count
            query.amount_paid = get_lotto_amount_paid_for_subscriber(phone_number=phone_number)

            if query.converted is False and renewals_count > 0:
                query.converted = True

                if query.postback_sent is False:
                    # send_source_postback = send_ads_tracker_postback_url(
                    #     source=query.source,
                    #     click_id=query.click_id,
                    #     amount=query.amount_played
                    #     )
                    query.postback_sent = True
                    # query.postback_response = send_source_postback
            elif renewals_count < 1:
                query.converted = False
            else:
                pass

            query.save()

        self.message_user(request, "Successfully updated renewals count")
    
    def update_glo_sts_renewals_count(self, request, queryset):
        from wyse_ussd.models import NitroSwitchDailySubscription
        from django.db.models import Sum

        for query in queryset:
            query: MobidTracker
            phone_number = query.phone_number

            renewals_qs = NitroSwitchDailySubscription.objects.filter(phone_number=phone_number, amount__gt=0)
            renewals_count = renewals_qs.count()
            renewal_amount = renewals_qs.aggregate(total_amount_paid=Sum("amount"))["total_amount_paid"] or 0.00

            query = MobidTracker.objects.filter(phone_number=phone_number).last()
            if query:
                query.number_of_renewals = renewals_count
                query.amount_paid = renewal_amount

                if query.converted is False and renewals_count > 0:
                    query.converted = True

                    if query.postback_sent is False:
                        query.postback_sent = True
                elif renewals_count < 1:
                    query.converted = False
                else:
                    pass
                query.save()

        self.message_user(request, "Successfully updated glo renewals count")

    def resolve_bad_organic_traffic(self, request, queryset):
        for query in queryset:
            query: MobidTracker
            reference = query.click_id
            if "golden" in reference:
                reference = reference.translate({ord(i): None for i in "golden"})
                send_golden_golden_callback = send_ads_tracker_postback_url(source="GOLDEN_GOOSE", click_id=reference, amount=query.amount_played)
                query.postback_response = send_golden_golden_callback
                query.postback_sent = True
                query.source = "GOLDEN_GOOSE"
                query.click_id = reference
                query.save()
        self.message_user(request, "bad sources resolved")

    update_renewals_count.short_description = "update renewals count"
    resolve_secure_d_amount.short_description = "amount_played must be same as subscription amount on SecureDTransactions Table"
    resolve_bad_organic_traffic.short_description = "Resolve bad organic traffic"
    update_glo_sts_renewals_count.short_description = "Update glo renewals count"
    actions = [resolve_secure_d_amount, update_renewals_count, resolve_bad_organic_traffic, update_glo_sts_renewals_count]


class ClickIdResource(resources.ModelResource):
    class Meta:
        model = ClickId


class ClickIdResourceAdmin(ImportExportModelAdmin):
    resource_class = ClickIdResource
    search_fields = ["click_id", "source"]
    list_filter = ["source", "date_created"]
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return self.readonly_fields + ("click_id",)
        else:
            return self.readonly_fields


class MarketingCampaignAnalyticsResourceAdmin(ImportExportModelAdmin):
    using = "external"

    resource_class = MarketingCampaignAnalyticsResource

    list_filter = ["agency", "created_at"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_queryset(self, request):
        # Tell Django to look for objects on the 'other' database.
        return super(MarketingCampaignAnalyticsResourceAdmin, self).get_queryset(request).using(self.using)


class WinwiseUpsellingResource(resources.ModelResource):
    class Meta:
        model = WinwiseUpselling


class WinwiseUpsellingResourceAdmin(ImportExportModelAdmin):
    resource_class = WinwiseUpsellingResource
    search_fields = ["click_id", "phone_number", "number_of_renewals", "game_type"]
    list_filter = ["source", "date_created", "amount_played", "converted", "game_type"]
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return self.readonly_fields + ("amount_played", "click_id", "number_of_renewals")
        else:
            return self.readonly_fields


class UnsubscriptionResourceAdmin(ImportExportModelAdmin):
    resource_class = UnsubscriptionResource
    search_fields = ["click_id", "phone_number", "number_of_renewals", "game_type"]
    list_filter = ["source", "date_created", "amount_played", "converted", "game_type", "subscription_date"]
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return self.readonly_fields + ("amount_played", "click_id", "number_of_renewals")
        else:
            return self.readonly_fields


admin.site.register(MobidTracker, MobidTranckerResourceAdmin)
admin.site.register(ClickId, ClickIdResourceAdmin)
admin.site.register(MarketingCampaignAnalytics, MarketingCampaignAnalyticsResourceAdmin)
admin.site.register(WinwiseUpselling, WinwiseUpsellingResourceAdmin)
admin.site.register(Unsubscription, UnsubscriptionResourceAdmin)
