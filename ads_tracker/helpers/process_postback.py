import time
from concurrent.futures import ThreadPoolExecutor
from ads_tracker.tasks import send_marketing_partners_postback_task
from wyse_ussd.models import SecureDDataDump
from django.utils import timezone


# Function to process a single item
def process_item(item):
    try:
        print(f"Processing item ID {item.id}")
        send_marketing_partners_postback_task(instance_id=item.id)
    except Exception as e:
        print(f"Error processing item ID {item.id}: {e}")

# Process items in batches
def process_in_batches(queryset, batch_size=50, delay=0):
    total = len(queryset)
    start_time = timezone.now()
    for i in range(0, total, batch_size):
        batch = list(queryset[i:i + batch_size])  # Evaluate batch to avoid hitting the DB repeatedly
        print(f"\nProcessing batch {i // batch_size + 1} ({len(batch)} items)...")

        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(process_item, item) for item in batch]
            for future in futures:
                try:
                    future.result()  # Raise any exception that occurred during processing
                except Exception as e:
                    print(f"Error in batch processing: {e}")

        if i + batch_size < total:
            print(f"Waiting {delay} seconds before next batch...")
            time.sleep(delay)
    print(f"TIME TAKEN:::: {(timezone.now() - start_time).seconds} seconds")
