import requests
from django.db.models import Sum

from awoof_app.models import AwoofGameTable
from main.models import LotteryModel, LottoTicket
from sport_app.models import SoccerPrediction


def send_ads_tracker_postback_url(source, click_id, amount=0):
    if source == "MORBIDTEK_MEDIA":
        url = f"http://mobtekmedia.hopb0.com/notify/110122/?click_id={click_id}"
    elif source == "MOBPLUS":
        url = f"http://m.mobplus.net/c/p/74a3eac380d944aab100c0f6112b268f?txid={click_id}"
    elif source == "AD_MAVEN":
        url = f"https://xml.realtime-bid.com/conversion?c={click_id}&count=1&value={amount}"
    elif source == "ANGEL_MEDIA":
        url = f"http://postback.rustclick.com/pb/377?click_id={click_id}&payout="
    elif source == "TRAFFIC_COMPANY":
        url = f"https://postback.level23.nl/?currency=USD&handler=11342&hash=14f1e1b8e3a711e0c49d672857610ea6&tracker={click_id}"
    elif source == "GOLDEN_GOOSE":
        url = f"http://n.gg.agency/ntf1/?token=af54c5e8739f11b00bca46da47962675&click_id={click_id}"
    elif source == "Phoenix_MTN":
        url = f"https://adwh.mywkd.com/thirdparty?event_name=Subscribe&phx_click_id={click_id}&product_id=832"
    elif source == "VICTORY_ADS":
        url = f"http://mnoi.online/trackwsp.php?subid={click_id}"
    elif source == "GOLDEN_GOLDEN":
        url = f"http://n.gg.agency/ntf1/?token=af54c5e8739f11b00bca46da47962675&click_id={click_id}"
    elif source == "CLICK_STREAM":
        url = f"https://track.goforclicks.swaarm-clients.com/postback?click_id={click_id}&security_token=4fe0f47d-81b5-4077-9b0a-b2a402969743"
    elif source == "MOBIPIUM":
        url = f"https://smobipiumlink.com/conversion/index.php?jp={click_id}&source=WINWISE"
    elif source == "CLICKBYTE_MEDIA":
        url = f"https://click.clickbyte-media.com/postback?cid={click_id}&payout={amount}"
    elif source == "SIGMA":
        url = f"https://cd.sigmamobi.com/callbacks/223?request_id={click_id}&event=lead"
    elif source == "SHINE_DIGITAL":
        url = f"http://shinedigitalworld.offerstrack.net/advBack.php?click_id={click_id}"
    else:
        response = "Failed"
        # url = f"http://mobtekmedia.hopb0.com/notify/110122/?click_id={click_id}"

    try:
        response = requests.get(url=url, timeout=2)
        if source == "VICTORY_ADS":
            return response.text
        elif source in ["CLICK_STREAM", "MOBIPIUM", "CLICKBYTE_MEDIA", "SHINE_DIGITAL"]:
            return response.status_code
        else:
            return response.text
    except Exception:
        response = "Failed"
        return response


def get_lotto_subscribers_phone_list(phone_number, game_type=None):
    if game_type:
        lotto_tickets_phones_list = list(
            LottoTicket.objects.filter(channel="USSD", paid=True, phone=phone_number, lottery_type=game_type).values_list("phone", flat=True)
        )
        lottery_model_phones_list = list(
            LotteryModel.objects.filter(paid=True, phone=phone_number, lottery_type=game_type).values_list("phone", flat=True)
        )
        soccer_prediction_phones_list = list(
            SoccerPrediction.objects.filter(paid=True, phone=phone_number, lottery_type=game_type).values_list("phone", flat=True)
        )
        awoof_phones_list = list(AwoofGameTable.objects.filter(paid=True, phone=phone_number, lottery_type=game_type).values_list("phone", flat=True))
    else:
        lotto_tickets_phones_list = list(LottoTicket.objects.filter(channel="USSD", paid=True, phone=phone_number).values_list("phone", flat=True))
        lottery_model_phones_list = list(LotteryModel.objects.filter(paid=True, phone=phone_number).values_list("phone", flat=True))
        soccer_prediction_phones_list = list(SoccerPrediction.objects.filter(paid=True, phone=phone_number).values_list("phone", flat=True))
        awoof_phones_list = list(AwoofGameTable.objects.filter(paid=True, phone=phone_number).values_list("phone", flat=True))

    all_subscribers_phones_list = lotto_tickets_phones_list + lottery_model_phones_list + soccer_prediction_phones_list + awoof_phones_list

    return all_subscribers_phones_list


def get_lotto_amount_paid_for_subscriber(phone_number, game_type=None):
    if game_type:
        lotto_tickets_subscriber_amount = (
            list(
                LottoTicket.objects.filter(phone=phone_number, channel="USSD", paid=True, lottery_type=game_type)
                .aggregate(Sum("amount_paid"))
                .values()
            )[0]
            or 0
        )
        lottery_model_subscriber_amount = (
            list(LotteryModel.objects.filter(phone=phone_number, paid=True, lottery_type=game_type).aggregate(Sum("amount_paid")).values())[0] or 0
        )
        soccer_prediction_subscriber_amount = (
            list(SoccerPrediction.objects.filter(phone=phone_number, paid=True, lottery_type=game_type).aggregate(Sum("amount_paid")).values())[0]
            or 0
        )
        awoof_subscriber_amount = (
            list(AwoofGameTable.objects.filter(phone=phone_number, paid=True, lottery_type=game_type).aggregate(Sum("amount_paid")).values())[0] or 0
        )
    else:
        lotto_tickets_subscriber_amount = (
            list(LottoTicket.objects.filter(phone=phone_number, channel="USSD", paid=True).aggregate(Sum("amount_paid")).values())[0] or 0
        )
        lottery_model_subscriber_amount = (
            list(LotteryModel.objects.filter(phone=phone_number, paid=True).aggregate(Sum("amount_paid")).values())[0] or 0
        )
        soccer_prediction_subscriber_amount = (
            list(SoccerPrediction.objects.filter(phone=phone_number, paid=True).aggregate(Sum("amount_paid")).values())[0] or 0
        )
        awoof_subscriber_amount = list(AwoofGameTable.objects.filter(phone=phone_number, paid=True).aggregate(Sum("amount_paid")).values())[0] or 0

    total_subscriber_amount = (
        lotto_tickets_subscriber_amount + lottery_model_subscriber_amount + soccer_prediction_subscriber_amount + awoof_subscriber_amount
    )

    return total_subscriber_amount


def postback_skip_decisioning():
    from admin_dashboard.models import ConstantTable

    const_obj = ConstantTable.objects.last()
    postback_count = const_obj.postback_count
    postback_skip_count = const_obj.postback_skip_count
    postback_to_send = const_obj.postback_to_send + 1
    postback_to_skip = const_obj.postback_to_skip - 1

    status = False
    if postback_count <= postback_to_send:
        const_obj.postback_count += 1
        status = False
    else:
        if postback_skip_count <= postback_to_skip:
            const_obj.postback_skip_count += 1
            status = True
        else:
            const_obj.postback_skip_count = 1
            const_obj.postback_count = 1
            status = True
    const_obj.save()
    return status
