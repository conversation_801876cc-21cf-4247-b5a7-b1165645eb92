from django.urls import path

from ads_tracker import views

urlpatterns = [
    path("click-id-callback", views.MobidTrackerClickIdView.as_view()),
    path("create-click-id-record", views.CreateClickIdRecordView.as_view()),
    path("update-phone-number-seen-screen", views.UpdateClickIdView.as_view()),
    path("update-morbid-tracker/<pk>", views.UpdateMorbidTrackerView.as_view()),
    path("create-winwise-upselling-record", views.CreateWinwiseUpsellingRecordView.as_view()),
    path("release-pending-postback", views.ReleasePendingPostbacksView.as_view()),
    # path("winwise-upselling-detail/<pk>", views.GetWinwiseUpsellingRecordView.as_view()),
]
