from datetime import datetime, timedelta

import pytz
from django.conf import settings
from django.db import models
from django.db.models import F, Q
from django.utils import timezone

from ads_tracker.models_choices import TrackingSourceChoices
from main.models import ConstantVariable
from wyse_ussd.models import TelcoSubscriptionPlan


class MobidTracker(models.Model):
    """
    This table tracks all the click ids for games promotions
    via any of our promotion channels.
    """

    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
        ("ST_GLO", "ST_GLO"),
    )

    click_id = models.CharField(max_length=1000, unique=True)
    phone_number = models.CharField(max_length=15)
    converted = models.BooleanField(default=False)
    postback_sent = models.BooleanField(default=False)
    amount_played = models.FloatField(default=0.00)
    amount_paid = models.FloatField(default=0.00)
    source = models.CharField(max_length=100, choices=TrackingSourceChoices.choices, default="ORGANIC")
    game_type = models.CharField(max_length=255, blank=True, null=True)
    number_of_renewals = models.IntegerField(default=0)
    telco_network = models.CharField(max_length=100, choices=NETWORK_PROVIDER, blank=True, null=True)
    postback_response = models.TextField(blank=True, null=True)
    controled = models.BooleanField(default=False)
    unsubscribed = models.BooleanField(default=False)
    number_of_unsubsriptions = models.IntegerField(default=0, blank=True, null=True)
    date_unsubscribed = models.DateTimeField(blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-date_created"]
        indexes = [
            models.Index(
                fields=[
                    "phone_number",
                ]
            ),
        ]

    def save(self, *args, **kwargs):
        phone_number = self.phone_number

        formatted_phone = self.phone_number
        if not phone_number.startswith("234"):
            phone_end = phone_number[1::]
            formatted_phone = "234" + phone_end
        self.phone_number = formatted_phone

        return super().save(*args, **kwargs)

    @classmethod
    def update_record_with_anount_paid(cls, phone: str, amount: float, telco_network: str):
        """
        THIS FUNCTION UPDATES THE AMOUNT PLAYED FIELD OF A RECORD

        IF THE RECORD EXISTSM AND WAS CREATED WITHIN THE LAST 1 HOUR
        """

        # get the record
        record = cls.objects.filter(phone_number=phone).last()

        if record:
            # check if the record was created within the last 2 hours
            ONE_HOUR_AGO = datetime.now() - timedelta(hours=1)
            if record.date_created >= ONE_HOUR_AGO:
                record.amount_paid = F("amount_paid") + amount
                record.telco_network = telco_network
                record.save()
                return record
        return None


class ClickId(models.Model):
    """A record of click ids that have been generated for us.
    With this we can determine the number of clicks that may have
    been generated via an ads campaign.
    """

    click_id = models.CharField(max_length=1000, unique=True)
    source = models.CharField(max_length=100, choices=TrackingSourceChoices.choices, default="MORBIDTEK_MEDIA")
    game_type = models.CharField(max_length=255, blank=True, null=True)
    converted = models.BooleanField(default=False)
    phone_number_input_seen = models.BooleanField(default=False)
    loaded_mancala_screen = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-date_created"]


class MarketingCampaignAnalytics(models.Model):
    """A record of marketing campaigns and their analytics."""

    AGENCIES = (("SECURE_D", "SECURE_D"), ("ONE_PIN", "ONE_PIN"))
    total_players = models.IntegerField(default=0)
    churn_players = models.IntegerField(default=0)
    total_sales = models.FloatField(default=0.00)
    rto = models.FloatField(default=0.00)
    cummulative_rto = models.FloatField(default=0.00)
    agency = models.CharField(max_length=100, choices=AGENCIES, default="SECURE_D")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.agency

    class Meta:
        verbose_name = "MARKETING CAMPAIGN ANALYTICS"
        verbose_name_plural = "MARKETING CAMPAIGN ANALYTICS"

    @classmethod
    def create_record(cls, agency, total_players, churn_players, total_sales, rto, cummulative_rto):
        record = cls.objects.using("external").create(
            agency=agency,
            total_players=total_players,
            churn_players=churn_players,
            total_sales=total_sales,
            rto=rto,
            cummulative_rto=cummulative_rto,
        )

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        record.created_at = TODAY
        record.updated_at = TODAY
        record.save(using="external")
        return record

    @classmethod
    def update_record(cls, agency, players_phone_number, amount_paid):
        # check if player has played before today
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        # subscription_record = TelcoSubscriptionPlan.objects.filter(
        #     phone_number=players_phone_number, created_at__month=TODAY.month
        # ).filter(
        #     Q(subscription_status="ACTIVE")
        #     | Q(subscription_status="DEACTIVATED")
        #     | Q(subscription_status="STOPPED")
        # )

        subscription_record = TelcoSubscriptionPlan.objects.filter(phone_number=players_phone_number, created_at__month=TODAY.month)

        is_a_new_subscription = True
        if len(subscription_record) > 1:
            is_a_new_subscription = False

        # calculate rtp

        # telco commission
        ussd_telco_commission = ConstantVariable().get_telco_commission()

        ussd_telco_commission_value = round(amount_paid * ussd_telco_commission, 2)

        # aggregator commission
        ussd_telco_aggregator_commission = ConstantVariable().get_aggregator_commission()
        ussd_telco_aggregator_commission_value = round(amount_paid * ussd_telco_aggregator_commission, 2)

        # amount paid after removing commission
        amount_paid_after_removing_comission = round(
            amount_paid - (ussd_telco_commission_value + ussd_telco_aggregator_commission_value),
            2,
        )

        # telco rtp
        telo_rtp_percentage = (ConstantVariable().telco_rtp_perc()).get("rtp")
        rtp_per = float(telo_rtp_percentage) * 100
        rto_per = (100 - float(rtp_per)) / 100

        # get the rto value
        rto = round(amount_paid_after_removing_comission * rto_per, 2)

        print(
            f"""
        ussd_telco_aggregator_commission: {ussd_telco_aggregator_commission}
        ussd_telco_aggregator_commission_value: {ussd_telco_aggregator_commission_value}
        \n\n

        ussd_telco_commission: {ussd_telco_commission}
        ussd_telco_commission_value: {ussd_telco_commission_value}
        \n\n

        amount_paid_after_removing_comission: {amount_paid_after_removing_comission}

        rtp_per: {rtp_per}
        rto_per: {rto_per}
        rto: {rto}

        """
        )

        # get marketing campaign analytics record
        record = (
            cls.objects.using("external")
            .filter(
                Q(agency=agency),
                Q(created_at__year=TODAY.year) | Q(created_at__month=TODAY.month),
            )
            .last()
        )
        if record is None:
            # last month date
            def get_last_month_date(date_obj):
                """Calculates the accurate last date of the previous month, considering year changes."""

                last_month = date_obj.replace(day=1) - timedelta(days=1)
                return last_month.replace(day=min(last_month.day, date_obj.day))

            last_month = get_last_month_date(TODAY)
            last_month_record = (
                cls.objects.using("external")
                .filter(
                    Q(agency=agency),
                    Q(created_at__year=last_month.year) | Q(created_at__month=last_month.month),
                )
                .last()
            )

            players = 0
            churn_players = 0
            if is_a_new_subscription:
                players = 1
            else:
                churn_players = 1

            if last_month_record is None:
                cls.create_record(agency, players, churn_players, amount_paid, rto, rto)
            else:
                cls.create_record(
                    agency,
                    players,
                    churn_players,
                    amount_paid,
                    rto,
                    last_month_record.cummulative_rto + rto,
                )

        else:
            players = 0
            churn_players = 0
            if is_a_new_subscription:
                players = 1
            else:
                churn_players = 1

            record.total_players = F("total_players") + players
            record.churn_players = F("churn_players") + churn_players
            record.total_sales = F("total_sales") + amount_paid
            record.rto = F("rto") + rto
            record.cummulative_rto = F("cummulative_rto") + rto
            record.save(using="external")


class WinwiseUpselling(models.Model):
    click_id = models.CharField(unique=True, max_length=3000)
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    converted = models.BooleanField(default=False)
    amount_played = models.FloatField(default=0.00)
    amount_paid = models.FloatField(default=0.00)
    source = models.CharField(max_length=100, choices=TrackingSourceChoices.choices, default=TrackingSourceChoices.WINWISE_UPSELLING)
    game_type = models.CharField(max_length=255, blank=True, null=True)
    number_of_renewals = models.IntegerField(default=0)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        phone_number = self.phone_number

        formatted_phone = self.phone_number
        if phone_number and not phone_number.startswith("234"):
            phone_end = phone_number[1::]
            formatted_phone = "234" + phone_end
        self.phone_number = formatted_phone

        return super().save(*args, **kwargs)


class Unsubscription(models.Model):
    """
    This table tracks unsubscriptions
    """

    click_id = models.CharField(max_length=1000, unique=True)
    phone_number = models.CharField(max_length=15)
    converted = models.BooleanField(default=False)
    postback_sent = models.BooleanField(default=False)
    amount_played = models.FloatField(default=0.00)
    amount_paid = models.FloatField(default=0.00)
    source = models.CharField(max_length=100, choices=TrackingSourceChoices.choices, blank=True, null=True)
    game_type = models.CharField(max_length=255, blank=True, null=True)
    number_of_renewals = models.IntegerField(default=0)
    subscription_date = models.DateTimeField(blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def create_unsubscription(cls, phone_number: str = None):
        morbid_tracker_instance = MobidTracker.objects.filter(phone_number=phone_number).last()

        if morbid_tracker_instance:
            cls.objects.create(
                click_id=morbid_tracker_instance.click_id,
                phone_number=morbid_tracker_instance.phone_number,
                converted=morbid_tracker_instance.converted,
                postback_sent=morbid_tracker_instance.postback_sent,
                amount_played=morbid_tracker_instance.amount_played,
                amount_paid=morbid_tracker_instance.amount_paid,
                source=morbid_tracker_instance.source,
                game_type=morbid_tracker_instance.game_type,
                number_of_renewals=morbid_tracker_instance.number_of_renewals,
                subscription_date=morbid_tracker_instance.date_created,
            )
            morbid_tracker_instance.unsubscribed = True
            morbid_tracker_instance.date_unsubscribed = timezone.now()
            morbid_tracker_instance.number_of_unsubsriptions = F("number_of_unsubsriptions") + 1
            morbid_tracker_instance.save()
        else:
            pass
