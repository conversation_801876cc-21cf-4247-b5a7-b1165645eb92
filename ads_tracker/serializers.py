import datetime
import uuid

from rest_framework import serializers

from ads_tracker.models import ClickId, MobidTracker, WinwiseUpselling


class MobidTrackerSerializer(serializers.ModelSerializer):
    class Meta:
        model = MobidTracker
        fields = ["id", "click_id", "phone_number", "source"]

    def validate_phone_number(self, phone_number):
        if len(phone_number) < 11:
            raise serializers.ValidationError("phone number is invalid")
        return phone_number


class ClickIdSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClickId
        fields = ["id", "click_id", "source"]


class WinwiseUpsellingSerializer(serializers.ModelSerializer):
    class Meta:
        model = WinwiseUpselling
        fields = ["click_id"]

    def validate(self, attrs):
        uuid_field = str(uuid.uuid4())
        uuid_str = f'WINWISE{uuid_field}_{datetime.datetime.now().strftime("%m/%d/%Y-%H:%M:%S")}'
        attrs["click_id"] = uuid_str
        self.click_id = uuid_str
        return super().validate(attrs)
