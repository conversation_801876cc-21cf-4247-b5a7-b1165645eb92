import json

from celery import shared_task
from django.db.utils import IntegrityError
from django.db.models import Sum

from ads_tracker.helpers.helpers import (
    get_lotto_amount_paid_for_subscriber,
    get_lotto_subscribers_phone_list,
    postback_skip_decisioning,
    send_ads_tracker_postback_url,
)
from ads_tracker.models import (
    MarketingCampaignAnalytics,
    MobidTracker,
    Unsubscription,
    WinwiseUpselling,
)
from wyse_ussd.enums import PurposeChoices
from wyse_ussd.models import (
    PendingAsyncTask,
    SecureDTransaction,
    TelcoDataSync,
    PendingNitroswitchAsyncTask,
    NitroSwitchDataSync,
    NitroSwitchDailySubscription,
)


@shared_task
def celery_update_marketing_campaign_analytics(agency, players_phone_number, amount_paid):
    MarketingCampaignAnalytics.update_record(agency, players_phone_number, amount_paid)


@shared_task
def update_tracker_renewals_count():
    queryset = MobidTracker.objects.all()

    for query in queryset:
        phone_number = query.phone_number
        all_subscribers_phones_list = get_lotto_subscribers_phone_list(phone_number=phone_number)
        renewals_count = all_subscribers_phones_list.count(phone_number)
        query.number_of_renewals = renewals_count
        query.amount_paid = get_lotto_amount_paid_for_subscriber(phone_number=phone_number)

        if query.converted is False and renewals_count > 0:
            query.converted = True

            if query.postback_sent is False:
                # send_source_postback = send_ads_tracker_postback_url(
                #     source=query.source,
                #     click_id=query.click_id,
                #     amount=query.amount_played
                #     )
                query.postback_sent = True
                # query.postback_response = send_source_postback
        elif renewals_count < 1:
            query.converted = False
        else:
            pass

        query.save()
    return "DONE!!!!"


@shared_task
def update_single_tracker_renewals_count(phone_number):
    query = MobidTracker.objects.filter(phone_number=phone_number).last()
    if query:
        all_subscribers_phones_list = get_lotto_subscribers_phone_list(phone_number=query.phone_number)
        renewals_count = all_subscribers_phones_list.count(phone_number)
        query.number_of_renewals = renewals_count
        query.amount_paid = get_lotto_amount_paid_for_subscriber(phone_number=phone_number)

        if query.converted is False and renewals_count > 0:
            query.converted = True

            if query.postback_sent is False:
                query.postback_sent = True
        elif renewals_count < 1:
            query.converted = False
        else:
            pass

        query.save()
        return "DONE!!!!"
    else:
        pass


@shared_task
def run_send_ads_traffic_postback(reference, data_dumps=None):
    if data_dumps:
        phone_number = data_dumps.get("phone_number")
        reference = data_dumps.get("reference")
        subscription_amount = data_dumps.get("subscription_amount")
        game_type = data_dumps.get("game_type")
        network_provider = data_dumps.get("network_provider")
    else:
        instance = SecureDTransaction.objects.filter(reference=reference).last()
        phone_number = instance.phone_number
        reference = instance.reference
        subscription_amount = instance.subscription_amount
        game_type = instance.game_type
        network_provider = None

    all_subscribers_phones_list = get_lotto_subscribers_phone_list(phone_number=phone_number)
    sent = False
    postback_response = ""
    is_winwise = True if reference.startswith("WINWISE") else False

    if MobidTracker.objects.filter(phone_number=phone_number) and is_winwise is False:
        return "DONE"
    elif phone_number in all_subscribers_phones_list and is_winwise is False:
        return "DONE"
    else:
        pass

    postback_skip = postback_skip_decisioning()

    if reference.startswith("a1"):
        if postback_skip:
            source = "ANGEL_MEDIA"
            sent = False
            postback_response = "pass"
        else:
            send_angel_postback = send_ads_tracker_postback_url(
                source="ANGEL_MEDIA", click_id=reference,
                amount=subscription_amount
                )

            source = "ANGEL_MEDIA"
            sent = True
            postback_response = send_angel_postback

    elif reference.startswith("aazz") and len(reference) == 55:
        if postback_skip:
            source = "MORBIDTEK_MEDIA"
            sent = False
            postback_response = "pass"
        else:
            send_morbidtek_postback = send_ads_tracker_postback_url(
                source="MORBIDTEK_MEDIA", click_id=reference,
                amount=subscription_amount
                )

            postback_response = send_morbidtek_postback
            if send_morbidtek_postback == "Failed":
                pass
            else:
                send_morbidtek_postback = json.loads(send_morbidtek_postback)
                if send_morbidtek_postback.get("error") == 0 and send_morbidtek_postback.get("info") == "Conversion Received.":
                    sent = True
            source = "MORBIDTEK_MEDIA"
    elif "mobitech" in reference:
        # reference = reference.translate({ord(i): None for i in "mobitech}"})
        reference = reference[8:]
        if postback_skip:
            source = "MORBIDTEK_MEDIA"
            sent = False
            postback_response = "pass"
        else:
            send_morbidtek_postback = send_ads_tracker_postback_url(
                source="MORBIDTEK_MEDIA",
                click_id=reference,
                amount=subscription_amount,
            )
            postback_response = send_morbidtek_postback
            if send_morbidtek_postback == "Failed":
                pass
            else:
                send_morbidtek_postback = json.loads(send_morbidtek_postback)
                if send_morbidtek_postback.get("error") == 0 and send_morbidtek_postback.get("info") == "Conversion Received.":
                    sent = True
            source = "MORBIDTEK_MEDIA"
    elif len(reference) == 32:
        if postback_skip:
            source = "MOBPLUS"
            sent = False
            postback_response = "pass"
        else:
            send_mobplus_postback = send_ads_tracker_postback_url(source="MOBPLUS", click_id=reference, amount=subscription_amount)
            postback_response = send_mobplus_postback

            if send_mobplus_postback == "success":
                source = "MOBPLUS"
                sent = True
            else:
                source = "MOBPLUS"
                sent = False
    elif ("," in reference or "%" in reference) and len(reference) > 29:
        if postback_skip:
            source = "TRAFFIC_COMPANY"
            sent = False
            postback_response = "pass"
        else:
            source = "TRAFFIC_COMPANY"

    elif "phoenix" in reference or reference.startswith("sci_") or "_832" in reference:
        if postback_skip:
            source = "Phoenix_MTN"
            sent = False
            postback_response = "pass"
        else:
            send_phoenix_callback = send_ads_tracker_postback_url(source="Phoenix_MTN", click_id=reference, amount=subscription_amount)
            postback_response = send_phoenix_callback
            send_phoenix_callback = json.loads(send_phoenix_callback)
            if send_phoenix_callback.get("code") == 0 and send_phoenix_callback.get("msg") == "report success":
                source = "Phoenix_MTN"
                sent = True
            else:
                pass
    else:
        if reference.startswith("5"):
            if postback_skip:
                source = "GOLDEN_GOOSE"
                sent = False
                postback_response = "pass"
            else:
                send_golden_goose_callback = send_ads_tracker_postback_url(
                    source="GOLDEN_GOOSE",
                    click_id=reference,
                    amount=subscription_amount,
                )
                postback_response = send_golden_goose_callback
                if send_golden_goose_callback == "Received":
                    source = "GOLDEN_GOOSE"
                    sent = True
                else:
                    pass
        else:
            if "vic" in reference:
                if postback_skip:
                    source = "VICTORY_ADS"
                    sent = False
                    postback_response = "pass"
                else:
                    # reference = reference.translate({ord(i): None for i in "vic"})
                    reference = reference[3:]
                    send_victory_ads_callback = send_ads_tracker_postback_url(
                        source="VICTORY_ADS",
                        click_id=reference,
                        amount=subscription_amount,
                    )
                    send_victory_ads_callback = json.loads(send_victory_ads_callback)
                    postback_response = send_victory_ads_callback
                    source = "VICTORY_ADS"
                    if send_victory_ads_callback.get("message") == "conversion recorded successfully":
                        sent = True
            elif "upstream_paidMTN" in reference:
                source = "UPSTREAM"
            elif reference.startswith("golden"):
                if postback_skip:
                    source = "GOLDEN_GOOSE"
                    sent = False
                    postback_response = "pass"
                else:
                    reference = reference[6:]
                    send_golden_golden_callback = send_ads_tracker_postback_url(
                        source="GOLDEN_GOOSE",
                        click_id=reference,
                        amount=subscription_amount,
                    )
                    postback_response = send_golden_golden_callback
                    source = "GOLDEN_GOOSE"
                    sent = True
            elif is_winwise is True:
                source = "WINWISE_UPSELLING"
            elif len(reference) == 47:
                if postback_skip:
                    source = "CLICK_STREAM"
                    sent = False
                    postback_response = "pass"
                else:
                    # reference = reference.translate({ord(i): None for i in 'golden'})
                    send_click_stream_callback = send_ads_tracker_postback_url(
                        source="CLICK_STREAM",
                        click_id=reference,
                        amount=subscription_amount,
                    )
                    postback_response = send_click_stream_callback
                    source = "CLICK_STREAM"
                    sent = True
            elif len(reference) == 61 and reference.startswith("9399_"):
                if postback_skip:
                    source = "MOBIPIUM"
                    sent = False
                    postback_response = "pass"
                else:
                    # reference = reference.translate({ord(i): None for i in 'golden'})
                    send_mobipium_callback = send_ads_tracker_postback_url(
                        source="MOBIPIUM",
                        click_id=reference,
                        amount=subscription_amount,
                    )
                    postback_response = send_mobipium_callback
                    source = "MOBIPIUM"
                    sent = True
            elif "CBT" in reference:
                reference = reference = reference[3:]
                if postback_skip:
                    source = "CLICKBYTE_MEDIA"
                    sent = False
                    postback_response = "pass"
                else:
                    send_click_byte_callback = send_ads_tracker_postback_url(
                        source="CLICKBYTE_MEDIA",
                        click_id=reference,
                        amount=subscription_amount,
                    )
                    postback_response = send_click_byte_callback
                    source = "CLICKBYTE_MEDIA"
                    sent = True
            elif "sigma" in reference:
                reference = reference[5:]
                if postback_skip:
                    source = "SIGMA"
                    sent = False
                    postback_response = "pass"
                else:
                    send_sigma_callback = send_ads_tracker_postback_url(
                        source="SIGMA",
                        click_id=reference,
                        amount=subscription_amount,
                    )
                    postback_response = send_sigma_callback
                    source = "SIGMA"
                    sent = True
            elif "shine" in reference:
                reference = reference[5:]
                if postback_skip:
                    source = "SHINE_DIGITAL"
                    sent = False
                    postback_response = "pass"
                else:
                    send_shine_callback = send_ads_tracker_postback_url(
                        source="SHINE_DIGITAL",
                        click_id=reference,
                        amount=subscription_amount,
                    )
                    postback_response = send_shine_callback
                    source = "SHINE_DIGITAL"
                    sent = True
            else:
                source = "ORGANIC"
    try:
        if is_winwise:
            record, created = WinwiseUpselling.objects.get_or_create(click_id=reference)
            record.phone_number = phone_number
            record.converted = False
            record.amount_played = subscription_amount
            record.game_type = game_type
            record.source = source
            record.save()
        else:
            tracker_record, created = MobidTracker.objects.get_or_create(
                click_id=reference,
                phone_number=phone_number,
                converted=False,
                postback_sent=sent,
                amount_played=subscription_amount,
                game_type=game_type,
                source=source,
                postback_response=postback_response,
                controled=postback_skip,
                telco_network=network_provider,
            )
    except (IntegrityError, UnboundLocalError):
        pass


@shared_task
def resolve_pending_morbid_async_tasks():
    pending_tasks = PendingAsyncTask.objects.filter(is_treated=False, purpose=PurposeChoices.MORBID_RENEWALS)[:5000]

    for task in pending_tasks:
        instance_id = task.telco_datasync_instance_id
        try:
            instance = TelcoDataSync.objects.using("external2").get(id=instance_id)
            serialized_data = instance.serilaized_data
        except TelcoDataSync.DoesNotExist:
            serialized_data = task.payload

        if serialized_data:
            try:
                data = json.loads(serialized_data)

                update_desc = data.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation").get("ns2:updateDesc")

                phone_number = data.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:syncOrderRelation").get("ns2:userID", {}).get("ID")

                upselling_instance = WinwiseUpselling.objects.filter(phone_number=phone_number).last()
                if upselling_instance:
                    upselling_game_type = upselling_instance.game_type
                    amount_paid = get_lotto_amount_paid_for_subscriber(phone_number=phone_number, game_type=upselling_game_type)
                    renewals_count = get_lotto_subscribers_phone_list(phone_number=phone_number, game_type=upselling_game_type)
                    upselling_instance.amount_paid = amount_paid
                    upselling_instance.number_of_renewals = len(renewals_count)
                    if renewals_count:
                        upselling_instance.converted = True
                    upselling_instance.save()

                update_single_tracker_renewals_count(phone_number=phone_number)

                if isinstance(update_desc, str) and update_desc.lower() == "deletion":
                    Unsubscription.create_unsubscription(phone_number=phone_number)
                task.is_treated = True
                task.save()
            except Exception:
                pass

            return "DONE!!!"


@shared_task
def send_marketing_partners_postback_task(instance_id):
    from ads_tracker.tasks import run_send_ads_traffic_postback
    from prices.game_price import secure_d_and_upstream_service_and_prodct_details
    from wyse_ussd.models import SecureDDataDump, NitroSwitchData
    import ast

    instance = SecureDDataDump.objects.get(id=instance_id)

    """
    This signal checks that the trxId is from AngelMedia.
    If trxId/ClickId is from AngelMedia, a conversion record is created.
    """
    instance.postback_processed = True
    instance.save()

    try:
        com_data = instance.data
        try:
            data = json.loads(com_data)
        except Exception:
            form_data = com_data[1 : len(com_data)]
            data = ast.literal_eval(form_data)
            data = json.loads(data)

        trans_ref = data.get("trxId")
        phone_number = data.get("msisdn")
        activation = data.get("activation")
        description = data.get("description")
        productID = data.get("productID")

        if instance.source == "ST_GLO":
            nitroswitch_servicer_codes = NitroSwitchData.subscription_service_codes()
            nitroswitch_equivalent_service_id = NitroSwitchData.get_equivalent_product_code()
            product_code = nitroswitch_equivalent_service_id.get(productID, {})
            subscription_amount = nitroswitch_servicer_codes.get(product_code, {}).get("price", 0.00)
            game_type = nitroswitch_servicer_codes.get(product_code, {}).get("name")
        elif instance.source == "MTN":
            get_game_details_object = secure_d_and_upstream_service_and_prodct_details(product_id=productID)
            subscription_amount = get_game_details_object.get("amount", 0.00)
            game_type = get_game_details_object.get("product_name")
        else:
            subscription_amount = None
            game_type = None

        data = {
            "phone_number": phone_number,
            "reference": trans_ref,
            "subscription_amount": subscription_amount,
            "game_type": game_type,
            "network_provider": instance.source,
        }

        if description.lower() == "success":
            run_send_ads_traffic_postback(
                reference=trans_ref,
                data_dumps=data,
            )
    except Exception as e:
        return f"An exception occurred: {str(e)}"
    return "Done"


@shared_task
def resolve_pending_nitroswitch_async_tasks():
    import ast

    pending_tasks = PendingNitroswitchAsyncTask.objects.filter(is_treated=False, purpose=PurposeChoices.MORBID_RENEWALS)[:5000]

    for task in pending_tasks:
        instance_id = task.telco_datasync_instance_id

        try:
            instance = NitroSwitchDataSync.objects.get(id=instance_id)
            serialized_data = instance.data
        except TelcoDataSync.DoesNotExist:
            serialized_data = task.payload

        if serialized_data:
            try:
                try:
                    data = json.loads(serialized_data)
                except Exception:
                    data = ast.literal_eval(serialized_data)

                phone_number = data.get("msisdn")[0]
                update_desc = data.get("action")[0]

                renewals_qs = NitroSwitchDailySubscription.objects.filter(phone_number=phone_number, amount__gt=0)
                renewals_count = renewals_qs.count()
                renewal_amount = renewals_qs.aggregate(total_amount_paid=Sum("amount"))["total_amount_paid"] or 0.00

                query = MobidTracker.objects.filter(phone_number=phone_number).last()
                if query:
                    query.number_of_renewals = renewals_count
                    query.amount_paid = renewal_amount

                    if query.converted is False and renewals_count > 0:
                        query.converted = True

                        if query.postback_sent is False:
                            query.postback_sent = True
                    elif renewals_count < 1:
                        query.converted = False
                    else:
                        pass

                    query.save()

                if isinstance(update_desc, str) and update_desc[0].lower() == "deletion":
                    Unsubscription.create_unsubscription(phone_number=phone_number)
                task.is_treated = True
                task.save()
            except Exception:
                pass

            return "DONE!!!"


@shared_task
def release_pending_postbacks():
    from wyse_ussd.models import SecureDDataDump
    from django.utils import timezone

    time_now = timezone.now()
    start_date = time_now.replace(hour=0)

    pending_dumps_qs = SecureDDataDump.objects.filter(created_at__gte=start_date)

    for dump in pending_dumps_qs:
        send_marketing_partners_postback_task.apply_async(
            kwargs={
                "instance_id": dump.id,
            },
            queue="celery_ads_postback",
        )
    return "DONE!!!"
