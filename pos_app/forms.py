from django import forms

from .models import Agent


class AgentForm(forms.ModelForm):
    class Meta:
        model = Agent
        fields = "__all__"

    def clean(self):
        cleaned_data = super().clean()
        instance = self.instance
        # changed_fields = []
        if instance is not None:
            # print("instance", instance.icash_sold_dict)
            # print("hderr agent form \n\n\n\n")
            if not instance.icash_sold_dict:
                # print("none icash_sold_dict")
                instance.icash_sold_dict = {"150": 0}
                instance.save()

                cleaned_data["icash_sold_dict"] = {"150": 0}

            if not instance.icash_flavour_dict:
                # print("none icash_flavour_dict")
                instance.icash_flavour_dict = {"150": 0}
                instance.save()

                cleaned_data["icash_flavour_dict"] = {"150": 0}

            for field in self.Meta.fields:
                # Compare form data to instance data to check if field has changed
                if cleaned_data.get(field) != getattr(instance, field):
                    # changed_fields.append(field)
                    if field == "suspended_on_agency_banking":
                        if cleaned_data.get(field) is True:
                            if cleaned_data.get("suspension_reason") is None or cleaned_data.get("suspension_reason") == "":
                                raise forms.ValidationError("To suspend an agent on agency banking, you must provide a reason for the suspension")
                        else:
                            if cleaned_data.get("un_suspension_reason") is None or cleaned_data.get("un_suspension_reason") == "":
                                raise forms.ValidationError(
                                    "To unsuspend an agent on agency banking, you must not provide a reason for the unsuspension"
                                )
        print("cleaned_data", cleaned_data)
        return cleaned_data
