import json
import math
import random
import string
import time
import uuid
from dataclasses import dataclass
from datetime import datetime, timedelta
from random import randint
from typing import Optional

import numpy as np
import redis
import requests
from decouple import config
from django.conf import settings
from django.core.paginator import EmptyPage, PageNotAnInteger, Paginator
from django.db import transaction as db_transaction
from django.db.models import Q, Sum
from rest_framework import status

from main.api.api_lottery_helpers import generate_game_play_id
from main.helpers.redis_storage import RedisStorage
from main.models import (
    ConstantVariable,
    GamePlayEscrow,
    Jackpot,
    JackpotConstantVariable,
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
)
from main.tasks import celery_send_whatsapp_payment_notification_admin
from main.ussd.helpers import Utility

# from overide_print import print
from pos_app.models import (
    AgencyBankingLoginAttempt,
    AgencyBankingToken,
    Agent,
    AgentConstantVariables,
    AgentNoPreFunding,
    AgentWallet,
    AgentWalletTransaction,
    ChargeAgentTransaction,
    GamesDailyActivities,
    GeneralRetailLottoGames,
    LottoAgentRemittanceTable,
    LottoSuperAgents,
    LottoVerticalLeadWallet,
    RetailWalletTransactions,
    SalesRepAgencyDumpData,
    SalesReps,
    SuperAgentAgencyDumpData,
    Supervisor,
    SupervisorAgencyDumpData,
    SupervisorWallet,
)
from pos_app.tasks import (
    celery_agent_commission_reward,
    share_pos_payment_across_lottery_pool,
)
from pos_app.utils import serialize_ticket
from prices.game_price import (
    InstantCashOutPriceModel,
    QuikaPriceModel,
    SalaryForLifePriceModel,
    VirtualSoccerPriceModel,
    WyseCashPriceModel,
)
from wallet_app.models import (
    CommissionWallet,
    DebitCreditRecord,
    ServiceChargeWallet,
    UserWallet,
)
from wallet_system.models import Wallet
from wyse_ussd.models import UssdLotteryPayment


def generate_pin():
    n = 6
    range_start = 10 ** (n - 1)
    range_end = (10**n) - 1
    value = randint(range_start, range_end)
    return str(value)


@dataclass
class PosAgentHelper:
    agent_instance: Optional[Agent] = None
    amount: float = 0.0
    pin: Optional[str] = None

    def agent_login(self):
        return {"access": AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")}

    def reverify_payment(self, reference, auth_token):
        url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/get_service_transaction?unique_reference={reference}"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}",
        }

        response = requests.get(url, headers=headers)

        print(
            f"""

              ---------------------------
              agent reverify_payment response {response.text}

              """
        )

        try:
            return response.json()
        except Exception:
            return {"message": "failed"}

    def charge_agent_wallet(self):
        """
        CHARGE AGENT WALLET


        """
        from wallet_app.models import FloatWallet

        # print("got to charge agent wallet")
        # initiate libertypay buddy to buddy transfer
        # create transaction record

        if self.agent_instance.email in settings.POS_INSTANT_CASHOUT_TEST_FOR_POS_AGENT:
            pass
        else:
            charge_agent_trans_instance = ChargeAgentTransaction.objects.create(
                agent=self.agent_instance,
                amount=self.amount,
            )
        # payload = {
        #     "user_id": self.agent_instance.user_uuid,
        #     "unique_reference": charge_agent_trans_instance.transaction_ref,
        #     "total_amount": self.amount,
        #     "lotto_service_amount": charge_agent_trans_instance.received_amount,
        #     "agent_comm": charge_agent_trans_instance.commission_amount,
        # }

        if ((self.agent_instance.terminal_id is None) or (self.agent_instance.terminal_id == "")) or (
            self.agent_instance.email in settings.POS_INSTANT_CASHOUT_TEST_FOR_POS_AGENT
        ):
            payload = {
                "service_name": "LOTTO_PLAY",
                "user_id": self.agent_instance.user_id,
                "unique_reference": charge_agent_trans_instance.transaction_ref,
                "narration": "LOTTO_PLAY",
                "total_amount": self.amount,
                "service_comm": self.amount,
                "agent_comm": 0,
                "transaction_pin": self.pin,
            }
        else:
            if AgentNoPreFunding.objects.filter(phone_number=self.agent_instance.phone).exists():
                payload = {
                    "service_name": "LOTTO_PLAY",
                    "user_id": self.agent_instance.user_id,
                    "unique_reference": charge_agent_trans_instance.transaction_ref,
                    "narration": "LOTTO_PLAY",
                    "total_amount": self.amount,
                    "service_comm": self.amount,
                    "agent_comm": 0,
                    "transaction_pin": self.pin,
                }
            else:
                payload = {
                    "service_name": "LOTTO_PLAY",
                    "user_id": self.agent_instance.user_id,
                    "unique_reference": charge_agent_trans_instance.transaction_ref,
                    "narration": "LOTTO_PLAY",
                    "total_amount": self.amount,
                    "service_comm": charge_agent_trans_instance.received_amount,
                    "agent_comm": charge_agent_trans_instance.commission_amount,
                    "transaction_pin": self.pin,
                }

        # agent_login
        token = (self.agent_login())["access"]

        print(payload)

        # ---------------- CREATE TRANSACTION INSTANCE ----------------

        agent_wallet = AgentWallet.objects.filter(agent=self.agent_instance).last()

        agent_transaction_instance = AgentWalletTransaction.objects.create(
            transaction_reference=charge_agent_trans_instance.transaction_ref,
            agent_wallet=agent_wallet,
            amount=self.amount,
            transaction_type="DEBIT",
            rewarded_commission=charge_agent_trans_instance.commission_amount,
            transaction_from="LIBERTY_PAY_LOTTO_CHARGE",
            agent_name=agent_wallet.agent.first_name + " " + agent_wallet.agent.last_name,
            agent_phone_number=agent_wallet.agent.phone,
            agent_email=agent_wallet.agent.email,
            terminal_id=agent_wallet.agent.terminal_id,
            terminal_serial_number=agent_wallet.agent.terminal_serial_number,
            wave=agent_wallet.agent.get_wave(),
        )

        if (self.agent_instance.terminal_id is None) or (self.agent_instance.terminal_id == ""):
            agent_commission_transaction_instance = AgentWalletTransaction.objects.create(
                transaction_reference=charge_agent_trans_instance.transaction_ref,
                agent_wallet=agent_wallet,
                amount=charge_agent_trans_instance.commission_amount,
                transaction_type="CREDIT",
                rewarded_commission=charge_agent_trans_instance.commission_amount,
                transaction_from="COMMISSION_ON_GAME_PLAY",
                agent_name=agent_wallet.agent.first_name + " " + agent_wallet.agent.last_name,
                agent_phone_number=agent_wallet.agent.phone,
                agent_email=agent_wallet.agent.email,
                terminal_id=agent_wallet.agent.terminal_id,
                terminal_serial_number=agent_wallet.agent.terminal_serial_number,
                wave=agent_wallet.agent.get_wave(),
            )

        # ---------------- CREATE TRANSACTION INSTANCE ----------------

        # initiate transaction
        url = f"{settings.AGENCY_BANKING_BASE_URL}/send/other_services_charge/"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        try:
            charge_response = requests.post(url, headers=headers, json=payload)

            if charge_response.status_code == 401:
                redis_db = redis.StrictRedis(
                    host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                )
                redis_db.delete("agent_login_token")

                return self.charge_agent_wallet()

            # update float wallet
            FloatWallet().update_float_wallet()

            print(
                f"""

                ---------------------------
                agent charge_response response {charge_response.text}


                """
            )
        except requests.exceptions.RequestException as e:
            agent_transaction_instance.status = "FAILED"
            agent_transaction_instance.save()
            data = {
                "status": "failed",
                "message": e,
            }

            return data

        try:
            reponse = charge_response.json()
        except Exception:
            agent_transaction_instance.status = "FAILED"
            agent_transaction_instance.save()

            data = {
                "status": "failed",
                "message": charge_response.text,
            }

            return data

        # reponse = {
        #     "message": "success",
        #     "data": {
        #         "message": "Transaction completed successfully",
        #         "amount_sent": self.amount,
        #     },
        #     "date_completed": "2022-10-07T18:04:10.155913",
        # }

        """
        charge_response

        {'message': 'success', 'data': {'message': 'Transaction completed successfully', 'amount_sent': 300.0},
        'date_completed': '2022-10-07T18:04:10.155913'}

        """

        # print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")

        if reponse.get("error") is not None:
            # print("got to error")
            agent_transaction_instance.status = "FAILED"
            agent_transaction_instance.save()
            data = {
                "status": "failed",
                "message": reponse.get("message"),
            }

            return data

        print(
            f"charge_response.status_code >>>>>>>>>> {type(charge_response.status_code)}, {charge_response.status_code}"
        )
        if charge_response.status_code == 400 or charge_response.status_code == 500:
            agent_transaction_instance.status = "FAILED"
            agent_transaction_instance.save()

            data = {
                "status": "failed",
                "message": charge_response,
            }

            print("got to 400 or 500")

            return data

        # print("charge response", reponse, "\n\n\n\n\n\n")

        charge_agent_trans_instance.payment_initiated = True
        charge_agent_trans_instance.save()

        # verify charge
        verify_response = self.reverify_payment(charge_agent_trans_instance.transaction_ref, token)
        # verify_response = {
        #     "transaction_type": "LOTTO_PLAY",
        #     "liberty_reference": "LGLP_FND_BUDDY-33e9af00-3ad0-4994-b814-133076dafbb4",
        #     "unique_reference": "('70', '0da873ee-0deb-46ec-844c-49e805430d20')",
        #     "amount": self.amount,
        #     "fee": 0.0,
        #     "sms_charge": 0.0,
        #     "transaction_id": "1863ffe5-5096-46f2-b21a-dba18d7849e1",
        #     "wallet_type": "COLLECTION",
        #     "narration": "LOTTO_PLAY",
        #     "status": "SUCCESSFUL",
        #     "balance_before": 26228.39,
        #     "balance_after": 26378.39,
        #     "beneficiary_account_name": None,
        #     "source_account_name": "CHUKWUEMEKA NWAOMA",
        #     "date_created": "2022-10-07T18:27:32.041907+01:00",
        #     "last_updated": "2022-10-07T18:27:32.459578+01:00",
        # }

        """
        verification response

        {'transaction_type': 'LOTTO_PLAY', 'liberty_reference': 'LGLP_FND_BUDDY-33e9af00-3ad0-4994-b814-133076dafbb4',
        'unique_reference': "('70', '0da873ee-0deb-46ec-844c-49e805430d20')",
        'amount': 150.0, 'fee': 0.0, 'sms_charge': 0.0, 'transaction_id': '1863ffe5-5096-46f2-b21a-dba18d7849e1',
        'wallet_type': 'COLLECTION', 'narration': 'LOTTO_PLAY', 'status': 'SUCCESSFUL', 'balance_before': 26228.39,
        'balance_after': 26378.39, 'beneficiary_account_name': None, 'source_account_name': 'CHUKWUEMEKA NWAOMA',
        'date_created': '2022-10-07T18:27:32.041907+01:00', 'last_updated': '2022-10-07T18:27:32.459578+01:00'}

        """

        # print("verify response", verify_response, "\n\n\n\n\n\n")

        if verify_response.get("status") == "SUCCESSFUL":
            agent_transaction_instance.status = "SUCCESSFUL"
            agent_transaction_instance.save()

            if (self.agent_instance.terminal_id is None) or (self.agent_instance.terminal_id == ""):
                agent_commission_transaction_instance.status = "SUCCESSFUL"
                agent_commission_transaction_instance.save()

            return {"message": "success", "data": verify_response}

        else:
            agent_transaction_instance.status = "FAILED"
            agent_transaction_instance.save()

            return {"message": "failed", "data": verify_response}

    def charge_agent_wallet_for_lotto_tickets(self):
        from wallet_app.models import FloatWallet

        charge_agent_trans_instance = ChargeAgentTransaction.objects.create(
            agent=self.agent_instance,
            amount=self.amount,
        )

        payload = {
            "service_name": "LOTTO_PLAY",
            "user_id": self.agent_instance.user_id,
            "unique_reference": charge_agent_trans_instance.transaction_ref,
            "narration": "LOTTO_PLAY",
            "total_amount": self.amount,
            "service_comm": self.amount,
            "agent_comm": 0,
            "transaction_pin": self.pin,
        }

        token = (self.agent_login())["access"]
        agent_wallet = AgentWallet.objects.filter(agent=self.agent_instance).last()

        print(f"CHARGE REF :: {charge_agent_trans_instance.transaction_ref}")
        agent_transaction_instance = AgentWalletTransaction.objects.create(
            transaction_reference=charge_agent_trans_instance.transaction_ref,
            agent_wallet=agent_wallet,
            amount=self.amount,
            transaction_type="DEBIT",
            rewarded_commission=charge_agent_trans_instance.commission_amount,
            transaction_from="LIBERTY_PAY_LOTTO_CHARGE",
            agent_name=agent_wallet.agent.first_name + " " + agent_wallet.agent.last_name,
            agent_phone_number=agent_wallet.agent.phone,
            agent_email=agent_wallet.agent.email,
            terminal_id=agent_wallet.agent.terminal_id,
            terminal_serial_number=agent_wallet.agent.terminal_serial_number,
            wave=agent_wallet.agent.get_wave(),
        )

        # initiate transaction
        url = f"{settings.AGENCY_BANKING_BASE_URL}/send/other_services_charge/"
        # url = f"http://127.0.0.1:9091/send/other_services_charge/"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        try:
            charge_response = requests.post(url, headers=headers, json=payload)

            if charge_response.status_code == 401:
                redis_db = redis.StrictRedis(
                    host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                )
                redis_db.delete("agent_login_token")

                return self.charge_agent_wallet_for_lotto_tickets()

            print(f"AGENCY BANKING RESP :: {charge_response} ")

            # update float wallet
            FloatWallet().update_float_wallet()

            print(
                f"""

                ---------------------------
                agent charge_response response {charge_response.text}


                """
            )
        except requests.exceptions.RequestException as e:
            agent_transaction_instance.status = "FAILED"
            agent_transaction_instance.save()
            data = {
                "status": "failed",
                "message": e,
            }
            print(f"AGENCY BANKING RESP :: {e} ")
            return data

        try:
            reponse = charge_response.json()
        except Exception:
            agent_transaction_instance.status = "FAILED"
            agent_transaction_instance.save()

            data = {
                "status": "failed",
                "message": charge_response.text,
            }

            return data

        if reponse.get("error") is not None:
            # print("got to error")
            agent_transaction_instance.status = "FAILED"
            agent_transaction_instance.save()
            data = {
                "status": "failed",
                "message": reponse.get("message"),
            }

            return data

        print(
            f"charge_response.status_code >>>>>>>>>> {type(charge_response.status_code)}, {charge_response.status_code}"
        )
        if charge_response.status_code == 400 or charge_response.status_code == 500:
            agent_transaction_instance.status = "FAILED"
            agent_transaction_instance.save()

            data = {
                "status": "failed",
                "message": charge_response,
            }

            print("got to 400 or 500")

            return data

        charge_agent_trans_instance.payment_initiated = True
        charge_agent_trans_instance.save()

        # verify charge
        verify_response = self.reverify_payment(charge_agent_trans_instance.transaction_ref, token)
        if verify_response.get("status") == "SUCCESSFUL":
            agent_transaction_instance.status = "SUCCESSFUL"
            agent_transaction_instance.save()

            return {"message": "success", "data": verify_response}

        else:
            agent_transaction_instance.status = "FAILED"
            agent_transaction_instance.save()

            return {"message": "failed", "data": verify_response}

    def handle_agent_charge_and_lottery_play(
        self,
        lottery_qs,
        instant_cashout_game=False,
        return_after_successfull_charge=False,
        _game_play_id=None,
        lottery_type=None,
    ):
        # get agent play balance
        agent_wallet = AgentWallet.objects.filter(agent=self.agent_instance).last()

        if agent_wallet is None:
            agent_wallet = AgentWallet.objects.create(
                agent=self.agent_instance,
                agent_name=f"{self.agent_instance.first_name} {self.agent_instance.last_name}",
                agent_phone_number=self.agent_instance.phone,
                agent_email=self.agent_instance.email,
            )

        print("agent_wallet.game_play_bal", agent_wallet.game_play_bal, "\n")
        print("self.amount", self.amount, "\n")
        if agent_wallet.game_play_bal < self.amount:
            # charge_agent_res = self.charge_agent_wallet().get("message")

            data = {
                "status": "failed",
                "message": "Insufficient balance",
            }

            return data

        else:
            transaction_ref = f"{uuid.uuid4()}{int(time.time())}{self.agent_instance.id}"

            try:
                lottery_type = lottery_qs.first().lottery_type
            except Exception:
                lottery_type = lottery_type

            charge_response = PosAgentHelper.charge_agent_play_wallet(
                agent_id=self.agent_instance.id,
                amount=self.amount,
                transaction_ref=transaction_ref,
                ticket_instance="GAME_PLAY",
                game_play_id=_game_play_id,
                game_type=lottery_type,
            )

            charge_agent_res = charge_response.get("message")

            print("charge_agent_res", charge_agent_res, "\n\n")

        if return_after_successfull_charge is True:
            return charge_agent_res

        if charge_agent_res == "success":
            lottery_instance = lottery_qs.last()

            """ CATERING FOR SERVICE CHARGES AND VALIDATING PAYMENT AMOUNT """
            _lottery_types = [
                "INSTANT_CASHOUT",
                "WYSE_CASH",
                "SALARY_FOR_LIFE",
                "VIRTUAL_SOCCER",
                "QUIKA",
            ]

            GamePlayEscrow.objects.create(
                game_play_id=lottery_instance.game_play_id,
                game_type=lottery_instance.lottery_type,
                batch_uuid=lottery_instance.batch.batch_uuid,
            )

            if lottery_instance.lottery_type in _lottery_types:
                if lottery_instance.lottery_type == "QUIKA":
                    quika_price_model = QuikaPriceModel()
                    price_model = quika_price_model = quika_price_model.get_ticket_price_details_with_stake_amount(
                        channel="POS", stake_amount=self.amount
                    )
                    if isinstance(price_model, dict):
                        africastalking_service_charge = price_model.get("africastalking_charge", 0)
                        woven_charge = price_model.get("woven_service_charge", 0)

                        if africastalking_service_charge > 0:
                            ServiceChargeWallet().add_fund(
                                amount=africastalking_service_charge,
                                service="AFRICASTALKING",
                            )

                        if woven_charge > 0:
                            ServiceChargeWallet().add_fund(amount=woven_charge, service="WOVEN")

                elif lottery_instance.lottery_type == "INSTANT_CASHOUT":
                    print("CHARGE WAS SUCCESSFUL AND GOT TO INSTANT CASHOUT")
                    i_cash_price_model = InstantCashOutPriceModel()
                    price_model = i_cash_price_model.get_ticket_price_details_with_stake_amount(
                        channel="POS", stake_amount=self.amount
                    )
                    if isinstance(price_model, dict):
                        africastalking_service_charge = price_model.get("africastalking_charge", 0)
                        woven_charge = price_model.get("woven_service_charge", 0)

                        if africastalking_service_charge > 0:
                            ServiceChargeWallet().add_fund(
                                amount=africastalking_service_charge,
                                service="AFRICASTALKING",
                            )

                        if woven_charge > 0:
                            ServiceChargeWallet().add_fund(amount=woven_charge, service="WOVEN")

                elif lottery_instance.lottery_type == "WYSE_CASH":
                    wyse_cash_model = WyseCashPriceModel()
                    price_model = wyse_cash_model.ticket_price(channel="POS", band=lottery_instance.band, no_of_line=1)

                    if isinstance(price_model, dict):
                        africastalking_service_charge = price_model.get("africastalking_charge", 0)
                        woven_charge = price_model.get("woven_service_charge", 0)

                        if africastalking_service_charge > 0:
                            ServiceChargeWallet().add_fund(
                                amount=africastalking_service_charge,
                                service="AFRICASTALKING",
                            )

                        if woven_charge > 0:
                            ServiceChargeWallet().add_fund(amount=woven_charge, channel="WOVEN")

                elif lottery_instance.lottery_type == "SALARY_FOR_LIFE":
                    sal_4_life_price_model = SalaryForLifePriceModel()
                    price_model = sal_4_life_price_model.get_ticket_price_details_with_stake_amount(
                        channel="POS", stake_amount=self.amount
                    )
                    if isinstance(price_model, dict):
                        africastalking_service_charge = price_model.get("africastalking_charge", 0)
                        woven_charge = price_model.get("woven_service_charge", 0)

                        if africastalking_service_charge > 0:
                            ServiceChargeWallet().add_fund(
                                amount=africastalking_service_charge,
                                service="AFRICASTALKING",
                            )

                        if woven_charge > 0:
                            ServiceChargeWallet().add_fund(amount=woven_charge, service="WOVEN")

                elif lottery_instance.lottery_type == "VIRTUAL_SOCCER":
                    virtual_soccer_price_model = VirtualSoccerPriceModel()
                    price_model = virtual_soccer_price_model.get_ticket_price_details_with_stake_amount(
                        channel="POS", stake_amount=self.amount
                    )
                    if isinstance(price_model, dict):
                        africastalking_service_charge = price_model.get("africastalking_charge", 0)
                        woven_charge = price_model.get("woven_service_charge", 0)

                        if africastalking_service_charge > 0:
                            ServiceChargeWallet().add_fund(
                                amount=africastalking_service_charge,
                                service="AFRICASTALKING",
                            )

                        if woven_charge > 0:
                            ServiceChargeWallet().add_fund(amount=woven_charge, service="WOVEN")

            # config illusion for pos agent on successful charge
            if lottery_instance.lottery_type == "QUIKA":
                in_excess_deduction = 200 if lottery_instance.expected_amount >= 1000 else 100
                from main.helpers.illution_feature import GameIllusion

                GameIllusion().set_excess_amount(amount=in_excess_deduction, game_type="QUIKA")
            else:
                pass

            share_pos_payment_response = share_pos_payment_across_lottery_pool(
                lottery_instance.user_profile.phone_number,
                self.amount,
                lottery_instance.game_play_id,
            )

            if isinstance(share_pos_payment_response, dict) and share_pos_payment_response.get("status") == "failed":
                data = {
                    "status": "failed",
                    "message": share_pos_payment_response.get("message"),
                }

                return data

            agent_played_for_self = False

            if lottery_instance.user_profile.phone_number == lottery_instance.agent_profile.phone:
                agent_played_for_self = True
            if lottery_instance.lottery_type == "AWOOF":
                pass
            else:
                celery_send_whatsapp_payment_notification_admin.apply_async(
                    queue="celery3",
                    kwargs={
                        "phone_number": lottery_instance.user_profile.phone_number,
                        "batch_id": lottery_instance.batch.batch_uuid,
                        "amount": self.amount,
                        "paid_via": f"POS AGENT {self.agent_instance.first_name} {self.agent_instance.last_name}",
                        "agent_played_for_self": agent_played_for_self,
                    },
                )

            # celery_send_whatsapp_payment_notification_admin.delay(
            #     phone_number=lottery_instance.user_profile.phone_number,
            #     batch_id=lottery_instance.batch.batch_uuid,
            #     amount=self.amount,
            #     paid_via=f"POS AGENT {self.agent_instance.first_name} {self.agent_instance.last_name}",
            # )

            data = {
                "status": "success",
                "lottery_type": lottery_instance.lottery_type,
                "game_play_id": lottery_instance.game_play_id,
            }

            # update_duplicate_lottery_to_paid.delay(
            #     lottery_instance.game_play_id, lottery_instance.lottery_type
            # )

            return data

        else:
            data = {
                "status": "failed",
                "message": charge_agent_res,
            }

            return data

    def agent_commission_reward(self, **kwargs):

        url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/give_commision_from_me/"

        token = AgencyBankingToken.retrieve_token("RETAIL_COMMISSION_WALLET")
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        response = requests.post(url, headers=headers, json=kwargs)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(
                host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
            )
            redis_db.delete("agency_banking_retail_commission_wallet_login")

            return self.agent_commission_reward(**kwargs)

        try:
            return response.json()
        except Exception:
            return response.text

    def fund_agent_winning_wallet(self) -> bool:
        """
        FUND AGENT WINNING WALLET
        """

        # print("agent id in fund reward", self.agent_instance)
        # agent_wallet, created = AgentWallet.objects.get_or_create(
        #     agent=self.agent_instance
        # )

        # agent_wallet.winnings_bal += self.amount
        # agent_wallet.save()

        return {"message": "Agent winning wallet funded"}

    def withdrawal(self):
        pass

    def verify_agent_transaction_pin(self, pin) -> bool:
        if settings.DEBUG is True or settings.DEBUG:
            print("DEBUG IS ON::::::::::::::::::::::::::::::")
            return True

        # agent_login
        token = (self.agent_login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        url = f"{settings.AGENCY_BANKING_BASE_URL}/agency/other_service_verify_trans_pin/"

        # payload = {"transaction_pin": pin}

        payload = {"user_id": self.agent_instance.user_id, "transaction_pin": pin}

        if settings.DEBUG is True or settings.DEBUG:
            return True

        response = requests.post(url, headers=headers, json=payload)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(
                host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
            )
            redis_db.delete("agent_login_token")

            return self.verify_agent_transaction_pin(pin)

        try:
            res = response.json()
        except Exception:
            res = response.text

        print(
            f"""

        AGENT TRANSACTION PIN VERIFICATION RESPONSE: {res}
        \n\n\n\n\n\n\n

        """
        )

        # if res.get("error") == "651":
        #     print("AGENT TRANSACTION PIN VERIFICATION RESPONSE: ", res)
        #     redis_db = redis.StrictRedis(
        #         host="localhost",
        #         port="6379",
        #         db=0,
        #         decode_responses=True,
        #         encoding="utf-8",
        #     )
        #     redis_db.delete("agent_login_token")

        #     return self.verify_agent_transaction_pin(pin)

        if not isinstance(res, dict):
            return False

        if res.get("message", {}) == "Pin Correct":
            return True

        elif res.get("message", {}) == "Incorrect Pin":
            return False

        else:
            return False

    def notify_agent_on_lottery_win(self):
        pass

    def fetch_buddy_account(self, phone):
        url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/{phone}/"

        token = (self.agent_login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 401:
            return response.text

        """
        ERROR RESPONSE:
        {'status': 'error', 'message': 'Buddy Does Not Exist', 'invite_user': 'https://backend.libertypayng.com/invite_new_user/*************/'}


        CORRECT RESPONSE:
        {'message': 'buddy found', 'data': {'full_name': 'JOSEPH OGBU', 'phone_number': '*************'}}

        """

        try:
            return response.json()
        except Exception:
            return response.text

    def agency_buddy_transfer(self, **kwargs):
        from wallet_app.models import FloatWallet

        if not str(config("ENVIRONMENT")).lower().startswith("pro"):
            return {"message": "TEST PAYOUT FROM LOTTO BACKEND"}

        is_late_withdrawal = kwargs.get("is_late_withdrawal", False)
        from_commission_wallet = kwargs.get("from_commission_wallet", False)

        if is_late_withdrawal is False:
            kwargs["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN
        else:
            kwargs["transaction_pin"] = config("PENDING_LATE_WITHDRAWAL_WALLET_TRANSACTION_PIN")

        if from_commission_wallet is True:
            kwargs["transaction_pin"] = config("AGENCY_BANKING_RETAIL_COMMISSION_WALLET_TRANSACTION_PIN")

        # Check VFD Float wallet and check if it has enough balance
        amount = kwargs.get("data")[0].get("amount")
        vfd_float_wallet = FloatWallet().get_float_wallet(source="RETAIL_RTP_WALLET")

        if vfd_float_wallet.amount < amount:
            return {"message": "Insufficient balance"}

        verify_buddy_account = self.fetch_buddy_account(self.agent_instance.phone)

        if isinstance(verify_buddy_account, dict):
            if verify_buddy_account.get("status") == "error" or "Not Exist" in verify_buddy_account.get("message"):
                return {" ====================== message": "Account not found ========================="}

            else:
                payload = kwargs

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

                if is_late_withdrawal is False:
                    token = AgencyBankingToken.retrieve_token("RETAIL_RTP_WALLET")
                else:
                    token = AgencyBankingToken.retrieve_token("PENDING_LATE_WITHDRAWAL_WALLET")

                if from_commission_wallet is True:
                    token = AgencyBankingToken.retrieve_token("RETAIL_COMMISSION_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                try:
                    del payload["is_late_withdrawal"]
                except:
                    pass

                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        return response.text

                    # update float wallet
                    FloatWallet().update_float_wallet()

                    """

                    SAMPLE RESPONSE:
                    {'message': 'success', 'data': {'message': 'Transaction completed successfully', 'amount_sent': 10.0}, 'date_completed': '2022-12-07T16:03:27.661556'}


                    """
                    return response.json()
                except Exception as e:
                    return {"message": "an error occured", "error": str(e)}

        else:
            return {"message": verify_buddy_account}
        
    def agency_buddy_transfer_commission(self, **kwargs):
        from wallet_app.models import FloatWallet

        if not str(config("ENVIRONMENT")).lower().startswith("pro"):
            return {"message": "TEST PAYOUT FROM LOTTO BACKEND"}
        
        
        kwargs["transaction_pin"] = config("AGENCY_BANKING_RETAIL_COMMISSION_WALLET_TRANSACTION_PIN")

        verify_buddy_account = self.fetch_buddy_account(self.agent_instance.phone)

        if isinstance(verify_buddy_account, dict):
            if verify_buddy_account.get("status") == "error" or "Not Exist" in verify_buddy_account.get("message"):
                return {" ====================== message": "Account not found ========================="}

            else:
                token = AgencyBankingToken.retrieve_token("RETAIL_COMMISSION_WALLET")
                
                payload = kwargs

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }
                
                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"


                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        return response.text

                    # update float wallet
                    FloatWallet().update_float_wallet()

                    """

                    SAMPLE RESPONSE:
                    {'message': 'success', 'data': {'message': 'Transaction completed successfully', 'amount_sent': 10.0}, 'date_completed': '2022-12-07T16:03:27.661556'}


                    """
                    return response.json()
                except Exception as e:
                    return {"message": "an error occured", "error": str(e)}

        else:
            return {"message": verify_buddy_account}

    @staticmethod
    def charge_agent_play_wallet(
        agent_id,
        amount,
        transaction_ref,
        ticket_instance="GAME_PLAY",
        game_play_id=None,
        game_type=None,
    ) -> dict:
        """

        Sorry for naming it ticket_instance, it was a mistake, it should be ticket_type
        Retail or just game play(RETAIL OR GAME_PLAY)

        """

        print(
            """

              charge_agent_play_wallet \n\n\n\n\n\n

              """
        )
        ticket_instances = ["RETAIL", "GAME_PLAY"]
        if ticket_instance.upper() not in ticket_instances:
            print("charge_agent_play_wallet Invalid ticket instance")
            return {"message": "Invalid ticket instance"}

        agent = Agent.objects.filter(id=agent_id).last()
        if agent is None:
            print("charge_agent_play_wallet Agent not found")
            return {"message": "Agent not found"}

        agent_wallet = AgentWallet.objects.filter(agent=agent).last()
        if agent_wallet is None:
            print("charge_agent_play_wallet Agent wallet not found")
            return {"message": "Agent wallet not found"}

        charge_agent_trans_instance = ChargeAgentTransaction.objects.create(
            agent=agent,
            amount=amount,
        )

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent.phone,
            amount=amount,
            channel="POS/MOBILE",
            reference=transaction_ref,
            transaction_type="DEBIT",
        )

        wallet_payload = {
            "transaction_from": "GAME_PLAY",
            "game_type": game_type,
            "game_play_id": game_play_id,
        }
        UserWallet.deduct_wallet(
            user=agent,
            amount=amount,
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="GAME_PLAY_WALLET",
            **wallet_payload,
        )

        # # reward agent commission
        # pos_agent_helper = PosAgentHelper(
        #     agent_instance=agent,
        #     amount=charge_agent_trans_instance.commission_amount,
        # )

        if agent.email in settings.POS_INSTANT_CASHOUT_TEST_FOR_POS_AGENT:
            pass
        else:
            # print(
            #     f"celery_agent_commission_reward, {charge_agent_trans_instance.commission_amount} \n\n\n\n\n\n\n"
            # )
            if AgentNoPreFunding.objects.filter(phone_number=agent.phone).exists():
                pass
            else:
                celery_agent_commission_reward.delay(
                    agent_id=agent.id,
                    amount=charge_agent_trans_instance.commission_amount,
                )
                # pass

        return {"message": "success"}

    @staticmethod
    def static_agent_login():
        return {"access": AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")}

    @staticmethod
    def create_agent_vfd_wallet(agent_instance):
        url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/create_personal_account/"
        payload = {"user_id": agent_instance.user_id, "provider": "VFD"}

        token = (PosAgentHelper.static_agent_login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        response = requests.post(url, json=payload, headers=headers)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(
                host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
            )
            redis_db.delete("agent_login_token")

            return PosAgentHelper.create_agent_vfd_wallet(agent_instance)

        print(
            f"""
              create_agent_vfd_wallet response: {response.text}, \n\n\n\n
              """
        )

        """
            "status": "success",
            "message": "account successfully created",
            "account_provider": created_acc.account_provider,
            "user_id": user.id,
            "data": {
                    "account_number": created_acc.account_number,
                    "account_name": created_acc.account_name,
                    "account_type": created_acc.account_type,
                    "bank_name": created_acc.bank_name,
                    "bank_code": created_acc.bank_code,
                    "is_test": created_acc.is_test,
                    "is_active": created_acc.is_active,
                    "timestamp": created_acc.date_created,
                },
            }


        FAILED RESPONSE:
            "status": "error",
                "error_code": "92",
                "message": "account failed to create",
                "account_provider": "VFD",
                "user_id": user.id,
                "data": {}
            }
        """
        print(f"create agent vfd wallet response {response.text}")
        return response.json()

    def charge_defaulted_remittance(self, **payload):
        if settings.DEBUG is True or settings.DEBUG:
            return None

        url = f"{settings.AGENCY_BANKING_BASE_URL}/send/other_services_charge/"

        # agent_login
        token = (self.agent_login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
            "Super-Token": self.get_agency_banking_super_token(),
        }

        response = requests.post(url, json=payload, headers=headers)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(
                host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
            )
            redis_db.delete("agent_login_token")

            return self.charge_defaulted_remittance(**payload)

        try:
            res = response.json()
        except Exception:
            res = response.text

        return res

    def suspend_lotto_agent_on_agency_banking(self, **payload):
        if settings.DEBUG is True or settings.DEBUG:
            None

        url = f"{settings.AGENCY_BANKING_BASE_URL}/agency/others_suspend_user/"

        # agent_login
        token = (self.agent_login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        response = requests.post(url, json=payload, headers=headers)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(
                host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
            )
            redis_db.delete("agent_login_token")

            return self.suspend_lotto_agent_on_agency_banking(**payload)

        try:
            res = response.json()
        except Exception:
            res = response.text

        return res

    def get_agency_banking_super_token(self):
        """
        response = {
            "error": "890",
            "message": "Incorrect Service User on Agency Banking List or Cannot Generate Super Token"
        }



        response = {
                        "error": "891",
                        "message": "IP not whitelisted"
                    }


        response = {
                    "status": "success",
                    "super_token": "dcfjdscfdsfffwefewfewfaefaesfraeferagfergergergergreagdfraervdscsdcmnjDSJOCADKBVCKJDSHCOIDSHIEHVIUDSGCYHBWJDBGASYCGXUAIHDKjxhUHkjhuaguGHSKJCDJVIDHJVKIHVUDGHUJV"
                }
        """

        redis_db = redis.StrictRedis(
            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
        )
        agency_super_token = redis_db.get("agency_banking_super_token")

        if agency_super_token is None:
            url = f"{settings.AGENCY_BANKING_BASE_URL}/agency/generate_super_token/"

            auth_token = PosAgentHelper.static_agent_login().get("access")

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {auth_token}",
            }

            response = requests.get(url, headers=headers)

            if response.status_code == 401:
                redis_db = redis.StrictRedis(
                    host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                )
                redis_db.delete("agent_login_token")

                return self.get_agency_banking_super_token()

            try:
                response = requests.get(url, headers=headers)
                res = response.json()

                if res.get("status") == "success":
                    redis_db.set(
                        "agency_banking_super_token",
                        res.get("super_token"),
                        timedelta(minutes=20),
                    )
                    return res.get("super_token")
                else:
                    return None
            except Exception:
                return None
        else:
            return agency_super_token

    def check_deposit_charge_on_agency_banking(self):
        if settings.DEBUG is True or settings.DEBUG:
            return {"message": "TEST PAYOUT FROM LOTTO BACKEND"}

        token = (self.agent_login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        payload = {"amount": self.amount, "user_email": self.agent_instance.email}

        """
        SAMPLE RESPONSE:

        {
            "charge": 5.0,
        }


        """

        url = f"{settings.AGENCY_BANKING_BASE_URL}/horizon/api/get_agent_deposit_fee/"

        response = requests.post(url, headers=headers, json=payload)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(
                host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
            )
            redis_db.delete("agent_login_token")

            return self.check_deposit_charge_on_agency_banking()

        try:
            res = response.json()
        except Exception:
            res = response.text

        return res


@dataclass
class PosLottoHelper:
    @staticmethod
    def play_lotto(user_instance, agent_instance, serialized_data, lotto_type, channel) -> dict:
        """
        PLAY LOTTO
        """

        from main.models import LotteryBatch, LottoTicket, UserProfile

        get_game_play_id = generate_game_play_id()
        identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"
        pin = generate_pin()

        lotto_data = serialized_data

        user_profile = UserProfile.objects.get(phone_number=user_instance.phone_number)

        if user_profile is None:
            return {"status": "error", "message": "user profile not found"}

        amount = lotto_data.get("amount")
        phone = lotto_data.get("phone_number")
        lottery = lotto_data.get("lottery")

        # ------------------------------- CHECK BATCH STATUS ------------------------------- #

        get_current_batch = LotteryBatch.objects.filter(is_active=True, lottery_type=lotto_type).last()

        if get_current_batch is None:
            if lotto_type.upper() == "SALARY_FOR_LIFE":
                from main.models import LotteryGlobalJackPot

                global_jackpot = LotteryGlobalJackPot().get_jackpot_instance()

                if global_jackpot is None:
                    return {"status": "error", "message": "No active batch found"}

                get_current_batch = LotteryBatch.objects.create(
                    lottery_type=lotto_type.upper(), global_jackpot=global_jackpot
                )

                time.sleep(3)

            else:
                get_current_batch = LotteryBatch.objects.create(lottery_type=lotto_type.upper())

                time.sleep(3)

        # ------------------------------- CHECK BATCH STATUS ------------------------------- #

        # check lotto type. if it's SALARY_FOR_LIFE play and return data.
        # if it's INSTANT_CASHOUT play and draw the lotto and return data.

        lottery_generated_cashout_pin = generate_pin()

        if lotto_type == "SALARY_FOR_LIFE":
            number_of_ticket = len(lottery)
            # expected_amount = int(number_of_ticket) * int(amount)

            sal_4_life_price_model = SalaryForLifePriceModel()
            sal_4_life_price = sal_4_life_price_model.ticket_price(channel="POS", ticket_line=number_of_ticket)

            if sal_4_life_price is None:
                return {"status": "error", "message": "Invalid ticket line"}

            ticket_actual_stake = sal_4_life_price.get("ticket_price")
            expected_amount = (
                sal_4_life_price.get("ticket_price")
                + sal_4_life_price.get("woven_service_charge", 0)
                + sal_4_life_price.get("africastalking_charge", 0)
            )

            data = {
                "status": "Accepted",
                "agent_id": agent_instance.user_id,
                "ticket_owner": user_instance.phone_number,
                "game_id": get_game_play_id,
                "game_type": lotto_type,
                "stake_per_pick": expected_amount / number_of_ticket,
                "total_stake": expected_amount,
                "total_ticket": number_of_ticket,
                "tickets": lottery,
            }

            _channel = "POS_AGENT" if channel == "pos" else "MOBILE"

            user_instance.save()

            lottery_with_db_id = []

            # new_amount = LottoTicket.salary_for_life_stake_amount_pontential_winning(
            #     number_of_ticket
            # ).get("stake_amount")

            for lottery_number in lottery:
                lottery_instance = LottoTicket.objects.create(
                    user_profile=user_profile,
                    agent_profile=agent_instance,
                    batch=get_current_batch,
                    phone=phone,
                    stake_amount=ticket_actual_stake / number_of_ticket,
                    expected_amount=expected_amount / number_of_ticket,
                    potential_winning=sal_4_life_price.get("potential_winning"),
                    paid=False,
                    number_of_ticket=number_of_ticket,
                    channel=_channel,
                    game_play_id=get_game_play_id,
                    lottery_type=lotto_type,
                    ticket=serialize_ticket(lottery_number.get("ticket")),
                    pin=pin,
                    identity_id=identity_id,
                )

                """

                    CHECK if the channel is POS_AGENT and if it is,
                    check if the agent uses his phone number to play, if he does,
                    generate 6 digit pin for the ticket, for cashout if the case may be.
                """

                lottery_with_db_id.append(
                    {
                        "ticket": lottery_number.get("ticket"),
                        "id": lottery_instance.id,
                    }
                )

                # check if duplicate ticket for POS channel is turned on
                ConstantVariable.get_constant_variable().get("create_pos_duplicate_lottery")

            UssdLotteryPayment.objects.create(
                user=user_profile,
                amount=int(expected_amount),
                game_play_id=get_game_play_id,
                channel=_channel,
                lottery_type=lotto_type,
            )

            # try:
            #     PosAgentHelper(agent_instance, int(amount)).agent_commission_reward(
            #         narration="COMMISSION_ON_GAME_PLAY",
            #         winning_commision=False,
            #     )
            # except Exception:
            #     pass

            data["tickets"] = lottery_with_db_id

            if lottery_instance.channel == "POS_AGENT":
                if Agent.objects.filter(phone=lottery_instance.user_profile.phone_number).exists():
                    del data["ticket_owner"]
                    data["pin"] = lottery_generated_cashout_pin

            return data

        elif lotto_type == "INSTANT_CASHOUT":
            number_of_ticket = len(lottery)
            # print("registered amount:::", amount)
            # expected_amount = int(number_of_ticket) * int(amount)

            amount = int(amount)

            # get_potential_winning = I_CASH_UPDATED_AMOUNT[amount].get("winning_amount")

            instant_cashout_price_model = InstantCashOutPriceModel()
            instant_cashout_price = instant_cashout_price_model.ticket_price(
                channel="POS", ticket_line=number_of_ticket
            )

            stake_amount = instant_cashout_price.get("total_amount") / number_of_ticket

            if instant_cashout_price is None:
                return {"status": "error", "message": "Invalid ticket line"}

            ticket_actual_stake = instant_cashout_price.get("total_amount")
            expected_amount = (
                instant_cashout_price.get("total_amount")
                + instant_cashout_price.get("woven_service_charge", 0)
                + instant_cashout_price.get("africastalking_charge", 0)
            )
            get_potential_winning = instant_cashout_price.get("potential_winning")

            data = {
                "status": "Accepted",
                "agent_id": agent_instance.user_uuid,
                "ticket_owner": user_instance.phone_number,
                "game_id": get_game_play_id,
                "game_type": lotto_type,
                "stake_per_pick": instant_cashout_price.get("ticket_price") / number_of_ticket,
                "total_stake": instant_cashout_price.get("ticket_price"),
                "total_ticket": number_of_ticket,
            }

            _channel = "POS_AGENT" if channel == "pos" else "MOBILE"

            lottery_with_db_id = []

            # new_amount = LottoTicket.instant_stake_amount_pontential_winning(
            #     number_of_ticket
            # ).get("stake_amount")

            for lottery_number in lottery:
                lottery_insatnce = LottoTicket.objects.create(
                    user_profile=user_profile,
                    agent_profile=agent_instance,
                    batch=get_current_batch,
                    phone=phone,
                    stake_amount=stake_amount,
                    expected_amount=expected_amount / number_of_ticket,
                    potential_winning=get_potential_winning,
                    paid=False,
                    number_of_ticket=number_of_ticket,
                    channel=_channel,
                    game_play_id=get_game_play_id,
                    lottery_type=lotto_type,
                    ticket=serialize_ticket(lottery_number.get("ticket")),
                    pin=pin,
                    identity_id=identity_id,
                )

                """

                    CHECK if the channel is POS_AGENT and if it is,
                    check if the agent uses his phone number to play, if he does,
                    generate 6 digit pin for the ticket, for cashout if the case may be.
                """

                lottery_with_db_id.append(
                    {
                        "ticket": lottery_number.get("ticket"),
                        "id": lottery_insatnce.id,
                    }
                )

                # check if duplicate ticket for POS channel is turned on
                ConstantVariable.get_constant_variable().get("create_pos_duplicate_lottery")

            UssdLotteryPayment.objects.create(
                user=user_profile,
                amount=int(amount),
                game_play_id=get_game_play_id,
                channel=_channel,
                lottery_type=lotto_type,
            )

            data["tickets"] = lottery_with_db_id
            if lottery_insatnce.channel == "POS_AGENT":
                if Agent.objects.filter(phone=lottery_insatnce.user_profile.phone_number).exists():
                    del data["ticket_owner"]
                    data["pin"] = pin

            return data

        elif lotto_type == "WYSE_CASH":
            pass

        data = {
            "status": "success",
            "message": "Successfully played lotto",
        }

        return data

    @staticmethod
    def new_play_lotto(user_instance, agent_instance, serialized_data, lotto_type, channel) -> dict:
        """
        Process lottery gameplay for different lottery types.

        Args:
            user_instance: User playing the lottery
            agent_instance: Agent processing the transaction
            serialized_data: Dictionary containing lottery data
            lotto_type: Type of lottery (SALARY_FOR_LIFE, INSTANT_CASHOUT, etc.)
            channel: Channel through which lottery is played (pos, mobile)

        Returns:
            dict: Response with game details and status
        """
        from main.models import LotteryBatch, LottoTicket, UserProfile

        with db_transaction.atomic():

            # Generate necessary IDs
            game_play_id = generate_game_play_id()
            identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"
            pin = generate_pin()

            # Extract lottery data
            lotto_data = serialized_data
            amount = lotto_data.get("amount")
            phone = lotto_data.get("phone_number")
            lottery = lotto_data.get("lottery")

            # Get user profile
            user_profile = UserProfile.objects.get(phone_number=user_instance.phone_number)
            if user_profile is None:
                return {"status": "error", "message": "User profile not found"}

            # Get or create active batch
            active_batch = PosLottoHelper.get_or_create_active_batch(lotto_type)
            if active_batch.get("status") == "error":
                return active_batch

            current_batch = active_batch.get("batch")

            # Process based on lottery type
            if lotto_type == "SALARY_FOR_LIFE":
                return PosLottoHelper.process_salary_for_life(
                    user_profile,
                    agent_instance,
                    current_batch,
                    lottery,
                    amount,
                    phone,
                    game_play_id,
                    identity_id,
                    pin,
                    channel,
                )
            elif lotto_type == "INSTANT_CASHOUT":
                return PosLottoHelper.process_instant_cashout(
                    user_profile,
                    agent_instance,
                    current_batch,
                    lottery,
                    amount,
                    phone,
                    game_play_id,
                    identity_id,
                    pin,
                    channel,
                )
            elif lotto_type == "WYSE_CASH":
                # Not implemented
                pass

            return {
                "status": "success",
                "message": "Successfully played lotto",
            }

    def get_or_create_active_batch(lotto_type):
        """Get or create an active lottery batch."""
        from main.models import LotteryBatch, LotteryGlobalJackPot

        current_batch = LotteryBatch.objects.filter(is_active=True, lottery_type=lotto_type).last()

        if current_batch is None:
            if lotto_type.upper() == "SALARY_FOR_LIFE":
                global_jackpot = LotteryGlobalJackPot().get_jackpot_instance()

                if global_jackpot is None:
                    return {"status": "error", "message": "No active jackpot found"}

                current_batch = LotteryBatch.objects.create(
                    lottery_type=lotto_type.upper(), global_jackpot=global_jackpot
                )
            else:
                current_batch = LotteryBatch.objects.create(lottery_type=lotto_type.upper())

            time.sleep(3)  # Allow time for database operations

        return {"status": "success", "batch": current_batch}

    def process_salary_for_life(
        user_profile,
        agent_instance,
        current_batch,
        lottery_tickets,
        amount,
        phone,
        game_play_id,
        identity_id,
        pin,
        channel,
    ):
        """Process Salary for Life lottery type."""
        from main.models import LottoTicket

        number_of_tickets = len(lottery_tickets)

        # Calculate pricing
        price_model = SalaryForLifePriceModel()
        pricing = price_model.ticket_price(channel="POS", ticket_line=number_of_tickets)

        if pricing is None:
            return {"status": "error", "message": "Invalid ticket line"}

        ticket_actual_stake = pricing.get("ticket_price")
        total_amount = (
            pricing.get("ticket_price")
            + pricing.get("woven_service_charge", 0)
            + pricing.get("africastalking_charge", 0)
        )

        # Create channel indicator
        channel_indicator = "POS_AGENT" if channel == "pos" else "MOBILE"

        # Create ticket records and response data
        tickets_with_ids = PosLottoHelper.create_tickets(
            user_profile=user_profile,
            agent_profile=agent_instance,
            batch=current_batch,
            lottery_tickets=lottery_tickets,
            phone=phone,
            stake_amount=ticket_actual_stake / number_of_tickets,
            expected_amount=total_amount / number_of_tickets,
            potential_winning=pricing.get("potential_winning"),
            number_of_tickets=number_of_tickets,
            channel=channel_indicator,
            game_play_id=game_play_id,
            lottery_type="SALARY_FOR_LIFE",
            pin=pin,
            identity_id=identity_id,
        )

        # Record payment
        UssdLotteryPayment.objects.create(
            user=user_profile,
            amount=int(total_amount),
            game_play_id=game_play_id,
            channel=channel_indicator,
            lottery_type="SALARY_FOR_LIFE",
        )

        # Build response
        response = {
            "status": "Accepted",
            "agent_id": agent_instance.user_id,
            "ticket_owner": user_profile.phone_number,
            "game_id": game_play_id,
            "game_type": "SALARY_FOR_LIFE",
            "stake_per_pick": total_amount / number_of_tickets,
            "total_stake": total_amount,
            "total_ticket": number_of_tickets,
            "tickets": tickets_with_ids,
        }

        # Handle agent self-play
        if channel_indicator == "POS_AGENT":
            if Agent.objects.filter(phone=user_profile.phone_number).exists():
                del response["ticket_owner"]
                response["pin"] = pin

        return response

    def process_instant_cashout(
        user_profile,
        agent_instance,
        current_batch,
        lottery_tickets,
        amount,
        phone,
        game_play_id,
        identity_id,
        pin,
        channel,
    ):
        """Process Instant Cashout lottery type."""
        from main.models import LottoTicket

        number_of_tickets = len(lottery_tickets)
        amount = int(amount)

        # Calculate pricing
        price_model = InstantCashOutPriceModel()
        pricing = price_model.ticket_price(channel="POS", ticket_line=number_of_tickets)

        if pricing is None:
            return {"status": "error", "message": "Invalid ticket line"}

        ticket_actual_stake = pricing.get("total_amount")
        stake_amount = ticket_actual_stake / number_of_tickets

        total_amount = (
            pricing.get("total_amount")
            + pricing.get("woven_service_charge", 0)
            + pricing.get("africastalking_charge", 0)
        )

        # Create channel indicator
        channel_indicator = "POS_AGENT" if channel == "pos" else "MOBILE"

        # Create ticket records and response data
        tickets_with_ids = PosLottoHelper.create_tickets(
            user_profile=user_profile,
            agent_profile=agent_instance,
            batch=current_batch,
            lottery_tickets=lottery_tickets,
            phone=phone,
            stake_amount=stake_amount,
            expected_amount=total_amount / number_of_tickets,
            potential_winning=pricing.get("potential_winning"),
            number_of_tickets=number_of_tickets,
            channel=channel_indicator,
            game_play_id=game_play_id,
            lottery_type="INSTANT_CASHOUT",
            pin=pin,
            identity_id=identity_id,
        )

        # Record payment
        UssdLotteryPayment.objects.create(
            user=user_profile,
            amount=int(amount),
            game_play_id=game_play_id,
            channel=channel_indicator,
            lottery_type="INSTANT_CASHOUT",
        )

        # Build response
        response = {
            "status": "Accepted",
            "agent_id": agent_instance.user_uuid,
            "ticket_owner": user_profile.phone_number,
            "game_id": game_play_id,
            "game_type": "INSTANT_CASHOUT",
            "stake_per_pick": pricing.get("ticket_price") / number_of_tickets,
            "total_stake": pricing.get("ticket_price"),
            "total_ticket": number_of_tickets,
            "tickets": tickets_with_ids,
        }

        # Handle agent self-play
        if channel_indicator == "POS_AGENT":
            if Agent.objects.filter(phone=user_profile.phone_number).exists():
                del response["ticket_owner"]
                response["pin"] = pin

        return response

    def create_tickets(
        user_profile,
        agent_profile,
        batch,
        lottery_tickets,
        phone,
        stake_amount,
        expected_amount,
        potential_winning,
        number_of_tickets,
        channel,
        game_play_id,
        lottery_type,
        pin,
        identity_id,
    ):
        """Create lottery tickets in database and return formatted ticket data."""
        from main.models import LottoTicket

        tickets_with_ids = []

        if lottery_type == "INSTANT_CASHOUT":
            i_cash_price_model = InstantCashOutPriceModel()
            price_model = i_cash_price_model.ticket_price(channel="POS", ticket_line=len(lottery_tickets))
            new_payment_amount = (
                price_model.get("ticket_price", 0)
                + price_model.get("illusion_price", 0)
                + price_model.get("woven_service_charge", 0)
                + price_model.get("africastalking_charge", 0)
            )
        else:
            sal_for_life_price_model = SalaryForLifePriceModel()
            price_model = sal_for_life_price_model.ticket_price(channel="POS", ticket_line=len(lottery_tickets))
            new_payment_amount = (
                price_model.get("ticket_price", 0)
                + price_model.get("illusion_price", 0)
                + price_model.get("woven_service_charge", 0)
                + price_model.get("africastalking_charge", 0)
            )

        validate_amount_in_remiitance_status = LottoAgentRemittanceTable.valuate_amount_to_be_added(
            amount=new_payment_amount, agent=agent_profile
        )
        if validate_amount_in_remiitance_status.get("is_amount_much") is True:
            raise Exception(
                f"Hi, please, fund your wallet with this amount {Utility.currency_formatter(validate_amount_in_remiitance_status.get('amount'))} to continue game play"
            )

        pos_agent_helper = PosAgentHelper(
            agent_instance=agent_profile,
            amount=new_payment_amount,
            pin="1",
        )

        charge_response = pos_agent_helper.handle_agent_charge_and_lottery_play(
            lottery_qs="lottery_qs",
            return_after_successfull_charge=True,
            _game_play_id=game_play_id,
            lottery_type=lottery_type,
        )

        if charge_response != "success":
            if not isinstance(charge_response, dict):
                print("charge_response", charge_response)
                raise Exception("An error occured while charging agent wallet")

            if charge_response.get("message") != "success":
                raise Exception(charge_response.get("message"))

        # charge agent play wallet
        GamePlayEscrow.objects.create(game_play_id=game_play_id, game_type=lottery_type, batch_uuid=batch.batch_uuid)

        instant_game_commission_percentage = AgentConstantVariables.get_instant_game_commission_percentage()
        draw_game_commission_percentage = AgentConstantVariables.get_draw_game_commission_percentage()

        if lottery_type in ["BANKER", "SALARY_FOR_LIFE"]:
            commission_per = draw_game_commission_percentage
        else:
            commission_per = instant_game_commission_percentage

        _rtp = (ConstantVariable().rto_rtp()).get("rtp")

        if lottery_type in ["BANKER", "SALARY_FOR_LIFE"]:
            # Use separate RTP for BANKER and SALARY_FOR_LIFE
            _rtp = ConstantVariable.objects.all().last().rtp_s4l_bank_wysecash / 100

        rtp_per = float(_rtp) * 100

        rto_per = ((100 - (float(_rtp) * 100)) / 100) - commission_per

        for lottery_item in lottery_tickets:

            amount_paid = new_payment_amount / len(lottery_tickets)

            commission_value = round(amount_paid * commission_per, 2)

            rto = round(amount_paid * rto_per, 2)

            agent_sales_commission_plus_per = rto_per + commission_per

            amount_paid_after_removing_comission = amount_paid - (amount_paid * agent_sales_commission_plus_per)  # rtp

            if lottery_type == "BANKER":
                _globa_jackpot = 0
            else:
                global_jackpot_per = JackpotConstantVariable().get_percentage_to_share()
                _globa_jackpot = amount_paid_after_removing_comission * global_jackpot_per

                # Add amount to jackpot
                Jackpot.add_to_jackpot(_globa_jackpot)

            amount_paid_after_removing_comission = amount_paid_after_removing_comission - _globa_jackpot

            win_commission_per = AgentConstantVariables().get_win_agent_commission()
            win_commission_value = round(
                amount_paid_after_removing_comission * win_commission_per,
                2,
            )

            CommissionWallet().update_commission_wallet_amount(
                amount=win_commission_value,
                phone_number=user_profile.phone_number,
            )

            amount_paid_after_removing_comission = amount_paid_after_removing_comission - win_commission_value

            _salary_for_life_jackpot = 0
            salary_for_life_jackpot_amount = 0
            salary_for_life_jackpot_per = 0

            if lottery_type == "SALARY_FOR_LIFE":
                redis_db = RedisStorage(redis_key="sla4life_global_jackpot_perc")
                sal_4_life_jackpot_per = redis_db.get_data()

                if sal_4_life_jackpot_per is None:
                    sal_4_life_jackpot_per = ConstantVariable().salary_for_life_global_jackpot_percentage()
                else:
                    sal_4_life_jackpot_per = float(sal_4_life_jackpot_per.decode("utf-8"))

                # Calculate salary for life jackpot amount
                _salary_for_life_jackpot = round(amount_paid_after_removing_comission * sal_4_life_jackpot_per, 2)

                # Set jackpot values
                salary_for_life_jackpot_per = sal_4_life_jackpot_per
                salary_for_life_jackpot_amount = _salary_for_life_jackpot

                amount_paid_after_removing_comission = (
                    amount_paid_after_removing_comission - salary_for_life_jackpot_amount
                )

            rtp = amount_paid_after_removing_comission
            supervisor_commission = 0
            vertical_lead_commission = 0

            ## calculate veritical lead and supervisor commission
            if agent_profile is not None:
                if agent_profile.supervisor is not None:
                    supervisor_commission = round(
                        rto * AgentConstantVariables.get_supervisor_commission_percentage(), 2
                    )

                    SupervisorWallet.fund_commission_wallet(
                        supervisor_phone_number=agent_profile.supervisor.phone,
                        amount=supervisor_commission,
                        transaction_type="CREDIT",
                        lottery_type=lottery_type,
                    )

                    if agent_profile.supervisor.vertical_lead is not None:
                        vertical_lead_commission = round(
                            rto * AgentConstantVariables.get_vertical_lead_commission_percentage(), 2
                        )

                        LottoVerticalLeadWallet.fund_commission_wallet(
                            vertical_lead_phone_number=agent_profile.supervisor.vertical_lead.phone,
                            amount=vertical_lead_commission,
                            transaction_type="CREDIT",
                            lottery_type=lottery_type,
                        )

            rto = rto - (supervisor_commission + vertical_lead_commission)

            Wallet.fund_wallet(amount=rto, wallet_type="RTO_WALLET", game_type=lottery_type)

            Wallet.fund_wallet(amount=rtp, wallet_type="RETAIL_RTP_WALLET", game_type=lottery_type)

            AgentWallet.reward_commission(
                agent_id=agent_profile.id,
                game_play_amount=amount_paid,
                commission_type="COMMISSION_ON_GAME_PLAY",
                game_type=lottery_type,
                rto_amount=rto,
            )

            _from_lotto_agent = True if agent_profile.terminal_id is not None else False

            GamesDailyActivities.create_record(
                game_type=lottery_type, rtp=rtp, rto=rto, from_lotto_agent=_from_lotto_agent
            )

            try:
                RetailWalletTransactions.create_debit_record_for_game_play(
                    amount=amount_paid, wallet_value=rtp, rto_value=rto, rtp_value=rtp, game_type=lottery_type
                )
            except:
                pass

            ticket_instance = LottoTicket.objects.create(
                user_profile=user_profile,
                agent_profile=agent_profile,
                batch=batch,
                phone=phone,
                stake_amount=stake_amount,
                expected_amount=expected_amount,
                potential_winning=potential_winning,
                number_of_ticket=number_of_tickets,
                channel=channel,
                game_play_id=game_play_id,
                unique_game_play_id=game_play_id,
                lottery_type=lottery_type,
                ticket=serialize_ticket(lottery_item.get("ticket")),
                pin=pin,
                identity_id=identity_id,
                is_agent=True,
                paid=True,
                amount_paid=amount_paid,
                rtp=rtp,
                rto=rto,
                commission_value=commission_value,
                commission_per=commission_per,
                rtp_per=rtp_per,
                win_commission_per=win_commission_per,
                win_commission_value=win_commission_value,
                salary_for_life_jackpot_per=salary_for_life_jackpot_per,
                salary_for_life_jackpot_amount=salary_for_life_jackpot_amount,
            )

            tickets_with_ids.append(
                {
                    "ticket": lottery_item.get("ticket"),
                    "id": ticket_instance.id,
                }
            )

            agent_instance = ticket_instance.agent_profile
            GeneralRetailLottoGames.create_record(
                agent_phone_number=agent_instance.phone,
                agent_name=agent_instance.full_name,
                agent_email=agent_instance.email,
                batch_uuid=ticket_instance.batch.batch_uuid,
                game_play_id=ticket_instance.game_play_id,
                game_pin=ticket_instance.pin,
                lucky_number=ticket_instance.ticket,
                purchase_amount=ticket_instance.amount_paid,
                lotto_db_id=ticket_instance.id,
                paid=ticket_instance.paid,
                number_of_ticket=ticket_instance.number_of_ticket,
                rtp=ticket_instance.rtp,
                rto=ticket_instance.rto,
                commission_percentage=ticket_instance.commission_per,
                type_of_agent=agent_instance.agent_type,
                lotto_game_type=ticket_instance.lottery_type,
                potential_winnings=ticket_instance.potential_winning,
            )

            # Check if duplicate ticket for POS channel is enabled
            # ConstantVariable.get_constant_variable().get("create_pos_duplicate_lottery")

        return tickets_with_ids

    @staticmethod
    def serilaize_WYSE_CASH_data(
        data,
        player_phone_number,
        agent_instance,
        using_agent_phone_number=False,
        pin=None,
        total_stake=0,
    ) -> dict:
        """
        SAMPLE DATA

        {
            "100":[
                {
                    "stake_amount":100.0,
                    "lucky_number":"DEV_JOE221",
                    "consent":true,
                    "band":"10000",
                    "id":146,
                    "get_game_play_id":"9V137y3"
                },
                {
                    "stake_amount":100.0,
                    "lucky_number":"DEV_JOE221",
                    "consent":true,
                    "band":"10000",
                    "id":147,
                    "get_game_play_id":"9V137y3"
                }
            ],
            "200":[
                {
                    "stake_amount":200.0,
                    "lucky_number":"DEV_JOE221",
                    "consent":true,
                    "band":"50000",
                    "id":148,
                    "get_game_play_id":"9V137y3"
                }
            ],
        }

        """

        response = {
            "status": "Accepted",
            "agent_id": agent_instance.user_uuid,
            "ticket_owner": player_phone_number,
            # "game_id": get_game_play_id,
            "game_type": "WHYSE CASH",
            # "stake_per_pick": 100,
            # "total_stake": number_of_ticket * 100,
            # "total_ticket": number_of_ticket,
            # "tickets": lottery,
        }

        if using_agent_phone_number is True:
            del response["ticket_owner"]

        _game_play_id = None

        data_keys = list(data.keys())

        loop_data_store = {i: [] for i in data_keys}

        total_amount_stake = 0
        potential_win = 0
        total_ticket = 0

        for key in data_keys:
            get_data = data.get(key)
            if get_data:
                total_ticket += 1

        for key in data_keys:
            get_data = data.get(key)
            for _data in get_data:
                if using_agent_phone_number is True:
                    if pin is not None:
                        response["pin"] = pin
                    else:
                        response["pin"] = _data.get("pin")

                loop_data_store[key].append(
                    {
                        "ticket": _data["lucky_number"],
                        "id": _data["id"],
                        "band": _data["band"],
                        "stake_amount": _data["stake_amount"],
                    }
                )
                total_amount_stake += _data.get("stake_amount")
                potential_win += float(_data.get("band"))

                if _game_play_id is None:
                    _game_play_id = _data.get("get_game_play_id")

        response["game_id"] = _game_play_id

        # structure lottery data

        lottery_data = []

        for key in loop_data_store:
            if loop_data_store[key]:
                lottery_data.append(loop_data_store[key])

        response["total_ticket"] = total_ticket
        response["total_stake"] = total_amount_stake if total_stake == 0 else total_stake
        response["potential_win"] = potential_win
        response["tickets"] = lottery_data

        # -- check status of this game (won, lost or pending draw)
        lottery_instance = LotteryModel.objects.filter(game_play_id=_game_play_id).last()
        if lottery_instance:
            if lottery_instance.paid is False:
                response["status"] = "awaiting payment"
            else:
                # check if batch is drawn
                if lottery_instance.batch.is_active is True:
                    response["status"] = "pending draw"
                else:
                    winning_qs = LotteryWinnersTable.objects.filter(game_play_id=lottery_instance.game_play_id)
                    if winning_qs.exists():
                        del response["potential_win"]
                        response["amount_won"] = winning_qs.aggregate(Sum("earning"))["earning__sum"]
                        response["status"] = "won"
                    else:
                        response["status"] = "lost"

        return response


def serialize_WYSE_CASH_game_history(data):
    # print("data", data, "\n\n\n\n")

    """
    EXPETED DATA FORMAT

    list of dictionary

    [   {
            "game_play_id": "2vA5577",
            "status": "accepted",
            "stake_amount": 1000.0,
            "number_of_ticket": "",
            "unique_id": "Aug-0-6a3a3",
            "date": "2022-08-25T12:42:48.228203+01:00",
            "won": false,
            "lost": false
        },
    ]

    """

    game_play_ids = []

    if data:
        for _game in data:
            if isinstance(_game, list) and len(_game) == 0:
                continue

            if _game["game_play_id"] in game_play_ids:
                continue

            game_play_ids.append(_game["game_play_id"])

    response_data = []

    game_play_number = {i: 0 for i in game_play_ids}
    stake_amounts = {i: 0 for i in game_play_ids}

    # get number of ticket and stake amount
    for i in game_play_ids:
        for _game in data:
            if isinstance(_game, list) and len(_game) == 0:
                continue

            if _game["game_play_id"] == i:
                game_play_number[i] += 1
                stake_amounts[i] += _game["stake_amount"]

    # summarize and merge data
    for j in game_play_ids:
        # print("data", data, "\n\n\n\n")
        for _game in data:
            if isinstance(_game, list) and len(_game) == 0:
                continue

            if _game["game_play_id"] == j:
                response_data.append(
                    {
                        "game_play_id": _game.get("game_play_id"),
                        "status": _game.get("status"),
                        "stake_amount": stake_amounts[j],
                        "number_of_ticket": game_play_number[j],
                        "game_type": _game.get("game_type"),
                        "paid": _game.get("paid"),
                        "date": _game.get("date"),
                        "won": _game.get("won"),
                        "lost": _game.get("lost"),
                    }
                )

                break

    # ------------------  calculate pontential winning ------------------ #
    pon_winning = 0
    for j in game_play_ids:
        for _game in data:
            try:
                pon_winning += _game["pontential_win"]
            except KeyError:
                pon_winning += 0
            except TypeError:
                pon_winning += 0

    if len(response_data) > 0:
        response_data[0]["pontential_win"] = pon_winning

    # ------------------  calculate pontential winning ------------------ #

    # ------------------  check if game id won ------------------ #

    if len(response_data) > 0:
        game_id = response_data[0]["game_play_id"]
        lottery_qs = LotteryModel.objects.filter(game_play_id=game_id, is_duplicate=False)

        tickets = []
        for lottery in lottery_qs:
            if LotteryWinnersTable.objects.filter(ticket=lottery.lucky_number).exists():
                tickets.append(
                    {
                        "id": lottery.id,
                        "drawn": True if lottery.batch.is_active else False,
                        "won": True,
                        "ticket": lottery.lucky_number,
                    }
                )

            else:
                tickets.append(
                    {
                        "id": lottery.id,
                        "drawn": True if lottery.batch.is_active else False,
                        "won": False,
                        "ticket": lottery.lucky_number,
                    }
                )

        if len(response_data) > 0:
            response_data[0]["tickets"] = tickets

    return response_data


def merge_and_sort_game_history(lotto_data, WYSE_CASH_data):
    """
    EXPECTED DATA FORMAT FOR BOTH PARAMETERS

    [   {
            "game_play_id": "2vA5577",
            "status": "accepted",
            "stake_amount": 1000.0,
            "number_of_ticket": "",
            "unique_id": "Aug-0-6a3a3",
            "date": "2022-08-25T12:42:48.228203+01:00",
            "won": false,
            "lost": false
        },
    ]


    """

    _merged_data = lotto_data + WYSE_CASH_data

    _merged_data.sort(key=lambda x: x["date"], reverse=True)

    return _merged_data


def new_merge_and_sort_game_history(lotto_data, WYSE_CASH_data, africa_lotto_data):
    """
    EXPECTED DATA FORMAT FOR BOTH PARAMETERS

    [   {
            "game_play_id": "2vA5577",
            "status": "accepted",
            "stake_amount": 1000.0,
            "number_of_ticket": "",
            "unique_id": "Aug-0-6a3a3",
            "date": "2022-08-25T12:42:48.228203+01:00",
            "won": false,
            "lost": false
        },
    ]


    """

    _merged_data = lotto_data + WYSE_CASH_data + africa_lotto_data

    _merged_data.sort(key=lambda x: x["date"], reverse=True)

    return _merged_data


def remove_duplicate_game_history(data):
    game_play_ids = []

    for _game in data:
        if isinstance(_game, list) and len(_game) == 0:
            continue

        if _game["game_play_id"] in game_play_ids:
            continue

        game_play_ids.append(_game["game_play_id"])

    response_data = []
    for id in game_play_ids:
        for i in data:
            if i and id == i["game_play_id"]:
                response_data.append(i)
                break

    return response_data


def lotto_ticket_search_response_remove_duplicate(data, paid=False, not_paid=False):
    game_play_ids = []

    for _game in data:
        if isinstance(_game, list) and len(_game) == 0:
            continue

        if _game.get("game_play_id") in game_play_ids:
            continue

        game_play_ids.append(_game.get("game_play_id"))

    response_data = []
    for id in game_play_ids:
        for i in data:
            if id == i.get("game_play_id"):
                response_data.append(i)
                break

    print("response_data", response_data)

    data = []

    if response_data:
        for item in response_data:
            if paid:
                lotto_qs = LottoTicket.objects.filter(game_play_id=item.get("game_play_id"), paid=True)
            elif not_paid:
                lotto_qs = LottoTicket.objects.filter(game_play_id=item.get("game_play_id"), paid=False)

            else:
                lotto_qs = []

            """
            BASICALLY, WE ARE GETTING THE LOTTO TICKET OBJECTS AND THEN WE ARE GETTING THE LOTTO TICKET OBJECTS THAT DRAW AND WON OR PENDING DRAW
            """
            if lotto_qs:
                item["data"] = [
                    {
                        "id": x.id,
                        "ticket": [int(j) for j in x.ticket.split(",")],
                        "drawn": True if x.batch.is_active is False else True,
                        "won": (
                            True
                            if LottoWinners.objects.filter(game_play_id=x.game_play_id, ticket=x.ticket).exists()
                            else False
                        ),
                    }
                    for x in lotto_qs
                ]

                """
                SAMPLE RESPONSE
                {
                    "id": 110114,
                    "ticket": [
                        13,
                        4,
                        5,
                        2
                    ],
                    "drawn": true,
                    "won": false
                }
                """

            else:
                item["data"]

            data.append(item)

    return data


class CustomPaginator:
    @staticmethod
    def paginate(request, queryset, page=1):
        # request_get_data = request.GET

        # queryset_size = 30
        # if int(page) > 1:
        #     queryset_size = 20

        paginator = Paginator(queryset, per_page=50)

        try:
            paginated_data = paginator.page(page)
        except PageNotAnInteger:
            paginated_data = paginator.page(1)
        except EmptyPage:
            paginated_data = []

        return paginated_data


def random_pin_generator(N=7):
    """
    Generate random alphanumeric digit based on your choice of length
    """

    return "".join(random.choices(string.ascii_uppercase + string.digits, k=N))


def lotto_data_sort_by_date(serializer):
    """
    sort data by date
    """
    date_list = []
    data = {}

    for i in serializer.data:
        if i["date"] not in date_list:
            date_list.append(i["date"])
            data[f"{i['date']}"] = []

    for item in serializer.data:
        data[f"{item['date']}"].append(item)

    restructured_data = []
    # restructe the data
    for _date in date_list:
        # print("date", f"{_date}")
        if data.get(f"{_date}", None) is not None:
            # print("date found", f"{_date}")
            for item in data[f"{_date}"]:
                restructured_data.append(
                    {
                        "date": f"{_date}",
                        "value": item,
                    }
                )

    new_return_data = [[] for _ in range(len(date_list))]
    for index, item in enumerate(date_list):
        for i in restructured_data:
            if str(item) == i.get("date"):
                new_return_data[index].append(i)

    date_list.clear()
    # return data
    return new_return_data


def salary_for_life_lotto_data_sort_by_date(serializer):
    """
    sort data by date
    """
    date_list = []
    data = {}

    system_number_pick = {}
    system_number_picks = {}

    for i in serializer.data:
        if i["date"] not in date_list:
            date_list.append(i["date"])
            data[f"{i['date']}"] = []

            system_number_pick[f"{i['date']}"] = []
            system_number_picks[f"{i['date']}"] = []

    """
    SAMPLE DATE LIST

    [datetime.date(2022, 10, 20), datetime.date(2022, 10, 26)]

    """

    for item in serializer.data:
        """

        TRYING TO FORMAT SALARY FOR LIFE SYSTEM PICK NUMBERS AND SORT THEM BY DATE

        e.g  , (1, 5, 7, 8, 9)
        """

        if item["lottery_winner_ticket_number"] == "" or item["lottery_winner_ticket_number"] == " ":
            continue
        if item.get("date") is not None:
            serialized_system_pick_num = machine_number_serializer(item.get("lottery_winner_ticket_number"))

            if len(serialized_system_pick_num) > 0:
                individual_lists = [item for item in serialized_system_pick_num]
                item["ticket_number"] = [x for x in individual_lists]

            else:
                item["ticket_number"] = []

            del item["lottery_winner_ticket_number"]

            data[f"{item['date']}"].append(item)

    # delete dict with empty list
    for key, value in list(data.items()):
        if not value:
            del data[key]

    restructured_data = []
    # restructe the data
    for _date in date_list:
        # print("date", f"{_date}")
        if data.get(f"{_date}", None) is not None:
            # print("date found", f"{_date}")
            for item in data[f"{_date}"]:
                restructured_data.append(
                    {
                        "date": f"{_date}",
                        "value": item,
                    }
                )

    """
    SAMPLE DATA RESPONSE

    {'2020-01-31': [OrderedDict([('id', 108), ('batch_uuid', 'Oct-b1e5ceaa-f7de-4dd0-a660-9d0f322cdac1'), ('game_play_id', ''), ('earning', 5000000), ('date', datetime.date(2020, 1, 31)), ('ticket_number', [['1', '5', '7', '8', '9']])])], '2020-05-24': [OrderedDict([('id', 162), ('batch_uuid', 'Oct-46a76cd0-a714-44c8-be30-6d4ee856d9b7'), ('game_play_id', ''), ('earning', 5000000), ('date', datetime.date(2020, 5, 24)), ('ticket_number', [['3', '22', '26', '34', '42']])])]}
    """
    # print(
    #     f"""

    #       """
    # )
    # return data

    # return restructured_data

    new_return_data = [[] for _ in range(len(date_list))]

    for index, item in enumerate(date_list):
        for i in restructured_data:
            if str(item) == i.get("date"):
                new_return_data[index].append(i)

    date_list.clear()

    return new_return_data


def salary_for_life_system_pick_serializer(serialized_data):
    batch_list = []
    data = {}
    system_number_pick = {}
    system_number_picks = {}

    response_data = []

    for i in serialized_data.data:
        if i["batch_uuid"] not in batch_list:
            batch_list.append(i["batch_uuid"])
            data[f"{i['batch_uuid']}"] = []
            system_number_pick[f"{i['batch_uuid']}"] = []
            system_number_picks[f"{i['batch_uuid']}"] = []

    for item in serialized_data.data:
        if item["lottery_winner_ticket_number"] == "" or item["lottery_winner_ticket_number"] == " ":
            continue

        if item.get("batch_uuid") is not None:
            system_number_pick[f"{item['batch_uuid']}"].append(item["lottery_winner_ticket_number"].split(",")[1:])

            for key in system_number_pick[f"{item['batch_uuid']}"]:
                formated_number_pick = []

                for num_pick in key:
                    formated_number_pick.append((num_pick.replace("(", "").replace(")", "").replace("'", "")).strip())

                system_number_picks[f"{item['batch_uuid']}"].append(formated_number_pick)
            # print("system_number_picks", system_number_picks)

    for j in serialized_data.data:
        if j.get("batch_uuid") is not None:
            # if len of ticket is greater than 5, split the list to contain 5 items each
            ticket = []
            if len(system_number_picks[f"{j['batch_uuid']}"][0]) > 5:
                for i in range(0, len(system_number_picks[f"{j['batch_uuid']}"][0]), 5):
                    # response_data.append(
                    #     system_number_picks[f"{j['batch_uuid']}"][i : i + 5]
                    # )

                    ticket.append(system_number_picks[f"{j['batch_uuid']}"][0][i : i + 5])

            else:
                ticket = system_number_picks[f"{j['batch_uuid']}"]

        # j["ticket_number"] = system_number_picks[f"{j['batch_uuid']}"]

        j["ticket"] = ticket

        del j["lottery_winner_ticket_number"]
        j["phone_number"] = ""
        j["stake_amount"] = ""

        response_data.append(j)

    # print("system_number_picks", system_number_picks)
    return response_data


def agent_login(retry=1):
    email = settings.AGENCY_BANKING_USEREMAIL
    password = settings.AGENCY_BANKING_PASSWORD

    url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

    payload = {
        "email": email,
        "password": password,
        "device_type": "MOBILE",
    }

    headers = {
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, json=payload, headers=headers)

        log_instance = AgencyBankingLoginAttempt.objects.create(response=response.text, account="VFD_DEFAULT_ACCOUNT")

        res = response.json()

        detail = res.get("detail", "")
        if "please contact admin" in str(detail).lower():
            user_id = "637"
            log_instance.unsuspension_response = unsuspend_account_on_agency_banking(user_id=user_id)
            log_instance.save()

            if retry > 0:
                retry -= 1
                return agent_login(retry=retry)

        # check if there's detail key

        # token = res["access"]
        # unsuspend_account_on_agency_banking
        return res["access"]
    except Exception:
        return None


def retail_commission_wallet_login():

    email = config("AGENCY_BANKING_RETAIL_COMMISSION_WALLET_EMAIL")
    password = config("AGENCY_BANKING_RETAIL_COMMISSION_WALLET_PASSWORD")

    url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

    payload = {
        "email": email,
        "password": password,
        "device_type": "MOBILE",
    }

    headers = {
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, json=payload, headers=headers)

        log_instance = AgencyBankingLoginAttempt.objects.create(
            response=response.text, account="RETAIL_COMMISSION_WALLET"
        )

        res = response.json()

        detail = res.get("detail", "")
        if "please contact admin" in str(detail).lower():
            user_id = Wallet.get_user_id("COMMISSION")
            log_instance.unsuspension_response = unsuspend_account_on_agency_banking(user_id=user_id)
            log_instance.save()

            if retry > 0:
                retry -= 1
                return agent_login(retry=retry)

        # token = res["access"]
        return res["access"]
    except Exception:
        return None


def pending_late_withdrawal_wallet_login():

    email = config("PENDING_LATE_WITHDRAWAL_WALLET_EMAIL")
    password = config("PENDING_LATE_WITHDRAWAL_WALLET_PASSWORD")

    url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

    payload = {
        "email": email,
        "password": password,
        "device_type": "MOBILE",
    }

    headers = {
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, json=payload, headers=headers)

        log_instance = AgencyBankingLoginAttempt.objects.create(
            response=response.text, account="RETAIL_COMMISSION_WALLET"
        )

        res = response.json()

        detail = res.get("detail", "")
        if "please contact admin" in str(detail).lower():
            user_id = Wallet.get_user_id("PENDING_LATE_WITHDRAWAL_WALLET")
            log_instance.unsuspension_response = unsuspend_account_on_agency_banking(user_id=user_id)
            log_instance.save()

            if retry > 0:
                retry -= 1
                return agent_login(retry=retry)

        # token = res["access"]
        return res["access"]
    except Exception:
        return None


def excess_wallet_login():

    email = config("AGENCY_BANKING_RETAIL_EXCESS_WALLET_EMAIL")
    password = config("AGENCY_BANKING_RETAIL_EXCESS_WALLET_PASSWORD")

    url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

    payload = {
        "email": email,
        "password": password,
        "device_type": "MOBILE",
    }

    headers = {
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, json=payload, headers=headers)

        log_instance = AgencyBankingLoginAttempt.objects.create(
            response=response.text, account="RETAIL_COMMISSION_WALLET"
        )

        res = response.json()

        detail = res.get("detail", "")
        if "please contact admin" in str(detail).lower():
            user_id = Wallet.get_user_id("PENDING_LATE_WITHDRAWAL_WALLET")
            log_instance.unsuspension_response = unsuspend_account_on_agency_banking(user_id=user_id)
            log_instance.save()

            if retry > 0:
                retry -= 1
                return agent_login(retry=retry)

        # token = res["access"]
        return res["access"]
    except Exception:
        return None


def pre_funding_wallet_login():

    email = config("PRE_FUNDING_WALLET_EMAIL")
    password = config("PRE_FUNDING_WALLET_PASSWORD")

    url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

    payload = {
        "email": email,
        "password": password,
        "device_type": "MOBILE",
    }

    headers = {
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, json=payload, headers=headers)

        log_instance = AgencyBankingLoginAttempt.objects.create(
            response=response.text, account="RETAIL_COMMISSION_WALLET"
        )

        res = response.json()

        detail = res.get("detail", "")
        if "please contact admin" in str(detail).lower():
            user_id = Wallet.get_user_id("PENDING_LATE_WITHDRAWAL_WALLET")
            log_instance.unsuspension_response = unsuspend_account_on_agency_banking(user_id=user_id)
            log_instance.save()

            if retry > 0:
                retry -= 1
                return agent_login(retry=retry)

        # token = res["access"]
        return res["access"]
    except Exception:
        return None


def agent_retail_wallet_login():

    email = config("AGENCY_BANKING_AGENT_RETAIL_WALLET_EMAIL")
    password = config("AGENCY_BANKING_AGENT_RETAIL_WALLET_PASSWORD")

    url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

    payload = {
        "email": email,
        "password": password,
        "device_type": "MOBILE",
    }

    headers = {
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, json=payload, headers=headers)

        log_instance = AgencyBankingLoginAttempt.objects.create(response=response.text, account="RETAIL_RTP_WALLET")

        res = response.json()

        detail = res.get("detail", "")
        if "please contact admin" in str(detail).lower():
            user_id = Wallet.get_user_id("RETAIL_RTP_WALLET")
            log_instance.unsuspension_response = unsuspend_account_on_agency_banking(user_id=user_id)
            log_instance.save()

            if retry > 0:
                retry -= 1
                return agent_login(retry=retry)

        # token = res["access"]
        return res["access"]
    except Exception:
        return None


def non_retail_wallet_login():

    email = config("AGENCY_BANKING_NON_RETAIL_WALLET_EMAIL")
    password = config("AGENCY_BANKING_NON_RETAIL_WALLET_PASSWORD")

    url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

    payload = {
        "email": email,
        "password": password,
        "device_type": "MOBILE",
    }

    headers = {
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, json=payload, headers=headers)

        log_instance = AgencyBankingLoginAttempt.objects.create(response=response.text, account="NON_RETAIL_WALLET")

        res = response.json()

        detail = res.get("detail", "")
        if "please contact admin" in str(detail).lower():
            user_id = Wallet.get_user_id("NON_RETAIL_WALLET")
            log_instance.unsuspension_response = unsuspend_account_on_agency_banking(user_id=user_id)
            log_instance.save()

            if retry > 0:
                retry -= 1
                return agent_login(retry=retry)

        # token = res["access"]
        return res["access"]
    except Exception:
        return None


def ghana_lotto_wallet_login():

    email = config("AGENCY_BANKING_GHANA_LOTTO_WALLET_EMAIL")
    password = config("AGENCY_BANKING_GHANA_LOTTO_WALLET_PASSWORD")

    url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

    payload = {
        "email": email,
        "password": password,
        "device_type": "MOBILE",
    }

    headers = {
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, json=payload, headers=headers)

        log_instance = AgencyBankingLoginAttempt.objects.create(response=response.text, account="GHANA_RTP_WALLET")

        res = response.json()

        detail = res.get("detail", "")
        if "please contact admin" in str(detail).lower():
            user_id = Wallet.get_user_id("GHANA_RTP_WALLET")
            log_instance.unsuspension_response = unsuspend_account_on_agency_banking(user_id=user_id)
            log_instance.save()

            if retry > 0:
                retry -= 1
                return agent_login(retry=retry)

        # token = res["access"]
        return res["access"]
    except Exception:
        return None


def rto_wallet_login():

    email = config("AGENCY_BANKING_RTO_WALLET_EMAIL")
    password = config("AGENCY_BANKING_RTO_WALLET_PASSWORD")

    url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

    payload = {
        "email": email,
        "password": password,
        "device_type": "MOBILE",
    }

    headers = {
        "Content-Type": "application/json",
    }

    try:
        response = requests.post(url, json=payload, headers=headers)

        log_instance = AgencyBankingLoginAttempt.objects.create(response=response.text, account="RTO_WALLET")

        res = response.json()

        detail = res.get("detail", "")
        if "please contact admin" in str(detail).lower():
            user_id = Wallet.get_user_id("RTO_WALLET")
            log_instance.unsuspension_response = unsuspend_account_on_agency_banking(user_id=user_id)
            log_instance.save()

            if retry > 0:
                retry -= 1
                return agent_login(retry=retry)

        # token = res["access"]
        return res["access"]
    except Exception:
        return None


def verify_agency_banking_transaction_for_lotto_agent_wallet_funding(reference):
    """
    Verify agency banking transaction for lotto agent wallet funding

    """

    from wallet_app.models import FloatWallet

    url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/transaction_enquiry/"
    token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")
    headers = {
        "content-type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    payload = {
        "liberty_reference": reference,
    }

    # update float wallet
    FloatWallet().update_float_wallet()

    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 401:
        redis_db = redis.StrictRedis(
            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
        )
        redis_db.delete(f"agent_login_token")

        return verify_agency_banking_transaction_for_lotto_agent_wallet_funding(reference)

    """
    RESPONSE SAMPLE:

    {
        "status":"transaction found",
        "data":{
            "status":"SUCCESSFUL",
            "transaction_type":"FUND_LOTTO_WALLET",
            "liberty_reference":"LGLP_FND_LOTTO_WALL-3c1f7f2f-7216-4ea0-9db4-14f4b3abcec1",
            "unique_reference":null,
            "account_provider":null,
            "amount":10.0,
            "liberty_commission":0.0,
            "sms_charge":0.0,
            "total_amount_charged":null,
            "transaction_leg":"EXTERNAL",
            "balance_before":189218.12,
            "balance_after":189228.12,
            "beneficiary_account_name":null,
            "source_account_name":"CHUKWUEMEKA NWAOMA",
            "date_created":"2022-12-09T12:25:56.777100+01:00",
            "lotto_agent_user_id":"145",
            "lotto_agent_user_phone":"*************",
            "type_of_user":"PERSONAL"
        }
    }
    """

    try:
        print("response", response.text)
        res = response.json()
        if isinstance(res, dict):
            if res.get("data", {}).get("status") == "SUCCESSFUL":
                return True
            else:
                return False
    except Exception:
        return False


def verify_agency_banking_transaction_for_vfd_funding(reference):
    """
    Verify agency banking transaction for lotto agent wallet funding

    """

    print("reference", reference, "\n\n\n")

    # if settings.DEBUG is True:
    #     return True

    from wallet_app.models import FloatWallet

    url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/transaction_enquiry/"

    headers = {
        "content-type": "application/json",
        "Authorization": f"Token {settings.AGENCY_BANKING_TOKEN}",
    }

    payload = {
        "liberty_reference": reference,
    }

    response = requests.post(url, headers=headers, json=payload)

    # update float wallet
    FloatWallet().update_float_wallet()

    print(
        f"""

          verify_agency_banking_transaction_for_vfd_funding: {response.text}
          \n\n

          """
    )

    """
    RESPONSE SAMPLE:

    {
        "status":"transaction found",
        "data":{
            "status":"SUCCESSFUL",
            "transaction_type":"FUND_LOTTO_WALLET",
            "liberty_reference":"LGLP_FND_LOTTO_WALL-3c1f7f2f-7216-4ea0-9db4-14f4b3abcec1",
            "unique_reference":null,
            "account_provider":null,
            "amount":10.0,
            "liberty_commission":0.0,
            "sms_charge":0.0,
            "total_amount_charged":null,
            "transaction_leg":"EXTERNAL",
            "balance_before":189218.12,
            "balance_after":189228.12,
            "beneficiary_account_name":null,
            "source_account_name":"CHUKWUEMEKA NWAOMA",
            "date_created":"2022-12-09T12:25:56.777100+01:00",
            "lotto_agent_user_id":"145",
            "lotto_agent_user_phone":"*************",
            "type_of_user":"PERSONAL"
        }
    }



    {
        "status":"transaction found",
        "status_code": "00",
        "data":{
            "status":"SUCCESSFUL",
            "transaction_type":"FUND_COLLECTION_ACCOUNT",
            "liberty_reference":"LGLP_FND_COLL_ACCT-**********-31b48d6a-9c66-4959-878b-2c9609c6af7c",
            "unique_reference":"f1077fe6-3d5e-405b-8196-2bc567b1c5df_1682712175",
            "account_provider":null,
            "amount":3500.0,
            "liberty_commission":0.0,
            "sms_charge":0.0,
            "total_amount_charged":null,
            "transaction_leg":"EXTERNAL",
            "balance_before":5985.14,
            "balance_after":9485.14,
            "beneficiary_account_name":null,
            "source_account_name":"CHUKWUEMEKA NWAOMA",
            "date_created":"2023-04-28T21:02:57.330958+01:00",
            "lotto_agent_user_id":null,
            "lotto_agent_user_phone":null,
            "type_of_user":"PERSONAL"
        }
    }
    """
    try:
        res = response.json()

        print(
            f"""

          json response: {res}
          \n\n

          """
        )

        if res.get("status_code") == "00":
            new_res = {"status": True, "data": res}
        else:
            new_res = {"status": False, "data": res}

    except requests.exceptions.RequestException as err:
        new_res = {"status": False, "error_message": f"{err}"}

    return new_res


def get_agent_detail_type(agent_id):
    if settings.DEBUG is True or settings.DEBUG:
        types_of_user = ["PERSONAL", "MERCHANT", "AGENT", "LOTTO_AGENT"]

        type_of_user = random.choice(types_of_user)

        data = {"status": "success", "type_of_user": type_of_user}

        return data

    url = f"{settings.AGENCY_BANKING_BASE_URL}/agency/get_type_of_user?user_id={agent_id}"

    token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")
    headers = {
        "content-type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 401:
        redis_db = redis.StrictRedis(
            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
        )
        redis_db.delete("agent_login_token")

        return get_agent_detail_type(agent_id)

    """
    SAMPLE RESPONSES:
    {
        "status": "error",
        "message": "user with ID does not exist"
    }
    {
        "status": "success",
        "type_of_user": "PERSONAL"
    }
    {
        "status": "error",
        "message": "No user id attached"
    }
    {
        'status': 'success',
        'type_of_user': 'PERSONAL',
        'terminal_id': None
    }
    """

    print("get_agent_detail_type", response.text)

    try:
        return response.json()
    except Exception:
        return {}


def get_agent_details_on_agency_banking(agent_id):
    if settings.DEBUG is True or settings.DEBUG:
        types_of_user = ["PERSONAL", "MERCHANT", "AGENT", "LOTTO_AGENT"]

        type_of_user = random.choice(types_of_user)

        data = {"status": "success", "type_of_user": type_of_user}

        return data

    url = f"{settings.AGENCY_BANKING_BASE_URL}/agency/get_user_superagent/?user_id={agent_id}"

    token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")
    headers = {
        "content-type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 401:
        redis_db = redis.StrictRedis(
            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
        )
        redis_db.delete("agent_login_token")

        return get_agent_detail_type(agent_id)

    """
    SAMPLE RESPONSES:
    {
        "status": "error",
        "message": "user with ID does not exist"
    }
    {
        "status": "success",
        "type_of_user": "PERSONAL"
    }
    {
        "status": "error",
        "message": "No user id attached"
    }
    {
        'status': 'success',
        'type_of_user': 'PERSONAL',
        'terminal_id': None
    }
    """

    print("get_agent_detail_type", response.text)

    try:
        return response.json()
    except Exception:
        return {}


def verify_game_play_id_payment(game_play_id):
    from rest_framework.response import Response

    lottery_ticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id).last()

    if lottery_ticket_qs is None:
        lottery_ticket_qs = LotteryModel.objects.filter(game_play_id=game_play_id).last()
        if lottery_ticket_qs is None:
            data = {
                "message": "Ticket does not exist",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)
        else:
            if lottery_ticket_qs.paid is True:
                data = {
                    "message": "Ticket already paid",
                }
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = {
                    "message": "Ticket not paid",
                }

                return Response(data=data, status=status.HTTP_200_OK)

    else:
        if lottery_ticket_qs.paid is True:
            data = {
                "message": "Ticket already paid",
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)
        else:
            data = {
                "message": "Ticket not paid",
            }

            return Response(data=data, status=status.HTTP_200_OK)


def charge_lotto_agent_vfd_virtual_account(account_number, reference):
    payload = {
        "provider": "VFD",
        "account_number": account_number,
        "liberty_reference": reference,
    }

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/debit_other_coll_account/"

    from wallet_app.models import FloatWallet

    token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")

    headers = {
        "content-type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 401:
        redis_db = redis.StrictRedis(
            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
        )
        redis_db.delete("agent_login_token")

        return charge_lotto_agent_vfd_virtual_account(account_number, reference)

    # update float wallet
    FloatWallet().update_float_wallet()

    print("charge_lotto_agent_vfd_virtual_account", response.text, "\n\n\n\n\n\n")

    """
    SAMPLE RESPONSES:

    {
        "error":"650",
        "message":"Account Was Not Requested By This User"
    }

    {
        "error":"550",
        "message":"No Transaction Found"
    }

    {
        "error":"651",
        "message":"Transaction has already been settled",
        "settlement_reference":"get_trans_det.oth_acct_num_settlement_ref"
    }

    {
        "message":"success",
        "data":{
            "message":"Transaction completed successfully",
            "amount_sent":"amount",
            "settlement_reference":"escrow_instance.liberty_reference"
        }
    }

    """

    return response.json()


def liberty_pay_vfd_account_enquiry(account_number=None, source="VFD_DEFAULT_ACCOUNT", retry_count=2):
    """
    Enquire VFD account details
    """

    if source == "VFD_DEFAULT_ACCOUNT":
        token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")

        headers = {
            "content-type": "application/json",
            "Authorization": f"Bearer {token}",
        }

    else:
        if source == "NON_RETAIL_WALLET":
            token = AgencyBankingToken.retrieve_token("NON_RETAIL_WALLET")
            headers = {
                "content-type": "application/json",
                "Authorization": f"Bearer {token}",
            }
        elif source == "RETAIL_RTP_WALLET":
            token = AgencyBankingToken.retrieve_token("RETAIL_RTP_WALLET")
            headers = {
                "content-type": "application/json",
                "Authorization": f"Bearer {token}",
            }

        elif source == "GHANA_RTP_WALLET":
            token = AgencyBankingToken.retrieve_token("GHANA_RTP_WALLET")
            headers = {
                "content-type": "application/json",
                "Authorization": f"Bearer {token}",
            }

        elif source == "RTO_WALLET":
            token = AgencyBankingToken.retrieve_token("RTO_WALLET")
            headers = {
                "content-type": "application/json",
                "Authorization": f"Bearer {token}",
            }

        elif source == "RETAIL_COMMISSION_WALLET":
            token = AgencyBankingToken.retrieve_token("RETAIL_COMMISSION_WALLET")
            headers = {
                "content-type": "application/json",
                "Authorization": f"Bearer {token}",
            }

        elif source == "PENDING_LATE_WITHDRAWAL_WALLET":
            token = AgencyBankingToken.retrieve_token("PENDING_LATE_WITHDRAWAL_WALLET")
            headers = {
                "content-type": "application/json",
                "Authorization": f"Bearer {token}",
            }

        elif source == "EXCESS_WALLET":
            token = AgencyBankingToken.retrieve_token("EXCESS_WALLET")
            headers = {
                "content-type": "application/json",
                "Authorization": f"Bearer {token}",
            }

        elif source == "PRE_FUNDING_WALLET":
            token = AgencyBankingToken.retrieve_token("PRE_FUNDING_WALLET")
            headers = {
                "content-type": "application/json",
                "Authorization": f"Bearer {token}",
            }

        else:
            return {}

    if account_number is None:
        url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/get_other_accounts/"
    else:
        url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/get_other_accounts/?account_num={account_number}/"

    response = requests.get(url, headers=headers)

    """
    SAMPLE RESPONSES:
    {'user_id': 00001926888000000000000, 'account_provider': 'VFD', 'account_number': '111111111111111111111', 'account_name': 'Okoro Samson',
    'bank_name': 'VFD Microfinance Bank',
    'bank_code': '999999', 'is_test': False, 'is_active': True, 'available_balance': 15
    }

    """

    if response.status_code == 401:
        redis_db = redis.StrictRedis(
            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
        )
        if source == "VFD_DEFAULT_ACCOUNT":
            redis_db.delete("agent_login_token")
        elif source == "NON_RETAIL_WALLET":
            redis_db.delete("non_retail_wallet_login")
        elif source == "RETAIL_RTP_WALLET":
            redis_db.delete("agent_retail_wallet_login")
        elif source == "GHANA_RTP_WALLET":
            redis_db.delete("ghana_lotto_wallet_login")
        elif source == "RTO_WALLET":
            redis_db.delete("rto_wallet_login")
        elif source == "RETAIL_COMMISSION_WALLET":
            redis_db.delete("agency_banking_retail_commission_wallet_login")

        if retry_count > 0:
            retry_count -= 1
            return liberty_pay_vfd_account_enquiry(
                account_number=account_number, source=source, retry_count=retry_count
            )

    try:
        return response.json()
    except Exception:
        return {}


def machine_number_serializer(sal_4_system_pick):
    if sal_4_system_pick is None:
        return []
    system_number_pick = sal_4_system_pick.split(",")[1:]

    formated_number_pick = []
    for num_pick in system_number_pick:
        formated_number_pick.append((num_pick.replace("(", "").replace(")", "").replace("'", "")).strip())

    ticket = []

    sublists = []
    sublist = []

    while len(formated_number_pick) > 0:
        if len(sublist) < 5:
            sublist.append(formated_number_pick.pop(0))
        else:
            sublists.append(sublist)
            sublist = []
    if len(sublist) > 0:  # add the final sublist if there are any items left
        sublists.append(sublist)

    for n in sublists:
        e = eval(",".join(n))
        if isinstance(e, tuple):
            e = list(e)
        ticket.append(e)

    return ticket


def get_agent_detail_type(agent_id):
    if settings.DEBUG is True or settings.DEBUG:
        types_of_user = ["PERSONAL", "MERCHANT", "AGENT", "LOTTO_AGENT"]

        type_of_user = random.choice(types_of_user)

        data = {"status": "success", "type_of_user": type_of_user}

        return data

    url = f"{settings.AGENCY_BANKING_BASE_URL}/agency/get_type_of_user?user_id={agent_id}"

    token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")
    headers = {
        "content-type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    response = requests.get(url, headers=headers)

    if response.status_code == 401:
        redis_db = redis.StrictRedis(
            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
        )
        redis_db.delete("agent_login_token")

        return get_agent_detail_type(agent_id)

    """
    SAMPLE RESPONSES:

    {
        "status": "error",
        "message": "user with ID does not exist"
    }


    {
        "status": "success",
        "type_of_user": "PERSONAL"
    }

    {
        "status": "error",
        "message": "No user id attached"
    }

    {
        'status': 'success',
        'type_of_user': 'PERSONAL',
        'terminal_id': None
    }

    """

    print("get_agent_detail_type", response.text)

    try:
        return response.json()
    except Exception:
        return {}


def get_user_details_on_agency_banking(email):
    if settings.DEBUG is True or settings.DEBUG:
        types_of_user = ["PERSONAL", "MERCHANT", "AGENT", "LOTTO_AGENT"]

        type_of_user = random.choice(types_of_user)

        data = {"status": "success", "type_of_user": type_of_user}

        return data

    url = f"{settings.AGENCY_BANKING_BASE_URL}/agency/check_email_exist?email={email}"

    response = requests.get(url)

    """
    SAMPLE RESPONSES:

    {
        "success": true,
        "message": "user already exist",
        "data": {
            "email": "<EMAIL>",
            "phone_number": "*************",
            "username": "Yitlast",
            "type_of_user": "STAFF_AGENT",
            "full_name": "Omobola Akinsanya",
            "user_id": 7441,
            "unique_id": "D99793",
            "customer_id": "061a9165-088c-4467-b202-d5172415b311",
            "terminal_id": null
        }
    }

    """

    try:
        return response.json()
    except Exception:
        return {}


def get_agent_sales_rep_super_agent_and_supervisor(user_id=None, retry_count=1):
    token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")
    headers = {
        "content-type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    url = f"{settings.AGENCY_BANKING_BASE_URL}/agency/get_user_superagent/?user_id={user_id}"
    
    response = requests.get(url, headers=headers)
    if response.status_code == 401:
        if retry_count > 0:
            AgencyBankingToken.objects.filter(account="VFD_DEFAULT_ACCOUNT").delete()
            
            return get_agent_sales_rep_super_agent_and_supervisor(user_id)
    try:
        res = response.json()
    except Exception:
        res = response.text
        
        
    """
        {
            "status":"success",
            "data":{
                "agent":11493,
                "phone_number":"*************",
                "sales_rep":"None",
                "super_agent":"None",
                "supervisor":{
                    "id":8903,
                    "email":"<EMAIL>",
                    "first_name":"benard",
                    "last_name":"olaniyi",
                    "phone_number":"*************",
                    "unique_id":"399A6F",
                    "customer_id":"4f52561b-054c-4b98-b0c0-c23cacd7402b"
                },
                "user_own_accounts":"None"
            }
            }
    """

    return res
        


class LandingPageResultPaginator:
    @staticmethod
    def paginate(request, queryset, page):
        # request_get_data = request.GET

        # queryset_size = 30
        # if int(page) > 1:
        #     queryset_size = 20

        paginator = Paginator(queryset, per_page=20)

        try:
            paginated_data = paginator.page(page)
        except PageNotAnInteger:
            paginated_data = paginator.page(1)
        except EmptyPage:
            paginated_data = []

        return paginated_data


def get_week_info(date):
    """
    Returns the week number and week range (Monday to Sunday) for the given date.
    """
    # Calculate the difference between the date and the beginning of the week (Monday)
    days_to_beginning = date.weekday()  # 0 for Monday, 1 for Tuesday, etc.

    # Calculate the start date (Monday) of the week
    week_start = date - timedelta(days=days_to_beginning)

    # Calculate the end date (Sunday) of the week
    week_end = week_start + timedelta(days=6)

    # Get the ISO week number (week 1 is the first week with at least 4 days in January)
    week_number = date.isocalendar()[1]

    # Format the result as a string
    week_range_str = f"{week_start.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}"

    return week_number, week_range_str


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (float, np.float64, np.float32)):
            if math.isnan(obj):
                return None
            elif math.isinf(obj):
                return 100 if obj > 0 else 0  # 100 for +inf, 0 for -inf
        return super().default(obj)


def unsuspend_account_on_agency_banking(user_id, suspend=False):
    """
    Unsuspend account on agency banking
    """

    if str(config("ENVIRONMENT")).lower().startswith("pro"):
        url = f"{settings.AGENCY_BANKING_BASE_URL}/agency/others_suspend_user/"

        try:
            token = AgencyBankingToken.objects.latest("created_at").token
        except Exception:
            token = agent_login()

        headers = {
            "content-type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        payload = {
            "user_id": user_id,
            "suspend": suspend,
            "suspend_or_unsuspend_reason": "Account unsuspended from lotto",
        }

        res = requests.post(url, headers=headers, json=payload)

        response_payload = {
            "status": res.status_code,
            "response": res.text,
            "payload": payload,
            "url": url,
        }
        return json.dumps(response_payload)
