import uuid
from collections import OrderedDict
from datetime import datetime
from time import sleep

from main.api.api_lottery_helpers import generate_game_play_id, generate_pin
from main.models import ConstantVariable, LottoTicket
from pos_app.prices.structure import quika_i_cash_lines_amount
from wyse_ussd.models import UssdLotteryPayment


def lotto_registration(
    lottery_play,
    phone_number,
    stake_winning_amount,
    serialize_ticket,
    lotto_type,
    agent_instance,
    player,
    channel,
    current_batch,
    is_new_quika_game=False,
):
    game_play_ids_generated = []
    game_amount = []

    count = 0

    current_batch.save()
    sleep(2)

    print("lottery_play lottery_play ::::::::::::::::::::::::::", lottery_play, "\n\n\n")

    for game in lottery_play:
        # get number of tickets in game for each game play
        stake_amount = game["amount"]

        if lotto_type == "QUIKA":
            amount_reg_helper = stake_winning_amount(stake_amount, channel=channel)
            ticket_reg_count = amount_reg_helper["reg_count"]
            potential_winning = amount_reg_helper["potential_winning"]

        elif lotto_type == "INSTANT_CASHOUT":
            ticket_reg_count = len(game["lottery"])
            amount_reg_helper = stake_winning_amount(stake_amount)
            potential_winning = amount_reg_helper["winning_amount"]

        game_play_id = generate_game_play_id()
        ticket_pin = generate_pin()
        identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        stake_illusion_amount = quika_i_cash_lines_amount[ticket_reg_count]

        if (ConstantVariable.objects.last().game_illusion_feature == "OFF") or (lotto_type == "INSTANT_CASHOUT"):
            # print("ILLUSION FALSE \n\n\n")
            has_illusion = False
            illusion_amount = 0

        else:
            # print("ILLUSION True \n\n\n")
            illusion_amount = stake_illusion_amount["illusion"]
            has_illusion = True

            if lotto_type == "QUIKA":
                illusion_amount = stake_illusion_amount["illusion"]
            if lotto_type == "INSTANT_CASHOUT":
                illusion_amount = 0.0

        """
        NEW QUIKA UPDATE.

        WHEN THE TICKET IS 600, CREATE 3 INSTANCE OF THE TICKET AND RECORD 200 AS STAKE AMOUNT PER TICKET
        """
        if lotto_type == "QUIKA" and game["amount"] == 600:
            for ticket_count in range(0, 3):
                lottery = game["lottery"][0]
                LottoTicket.objects.create(
                    user_profile=player,
                    agent_profile=agent_instance,
                    batch=current_batch,
                    phone=phone_number,
                    stake_amount=200,
                    expected_amount=200,
                    illusion=0,
                    potential_winning=potential_winning,
                    paid=False,
                    number_of_ticket=ticket_reg_count,
                    channel="POS_AGENT" if channel == "pos" else "MOBILE",
                    game_play_id=game_play_id,
                    lottery_type=lotto_type,
                    ticket=serialize_ticket(lottery["ticket"]),
                    pin=ticket_pin,
                    identity_id=identity_id,
                    is_new_quika_game=is_new_quika_game,
                )
                game_play_ids_generated.append(game_play_id)

        else:
            for ticket_count in range(ticket_reg_count):
                lottery = game["lottery"][0 if lotto_type == "QUIKA" else ticket_count]

                LottoTicket.objects.create(
                    user_profile=player,
                    agent_profile=agent_instance,
                    batch=current_batch,
                    phone=phone_number,
                    stake_amount=stake_illusion_amount["reg_amount"] / ticket_reg_count,
                    expected_amount=stake_amount / ticket_reg_count,
                    illusion=illusion_amount / ticket_reg_count,
                    potential_winning=potential_winning,
                    paid=False,
                    number_of_ticket=ticket_reg_count,
                    channel="POS_AGENT" if channel == "pos" else "MOBILE",
                    game_play_id=game_play_id,
                    lottery_type=lotto_type,
                    ticket=serialize_ticket(lottery["ticket"]),
                    pin=ticket_pin,
                    identity_id=identity_id,
                    is_new_quika_game=is_new_quika_game,
                )
                game_play_ids_generated.append(game_play_id)

                # check if duplicate ticket for POS channel is turned on
                # create_pos_duplicate_lottery = ConstantVariable.get_constant_variable().get(
                #     "create_pos_duplicate_lottery"
                # )

        game_amount.append(stake_amount)

        UssdLotteryPayment.objects.create(
            user=player,
            amount=int(stake_amount),
            game_play_id=game_play_id,
            channel="POS_AGENT" if channel == "pos" else "MOBILE",
            lottery_type=lotto_type,
            illusion_amount=illusion_amount,
            has_illusion=has_illusion,
        )

        count += 1

        lottery_play[count - 1]["game_play_id"] = game_play_id

    sorted_game_id = list(OrderedDict.fromkeys(game_play_ids_generated))

    reg_result = {"sorted_game_id": sorted_game_id, "game_amount": game_amount}
    return reg_result
