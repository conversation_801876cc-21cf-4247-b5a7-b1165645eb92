from main.models import LotteryBatch
from pos_app.prices.structure import I_CASH_UPDATED_AMOUNT, quika_pricing_and_reg_count


class PlayMultipleLottoTicket:
    def __init__(
        self,
        request_body,
        lotto_type,
        agent_instance,
        channel,
        player,
        phone_no=None,
        is_new_quika_game=False,
    ):
        self.request_body = request_body
        self.lotto_type = lotto_type
        self.agent_instance = agent_instance
        self.phone_number = phone_no
        self.channel = channel
        self.lottery_play = request_body.get("lottery_play")
        self.player = player
        self.is_new_quika_game = is_new_quika_game

    @staticmethod
    def _serialize_ticket(number_list):
        return ",".join(map(str, map(int, number_list)))

    def _current_batch(self):
        batch = LotteryBatch.objects.filter(is_active=True, lottery_type="INSTANT_CASHOUT")
        if batch.count() > 0:
            return batch.last()

        else:
            return LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT")

    def _stake_winning_amount(self, stake_amount, channel=None, ticket_count=None):
        if self.lotto_type == "QUIKA":
            # quika_amount_instance = ConstantVariable().quika_admin_stake_and_winning_amount()[int(ticket_count)]
            #
            # amounts = {
            #     "stake_amount": quika_amount_instance["stake_amount"],
            #     "winning_amount": quika_amount_instance["total_winning_amount"],
            # }
            # print(amounts)
            # return amounts

            print("stake_amount rrrrrrrrrrrrr ", stake_amount, "\n\n\n\n\n\n\n\n\n\n")

            return quika_pricing_and_reg_count(stake_amount=stake_amount, channel=channel)

        elif self.lotto_type == "INSTANT_CASHOUT":
            # instant_cash_out_amount_instance = LottoTicket.instant_stake_amount_pontential_winning(
            #     ticket_count=ticket_count
            # )
            # amounts = {
            #     "stake_amount": instant_cash_out_amount_instance["stake_amount"],
            #     "winning_amount": instant_cash_out_amount_instance[
            #         "total_winning_amount"
            #     ],
            # }

            amount = I_CASH_UPDATED_AMOUNT[stake_amount]

            amounts = {
                "reg_amount": stake_amount,
                "winning_amount": amount["winning_amount"],
                "main_stake_amount": stake_amount,
            }
            return amounts

    def register_game_play(self):
        from pos_app.multiple_play.i_cash_quika.registration import lotto_registration

        # print(self._serialize_ticket,"-------------------------")

        return lotto_registration(
            self.lottery_play,
            self.phone_number,
            self._stake_winning_amount,
            self._serialize_ticket,
            self.lotto_type,
            self.agent_instance,
            self.player,
            self.channel,
            self._current_batch(),
            self.is_new_quika_game,
        )

    def payment_game_check(self):
        from pos_app.multiple_play.i_cash_quika.payment_game_check import (
            pay_lotto_ticket_and_check_game_result,
        )

        registration_result = self.register_game_play()
        try:
            pin = self.request_body.pop("pin")
        except KeyError:
            pin = "0000"

        print("self._stake_winning_amount", self._stake_winning_amount, "\n\n\n")

        result = pay_lotto_ticket_and_check_game_result(
            sorted_game_id=registration_result["sorted_game_id"],
            agent_instance=self.agent_instance,
            lottery_play=self.lottery_play,
            pin=pin,
            game_amount=registration_result["game_amount"],
            request_body=self.request_body,
            lotto_type=self.lotto_type,
            stake_winning_amount=self._stake_winning_amount,
        )
        if isinstance(result, dict):
            response_body = {
                "status": "success",
                "lotto_type": self.lotto_type,
                "agent_id": self.agent_instance.user_uuid,
                "message": "Accepted",
            }
            response_body.update(result)

        else:
            response_body = {
                "status": "error",
                "paid": False,
                "message": result[1].get("message"),
            }
        return response_body
