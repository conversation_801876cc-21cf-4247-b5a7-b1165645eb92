import math
import random
from time import sleep

from django.db.models import Sum

from pos_app.models import PosLotteryWinners


def pay_lotto_ticket_and_check_game_result(
    sorted_game_id,
    agent_instance,
    lottery_play,
    pin,
    game_amount,
    request_body,
    lotto_type,
    stake_winning_amount,
):
    # from main.helpers.helper_functions import mask_winners_phone_number

    from main.models import LottoTicket, LottoWinners
    from pos_app.pos_helpers import PosAgentHelper

    for game_count in range(len(sorted_game_id)):
        # print(game_count, "AT GAME COUNT")

        game_id = sorted_game_id[game_count]

        lottery_ticket_qs = LottoTicket.objects.filter(
            game_play_id=game_id,
            paid=False,
            agent_profile=agent_instance,
            is_duplicate=False,
        )

        pos_agent_helper = PosAgentHelper(
            agent_instance=agent_instance,
            amount=game_amount[game_count],
            pin=pin,
        )
        response = pos_agent_helper.handle_agent_charge_and_lottery_play(lottery_ticket_qs, instant_cashout_game=True, _game_play_id=game_id)

        # print("HANDLE CHARGE AGENT RESPONSE :::::::::::::",response)
        if response.get("status") != "success":
            lottery_play[game_count]["paid"] = False
            lottery_play[game_count]["status"] = "Payment failed"

            data = {
                "message": response.get("message"),
            }
            return False, data

        lottery_play[game_count]["paid"] = True if response.get("status") == "success" else False

        lotto_queryset = LottoTicket.objects.filter(
            game_play_id=game_id,
            lottery_type=lotto_type,
            paid=True,
        )
        # print(lotto_queryset, "QUIKA QUERY SET ---------------------------")
        ticket_count = lotto_queryset.count()
        stake_amount = lotto_queryset.last().expected_amount * ticket_count
        # print(stake_amount)
        amounts = stake_winning_amount(stake_amount=stake_amount, channel=lotto_queryset.last().channel)
        lotto_instance = lotto_queryset.last()

        if lotto_instance is None:
            return False, {"message": "ticket not paid for"}

        if (lotto_instance.lottery_type == "QUIKA") and (lotto_instance.is_new_quika_game is False):
            _total_stake_pick = (lotto_instance.stake_amount * len(lotto_queryset)) + (lotto_instance.illusion * len(lotto_queryset))
            _stake_per_pick = _total_stake_pick / lotto_instance.number_of_ticket

            lottery_play[game_count]["stake_per_pick"] = _stake_per_pick
            lottery_play[game_count]["total_stake"] = _total_stake_pick
            lottery_play[game_count]["total_ticket"] = lotto_instance.number_of_ticket

        else:
            lottery_play[game_count]["stake_per_pick"] = amounts["main_stake_amount"] / ticket_count
            lottery_play[game_count]["total_stake"] = amounts["main_stake_amount"]
            lottery_play[game_count]["total_ticket"] = lotto_queryset.count()

        lottery_play[game_count]["ticket_owner"] = lotto_instance.user_profile.phone_number

        lottery_play[game_count]["date"] = lotto_instance.date

        winning_tickets = []
        winning_instance = LottoWinners.objects.filter(
            game_play_id=game_id,
            lottery__user_profile__phone_number=lotto_instance.user_profile.phone_number,
        )

        sleep(2)
        if winning_instance.exists():
            winning_ins = winning_instance.last()
            if (winning_ins.win_flavour == "WHITE") and (winning_ins.match_type != "PERM_0"):
                lottery_play[game_count]["status"] = "won"
                lottery_play[game_count]["amount_won"] = winning_instance.aggregate(Sum("earning"))["earning__sum"]
                won_tickets = [ticket.ticket for ticket in winning_instance]

                for x in won_tickets:
                    winning_tickets.append(x)

            if (winning_ins.win_flavour == "BLACK") or (winning_ins.win_flavour == "CASHBACK"):
                cash_back_perc = winning_ins.get_cashback_percentage
                lottery_play[game_count]["status"] = f"{cash_back_perc}% cashback"
                lottery_play[game_count]["cashback_amount"] = winning_instance.aggregate(Sum("earning"))["earning__sum"]
                won_tickets = [ticket.ticket for ticket in winning_instance]
                for x in won_tickets:
                    winning_tickets.append(x)

            if (winning_ins.win_flavour == "WHITE") and (winning_ins.match_type == "PERM_0"):
                cash_back_perc = winning_ins.get_cashback_percentage
                lottery_play[game_count]["status"] = f"{cash_back_perc}% cashback"
                lottery_play[game_count]["cashback_amount"] = winning_instance.aggregate(Sum("earning"))["earning__sum"]
                won_tickets = [ticket.ticket for ticket in winning_instance]
                for x in won_tickets:
                    winning_tickets.append(x)

        else:
            lottery_play[game_count]["status"] = "lost"
            lottery_play[game_count]["amount_won"] = 0.0

        lottery = lottery_play[game_count]["lottery"]

        for ticket_count in range(len(lottery)):
            ticket = lottery[ticket_count]["ticket"]
            ticket_string = ",".join(str(x) for x in ticket)

            if ticket_string in winning_tickets:
                lottery[ticket_count]["status"] = "won"

            else:
                lottery[ticket_count]["status"] = "lost"

        lottery_play[game_count]["system_pick"] = (
            [] if lotto_instance.system_generated_num is None else [int(x) for x in lotto_instance.system_generated_num.split(",")]
        )
        lottery_play[game_count]["pin"] = lotto_instance.pin if lotto_instance is not None else None
        # --------------JACKPOT--------------
        jackpot_winning = PosLotteryWinners.objects.filter(game_id=game_id, jackpot=True).last()

        lottery_play[game_count]["jackpot"] = {
            "won": True if jackpot_winning is not None else False,
            "win_amount": (jackpot_winning.amount_won if jackpot_winning is not None else 0.0),
            "pin": jackpot_winning.pin if jackpot_winning is not None else None,
        }

        request_body["phone_number"] = request_body["phone_number"]

    return request_body


def pay_quika_lotto_ticket_and_check_game_result(game_play_id, agent_instance, pin, game_amount, lotto_type):
    """
    Processes payment for a QUIKA lottery ticket and checks game results.

    Args:
        game_play_id (str): The unique ID of the lottery game play.
        agent_instance (object): The agent profile handling the transaction.
        pin (str): Security PIN for the transaction.
        game_amount (float): The amount paid for the lottery ticket.
        lotto_type (str): The type of lottery being played.

    Returns:
        tuple: (bool, dict) - Success status and response data containing game details.
    """

    from main.models import LottoTicket, LottoWinners
    from pos_app.pos_helpers import PosAgentHelper

    # Fetch unpaid lottery tickets
    lottery_ticket_qs = LottoTicket.objects.filter(
        game_play_id=game_play_id,
        paid=False,
        agent_profile=agent_instance,
        is_duplicate=False,
        lottery_type="QUIKA",
    )

    pos_agent_helper = PosAgentHelper(agent_instance=agent_instance, amount=game_amount, pin=pin)

    response = pos_agent_helper.handle_agent_charge_and_lottery_play(
        lottery_ticket_qs, instant_cashout_game=True, _game_play_id=game_play_id, lottery_type="QUIKA"
    )

    if response.get("status") != "success":
        return False, {"message": response.get("message")}

    lotto_queryset = LottoTicket.objects.filter(game_play_id=game_play_id, lottery_type=lotto_type, paid=True)

    if not lotto_queryset.exists():
        return False, {"message": "Ticket not paid for"}

    last_instance = lotto_queryset.last()

    response_data = {
        "status": "success",
        "game_id": game_play_id,
        "ticket_owner": last_instance.user_profile.phone_number,
        "total_stake": game_amount,
        "total_ticket": 1,
        "pin": last_instance.pin,
        "date": last_instance.date,
    }

    winning_instance = LottoWinners.objects.filter(
        game_play_id=game_play_id,
        lottery__user_profile__phone_number=last_instance.user_profile.phone_number,
    )

    winning_amount = winning_instance.aggregate(Sum("earning")).get("earning__sum", 0) if winning_instance.exists() else 0

    if winning_instance.exists():
        winning_ins = winning_instance.last()
        cash_back_perc = winning_ins.get_cashback_percentage if winning_ins.match_type == "PERM_0" else None

        if winning_ins.win_flavour == "WHITE" and winning_ins.match_type != "PERM_0":
            response_data.update({"status": "won", "amount_won": winning_amount})
        elif winning_ins.win_flavour in ["BLACK", "CASHBACK", "WHITE"] and winning_ins.match_type == "PERM_0":
            response_data.update({"status": f"{cash_back_perc}% cashback", "cashback_amount": winning_amount})
        else:
            response_data.update({"status": "lost", "amount_won": 0.0})
    else:
        response_data.update({"status": "lost", "amount_won": 0.0})

    user_ticket = [int(x) for x in last_instance.ticket.split(",") if x and x != "0"]
    response_data["lottery_play"] = user_ticket

    generated_numbers = set()
    while len(generated_numbers) < 12:
        generated_number = random.randint(1, 40)
        if generated_number not in user_ticket:
            generated_numbers.add(generated_number)

    list_of_random_generated_amount = [
        "900",
        "1k",
        "4k",
        "600",
        "2k",
        "1.5k",
        "800",
        "3k",
        "1.2k",
        "6k",
        "700",
        "2.5k",
        "1.8k",
        "1k",
        "12k",
        "15k",
        "500",
        "10k",
        "1.3k",
        "18k",
        "800",
        "2.5k",
        "700",
        "4.2k",
        "1k",
        "900",
        "2k",
        "1.6k",
        "1.3k",
        "600",
        "14k",
        "3k",
        "1.1k",
        "5.5k",
        "10k",
        "1.8k",
        "750",
        "18k",
        "1.4k",
        "7k",
    ]

    generated_numbers_with_amount = []
    min_nonzero_amount = min(int(float(a.replace("k", "000"))) for a in list_of_random_generated_amount)

    if response_data["status"] == "lost" or "cashback" in response_data["status"]:
        generated_numbers = list(generated_numbers)
        for i, gen_num in enumerate(generated_numbers):
            if gen_num in user_ticket:
                new_number = random.choice([x for x in range(1, 41) if x not in user_ticket and x not in generated_numbers])
                generated_numbers[i] = new_number
            generated_numbers_with_amount.append(
                {
                    "number": generated_numbers[i],
                    "amount": random.choice(list_of_random_generated_amount),
                    "won": False,
                }
            )
        response_data["status"] = "successful"
    else:
        generated_numbers = list(generated_numbers)
        for i, num in enumerate(user_ticket):
            index = random.randint(0, len(generated_numbers) - 1)
            generated_numbers[index] = num

        for num in generated_numbers:
            won = num in user_ticket and winning_amount > 0
            win_amount_per_number = math.floor(winning_amount / len(user_ticket)) if won else random.choice(list_of_random_generated_amount)

            # Ensure win_amount_per_number is never zero
            if isinstance(win_amount_per_number, int) and win_amount_per_number == 0:
                win_amount_per_number = min_nonzero_amount

            generated_numbers_with_amount.append(
                {
                    "number": num,
                    "amount": win_amount_per_number,
                    "won": won,
                }
            )

    response_data["winning_numbers"] = generated_numbers_with_amount

    sleep(1)

    jackpot_winning = PosLotteryWinners.objects.filter(game_id=game_play_id, jackpot=True).last()
    response_data["jackpot"] = {
        "won": jackpot_winning is not None,
        "win_amount": jackpot_winning.amount_won if jackpot_winning else 0.0,
        "pin": jackpot_winning.pin if jackpot_winning else None,
    }

    return True, response_data
