from django.core.management.base import BaseCommand
from django.db.models import Q, Sum
from datetime import datetime

import pytz
from django.conf import settings

from africa_lotto.models import AfricaLotto, AfricaLottoConstants
from main.models import ConstantVariable, LotteryModel, LottoTicket
from pos_app.models import Agent, AgentConstantVariables, AgentWalletTransaction, GamesDailyActivities, LottoAgentSalesActivity, PosLotteryWinners
from retail_metrics.models import AgentMetricSummary, GameAnalytics, ReturnToPlayerAndReturnToOwnerAnalytics
from retail_metrics.retails_helper import SalesBasedMetricsCalculator


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))


        total_instance, _ = GamesDailyActivities.objects.get_or_create(game_type = "TOTAL", created_at__date = TODAY.date())

        total_sales = 0

        total_winnings = 0

        total_commission = 0

        total_rtp = 0

        total_rto = 0


        games = [
            "SALARY_FOR_LIFE", "INSTANT_CASHOUT", "WYSE_CASH", "VIRTUAL_SOCCER", "SOCCER_CASH", "BANKER", "QUIKA",
            "GHANA_LOTTO", "NIGERIA_LOTTO", "KENYA_LOTTO", "KENYA_30_LOTTO", "K_NOW"
        ]

        for game in games:
            game_transaction_qs = AgentWalletTransaction.objects.filter(date_created__date = TODAY.date(), game_type = game)

            game_total_sales = game_transaction_qs.filter(transaction_from = "GAME_PLAY").aggregate(Sum("amount")).get("amount__sum") or 0

            total_sales += game_total_sales

            game_total_winnings = PosLotteryWinners.objects.filter(lottery_type = game, date_created__date = TODAY.date()).aggregate(Sum("amount_won")).get("amount_won__sum") or 0

            total_winnings += game_total_winnings

            game_total_commission = 0

            if game_total_sales > 0:
                commission_percentage = 0
                if game in ["SALARY_FOR_LIFE", "GHANA_LOTTO", "KENYA_LOTTO", "KENYA_30_LOTTO", "WYSE_CASH", "BANKER"]:
                    commission_percentage = AgentConstantVariables.get_draw_game_commission_percentage()
                elif game == "K_NOW":
                    commission_percentage = AfricaLottoConstants.get_k_now_commission_percentage()
                else:
                    commission_percentage = AgentConstantVariables.get_instant_game_commission_percentage()


                game_total_commission = game_total_sales * commission_percentage

            
            total_commission += game_total_commission

            game_total_rtp = 0
            game_total_rto = 0

            if game in ["SALARY_FOR_LIFE", "BANKER", "INSTANT_CASHOUT", "VIRTUAL_SOCCER", "QUIKA"]:
                game_lotto_ticket_qs = LottoTicket.objects.filter(date__date = TODAY.date(), lottery_type = game, agent_profile__isnull = False)

                rtp = game_lotto_ticket_qs.aggregate(Sum("rtp")).get("rtp__sum") or 0
                rto = game_lotto_ticket_qs.aggregate(Sum("rto")).get("rto__sum") or 0
                effective_rtp = game_lotto_ticket_qs.aggregate(Sum("effective_rtp")).get("effective_rtp__sum") or 0


            elif game == "WYSE_CASH":
                game_lotto_ticket_qs = LotteryModel.objects.filter(date__date = TODAY.date(), lottery_type = game, agent_profile__isnull = False)

                rtp = game_lotto_ticket_qs.aggregate(Sum("rtp")).get("rtp__sum") or 0
                rto = game_lotto_ticket_qs.aggregate(Sum("rto")).get("rto__sum") or 0
                effective_rtp = 0

            elif game in ["K_NOW", "GHANA_LOTTO", "KENYA_LOTTO", "KENYA_30_LOTTO"]:

                
                game_lotto_ticket_qs = AfricaLotto.objects.filter(created_at__date = TODAY.date(), game_type = game, agent_phone_number__isnull = False)

                rtp = game_lotto_ticket_qs.aggregate(Sum("rtp")).get("rtp__sum") or 0
                rto = game_lotto_ticket_qs.aggregate(Sum("rto")).get("rto__sum") or 0
                effective_rtp = game_lotto_ticket_qs.aggregate(Sum("effective_rtp")).get("effective_rtp__sum") or 0

                

            else:
                rtp = 0
                rto = 0
                effective_rtp = 0


            

            rtp = max(effective_rtp, rtp)

            game_total_rtp = rtp
            game_total_rto = rto

            total_rtp += game_total_rtp
            total_rto += game_total_rto

            

            instance, _ = GamesDailyActivities.objects.get_or_create(game_type = game, created_at__date = TODAY.date())
            if instance.running_balance is None:
                if game =="BANKER":
                    boost, boost_percent = ConstantVariable.boost_banker_rtp(rtp).values()
                    instance.running_balance = boost
                
                if game =="SALARY_FOR_LIFE":
                    const_obj = ConstantVariable.objects.all().last()
                    instance.running_balance = const_obj.salary_4_life_running_balance

                if game == "KENYA_LOTTO":
                    instance.running_balance = AfricaLottoConstants.objects.last().kenya_running_balance
                elif game == "K_NOW":
                    instance.running_balance = AfricaLottoConstants.objects.last().k_now_running_balance
                elif game == "KENYA_30_LOTTO":
                    instance.running_balance = AfricaLottoConstants.objects.last().kenya30_running_balance


            try:
                GameAnalytics.update_record(
                    game_type = game,
                    sales_amount = game_total_sales,
                    winning_amount = game_total_winnings,
                    year = TODAY.year,
                    month = TODAY.month
                )
            except:
                pass


            if game_total_winnings > 0:
                instance.excess_winnings = round(game_total_rtp - game_total_winnings, 2)

            instance.daily_net = round(game_total_sales - game_total_winnings - game_total_commission, 2)


            if game_total_sales > 0:
                percentage_of_winnings_to_sales = (game_total_winnings / game_total_sales) * 100
            else:
                percentage_of_winnings_to_sales = 0

            instance.percentage_of_winnings_to_sales = round(percentage_of_winnings_to_sales, 2)


            try:
                ReturnToPlayerAndReturnToOwnerAnalytics.update_record(
                    game_type = game,
                    rtp = rtp,
                    rto = rto,
                    year = TODAY.year,
                    month = TODAY.month
                )
            except:
                pass

            instance.sales = round(game_total_sales, 2)
            instance.winnings = round(game_total_winnings, 2)
            instance.commission = round(game_total_commission, 2)
            instance.rtp = round(game_total_rtp, 2)
            instance.rto = round(game_total_rto, 2)
            instance.save()



        if total_sales > 0:
            total_percentage_to_winnings = (total_winnings / total_sales ) * 100
            total_percentage_to_winnings = round(total_percentage_to_winnings, 2)
        else:
            total_percentage_to_winnings = 0

        


        total_instance.sales = round(total_sales, 2)
        total_instance.winnings = round(total_winnings, 2)
        total_instance.commission = round(total_commission, 2)
        total_instance.rtp = round(total_rtp, 2)
        total_instance.rto = round(total_rto, 2)
        total_instance.percentage_of_winnings_to_sales = total_percentage_to_winnings
        if total_instance.winnings > 0:
            total_instance.excess_winnings = round(total_instance.rtp - total_instance.winnings, 2)
        total_instance.daily_net = round(total_instance.sales - total_instance.winnings - total_instance.commission, 2)
        total_instance.save()


        # Filter agent that have made sales or winnings today and save their data
        agent_that_performed_transactions_today = list(AgentWalletTransaction.objects.filter(date_created__date = TODAY.date(), transaction_from__in = ["GAME_PLAY", "WINNINGS"]).distinct("agent_phone_number").values_list("agent_phone_number", flat=True))

        for agent_phone in agent_that_performed_transactions_today:

            try:
                agent = Agent.objects.get(phone = agent_phone, terminal_id__isnull = False)
            except:
                continue
            
            agent_transaction_for_today = AgentWalletTransaction.objects.filter(date_created__date = TODAY.date(), agent_phone_number = agent_phone)

            agent_winnings = agent_transaction_for_today.filter(transaction_from = "WINNINGS").aggregate(Sum("amount")).get("amount__sum") or 0
            agent_sales = agent_transaction_for_today.filter(transaction_from = "GAME_PLAY").aggregate(Sum("amount")).get("amount__sum") or 0


            LottoAgentSalesActivity.update_agent_sales_activity(
                agent_name=f"{agent.first_name} {agent.last_name}",
                agent_email=agent.email,
                agent_phone_number=agent.phone,
                terminal_id=agent.terminal_id,
                sales=agent_sales,
                winnings = agent_winnings,
                agent_instance = agent
            )
            
        
        
        periods = ["all_time", "today", "yesterday", "this_week", "last_week", "this_month", "last_month", "this_year"]

        for period in periods:
            metrics = SalesBasedMetricsCalculator.calculate_agent_metrics_by_sales(wave="WAVE_TWO", period=period)

            # Update the metrics table
            for metric_name, value in metrics.items():
                AgentMetricSummary.set_metric_value(metric_name, period, value)




            





            





                


            

            









