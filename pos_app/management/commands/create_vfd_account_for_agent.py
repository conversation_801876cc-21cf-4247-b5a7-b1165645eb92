from django.core.management.base import BaseCommand

from pos_app.models import Agent<PERSON><PERSON><PERSON>
from pos_app.tasks import create_agent_vfd_virtual_wallet


class Command(BaseCommand):
    help = "CREATE VFD VIRTUAL ACCOUNT FOR AGENT"

    def handle(self, *args, **kwargs):
        agent__wallets = AgentWallet.objects.filter(vfd_account__isnull=True)

        if agent__wallets:
            for agent__wallet in agent__wallets:
                create_agent_vfd_response = create_agent_vfd_virtual_wallet(agent_id=agent__wallet.agent.id)
                print("create_agent_vfd_response", create_agent_vfd_response)

            print("vfd account created for agent")
        else:
            print("all agent has vfd account")
