import random
import uuid
from datetime import datetime, time

import pytz
from django.conf import settings
from django.contrib.auth.hashers import make_password
from django.core.management.base import BaseCommand

from account.models import User
from africa_lotto.models import AfricaLotto, AfricaLottoBatch, AfricaLottoGameType
from africa_lotto.tasks import celery_africa_lotto_kenya_draw
from banker_lottery.models import SureBanker
from main.api.api_lottery_helpers import generate_game_play_id
from main.models import LotteryBatch, LottoTicket, UserProfile
from pos_app.models import Agent
from pos_app.pos_helpers import PosLottoHelper


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):

        agent_details = {
            "first_name": "Liberty Test",
            "last_name": "Account",
            "full_name": "Liberty Test Account",
            "phone": "*************",
            "email": "<EMAIL>",
        }

        # check if user profile exist
        try:
            user_profile = UserProfile.objects.get(phone_number=agent_details.get("phone"))
        except UserProfile.DoesNotExist:
            user_profile = UserProfile.objects.create(
                phone_number=agent_details.get("phone"),
                email=agent_details.get("email"),
                first_name=agent_details.get("first_name"),
                last_name=agent_details.get("last_name"),
                channel="POS",
            )

        # check if user instance exist
        try:
            User.objects.get(
                phone=agent_details.get("phone"),
            )
        except User.DoesNotExist:
            User.objects.create(
                email=agent_details.get("email"),
                phone=agent_details.get("phone"),
                password=make_password(agent_details.get("email")),
                first_name=agent_details.get("first_name"),
                last_name=agent_details.get("last_name"),
                channel="APP/POS",
                phone_is_verified=True,
            )

        try:
            agent_instance = Agent.objects.get(phone=agent_details.get("phone"))
        except:
            agent_instance = Agent.objects.create(
                first_name=agent_details.get("first_name"),
                last_name=agent_details.get("last_name"),
                phone=agent_details.get("phone"),
                email=agent_details.get("email"),
                user_id=agent_details.get("email"),
                user_uuid=agent_details.get("email"),
                agent_type="PERSONAL",
                wave="WAVE_TWO",
            )

        # get banker active batch
        banker_batch = SureBanker.current_batch()

        # check if this user has game in this batch
        try:
            LottoTicket.objects.get(batch=banker_batch, agent_profile=agent_instance)
        except LottoTicket.DoesNotExist:
            identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"
            ticket_instance = LottoTicket.objects.create(
                user_profile_id=user_profile.id,
                agent_profile=agent_instance,
                batch=banker_batch,
                phone=agent_details.get("phone"),
                stake_amount=20,
                expected_amount=20,
                potential_winning=0,
                channel="POS_AGENT",
                game_play_id=generate_game_play_id(),
                lottery_type="BANKER",
                number_of_ticket=1,
                ticket="4,5,1,3,5",
                pin="0000",
                identity_id=identity_id,
            )

            ticket_instance.paid = True
            ticket_instance.amount_paid = 20
            ticket_instance.save()

        # check salary for life game
        active_batch = PosLottoHelper.get_or_create_active_batch("SALARY_FOR_LIFE")

        try:
            LottoTicket.objects.get(batch=active_batch.get("batch"), agent_profile=agent_instance)
        except LottoTicket.DoesNotExist:

            game_play_id = generate_game_play_id()

            LottoTicket.objects.create(
                user_profile=user_profile,
                agent_profile=agent_instance,
                batch=active_batch.get("batch"),
                phone=agent_details.get("phone"),
                stake_amount=20,
                expected_amount=20,
                potential_winning=100,
                number_of_ticket=1,
                channel="POS_AGENT",
                game_play_id=game_play_id,
                unique_game_play_id=game_play_id,
                lottery_type="SALARY_FOR_LIFE",
                ticket="2,3,4,1,5",
                pin="0000",
                is_agent=True,
                paid=True,
                amount_paid=20,
                rtp=1,
                rto=1,
                commission_value=0,
                commission_per=0,
                rtp_per=0,
            )

        # check kenya lotto
        sales_date = datetime.now().date()
        current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        current_time_cutoff = current_time.time()
        cutoff_time = time(21, 00)

        if current_time_cutoff <= cutoff_time:
            # get lottery batch
            active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_LOTTO)

            # check the draw time for the batch and make sure this game play is not exactly at the time or the draw time should have drawn
            batch_draw_date = f"{sales_date.strftime('%Y-%m-%d')} {active_batch.next_draw_time}"
            batch_draw_date = datetime.strptime(batch_draw_date, "%Y-%m-%d %I:%M %p")

            batch_draw_date = pytz.timezone(settings.TIME_ZONE).localize(batch_draw_date)
            current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
            if current_time >= batch_draw_date:
                active_batch.batch_status = False
                active_batch.save()

                celery_africa_lotto_kenya_draw(batch_db_id=active_batch.id)

                active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_LOTTO)
        else:
            active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_LOTTO)

            if active_batch.next_draw_time.strip() != "09:01 AM":

                active_batch.batch_status = False
                active_batch.save()

                # Get a new active batch
                active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_LOTTO)

        
        
        created__tickets = AfricaLotto.objects.filter(agent_phone_number=agent_instance.phone, batch=active_batch)
        if len(created__tickets) >= 6:
            pass
        else:

            for i in range(0, 6):
                game_play_id = generate_game_play_id()

                lottery_types = ["NAP2", "NAP3", "NAP4", "NAP5", "PERM2", "PERM3", "PERM4", "PERM5", "BANKER"]

                random_lottery_type = random.choice(lottery_types)

                if random_lottery_type == "BANKER":
                    lucky_number = f"{random.randint(1, 48)}"

                elif random_lottery_type.startswith("NAP"):
                    num_count = int(random_lottery_type[3])
                    numbers = random.sample(range(1, 49), num_count)
                    lucky_number = ",".join(map(str, numbers))

                elif random_lottery_type.startswith("PERM"):
                    num_count = int(random_lottery_type[4])
                    numbers = random.sample(range(1, 49), num_count)
                    lucky_number = ",".join(map(str, numbers))

                AfricaLotto.objects.create(
                    user_phone_number=kwargs.get("phone_number"),
                    agent_phone_number=agent_instance.phone,
                    agent_name=agent_instance.full_name,
                    batch=active_batch,
                    game_play_id=game_play_id,
                    game_pin="000",
                    lucky_number=lucky_number,
                    bottom_ticket=None,
                    stake_per_line=0,
                    purchase_amount=25,
                    total_game_stake_amount=25,
                    potential_winnings=25,
                    multiplier=1,
                    lottery_type=random_lottery_type,
                    channel="POS_AGENT",
                    game_type=AfricaLottoGameType.KENYA_LOTTO,
                    paid=True,
                    rtp=2,
                    rto=2,
                )
                
                
                
        # check kenya 30 lotto
        cutoff_time = time(21, 45)

        if current_time_cutoff <= cutoff_time:
            # get lottery batch
            active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_30_LOTTO)

            # check the draw time for the batch and make sure this game play is not exactly at the time or the draw time should have drawn
            batch_draw_date = f"{sales_date.strftime('%Y-%m-%d')} {active_batch.next_draw_time}"
            print("batch_draw_date", batch_draw_date, "\n\n\n")
            batch_draw_date = datetime.strptime(batch_draw_date, "%Y-%m-%d %I:%M %p")

            batch_draw_date = pytz.timezone(settings.TIME_ZONE).localize(batch_draw_date)
            current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
            if current_time >= batch_draw_date:
                active_batch.batch_status = False
                active_batch.save()

                active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_30_LOTTO)
        else:
            active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_30_LOTTO)

            # Check if next draw time is 8:15 AM
            if active_batch.next_draw_time.strip() != "08:15 AM":

                active_batch.batch_status = False
                active_batch.save()

                # Get a new active batch
                active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.KENYA_30_LOTTO)
                
                
        
        
        created__tickets = AfricaLotto.objects.filter(agent_phone_number=agent_instance.phone, batch=active_batch)
        if len(created__tickets) >= 6:
            pass
        else:

            for i in range(0, 6):
                game_play_id = generate_game_play_id()

                lottery_types = ["NAP2", "NAP3", "NAP4", "NAP5", "PERM2", "PERM3", "PERM4", "PERM5", "BANKER"]

                random_lottery_type = random.choice(lottery_types)

                if random_lottery_type == "BANKER":
                    lucky_number = f"{random.randint(1, 48)}"

                elif random_lottery_type.startswith("NAP"):
                    num_count = int(random_lottery_type[3])
                    numbers = random.sample(range(1, 49), num_count)
                    lucky_number = ",".join(map(str, numbers))

                elif random_lottery_type.startswith("PERM"):
                    num_count = int(random_lottery_type[4])
                    numbers = random.sample(range(1, 49), num_count)
                    lucky_number = ",".join(map(str, numbers))

                AfricaLotto.objects.create(
                    user_phone_number=kwargs.get("phone_number"),
                    agent_phone_number=agent_instance.phone,
                    agent_name=agent_instance.full_name,
                    batch=active_batch,
                    game_play_id=game_play_id,
                    game_pin="000",
                    lucky_number=lucky_number,
                    bottom_ticket=None,
                    stake_per_line=0,
                    purchase_amount=25,
                    total_game_stake_amount=25,
                    potential_winnings=25,
                    multiplier=1,
                    lottery_type=random_lottery_type,
                    channel="POS_AGENT",
                    game_type=AfricaLottoGameType.KENYA_30_LOTTO,
                    paid=True,
                    rtp=2,
                    rto=2,
                )

        
            
    
        
                
                

        active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.K_NOW)

        # check the draw time for the batch and make sure this game play is not exactly at the time or the draw time should have drawn
        batch_draw_date = f"{sales_date.strftime('%Y-%m-%d')} {active_batch.next_draw_time}"
        batch_draw_date = datetime.strptime(batch_draw_date, "%Y-%m-%d %I:%M %p")
        current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        batch_draw_date = pytz.timezone(settings.TIME_ZONE).localize(batch_draw_date)

        current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        if current_time >= batch_draw_date:
            active_batch.batch_status = False
            active_batch.save()

            active_batch = AfricaLottoBatch.get_active_batch(AfricaLottoGameType.K_NOW)

        # check K now
        # play 6 different game for this account

        created__tickets = AfricaLotto.objects.filter(agent_phone_number=agent_instance.phone, batch=active_batch)
        if len(created__tickets) >= 6:
            pass
        else:

            for i in range(0, 6):
                game_play_id = generate_game_play_id()

                lottery_types = ["NAP2", "NAP3", "NAP4", "NAP5", "PERM2", "PERM3", "PERM4", "PERM5", "BANKER"]

                random_lottery_type = random.choice(lottery_types)

                if random_lottery_type == "BANKER":
                    lucky_number = f"{random.randint(1, 48)}"

                elif random_lottery_type.startswith("NAP"):
                    num_count = int(random_lottery_type[3])
                    numbers = random.sample(range(1, 49), num_count)
                    lucky_number = ",".join(map(str, numbers))

                elif random_lottery_type.startswith("PERM"):
                    num_count = int(random_lottery_type[4])
                    numbers = random.sample(range(1, 49), num_count)
                    lucky_number = ",".join(map(str, numbers))

                AfricaLotto.objects.create(
                    user_phone_number=kwargs.get("phone_number"),
                    agent_phone_number=agent_instance.phone,
                    agent_name=agent_instance.full_name,
                    batch=active_batch,
                    game_play_id=game_play_id,
                    game_pin="000",
                    lucky_number=lucky_number,
                    bottom_ticket=None,
                    stake_per_line=0,
                    purchase_amount=25,
                    total_game_stake_amount=25,
                    potential_winnings=25,
                    multiplier=1,
                    lottery_type=random_lottery_type,
                    channel="POS_AGENT",
                    game_type=AfricaLottoGameType.K_NOW,
                    paid=True,
                    rtp=2,
                    rto=2,
                )
