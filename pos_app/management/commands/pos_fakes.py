import base64
import csv
import json
import random
import uuid
from datetime import datetime
import requests
from django.contrib.auth.hashers import make_password

import csv
from dateutil.relativedelta import relativedelta

import pytz
from django.conf import settings
from django.core.management.base import BaseCommand
from django.db.models import Q, Sum

from account.models import BlackListed, User
from africa_lotto.models import AfricaLotto
from africa_lotto.tasks import process_winner_data
from main.api.api_lottery_helpers import generate_game_play_id
from main.helpers.helper_functions import get_last_month
from main.helpers.loandisk import full_name_split
from main.management.commands.fakes import generate_random_lucky_number, random_date
from main.models import (
    ConstantVariable,
    LotteryBatch,
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    PayoutTransactionTable,
    UserProfile,
)
from pos_app import enums
from pos_app.models import (
    Agent,
    AgentFundingTable,
    AgentWallet,
    AgentWalletTransaction,
    FailedRemittanceAgencyWalletCharge,
    GamesDailyActivities,
    LottoAgentRemittanceTable,
    LottoAgentSalesActivity,
    LottoSuperAgents,
    LottoSuperAgentWallet,
    PosLotteryWinners,
    Supervisor,
)
from pos_app.pos_helpers import PosAgentHelper
from pos_app.tasks import (
    celery_trigger_agent_reward,
    celery_update_winwise_agent_salary_record,
    celery_update_winwise_agent_salary_record_monthly,
    reward_agent_amd_super_agent_commission,
)
from pos_app.utils import DommyName, serialize_ticket
from scratch_cards.helpers.api import paystack_account_name
from wallet_app.models import DebitCreditRecord, UserWallet


def create_dummy_winners_for_agents():
    agents = Agent.objects.all()

    users = UserProfile.objects.all()

    for num in range(0, 2000):
        agent = random.choice(agents)

        band_choices = [10000, 50000, 100000, 200000]

        amount = 150

        stake_and_pont_choices = [
            {
                "stake_amount": amount,
                "total_winning_amount": 11250,
                "number_of_ticket": 1,
            },
            {
                "stake_amount": amount * 2,
                "total_winning_amount": 13500,
                "number_of_ticket": 2,
            },
            {
                "stake_amount": amount * 3,
                "total_winning_amount": 15750,
                "number_of_ticket": 3,
            },
            {
                "stake_amount": amount * 4,
                "total_winning_amount": 18000,
                "number_of_ticket": 4,
            },
            {
                "stake_amount": amount * 5,
                "total_winning_amount": 18750,
                "number_of_ticket": 5,
            },
            {
                "stake_amount": amount * 6,
                "total_winning_amount": 22500,
                "number_of_ticket": 6,
            },
            {
                "stake_amount": 1000,
                "total_winning_amount": 25000,
                "number_of_ticket": 7,
            },
        ]

        pontential_winning_choices = [
            5000,
            15000,
            50000,
            150000,
            250000,
            500000,
            750000,
            900000,
            1250000,
            1500000,
        ]

        for i in range(0, 2):  # WYSE CASH WINNERS
            lottery_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH").last()
            user = random.choice(users)
            band = random.choice(band_choices)
            if band == 10000:
                pool = "TEN_THOUSAND"
                stake_amount = 100
            elif band == 50000:
                pool = "FIFTY_THOUSAND"
                stake_amount = 200
            elif band == 100000:
                pool = "ONE_HUNDRED_THOUSAND"
                stake_amount = 500
            elif band == 200000:
                pool = "TWO_HUNDRED_THOUSAND"
                stake_amount = 1000

            number_of_ticket = random.randint(1, 10)

            earning = random.randint(1000, 10000)

            get_game_play_id = generate_game_play_id()

            if user.account_name is None:
                name = DommyName(6).generate_name()
            else:
                name = user.account_name

            names = full_name_split(name)

            lucky_number = f"{names.get('first_name')[0]}{names.get('last_name')[0]}-{generate_random_lucky_number()}"

            lottery_model_instance = LotteryModel.objects.create(
                user_profile=user,
                agent_profile=agent,
                batch=lottery_batch,
                phone=user.phone_number,
                pool=pool,
                stake_amount=stake_amount,
                expected_amount=stake_amount,
                instance_number=number_of_ticket,
                channel="POS_AGENT",
                game_play_id=get_game_play_id,
                lottery_type="WYSE_CASH",
                lucky_number=lucky_number,
                band=band,
            )

            # create winner instance

            LotteryWinnersTable.objects.create(
                lottery=lottery_model_instance,
                batch=lottery_batch,
                run_batch_id=number_of_ticket,
                phone_number=lottery_model_instance.user_profile.phone_number,
                playyer_id=lottery_model_instance.user_profile.id,
                unnique_id=lottery_model_instance.unique_id,
                game_play_id=lottery_model_instance.game_play_id,
                ticket=lottery_model_instance.lucky_number,
                win_type="ORDINARY_WINNER",
                pool=lottery_model_instance.pool,
                stake_amount=lottery_model_instance.stake_amount,
                earning=earning,
                lottery_source_tag=lottery_model_instance.channel,
            )

        for i in range(0, 1):  # INSTANT WINNERS
            user = random.choice(users)

            lottery_batch = LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT").last()

            ticket = []
            for i in range(0, 4):
                ticket.append(random.randint(1, 49))

            get_game_play_id = generate_game_play_id()
            identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"

            stake_pont_pick = random.choice(stake_and_pont_choices)

            lottery_instance = LottoTicket.objects.create(
                user_profile=user,
                agent_profile=agent,
                batch=lottery_batch,
                phone=user.phone_number,
                stake_amount=stake_pont_pick["stake_amount"],
                expected_amount=stake_pont_pick["stake_amount"],
                potential_winning=stake_pont_pick["total_winning_amount"],
                paid=False,
                number_of_ticket=stake_pont_pick["number_of_ticket"],
                channel="POS_AGENT",
                game_play_id=get_game_play_id,
                lottery_type="INSTANT_CASHOUT",
                ticket=serialize_ticket(ticket),
                pin="1234",
                identity_id=identity_id,
            )

            # create winner instance

            win_favour_choices = random.choice(["BLACK", "CASHBACK", "WHITE"])

            LottoWinners.objects.create(
                batch=lottery_batch,
                phone_number=user.phone_number,
                ticket=serialize_ticket(ticket),
                win_type="PERM_3",
                lotto_type="INSTANT_CASHOUT",
                game_play_id=get_game_play_id,
                stake_amount=stake_amount,
                earning=earning,
                channel_played_from="POS_AGENT",
                run_batch_id=stake_pont_pick["number_of_ticket"],
                lottery=lottery_instance,
                win_flavour=win_favour_choices,
            )

        for i in range(0, 2):  # SALARY FOR LIFE WINNERS WINNERS
            lottery_batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE").last()

            ticket = []
            for i in range(0, 5):
                ticket.append(random.randint(1, 10))

            # generate random stake amount
            stake_amount = random.randint(100, 1000)

            # generate random expected amount
            expected_amount = (random.randint(100, 1000)) * 2

            get_game_play_id = generate_game_play_id()

            random_iterate_number = random.randint(1, 10)

            lottery_instance = LottoTicket.objects.create(
                user_profile=user,
                agent_profile=agent,
                batch=lottery_batch,
                phone=user.phone_number,
                stake_amount=int(stake_amount),
                expected_amount=expected_amount,
                paid=False,
                number_of_ticket=random_iterate_number,
                channel="POS_AGENT",
                game_play_id=get_game_play_id,
                lottery_type="SALARY_FOR_LIFE",
                ticket=serialize_ticket(ticket),
                potential_winning=random.choice(pontential_winning_choices),
                pin="1234",
                identity_id=identity_id,
            )

            # create winner instance

            LottoWinners.objects.create(
                batch=lottery_batch,
                phone_number=user.phone_number,
                ticket=serialize_ticket(ticket),
                win_type="PERM_3",
                lotto_type="SALARY_FOR_LIFE",
                game_play_id=get_game_play_id,
                stake_amount=stake_amount,
                earning=earning,
                channel_played_from="POS_AGENT",
                run_batch_id=stake_pont_pick["number_of_ticket"],
                lottery=lottery_instance,
            )


def update_lottoticket_with_random_date():
    lotto_ticket = LottoTicket.objects.all()

    for i in range(0, 600):
        ticket = random.choice(lotto_ticket)
        ticket.date = random_date()
        ticket.save()


def generate_auth_token():
    username = settings.LIBERTY_VAS_AUTH_USERNAME
    password = settings.LIBERTY_VAS_AUTH_PASSWORD

    STRING_VALUE = f"{username}:{password}"
    AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

    return AUTH_TOKEN


def rewarding_agent_commission():
    agents = Agent.objects.all()

    for agent in agents:
        print(celery_trigger_agent_reward(agent.id))


def super_agent_commission_reward():
    super_agents = LottoSuperAgents.objects.all()
    if super_agents:
        for super_agent in super_agents:
            super_agent_wallet_instance = LottoSuperAgentWallet.objects.filter(super_agent__id=super_agent.id).last()

            if super_agent_wallet_instance is not None:
                if super_agent_wallet_instance.commission_balance <= 0:
                    continue

                amount_to_remove = super_agent_wallet_instance.commission_balance
                super_agent_wallet_instance.commission_balance -= amount_to_remove
                super_agent_wallet_instance.save()

                if amount_to_remove <= 0:
                    continue

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": super_agent.phone,
                            "amount": amount_to_remove,
                            "narration": "SUPER_AGENT_COMMISSION_REWARD",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                        }
                    ],
                }

                payload["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN

                agent = Agent.objects.all().last()

                pos_agent_helper = PosAgentHelper(agent_instance=agent, amount=amount_to_remove)

                agency_buddy = pos_agent_helper.agency_buddy_transfer(**payload)

                if isinstance(agency_buddy, dict):
                    if agency_buddy.get("message") == "success":
                        super_agent_wallet_instance.rewarded_commission_balance += amount_to_remove

                        super_agent_wallet_instance.save()
                    else:
                        super_agent_wallet_instance.commission_balance += amount_to_remove
                        super_agent_wallet_instance.transaction_type = "REVERSAL"
                        super_agent_wallet_instance.save()

                else:
                    super_agent_wallet_instance.commission_balance += amount_to_remove
                    super_agent_wallet_instance.transaction_type = "REVERSAL"
                    super_agent_wallet_instance.save()


def update_lottery_draw_date():
    lottery_batch_without_draw_date = LotteryBatch.objects.filter(draw_date__isnull=True)

    if lottery_batch_without_draw_date:
        for batch in lottery_batch_without_draw_date:
            batch.draw_date = batch.last_updated
            batch.save()


def empty_agent_user_wallet():
    agent_user = Agent.objects.all()

    for agent in agent_user:
        UserWallet.objects.filter(user__phone_number=agent.phone, wallet_tag="WEB").update(withdrawable_available_balance=0)

    print("done empty_agent_user_wallet")


def reversal_all_failed_transactions():
    datetime.now().date()

    transactions = PayoutTransactionTable.objects.filter(
        is_verified=True,
        disbursed=False,
        id__lt=10443,
        channel="POS",
        verification_response_payload="REVERSED",
    )

    print(len(transactions))

    if transactions:
        for num, trans in enumerate(transactions):
            print(num)

            if trans.channel == "POS":
                # check if game id is more than two, reason because i've worked on some of the transactions
                trans_game_id = PayoutTransactionTable.objects.filter(game_play_id=trans.game_play_id)
                if len(trans_game_id) > 2:
                    continue

            agent_wallet = AgentWallet.objects.filter(agent__phone=trans.phone).last()
            if agent_wallet:
                agent_wallet.winnings_bal = 0
                agent_wallet.withdrawable_available_bal += trans.amount
                agent_wallet.transaction_from = "WINNING"
                agent_wallet.transaction_type = "PAYOUT_REVERSAL"
                agent_wallet.save()

            # # # is_reversed = disbuggrsennt_reversal(trans)
            # # if is_reversed:
            # #     trans.is_verified = True
            # #     trans.verification_response_payload = "REVERSED"
            # #     trans.status = "FAILED"
            # #     trans.save()

            #     print("reversed")
            # else:
            #     print("not reversed")
            #     print(
            #         f"""
            #     phone: {trans.phone}
            #     trans_ref: {trans.disbursement_unique_id}
            #     """
            #     )


def update_salary_for_life_per2_winner_wallet():
    winners = LottoWinners.objects.filter(match_type="PERM_2", lotto_type="SALARY_FOR_LIFE", earning__lt=1)

    print("winners", len(winners))

    if winners:
        for winner in winners:
            winning_ticket = LottoTicket.objects.filter(game_play_id=winner.game_play_id)

            print(
                f"""
            game_id: {winner.game_play_id}
            """
            )
            if winning_ticket.exists():
                # amount_won = 1
                number_of_line = winning_ticket.last().number_of_ticket
                amount_won = LottoTicket.salary_for_life_win_per_line(2, number_of_line)

                print(
                    f"""
                WINNING AMOUNT PARAMS
                match_type: {winner.match_type}
                number_of_line: {number_of_line}
                """
                )

                print("amount_won", amount_won)
                input("PRESS ENTER TO CONTINUE")

                if winner.channel_played_from == "POS_AGENT":
                    agent = Agent.objects.filter(id=winner.lottery.agent_profile.id).last()
                    if agent:
                        agent_wallet = AgentWallet.objects.filter(agent__id=agent.id).last()
                        # user_profile = UserProfile.objects.filter(phone_number=winner.lottery.user_profile.phone_number).last()

                        print(""" GETTING POS LOTTERY WINNER """)
                        post_lottery_winner = PosLotteryWinners.objects.filter(game_id=winner.game_play_id, agent=agent)

                        print(""" DELETING DUPLICATE POS LOTTERY WINNER """)
                        delete_instance_qs = PosLotteryWinners.objects.filter(game_id=winner.game_play_id, agent=agent, pin="")

                        print(
                            f"""
                            winner.game_play_id: {winner.game_play_id}
                            agent: {agent}
                            pin__isnull: {True}
                            delete_instance_qs: {delete_instance_qs}
                        """
                        )

                        delete_instance_qs.delete()

                        if post_lottery_winner.exists():
                            for pos_winner in post_lottery_winner:
                                if pos_winner is None or pos_winner == "":
                                    print("YES PIN IS NONE")
                                    print(
                                        f"pos_winner.delete() {pos_winner.id}",
                                        pos_winner.delete(),
                                    )
                                    print("\n\n\n")
                                    continue

                                else:
                                    PosLotteryWinners.objects.filter(id=pos_winner.id).update(amount_won=amount_won)
                                    print(f""" UPDATING POS LOTTERY WINNER  pos_winner.id {pos_winner.id}""")
                                    # pos_winner.amount_won = amount_won
                                    # pos_winner.save()

                                    if agent_wallet:
                                        agent_wallet.winnings_bal += amount_won
                                        agent_wallet.transaction_from = "WINNINGS"
                                        agent_wallet.phone_number = agent.phone
                                        agent_wallet.game_type = "SALARY_FOR_LIFE"
                                        agent_wallet.game_play_id = winner.game_play_id
                                        agent_wallet.save()

                                    else:
                                        pass

                        else:
                            pass

                    else:
                        pass

                else:
                    user_wallet = UserWallet.objects.filter(
                        user__phone_number=winner.lottery.user_profile.phone_number,
                        wallet_tag="WEB",
                    ).last()
                    if user_wallet:
                        user_wallet.withdrawable_available_balance += amount_won
                        user_wallet.transaction_from = "SAL_4_LIFE_GAME_WIN"
                        user_wallet.save()

                    else:
                        pass
            else:
                pass


def update_agent_pending_withdrawal_to_wallet_value():
    pending_withdrawal = PosLotteryWinners.objects.filter(
        is_win_claimed=False,
        withdrawl_initiated=False,
    )
    agents_id = []

    if pending_withdrawal:
        for pending_w in pending_withdrawal:
            if pending_w.agent.id not in agents_id:
                agents_id.append(pending_w.agent.id)

                agents_winnings_ids = (
                    PosLotteryWinners.objects.filter(
                        agent=pending_w.agent,
                        is_win_claimed=False,
                        withdrawl_initiated=False,
                    )
                    .distinct("game_id")
                    .values_list("id", flat=True)
                )

                agents_winnings = PosLotteryWinners.objects.filter(id__in=agents_winnings_ids).aggregate(Sum("amount_won"))["amount_won__sum"]

                if agents_winnings is None:
                    agents_winnings = 0

                agent_wallet = AgentWallet.objects.filter(agent=pending_w.agent).last()
                if agent_wallet:
                    agent_wallet.winnings_bal = agents_winnings
                    agent_wallet.transaction_from = "WINNINGS_MANUALLY_UPDATED"
                    agent_wallet.save()


def update_agent_pending_withdrawal_to_wallet_value_2():
    import pytz

    now_utc = datetime.now(pytz.utc)

    tz = pytz.timezone("Africa/Lagos")
    now_tz = now_utc.astimezone(tz)

    pending_withdrawal = PosLotteryWinners.objects.filter(
        is_win_claimed=False,
        withdrawl_initiated=False,
        payout_successful=False,
        payout_verified=False,
        date_created__year=now_tz.year,
    )

    unique_phone_numbers = list(pending_withdrawal.values_list("agent__phone", flat=True).distinct())
    for phone in unique_phone_numbers:
        check_payout_record = PosLotteryWinners.objects.filter(
            agent__phone=phone,
            is_win_claimed=False,
            withdrawl_initiated=False,
            payout_successful=False,
            payout_verified=False,
            date_created__year=now_tz.year,
        ).aggregate(Sum("amount_won"))["amount_won__sum"]

        agent_wallet = AgentWallet.objects.filter(agent__phone=phone).last()

        if agent_wallet is not None:
            if agent_wallet.winnings_bal != check_payout_record:
                agent_wallet.winnings_bal = check_payout_record
                agent_wallet.transaction_from = "WINNINGS_MANUALLY_UPDATED"
                agent_wallet.save()

    # if pending_withdrawal:
    #     for pending_w in pending_withdrawal:
    #         check_payout_record = PayoutTransactionTable.objects.filter(
    #             game_play_id=pending_w.game_id,
    #             disbursed=True,
    #             is_verified=True,
    #             payment_initiated=True,
    #         ).last()
    #         if check_payout_record:
    #             agent_wallet = AgentWallet.objects.filter(
    #                 agent__id=pending_w.agent.id
    #             ).last()
    #             if agent_wallet:
    #                 if agent_wallet.winnings_bal >= pending_w.amount_won:
    #                     agent_wallet.winnings_bal -= pending_w.amount_won
    #                     agent_wallet.transaction_from = "WINNINGS_MANUALLY_UPDATED"
    #                     agent_wallet.save()

    #                 pending_w.is_win_claimed = True
    #                 pending_w.withdrawl_initiated = True
    #                 pending_w.payout_successful = True
    #                 pending_w.payout_verified = True

    #                 pending_w.save()

    #                 print("UPDATED FOR THIS AGENT", pending_w.agent.full_name)


def make_failed_pos_lottery_winnings_available():
    PosLotteryWinners.objects.filter(
        is_win_claimed=True,
        withdrawl_initiated=True,
        payout_successful=False,
        payout_verified=False,
    ).update(is_win_claimed=False, withdrawl_initiated=False)


def lotto_agent_commission_reward():
    agents = Agent.objects.filter(super_agent__isnull=True)
    agents_phones = []
    agent_rewarded = []
    agent_failed_reward = []
    if agents:
        for agent in agents:
            agents_phones.append(agent.phone)
            print(f"agent: {agent.phone}")
            agent_remittance = LottoAgentRemittanceTable.objects.filter(Q(agent=agent), Q(created_at__month=2), Q(remitted=True)).filter(
                Q(created_at__day__gt=24)
            )
            if agent_remittance:
                total_amount_played = agent_remittance.aggregate(Sum("amount"))["amount__sum"]
                if total_amount_played is None:
                    total_amount_played = 0

                if total_amount_played < 1:
                    print(f"total_amount_played: {total_amount_played}")
                    continue

                agent_commission = total_amount_played * 0.15
                agent_wallet = AgentWallet.objects.filter(agent=agent).last()
                agent_wallet_transaction_instance = AgentWalletTransaction.objects.create(
                    agent_wallet=agent_wallet,
                    transaction_from="COMMISSION_REWARD",
                    transaction_type="DEBIT",
                    amount=agent_commission,
                    type_of_agent=("WINWISE_AGENT" if agent_wallet.agent.is_winwise_agent else "OTHER_AGENT"),
                    agent_name = agent_wallet.agent.first_name + " " + agent_wallet.agent.last_name,
                    agent_phone_number = agent_wallet.agent.phone,
                    agent_email = agent_wallet.agent.email,
                    terminal_id=agent_wallet.agent.terminal_id,
                    terminal_serial_number=agent_wallet.agent.terminal_serial_number,
                    wave = agent_wallet.agent.get_wave()
                )

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": agent.phone,
                            "amount": agent_commission,
                            "narration": "LOTTO_COMMISSION",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                        }
                    ],
                }
                payload["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN
                agent_helper = PosAgentHelper(agent_instance=agent, amount=0)
                agency_buddy = agent_helper.agency_buddy_transfer(**payload)

                if isinstance(agency_buddy, dict):
                    if agency_buddy.get("message") == "success":
                        agent_wallet.commission_rewarded += agent_commission
                        agent_wallet.transaction_from = "COMMISSION"
                        agent_wallet.save()
                        agent_rewarded.append(agent.phone)

                        agent_wallet_transaction_instance.status = "SUCCESSFUL"
                        agent_wallet_transaction_instance.save()

                    else:
                        print("FAILED TO REWARD AGENT COMMISSION WALLET")
                        agent_wallet.commission_bal += agent_commission
                        agent_wallet.save()
                        agent_failed_reward.append(agent.phone)

                        agent_wallet_transaction_instance.status = "FAILED"
                        agent_wallet_transaction_instance.save()
                else:
                    agent_wallet.commission_bal += agent_commission
                    agent_wallet.save()
                    agent_wallet_transaction_instance.status = "FAILED"
                    agent_wallet_transaction_instance.save()

                    agent_failed_reward.append(agent.phone)

    print(f"REWARDED AGENTS: {agent_rewarded}")
    print("\n\n\n\n\n")
    print(f"FAILED AGENTS: {agent_failed_reward}")
    print("\n\n\n\n\n")
    print(f"ALL AGENTS: {agents_phones}")


def count_agent_winnings_and_number_of_disbursed_winnings():
    pos_lottery_winners = PosLotteryWinners.objects.filter(agent__phone="2348179298803")

    data = []

    if pos_lottery_winners:
        for _winnings in pos_lottery_winners:
            payout_transaction = PayoutTransactionTable.objects.filter(game_play_id=_winnings.game_id)
            _data = {
                "game_id": _winnings.game_id,
                "count_of_payout": payout_transaction.count(),
                "count_of_disbursed": payout_transaction.filter(disbursed=True).count(),
                "count_of_verified": payout_transaction.filter(is_verified=True).count(),
                "count_of_marked_paid_on_pos_lottery_winners_table": pos_lottery_winners.filter(payout_successful=True).count(),
            }

            data.append(_data)

    print(
        f"""
    DATA :
    {data}
    \n\n\n\n\n
    """
    )


def get_failed_remittance_charge_but_marked_as_successful():
    charged_remittance = FailedRemittanceAgencyWalletCharge.objects.filter(successfully_charged=True)

    data = []

    if charged_remittance:
        for charge_r in charged_remittance:
            if charge_r.response_payload is not None:
                res_payload_json_string = charge_r.response_payload.replace("'", '"')

                print(f"res_payload_json_string: {res_payload_json_string}")

                try:
                    res_payload = json.loads(res_payload_json_string)
                except Exception:
                    print(
                        f"""
                    res_payload_json_string: {res_payload_json_string}
                    """
                    )

                    continue

                if res_payload.get("message") == "success":
                    if res_payload.get("data") is None:
                        data.append(
                            {
                                "ref": charge_r.trans_ref,
                                "email": charge_r.agent.email,
                            }
                        )

    print(
        f"""
    DATA: {data}
    """
    )


def failed_remittance_marked_as_successful():
    data = [
        {
            "ref": "RC-0e2e6300-4816-42da-b54b-1b25e6314ad1",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-4eaa0625-61cd-4d0a-b4d9-e09a52eded5a",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-7017c259-0461-4af2-be68-187a7f0634c0",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-9c8d1ee2-4c63-485b-bb83-be0c395cdb7d",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-2e03caf6-34d6-48cb-a2f6-2736fef0caf1",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-4173896e-d460-484d-a392-ebf45d809048",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-a9bc7895-bc0f-45c1-abca-07687c8d3879",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-3271dd41-c66c-481a-b32b-ab13154cc89f",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-b3b60ca1-cf5d-49f7-b1a5-1523d1a75afe",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-3d0b24c8-ea6b-477a-9b9a-891c76174592",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-72f5c64b-af6d-445a-ad6a-a0a76d4f7b26",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-6a34a410-1a1e-4a28-a4e4-20b08e479ef1",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-2142273b-cecb-4265-a16a-11c4bc79c12c",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-c33c3606-f794-415b-a6b2-a5b96d559a14",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-ce374ab5-9a4d-489e-afa0-c99cc7340b07",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-3594c3a5-898f-4193-bbd7-c252b06e5430",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-53930111-b7d7-44b9-9f67-6617b885eb36",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-32eb5c57-b9bf-48ae-a2ea-1ae45c6f41a4",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-5f0ba8ec-bd25-4101-b44d-aa85873b08b9",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-cd9d9e72-8bfd-4977-a95a-46fda7ff28a7",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-f9a0a7e3-ee62-4769-83a1-bfb9325fa5ed",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-74d0e6a4-2cd1-4dec-9b3f-b31a5e705de4",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-b94ef757-b54a-4a3e-901f-27317e473da2",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-ea7b5f52-e5f7-4408-939d-6446f39d4922",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-e456273b-c996-4523-9673-d4bf61b7d367",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-f62847ce-c60a-44a9-ad24-43cdecaace3b",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-41187aba-ce57-4200-8a37-32da2583ad57",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-d7b5a0d3-a54e-444c-b5c9-2638293d8c40",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-9f976d67-55f4-4618-abbf-f365793e17a0",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-842facb5-304e-47b6-9973-795cc3e4e59f",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-b889befc-e424-4231-af70-fb2bda95675c",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-10eb0a5a-39da-49cc-8342-ac9d3824779f",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-b9a2a30f-d342-42c5-bda8-0ad71f694b8a",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-6760d3d6-6e36-4cd0-8d81-9645838cf5f2",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-4f2eeaeb-61b2-414a-9e2b-3b8013a378d4",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-9ce1d4ec-e57a-469e-be0c-a63d9e00f5ab",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-b068c22e-c1c1-4ee0-bc16-13e67ad3301d",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-ec05699d-a6da-4de9-943d-d5e8f7e6d446",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-7ac20fe8-f863-40ed-916c-270302e2d59b",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-305c79d4-5251-48d2-8e77-8fc5f4e33c5e",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-f339c3a7-407c-491e-a675-5c635327001b",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-380daa8e-b4c0-41cf-8105-a816267c8f5b",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-be58aed1-de64-40b9-beee-c54d660580a4",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-5d1709a3-f64a-421e-950f-6d5a0671edb8",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-8b893f6f-bcbb-44e8-a819-28a1283ff112",
            "email": "<EMAIL>",
        },
        {
            "ref": "RC-b1c6118c-32b0-422c-8107-e7a0f4025c5b",
            "email": "<EMAIL>",
        },
    ]

    response_data = {}
    for i in data:
        response_data[i.get("email")] = []

    for e in data:
        response_data[e.get("email")].append(e.get("ref"))

    print(response_data)


class AgentTransactionRecord:
    @classmethod
    def game_play_record(cls, agent_id):
        agent = Agent.objects.get(id=agent_id)
        payload = {
            "transaction_from": "GAME_PLAY",
            "game_type": "WYSE_CASH",
            "game_play_id": "poijhbt02",
        }
        deduct_wallet_balance = UserWallet.deduct_wallet(
            user=agent,
            amount=400,
            channel="POS",
            transaction_id="09876",
            user_wallet_type="GAME_PLAY_WALLET",
            **payload,
        )

        print("deduct_wallet_balance", deduct_wallet_balance)

    @classmethod
    def pre_funding_record(cls, agent_id):
        agent = Agent.objects.get(id=agent_id)
        payload = {
            "transaction_from": "PRE_FUNDING",
        }
        deduct_wallet_balance = UserWallet.fund_wallet(
            user=agent,
            amount=20000,
            channel="POS",
            transaction_id="09876------JMKN",
            user_wallet_type="GAME_PLAY_WALLET",
            **payload,
        )

        print("deduct_wallet_balance", deduct_wallet_balance)

    @classmethod
    def debit_winnings_record(cls, agent_id):
        agent = Agent.objects.get(id=agent_id)
        payload = {
            "transaction_from": "WINNINGS_WITHDRAW",
            "game_type": "WYSE_CASH",
            "game_play_id": "poijhbt02",
        }

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent.phone,
            amount=200,
            channel="POS/MOBILE",
            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="DEBIT",
        )

        # wallet_payload = {
        #     "transaction_from": "BONUS",
        # }

        deduct_res = UserWallet.deduct_wallet(
            user=agent,
            amount=200,
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **payload,
        )

        # deduct_wallet_balance = UserWallet.deduct_wallet(
        #     user=agent,
        #     amount=400,
        #     channel="POS",
        #     transaction_id="09876",
        #     user_wallet_type="WINNINGS_WALLET",
        #     **payload,
        # )

        print("deduct_wallet_balance", deduct_res)

    @classmethod
    def debit_user_winnings_record(cls, agent_id):
        user_wallet = UserWallet.objects.first()
        payload = {
            "transaction_from": "WINNINGS_WITHDRAW",
            "game_type": "WYSE_CASH",
            "game_play_id": "poijhbt02",
        }

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=user_wallet.user.phone_number,
            amount=200,
            channel="WEB",
            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="DEBIT",
        )

        # wallet_payload = {
        #     "transaction_from": "BONUS",
        # }

        deduct_res = UserWallet.deduct_wallet(
            user=user_wallet.user,
            amount=200,
            channel="WEB",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **payload,
        )

        # deduct_wallet_balance = UserWallet.deduct_wallet(
        #     user=agent,
        #     amount=400,
        #     channel="POS",
        #     transaction_id="09876",
        #     user_wallet_type="WINNINGS_WALLET",
        #     **payload,
        # )

        print("deduct_wallet_balance", deduct_res)

    @classmethod
    def debit_game_play_record(cls):
        user_wallet = UserWallet.objects.first()

        payload = {
            "transaction_from": "WINNINGS_WITHDRAW",
            "game_type": "WYSE_CASH",
            "game_play_id": "poijhbt02",
        }

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=user_wallet.user.phone_number,
            amount=200,
            channel="WEB",
            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="DEBIT",
        )

        # wallet_payload = {
        #     "transaction_from": "BONUS",
        # }

        deduct_res = UserWallet.deduct_wallet(
            user=user_wallet.user,
            amount=200,
            channel="WEB",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="GAME_PLAY_WALLET",
            **payload,
        )

        # deduct_wallet_balance = UserWallet.deduct_wallet(
        #     user=agent,
        #     amount=400,
        #     channel="POS",
        #     transaction_id="09876",
        #     user_wallet_type="WINNINGS_WALLET",
        #     **payload,
        # )

        print("deduct_wallet_balance", deduct_res)

    @classmethod
    def add_winning(cls, agent_id):
        agent = Agent.objects.get(id=agent_id)
        payload = {
            "transaction_from": "WINNINGS_WITHDRAW",
            "game_type": "WYSE_CASH",
            "game_play_id": "poijhbt02",
        }

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent.phone,
            amount=1000,
            channel="POS/MOBILE",
            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="CREDIT",
        )

        add_res = UserWallet.fund_wallet(
            user=agent,
            amount=1000,
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **payload,
        )

        print("add_res", add_res)

    @classmethod
    def credi_winnings_record(cls, agent_id):
        agent = Agent.objects.get(id=agent_id)
        payload = {
            "transaction_from": "WINNINGS_WITHDRAW",
            "game_type": "WYSE_CASH",
            "game_play_id": "poijhbt02",
        }

        DebitCreditRecord.create_record(
            phone_number=agent.phone,
            amount=200,
            channel="POS/MOBILE",
            reference=f"bonus-{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="CREDIT",
        )

        # wallet_payload = {
        #     "transaction_from": "BONUS",
        # }

        deduct_wallet_balance = UserWallet.deduct_wallet(
            user=agent,
            amount=400,
            channel="POS",
            transaction_id="09876",
            user_wallet_type="WINNINGS_WALLET",
            **payload,
        )

        print("deduct_wallet_balance", deduct_wallet_balance)

    @classmethod
    def credi_user_winnings_record(cls):
        user_wallet = UserWallet.objects.first()
        # payload = {
        #     "transaction_from": "WINNINGS_WITHDRAW",
        #     "game_type": "WYSE_CASH",
        #     "game_play_id": "poijhbt02",
        # }

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=user_wallet.user.phone_number,
            amount=200,
            channel="WEB",
            reference=f"bonus-{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="CREDIT",
        )

        wallet_payload = {
            "transaction_from": "BONUS",
        }

        credit_user_win = UserWallet.fund_wallet(
            user=user_wallet.user,
            amount=200,
            channel="WEB",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **wallet_payload,
        )

        print("credit_user_win", credit_user_win)

    @classmethod
    def funding_record(cls, agent_id):
        agent = Agent.objects.get(id=agent_id)
        payload = {
            "transaction_from": "REMITTANCE_EXCESS",
        }

        deduct_wallet_balance = UserWallet.fund_wallet(
            user=agent,
            amount=800,
            channel="POS",
            transaction_id="09876------JMKN",
            user_wallet_type="GAME_PLAY_WALLET",
            **payload,
        )

        print("deduct_wallet_balance", deduct_wallet_balance)

    @classmethod
    def commission_reward(cls, agent_id):
        agent = Agent.objects.get(id=agent_id)
        payload = {
            "transaction_from": "COMMISSION_REWARD",
        }

        deduct_wallet_balance = UserWallet.fund_wallet(
            user=agent,
            amount=800,
            channel="POS",
            transaction_id="09876------JMKN",
            user_wallet_type="COMMISSION_WALLET",
            **payload,
        )

        print("deduct_wallet_balance", deduct_wallet_balance)


def debit_agent_play_wallet():
    agent_emails = [
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL> ",
        "<EMAIL>",
    ]

    # amount =

    agent_not_found = []

    for email in agent_emails:
        agent_wallet = AgentWallet.objects.filter(agent__email=email).last()
        if agent_wallet:
            pass

        else:
            agent_not_found.append(email)


def update_remove_block_users_from_withdrawing():
    blocked_users = BlackListed.objects.all().last()

    email_of_fail_to_update = []
    phone_of_fail_to_update = []

    if blocked_users:
        blocked_from_withdrawing = blocked_users.withdrawal
        emails = blocked_from_withdrawing.get("emails")
        phones = blocked_from_withdrawing.get("phones")

        for email in emails:
            print(f"email: {email}")
            user_profile = UserProfile.objects.filter(email=email).last()
            if user_profile:
                user_wallets = UserWallet.objects.filter(user=user_profile)
                if user_wallets.exists():
                    for user_wallet in user_wallets:
                        if user_wallet.withdrawable_available_balance > 0:
                            debit_credit_record = DebitCreditRecord.create_record(
                                phone_number=user_wallet.user.phone_number,
                                amount=float(user_wallet.withdrawable_available_balance),
                                channel="WEB",
                                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                                transaction_type="DEBIT",
                            )

                            wallet_payload = {
                                "transaction_from": "SPECIAL_CASE",
                            }

                            UserWallet.deduct_wallet(
                                user=user_wallet.user,
                                amount=float(user_wallet.withdrawable_available_balance),
                                channel="WEB",
                                transaction_id=debit_credit_record.reference,
                                user_wallet_type="WINNINGS_WALLET",
                                **wallet_payload,
                            )

            agent_profile = Agent.objects.filter(email=email).last()
            if agent_profile:
                agent_wallet = AgentWallet.objects.filter(agent=agent_profile).first()
                if agent_wallet:
                    if agent_wallet.winnings_bal > 0:
                        debit_credit_record = DebitCreditRecord.create_record(
                            phone_number=agent_profile.phone,
                            amount=float(agent_wallet.winnings_bal),
                            channel="POS/MOBILE",
                            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                            transaction_type="DEBIT",
                        )

                        wallet_payload = {
                            "transaction_from": "SPECIAL_CASE",
                        }

                        UserWallet.deduct_wallet(
                            user=agent_wallet.agent,
                            amount=float(agent_wallet.winnings_bal),
                            channel="POS",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="WINNINGS_WALLET",
                            **wallet_payload,
                        )

                        if agent_profile.phone in phone_of_fail_to_update:
                            phone_of_fail_to_update.remove(agent_profile.phone)

            else:
                email_of_fail_to_update.append(email)

        for phone in phones:
            print(f"phone: {phone}")
            user_profile = UserProfile.objects.filter(phone_number=phone).last()
            if user_profile:
                user_wallets = UserWallet.objects.filter(user=user_profile)
                if user_wallets.exists():
                    for user_wallet in user_wallets:
                        if user_wallet.withdrawable_available_balance > 0:
                            debit_credit_record = DebitCreditRecord.create_record(
                                phone_number=user_wallet.user.phone_number,
                                amount=float(user_wallet.withdrawable_available_balance),
                                channel="WEB",
                                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                                transaction_type="DEBIT",
                            )

                            wallet_payload = {
                                "transaction_from": "SPECIAL_CASE",
                            }

                            UserWallet.deduct_wallet(
                                user=user_wallet.user,
                                amount=float(user_wallet.withdrawable_available_balance),
                                channel="WEB",
                                transaction_id=debit_credit_record.reference,
                                user_wallet_type="WINNINGS_WALLET",
                                **wallet_payload,
                            )

            agent_profile = Agent.objects.filter(phone=phone).last()
            if agent_profile:
                agent_wallet = AgentWallet.objects.filter(agent=agent_profile).first()
                if agent_wallet:
                    if agent_wallet.winnings_bal > 0:
                        debit_credit_record = DebitCreditRecord.create_record(
                            phone_number=agent_profile.phone,
                            amount=float(agent_wallet.winnings_bal),
                            channel="POS/MOBILE",
                            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                            transaction_type="DEBIT",
                        )

                        wallet_payload = {
                            "transaction_from": "SPECIAL_CASE",
                        }

                        UserWallet.deduct_wallet(
                            user=agent_wallet.agent,
                            amount=float(agent_wallet.winnings_bal),
                            channel="POS",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="WINNINGS_WALLET",
                            **wallet_payload,
                        )

                        if agent_profile.phone in phone_of_fail_to_update:
                            phone_of_fail_to_update.remove(agent_profile.phone)

            # else:
            #     email_of_fail_to_update.append(phone)

        # if len(email_of_fail_to_update) > 0:
        #     data = {
        #         "emails": email_of_fail_to_update,
        #         "phones": phone_of_fail_to_update,
        #     }
        #     blocked_users.withdrawal = data
        #     blocked_users.save()


def manually_charge_agent_on_agency_banking(agent_id):
    agent = Agent.objects.get(id=agent_id)

    amount_to_charge = 15000

    payload = {
        "service_name": "LOTTO_PLAY",
        "user_id": agent.user_id,
        "narration": "MANUAL_DEDUCTION",
        "total_amount": amount_to_charge,
        "service_comm": amount_to_charge,
        "agent_comm": 0,
    }

    cp_payload = payload
    cp_payload["transaction_pin"] = 0000
    cp_payload["unique_reference"] = FailedRemittanceAgencyWalletCharge().generate_transaction_ref()

    pos_agent_helper = PosAgentHelper(
        agent_instance=agent,
        amount=0,
        pin=0,
    )

    deduction_response = pos_agent_helper.charge_defaulted_remittance(**cp_payload)

    if isinstance(deduction_response, dict):
        if deduction_response.get("error"):
            error_code = deduction_response.get("error")
            success_error_message = deduction_response.get("message")

            if "1621" == error_code or "does not have" in success_error_message:
                agent_balance_response = deduction_response.get("user_balance")

                payload = {
                    "service_name": "LOTTO_PLAY",
                    "user_id": agent.user_id,
                    "narration": "REMITTANCE_DEDUCTION",
                    "total_amount": agent_balance_response,
                    "service_comm": agent_balance_response,
                    "agent_comm": 0,
                }

                copy_payload = payload
                copy_payload["transaction_pin"] = 0000
                copy_payload["unique_reference"] = FailedRemittanceAgencyWalletCharge().generate_transaction_ref()

                pos_agent_helper = PosAgentHelper(
                    agent_instance=agent,
                    amount=0,
                    pin=0,
                )

                deduction_response = pos_agent_helper.charge_defaulted_remittance(**copy_payload)

    return deduction_response


def calculate_agent_monthly_commission_and_salary(agent_id):
    # agent = Agent.objects.get(id=agent_id)

    # total_amount_for_agents = {}
    # total_amount_to_be_paid_to_agents = {}
    # percentages_to_reward = {}
    # total_sales_for_agents = {}

    winwise_agents = Agent.objects.filter(is_winwise_staff_agent=True, agent_type="LOTTO_AGENT")

    total_amount_for_agents = {}
    total_amount_to_be_paid_to_agents = {}
    percentages_to_reward = {}
    total_sales_for_agents = {}

    data_header = [
        "agent_id",
        "agent_name",
        "agent_email",
        "agent_phone",
        "total_commission",
        "amount_to_be_paid",
        "total_sales",
    ]

    csv_data = []

    if winwise_agents:
        for agent in winwise_agents:
            year_od_last_month, month_of_last_month = get_last_month()

            lotto_ticket_table = LottoTicket.objects.filter(
                Q(agent_profile=agent),
                Q(date__year=year_od_last_month) & Q(date__month=month_of_last_month) & Q(date__day__gt=28),
                Q(paid=True),
            )
            lottery_model = LotteryModel.objects.filter(
                Q(agent_profile=agent),
                Q(date__year=year_od_last_month) & Q(date__month=month_of_last_month) & Q(date__day__gt=28),
                Q(paid=True),
            )

            if lotto_ticket_table.exists():
                unique_commission_percentage = list(lotto_ticket_table.values_list("commission_per", flat=True).distinct())

                agent_total_sales = lotto_ticket_table.aggregate(Sum("amount_paid"))["amount_paid__sum"]

                if agent_total_sales is None:
                    agent_total_sales = 0

                if total_sales_for_agents.get(agent.id) is None:
                    total_sales_for_agents[agent.id] = agent_total_sales
                else:
                    total_sales_for_agents[agent.id] += agent_total_sales

                if len(unique_commission_percentage) > 0:
                    for perc in unique_commission_percentage:
                        if perc == 0.0:
                            continue

                        ticket_sold_by_agent = lotto_ticket_table.filter(commission_per=perc).aggregate(Sum("commission_value"))[
                            "commission_value__sum"
                        ]

                        percentage_to_reward = (9 / perc) / 100

                        if ticket_sold_by_agent is None:
                            ticket_sold_by_agent = 0

                        if total_amount_for_agents.get(agent.id) is None:
                            total_amount_for_agents[agent.id] = ticket_sold_by_agent
                        else:
                            total_amount_for_agents[agent.id] += ticket_sold_by_agent

                        if total_amount_to_be_paid_to_agents.get(agent.id) is None:
                            total_amount_to_be_paid_to_agents[agent.id] = ticket_sold_by_agent * percentage_to_reward
                        else:
                            total_amount_to_be_paid_to_agents[agent.id] += ticket_sold_by_agent * percentage_to_reward

                        if percentages_to_reward.get(agent.id) is None:
                            percentages_to_reward[agent.id] = f"{percentage_to_reward},"
                        else:
                            percentages_to_reward[agent.id] += f"{percentage_to_reward},"

            if lottery_model.exists():
                unique_commission_percentage = list(lottery_model.values_list("commission_per", flat=True).distinct())

                agent_total_sales = lottery_model.aggregate(Sum("amount_paid"))["amount_paid__sum"]

                if agent_total_sales is None:
                    agent_total_sales = 0

                if total_sales_for_agents.get(agent.id) is None:
                    total_sales_for_agents[agent.id] = agent_total_sales
                else:
                    total_sales_for_agents[agent.id] += agent_total_sales

                if len(unique_commission_percentage) > 0:
                    for perc in unique_commission_percentage:
                        if perc == 0.0:
                            continue

                        ticket_sold_by_agent = lottery_model.filter(commission_per=perc).aggregate(Sum("commission_value"))["commission_value__sum"]
                        percentage_to_reward = (9 / perc) / 100

                        if ticket_sold_by_agent is None:
                            ticket_sold_by_agent = 0

                        if total_amount_for_agents.get(agent.id) is None:
                            total_amount_for_agents[agent.id] = ticket_sold_by_agent
                        else:
                            total_amount_for_agents[agent.id] += ticket_sold_by_agent

                        if total_amount_to_be_paid_to_agents.get(agent.id) is None:
                            total_amount_to_be_paid_to_agents[agent.id] = ticket_sold_by_agent * percentage_to_reward
                        else:
                            total_amount_to_be_paid_to_agents[agent.id] += ticket_sold_by_agent * percentage_to_reward

                        if percentages_to_reward.get(agent.id) is None:
                            percentages_to_reward[agent.id] = f"{percentage_to_reward},"
                        else:
                            percentages_to_reward[agent.id] += f"{percentage_to_reward},"

    agents__ = []

    if total_amount_for_agents:
        for agent_id, amount in total_amount_for_agents.items():
            # print("total_amount_for_agents", total_amount_for_agents, "\n\n\n")

            agent = Agent.objects.get(id=agent_id)

            if agent.id in agents__:
                print("agent_id", agent.id, "agent_id in agents__", agent.id in agents__)
                break

            loop_data = [
                agent_id,
                agent.full_name,
                agent.email,
                agent.phone,
                amount,
                total_amount_to_be_paid_to_agents.get(agent.id),
                total_sales_for_agents.get(agent.id),
            ]

            csv_data.append(loop_data)

            # break

    if csv_data:
        with open("agent_commission_salary.csv", "w") as csv_file:
            csv_writer = csv.writer(csv_file)
            csv_writer.writerow(data_header)
            csv_writer.writerows(csv_data)

            print("agent_commission created")

    print("END. DONE")

    celery_update_winwise_agent_salary_record_monthly()

    celery_update_winwise_agent_salary_record()


def manually_update_agent_winnings():
    # datas = {

    #     "ia03288": "9,31,25,15",
    #     "35a34y7": "10,36,15,15",
    # }

    datas = [
        {
            "game_id": "ia03288",
            "ticket": "9,31,25,15",
            "match_win_type": 2,
        },
        {
            "game_id": "35a34y7",
            "ticket": "10,36,15,15",
            "match_win_type": 2,
        },
    ]

    data = [
        {
            "game_id": "ia03288",
            "ticket": "9,31,25,15",
            "match_win_type": 2,
        },
        {
            "game_id": "35a34y7",
            "ticket": "10,36,15,15",
            "match_win_type": 2,
        }
    ]

    const_obj = ConstantVariable.objects.all().last()

    for data in datas:
        print("game_id", data)
        lotto_ticket = LottoTicket.objects.filter(game_play_id=data.get("game_id"), paid=True)
        if lotto_ticket.exists():
            won_ticket = lotto_ticket.filter(ticket=data.get("ticket"))

            if won_ticket.exists():
                won_ticket_instance = won_ticket.first()

                _draw_batch_id = generate_game_play_id()

                s4l_win_factor_constant = const_obj.s4l_win_factor_constant or "100,100"

                s4lmin, s4lmax = s4l_win_factor_constant.split(",")

                factor = int(random.choice(range(int(s4lmin), int(s4lmax) + 1))) / 100

                prices = {
                    1: 5000.00 / 1 * factor,
                    2: 15000.00 / 2 * factor,
                    3: 50000.00 / 3 * factor,
                    4: 150000.00 / 4 * factor,
                    5: 250000.00 / 5 * factor,
                    6: 500000.00 / 6 * factor,
                    7: 750000.00 / 7 * factor,
                    8: 900000.00 / 8 * factor,
                    9: 1250000.00 / 9 * factor,
                    10: 1500000.00 / 10 * factor,
                }

                amount_won = LottoTicket.salary_for_life_win_per_line(
                    data.get("match_win_type"),
                    won_ticket_instance.number_of_ticket,
                    prices,
                )

                ticket = str(won_ticket_instance.ticket).split(",")

                match_win_type = data.get("match_win_type")

                win_type = "PERM_4"

                if match_win_type == 4:
                    win_type = "PERM_4"
                elif match_win_type == 3:
                    win_type = "PERM_3"
                elif match_win_type == 2:
                    win_type = "PERM_2"

                LottoWinners.create_lotto_winner_obj(
                    lottery=won_ticket_instance,
                    batch=won_ticket_instance.batch,
                    phone_number=won_ticket_instance.user_profile.phone_number,
                    ticket=ticket,
                    win_type="ORDINARY_WINNER",
                    match_type=win_type,
                    lotto_type="SALARY_FOR_LIFE",
                    game_play_id=won_ticket_instance.game_play_id,
                    stake_amount=won_ticket_instance.stake_amount,
                    earning=amount_won,
                    channel_played_from=won_ticket_instance.channel,
                    run_batch_id=_draw_batch_id,
                )

                print("WON TICKET ADDED")
        else:
            print("NO TICKET FOUND")


def move_agent_money_to_float(agent: Agent, amount: float):
    payload = {
        "service_name": "LOTTO_PLAY",
        "user_id": agent.user_id,
        "narration": "LOTTO COMMISSION DEDUCTION",
        "total_amount": amount,
        "service_comm": amount,
        "agent_comm": 0,
    }

    trans_ref = FailedRemittanceAgencyWalletCharge().generate_transaction_ref()
    print("trans_ref", trans_ref)

    cp_payload = payload
    cp_payload["transaction_pin"] = 0000
    cp_payload["unique_reference"] = trans_ref

    pos_agent_helper = PosAgentHelper(
        agent_instance=agent,
        amount=0,
        pin=0,
    )

    deduction_response = pos_agent_helper.charge_defaulted_remittance(**cp_payload)

    print("deduction_response", deduction_response, "\n\n")


def jsakjdkd():
    try:
        super_agen_wallet = LottoSuperAgentWallet.objects.get(super_agent__email="<EMAIL>")
    except LottoSuperAgentWallet.DoesNotExist:
        super_agen_wallet = None

    agents = Agent.objects.filter(email="<EMAIL>").last()
    if super_agen_wallet is not None:
        pos_agent_helper = PosAgentHelper(
            agent_instance=agents,
            amount=super_agen_wallet.commission_balance,
        )
        pos_agent_helper.reward_super_agent_commission(
            customer_reference=super_agen_wallet.super_agent.user_uuid,
        )


def update_agents_winnins_to_wallet():
    agent_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

    now_utc = datetime.now(pytz.utc)

    tz = pytz.timezone("Africa/Lagos")
    now_tz = now_utc.astimezone(tz)

    for email in agent_emails:
        check_payout_record = PosLotteryWinners.objects.filter(
            agent__email=email,
            is_win_claimed=False,
            withdrawl_initiated=False,
            payout_successful=False,
            payout_verified=False,
            date_created__year=now_tz.year,
        ).aggregate(Sum("amount_won"))["amount_won__sum"]

        if check_payout_record is None:
            check_payout_record = 0

        agent_wallet = AgentWallet.objects.filter(agent__email=email).last()

        agent_wallet.winnings_bal = check_payout_record
        agent_wallet.save()



def agent_monthly_sales_and_winning_data():
    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    start_of_month = TODAY.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    queryset = AgentWalletTransaction.objects.filter(
        date_created__gte=start_of_month
    )
    agent_that_has_perform_transactions_this_month = list(queryset.distinct("agent_phone_number").values_list("agent_phone_number", flat=True))

    data_header = [
        "agent phone number",
        "agent name",
        "total_sales_value",
        "instant cashout sales count",
        "instant cashout sales value",
        "salary for life sales count",
        "salary for life sales value",
        "wyse cash sales count",
        "wyse cash sales value",
        "quika sales count",
        "quika sales value",
        "banker sales count",
        "banker sales value",
        "kenya game sales count",
        "kenya game sales value",
        "ghana game sales count",
        "ghana game sales value",
        "total winnings",
        "total withdrawals",
    ]

    data = []

    for agent_phone in agent_that_has_perform_transactions_this_month:

        try:
            agent = Agent.objects.get(phone=agent_phone)
        except Agent.DoesNotExist:
            continue

        winnings_queryset = queryset.filter(
            agent_phone_number=agent_phone,
            transaction_from__in=["GLOBAL_WINNINGS", "WINNINGS"],
        )

        last_transaction_instance = queryset.last()

        total_winnings = winnings_queryset.aggregate(Sum("amount"))["amount__sum"] or 0

        game_play_queryset = queryset.filter(
            agent_phone_number=agent_phone,
            transaction_from__in=["GAME_PLAY"],
        )

        instant_cashout_sales = game_play_queryset.filter(
            game_type="INSTANT_CASHOUT",
        )

        salary_for_life_sales = game_play_queryset.filter(
            game_type="SALARY_FOR_LIFE",
        )

        wyse_cash_sales = game_play_queryset.filter(
            game_type="WYSE_CASH",
        )

        quicka_sales = game_play_queryset.filter(
            game_type="QUIKA",
        )

        banker_sales = game_play_queryset.filter(
            game_type="BANKER",
        )

        kenya_game_sales = game_play_queryset.filter(
            game_type="KENYA_LOTTO",
        )

        ghana_game_sales = game_play_queryset.filter(
            game_type="GHANA_LOTTO",
        )

        total_sales_value = game_play_queryset.aggregate(Sum("amount"))["amount__sum"] or 0

        instant_cashout_sales_count = instant_cashout_sales.count()
        instant_cashout_sales_value = instant_cashout_sales.aggregate(Sum("amount"))["amount__sum"] or 0

        salary_for_life_sales_count = salary_for_life_sales.count()
        salary_for_life_sales_value = salary_for_life_sales.aggregate(Sum("amount"))["amount__sum"] or 0

        wyse_cash_sales_count = wyse_cash_sales.count()
        wyse_cash_sales_value = wyse_cash_sales.aggregate(Sum("amount"))["amount__sum"] or 0

        quicka_sales_count = quicka_sales.count()
        quicka_sales_value = quicka_sales.aggregate(Sum("amount"))["amount__sum"] or 0

        banker_sales_count = banker_sales.count()
        banker_sales_value = banker_sales.aggregate(Sum("amount"))["amount__sum"] or 0

        kenya_game_sales_count = kenya_game_sales.count()
        kenya_game_sales_value = kenya_game_sales.aggregate(Sum("amount"))["amount__sum"] or 0

        ghana_game_sales_count = ghana_game_sales.count()
        ghana_game_sales_value = ghana_game_sales.aggregate(Sum("amount"))["amount__sum"] or 0

        withdrawal_queryset = PayoutTransactionTable.objects.filter(
            phone = agent_phone,
            date_added__gte=start_of_month,
            disbursed=True,
            source = "BUDDY"
        )

        total_withdrawals = withdrawal_queryset.aggregate(Sum("amount"))["amount__sum"] or 0

        loop_data = [
            agent_phone,
            agent.name,
            total_sales_value,
            instant_cashout_sales_count,
            instant_cashout_sales_value,
            salary_for_life_sales_count,
            salary_for_life_sales_value,
            wyse_cash_sales_count,
            wyse_cash_sales_value,
            quicka_sales_count,
            quicka_sales_value,
            banker_sales_count,
            banker_sales_value,
            kenya_game_sales_count,
            kenya_game_sales_value,
            ghana_game_sales_count,
            ghana_game_sales_value,
            total_winnings,
            total_withdrawals,
        ]

        data.append(loop_data)

    if data:
        with open("static/agent_monthly_sales_summary.csv", "w", encoding="UTF-8", newline="") as f:
            writer = csv.writer(f)
            writer.writerow(data_header)
            writer.writerows(data)
        




from datetime import datetime, timedelta

def get_week_ranges(start_month, end_month, year=2025):
    """
    Generate all week ranges (Monday to Sunday) from start_month to end_month inclusive.
    Returns list of strings in format 'YYYY-MM-DD to YYYY-MM-DD'
    """
    # Create start date (first day of start month)
    start_date = datetime(year, start_month, 1)
    
    # Find the first Monday on or after start date
    days_until_monday = (7 - start_date.weekday()) % 7
    if days_until_monday == 0:
        first_monday = start_date  # Start date is already a Monday
    else:
        first_monday = start_date + timedelta(days=days_until_monday)
    
    # Create end date (last day of end month)
    if end_month == 12:
        end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
    else:
        end_date = datetime(year, end_month + 1, 1) - timedelta(days=1)
    
    # Generate all week ranges
    week_ranges = []
    week_start = first_monday
    
    while week_start <= end_date:
        week_end = week_start + timedelta(days=6)  # Sunday is 6 days after Monday
        week_range_str = f"{week_start.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}"
        week_ranges.append(week_range_str)
        week_start += timedelta(days=7)  # Move to next Monday
    
    return week_ranges



def get_week_info(date):
    """
    Returns the week number and week range (Monday to Sunday) for the given date.
    """
    # Calculate the difference between the date and the beginning of the week (Monday)
    days_to_beginning = date.weekday()  # 0 for Monday, 1 for Tuesday, etc.
    
    # Calculate the start date (Monday) of the week
    week_start = date - timedelta(days=days_to_beginning)
    
    # Calculate the end date (Sunday) of the week
    week_end = week_start + timedelta(days=6)
    
    # Get the ISO week number (week 1 is the first week with at least 4 days in January)
    week_number = date.isocalendar()[1]
    
    # Format the result as a string
    week_range_str = f"{week_start.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}"
    
    return week_number, week_range_str





def agent_sales_metrics():
    week_ranges = get_week_ranges(1, 5)

    # Print the results
    # print(f"Found {len(week_ranges)} full week ranges from January to April:")
    # for i, week_range in enumerate(week_ranges, 1):
    #     print(f"Week {i}: {week_range}")
    
    print("\n\n")

    current_date = datetime.now()
    week_num, week_range = get_week_info(current_date)

    print(f""" 
        week_num: {week_num}
        week_range: {week_range}
    
        """)


    date_string = "2025-01-06"
    date_object = datetime.strptime(date_string, "%Y-%m-%d").date()

    week_num, week_range = get_week_info(date_object)

    print(f""" 
        week_num 2: {week_num}
        week_range 2: {week_range}
    
        """)
    

    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    week_num, week_range = get_week_info(TODAY)

    parsed_week_ranges = []
    for week_range in week_ranges:
        dates_string = week_range.split(" to ")
        start_date = datetime.strptime(dates_string[0], "%Y-%m-%d").date()
        end_date = datetime.strptime(dates_string[1], "%Y-%m-%d").date()
        week_num, formatted_week_range = get_week_info(start_date)
        parsed_week_ranges.append({
            'start_date': start_date,
            'end_date': end_date,
            'week_num': week_num,
            'week_range': formatted_week_range,
            'week_date_range': f"Week {week_num}: {formatted_week_range}"
        })

    # Get all agents in one query
    agents = Agent.objects.filter(terminal_retrieved=False, terminal_id__isnull=False)
    agent_phones = [agent.phone for agent in agents]

    # Batch process in chunks to avoid memory issues
    BATCH_SIZE = 500
    activity_records = []

    for i in range(0, len(agents), BATCH_SIZE):
        batch_agents = agents[i:i+BATCH_SIZE]
        batch_phones = [agent.phone for agent in batch_agents]
        
        for week_data in parsed_week_ranges:
            start_date = week_data['start_date']
            end_date = week_data['end_date']
            
            # Get all sales data for this batch of agents in one query
            sales_data = AgentWalletTransaction.objects.filter(
                agent_phone_number__in=batch_phones,
                transaction_from="GAME_PLAY",
                date_created__date__range=[start_date, end_date]
            ).values('agent_phone_number').annotate(total_sales=Sum('amount'))
            
            # Convert to dictionary for fast lookup
            sales_dict = {item['agent_phone_number']: item['total_sales'] for item in sales_data}
            
            # Get all winnings data in one query
            winnings_data = PosLotteryWinners.objects.filter(
                agent__phone__in=batch_phones,
                date_created__date__range=[start_date, end_date]
            ).values('agent__phone').annotate(total_winnings=Sum('amount_won'))
            
            # Convert to dictionary for fast lookup
            winnings_dict = {item['agent__phone']: item['total_winnings'] for item in winnings_data}
            
            # Create activity records without saving yet
            for agent in batch_agents:
                agent_sales = sales_dict.get(agent.phone, 0) or 0
                
                if agent_sales < 1:
                    continue
                    
                agent_winnings = winnings_dict.get(agent.phone, 0) or 0
                avg_sales = round(agent_sales / 7, 2)
                
                if avg_sales < 5000:
                    activity_status = enums.AgentActivityStatus.INACTIVE
                elif 5000 < avg_sales <= 10000:
                    activity_status = enums.AgentActivityStatus.UNDER_PERFORMING
                elif 10000 < avg_sales < 30000:
                    activity_status = enums.AgentActivityStatus.PARTIALLY_ACTIVE
                else:  # avg_sales >= 30000
                    activity_status = enums.AgentActivityStatus.ACTIVE
                    
                activity_records.append(
                    LottoAgentSalesActivity(
                        agent_name=f"{agent.first_name} {agent.last_name}",
                        agent_email=agent.email,
                        agent_phone_number=agent.phone,
                        agent_terminal_id=agent.terminal_id,
                        sales_for_the_week=agent_sales,
                        average_sales=avg_sales,
                        winnings=agent_winnings,
                        week_date_range=week_data['week_date_range'],
                        activity_status=activity_status
                    )
                )
                
                # Bulk create in batches to avoid memory issues
                if len(activity_records) >= 1000:
                    LottoAgentSalesActivity.objects.bulk_create(activity_records)
                    activity_records = []

        
    # Create any remaining records
    if activity_records:
        LottoAgentSalesActivity.objects.bulk_create(activity_records)




def update_game_daily_activties():
    games_types = [
            "SALARY_FOR_LIFE",
            "INSTANT_CASHOUT",
            "WYSE_CASH",
            "VIRTUAL_SOCCER",
            "SOCCER_CASH",
            "BANKER",
            "QUIKA",
            "GHANA_LOTTO",
            "NIGERIA_LOTTO",
            "KENYA_LOTTO",
            "KENYA_30_LOTTO",
            "K_NOW"
    ]

    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

    for game_type in games_types:
        winnings_qs = PosLotteryWinners.objects.filter(
            lottery_type=game_type,
            date_created__date=TODAY.date(),
        )

        lotto_agent_winnings = winnings_qs.filter(agent__terminal_id__isnull = False)
        other_agent_winnings = winnings_qs.filter(agent__terminal_id__isnull = True)

        total_winnings = winnings_qs.aggregate(Sum("amount_won"))["amount_won__sum"] or 0
        lotto_agent_total_winnings = lotto_agent_winnings.aggregate(Sum("amount_won"))["amount_won__sum"] or 0
        other_agent_total_winnings = other_agent_winnings.aggregate(Sum("amount_won"))["amount_won__sum"] or 0

        if game_type in  ["GHANA_LOTTO", "NIGERIA_LOTTO", "KENYA_LOTTO", "KENYA_30_LOTTO", "K_NOW"]:
            lotto_agent_total_winnings = total_winnings
            other_agent_total_winnings = 0


        total_sales = 0
        lotto_agent_total_sales = 0
        other_agent_total_sales = 0

        if game_type not in  ["GHANA_LOTTO", "NIGERIA_LOTTO", "KENYA_LOTTO", "KENYA_30_LOTTO", "K_NOW"]:
            sales_queryset = AgentWalletTransaction.objects.filter(
                game_type=game_type,
                transaction_from="GAME_PLAY",
                date_created__date=TODAY.date(),
            )

            total_sales = sales_queryset.aggregate(Sum("amount"))["amount__sum"] or 0
            lotto_agent_sales_queryset = sales_queryset.filter(agent_wallet__agent__terminal_id__isnull = False)
            other_agent_sales_queryset = sales_queryset.filter(agent_wallet__agent__terminal_id__isnull = True)

            lotto_agent_total_sales = lotto_agent_sales_queryset.aggregate(Sum("amount"))["amount__sum"] or 0
            other_agent_total_sales = other_agent_sales_queryset.aggregate(Sum("amount"))["amount__sum"] or 0
        else:
            sales_queryset = AfricaLotto.objects.filter(
                game_type=game_type,
                created_at__date=TODAY.date(),
            )

            total_sales = sales_queryset.aggregate(Sum("purchase_amount"))["purchase_amount__sum"] or 0
            lotto_agent_total_sales = total_sales
        

        if total_sales < 1:
            print("NO SALES")
            continue
        try:
            game_activity_instance = GamesDailyActivities.objects.get(
                game_type = game_type, created_at__date = TODAY.date()
            )
        except GamesDailyActivities.DoesNotExist:
            game_activity_instance = GamesDailyActivities.objects.create(
                game_type = game_type, created_at = TODAY.date()
            )

        
        print(f""" 
        Game Type: {game_type}
        Total Sales: {total_sales}
        Lotto Agent Sales: {lotto_agent_total_sales}
        Other Agent Sales: {other_agent_total_sales}
        Total Winnings: {total_winnings}
        Lotto Agent Winnings: {lotto_agent_total_winnings}
        Other Agent Winnings: {other_agent_total_winnings}
        \n\n
        """)

        GamesDailyActivities.objects.filter(id = game_activity_instance.id).update(
            sales = total_sales,
            lotto_agent_sales = lotto_agent_total_sales,
            other_sales = other_agent_total_sales,
            winnings = total_winnings,
            other_winnings = other_agent_total_winnings,
            lotto_agent_winnings = lotto_agent_total_winnings,
        )
        
        # game_activity_instance.sales = total_sales
        # game_activity_instance.lotto_agent_sales = lotto_agent_total_sales
        # game_activity_instance.other_sales = other_agent_total_sales
        # game_activity_instance.winnings = total_winnings
        # game_activity_instance.lotto_agent_sales = lotto_agent_total_winnings
        # game_activity_instance.other_winnings = other_agent_total_winnings
        # game_activity_instance.save()





def update_lotto_agent_with_terminal_id():
    data = """
        phone_number	email	terminal_id	terminal_serial
        2348153614175	<EMAIL>	21048968	S6805370012286
        2347081772920	<EMAIL>	21048984	S6805370011844
        2347015928628	<EMAIL>	21048970	S6805370012283
        2348024960907	<EMAIL>	21049028	S6805370012279
        2348073752926	<EMAIL>	21048951	S6805370012266
        2349115140552	<EMAIL>	21048983	S6805370011242
        2349027273788	<EMAIL>	21044499	S6805370011431
        *************	<EMAIL>	21049023	S6805370011833
        2347025596296	<EMAIL>	21049024	S6805370011845
        2347041579232	<EMAIL>	21049058	S6805370011839
        *************	<EMAIL>	21044815	S6805370011451
        2348122787648	<EMAIL>	21049053	11230427970610
        2349137886948	<EMAIL>	21049049	S6805370012265
        2347089366488	<EMAIL>	21048788	S6805370011445
        2347062651261	<EMAIL>	21049045	S6805370012268
        2348080663340	<EMAIL>	21049044	S6805370012264
        *************	<EMAIL>	21048953	S6805370011495
        2348136464198	<EMAIL>	21049048	S6805370011836
        2347047668408	<EMAIL>	21049030	S6805370011831
        2348030691020	<EMAIL>	21049011	S6805370011850
        2347070533437	<EMAIL>	21048994	S6805370011438
        2349131630900	<EMAIL>	21049004	S6805370011832
        *************	<EMAIL>	21048993	S6805370011846
        2347081757438	<EMAIL>	21049001	S6805370011835
        2347065519640	<EMAIL>	21049032	S6805370011506
        *************	<EMAIL>	21048988	S6805370011840
        2349075314817	<EMAIL>	21048985	S6805370011838
        2348131694083	<EMAIL>	21048819	S6805370011466
        *************	<EMAIL>	21044783	S6805370011448
        2349042712003	<EMAIL>	21048742	S6805370011459
        2348101783814	<EMAIL>	21048982	S6805370011496
        2348036475209	<EMAIL>	21048974	S6805370011498
        2348037027335	<EMAIL>	21048976	S6805370011504
        *************	<EMAIL>	21049027	S6805370011223
        *************	<EMAIL>	21048954	11230427970595
        2349069137801	<EMAIL>	21048948	S6805370011492
        2348133355405	<EMAIL>	21048884	11230427970504
        2349167858790	<EMAIL>	21048881	11230427970536
        2349157455944	<EMAIL>	21048878	11230427970604
        2348115039406	<EMAIL>	21048879	11230427970573
        2348108432773	<EMAIL>	21048863	S6805370011508
        2348038771815	<EMAIL>	21048972	S6805370011363
        *************	<EMAIL>	21048811	S6805370011502
        2347058731963	<EMAIL>	21048813	S6805370011509
        2349042449199	<EMAIL>	21048814	S6805370011500
        2349122170899	<EMAIL>	21048816	S6805370011491
        2348068214653	<EMAIL>	21048817	S6805370011465
        2347086474471	<EMAIL>	21048818	S6805370011499
        2348058585018	<EMAIL>	21048807	S6805370011442
        2348037239055	<EMAIL>	21048821	S6805370011443
        2347069358167	<EMAIL>	21048793	S6805370011237
        2349163645655	<EMAIL>	21048792	S6805370011444
        2349027391199	<EMAIL>	2215SV03	S6805370011152
        2348108425953	<EMAIL>	21048789	S6805370011241
        2348051553141	<EMAIL>	21048787	S6805370011440
        2348153020701	<EMAIL>	21048786	S6805370011441
        2349115621387	<EMAIL>	21048776	S6805370011453
        2349167623499	<EMAIL>	21048775	S6805370011231
        2348085392937	<EMAIL>	21048774	S6805370011468
        2347045800878	<EMAIL>	21048773	S6805370011460
        2349124439553	<EMAIL>	21044518	S6805370011170
        2349074241449	<EMAIL>	21044754	S6805370011249
        2348167083202	<EMAIL>	21048785	S6805370011214
        2347053597005	<EMAIL>	21048784	S6805370011220
        2348068953666	<EMAIL>	21044827	S6805370011212
        2348181840210	<EMAIL>	21044483	S6805370011216
        2348089321213	<EMAIL>	21044761	S6805370011169
        *************	<EMAIL>	21044451	S6805370011219
        2348155107590	<EMAIL>	21044813	S6805370011167
        2348022406534	<EMAIL>	21044825	S6805370011218
        2348063467675	<EMAIL>	21044497	S6805370011234
        2347055344528	<EMAIL>	21044448	S6805370011233
        2349121747168	<EMAIL>	21044832	S6805370011246
        2349136127294	<EMAIL>	21044513	S6805370011232
        2348027101738	<EMAIL>	21044770	S6805370011244
        2349046887406	<EMAIL>	21044515	S6805370011238
        2348027081959	<EMAIL>	21044787	S6805370011467
        *************	<EMAIL>	21044771	S6805370011235
        2348108044316	<EMAIL>	21044840	S6805370011433
        2341387772559	<EMAIL>	21044846	S6805370011229
        2348164249383	<EMAIL>	21044769	S6805370011239
        2348052577248	<EMAIL>	21044806	S6805370011248
        2349137137782	<EMAIL>	21044833	S6805370011362
        2348071862077	<EMAIL>	21044511	S6805370011361
        2348086377844	<EMAIL>	21044753	S6805370011367
        2348128321853	<EMAIL>	21044774	S6805370011368
        *************	<EMAIL>	21041824	S6805370011454
        2349029575832	<EMAIL>	21044509	S6805370011161
        2347151870815	<EMAIL>	21041826	S6805370011450
        2348126703002	<EMAIL>	21044773	S6805370011240
        2348095267515	<EMAIL>	21048745	S6805370011250
        2349020212161	<EMAIL>	21048748	S6805370011435
        *************	<EMAIL>	21048744	S6805370011247
        2349027146890	<EMAIL>	21048736	S6805370011225
        2348062733043	<EMAIL>	21048733	S6805370011162
        2349065385738	<EMAIL>	21048730	S6805370011222
        2347034380877	<EMAIL>	21048734	S6805370011160
        2348039121616	<EMAIL>	21048732	S6805370011224
        2348151642550	<EMAIL>	21044491	S6805370011156
        2348129298506	<EMAIL>	21048743	S6805370011457
        2348124817911	<EMAIL>	21048747	S6805370011470
        2348109401799	<EMAIL>	21048718	S6805370011163
        2347010553872	<EMAIL>	21048746	S6805370011455
        *************	<EMAIL>	21048720	S6805370011159
        2348075472458	<EMAIL>	21048727	S6805370011227
        *************	<EMAIL>	21044839	S6805370011164
        *************	<EMAIL>	21048780	S6805370011510
        2349028503540	<EMAIL>	21048722	S6805370011226
        2349060114310	<EMAIL>	21048809	S6805370011462
        2348035216395	<EMAIL>	21044820	S6805370011151
        2349068526287	<EMAIL>	21048740	S6805370011230
        2349112817330	<EMAIL>	21048724	S6805370011166
        2349131899278	<EMAIL>	21048947	S6805370011165
        2349050768217	<EMAIL>	21044519	S6805370011452
        2349076609900	<EMAIL>	21048726	S6805370011157
        *************	<EMAIL>	21044450	S6805370011449
        2348165036707	<EMAIL>	21049031	S6805370011436
        2347044121835	<EMAIL>	21044828	S6805370011243
        2349079971305	<EMAIL>	21048955	11230427970596
        2347085832724	<EMAIL>	21048781	S6805370011439
        2349024675224	<EMAIL>	21048946	S6805370011507
        2342103690248	<EMAIL>	2215TG41	98220415907814
        2342175893855	<EMAIL>	21041828	S6805370011458
        2342761241125	<EMAIL>	21049005	S6805370011463
        2342506889288	<EMAIL>	2215TL47	S6805370011155
        2342054409429	<EMAIL>	2215TH82	98220415907896
        2342836863949	<EMAIL>	2215TG37	98220415907810
        *************	<EMAIL>	2215SW30	98220709980447
        2342911046916	<EMAIL>	2215SU87	98220709980304
        2342363438479	<EMAIL>	2215SW52	98220709980469
        2342916090901	<EMAIL>	2215TF64	98220709980241
        2342876473348	<EMAIL>	2215SV20	98220709980337
        2342216101982	<EMAIL>	2215SW03	98220709980420
        2342169490347	<EMAIL>	2215SU70	98220709980287
        2342491990279	<EMAIL>	2215TG46	98220415907819
        2348026386843	<EMAIL>	21048721	S6805370011168
        2348179128771	<EMAIL>	2215TN03	98220415909149
        2348076861278	<EMAIL>	21048725	S6805370011217
        2348179301822	<EMAIL>	2215SV10	98220709980327
        2348122665145	<EMAIL>	21048728	S6805370011228
        2348179273777	<EMAIL>	2215TM99	98220415909145
        2348179997252	<EMAIL>	2215TK89	98220415907932
        2348179657413	<EMAIL>	2215TM79	98220415909125
        2348179452112	<EMAIL>	2215TL57	98220415909093
        2349066379792	<EMAIL>	2215TJ96	98220415907994
        2348179040540	<EMAIL>	2215TL46	98220415909082
        2347082899064	<EMAIL>	2215TG17	98220415907790
        2348162848128	<EMAIL>	2215TK92	98220415907935
        2349079731187	<EMAIL>	2215SY49	98220415904637
        2349019229572	<EMAIL>	2215SW35	98220709980452
        2348033453956	<EMAIL>	2215TH31	98220415907845
        2349079153737	<EMAIL>	2215TH34	98220415907848
        2349060409679	<EMAIL>	2215SW17	98220709980434
        2348112376114	<EMAIL>	2215SW65	98220709980482
        2348179828503	<EMAIL>	2215SW22	98220709980439
        2349013951970	<EMAIL>	2215SW63	98220709980480
        2348166195605	<EMAIL>	2215TF67	98220415907740
        2348060928758	<EMAIL>	2215SW74	98220709980491
        2348188167047	<EMAIL>	21048750	S6805370011366
        2348033020926	<EMAIL>	2215SV76	98220709980393
        2348179467924	<EMAIL>	2215SV51	98220709980368
        2348179210969	<EMAIL>	2057QHQD	98220415907717
        2349030162256	<EMAIL>	2215SW61	98220709980478
        2347017989011	<EMAIL>	2057QHQK	98220415907705
        2348033436825	<EMAIL>	2215SX92	98220415904609
        2349093040471	<EMAIL>	2215SW39	98220709980456
        2348163188941	<EMAIL>	2215SV50	98220709980367
        2348169475282	<EMAIL>	2057QHQC	98220415907716
        2347039226705	<EMAIL>	2215TF88	98220415907761
        2348146932064	<EMAIL>	2215SW79	98220709980496
        2348061944311	<EMAIL>	2215SW51	98220709980468
        2348039638575	<EMAIL>	21048737	S6805370011215
        2349163537644	<EMAIL>	2215SY47	98220415904635
        2347069042581	<EMAIL>	21048867	S6805370011153
        2338180843109	<EMAIL>	2030LR53	98201011990788
    """

    def convert_to_dict(data_string):
        lines = data_string.strip().split('\n')
        header = lines[0].split('\t')
        
        # Create a list of dictionaries
        data_list = []
        
        for line in lines[1:]:
            values = line.split('\t')
            entry = {}
            
            for i, field in enumerate(header):
                if i < len(values):  # Ensure we don't go out of bounds
                    entry[field] = values[i]
                else:
                    entry[field] = None  # Handle missing values
                    
            data_list.append(entry)
        
        return data_list
    

    result = convert_to_dict(data)

    for item in result:
        email = str(item.get("email")).strip()
        terminal_id = str(item.get("terminal_id")).strip()


        # check if agent with this email already exists
        try:
            agent = Agent.objects.get(email=email)
        except Agent.DoesNotExist:
            print(f"Agent with email {email} does not exist.")
            continue

        # check if agent already has terminal id
        if agent.terminal_id is not None:
            print(f"Agent with email {email} already has terminal id.")
            continue

        # agent agent remittance value
        agent_wallet_transaction_pre_funding_record = AgentWalletTransaction.objects.filter(
            agent_wallet__agent__id=agent.id,
            transaction_from="PRE_FUNDING",
        ).last()
        if agent_wallet_transaction_pre_funding_record is None:
            agent.terminal_id = terminal_id
            agent.agent_type = "LOTTO_AGENT"

            agent.can_withdraw = True
            agent.suspended_on_agency_banking = False
            agent.un_suspension_reason = "done from lotto"
            agent.terminal_retrieved = False
            agent.has_pre_funding = False

            print(f"Agent with email {email} has no pre-funding record.")

            try:
                agent.save()
            except Exception as e:
                print(f"Error saving agent {email}: {e}")
            
            continue


        # get agent wallet instance
        agent_wallet_instance = AgentWallet.objects.filter(agent=agent).last()

        # check if agent has outstanding remittance to pay
        agent_remittance_record = LottoAgentRemittanceTable.objects.filter(agent = agent, remitted = False).last()
        outstanding_remittance = 0
        if agent_remittance_record:
            outstanding_remittance = agent_remittance_record.amount - agent_remittance_record.amount_paid
        
        if outstanding_remittance > 0:
            agent_bal = agent_wallet_instance.game_play_bal

            agent_bal = agent_wallet_transaction_pre_funding_record.amount - outstanding_remittance
        else:
            agent_bal = agent_wallet_transaction_pre_funding_record.amount


        agent.terminal_id = terminal_id
        agent.agent_type = "LOTTO_AGENT"

        agent.can_withdraw = True
        agent.suspended_on_agency_banking = False
        agent.un_suspension_reason = "done from lotto"
        agent.terminal_retrieved = False
        agent.has_pre_funding = True

        try:
            agent.save()
        except Exception as e:
            print(f"Error saving agent {email}: {e}")
            continue

        
        agent_wallet_instance.game_play_bal = agent_bal
        agent_wallet_instance.save()





def multiple_winnings_records():

    data = [
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"b337395",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"99306r4",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"854313y",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"158089u",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"a861265",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"7027b09",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"8m04433",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"w076680",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"l082876",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"0142t49",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"378m379",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"298f995",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"89s2997",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"1900y20",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"s623306",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1800.0,
            "game_id":"6626t93",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"32519p6",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"8703k52",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"351d373",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"94212x1",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"46n1526",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"i293153",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"39b5879",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"305j494",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"7l83202",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"14153h3",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"23h2720",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"y690976",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"p690769",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"9973x53",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"6q58133",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"726l110",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"741z845",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"4i86389",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"70a6290",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"0d12008",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"e682686",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"41166h5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"7n41008",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"i513729",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"379s780",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"170223v",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"w676609",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"7373m80",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"78499w5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"9570k83",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"391z025",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"30b6734",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"40b4225",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"01v6660",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"60137s5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"j250103",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"7854y09",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"786c994",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"439a447",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"r495403",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"4o32901",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"8652m83",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"4h84020",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":800.0,
            "game_id":"1a27018",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"646f881",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"8m63227",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"k557615",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"785407y",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"3095p46",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":800.0,
            "game_id":"67g3891",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"73t5094",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"556d677",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"739d083",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"487240g",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"59665r9",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"73874d2",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"989855x",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"11695w8",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"779j659",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"0739u01",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"0822q84",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"49o4723",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"930615o",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"0k37378",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"703y132",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"38490j8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"z771496",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"439t406",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"8697p61",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"83622m4",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"15c5405",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"982724h",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"5865o23",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"5m11529",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"4857x80",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"4351v45",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"87638y8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"2q62164",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"34e7251",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"448131t",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"95v2563",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"604732q",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"m574819",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"410g969",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"d259923",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"4z69772",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"5l19341",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"e497758",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"291m789",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"197094r",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"3416r13",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"405173j",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"23935f4",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"52565m9",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"7e89585",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":800.0,
            "game_id":"57603k2",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"3410h04",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"308z938",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"3111p16",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"0983f70",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"0465s66",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"j740406",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"mark umeh",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":4800.0,
            "game_id":"0805k03",
            "game_type":"KENYA_LOTTO",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"10823e3",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"q787925",
            "game_type":"BANKER",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"33m4294",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"99133v9",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3120.0,
            "game_id":"6009d29",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"m710316",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"85732c5",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"983a124",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"95912w5",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"824o063",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"660l788",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"2y05239",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"952h355",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"108223s",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"benard olaniyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"46540o9",
            "game_type":"KENYA_LOTTO",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"6915j60",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"9845o86",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"030c285",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"j998869",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"611628u",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"83657w4",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"benard olaniyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"11157b4",
            "game_type":"KENYA_LOTTO",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"y821120",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"57332w6",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"869m765",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"41423p2",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"5a27334",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"5828z35",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"1170a25",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"187b001",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"541c509",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"24596z7",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"1127w84",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"8020i54",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"824712h",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"g062410",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"38n3990",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"315277c",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"43172r0",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"helen leo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"98914x9",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"helen leo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"07645l7",
            "game_type":"QUIKA",
            "recovered":False
        },
        {
            "agent_name":"helen leo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"92f5846",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"5143b49",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"62u2014",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"579669n",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"214q780",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"9w63997",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"78b3578",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"2422t64",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"141u014",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"63302n5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"45577r8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"8o59446",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"269655i",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"45o7623",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"7159n68",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"l254410",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"7017a76",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"97f2891",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"05283e1",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":800.0,
            "game_id":"1346l25",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"s089063",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"4w96921",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"i532528",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"157m445",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"34003f6",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"359v083",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"5m21578",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Oguneletu Israel",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"2c69669",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"u393309",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"69v9135",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Damilola Afolayan - Amukoko 1",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349024675224",
            "amount":3600.0,
            "game_id":"828046z",
            "game_type":"KENYA_LOTTO",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"913629c",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"8l13247",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"96925l3",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"59994q5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"590f759",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"82j0843",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"98059u7",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"945443e",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"386z850",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1800.0,
            "game_id":"7o30730",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"76q4378",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"802977g",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"7q44133",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"2992l52",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"483c099",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"m550797",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"714w625",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"36348n4",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"helen leo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"717e160",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"931011u",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"e755248",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"g364933",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"914v544",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"355m973",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"4t18161",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"68k0804",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"535n528",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"80k5377",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"kolapo Abideen",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348115039406",
            "amount":1200.0,
            "game_id":"972676c",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Damilola Afolayan - Amukoko 1",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349024675224",
            "amount":2400.0,
            "game_id":"078940s",
            "game_type":"KENYA_LOTTO",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1400.0,
            "game_id":"10013n7",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"5379b01",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"6509g04",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"0802o18",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"069829k",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"97931e5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"32t2149",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"25b8251",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"87615q1",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"4748d08",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":800.0,
            "game_id":"27808h7",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"829x792",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"7w00892",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":700.0,
            "game_id":"1697h07",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"534m354",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"48955c6",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"3116d51",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"8888v80",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"86o0744",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"96l4612",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"23a0093",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"09651n1",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"024309h",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"02007c2",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"71d9561",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":800.0,
            "game_id":"71067t7",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"v384005",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"9k18918",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"7n65308",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"780013v",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"566348c",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"m678887",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"736559u",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"89h2652",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"x678691",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"398039y",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"g845202",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3000.0,
            "game_id":"298q828",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"89410t3",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"helen leo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"320772s",
            "game_type":"QUIKA",
            "recovered":False
        },
        {
            "agent_name":"helen leo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"42502q2",
            "game_type":"QUIKA",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"n158888",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"916257q",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"m413239",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"859b891",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"866433v",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"239v617",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"l730010",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"0831s61",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"41h0745",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"496q819",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"2j34098",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"00d1786",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"7e41950",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"51574i5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"6682p38",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"990g469",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"60406q8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"0e69106",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"138924j",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"5437h95",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"89557e9",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"4712g25",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"2797e72",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"90n1467",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"635648a",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"161b347",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"39091x9",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"212z660",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"02d5972",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"i162094",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"21r0198",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"i323788",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"6789t19",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"88503a1",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"787p653",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"r197846",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"726p038",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"210t146",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"9223o17",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"d065471",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"03x8036",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"91658q9",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"521797g",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"5k05476",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3075.0,
            "game_id":"5w86257",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Afuye Samuel",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"6714y88",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Afuye Samuel",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"722p313",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"3727v50",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"9374o94",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"738556r",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"03489p6",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"896j179",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"3s91614",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"x496788",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"341578q",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"1280q12",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"43v1531",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"593r542",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"l784075",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"1z09254",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"683f262",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"8488d80",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"144q019",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"2669y80",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"20i9206",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"941869y",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"33c8744",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"627059e",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"38l2479",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1800.0,
            "game_id":"41c4950",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"52905c8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"16u9170",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"7470y12",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"08911t2",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"542f266",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"6438h60",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"362494g",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"j743339",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"21671j5",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"984j181",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"paul onyekwere",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"566275o",
            "game_type":"QUIKA",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"73378r6",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"paul onyekwere",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":5000.0,
            "game_id":"51p3872",
            "game_type":"SALARY_FOR_LIFE",
            "recovered":False
        },
        {
            "agent_name":"paul onyekwere",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":5000.0,
            "game_id":"57n0014",
            "game_type":"SALARY_FOR_LIFE",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"6d49633",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"6663o29",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"06x3449",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"799312n",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"133469h",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"955p074",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"1s02007",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"871g099",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3000.0,
            "game_id":"87640f5",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"00o0205",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"387q202",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"327e800",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"934r130",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"d847937",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"30710q6",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"90720g9",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"70209m1",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"00953d6",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"341f310",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"427a066",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"7t50329",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"r725184",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"80514s1",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"648680l",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"20z5383",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"985f180",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"i927642",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"0x43442",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"c238855",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"m801642",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"75728q0",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1800.0,
            "game_id":"6o45096",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"670776s",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"599220x",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"138f889",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1800.0,
            "game_id":"967m865",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"55043o7",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"6833d67",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"96785z0",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"1j22518",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"494d895",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"717a057",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"j174493",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"m402451",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"10n8872",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"84970e8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"472444s",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"3y80245",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"971o042",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"762e627",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"890m340",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"39906r4",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"395m894",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"78e0952",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"454323t",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"g620691",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"04z7515",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"098m592",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"288b886",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"h452140",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"s337129",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"81k2117",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"e037436",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"02844o4",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"3r13597",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"7f84058",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"144e533",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"47850o5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"s075074",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"00p3733",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"7m82705",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1800.0,
            "game_id":"5257p19",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"541092w",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"37p5048",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"s690448",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"1t42292",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"9338r16",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"86114o9",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"1m82359",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"7341u10",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"16794k5",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"3j26610",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"3653t51",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"46612z6",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"6x38694",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"7y28643",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"079011i",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"1t37104",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"046239q",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"03r6233",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"01k1396",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"443m678",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"93534t6",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"063o094",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"370i829",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"82576n2",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"d804767",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"3f59278",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"u647132",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"99319s8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"52228r5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"6o50889",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"0846j32",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":800.0,
            "game_id":"666642u",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"2q09916",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"6653t22",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"1844z34",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"4252j27",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FARUQ ABIODUN",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"67i2292",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"24838n8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"567728u",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"09n0565",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"m847784",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"955811i",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"3b32212",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"v274072",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"9869v38",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"David Olaniyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":6000.0,
            "game_id":"6865i13",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"0s25014",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"FARUQ ABIODUN",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":4800.0,
            "game_id":"c827521",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"385d248",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"y049268",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"a743468",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Akeem Abiodun",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348082175003",
            "amount":100.0,
            "game_id":"6h42190",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"303l267",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"paul onyekwere",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2250.0,
            "game_id":"39e7332",
            "game_type":"SALARY_FOR_LIFE",
            "recovered":False
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":2400.0,
            "game_id":"1738j44",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"p216147",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"65g3576",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"23z0225",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"034064w",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"074w691",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"e860224",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"7320y21",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"236645o",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"47756o8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"4423t51",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"953i976",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"1234h44",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"173716d",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"792d954",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"49y4185",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"8382z75",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"59208v8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"1432v34",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"852b934",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"2x64865",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"3p28246",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"50742o0",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"49h1866",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"b161279",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1800.0,
            "game_id":"748d133",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"9750r38",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"639h187",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"8101l06",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"0535f73",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"95y6605",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"9528n57",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"12427j3",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"0635o63",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"6377q79",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"955g164",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"9a65196",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"429765h",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"2v98561",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"4v00015",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"1928b98",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"714e741",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"c934606",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"666699b",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"49963i5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"0p77280",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"72372f2",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"01067r1",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"89983x5",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"k016199",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1500.0,
            "game_id":"92e7202",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"z973156",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"b671908",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"j939546",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"538n412",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"n083542",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"267f107",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"v809184",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"386274z",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"8257d16",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"61088m9",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"60287d4",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"679r787",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"7230f29",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"37666l5",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oti Igbene",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"58913b4",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"91400b1",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"x172275",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"z869203",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"juwon ikuomola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"68b4711",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"63814g3",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"9681o36",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"e661656",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"18d3715",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"32421t0",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":1200.0,
            "game_id":"8716y24",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"3b22756",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"f208328",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3000.0,
            "game_id":"37f1358",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Faith Sunday - Apapa 2",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"2p66101",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"v313555",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"e245221",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"761039n",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"512030v",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"c695258",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"402815w",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"025636k",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"821g946",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"0s37886",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"373149o",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"120n635",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"925v086",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"91555z8",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"976516a",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"510228y",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3600.0,
            "game_id":"3205r47",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oloruntobi Azeeat Kikelomo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":6000.0,
            "game_id":"9v84792",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kaosarat Morenikeji Idowu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":4800.0,
            "game_id":"552131k",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"chioma mark",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348133355405",
            "amount":7200.0,
            "game_id":"e900875",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":2400.0,
            "game_id":"3071l98",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"65y7934",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"420082t",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"570v018",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kaosarat Morenikeji Idowu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"113886w",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":7200.0,
            "game_id":"c754486",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"66t7694",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Richard Phillips",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348124817911",
            "amount":2400.0,
            "game_id":"9517x82",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"David Olaniyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":9600.0,
            "game_id":"3b43840",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"FARUQ ABIODUN",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":12000.0,
            "game_id":"294425j",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":6000.0,
            "game_id":"853c567",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Damilare Titilayo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348101783814",
            "amount":2400.0,
            "game_id":"33s8478",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"301l782",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"62w5613",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"David Olaniyi -  Amukoko 3",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348131694083",
            "amount":3600.0,
            "game_id":"16594f0",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"k964130",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":7200.0,
            "game_id":"f958390",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":7200.0,
            "game_id":"m786856",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":2400.0,
            "game_id":"547t345",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":7200.0,
            "game_id":"82m1940",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":14400.0,
            "game_id":"43720p1",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"juwon ikuomola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"58l4265",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":7200.0,
            "game_id":"0999z76",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"76m0977",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"3q51514",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed - \<EMAIL>",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2342054409429",
            "amount":600.0,
            "game_id":"y284488",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"4p65097",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"380v290",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"3933s13",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"j849658",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"x566766",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348035545804",
            "amount":3600.0,
            "game_id":"429z162",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3600.0,
            "game_id":"7d72062",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":2400.0,
            "game_id":"715t244",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":4800.0,
            "game_id":"u298388",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":4800.0,
            "game_id":"019y676",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oloruntobi Azeeat Kikelomo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"585533l",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348035545804",
            "amount":1200.0,
            "game_id":"6s82020",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"3101k69",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Alero iseyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"290r349",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"p305499",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"0n53093",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"6873v99",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"3n36440",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"a142427",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"3w60819",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348085392937",
            "amount":3600.0,
            "game_id":"x878661",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3600.0,
            "game_id":"0m52785",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"n269548",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348085392937",
            "amount":1200.0,
            "game_id":"q408827",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":6000.0,
            "game_id":"46z7154",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kaosarat Morenikeji Idowu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1800.0,
            "game_id":"733n902",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oloruntobi Azeeat Kikelomo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":6000.0,
            "game_id":"507659q",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"15s7584",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"895h592",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":6000.0,
            "game_id":"g221365",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348085392937",
            "amount":2400.0,
            "game_id":"29a0756",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":2400.0,
            "game_id":"53e4117",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"976g729",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348035545804",
            "amount":6000.0,
            "game_id":"6d97150",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348085392937",
            "amount":2400.0,
            "game_id":"61w4486",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"780912x",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"88i9416",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":4800.0,
            "game_id":"6a85283",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"FARUQ ABIODUN",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":7200.0,
            "game_id":"25x1380",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"2z89026",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":6000.0,
            "game_id":"432o256",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":4800.0,
            "game_id":"23s4598",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"k566578",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348035545804",
            "amount":1200.0,
            "game_id":"5j26245",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"u427104",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"7e91303",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348035545804",
            "amount":1200.0,
            "game_id":"317i306",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":6000.0,
            "game_id":"12554i5",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"a446667",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"juwon ikuomola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":7200.0,
            "game_id":"78o6703",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"paul onyekwere",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"v180009",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"1h03152",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"j533647",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"paul onyekwere",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"d608466",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"elizabeth moronkeji",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"408454x",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"72g7017",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"23q6628",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"e740311",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"8l03036",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":7200.0,
            "game_id":"881779v",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"0125v82",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"96f2084",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"74g3939",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kaosarat Morenikeji Idowu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3600.0,
            "game_id":"392t682",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3000.0,
            "game_id":"y052331",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"paul onyekwere",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":10000.0,
            "game_id":"45y7239",
            "game_type":"WYSE_CASH",
            "recovered":False
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348085392937",
            "amount":1200.0,
            "game_id":"5u67037",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"93y4422",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"826136y",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"c749728",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2080.0,
            "game_id":"6j07612",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kaosarat Morenikeji Idowu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":6000.0,
            "game_id":"65o9338",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Precious Esther Aderibigbe",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"3k44639",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":7200.0,
            "game_id":"058464m",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347086474471",
            "amount":1200.0,
            "game_id":"5961x48",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"608392c",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":7200.0,
            "game_id":"436n056",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kaosarat Morenikeji Idowu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":7200.0,
            "game_id":"99767g4",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oloruntobi Azeeat Kikelomo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":6000.0,
            "game_id":"039x214",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Damilare Titilayo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348101783814",
            "amount":2400.0,
            "game_id":"99z4625",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"40u2070",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"FARUQ ABIODUN",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"79527r7",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":2400.0,
            "game_id":"18495m6",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"David Olaniyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349015108922",
            "amount":2400.0,
            "game_id":"55654m1",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"9e50576",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347086474471",
            "amount":1200.0,
            "game_id":"h041339",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"3260h09",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348085392937",
            "amount":2400.0,
            "game_id":"94q5106",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":1200.0,
            "game_id":"5846e94",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Damilare Titilayo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348101783814",
            "amount":2400.0,
            "game_id":"h683553",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"chioma mark",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348133355405",
            "amount":2400.0,
            "game_id":"31y6067",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"q642496",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"David Olaniyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"2554h08",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"2404i84",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347086474471",
            "amount":2400.0,
            "game_id":"u971661",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":4800.0,
            "game_id":"621840g",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":2400.0,
            "game_id":"92296j0",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"FARUQ ABIODUN",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":7200.0,
            "game_id":"0005f51",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":4800.0,
            "game_id":"51402o7",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347086474471",
            "amount":4800.0,
            "game_id":"6m81697",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"FARUQ ABIODUN",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"3108s09",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"3483y22",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Abiodun Adesanya",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347047668408",
            "amount":2400.0,
            "game_id":"729951z",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"0l73986",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Ogunbiyi Taiwo Oluwaseyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347089366488",
            "amount":2400.0,
            "game_id":"67z9756",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"f993614",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"32f7688",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347086474471",
            "amount":1200.0,
            "game_id":"79093s4",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"941u266",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"89r3236",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"David Olaniyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"960e144",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":1200.0,
            "game_id":"0630b20",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"8752h94",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"FARUQ ABIODUN",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"316i021",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"3d30824",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348035545804",
            "amount":1200.0,
            "game_id":"f885906",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"chioma mark",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348133355405",
            "amount":2400.0,
            "game_id":"93669f9",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"abdulkabir sodiq",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"2b31316",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Ogunbiyi Taiwo Oluwaseyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347089366488",
            "amount":2400.0,
            "game_id":"8t02342",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"046m780",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"487449b",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"23m4570",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"50x5992",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"07r8321",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"i338537",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"4101u33",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"27791v4",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":500.0,
            "game_id":"m510748",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":800.0,
            "game_id":"462727t",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"p904865",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"FATIMA GADAS",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"638q741",
            "game_type":"INSTANT_CASHOUT",
            "recovered":False
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"0336u35",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"6l60691",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"3n42007",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"98203h8",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"338457a",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"35i9968",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"z199220",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":100.0,
            "game_id":"827g222",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Fatima Rabiu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":200.0,
            "game_id":"441c194",
            "game_type":"INSTANT_CASHOUT",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"19z2840",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"4j82157",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oloruntobi Azeeat Kikelomo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"403w080",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"3973o28",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"67304u2",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"abdulkabir sodiq",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"58096y9",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"06m3132",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348035545804",
            "amount":1200.0,
            "game_id":"407c593",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"438112w",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"d812333",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"3w66915",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"juwon ikuomola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"7432g41",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"758q821",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"5e45312",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Ifeoluwa Dorcas Odeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"6531b16",
            "game_type":"GHANA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"09345o1",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"9631w70",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Precious Esther Aderibigbe",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"85721y3",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"6242m86",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"99522s2",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348035545804",
            "amount":1200.0,
            "game_id":"462662y",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Ogunbiyi Taiwo Oluwaseyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347089366488",
            "amount":2400.0,
            "game_id":"511c401",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":4800.0,
            "game_id":"2248j85",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Faith Sunday - Apapa 2",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"752t928",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"744z554",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"FARUQ ABIODUN",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"6y10159",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348035545804",
            "amount":1200.0,
            "game_id":"j388841",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Richard Phillips",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348124817911",
            "amount":4800.0,
            "game_id":"91754u3",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"1632r15",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"FARUQ ABIODUN",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":9600.0,
            "game_id":"904264t",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"m025280",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":2400.0,
            "game_id":"650j145",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"chioma mark",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348133355405",
            "amount":1200.0,
            "game_id":"338t333",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Ogunbiyi Taiwo Oluwaseyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349131630900",
            "amount":2400.0,
            "game_id":"7430c00",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oguneletu Israel",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348022406534",
            "amount":4800.0,
            "game_id":"58370e9",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"8k66750",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"juwon ikuomola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"08773q5",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":4800.0,
            "game_id":"7194c83",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"c748327",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":4800.0,
            "game_id":"355x966",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"juwon ikuomola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":9600.0,
            "game_id":"31018g1",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"075861a",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"5f94837",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"x336690",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oloruntobi Azeeat Kikelomo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"32188o9",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3000.0,
            "game_id":"4649e95",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"32257w4",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oluwafemi Opeyemi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1800.0,
            "game_id":"2501z85",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oloruntobi Azeeat Kikelomo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":6000.0,
            "game_id":"0199h42",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Precious Esther Aderibigbe",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":6000.0,
            "game_id":"9280z82",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kaosarat Morenikeji Idowu",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":6000.0,
            "game_id":"y715807",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Oloruntobi Azeeat Kikelomo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":7200.0,
            "game_id":"346n192",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349157455944",
            "amount":2400.0,
            "game_id":"16b4235",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":7200.0,
            "game_id":"m747427",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Igwe Chinonso",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349069137801",
            "amount":2400.0,
            "game_id":"847a908",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Richard Phillips",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348124817911",
            "amount":6000.0,
            "game_id":"8i10722",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"David Olaniyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":3600.0,
            "game_id":"98j3096",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Ogunbiyi Taiwo Oluwaseyi",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2347089366488",
            "amount":2400.0,
            "game_id":"3770x08",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Tumininu Adeoye",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"26655q5",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"juwon ikuomola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"12q9682",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"u064224",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"63213g3",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"33i6872",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"35643o4",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed - <EMAIL>",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"81v6986",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"juwon ikuomola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"84673q8",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349157455944",
            "amount":1200.0,
            "game_id":"8v18518",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed - <EMAIL>",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"9588i60",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"862s715",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"4s93030",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed - <EMAIL>",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"90m7604",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed - <EMAIL>",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":600.0,
            "game_id":"4n32483",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Adeyin Oyindamola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"437n807",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":4800.0,
            "game_id":"m791244",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":4800.0,
            "game_id":"819733s",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"3k95813",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2400.0,
            "game_id":"91339m9",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"hassan Ahmed",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2349027391199",
            "amount":4800.0,
            "game_id":"24720e7",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"5861q84",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"David Olaniyi - Amukoko 3",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348151821951",
            "amount":1200.0,
            "game_id":"08t6027",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"ikuomola bolanle",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"2348109401799",
            "amount":2400.0,
            "game_id":"821362z",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":4800.0,
            "game_id":"203113q",
            "game_type":"KENYA_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"d465163",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"093e285",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"olarewaju damilola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"25a2894",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"Kehinde Ojediran",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":2000.0,
            "game_id":"740o348",
            "game_type":"BANKER",
            "recovered":False
        },
        {
            "agent_name":"juwon ikuomola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"46580u3",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"765b321",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"juwon ikuomola",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"112l619",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        },
        {
            "agent_name":"sunday dairo",
            "agent_email":"<EMAIL>",
            "agent_phone_number":"*************",
            "amount":1200.0,
            "game_id":"5i15146",
            "game_type":"KENYA_30_LOTTO",
            "recovered":True
        }
    ]


    
    total_count =  827
    total_amount = 2821650.0
    total_amount_recovered= 893100.0


    new_data = []

    debitors = {}

    
    for i in data:
        if i["game_type"] in ["GHANA_LOTTO", "NIGERIA_LOTTO", "KENYA_LOTTO", "KENYA_30_LOTTO"]:
            total_count -= 1
            if i["recovered"] == True:
                total_amount -= i["amount"]
                total_amount_recovered -= i["amount"]

            
            continue


        new_data.append(i)

        if i["recovered"] == False:
            try:
                debitors[i["agent_phone_number"]]["total_amount"] += i["amount"]
            except KeyError:
                debitors[i["agent_phone_number"]] = {
                    "total_amount": i["amount"],
                    "agent_name": i["agent_name"],
                    "agent_email": i["agent_email"],
                    "agent_phone_number": i["agent_phone_number"],
                    "game_id": i["game_id"],
                    "game_type": i["game_type"],
                }

        

        
    
    # print(f"Total records: {len(new_data)}")
    # print(f"Total amount: {total_amount}")
    # print(f"Total amount recovered: {total_amount_recovered}")
    # print("\n\n\n\n")
    # print(new_data)


    print(debitors)



class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        query_set =  PosLotteryWinners.objects.filter(expired = False, payout_successful = False, expiry_date__isnull = True)
        for instance in query_set:
            expiry_date = instance.date_created + relativedelta(days=7)
            instance.expiry_date = expiry_date
            instance.save()
            instance.refresh_from_db()

            todays_date = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()

            if instance.expiry_date < todays_date:
                instance.expired = True
                instance.save()
                instance.refresh_from_db()

            




        
        


            


