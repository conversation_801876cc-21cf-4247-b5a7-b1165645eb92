import random
import datetime
import uuid
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.utils import timezone
import requests
from django.conf import settings

from account.models import User
# from django.contrib.auth.models import User

from pos_app.models import (
    Supervisor, Agent, AgentWallet,
    AgentWalletTransaction, LottoVerticalLead, LottoVerticalLeadWallet
)


class Command(BaseCommand):
    help = "Create test data and hit the supervisor dashboard endpoint"

    def add_arguments(self, parser):
        parser.add_argument(
            '--base-url',
            type=str,
            default='http://localhost:8000',
            help='Base URL of the API'
        )
        parser.add_argument(
            '--username',
            type=str,
            default='admin',
            help='Username for authentication'
        )
        parser.add_argument(
            '--password',
            type=str,
            default='admin',
            help='Password for authentication'
        )
        parser.add_argument(
            '--redis-host',
            type=str,
            default='localhost',
            help='Redis host to use'
        )

    def handle(self, *args, **options):
        base_url = options['base_url']
        username = options['username']
        password = options['password']
        redis_host = options['redis_host']

        # Override Redis host if provided
        if redis_host != 'localhost':
            # This is just for logging, doesn't actually change the setting
            self.stdout.write(self.style.SUCCESS(f'Using Redis host: {redis_host}'))
            # We'll handle Redis connections directly in our methods

        self.stdout.write(self.style.SUCCESS('Creating test data...'))

        vertical_lead = self._create_vertical_lead()
        self.stdout.write(
            self.style.SUCCESS(f'Created vertical lead: {vertical_lead.first_name} {vertical_lead.last_name}'))

        supervisor = self._create_supervisor(vertical_lead)
        self.stdout.write(self.style.SUCCESS(f'Created supervisor: {supervisor.first_name} {supervisor.last_name}'))

        agents = self._create_agents(supervisor, 10)
        self.stdout.write(self.style.SUCCESS(f'Created {len(agents)} agents'))

        agent_wallets = self._create_agent_wallets()
        self.stdout.write(self.style.SUCCESS(f'Created {len(agent_wallets)} agent wallets'))

        transactions = self._create_wallet_transactions()
        self.stdout.write(self.style.SUCCESS(f'Created {len(transactions)} wallet transactions'))

        # # Get auth token
        # token = self._get_auth_token(username, password)
        # if not token:
        #     self.stdout.write(self.style.ERROR('Failed to get authentication token'))
        #     return

        # Hit the endpoint
        self.stdout.write(self.style.SUCCESS('Hitting the supervisor dashboard endpoint...'))
        response = self._hit_endpoint(base_url, supervisor.id, token=None)

        self.stdout.write(self.style.SUCCESS('Response:'))
        self.stdout.write(str(response))

    def _create_vertical_lead(self):
        """Create a vertical lead"""
        phone_number = f"090{random.randint(********, ********)}"
        vertical_lead, created = LottoVerticalLead.objects.get_or_create(
            phone=phone_number,
            defaults={
                'first_name': "Vertical",
                'last_name': "Lead",
                'full_name': "Vertical Lead",
                'email': f"vertical_lead_{random.randint(1000, 9999)}@example.com",
                'user_id': str(random.randint(10000, 99999)),
                'user_uuid': str(uuid.uuid4()),
                'address': "123 Vertical Street, Lagos"
            }
        )

        # Create vertical lead wallet
        try:
            wallet, _ = LottoVerticalLeadWallet.objects.get_or_create(
                vertical_lead=vertical_lead,
                defaults={
                    'commission_balance': Decimal(str(random.randint(1000, 10000))),
                    'rewarded_commission_balance': Decimal(str(random.randint(1000, 10000)))
                }
            )
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating vertical lead wallet: {str(e)}'))

        return vertical_lead

    def _create_supervisor(self, vertical_lead):
        """Create a supervisor"""
        supervisor, created = Supervisor.objects.get_or_create(
            first_name="Test",
            last_name="Supervisor",
            defaults={
                'full_name': "Test Supervisor",
                'phone': f"080{random.randint(********, ********)}",
                'email': f"supervisor_{random.randint(1000, 9999)}@example.com",
                'user_id': str(random.randint(10000, 99999)),
                'user_uuid': str(uuid.uuid4()),
                'address': "123 Supervisor Street, Lagos",
                'performance_status': "PERFORMING",
                'vertical_lead': vertical_lead
            }
        )
        return supervisor

    def _create_agents(self, supervisor, count=10):
        """Create agents and assign to supervisor"""
        agents = []
        for i in range(count):
            try:
                agent, created = Agent.objects.get_or_create(
                    first_name=f"Agent{i}",
                    last_name=f"Test{i}",
                    supervisor=supervisor,
                    defaults={
                        'email': f"agent_{i}_{random.randint(1000, 9999)}@example.com",
                        'phone': f"23470{random.randint(********, ********)}",
                        'user_id': str(random.randint(10000, 99999)),
                        'user_uuid': str(uuid.uuid4())
                    }
                )

                agents.append(agent)
                self.stdout.write(self.style.SUCCESS(f'Created agent: {agent.first_name} {agent.last_name}'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error creating agent: {str(e)}'))

        return agents

    def _create_agent_wallets(self):
        """Create agents and assign to supervisor"""
        agents = Agent.objects.all()
        agent_wallets = []
        for agent in agents:
            # try:
            # Create agent wallet without using Redis
            wallet, created = AgentWallet.objects.get_or_create(
                agent=agent,
                defaults={
                    'agent_name': f"{agent.first_name} {agent.last_name}",
                    'agent_phone_number': agent.phone,
                    'agent_email': agent.email,
                    'commission_bal': round(Decimal(str(random.randint(1000, 5000))), 2),
                    'game_play_bal': round(Decimal(str(random.randint(10000, 50000))), 2),
                    'commission_rewarded': round(Decimal(str(random.randint(10000, 50000))), 2),
                    'bonus_bal': round(Decimal(str(random.randint(10000, 50000))), 2),
                    'used_bonus_bal': round(Decimal(str(random.randint(10000, 50000))), 2),
                    'winnings_bal': round(Decimal(str(random.randint(10000, 50000))), 2),
                    'withdrawable_available_bal': round(Decimal(str(random.randint(10000, 50000))), 2)}
            )

            agent_wallets.append(wallet)
            self.stdout.write(self.style.SUCCESS(f'Created agent wallet for: {agent.first_name} {agent.last_name}'))
            # except Exception as e:
            #     self.stdout.write(self.style.ERROR(f'Error creating agent: {str(e)}'))

        return agent_wallets

    def _create_wallet_transactions(self):
        """Create wallet transactions for agents spanning a month"""
        transactions = []
        today = timezone.now().date()
        agent_wallets = AgentWallet.objects.all()
        for agent_wallet in agent_wallets:
            # Create transactions for the past 30 days
            for day in range(30):
                transaction_date = today - datetime.timedelta(days=day)
                self.stdout.write(self.style.SUCCESS(f'creating transaction_date: {transaction_date}'))

                # Create 0-5 transactions per day
                for _ in range(random.randint(0, 5)):
                    # try:
                    amount = float(str(random.randint(1000, 10000)))

                    transaction = AgentWalletTransaction.objects.create(
                        agent_wallet=agent_wallet,
                        agent_phone_number=agent_wallet.agent.phone,
                        amount=amount,
                        # transaction_from='GAME_PLAY',
                        transaction_reference=f"REF{agent_wallet.agent.id}{day}{random.randint(10000, 99999)}",
                        game_play_id=f"GAME{agent_wallet.agent.id}{day}{random.randint(10000, 99999)}",
                        # date_created=timezone.make_aware(datetime.datetime.combine(
                        #     transaction_date,
                        #     datetime.time(hour=random.randint(8, 20), minute=random.randint(0, 59))
                        # )),
                    )
                    transaction.date_created = timezone.make_aware(datetime.datetime.combine(
                        transaction_date,
                        datetime.time(hour=random.randint(8, 20), minute=random.randint(0, 59))
                    ))
                    transaction.save()
                    transaction.refresh_from_db()
                    transactions.append(transaction)
                    # except Exception as e:
                    #     self.stdout.write(self.style.ERROR(f'Error creating transaction: {str(e)}'))

        return transactions

    def _get_auth_token(self, username, password):
        """Get authentication token"""
        base_url = 'https://dev.libertypayng.com/user/login/create/'
        # try:
        # Check if the User exists
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            # Create a superuser if it doesn't exist
            User.objects.create_superuser(username=username, email='<EMAIL>', password=password)
            self.stdout.write(self.style.SUCCESS(f'Created superuser: {username}'))

        # Get token from API
        response = requests.post(
            f"{base_url}",
            json={"username": username, "password": password}
        )

        if response.status_code == 200:
            return response.json().get('access')
        else:
            self.stdout.write(self.style.ERROR(f'Failed to get token: {response.text}'))
            return None
        # except Exception as e:
        #     self.stdout.write(self.style.ERROR(f'Error getting token: {str(e)}'))
        #     return None

    def _hit_endpoint(self, base_url, supervisor_id, token=None):
        """Hit the supervisor dashboard endpoint"""
        try:
            response = requests.get(
                f"{base_url}/agent/api/supervisor/dashboard-summary",
                headers={"Authorization": f"Bearer {token}"}
            )

            if response.status_code == 200:
                print(f"HERE'S YOUR RESPONSE |=>|=>|=>|=>|=>|=>|=>|=>|=> \n\n\n {response.json()} \n\n\n")
                return response.json()
            else:
                self.stdout.write(self.style.ERROR(f'Failed to get dashboard data: {response.text}'))
                return None
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error hitting endpoint: {str(e)}'))
            return None

# import random
# import datetime
# import uuid
# from decimal import Decimal
# from django.core.management.base import BaseCommand
# from django.utils import timezone
# import requests
# from account.models import User
# from pos_app.models import (
#     Supervisor, Agent, AgentWallet,
#     AgentWalletTransaction, LottoVerticalLead, LottoVerticalLeadWallet
# )
#
#
# class Command(BaseCommand):
#     help = "Create test data and hit the supervisor dashboard endpoint"
#
#     def add_arguments(self, parser):
#         parser.add_argument(
#             '--base-url',
#             type=str,
#             default='http://localhost:8000',
#             help='Base URL of the API'
#         )
#         parser.add_argument(
#             '--username',
#             type=str,
#             default='admin',
#             help='Username for authentication'
#         )
#         parser.add_argument(
#             '--password',
#             type=str,
#             default='admin',
#             help='Password for authentication'
#         )
#
#     def handle(self, *args, **options):
#         base_url = options['base_url']
#         username = options['username']
#         password = options['password']
#
#         # Create test data
#         self.stdout.write(self.style.SUCCESS('Creating test data...'))
#
#         # Create vertical lead
#         vertical_lead = self._create_vertical_lead()
#         self.stdout.write(
#             self.style.SUCCESS(f'Created vertical lead: {vertical_lead.first_name} {vertical_lead.last_name}'))
#
#         # Create supervisor and assign vertical lead
#         supervisor = self._create_supervisor(vertical_lead)
#         self.stdout.write(self.style.SUCCESS(f'Created supervisor: {supervisor.first_name} {supervisor.last_name}'))
#
#         # Create agents and assign to supervisor
#         # agents = self._create_agents(supervisor, 10)
#         # self.stdout.write(self.style.SUCCESS(f'Created {len(agents)} agents'))
#         #
#         # # Create wallet transactions for agents
#         # transactions = self._create_wallet_transactions(agents)
#         # self.stdout.write(self.style.SUCCESS(f'Created {len(transactions)} wallet transactions'))
#
#         # # Get auth token
#         # token = self._get_auth_token(base_url, username, password)
#         # if not token:
#         #     self.stdout.write(self.style.ERROR('Failed to get authentication token'))
#         #     return
#
#         # Hit the endpoint
#         self.stdout.write(self.style.SUCCESS('Hitting the supervisor dashboard endpoint...'))
#         response = self._hit_endpoint(base_url, supervisor.id)
#
#         # Print response
#         self.stdout.write(self.style.SUCCESS('Response:'))
#         self.stdout.write(str(response))
#
#     def _create_vertical_lead(self):
#         """Create a vertical lead"""
#         phone_number = f"090{random.randint(********, ********)}"
#         vertical_lead, created = LottoVerticalLead.objects.get_or_create(
#             phone=phone_number,
#             defaults={
#                 'first_name': "Vertical",
#                 'last_name': "Lead",
#                 'full_name': "Vertical Lead",
#                 'email': f"vertical_lead_{random.randint(1000, 9999)}@example.com",
#                 'user_id': str(random.randint(10000, 99999)),
#                 'user_uuid': str(uuid.uuid4()),
#                 'address': "123 Vertical Street, Lagos"
#             }
#         )
#
#         # Create vertical lead wallet
#         wallet, _ = LottoVerticalLeadWallet.objects.get_or_create(
#             vertical_lead=vertical_lead,
#             defaults={
#                 'commission_balance': Decimal(str(random.randint(1000, 10000))),
#                 'rewarded_commission_balance': Decimal(str(random.randint(1000, 10000)))
#             }
#         )
#
#         return vertical_lead
#
#     def _create_supervisor(self, vertical_lead):
#         """Create a supervisor with vertical lead"""
#         supervisor, created = Supervisor.objects.get_or_create(
#             first_name="Test",
#             last_name="Supervisor",
#             defaults={
#                 'full_name': "Test Supervisor",
#                 'phone': f"080{random.randint(********, ********)}",
#                 'email': f"supervisor_{random.randint(1000, 9999)}@example.com",
#                 'user_id': str(random.randint(10000, 99999)),
#                 'user_uuid': str(uuid.uuid4()),
#                 'address': "123 Supervisor Street, Lagos",
#                 'performance_status': "PERFORMING",
#                 'vertical_lead': vertical_lead
#             }
#         )
#
#         # If the supervisor already existed, update the vertical_lead
#         if not created:
#             supervisor.vertical_lead = vertical_lead
#             supervisor.save()
#
#         return supervisor
#
#     def _create_agents(self, supervisor, count=10):
#         """Create agents and assign to supervisor"""
#         agents = []
#         for i in range(count):
#             try:
#                 agent, created = Agent.objects.get_or_create(
#                     first_name=f"Agent{i}",
#                     last_name=f"Test{i}",
#                     defaults={
#                         'full_name': f"Agent{i} Test{i}",
#                         'phone': f"070{random.randint(********, ********)}",
#                         'email': f"agent_{i}_{random.randint(1000, 9999)}@example.com",
#                         'user_id': str(random.randint(10000, 99999)),
#                         'user_uuid': str(uuid.uuid4()),
#                         'supervisor': supervisor,
#                         'agent_type': "LOTTO_AGENT",
#                         'can_withdraw': True,
#                         'suspended_on_agency_banking': False,
#                         'terminal_retrieved': False,
#                         'has_pre_funding': True
#                     }
#                 )
#
#                 # Create agent wallet
#                 wallet, _ = AgentWallet.objects.get_or_create(
#                     agent=agent,
#                     defaults={
#                         'agent_name': f"{agent.first_name} {agent.last_name}",
#                         'agent_phone_number': agent.phone,
#                         'agent_email': agent.email,
#                         'commission_balance': Decimal(str(random.randint(1000, 5000))),
#                         'available_balance': Decimal(str(random.randint(5000, 20000))),
#                         'game_play_bal': Decimal(str(random.randint(10000, 50000)))
#                     }
#                 )
#
#                 agents.append(agent)
#                 self.stdout.write(self.style.SUCCESS(f'Created agent: {agent.first_name} {agent.last_name}'))
#             except Exception as e:
#                 self.stdout.write(self.style.ERROR(f'Error creating agent: {str(e)}'))
#
#     def _create_wallet_transactions(self, agents):
#         """Create wallet transactions for agents spanning a month"""
#         transactions = []
#         today = timezone.now().date()
#
#         for agent in agents:
#             # Create transactions for the past 30 days
#             for day in range(30):
#                 transaction_date = today - datetime.timedelta(days=day)
#
#                 # Create 0-5 transactions per day
#                 for _ in range(random.randint(0, 5)):
#                     amount = Decimal(str(random.randint(1000, 10000)))
#
#                     transaction = AgentWalletTransaction.objects.create(
#                         agent=agent,
#                         agent_phone_number=agent.phone,
#                         amount=amount,
#                         transaction_from='GAME_PLAY',
#                         reference=f"REF{agent.id}{day}{random.randint(10000, 99999)}",
#                         game_play_id=f"GAME{agent.id}{day}{random.randint(10000, 99999)}",
#                         created_at=timezone.make_aware(datetime.datetime.combine(
#                             transaction_date,
#                             datetime.time(hour=random.randint(8, 20), minute=random.randint(0, 59))
#                         )),
#                     )
#                     transactions.append(transaction)
#
#         return transactions
#
#     def _get_auth_token(self, base_url, username, password):
#         """Get authentication token"""
#
#         try:
#             # Check if the User exists
#             try:
#                 user = User.objects.get(username=username)
#             except User.DoesNotExist:
#                 # Create a superuser if it doesn't exist
#                 User.objects.create_superuser(username=username, email='<EMAIL>', password=password)
#                 self.stdout.write(self.style.SUCCESS(f'Created superuser: {username}'))
#
#             # Get token from API
#             response = requests.post(
#                 # f"{base_url}/api/token/",
#                 'https://dev.libertypayng.com/user/login/create/',
#                 json={"username": username, "password": password}
#             )
#
#             if response.status_code == 200:
#                 return response.json().get('access')
#             else:
#                 self.stdout.write(self.style.ERROR(f'Failed to get token: {response.text}'))
#                 return None
#         except Exception as e:
#             self.stdout.write(self.style.ERROR(f'Error getting token: {str(e)}'))
#             return None
#
#     def _hit_endpoint(self, base_url, supervisor_id):
#         """Hit the supervisor dashboard endpoint"""
#         try:
#             print(f"HERE'S YOUR SUPERVISOR_ID |=>|=>|=>|=>|=>|=>|=>|=>|=> \n\n\n {supervisor_id} \n\n\n")
#             response = requests.get(
#                 f"{base_url}/agent/api/supervisor/{supervisor_id}/dashboard-summary"
#                 # headers={"Authorization": f"Bearer {token}"}
#             )
#
#             if response.status_code == 200:
#                 print(f"HERE'S YOUR RESPONSE |=>|=>|=>|=>|=>|=>|=>|=>|=> \n\n\n {response.json()} \n\n\n")
#                 return response.json()
#             else:
#                 self.stdout.write(self.style.ERROR(f'Failed to get dashboard data: {response.text}'))
#                 return None
#         except Exception as e:
#             self.stdout.write(self.style.ERROR(f'Error hitting endpoint: {str(e)}'))
#             return None


# import random
# import datetime
# import uuid
# from decimal import Decimal
# from django.core.management.base import BaseCommand
# from django.utils import timezone
# import requests
# from django.contrib.auth.models import User
#
# from pos_app.models import (
#     Supervisor, Agent, AgentWallet, AgentWalletTransaction, LottoVerticalLead, LottoVerticalLeadWallet
# )
#
#
# class Command(BaseCommand):
#     help = "Create test data and hit the supervisor dashboard endpoint"
#
#     def add_arguments(self, parser):
#         parser.add_argument(
#             '--base-url',
#             type=str,
#             default='http://localhost:8000',
#             help='Base URL of the API'
#         )
#         parser.add_argument(
#             '--username',
#             type=str,
#             default='admin',
#             help='Username for authentication'
#         )
#         parser.add_argument(
#             '--password',
#             type=str,
#             default='admin',
#             help='Password for authentication'
#         )
#
#     def handle(self, *args, **options):
#         base_url = options['base_url']
#         username = options['username']
#         password = options['password']
#
#         # Create test data
#         self.stdout.write(self.style.SUCCESS('Creating test data...'))
#
#         # Create vertical lead
#         vertical_lead = self._create_vertical_lead()
#         self.stdout.write(
#             self.style.SUCCESS(f'Created vertical lead: {vertical_lead.first_name} {vertical_lead.last_name}'))
#
#         # Create supervisor and assign vertical lead
#         supervisor = self._create_supervisor(vertical_lead)
#         self.stdout.write(self.style.SUCCESS(f'Created supervisor: {supervisor.first_name} {supervisor.last_name}'))
#
#         # Create agents and assign to supervisor
#         agents = self._create_agents(supervisor, 10)
#         self.stdout.write(self.style.SUCCESS(f'Created {len(agents)} agents'))
#
#         # Create wallet transactions for agents
#         transactions = self._create_wallet_transactions(agents)
#         self.stdout.write(self.style.SUCCESS(f'Created {len(transactions)} wallet transactions'))
#
#         # Get auth token
#         token = self._get_auth_token(base_url, username, password)
#         if not token:
#             self.stdout.write(self.style.ERROR('Failed to get authentication token'))
#             return
#
#         # Hit the endpoint
#         self.stdout.write(self.style.SUCCESS('Hitting the supervisor dashboard endpoint...'))
#         response = self._hit_endpoint(base_url, supervisor.id, token)
#
#         # Print response
#         self.stdout.write(self.style.SUCCESS('Response:'))
#         self.stdout.write(str(response))
#
#     def _create_vertical_lead(self):
#         """Create a vertical lead"""
#         phone_number = f"090{random.randint(********, ********)}"
#         vertical_lead, created = LottoVerticalLead.objects.get_or_create(
#             phone=phone_number,
#             defaults={
#                 'first_name': "Vertical",
#                 'last_name': "Lead",
#                 'full_name': "Vertical Lead",
#                 'email': f"vertical_lead_{random.randint(1000, 9999)}@example.com",
#                 'user_id': str(random.randint(10000, 99999)),
#                 'user_uuid': str(uuid.uuid4()),
#                 'address': "123 Vertical Street, Lagos"
#             }
#         )
#
#         # Create vertical lead wallet
#         wallet, _ = LottoVerticalLeadWallet.objects.get_or_create(
#             vertical_lead=vertical_lead,
#             defaults={
#                 'commission_balance': Decimal(str(random.randint(1000, 10000))),
#                 'rewarded_commission_balance': Decimal(str(random.randint(1000, 10000)))
#             }
#         )
#
#         return vertical_lead
#
#     def _create_supervisor(self, vertical_lead):
#         """Create a supervisor with vertical lead"""
#         supervisor, created = Supervisor.objects.get_or_create(
#             first_name="Test",
#             last_name="Supervisor",
#             vertical_lead=vertical_lead,
#             defaults={
#                 'phone': f"080{random.randint(********, ********)}",
#                 'email': f"supervisor_{random.randint(1000, 9999)}@example.com"
#             }
#         )
#         return supervisor
#
#     def _create_agents(self, supervisor, count=10):
#         """Create agents and assign to supervisor"""
#         agents = []
#         for i in range(count):
#             agent, created = Agent.objects.get_or_create(
#                 first_name=f"Agent{i}",
#                 last_name=f"Test{i}",
#                 defaults={
#                     'phone': f"070{random.randint(********, ********)}",
#                     'email': f"agent_{i}_{random.randint(1000, 9999)}@example.com",
#                     'supervisor': supervisor,
#                     'agent_type': "LOTTO_AGENT",
#                     'can_withdraw': True,
#                     'suspended_on_agency_banking': False,
#                     'terminal_retrieved': False,
#                     'has_pre_funding': True
#                 }
#             )
#
#             # Create agent wallet
#             wallet, _ = AgentWallet.objects.get_or_create(
#                 agent=agent,
#                 defaults={
#                     'agent_name': f"{agent.first_name} {agent.last_name}",
#                     'agent_phone_number': agent.phone,
#                     'agent_email': agent.email,
#                     'commission_balance': Decimal(str(random.randint(1000, 5000))),
#                     'available_balance': Decimal(str(random.randint(5000, 20000))),
#                     'game_play_bal': Decimal(str(random.randint(10000, 50000)))
#                 }
#             )
#             agents.append(agent)
#
#         return agents
#
#     def _create_wallet_transactions(self, agents):
#         """Create wallet transactions for agents spanning a month"""
#         transactions = []
#         today = timezone.now().date()
#
#         for agent in agents:
#             # Create transactions for the past 30 days
#             for day in range(30):
#                 transaction_date = today - datetime.timedelta(days=day)
#
#                 # Create 0-5 transactions per day
#                 for _ in range(random.randint(0, 5)):
#                     amount = Decimal(str(random.randint(1000, 10000)))
#
#                     transaction = AgentWalletTransaction.objects.create(
#                         agent=agent,
#                         agent_phone_number=agent.phone,
#                         amount=amount,
#                         transaction_from='GAME_PLAY',
#                         reference=f"REF{agent.id}{day}{random.randint(10000, 99999)}",
#                         game_play_id=f"GAME{agent.id}{day}{random.randint(10000, 99999)}",
#                         created_at=timezone.make_aware(datetime.datetime.combine(
#                             transaction_date,
#                             datetime.time(hour=random.randint(8, 20), minute=random.randint(0, 59))
#                         )),
#                     )
#                     transactions.append(transaction)
#
#         return transactions
#
#     def _get_auth_token(self, base_url, username, password):
#         """Get authentication token"""
#         try:
#             # Check if the User exists
#             try:
#                 user = User.objects.get(username=username)
#             except User.DoesNotExist:
#                 # Create a superuser if it doesn't exist
#                 User.objects.create_superuser(username=username, email='<EMAIL>', password=password)
#                 self.stdout.write(self.style.SUCCESS(f'Created superuser: {username}'))
#
#             # Get token from API
#             response = requests.post(
#                 f"{base_url}/api/token/",
#                 json={"username": username, "password": password}
#             )
#
#             if response.status_code == 200:
#                 return response.json().get('access')
#             else:
#                 self.stdout.write(self.style.ERROR(f'Failed to get token: {response.text}'))
#                 return None
#         except Exception as e:
#             self.stdout.write(self.style.ERROR(f'Error getting token: {str(e)}'))
#             return None
#
#     def _hit_endpoint(self, base_url, supervisor_id, token):
#         """Hit the supervisor dashboard endpoint"""
#         try:
#             response = requests.get(
#                 f"{base_url}/api/supervisor/{supervisor_id}/dashboard-summary",
#                 headers={"Authorization": f"Bearer {token}"}
#             )
#
#             if response.status_code == 200:
#                 print(f"HERE'S YOUR RESPONSE |=>|=>|=>|=>|=>|=>|=>|=>|=> \n\n\n {response.json()} \n\n\n")
#                 return response.json()
#             else:
#                 self.stdout.write(self.style.ERROR(f'Failed to get dashboard data: {response.text}'))
#                 return None
#         except Exception as e:
#             self.stdout.write(self.style.ERROR(f'Error hitting endpoint: {str(e)}'))
#             return None
