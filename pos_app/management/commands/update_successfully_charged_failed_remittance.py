from django.core.management.base import BaseCommand

from pos_app.models import Agent, AgentWallet, FailedRemittanceAgencyWalletCharge


class Command(BaseCommand):
    queryset = FailedRemittanceAgencyWalletCharge.objects.filter(response_payload__icontains="success", successfully_charged=False)

    if queryset:
        for q in queryset:
            agent = Agent.objects.get(id=q.agent.id)
            agent_wallet = AgentWallet.objects.filter(agent=agent).last()

            agent_wallet.game_play_bal += q.amount_charge
            agent_wallet.save()

            q.successfully_charged = True
            q.amount_charge = q.outstanding_remittance
            q.save()
