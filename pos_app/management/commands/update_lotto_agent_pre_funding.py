from django.core.management.base import BaseCommand

from pos_app.models import Agent, AgentConstantVariables, AgentNoPreFunding, AgentWalletTransaction
from wallet_app.models import DebitCreditRecord, UserWallet
from datetime import datetime
import uuid


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        lotto_agents_without_pre_funding = Agent.objects.filter(agent_type="LOTTO_AGENT", has_pre_funding = False)

        for agent in lotto_agents_without_pre_funding:
            if AgentNoPreFunding.objects.filter(phone_number=agent.phone).exists():
                agent.has_pre_funding = True
                agent.save()
                continue
            else:
                # check agent wallet transactions
                pre_funding_transactions = AgentWalletTransaction.objects.filter(agent_wallet__agent__id = agent.id, transaction_from="PRE_FUNDING")
                if pre_funding_transactions.exists():
                    agent.has_pre_funding = True
                    agent.save()
                    continue
                else:
                    lotto_agent_pre_funding_amount = AgentConstantVariables().get_lotto_agent_pre_funding_amount()
                    
                    if lotto_agent_pre_funding_amount > 0:
                        debit_credit_record = DebitCreditRecord.create_record(
                            phone_number=agent.phone,
                            amount=lotto_agent_pre_funding_amount,
                            channel="POS/MOBILE",
                            reference=f"pre_F-{uuid.uuid4()}{datetime.now().timestamp()}",
                            transaction_type="CREDIT",
                        )

                        wallet_payload = {
                            "transaction_from": "PRE_FUNDING",
                        }

                        UserWallet.fund_wallet(
                            user=agent,
                            amount=lotto_agent_pre_funding_amount,
                            channel="POS",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="GAME_PLAY_WALLET",
                            **wallet_payload,
                        )

                        agent.has_pre_funding = True
                        agent.save()
                    else:
                        agent.has_pre_funding = True
                        agent.save()
                        