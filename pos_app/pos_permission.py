from rest_framework import permissions

from main.api.exceptions.execptions import EntryDisallowedException

from .models import Agent


def token_seriliazed(token):
    """
    Serialize token and return user email and user_id
    """
    if token is None:
        return None

    new_token = (token.replace("token ", "")).split(":")
    email = new_token[0]
    user_id = new_token[1]

    return email, user_id


class AgentCanLogin(permissions.BasePermission):
    """ """

    def is_authenticated(self, email, user_id):
        return Agent.objects.filter(email=email, user_id=user_id)

    def has_permission(self, request, view):
        token = request.headers.get("Authorization")

        if token is None:
            return False

        email, user_id = token_seriliazed(token)
        user = self.is_authenticated(email, user_id)

        if user.exists():
            request.user = user.last()

            return True

        else:
            raise EntryDisallowedException()
