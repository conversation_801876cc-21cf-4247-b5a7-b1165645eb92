from django.db.models import TextChoices


class TypeOfAgentDownlinesRestriction(TextChoices):
    TERMINAL_RESTRICTION = "TERMINAL_RESTRICTION", "TERMINAL_RESTRICTION"
    WITHDRAWAL_RESTRICTION = "WIT<PERSON><PERSON>WAL_RESTRICTION", "WITHD<PERSON>WAL_RESTRICTION"


class TypeOfSuperAgent(TextChoices):
    SUPER_AGENT = "SUPER_AGENT", "SUPER_AGENT"
    BUSINESS_DEVELOPMENT = "BUSINESS_DEVELOPMENT", "BUSINESS_DEVELOPMENT"
    RELATIONSHIP_MANAGER = "RELATIONSHIP_MANAGER", "RELATIONSHIP_MANAGER"


class AgentActivityStatus(TextChoices):
    INACTIVE = "INACTIVE", "INACTIVE"
    UNDER_PERFORMING = "UNDER_PERFORMING", "UNDER_PERFORMING"
    PARTIALLY_ACTIVE = "PARTIALLY_ACTIVE", "PARTIALLY_ACTIVE"
    ACTIVE = "ACTIVE", "ACTIVE"
    ABSENT = "ABSENT", "ABSENT"
