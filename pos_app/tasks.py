import csv
import json
import math
import os
import time
import uuid
from datetime import datetime, timedelta
from pathlib import Path
from string import Template

import pytz
import requests
from celery import shared_task
from django.conf import settings
from django.db.models import Avg, Q, Subquery, Sum
from django.utils import timezone

from main.tasks import celery_send_whatsapp_payment_notification_admin
from overide_print import print
from prices.game_price import (
    InstantCashOutPriceModel,
    PosNewQuikaPriceModel,
    QuikaPriceModel,
)
from sms_campaign.helpers.smart_sms import send_sms


def total_paid_amount(lottery, lotto_query_set, LottoTicket, LotteryModel):
    # print("Total paid amount ----------------------------------------------------------------")

    if (lottery.channel == "POS_AGENT" or lottery.channel == "SYSTEM_BONUS") and (lottery.lottery_type == "INSTANT_CASHOUT"):
        # amount_paid = lotto_query_set.last().expected_amount

        instant_cashout_price_model = InstantCashOutPriceModel()
        instant_cashout_price = instant_cashout_price_model.ticket_price(channel="POS", ticket_line=lottery.number_of_ticket)
        amount_paid = instant_cashout_price.get("ticket_price") / lottery.number_of_ticket

    # amount_paid SALARY_FOR_LIFE
    elif (lottery.channel == "POS_AGENT" or lottery.channel == "SYSTEM_BONUS") and (lottery.lottery_type == "SALARY_FOR_LIFE"):
        amount_paid = lottery.expected_amount

    # amount_paid WYSE_CASH
    elif (lottery.channel == "POS_AGENT" or lottery.channel == "SYSTEM_BONUS") and lottery.lottery_type == "WYSE_CASH":
        amount_paid = lottery.expected_amount

    # quika
    elif lottery.lottery_type == "QUIKA":
        if lottery.is_new_quika_game is False:
            amount_paid = lottery.stake_amount
        else:
            quick_tickets = str(lottery.ticket).replace("0", "")
            quick_tickets = [x for x in quick_tickets.split(",") if x != "" and x != 0 and x != "0"]

            quika_price_model = PosNewQuikaPriceModel()

            quika_price_dict = quika_price_model.ticket_price(ticket_line=len(quick_tickets))

            amount_paid = quika_price_dict.get("ticket_price")

    # awoof
    elif lottery.lottery_type == "AWOOF":
        amount_paid = lottery.awoof_stake_amount
    # other games
    else:
        amount_paid = lottery.stake_amount

    print("AMOUNT PAID ----", amount_paid)
    # print("STAKE AMOUNT ----", lottery.stake_amount)
    print("QS COUNT ----", lotto_query_set.count())
    print("LOTTO TYPE ----", lottery.lottery_type)

    return amount_paid


@shared_task
def create_agent_vfd_virtual_wallet(agent_id):
    pass

    """
    CREATE VFD VIRTUAL WALLET FOR AGENT
    """

    from pos_app.models import AgentVFDAccountDetail
    from pos_app.pos_helpers import PosAgentHelper

    from .models import Agent, AgentWallet

    agent = Agent.objects.filter(id=agent_id).last()

    if agent is None:
        return {"status": "error", "message": "Agent does not exist"}

    agent_vfd_account_details = AgentVFDAccountDetail.objects.filter(agent=agent).last()

    agent_wallet = AgentWallet.objects.filter(agent__id=agent_id).last()

    if agent_vfd_account_details is None:
        vfd_agent_virtual_acct_response = PosAgentHelper.create_agent_vfd_wallet(agent)
        if isinstance(vfd_agent_virtual_acct_response, dict):
            if vfd_agent_virtual_acct_response.get("data", {}).get("status") == "success":
                agent_trans_instance = AgentVFDAccountDetail.objects.create(
                    agent=agent,
                    vnuban=vfd_agent_virtual_acct_response.get("data", {}).get("data", {}).get("account_number"),
                    acct_name=vfd_agent_virtual_acct_response.get("data", {}).get("data", {}).get("account_name"),
                    bank_name=vfd_agent_virtual_acct_response.get("data", {}).get("data", {}).get("bank_name"),
                    account_type=vfd_agent_virtual_acct_response.get("data", {}).get("data", {}).get("account_type"),
                    bank_code=vfd_agent_virtual_acct_response.get("data", {}).get("data", {}).get("bank_code"),
                    response=vfd_agent_virtual_acct_response,
                )

                if agent_wallet is None:
                    AgentWallet.objects.create(
                        agent=agent,
                        vfd_account=agent_trans_instance,
                        agent_name=f"{agent.first_name} {agent.last_name}",
                        agent_phone_number=agent.phone,
                        agent_email=agent.email,
                    )

                else:
                    agent_wallet.vfd_account = agent_trans_instance
                    agent_wallet.save()

                return {"status": "success", "message": "Agent VFD wallet created"}
            else:
                return {"status": "error", "message": "Agent VFD wallet not created"}
        else:
            return {"status": "error", "message": "Agent VFD wallet not created"}

    else:
        if agent_wallet is None:
            AgentWallet.objects.create(
                agent=agent,
                vfd_account=agent_vfd_account_details,
                agent_name=f"{agent.first_name} {agent.last_name}",
                agent_phone_number=agent.phone,
                agent_email=agent.email,
            )

        else:
            agent_wallet.vfd_account = agent_vfd_account_details
            agent_wallet.save()

        return {"status": "success", "message": "Agent VFD wallet created"}


@shared_task
def share_pos_payment_across_lottery_pool(phone_number, amount, game_play_id):
    from main.models import LotteryBatch, LotteryModel, LottoTicket
    from wyse_ussd.models import UssdLotteryPayment
    from wyse_ussd.tasks import possible_lottery_or_game_played

    fields_to_exclude = {
        "system_generated_num",
    }
    # automatically populate a list with all fields, except the ones you want to exclude
    [f.name for f in LottoTicket._meta.get_fields() if f.name not in fields_to_exclude and not f.auto_created]

    """
    This task is called when a user has successfully paid for a lottery ticket
    """
    print("game_play_id", game_play_id, "\n\n\n\n")

    # user_profile = UserProfile.objects.filter(phone_number=phone_number).last()

    # get the lottery game play id that this payment was made for
    # game_play_id = (
    #     UssdLotteryPayment.objects.filter(user=user_profile).last().game_play_id
    # )

    # ----------------------------------------------------------------------------------------------
    # get game lottery qs
    lotto_query_set = possible_lottery_or_game_played(game_play_id=game_play_id)

    if lotto_query_set:
        # get a single ticket instance
        ticket_instance = lotto_query_set.last()

        # get stake_amount
        # lottery_stake_amount = get_lottery_stake_amount(lottery_instance=ticket_instance,
        #                                                 ticket_qs=lotto_query_set)

        if ticket_instance.lottery_type == "AWOOF":
            lottery_stake_amount = lotto_query_set.aggregate(Sum("awoof_amount"))["awoof_amount__sum"]

        else:
            lottery_stake_amount = lotto_query_set.aggregate(Sum("stake_amount"))["stake_amount__sum"]
            # check if queryset batch is still active
            if ticket_instance.batch.is_active is True:
                pass

            else:
                # get current batch
                active_batch = LotteryBatch.objects.filter(lottery_type=ticket_instance.lottery_type, is_active=True).last()
                if active_batch:
                    pass
                else:
                    active_batch = LotteryBatch.create_batch(lottery_type=ticket_instance.lottery_type)

                    active_batch.save()
                    lotto_query_set.update(batch=active_batch)

        if (ticket_instance.channel == "POS_AGENT" or ticket_instance.channel == "SYSTEM_BONUS") and (
            ticket_instance.lottery_type == "INSTANT_CASHOUT"
        ):
            instant_cashout_price_model = InstantCashOutPriceModel()
            instant_cashout_price = instant_cashout_price_model.ticket_price(channel="POS", ticket_line=ticket_instance.number_of_ticket)
            lottery_stake_amount = instant_cashout_price.get("ticket_price")

        # NEW QUIKA GAME PRICE PAYMENT IMPLEMENTATION
        if (ticket_instance.channel == "POS_AGENT" or ticket_instance.channel == "SYSTEM_BONUS" or ticket_instance.channel == "MOBILE") and (
            ticket_instance.lottery_type == "QUIKA"
        ):
            print("QUIKA GAME PRICE PAYMENT IMPLEMENTATION")

            if ticket_instance.is_new_quika_game is True:
                quick_tickets = str(ticket_instance.ticket).replace("0", "")
                quick_tickets = [x for x in quick_tickets.split(",") if x != "" and x != 0 and x != "0"]

                quika_price_model = PosNewQuikaPriceModel()

                quika_price_dict = quika_price_model.ticket_price(ticket_line=len(quick_tickets))

                print("quick_tickets", quick_tickets)

                lottery_stake_amount = quika_price_dict.get("ticket_price")
            else:
                quika_price_model = QuikaPriceModel()

                print("ticket_instance.number_of_ticket", ticket_instance.number_of_ticket)

                quika_price_dict = quika_price_model.ticket_price(channel="POS", ticket_line=ticket_instance.number_of_ticket)

                print("quika_price_dict", quika_price_dict)

                lottery_stake_amount = quika_price_dict.get("total_amount")

        if amount < lottery_stake_amount:
            f"{uuid.uuid4()}-{int(time.time())}"

            return {"status": "failed", "message": "Insufficient amount charged"}

        else:
            if (
                (len(lotto_query_set) == 4)
                and (ticket_instance.lottery_type == "INSTANT_CASHOUT")
                and (ticket_instance.channel in ["POS_AGENT", "MOBILE", "SYSTEM_BONUS"])
            ):
                # if lottery type is instant cashout and number of ticket is 4, instead of storing 100 naira for the 4 ticket, store 200 for two ticket and store 0 for the other two
                _counter = 0
                for lottery in lotto_query_set:
                    if _counter < 2:
                        lottery.paid = True
                        lottery.amount_paid = 200
                        lottery.save()
                        _counter += 1
                    else:
                        lottery.paid = True
                        lottery.amount_paid = 0
                        lottery.save()
                        _counter += 1

            else:
                for lottery in lotto_query_set:
                    amount_paid = total_paid_amount(lottery, lotto_query_set, LottoTicket, LotteryModel)
                    lottery.paid = True
                    lottery.amount_paid = amount_paid
                    lottery.save()

            ussd_payment_ins = UssdLotteryPayment.objects.filter(game_play_id=game_play_id).last()
            game_play_id_instance_id = ussd_payment_ins.id

            ussd_payment_ins.is_verified = True
            ussd_payment_ins.is_successful = True
            ussd_payment_ins.save()

            game_play_id = UssdLotteryPayment.objects.filter(~Q(id=game_play_id_instance_id), Q(game_play_id=game_play_id)).delete()

    else:
        return "No game found"

    # -------------------------------------------------------------------------------------------

    # # get game lottery qs
    # lottoticket_qs = LottoTicket.objects.filter(
    #     game_play_id=game_play_id, is_duplicate=False
    # )
    # print("lottoticket_qs tottoticket", lottoticket_qs, "\n\n\n\n")
    # if not lottoticket_qs.exists():
    #     lottoticket_qs = LotteryModel.objects.filter(
    #         game_play_id=game_play_id, is_duplicate=False
    #     )
    #
    #     if not lottoticket_qs.exists():
    #
    #         # soccer cash query
    #         lottoticket_qs = SoccerPrediction.objects.filter(game_id=game_play_id)
    #
    #         if not lottoticket_qs.exists():
    #             return "No game found"
    #
    #         lottery_stake_amount = lottoticket_qs.aggregate(Sum("stake_amount"))[
    #             "stake_amount__sum"
    #         ]
    #
    #         lottery_instance = lottoticket_qs.last()
    #
    #         if amount < lottery_stake_amount:
    #             return "Insufficient funds"
    #         else:
    #             for lottery in lottoticket_qs:
    #                 lottery.paid = True
    #                 lottery.amount_paid = lottery.stake_amount
    #                 lottery.save()
    #
    #             game_play_id_instance_id = (
    #                 UssdLotteryPayment.objects.filter(game_play_id=game_play_id)
    #                     .last()
    #                     .id
    #             )
    #
    #             game_play_id = UssdLotteryPayment.objects.filter(
    #                 ~Q(id=game_play_id_instance_id), Q(game_play_id=game_play_id)
    #             ).delete()
    #
    #             return "Payment successful"
    #
    #     else:
    #         print("lotterymodel ticket found")
    #         # sum lottery stake amount
    #         lottery_stake_amount = lottoticket_qs.aggregate(Sum("stake_amount"))[
    #             "stake_amount__sum"
    #         ]
    #
    #         print("aggregated lottery_stake_amount", lottery_stake_amount, "\n\n\n\n")
    #
    #         # check if lottery batch is still active
    #         lottery_instance = lottoticket_qs.last()
    #         if not lottery_instance.batch.is_active is False:
    #             print("lottery model batch is not active")
    #             # wyse cash active bash
    #             lottery_batch = LotteryBatch.objects.filter(
    #                 lottery_type="WYSE_CASH", is_active=True
    #             ).last()
    #             if lottery_batch is None:
    #                 lottery_batch = LotteryBatch.objects.create(
    #                     lottery_type="WYSE_CASH", is_active=True
    #                 )
    #                 lottoticket_qs.update(batch=lottery_batch)
    #
    #         if amount < lottery_stake_amount:
    #             print("user wallet balance is less than lottery stake amount")
    #             amount_to_deduct_from_wallet = 0
    #
    #             for lottery in lottoticket_qs:
    #                 # get paid amount for pos channel, but the stake/amount_to_be_paid is different for all channels
    #
    #                 amount_paid = lottery.stake_amount
    #                 if (
    #                         lottery.channel == "POS_AGENT"
    #                         or lottery.channel == "SYSTEM_BONUS"
    #                 ):
    #                     amount_paid = (
    #                             LotteryModel()
    #                             .wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
    #                                 band=int(lottery.band),
    #                                 no_of_line=lottoticket_qs.count(),
    #                             )
    #                             .get("stake_amount")
    #                             / lottoticket_qs.count()
    #                     )
    #
    #                 if amount < amount_paid:
    #                     continue
    #                 else:
    #                     amount_to_deduct_from_wallet += amount_paid
    #                     amount -= amount_paid
    #
    #                     lottery.paid = True
    #                     lottery.amount_paid = amount_paid
    #                     lottery.save()
    #
    #             print(
    #                 "amount_to_deduct_from_wallet",
    #                 amount_to_deduct_from_wallet,
    #                 "\n\n\n\n",
    #             )
    #
    #             game_play_id_instance_id = (
    #                 UssdLotteryPayment.objects.filter(game_play_id=game_play_id)
    #                     .last()
    #                     .id
    #             )
    #
    #             game_play_id = UssdLotteryPayment.objects.filter(
    #                 ~Q(id=game_play_id_instance_id), Q(game_play_id=game_play_id)
    #             ).delete()
    #
    #         else:
    #             for lottery in lottoticket_qs:
    #                 # get paid amount for pos channel, but the stake/amount_to_be_paid is different for all channels
    #
    #                 amount_paid = lottery.stake_amount
    #                 if (
    #                         lottery.channel == "POS_AGENT"
    #                         or lottery.channel == "SYSTEM_BONUS"
    #                 ):
    #                     amount_paid = (
    #                         LotteryModel()
    #                             .wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
    #                             band=int(lottery.band),
    #                             no_of_line=1,
    #                         )
    #                             .get("stake_amount")
    #                     )
    #
    #                 lottery.paid = True
    #                 lottery.amount_paid = amount_paid
    #                 lottery.save()
    #
    #             game_play_id_instance_id = (
    #                 UssdLotteryPayment.objects.filter(game_play_id=game_play_id)
    #                     .last()
    #                     .id
    #             )
    #
    #             game_play_id = UssdLotteryPayment.objects.filter(
    #                 ~Q(id=game_play_id_instance_id), Q(game_play_id=game_play_id)
    #             ).delete()
    #
    # else:
    #     # sum lottery stake amount
    #     lottery_stake_amount = lottoticket_qs.aggregate(Sum("stake_amount"))[
    #         "stake_amount__sum"
    #     ]
    #
    #     # check if lottery batch is still active
    #     lottery_instance = lottoticket_qs.last()
    #     if not lottery_instance.batch.is_active is False:
    #
    #         # check lottery type
    #         if lottery_instance.lottery_type == "INSTANT_CASHOUT":
    #             active_batch = LotteryBatch.objects.filter(
    #                 is_active=True, lottery_type="INSTANT_CASHOUT"
    #             ).last()
    #             if active_batch is None:
    #                 active_batch = LotteryBatch.objects.create(
    #                     lottery_type="INSTANT_CASHOUT"
    #                 )
    #                 lottoticket_qs.update(batch=active_batch)
    #
    #         elif lottery_instance.lottery_type == "SALARY_FOR_LIFE":
    #             active_batch = LotteryBatch.objects.filter(
    #                 is_active=True, lottery_type="SALARY_FOR_LIFE"
    #             ).last()
    #
    #             if active_batch is None:
    #                 global_jackpot = LotteryGlobalJackPot.get_jackpot_instance()
    #
    #                 active_batch = LotteryBatch.objects.create(
    #                     lottery_type="SALARY_FOR_LIFE", global_jackpot=global_jackpot
    #                 )
    #
    #                 lottoticket_qs.update(batch=active_batch)
    #     # print("user_wallet_bal", user_wallet_bal, "\n\n\n\n")
    #     # print("lottery_stake_amount", lottery_stake_amount, "\n\n\n\n")
    #     if amount < lottery_stake_amount:
    #         amount_to_deduct_from_wallet = 0
    #
    #         for lottery in lottoticket_qs:
    #             amount_paid = lottery.stake_amount
    #             # get paid amount for pos channel, but the stake/amount_to_be_paid is different for all channels
    #             if (
    #                     lottery.channel == "POS_AGENT" or lottery.channel == "SYSTEM_BONUS"
    #             ) and lottery.lottery_type == "INSTANT_CASHOUT":
    #                 amount_paid = (
    #                         LottoTicket()
    #                         .instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
    #                             ticket_count=lottoticket_qs.count()
    #                         )
    #                         .get("stake_amount")
    #                         / lottoticket_qs.count()
    #                 )
    #
    #             elif (
    #                     lottery.channel == "POS_AGENT" or lottery.channel == "SYSTEM_BONUS"
    #             ) and lottery.lottery_type == "SALARY_FOR_LIFE":
    #                 amount_paid = (
    #                         LottoTicket()
    #                         .salary_for_life_stake_amount_pontential_winning_for_lotto_agents(
    #                             ticket_count=lottoticket_qs.count()
    #                         )
    #                         .get("stake_amount")
    #                         / lottoticket_qs.count()
    #                 )
    #
    #             if amount < amount_paid:
    #                 continue
    #             else:
    #                 amount_to_deduct_from_wallet += amount_paid
    #                 amount -= amount_paid
    #
    #                 lottery.paid = True
    #                 lottery.amount_paid = amount_paid
    #                 lottery.save()
    #
    #         # print(
    #         #     "amount_to_deduct_from_wallet", amount_to_deduct_from_wallet, "\n\n\n\n"
    #         # )
    #         # print("user_wallet_bal", user_wallet_bal, "\n\n\n\n")
    #
    #     else:
    #         for lottery in lottoticket_qs:
    #             amount_paid = lottery.stake_amount
    #
    #             # get paid amount for pos channel, but the stake/amount_to_be_paid is different for all channels
    #             if (
    #                     lottery.channel == "POS_AGENT" or lottery.channel == "SYSTEM_BONUS"
    #             ) and lottery.lottery_type == "INSTANT_CASHOUT":
    #                 amount_paid = (
    #                         LottoTicket()
    #                         .instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
    #                             ticket_count=lottoticket_qs.count()
    #                         )
    #                         .get("stake_amount")
    #                         / lottoticket_qs.count()
    #                 )
    #
    #             elif (
    #                     lottery.channel == "POS_AGENT" or lottery.channel == "SYSTEM_BONUS"
    #             ) and lottery.lottery_type == "SALARY_FOR_LIFE":
    #                 amount_paid = (
    #                         LottoTicket()
    #                         .salary_for_life_stake_amount_pontential_winning_for_lotto_agents(
    #                             ticket_count=lottoticket_qs.count()
    #                         )
    #                         .get("stake_amount")
    #                         / lottoticket_qs.count()
    #                 )
    #
    #             lottery.paid = True
    #             lottery.amount_paid = amount_paid
    #             lottery.save()
    #
    #         game_play_id_instance_id = (
    #             UssdLotteryPayment.objects.filter(game_play_id=game_play_id).last().id
    #         )
    #
    #         game_play_id = UssdLotteryPayment.objects.filter(
    #             ~Q(id=game_play_id_instance_id), Q(game_play_id=game_play_id)
    #         ).delete()


@shared_task
def update_duplicate_lottery_to_paid(game_play_id, lottery_type):
    from main.models import LotteryModel, LottoTicket

    if lottery_type == "WYSE_CASH":
        lottery_queryset = LotteryModel.objects.filter(game_play_id=game_play_id, is_duplicate=True)
    else:
        lottery_queryset = LottoTicket.objects.filter(game_play_id=game_play_id, is_duplicate=True)

    if lottery_queryset:
        for lottery in lottery_queryset:
            lottery.amount_paid = lottery.stake_amount
            lottery.paid = True
            lottery.save()


@shared_task
def handle_retail_lottery_pament_across_pool(game_id, lottery_type):
    from main.models import LotteryModel, LottoTicket

    if lottery_type == "WYSE_CASH":
        lottery_queryset = LotteryModel.objects.filter(game_play_id=game_id)
        if lottery_queryset:
            for lottery in lottery_queryset:
                lottery.amount_paid = lottery.stake_amount
                lottery.paid = True
                lottery.save()

            amount = lottery_queryset.aggregate(Sum("stake_amount"))["stake_amount__sum"]

            celery_send_whatsapp_payment_notification_admin(
                phone_number=lottery_queryset.last().user_profile.phone_number,
                batch_id=lottery_queryset.last().batch.batch_uuid,
                amount=amount,
                paid_via=f"POS AGENT {lottery_queryset.last().agent_profile.first_name} {lottery_queryset.last().agent_profile.last_name} Retail lottery ticket",
            )

    else:
        lottery_queryset = LottoTicket.objects.filter(game_play_id=game_id)

        if lottery_queryset:
            for lottery in lottery_queryset:
                lottery.amount_paid = lottery.stake_amount
                lottery.paid = True
                lottery.save()

                amount = lottery_queryset.aggregate(Sum("stake_amount"))["stake_amount__sum"]

            celery_send_whatsapp_payment_notification_admin(
                phone_number=lottery_queryset.last().user_profile.phone_number,
                batch_id=lottery_queryset.last().batch.batch_uuid,
                amount=amount,
                paid_via=f"POS AGENT {lottery_queryset.last().agent_profile.first_name} {lottery_queryset.last().agent_profile.last_name} Retail lottery ticket",
            )


@shared_task
def celery_task_notify_agent_lottery_won(agent_instance):
    from pos_app.pos_helpers import PosAgentHelper

    pos_agent_helper = PosAgentHelper(agent_instance=agent_instance, amount=10)
    notification = pos_agent_helper.notify_agent_on_lottery_win()

    return (
        "message",
        "agent lottery won notification sent, response: {}".format(notification),
    )


# @shared_task
def verify_guarantor_nin(nin: str):
    url = "http://api.youverify.co/v2/api/identity/ng/nin"

    payload = json.dumps({"id": nin, "isSubjectConsent": True})
    headers = {
        "token": "GUuO6XCe.udOdK8kA3m1QkWwq0spSFioyMFl34zoJLUIC",
        "Content-Type": "application/json",
    }
    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)

    if response.status_code == 200:
        response_json = response.json()
        if response_json.get("data") and response_json["data"]["status"] == "found":
            return {"status": False, "message": "NIN found.", "data": response_json}
        else:
            return {"status": False, "message": "error NIN not found."}
    else:
        return {"status": False, "message": "error fetching NIN record(s)."}


@shared_task
def whisper_message_guarantor(agent_name: str, guarantor_name: str, guarantor_phone: str, ussd_code: str):
    whisper_url = "https://whispersms.xyz/transactional/send"
    whisper_headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }
    payload = json.dumps(
        {
            "receiver": guarantor_phone,
            "template": "00f8d441-4a12-4ea4-9580-2f202db101c2",
            "place_holders": {
                "agent_name": agent_name,
                "guarantor_name": guarantor_name,
                "ussd_code": ussd_code,
            },
        }
    )
    whisper_response = requests.request("POST", whisper_url, headers=whisper_headers, data=payload)
    try:
        response = json.dumps(whisper_response.json())
    except json.JSONDecodeError:
        response = whisper_response.text
    return response


@shared_task
def send_guarantor_verification_details(verification_id: str):
    from pos_app.models import LottoAgentGuarantorDetail

    libertypay_url = "https://backend.libertypayng.com/kyc/verify/guarantor/lotto"
    libertypay_headers = {
        "Authorization": settings.LIBERTYPAY_USER_AUTH,
        "Content-Type": "application/json",
    }
    guarantor = LottoAgentGuarantorDetail.objects.filter(verification_id=verification_id).first()
    payload = json.dumps(
        {
            "verification_unique_id": guarantor.verification_id,
            "guarantor_verified_first_name": guarantor.guarantor_verified_first_name,
            "guarantor_verified_last_name": guarantor.guarantor_verified_last_name,
            "guarantor_verified_address": guarantor.guarantor_verified_address,
            "guarantor_id_type": guarantor.means_of_verification,
            "guarantor_id_number": guarantor.verification_id,
            "guarantor_id_is_verified": guarantor.verified,
            "guarantor_id_verification_payload": {"status": guarantor.verified, "data": guarantor.payload},
        }
    )
    libertypay_response = requests.request("POST", libertypay_url, headers=libertypay_headers, data=payload)
    try:
        response = json.dumps(libertypay_response.json())
    except json.JSONDecodeError:
        response = libertypay_response.text
    guarantor.event_sent = True
    guarantor.libertypay_response = response
    guarantor.save()
    return response


@shared_task
def email_sender(recipient, subject, template_dir, file=None, file_name=None, **substitute):
    TEMPLATE_DIR = os.path.join("templates", template_dir)
    html_temp = os.path.abspath(TEMPLATE_DIR)

    with open(html_temp) as temp_file:
        template = temp_file.read()

    template = Template(template).safe_substitute(substitute)
    try:
        response = requests.post(
            "https://api.mailgun.net/v3/mg.whisperwyse.com/messages",
            auth=("api", settings.MAILGUN_API_KEY),
            data={
                "from": "WinWise <<EMAIL>>",
                "to": recipient,
                "subject": subject,
                "html": template,
            },
            files=[("attachment", (file_name, open(str(file), "rb").read()))],
        )
        print("\n#\nemail sender: ", response)
        return "EMAIL SENT"
    except Exception as error:
        print("failed to send email", error)
        return "EMAIL FAILED"


@shared_task
def smartsms_guarantor_message(verification_id, agent_name, guarantor_name, guarantor_phone, ussd):
    content = f"""
Hello {guarantor_name},\n
{agent_name} is onboarding as a WinWise Lottery Agent, and wants you to be a guarantor.\n
Dial {ussd} to complete this process.
    """

    smartsms_response = send_sms(
        message_content=content,
        recipients=guarantor_phone,
        batch_uuid=verification_id,
        sender="Liberty",
        routing=3,
    )
    if smartsms_response.get("success"):
        print("\nGUARANTOR VERIFICATION MESSAGE     =====>      MESSAGE SENT\n")
    else:
        print("\nGUARANTOR VERIFICATION MESSAGE     =====>      MESSAGE NOT SENT\n")


@shared_task
def remittance_drip():
    from pos_app.models import Agent, LottoAgentRemittanceTable
    from wyse_ussd.models import random_with_N_digits

    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    TODAYS_DATE = TODAY.date()

    # Remittance threshold for 10:00 am daily
    if TODAY.hour >= 10:
        # Fetch all agents with pending remittance
        pending_remittance_queryset = LottoAgentRemittanceTable.objects.filter(due=True).values("agent", "amount", "days_due")

        # Get agent contact and send customized message
        for data in pending_remittance_queryset:
            agent_id = data.get("agent")
            amount = data.get("amount")
            days_due = data.get("days_due")

            agent = Agent.objects.get(id=agent_id)

            text_one = f"""
Hello {agent.first_name},\n
Your remittance of #{amount} to WinWise Lotto is {days_due} day(s) DUE.\n
Fund your wallet to avoid suspension.\n
Call: *********
            """

            text_two = f"""
Hello {agent.first_name},\n
Your remittance of #{amount} to WinWise Lotto is {days_due} day(s) DUE.\n
Fund your wallet to avoid POS retrieval.\n
Call: *********
            """

            text = text_one if days_due <= 5 else text_two
            smartsms_response = send_sms(
                message_content=text,
                recipients=agent.phone,
                batch_uuid=str(f"ARD-TWO-{TODAYS_DATE}-{random_with_N_digits()[-4:]}"),
                sender="Liberty",
                routing=3,
            )
            if smartsms_response.get("success"):
                print("\nAGENT REMITTANCE DRIP     =====>      MESSAGE SENT\n")
            else:
                print("\nAGENT REMITTANCE DRIP     =====>      MESSAGE NOT SENT\n")
    else:
        print("\nAGENT REMITTANCE DRIP     =====>      THRESHOLD NOT MET\n")


@shared_task
def daily_remittance_report():
    from pos_app.utils import AgentsRemittanceReport

    TODAYS_DATE = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()
    start_date = TODAYS_DATE - timedelta(days=1)

    report_status = AgentsRemittanceReport.generate_report(report_type="Daily", start_date=start_date)
    return report_status


@shared_task
def weekly_remittance_report():
    from pos_app.utils import AgentsRemittanceReport

    TODAYS_DATE = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()

    if TODAYS_DATE.weekday() == 4:
        start_date = TODAYS_DATE - timedelta(days=7)
        end_date = TODAYS_DATE - timedelta(days=1)

        report_status = AgentsRemittanceReport.generate_report(report_type="Weekly", start_date=start_date, end_date=end_date)
        return report_status

    return {"message": "false, time threshold not met."}


@shared_task
def monthly_remittance_report():
    from pos_app.utils import AgentsRemittanceReport

    TODAYS_DATE = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()

    if TODAYS_DATE.day == 1:
        end_date = TODAYS_DATE - timedelta(days=1)
        start_date = end_date.replace(day=1)

        report_status = AgentsRemittanceReport.generate_report(report_type="Monthly", start_date=start_date, end_date=end_date)
        return report_status

    return {"message": "false, time threshold not met."}


@shared_task
def celery_create_vfd_virtual_account_for_agents():
    from pos_app.models import AgentWallet

    agent__wallets = AgentWallet.objects.filter(vfd_account__isnull=True)

    if agent__wallets:
        for agent__wallet in agent__wallets:
            create_agent_vfd_response = create_agent_vfd_virtual_wallet(agent_id=agent__wallet.agent.id)
            print("create_agent_vfd_response", create_agent_vfd_response)

        return {"message": "vfd account created for agent"}
    else:
        return {"message": "no agent to create vfd account for"}


@shared_task
def check_if_agent_has_super_agent(agent_id):
    # from pos_app.pos_helpers import get_agents_downline

    # return get_agents_downline(agent_id=agent_id)
    pass


@shared_task
def check_if_agent_has_sales_rep_super_agent_or_supervisor():
    from pos_app.pos_helpers import get_agent_sales_rep_super_agent_and_supervisor

    # agents = Agent.objects.filter()

    return get_agent_sales_rep_super_agent_and_supervisor()


@shared_task
def celery_trigger_agent_reward(agent_id):
    from pos_app.models import Agent, AgentWallet
    from pos_app.pos_helpers import PosAgentHelper

    if settings.DEBUG is True or settings.DEBUG:
        return True

    agent = Agent.objects.get(id=agent_id)
    agent_wallet = AgentWallet.objects.filter(agent__id=agent_id).last()
    if agent_wallet is not None and agent_wallet.commission_bal > 0:
        amount = agent_wallet.commission_bal
        # agent_wallet.commission_bal -= amount
        # agent_wallet.save()

        # if agent.super_agent is None:
        #     AgentWalletTransaction.objects.create(
        #         agent_wallet=agent_wallet,
        #         transaction_from="COMMISSION_REWARD",
        #         transaction_type="DEBIT",
        #         amount=amount,
        #     )

        pos_agent_helper = PosAgentHelper(
            agent_instance=agent,
            amount=amount,
        )

        pos_agent_helper.agent_commission_reward(
            narration="COMMISSION_REWARD",
            winning_commision=False,
            agent_has_remitted=True,
        )
        return {"message": "agent reward triggered"}


@shared_task
def celery_get_update_agent_terminal_id(agent_id=0):
    from pos_app.models import Agent
    from pos_app.pos_helpers import get_agent_detail_type

    agents = Agent.objects.filter(
        Q(agent_type="LOTTO_AGENT") | Q(agent_type="AGENT") | Q(agent_type="LIBERTY_RETAIL"),
        Q(terminal_id__isnull=True),
    )

    message = None
    for agent in agents:
        liberty_pay_agent_type = get_agent_detail_type(agent_id=agent.user_id)

        if isinstance(liberty_pay_agent_type, dict):
            terminal_id = liberty_pay_agent_type.get("terminal_id")
            if (terminal_id is None) or (terminal_id == ""):
                message = {"message": "no terminal id"}
            else:
                agent.terminal_id = terminal_id
                agent.save()
                message = {"message": "terminal id updated"}

        else:
            message = {"message": "no terminal id"}

    return message


@shared_task
def remittance_drip_two():
    from pos_app.models import Agent, LottoAgentRemittanceTable
    from wyse_ussd.models import random_with_N_digits

    TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
    TODAYS_DATE = TODAY.date()

    # Remittance threshold for 04:00 pm daily
    if TODAY.hour >= 16:
        # Fetch all agents with pending remittance
        pending_remittance_queryset = LottoAgentRemittanceTable.objects.filter(due=True).values("agent", "amount", "days_due")

        # Get agent contact and send customized message
        for data in pending_remittance_queryset:
            agent_id = data.get("agent")
            amount = data.get("amount")
            days_due = data.get("days_due")

            agent = Agent.objects.get(id=agent_id)

            text_one = f"""
Hello {agent.first_name},\n
Your remittance of #{amount} to WinWise Lotto is {days_due} day(s) DUE.\n
Fund your wallet to avoid suspension.\n
Call: *********
            """

            text_two = f"""
Hello {agent.first_name},\n
Your remittance of #{amount} to WinWise Lotto is {days_due} day(s) DUE.\n
Fund your wallet to avoid POS retrieval.\n
Call: *********
            """

            text = text_one if days_due <= 5 else text_two
            smartsms_response = send_sms(
                message_content=text,
                recipients=agent.phone,
                batch_uuid=str(f"ARD-ONE-{TODAYS_DATE}-{random_with_N_digits()[-4:]}"),
                sender="Liberty",
                routing=3,
            )
            if smartsms_response.get("success"):
                print("\nAGENT REMITTANCE DRIP     =====>      MESSAGE SENT\n")
            else:
                print("\nAGENT REMITTANCE DRIP     =====>      MESSAGE NOT SENT\n")
    else:
        print("\nAGENT REMITTANCE DRIP     =====>      THRESHOLD NOT MET\n")


@shared_task
def charge_all_agents_that_failed_to_remit():
    """

    CHARGE AGENT AGENCY BANKING WALLET WHEN THEY REFUSE TO REMIT FOR
    SOME DAYS

    """

    from pos_app.models import (
        Agent,
        AgentSuspensionRequestToAgencyBanking,
        AgentWallet,
        FailedRemittanceAgencyWalletCharge,
        LottoAgentRemittanceTable,
    )
    from pos_app.pos_helpers import PosAgentHelper
    from wallet_app.models import DebitCreditRecord, UserWallet

    defualted_agent_remittance_queryset = LottoAgentRemittanceTable.objects.filter(remitted=False, days_due__gte=1, terminal_retrieved=False)
    # defualted_agent_remittance_queryset = LottoAgentRemittanceTable.objects.filter(
    #     id = 13072
    # )

    if defualted_agent_remittance_queryset:
        pass

        for remittance in defualted_agent_remittance_queryset:
            agent = Agent.objects.get(id=remittance.agent.id)

            print("checking for agent", agent.user_id, "remittance")

            if agent.suspended_on_agency_banking is True:
                continue

            amount_to_charge = remittance.amount - remittance.amount_paid

            payload = {
                "service_name": "LOTTO_PLAY",
                "user_id": agent.user_id,
                "narration": "REMITTANCE_DEDUCTION",
                "total_amount": math.ceil(amount_to_charge),
                "service_comm": math.ceil(amount_to_charge),
                "agent_comm": 0,
            }

            record_instance = FailedRemittanceAgencyWalletCharge.objects.create(
                agent=agent,
                outstanding_remittance=math.ceil(amount_to_charge),
                payload=payload,
                trans_ref=FailedRemittanceAgencyWalletCharge().generate_transaction_ref(),
            )

            cp_payload = payload
            cp_payload["transaction_pin"] = 0000
            cp_payload["unique_reference"] = record_instance.trans_ref

            pos_agent_helper = PosAgentHelper(
                agent_instance=agent,
                amount=0,
                pin=0,
            )

            deduction_response = pos_agent_helper.charge_defaulted_remittance(**cp_payload)

            record_instance.response_payload = deduction_response
            record_instance.save()

            if isinstance(deduction_response, dict):
                # check if the deduction response contains error message
                if deduction_response.get("error"):
                    error_code = deduction_response.get("error")
                    success_error_message = deduction_response.get("message")

                    if "1621" == error_code or "does not have" in success_error_message:
                        agent_balance_response = deduction_response.get("user_balance")
                        if agent_balance_response is None:
                            if remittance.days_due > 2:
                                celery_suspend_agent_on_agency_banking(
                                    agent_id=agent.id,
                                    reason=f"AGENT FAILED TO REMIT {amount_to_charge} to winwise wallet",
                                )

                        else:
                            amount = float(agent_balance_response)
                            if (amount < 100) and (remittance.days_due > 2):
                                celery_suspend_agent_on_agency_banking(
                                    agent_id=agent.id,
                                    reason=f"AGENT FAILED TO REMIT {amount_to_charge} to winwise wallet",
                                )

                            else:
                                charge_agent_that_failed_to_remit.delay(agent.id, amount)

                    elif "success" in str(success_error_message).casefold():
                        agent_wallet = AgentWallet.objects.filter(agent=agent).last()
                        if agent_wallet:
                            debit_credit_record = DebitCreditRecord.create_record(
                                phone_number=agent_wallet.agent.phone,
                                amount=amount_to_charge,
                                channel="POS",
                                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                                transaction_type="CREDIT",
                            )

                            wallet_payload = {
                                "transaction_from": "REMITTANCE",
                            }

                            UserWallet.fund_wallet(
                                user=agent_wallet.agent,
                                amount=int(amount_to_charge),
                                channel="POS",
                                transaction_id=debit_credit_record.reference,
                                user_wallet_type="GAME_PLAY_WALLET",
                                **wallet_payload,
                            )

                            record_instance.successfully_charged = True
                            record_instance.amount_charge = amount_to_charge
                            record_instance.save()

                            # agent_wallet.game_play_bal += amount_to_charge
                            # agent_wallet.save()

                    else:
                        suspension_payload = {
                            "user_id": agent.user_id,
                            "suspend": "true",
                            "suspend_or_unsuspend_reason": f"AGENT FAILED TO REMIT {amount_to_charge} to winwise wallet",
                        }

                        # create a suspension record
                        suspension_record_instance = AgentSuspensionRequestToAgencyBanking.objects.create(
                            agent=agent,
                            reason=f"AGENT FAILED TO REMIT {amount_to_charge} to winwise wallet",
                            payload=suspension_payload,
                        )

                        suspension_response = pos_agent_helper.suspend_lotto_agent_on_agency_banking(**suspension_payload)
                        suspension_record_instance.refresh_from_db = suspension_response
                        suspension_record_instance.save()

                        cp_instance = suspension_record_instance

                        if isinstance(suspension_response, dict):
                            if (str(suspension_response.get("status")).lower()) == "success":
                                agent.suspended_on_agency_banking = True
                                agent.suspending_from_task = True
                                agent.save()

                                cp_instance.status = "APPROVED"
                                cp_instance.save()

                            elif suspension_response.get("error") == "478":
                                agent.suspended_on_agency_banking = True
                                agent.suspending_from_task = True
                                agent.save()

                                cp_instance.status = "APPROVED"
                                cp_instance.save()

                elif "success" in str(deduction_response.get("message")).casefold():
                    agent_wallet = AgentWallet.objects.filter(agent=agent).last()
                    if agent_wallet:
                        debit_credit_record = DebitCreditRecord.create_record(
                            phone_number=agent_wallet.agent.phone,
                            amount=amount_to_charge,
                            channel="POS",
                            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                            transaction_type="CREDIT",
                        )

                        wallet_payload = {
                            "transaction_from": "REMITTANCE",
                        }

                        UserWallet.fund_wallet(
                            user=agent_wallet.agent,
                            amount=int(amount_to_charge),
                            channel="POS",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="GAME_PLAY_WALLET",
                            **wallet_payload,
                        )

                        # agent_wallet.game_play_bal += math.ceil(amount_to_charge)
                        # agent_wallet.save()

            # transaction.on_commit(lambda: remittance.refresh_from_db())

            remittance.refresh_from_db()

    else:
        print("no agent to charge")


@shared_task
def charge_agent_that_failed_to_remit(agent_id, amount_to_charge):
    from pos_app.models import Agent, AgentWallet, FailedRemittanceAgencyWalletCharge
    from pos_app.pos_helpers import PosAgentHelper
    from wallet_app.models import DebitCreditRecord, UserWallet

    agent = Agent.objects.get(id=agent_id)

    payload = {
        "service_name": "LOTTO_PLAY",
        "user_id": agent.user_id,
        "narration": "REMITTANCE_DEDUCTION",
        "total_amount": amount_to_charge,
        "service_comm": amount_to_charge,
        "agent_comm": 0,
    }

    record_instance = FailedRemittanceAgencyWalletCharge.objects.create(
        agent=agent,
        outstanding_remittance=amount_to_charge,
        payload=payload,
        trans_ref=FailedRemittanceAgencyWalletCharge().generate_transaction_ref(),
    )

    copy_payload = payload
    copy_payload["transaction_pin"] = 0000
    copy_payload["unique_reference"] = record_instance.trans_ref

    pos_agent_helper = PosAgentHelper(
        agent_instance=agent,
        amount=0,
        pin=0,
    )

    pos_agent_helper = PosAgentHelper(
        agent_instance=agent,
        amount=0,
        pin=0,
    )

    deduction_response = pos_agent_helper.charge_defaulted_remittance(**copy_payload)

    record_instance.response_payload = deduction_response
    record_instance.save()

    if isinstance(deduction_response, dict):
        # message = deduction_response.get("message")
        if str(deduction_response.get("message")).casefold == "success":
            agent_wallet = AgentWallet.objects.filter(agent=agent).last()
            if agent_wallet is None:
                return {"error": "1621", "message": "Agent does not have a wallet"}

            amount = deduction_response.get("data", {}).get("amount_sent")

            if amount is None:
                return {"message": "invalid amount"}

            amount_to_add = amount

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=agent_wallet.agent.phone,
                amount=amount_to_add,
                channel="POS",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="CREDIT",
            )

            wallet_payload = {
                "transaction_from": "REMITTANCE",
            }

            UserWallet.fund_wallet(
                user=agent_wallet.agent,
                amount=int(amount_to_add),
                channel="POS",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="GAME_PLAY_WALLET",
                **wallet_payload,
            )

            # agent_wallet.game_play_bal += amount_to_add
            # agent_wallet.save()

            record_instance.amount_charge += math.ceil(amount_to_add)
            record_instance.successfully_charged = True
            record_instance.save()

    return {"response": deduction_response}


@shared_task
def update_successfully_charged_remittance():
    from pos_app.models import Agent, AgentWallet, FailedRemittanceAgencyWalletCharge
    from wallet_app.models import DebitCreditRecord, UserWallet

    queryset = FailedRemittanceAgencyWalletCharge.objects.filter(response_payload__icontains="success", successfully_charged=False)

    if queryset:
        for q in queryset:
            agent = Agent.objects.get(id=q.agent.id)
            agent_wallet = AgentWallet.objects.filter(agent=agent).last()

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=agent_wallet.agent.phone,
                amount=q.outstanding_remittance,
                channel="POS",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="CREDIT",
            )

            wallet_payload = {
                "transaction_from": "REMITTANCE",
            }

            UserWallet.fund_wallet(
                user=agent_wallet.agent,
                amount=int(q.outstanding_remittance),
                channel="POS",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="GAME_PLAY_WALLET",
                **wallet_payload,
            )

            # agent_wallet.game_play_bal += q.outstanding_remittance
            # agent_wallet.save()

            q.successfully_charged = True
            q.amount_charge = q.outstanding_remittance
            q.save()

        q.refresh_from_db()
        agent_wallet.refresh_from_db()


@shared_task
def agent_performance_status():
    from pos_app.models import Agent, AgentWalletTransaction

    three_weeks_ago = timezone.now() - timedelta(weeks=3)

    print("three_weeks_ago", three_weeks_ago.date())

    agents = Agent.objects.filter(created_date__date__lt=three_weeks_ago.date())
    if agents:
        for agent in agents:
            avg_daily_sales = AgentWalletTransaction.objects.filter(
                transaction_from="GAME_PLAY",
                agent_wallet__agent__id=agent.id,
                date_created__date=timezone.now().date(),
            ).aggregate(Avg("amount"))["amount__avg"]

            print("avg_daily_sales", avg_daily_sales)

            if avg_daily_sales is None:
                Agent.objects.filter(id=agent.id).update(performance_status="INACTIVE")
            else:
                if (avg_daily_sales < 10000) and (avg_daily_sales > 2000):
                    Agent.objects.filter(id=agent.id).update(performance_status="PERFORMING")

                elif (avg_daily_sales <= 1000) and (avg_daily_sales > 100):
                    Agent.objects.filter(id=agent.id).update(performance_status="UNDER_PERFORMING")

                elif avg_daily_sales < 100:
                    Agent.objects.filter(id=agent.id).update(performance_status="INACTIVE")

                elif (avg_daily_sales > 10000) and ((avg_daily_sales < 20000)):
                    Agent.objects.filter(id=agent.id).update(performance_status="HIGH_PERFORMING")

                elif (avg_daily_sales > 20000) and ((avg_daily_sales < 40000)):
                    Agent.objects.filter(id=agent.id).update(performance_status="TOP_PERFORMING")

                elif avg_daily_sales > 40000:
                    Agent.objects.filter(id=agent.id).update(performance_status="SUPER_PERFORMING")

    else:
        print("No agent found")


@shared_task
def check_pos_lottery_winners_table_to_payout():
    from main.models import PayoutTransactionTable
    from pos_app.models import PosLotteryWinners

    post_lottery_winners = PosLotteryWinners.objects.all()

    data_header = [
        "phone",
        "agent_name",
        "game_play_id",
        "amount",
        "total_amount",
        "count_of_attempt",
        "count_of_successful_disbursement",
    ]

    data = []

    for winner_instance in post_lottery_winners:
        payout_transactions = PayoutTransactionTable.objects.filter(game_play_id=winner_instance.game_id)
        if payout_transactions.exists():
            succssfully_disbursed = payout_transactions.filter(disbursed=True).count()
            if succssfully_disbursed > 1:
                loop_data = [
                    winner_instance.agent.phone,
                    winner_instance.agent.full_name,
                    winner_instance.game_id,
                    winner_instance.amount_won,
                    payout_transactions.aggregate(Sum("amount"))["amount__sum"],
                    payout_transactions.count(),
                    succssfully_disbursed,
                ]

                data.append(loop_data)

    whatsapp_res = None

    if data:
        with open(
            "pickyfolder/pos_lottery_winners_payout.csv",
            "w",
            encoding="UTF-8",
            newline="",
        ) as f:
            writer = csv.writer(f)
            writer.writerow(data_header)
            writer.writerows(data)

            BASE_DIR_SET = Path(__file__).resolve().parent.parent
            file_path = os.path.join(BASE_DIR_SET, "pickyfolder")

            new_file_path = os.path.join(file_path, "pos_lottery_winners_payout.csv")
            if os.stat(new_file_path).st_size == 0:
                return "NO data"

            host_server_site = settings.WINWISE_LOTTO_BACKEND_URL

            url = f"{host_server_site}/liberty/download_media_file/pos_lottery_winners_payout.csv"

            picky_url = "https://pickyassist.com/app/api/v2/push"

            # payload = {
            #     "token": f"{settings.PICKY_ASSIST_TOKEN}",
            #     "group_id": settings.AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID,
            #     "application": "10",
            #     "media_file": "vfd_failed_loans.csv",
            #     "globalmessage": f"Hello, \n-----------------------------\n\nPlease find attached link to the list of failed VFD loans(VFD LOAN SERVICE) \nLink: {url}\n\n--------------------------------------",
            #     "globalmedia": url,
            # }

            # phones = ["2348077469471", "2347039115243"]
            phones = [
                "2347039115243",
            ]

            for phone_number in phones:
                payload = {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "priority ": "0",
                    "application": "10",
                    "data": [
                        {
                            "number": phone_number,
                            "message": f"Link: {url}\n\n--------------------------------------",
                            "media_file": "pos_lottery_winners_payout.csv",
                            "globalmedia": url,
                        }
                    ],
                }

                headers = {"Content-type": "application/json"}

                try:
                    whatsapp_response = requests.request("POST", picky_url, headers=headers, json=payload)
                    whatsapp_res = whatsapp_response

                except requests.exceptions.RequestException as err:
                    whatsapp_res = {"status": "error", "message": err}

            return f"Whatsapp Message sent to admin response {whatsapp_res}"

    else:
        return


@shared_task
def remittance_reconciliation():
    from pos_app.models import (
        AgentFundingTable,
        AgentWalletTransaction,
        FailedRemittanceAgencyWalletCharge,
        LottoAgentRemittanceTable,
    )

    agents_with_pre_funding = AgentWalletTransaction.objects.filter(transaction_from="PRE_FUNDING")

    data_header = [
        "phone",
        "email",
        "name",
        "account_number",
        "pre_funding",
        "total_amount_paid_on_remittance_table",
        "total_amount_manually_charged",
        "total_amount_marked_as_successfully_charge_but_was_not_charged",
        "total_remittance_on_transaction_table",
        "total_amount_on_funding_table",
        "excess_funding",
    ]

    data = []

    if agents_with_pre_funding:
        for wallet_instance in agents_with_pre_funding:
            yesterday_date = datetime.now() - timedelta(days=1)

            remittance_transactions = AgentWalletTransaction.objects.filter(
                Q(agent_wallet__id=wallet_instance.agent_wallet.id),
                Q(transaction_from="REMITTANCE") | Q(transaction_from="REMITTANCE_EXCESS"),
                Q(date_created__date__lte=yesterday_date.date()),
            ).aggregate(Sum("amount"))["amount__sum"]
            total_prefunding = wallet_instance.amount

            remittance_table_transactions = LottoAgentRemittanceTable.objects.filter(
                agent__id=wallet_instance.agent_wallet.agent.id,
                created_at__date__lte=yesterday_date.date(),
            ).aggregate(Sum("amount_paid"))["amount_paid__sum"]

            manual_charge = FailedRemittanceAgencyWalletCharge.objects.filter(
                agent__id=wallet_instance.agent_wallet.agent.id,
                successfully_charged=True,
                created_at__date__lte=yesterday_date.date(),
            ).aggregate(Sum("amount_charge"))["amount_charge__sum"]

            total_transaction_on_funding_table_without_duplicate = AgentFundingTable.objects.filter(
                pk__in=Subquery(
                    AgentFundingTable.objects.filter(
                        agent__id=wallet_instance.agent_wallet.agent.id,
                        is_verified=True,
                        created_at__date__lte=yesterday_date.date(),
                    )
                    .distinct("reference")
                    .values("pk")
                )
            ).aggregate(Sum("amount"))["amount__sum"]

            total_transaction_on_funding_table = AgentFundingTable.objects.filter(
                agent__id=wallet_instance.agent_wallet.agent.id,
                is_verified=True,
                created_at__date__lte=yesterday_date.date(),
            ).aggregate(Sum("amount"))["amount__sum"]

            if total_transaction_on_funding_table_without_duplicate is None:
                total_transaction_on_funding_table_without_duplicate = 0

            if total_transaction_on_funding_table is None:
                total_transaction_on_funding_table = 0

            diff_ = total_transaction_on_funding_table - total_transaction_on_funding_table_without_duplicate

            failed_remittance_marked_as_successful_data = {
                "<EMAIL>": [
                    "RC-0e2e6300-4816-42da-b54b-1b25e6314ad1",
                    "RC-9ce1d4ec-e57a-469e-be0c-a63d9e00f5ab",
                ],
                "<EMAIL>": ["RC-4eaa0625-61cd-4d0a-b4d9-e09a52eded5a"],
                "<EMAIL>": ["RC-7017c259-0461-4af2-be68-187a7f0634c0"],
                "<EMAIL>": ["RC-9c8d1ee2-4c63-485b-bb83-be0c395cdb7d"],
                "<EMAIL>": ["RC-2e03caf6-34d6-48cb-a2f6-2736fef0caf1"],
                "<EMAIL>": [
                    "RC-4173896e-d460-484d-a392-ebf45d809048",
                    "RC-be58aed1-de64-40b9-beee-c54d660580a4",
                ],
                "<EMAIL>": ["RC-a9bc7895-bc0f-45c1-abca-07687c8d3879"],
                "<EMAIL>": ["RC-3271dd41-c66c-481a-b32b-ab13154cc89f"],
                "<EMAIL>": ["RC-b3b60ca1-cf5d-49f7-b1a5-1523d1a75afe"],
                "<EMAIL>": [
                    "RC-3d0b24c8-ea6b-477a-9b9a-891c76174592",
                    "RC-ec05699d-a6da-4de9-943d-d5e8f7e6d446",
                ],
                "<EMAIL>": ["RC-72f5c64b-af6d-445a-ad6a-a0a76d4f7b26"],
                "<EMAIL>": ["RC-6a34a410-1a1e-4a28-a4e4-20b08e479ef1"],
                "<EMAIL>": ["RC-2142273b-cecb-4265-a16a-11c4bc79c12c"],
                "<EMAIL>": ["RC-c33c3606-f794-415b-a6b2-a5b96d559a14"],
                "<EMAIL>": [
                    "RC-ce374ab5-9a4d-489e-afa0-c99cc7340b07",
                    "RC-7ac20fe8-f863-40ed-916c-270302e2d59b",
                ],
                "<EMAIL>": ["RC-3594c3a5-898f-4193-bbd7-c252b06e5430"],
                "<EMAIL>": ["RC-53930111-b7d7-44b9-9f67-6617b885eb36"],
                "<EMAIL>": ["RC-32eb5c57-b9bf-48ae-a2ea-1ae45c6f41a4"],
                "<EMAIL>": ["RC-5f0ba8ec-bd25-4101-b44d-aa85873b08b9"],
                "<EMAIL>": ["RC-cd9d9e72-8bfd-4977-a95a-46fda7ff28a7"],
                "<EMAIL>": ["RC-f9a0a7e3-ee62-4769-83a1-bfb9325fa5ed"],
                "<EMAIL>": ["RC-74d0e6a4-2cd1-4dec-9b3f-b31a5e705de4"],
                "<EMAIL>": ["RC-b94ef757-b54a-4a3e-901f-27317e473da2"],
                "<EMAIL>": ["RC-ea7b5f52-e5f7-4408-939d-6446f39d4922"],
                "<EMAIL>": [
                    "RC-e456273b-c996-4523-9673-d4bf61b7d367",
                    "RC-380daa8e-b4c0-41cf-8105-a816267c8f5b",
                ],
                "<EMAIL>": [
                    "RC-f62847ce-c60a-44a9-ad24-43cdecaace3b",
                    "RC-8b893f6f-bcbb-44e8-a819-28a1283ff112",
                ],
                "<EMAIL>": ["RC-41187aba-ce57-4200-8a37-32da2583ad57"],
                "<EMAIL>": ["RC-d7b5a0d3-a54e-444c-b5c9-2638293d8c40"],
                "<EMAIL>": ["RC-9f976d67-55f4-4618-abbf-f365793e17a0"],
                "<EMAIL>": ["RC-842facb5-304e-47b6-9973-795cc3e4e59f"],
                "<EMAIL>": ["RC-b889befc-e424-4231-af70-fb2bda95675c"],
                "<EMAIL>": ["RC-10eb0a5a-39da-49cc-8342-ac9d3824779f"],
                "<EMAIL>": ["RC-b9a2a30f-d342-42c5-bda8-0ad71f694b8a"],
                "<EMAIL>": ["RC-6760d3d6-6e36-4cd0-8d81-9645838cf5f2"],
                "<EMAIL>": ["RC-4f2eeaeb-61b2-414a-9e2b-3b8013a378d4"],
                "<EMAIL>": ["RC-b068c22e-c1c1-4ee0-bc16-13e67ad3301d"],
                "<EMAIL>": ["RC-305c79d4-5251-48d2-8e77-8fc5f4e33c5e"],
                "<EMAIL>": ["RC-f339c3a7-407c-491e-a675-5c635327001b"],
                "<EMAIL>": ["RC-5d1709a3-f64a-421e-950f-6d5a0671edb8"],
                "<EMAIL>": ["RC-b1c6118c-32b0-422c-8107-e7a0f4025c5b"],
            }

            if failed_remittance_marked_as_successful_data.get(wallet_instance.agent_wallet.agent.email) is not None:
                _failed_but_marked_as_successful = FailedRemittanceAgencyWalletCharge.objects.filter(
                    trans_ref__in=failed_remittance_marked_as_successful_data.get(wallet_instance.agent_wallet.agent.email)
                ).aggregate(Sum("amount_charge"))["amount_charge__sum"]
                # FailedRemittanceAgencyWalletCharge.objects.filter(trans_ref__in = failed_remittance_marked_as_successful_data.get(wallet_instance.agent_wallet.agent.email)).aggregate(Sum("amount_charge")).update(successfully_charged = False)

            else:
                _failed_but_marked_as_successful = 0

            loop_data = [
                wallet_instance.agent_wallet.agent.phone,
                wallet_instance.agent_wallet.agent.email,
                wallet_instance.agent_wallet.agent.full_name,
                wallet_instance.agent_wallet.vfd_account.vnuban if wallet_instance.agent_wallet.vfd_account else None,
                total_prefunding,
                remittance_table_transactions,
                manual_charge,
                _failed_but_marked_as_successful,
                remittance_transactions,
                total_transaction_on_funding_table_without_duplicate,
                diff_,
            ]

            data.append(loop_data)

    whatsapp_res = None

    if data:
        with open(
            "pickyfolder/agent_remittance_reconciliation.csv",
            "w",
            encoding="UTF-8",
            newline="",
        ) as f:
            writer = csv.writer(f)
            writer.writerow(data_header)
            writer.writerows(data)

            BASE_DIR_SET = Path(__file__).resolve().parent.parent
            file_path = os.path.join(BASE_DIR_SET, "pickyfolder")

            new_file_path = os.path.join(file_path, "agent_remittance_reconciliation.csv")
            if os.stat(new_file_path).st_size == 0:
                return "NO data"

            host_server_site = settings.WINWISE_LOTTO_BACKEND_URL

            url = f"{host_server_site}/liberty/download_media_file/agent_remittance_reconciliation.csv"

            picky_url = "https://pickyassist.com/app/api/v2/push"

            phones = [
                "2347039115243",
            ]

            for phone_number in phones:
                payload = {
                    "token": f"{settings.PICKY_ASSIST_TOKEN}",
                    "priority ": "0",
                    "application": "10",
                    "data": [
                        {
                            "number": phone_number,
                            "message": f"Link: {url}\n\n--------------------------------------",
                            "media_file": "agent_remittance_reconciliation.csv",
                            "globalmedia": url,
                        }
                    ],
                }

                headers = {"Content-type": "application/json"}

                try:
                    whatsapp_response = requests.request("POST", picky_url, headers=headers, json=payload)
                    whatsapp_res = whatsapp_response

                except requests.exceptions.RequestException as err:
                    whatsapp_res = {"status": "error", "message": err}

            return f"Whatsapp Message sent to admin response {whatsapp_res}"

    else:
        return


@shared_task
def celery_agent_commission_reward(agent_id, amount):
    from pos_app.models import Agent
    from pos_app.pos_helpers import PosAgentHelper

    agent = Agent.objects.get(id=agent_id)

    pos_agent_helper = PosAgentHelper(
        agent_instance=agent,
        amount=amount,
    )

    pos_agent_helper.agent_commission_reward(
        narration="COMMISSION_ON_GAME_PLAY",
        winning_commision=False,
    )
    return "AGENT COMMISSION REWARD"


@shared_task
def daily_performance_report():
    """
    Agent(s) performance/scoreboard report on ticket sales.
    """
    from pos_app.utils import AgentsPerformancesReport

    TODAYS_DATE = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()
    start_date = TODAYS_DATE - timedelta(days=1)

    report_status = AgentsPerformancesReport.generate_report(report_type="Daily", start_date=start_date)
    return report_status


@shared_task
def weekly_performance_report():
    """
    Agent(s) performance/scoreboard report on ticket sales.
    """
    from pos_app.utils import AgentsPerformancesReport

    TODAYS_DATE = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()

    if TODAYS_DATE.weekday() == 4:
        start_date = TODAYS_DATE - timedelta(days=7)
        end_date = TODAYS_DATE - timedelta(days=1)

        report_status = AgentsPerformancesReport.generate_report(report_type="Weekly", start_date=start_date, end_date=end_date)
        return report_status

    return {"message": "false, time threshold not met."}


@shared_task
def monthly_performance_report():
    """
    Agent(s) performance/scoreboard report on ticket sales.
    """
    from pos_app.utils import AgentsPerformancesReport

    TODAYS_DATE = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()

    if TODAYS_DATE.day == 1:
        end_date = TODAYS_DATE - timedelta(days=1)
        start_date = end_date.replace(day=1)
        time_threshold = end_date.day

        report_status = AgentsPerformancesReport.generate_report(
            report_type="Monthly", start_date=start_date, end_date=end_date, time_threshold=time_threshold
        )
        return report_status

    return {"message": "false, time threshold not met."}


@shared_task
def celery_winwise_agent_record_form(request_data):
    from pos_app.models import WinWiseEmployeeTable

    res = WinWiseEmployeeTable.create_user_data(request_data)

    print(res)

    return res


@shared_task
def celery_update_due_remittance():
    from pos_app.models import LottoAgentRemittanceTable

    LottoAgentRemittanceTable().update_due_remittance()

    return {"message": "Remittance updated successfully."}


@shared_task
def celery_suspend_agent_on_agency_banking(agent_id, reason):
    from pos_app.models import Agent, AgentSuspensionRequestToAgencyBanking
    from pos_app.pos_helpers import PosAgentHelper

    agent = Agent.objects.get(id=agent_id)

    pos_agent_helper = PosAgentHelper(
        agent_instance=agent,
        amount=0,
        pin=0,
    )

    suspension_payload = {
        "user_id": agent.user_id,
        "suspend": "true",
        "suspend_or_unsuspend_reason": reason,
    }

    # create a suspension record
    suspension_record_instance = AgentSuspensionRequestToAgencyBanking.objects.create(
        agent=agent,
        reason=reason,
        payload=suspension_payload,
    )

    suspension_response = pos_agent_helper.suspend_lotto_agent_on_agency_banking(**suspension_payload)

    suspension_record_instance.response_payload = suspension_response
    suspension_record_instance.save()

    if isinstance(suspension_response, dict):
        if str(suspension_response.get("message")).casefold() == "success":
            agent.suspended_on_agency_banking = True
            agent.suspending_from_task = True
            agent.save()

            suspension_record_instance.status = "APPROVED"
            suspension_record_instance.save()

        elif str(suspension_response.get("error")) == "478":
            agent.suspended_on_agency_banking = True
            agent.suspending_from_task = True
            agent.save()

            suspension_record_instance.status = "APPROVED"
            suspension_record_instance.save()


@shared_task
def celery_update_winwise_agent_salary_record():
    return
    from main.models import LotteryModel, LottoTicket
    from pos_app.models import Agent, WinwiseEmployeeSalary

    """
    THIS TASK IS TO BE RUN EVERYDAY AT 12:00AM
    """

    winwise_agents = Agent.objects.filter(is_winwise_staff_agent=True, agent_type="LOTTO_AGENT")

    total_amount_for_agents = {}
    total_amount_to_be_paid_to_agents = {}
    percentages_to_reward = {}
    total_sales_for_agents = {}

    if winwise_agents:
        for agent in winwise_agents:
            """
            fetch all all ticket sold by agent,
            get all the unique commission percentage for each of the game table,

            refetch all the ticket sold by agent base on the commission percentage,
            get the sum of the ticket sold by agent base on the commission percentage,

            the percentage of the the amount they'll be paid will be, (9 / unique_commission_percentage)



            """
            current_year = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date().year
            current_month = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date().month

            lotto_ticket_table = LottoTicket.objects.filter(
                Q(agent_profile=agent),
                Q(date__year=current_year) & Q(date__month=current_month) & Q(date__day__gte=1) & Q(date__day__lte=30),
                Q(paid=True),
            )
            lottery_model = LotteryModel.objects.filter(
                Q(agent_profile=agent),
                Q(date__year=current_year) & Q(date__month=current_month) & Q(date__day__gte=1) & Q(date__day__lte=30),
                Q(paid=True),
            )
            # soccer_prediction = SoccerPrediction.objects.filter(
            #     Q(agent=agent),
            #     Q(date_created__year=current_year)
            #     | Q(date_created__month=current_month),
            #     Q(paid=True),
            # )

            if lotto_ticket_table.exists():
                unique_commission_percentage = list(lotto_ticket_table.values_list("commission_per", flat=True).distinct())

                agent_total_sales = lotto_ticket_table.aggregate(Sum("amount_paid"))["amount_paid__sum"]

                if agent_total_sales is None:
                    agent_total_sales = 0

                print("agent_id", agent.id, "agent_total_sales", agent_total_sales)

                if total_sales_for_agents.get(agent.id) is None:
                    total_sales_for_agents[agent.id] = agent_total_sales
                else:
                    total_sales_for_agents[agent.id] += agent_total_sales

                if len(unique_commission_percentage) > 0:
                    for perc in unique_commission_percentage:
                        if perc == 0.0:
                            continue

                        ticket_sold_by_agent = lotto_ticket_table.filter(commission_per=perc).aggregate(Sum("commission_value"))[
                            "commission_value__sum"
                        ]
                        percentage_to_reward = (9 / perc) / 100

                        if ticket_sold_by_agent is None:
                            ticket_sold_by_agent = 0

                        if total_amount_for_agents.get(agent.id) is None:
                            total_amount_for_agents[agent.id] = ticket_sold_by_agent
                        else:
                            total_amount_for_agents[agent.id] += ticket_sold_by_agent

                        if total_amount_to_be_paid_to_agents.get(agent.id) is None:
                            total_amount_to_be_paid_to_agents[agent.id] = ticket_sold_by_agent * percentage_to_reward
                        else:
                            total_amount_to_be_paid_to_agents[agent.id] += ticket_sold_by_agent * percentage_to_reward

                        if percentages_to_reward.get(agent.id) is None:
                            percentages_to_reward[agent.id] = f"{percentage_to_reward},"
                        else:
                            percentages_to_reward[agent.id] += f"{percentage_to_reward},"

            if lottery_model.exists():
                unique_commission_percentage = list(lottery_model.values_list("commission_per", flat=True).distinct())

                agent_total_sales = lottery_model.aggregate(Sum("amount_paid"))["amount_paid__sum"]

                if agent_total_sales is None:
                    agent_total_sales = 0

                # print("agent_id", agent.id, "agent_total_sales", agent_total_sales)

                if total_sales_for_agents.get(agent.id) is None:
                    total_sales_for_agents[agent.id] = agent_total_sales
                else:
                    total_sales_for_agents[agent.id] += agent_total_sales

                if len(unique_commission_percentage) > 0:
                    for perc in unique_commission_percentage:
                        if perc == 0.0:
                            continue

                        ticket_sold_by_agent = lottery_model.filter(commission_per=perc).aggregate(Sum("commission_value"))["commission_value__sum"]
                        percentage_to_reward = (9 / perc) / 100

                        if ticket_sold_by_agent is None:
                            ticket_sold_by_agent = 0

                        if total_amount_for_agents.get(agent.id) is None:
                            total_amount_for_agents[agent.id] = ticket_sold_by_agent
                        else:
                            total_amount_for_agents[agent.id] += ticket_sold_by_agent

                        if total_amount_to_be_paid_to_agents.get(agent.id) is None:
                            total_amount_to_be_paid_to_agents[agent.id] = ticket_sold_by_agent * percentage_to_reward

                        else:
                            total_amount_to_be_paid_to_agents[agent.id] += ticket_sold_by_agent * percentage_to_reward

                        if percentages_to_reward.get(agent.id) is None:
                            percentages_to_reward[agent.id] = f"{percentage_to_reward},"

                        else:
                            percentages_to_reward[agent.id] += f"{percentage_to_reward},"

    if total_amount_for_agents:
        for agent_id, amount in total_amount_for_agents.items():
            # print(
            #     "<<<<<<<<<<<<< :::::::::::::: total_sales_for_agents.get(agent_id) >>>>>>>>>>>>>>>>>>>>>>>> ::::::::::::::::::::",
            #     total_sales_for_agents.get(agent_id),
            #     "\n\n",
            # )

            WinwiseEmployeeSalary().update_salary_record(
                agent_id=int(agent_id),
                total_commission=amount,
                amount_to_be_paid=total_amount_to_be_paid_to_agents.get(agent_id),
                perc=percentages_to_reward.get(agent_id),
                total_sales=total_sales_for_agents.get(agent_id),
            )


@shared_task
def celery_update_winwise_agent_salary_record_monthly():
    from main.models import LotteryModel, LottoTicket
    from pos_app.models import Agent, WinwiseEmployeeSalary

    return

    """
    THIS TASK IS TO BE RUN FIRST DAY OF EVERY MONTH AT 12:00AM AND 4AM.

    THIS TASK IS MEANT TO UPDATE ALL WINWISE AGENT SALARY RECORD FOR THE PREVIOUS MONTH.
    """

    winwise_agents = Agent.objects.filter(is_winwise_staff_agent=True, agent_type="LOTTO_AGENT")

    total_amount_for_agents = {}
    total_amount_to_be_paid_to_agents = {}
    percentages_to_reward = {}
    total_sales_for_agents = {}

    # data_header = [
    #     "Agent Phone",
    #     "Agent Email",
    #     "Agent Name",
    #     "Total Sales",
    #     "Total Commission",
    #     "Total Amount To Be Paid",
    # ]

    if winwise_agents:
        for agent in winwise_agents:
            # year_od_last_month, month_of_last_month = get_last_month()

            year_od_last_month, month_of_last_month = 2023, 12

            lotto_ticket_table = LottoTicket.objects.filter(
                Q(agent_profile=agent),
                Q(date__year=year_od_last_month) & Q(date__month=month_of_last_month),
                Q(paid=True),
            )
            lottery_model = LotteryModel.objects.filter(
                Q(agent_profile=agent),
                Q(date__year=year_od_last_month) & Q(date__month=month_of_last_month),
                Q(paid=True),
            )

            if lotto_ticket_table.exists():
                unique_commission_percentage = list(lotto_ticket_table.values_list("commission_per", flat=True).distinct())

                agent_total_sales = lotto_ticket_table.aggregate(Sum("amount_paid"))["amount_paid__sum"]

                if agent_total_sales is None:
                    agent_total_sales = 0

                if total_sales_for_agents.get(agent.id) is None:
                    total_sales_for_agents[agent.id] = agent_total_sales
                else:
                    total_sales_for_agents[agent.id] += agent_total_sales

                if len(unique_commission_percentage) > 0:
                    for perc in unique_commission_percentage:
                        if perc == 0.0:
                            continue

                        ticket_sold_by_agent = lotto_ticket_table.filter(commission_per=perc).aggregate(Sum("commission_value"))[
                            "commission_value__sum"
                        ]

                        percentage_to_reward = (9 / perc) / 100

                        if ticket_sold_by_agent is None:
                            ticket_sold_by_agent = 0

                        if total_amount_for_agents.get(agent.id) is None:
                            total_amount_for_agents[agent.id] = ticket_sold_by_agent
                        else:
                            total_amount_for_agents[agent.id] += ticket_sold_by_agent

                        if total_amount_to_be_paid_to_agents.get(agent.id) is None:
                            total_amount_to_be_paid_to_agents[agent.id] = ticket_sold_by_agent * percentage_to_reward
                        else:
                            total_amount_to_be_paid_to_agents[agent.id] += ticket_sold_by_agent * percentage_to_reward

                        if percentages_to_reward.get(agent.id) is None:
                            percentages_to_reward[agent.id] = f"{percentage_to_reward},"
                        else:
                            percentages_to_reward[agent.id] += f"{percentage_to_reward},"

            if lottery_model.exists():
                unique_commission_percentage = list(lottery_model.values_list("commission_per", flat=True).distinct())

                agent_total_sales = lottery_model.aggregate(Sum("amount_paid"))["amount_paid__sum"]

                if agent_total_sales is None:
                    agent_total_sales = 0

                if total_sales_for_agents.get(agent.id) is None:
                    total_sales_for_agents[agent.id] = agent_total_sales
                else:
                    total_sales_for_agents[agent.id] += agent_total_sales

                if len(unique_commission_percentage) > 0:
                    for perc in unique_commission_percentage:
                        if perc == 0.0:
                            continue

                        ticket_sold_by_agent = lottery_model.filter(commission_per=perc).aggregate(Sum("commission_value"))["commission_value__sum"]
                        percentage_to_reward = (9 / perc) / 100

                        if ticket_sold_by_agent is None:
                            ticket_sold_by_agent = 0

                        if total_amount_for_agents.get(agent.id) is None:
                            total_amount_for_agents[agent.id] = ticket_sold_by_agent
                        else:
                            total_amount_for_agents[agent.id] += ticket_sold_by_agent

                        if total_amount_to_be_paid_to_agents.get(agent.id) is None:
                            total_amount_to_be_paid_to_agents[agent.id] = ticket_sold_by_agent * percentage_to_reward
                        else:
                            total_amount_to_be_paid_to_agents[agent.id] += ticket_sold_by_agent * percentage_to_reward

                        if percentages_to_reward.get(agent.id) is None:
                            percentages_to_reward[agent.id] = f"{percentage_to_reward},"
                        else:
                            percentages_to_reward[agent.id] += f"{percentage_to_reward},"

    if total_amount_for_agents:
        for agent_id, amount in total_amount_for_agents.items():
            WinwiseEmployeeSalary().update_salary_record(
                agent_id=int(agent_id),
                total_commission=amount,
                amount_to_be_paid=total_amount_to_be_paid_to_agents.get(agent_id),
                perc=percentages_to_reward.get(agent_id),
                total_sales=total_sales_for_agents.get(agent_id),
            )

    #         agent_instance = Agent.objects.get(id=int(agent_id))
    #         if agent_instance is None:
    #             continue
    #         loop_data = [
    #             agent_instance.phone,
    #             agent_instance.email,
    #             agent_instance.full_name,
    #             total_sales_for_agents.get(agent_id),
    #             amount,
    #             total_amount_to_be_paid_to_agents.get(agent_id),
    #         ]

    #         csv_data.append(loop_data)

    # if csv_data:
    #     with open("agent_commission_salary_july.csv", "w") as csv_file:
    #         csv_writer = csv.writer(csv_file)
    #         csv_writer.writerow(data_header)
    #         csv_writer.writerows(csv_data)

    #         print("agent_commission created")

    # print("agent_commission created")


@shared_task
def celery_making_sure_agent_remittance_record_are_updated(remittance_db_id, amount, expected_db_amount):
    from pos_app.models import (
        AgentWallet,
        AgentWalletTransaction,
        LottoAgentRemittanceTable,
    )

    """
    WHAT THIS TASK DOES REALLY IS TO MAKE SURE THAT THE REMITTANCE RECORD IS UPDATED
    """
    agent_remittance = LottoAgentRemittanceTable.objects.get(id=remittance_db_id)
    if agent_remittance.amount_paid >= expected_db_amount:
        agent_remittance.remitted = True
        agent_remittance.save()

        # return "REMITTANCE RECORD UPDATED"
    else:
        if agent_remittance.amount_paid + amount >= expected_db_amount:
            agent_remittance.amount_paid += amount
            agent_remittance.remitted = True
            agent_remittance.save()

            if agent_remittance.amount_paid >= expected_db_amount:
                if agent_remittance.amount_paid >= agent_remittance.amount:
                    agent_remittance.remitted = True
                    agent_remittance.save()
                agent_remittance.save()
            else:
                return celery_making_sure_agent_remittance_record_are_updated(remittance_db_id, amount, expected_db_amount)

        else:
            agent_remittance.amount_paid += amount
            agent_remittance.save()

            if agent_remittance.amount_paid >= expected_db_amount:
                if agent_remittance.amount_paid >= agent_remittance.amount:
                    agent_remittance.remitted = True
                    agent_remittance.save()

                agent_remittance.save()
            else:
                return celery_making_sure_agent_remittance_record_are_updated(remittance_db_id, amount, expected_db_amount)

    agents_remittance = LottoAgentRemittanceTable.objects.filter(remitted=False)
    if agents_remittance:
        for remittance in agents_remittance:
            agent_wallet = AgentWallet.objects.filter(agent=remittance.agent).last()
            agent_prefunding = AgentWalletTransaction.objects.filter(agent_wallet__agent=remittance.agent, transaction_from="PRE_FUNDING").last()

            if agent_wallet.game_play_bal >= agent_prefunding.amount:
                remittance.remitted = True
                remittance.amount_paid = remittance.amount
                remittance.save()


@shared_task
def celery_update_agent_wallet_winning_record():
    """This tasks update agent winning wallet with winnings that failed to add to agent wallet"""

    from pos_app.models import AgentWallet, PosLotteryWinners
    from wallet_app.models import DebitCreditRecord, UserWallet

    pending_withdrawal = PosLotteryWinners.objects.filter(
        is_win_claimed=False,
        withdrawl_initiated=False,
    )

    if pending_withdrawal:
        for record in pending_withdrawal:
            agent_winnings = PosLotteryWinners.objects.filter(
                is_win_claimed=False,
                withdrawl_initiated=False,
                agent=record.agent,
            ).aggregate(
                Sum("amount_won")
            )["amount_won__sum"]

            if agent_winnings is None:
                agent_winnings = 0

            if agent_winnings == 0 or agent_winnings < 0:
                continue

            agent_wallet = AgentWallet.objects.filter(agent=record.agent).last()

            if agent_wallet is None:
                continue

            if agent_wallet.winnings_bal < agent_winnings:
                diff = agent_winnings - agent_wallet.winnings_bal

                debit_credit_record = DebitCreditRecord.create_record(
                    phone_number=agent_wallet.agent.phone,
                    amount=diff,
                    channel="POS/MOBILE",
                    reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                    transaction_type="CREDIT",
                )

                payload = {
                    "transaction_from": "WINNINGS_MANUALLY_UPDATED",
                }

                UserWallet.fund_wallet(
                    user=agent_wallet.agent,
                    amount=diff,
                    channel="POS",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="WINNINGS_WALLET",
                    **payload,
                )

            elif agent_wallet.winnings_bal == agent_winnings:
                continue
            else:
                diff = agent_wallet.winnings_bal - agent_winnings

                debit_credit_record = DebitCreditRecord.create_record(
                    phone_number=agent_wallet.agent.phone,
                    amount=diff,
                    channel="POS/MOBILE",
                    reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                    transaction_type="DEBIT",
                )

                payload = {
                    "transaction_from": "WINNINGS_MANUALLY_UPDATED",
                }

                UserWallet.deduct_wallet(
                    user=agent_wallet.agent,
                    amount=diff,
                    channel="POS",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="WINNINGS_WALLET",
                    **payload,
                )

    print("agent wallet updated with winnings")


@shared_task
def daily_super_agent_report():
    from pos_app.utils import AgentsPerformancesReport

    TODAYS_DATE = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()
    start_date = TODAYS_DATE - timedelta(days=1)

    report_status = AgentsPerformancesReport.generate_super_agent_report(report_type="Daily", start_date=start_date)
    return report_status


@shared_task
def weekly_super_agent_report():
    from pos_app.utils import AgentsPerformancesReport

    TODAYS_DATE = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()

    if TODAYS_DATE.weekday() == 4:
        start_date = TODAYS_DATE - timedelta(days=7)
        end_date = TODAYS_DATE - timedelta(days=1)

        report_status = AgentsPerformancesReport.generate_super_agent_report(report_type="Weekly", start_date=start_date, end_date=end_date)
        return report_status

    return {"message": "false, time threshold not met."}


@shared_task
def monthly_super_agent_report():
    from pos_app.utils import AgentsPerformancesReport

    TODAYS_DATE = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()

    if TODAYS_DATE.day == 1:
        end_date = TODAYS_DATE - timedelta(days=1)
        start_date = end_date.replace(day=1)
        time_threshold = end_date.day

        report_status = AgentsPerformancesReport.generate_super_agent_report(
            report_type="Monthly", start_date=start_date, end_date=end_date, time_threshold=time_threshold
        )
        return report_status

    return {"message": "false, time threshold not met."}


@shared_task
def guarantor_verification_messenger(verification_id: str):
    from pos_app.models import LottoAgentGuarantorDetail

    guarantor_details = LottoAgentGuarantorDetail.objects.filter(verification_id=verification_id).first()
    if guarantor_details is None:
        return f"NO RECORD MATCHES THE REQUEST ID: {verification_id}"

    unique_code = guarantor_details.unique_code
    agent_name = guarantor_details.get_agent_first_name
    guarantor_name = guarantor_details.get_guarantor_first_name
    guarantor_phone = guarantor_details.guarantor_phone
    ussd_code = f"*347*800*33*{unique_code}#"
    whisper_message_guarantor.apply_async(queue="celery1", args=[agent_name, guarantor_name, guarantor_phone, ussd_code])
    return f"SUCCESSFULLY TRIED MESSAGING THE GUARANTOR WITH THE REQUEST ID: {verification_id}."


def temp_agent_task():
    from pos_app.models import AgentWalletTransaction

    ahmed_downline_agent_wallet = AgentWalletTransaction.objects.filter(agent_wallet__agent__super_agent__id=3, transaction_from="REMITTANCE")

    data_header = ["agent_phone_number", "remittance", "created_at"]

    data = []

    for instance in ahmed_downline_agent_wallet:
        loop_data = [instance.agent_wallet.agent.phone, instance.amount, str(instance.date_created)]

        data.append(loop_data)

    with open("Ahmed_hassan_downline_agent_remittances.csv", "w", encoding="UTF-8", newline="") as f:
        writer = csv.writer(f)
        writer.writerow(data_header)
        writer.writerows(data)


@shared_task
def reward_agent_amd_super_agent_commission():
    # return "TEMPORARYLY DISABLED"
    from main.models import PayoutTransactionTable
    from pos_app.models import AgentWallet, LottoSuperAgentWallet
    from pos_app.pos_helpers import PosAgentHelper
    from wallet_app.models import DebitCreditRecord, UserWallet

    super_agents_with_commission_value = LottoSuperAgentWallet.objects.filter(commission_balance__gt=0)

    has_sufficient_balance = True

    for super_agent in super_agents_with_commission_value:
        amount_to_reward = super_agent.commission_balance
        super_agent.commission_balance = 0
        super_agent.rewarded_commission_balance += amount_to_reward
        super_agent.save()

        payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        payload = {
            "transaction_ref": payout_reference,
            "recipient": super_agent.super_agent.phone,
            "amount": amount_to_reward,
            "profit": amount_to_reward,
            "transaction_type": f"{super_agent.super_agent.type}_COMMISSION",
            "transaction_reason": f"{super_agent.super_agent.type}_COMMISSION",
        }

        _withdraw_table_instance = PayoutTransactionTable.objects.create(
            source="BUDDY",
            amount=amount_to_reward,
            disbursement_unique_id=payout_reference,
            phone=super_agent.super_agent.phone,
            payout_trans_ref=payout_reference,
            channel="POS",
            payout_payload=payload,
            balance_before=amount_to_reward,
            balance_after=0,
            joined_since=super_agent.super_agent.get_duration(),
            name=f"{super_agent.super_agent.first_name} {super_agent.super_agent.last_name}",
            type_of_transaction="COMMISSION",
            is_super_agent_commission=True,
            source_wallet="RETAIL_COMMISSION_WALLET",
            recipient_wallet="AGENT_COMMISSION_WALLET",
        )

        reward_response = PosAgentHelper().agent_commission_reward(**payload)

        _withdraw_table_instance.source_response_payload = reward_response
        _withdraw_table_instance.save()

        if isinstance(reward_response, dict):
            errors = reward_response.get("errors")
            if errors is None:
                if "sufficient" in errors:
                    has_sufficient_balance = False
                    break

    if has_sufficient_balance is False:
        return "INSUFFICIENT BALANCE"

    agents_with_commission_value = AgentWallet.objects.filter(commission_bal__gt=0)

    for agent_wallet in agents_with_commission_value:

        payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        amount_to_reward = agent_wallet.commission_bal

        # Create transaction record
        reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"
        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent_wallet.agent.phone,
            amount=amount_to_reward,
            channel="POS/MOBILE",
            reference=reference,
            transaction_type="DEBIT",
        )

        # Debit the wallet
        payload = {"transaction_from": "COMMISSION_REWARD"}
        UserWallet.deduct_wallet(
            user=agent_wallet.agent,
            amount=debit_credit_record.amount,
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="COMMISSION_WALLET",
            **payload,
        )

        payload = {
            "transaction_ref": payout_reference,
            "recipient": agent_wallet.agent.phone,
            "amount": debit_credit_record.amount,
            "profit": debit_credit_record.amount,
            "transaction_type": "AGENT_GAME_PLAY_COMMISSION",
            "transaction_reason": "AGENT_GAME_PLAY_COMMISSION",
        }

        _withdraw_table_instance = PayoutTransactionTable.objects.create(
            source="BUDDY",
            amount=debit_credit_record.amount,
            disbursement_unique_id=payout_reference,
            phone=agent_wallet.agent.phone,
            payout_trans_ref=payout_reference,
            channel="POS",
            payout_payload=payload,
            balance_before=amount_to_reward,
            balance_after=0,
            joined_since=agent_wallet.agent.get_duration(),
            name=f"{agent_wallet.agent.first_name} {agent_wallet.agent.last_name}",
            type_of_transaction="COMMISSION",
            is_agent_commission=True,
            source_wallet="RETAIL_COMMISSION_WALLET",
            recipient_wallet="AGENT_COMMISSION_WALLET",
        )

        reward_response = PosAgentHelper().agent_commission_reward(**payload)

        _withdraw_table_instance.source_response_payload = reward_response
        _withdraw_table_instance.save()

        if isinstance(reward_response, dict):
            errors = reward_response.get("errors")
            if errors is None:
                if "sufficient" in errors:
                    break
