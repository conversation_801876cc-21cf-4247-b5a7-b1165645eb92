from rest_framework import serializers

from main.models import (
    ConstantVariable,
    LotteryBatch,
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    PayoutTransactionTable,
    UserProfile,
)
from pos_app.enums import TypeOfAgentDownlinesRestriction
from prices.game_price import InstantCashOutPriceModel, QuikaPriceModel

from .models import (
    Agent,
    AgentWalletTransaction,
    BoughtLotteryTickets,
    PosLotteryWinners, LottoAgentSalesActivity,
)
from .prices.structure import LOTT0_AMOUNT
import uuid


class LottoApiSerializer(serializers.Serializer):
    ticket = serializers.Char<PERSON>ield()


class LottoSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    amount = serializers.IntegerField()
    lottery = serializers.ListSerializer(child=LottoApiSerializer())


class AgentLoginSerializer(serializers.Serializer):
    user_uuid = serializers.Char<PERSON><PERSON>()
    password = serializers.Char<PERSON><PERSON>()


class CreateAgentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Agent
        fields = "__all__"

    extra_kwargs = {
        "address": {"required": True},
    }

    def validate(self, data):
        # check if first_name and last name cobtains special characters
        if Agent.check_if_string_contains_special_characters(data["first_name"]):
            raise serializers.ValidationError("First name contains special characters")
        if Agent.check_if_string_contains_special_characters(data["last_name"]):
            raise serializers.ValidationError("Last name contains special characters")

        if data["first_name"].isdigit():
            raise serializers.ValidationError("First name contains numbers")
        if data["last_name"].isdigit():
            raise serializers.ValidationError("Last name contains numbers")

        # check if phone number is valid
        # if not Agent.check_if_phone_number_is_valid(data["phone"]):
        #     raise serializers.ValidationError(
        #         "Please check the length of the phone number or content of the phone number"
        #     )

        phone = LotteryModel.format_number_from_back_add_234(data["phone"])
        data["phone"] = phone

        # check if email is valid
        if not Agent.check_if_email_is_valid(data["email"]):
            raise serializers.ValidationError("Email is not valid")

        if not data["user_id"].isdigit():
            raise serializers.ValidationError("User id must be a number")

        if data.get("address") is None:
            raise serializers.ValidationError("Address is required")

        return data


class PhoneNumberSerializer(serializers.Serializer):
    phone_number = serializers.CharField()


class AgentPayoutSerializer(serializers.Serializer):
    amount = serializers.FloatField()
    account_number = serializers.CharField()
    bank_code = serializers.CharField()
    narration = serializers.CharField(allow_null=True, allow_blank=True)
    save_beneficiary = serializers.BooleanField(default=False)


class WinningsPayoutSerializer(serializers.Serializer):
    game_id = serializers.CharField()
    pin = serializers.CharField()
    phone = serializers.CharField()

    amount = serializers.FloatField()
    account_number = serializers.CharField()
    bank_code = serializers.CharField()
    narration = serializers.CharField()
    agent_transaction_pin = serializers.CharField()

    # validate the phone number
    def validate(self, data):
        # if not Agent.check_if_phone_number_is_valid(data["phone"]):
        #     raise serializers.ValidationError(
        #         "Please check the length of the phone number or content of the phone number"
        #     )

        # check account number field
        if not Agent.check_if_account_number_is_valid(data["account_number"]):
            raise serializers.ValidationError("Account number is not valid")

        return data


class LibertyPayWinningsPayoutSerializer(serializers.Serializer):
    game_id = serializers.CharField()
    pin = serializers.CharField()
    narration = serializers.CharField()
    agent_transaction_pin = serializers.CharField()
    click_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    # validate the phone number
    def validate(self, data):
        click_id = data.get("click_id")
        if click_id is None:
            data["click_id"] = f"sys-{uuid.uuid4()}"

        return data


class GameHistorySerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LottoTicket
        fields = [
            "id",
            "user_profile",
            "agent_profile",
            "batch",
            "phone",
            "stake_amount",
            "paid",
            "date",
            "number_of_ticket",
            "channel",
            "game_play_id",
            "lottery_type",
            "ticket",
            "amount_paid",
        ]

    def to_representation(self, obj):
        serialized_data = super(GameHistorySerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        if serialized_data["lottery_type"] == "INSTANT_CASHOUT":
            user_profile = UserProfile.objects.get(id=serialized_data.get("user_profile"))

            _is_lottery_winner = LottoWinners.objects.filter(
                game_play_id=serialized_data["game_play_id"],
                phone_number=user_profile.phone_number,
            ).last()
            if _is_lottery_winner:
                _status = "won"

                if (_is_lottery_winner.win_flavour != "WHITE") and (_is_lottery_winner.lotto_type == "INSTANT_CASHOUT"):
                    _status = "cashback"

                if (_is_lottery_winner.match_type == "PERM_0") and (_is_lottery_winner.lotto_type == "INSTANT_CASHOUT"):
                    _status = "cashback"

                if (
                        PosLotteryWinners().check_if_win_is_claimed(
                            agent_id=serialized_data.get("agent_profile"),
                            game_id=serialized_data.get("game_play_id"),
                        )
                        is True
                ):
                    _status = "claimed"

                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": _status,
                        "stake_amount": serialized_data["amount_paid"],
                        "number_of_ticket": serialized_data["number_of_ticket"],
                        "date": serialized_data["date"],
                        "won": True,
                        "lost": False,
                    }
                )

            else:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "lost",
                        "stake_amount": serialized_data["amount_paid"],
                        "number_of_ticket": serialized_data["number_of_ticket"],
                        "date": serialized_data["date"],
                        "won": False,
                        "lost": True,
                    }
                )

        else:
            is_batch_active = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().is_active

            if is_batch_active:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "accepted",
                        "stake_amount": serialized_data["amount_paid"],
                        "number_of_ticket": serialized_data["number_of_ticket"],
                        "date": serialized_data["date"],
                        "won": False,
                        "lost": False,
                    }
                )

            else:
                user_profile = UserProfile.objects.get(id=serialized_data.get("user_profile"))

                _is_lottery_winner = LottoWinners.objects.filter(
                    game_play_id=serialized_data["game_play_id"],
                    phone_number=user_profile.phone_number,
                ).last()

                if _is_lottery_winner:
                    _status = "won"

                    if (_is_lottery_winner.win_flavour != "WHITE") and (
                            _is_lottery_winner.lotto_type == "INSTANT_CASHOUT"):
                        _status = "cashback"

                    if (
                            PosLotteryWinners().check_if_win_is_claimed(
                                agent_id=serialized_data.get("agent_profile"),
                                game_id=serialized_data.get("game_play_id"),
                            )
                            is True
                    ):
                        _status = "claimed"

                    data.append(
                        {
                            "game_play_id": serialized_data["game_play_id"],
                            "status": _status,
                            "stake_amount": serialized_data["amount_paid"],
                            "number_of_ticket": serialized_data["number_of_ticket"],
                            "date": serialized_data["date"],
                            "won": True,
                            "lost": False,
                        }
                    )

                else:
                    data.append(
                        {
                            "game_play_id": serialized_data["game_play_id"],
                            "status": "lost",
                            "stake_amount": serialized_data["amount_paid"],
                            "number_of_ticket": serialized_data["number_of_ticket"],
                            "date": serialized_data["date"],
                            "won": False,
                            "lost": True,
                        }
                    )

        return data[0]


class MobileGameHistorySerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LottoTicket
        fields = [
            "id",
            "user_profile",
            "agent_profile",
            "batch",
            "phone",
            "stake_amount",
            "paid",
            "date",
            "number_of_ticket",
            "channel",
            "game_play_id",
            "lottery_type",
            "ticket",
        ]

    def to_representation(self, obj):
        serialized_data = super(MobileGameHistorySerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        is_batch_active = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().is_active

        if is_batch_active:
            if serialized_data["lottery_type"] == "INSTANT_CASHOUT":
                _is_lottery_winner = LottoWinners.objects.filter(game_play_id=serialized_data["game_play_id"]).last()
                if _is_lottery_winner:
                    data.append(
                        {
                            "game_play_id": serialized_data["game_play_id"],
                            "status": "accepted",
                            "stake_amount": serialized_data["stake_amount"],
                            "number_of_ticket": serialized_data["number_of_ticket"],
                            "date": serialized_data["date"],
                            "game_type": str(serialized_data["lottery_type"]).replace("_", " "),
                            "pontential_win": (
                                LottoTicket.salary_for_life_stake_amount_pontential_winning(
                                    serialized_data["number_of_ticket"])[
                                    "total_winning_amount"
                                ]
                                if serialized_data["lottery_type"] == "SALARY_FOR_LIFE"
                                else LottoTicket.instant_stake_amount_pontential_winning(
                                    serialized_data["number_of_ticket"])["total_winning_amount"]
                            ),
                            "paid": serialized_data["paid"],
                            "won": True,
                            "lost": False,
                        }
                    )

                else:
                    data.append(
                        {
                            "game_play_id": serialized_data["game_play_id"],
                            "status": "accepted",
                            "stake_amount": serialized_data["stake_amount"],
                            "number_of_ticket": serialized_data["number_of_ticket"],
                            "date": serialized_data["date"],
                            "game_type": str(serialized_data["lottery_type"]).replace("_", " "),
                            "pontential_win": (
                                LottoTicket.salary_for_life_stake_amount_pontential_winning(
                                    serialized_data["number_of_ticket"])[
                                    "total_winning_amount"
                                ]
                                if serialized_data["lottery_type"] == "SALARY_FOR_LIFE"
                                else LottoTicket.instant_stake_amount_pontential_winning(
                                    serialized_data["number_of_ticket"])["total_winning_amount"]
                            ),
                            "paid": serialized_data["paid"],
                            "won": False,
                            "lost": False,
                        }
                    )

            else:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "accepted",
                        "stake_amount": serialized_data["stake_amount"],
                        "number_of_ticket": serialized_data["number_of_ticket"],
                        "date": serialized_data["date"],
                        "game_type": str(serialized_data["lottery_type"]).replace("_", " "),
                        "pontential_win": (
                            LottoTicket.salary_for_life_stake_amount_pontential_winning(
                                serialized_data["number_of_ticket"])["total_winning_amount"]
                            if serialized_data["lottery_type"] == "SALARY_FOR_LIFE"
                            else
                            LottoTicket.instant_stake_amount_pontential_winning(serialized_data["number_of_ticket"])[
                                "total_winning_amount"]
                        ),
                        "paid": serialized_data["paid"],
                        "won": False,
                        "lost": False,
                    }
                )

        else:
            _is_lottery_winner = LottoWinners.objects.filter(game_play_id=serialized_data["game_play_id"]).last()
            if _is_lottery_winner:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "accepted",
                        "stake_amount": serialized_data["stake_amount"],
                        "number_of_ticket": serialized_data["number_of_ticket"],
                        "date": serialized_data["date"],
                        "game_type": str(serialized_data["lottery_type"]).replace("_", " "),
                        "pontential_win": (
                            LottoTicket.salary_for_life_stake_amount_pontential_winning(
                                serialized_data["number_of_ticket"])["total_winning_amount"]
                            if serialized_data["lottery_type"] == "SALARY_FOR_LIFE"
                            else
                            LottoTicket.instant_stake_amount_pontential_winning(serialized_data["number_of_ticket"])[
                                "total_winning_amount"]
                        ),
                        "paid": serialized_data["paid"],
                        "won": True,
                        "lost": False,
                    }
                )

            else:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "accepted",
                        "stake_amount": serialized_data["stake_amount"],
                        "number_of_ticket": serialized_data["number_of_ticket"],
                        "date": serialized_data["date"],
                        "game_type": str(serialized_data["lottery_type"]).replace("_", " "),
                        "pontential_win": (
                            LottoTicket.salary_for_life_stake_amount_pontential_winning(
                                serialized_data["number_of_ticket"])["total_winning_amount"]
                            if serialized_data["lottery_type"] == "SALARY_FOR_LIFE"
                            else
                            LottoTicket.instant_stake_amount_pontential_winning(serialized_data["number_of_ticket"])[
                                "total_winning_amount"]
                        ),
                        "paid": serialized_data["paid"],
                        "won": False,
                        "lost": True,
                    }
                )

        return data[0]


class SecondGameHistorySerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LotteryModel
        fields = "__all__"

    def to_representation(self, obj):
        serialized_data = super(SecondGameHistorySerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        is_batch_active = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().is_active

        if is_batch_active:
            data.append(
                {
                    "game_play_id": serialized_data["game_play_id"],
                    "status": "accepted",
                    "stake_amount": serialized_data["amount_paid"],
                    "number_of_ticket": "",
                    "unique_id": serialized_data["unique_id"],
                    "date": serialized_data["date"],
                    "won": False,
                    "lost": False,
                    "paid": serialized_data["paid"],
                    "game_type": str(serialized_data["lottery_type"]),
                    "pontential_win": int(serialized_data["band"]),
                }
            )
        else:
            _is_lottery_winner = LotteryWinnersTable.objects.filter(game_play_id=serialized_data["game_play_id"]).last()

            if _is_lottery_winner:
                _status = "won"

                if (
                        PosLotteryWinners().check_if_win_is_claimed(
                            agent_id=serialized_data.get("agent_profile"),
                            game_id=serialized_data.get("game_play_id"),
                        )
                        is True
                ):
                    _status = "claimed"

                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": _status,
                        "stake_amount": serialized_data["amount_paid"],
                        "number_of_ticket": "",
                        "unique_id": serialized_data["unique_id"],
                        "date": serialized_data["date"],
                        "won": True,
                        "lost": False,
                        "paid": serialized_data["paid"],
                        "game_type": str(serialized_data["lottery_type"]),
                        "pontential_win": int(serialized_data["band"]),
                    }
                )
            else:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "lost",
                        "stake_amount": serialized_data["amount_paid"],
                        "number_of_ticket": "",
                        "unique_id": serialized_data["unique_id"],
                        "date": serialized_data["date"],
                        "won": False,
                        "lost": True,
                        "paid": serialized_data["paid"],
                        "game_type": str(serialized_data["lottery_type"]),
                        "pontential_win": int(serialized_data["band"]),
                    }
                )

        return data[0]


class TicketDetailsSerializer(serializers.Serializer):
    game_id = serializers.CharField()
    pin = serializers.CharField()
    phone = serializers.CharField()


class AgentTransactionHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = AgentWalletTransaction
        fields = "__all__"


class GamePlayIdSerializer(serializers.Serializer):
    game_play_id = serializers.CharField()


class PosLotteryPaymentSerializer(serializers.Serializer):
    game_play_id = serializers.CharField()
    # pin = serializers.CharField()


class PosLottoFilterWinnersSerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LottoTicket
        fields = [
            "id",
            "user_profile",
            "agent_profile",
            "batch",
            "phone",
            "stake_amount",
            "paid",
            "date",
            "number_of_ticket",
            "channel",
            "game_play_id",
            "lottery_type",
            "ticket",
        ]

    def to_representation(self, obj):
        serialized_data = super(PosLottoFilterWinnersSerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        is_batch_active = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().is_active

        if is_batch_active:
            if serialized_data["lottery_type"] == "INSTANT_CASHOUT":
                _is_lottery_winner = LottoWinners.objects.filter(game_play_id=serialized_data["game_play_id"]).last()
                if _is_lottery_winner:
                    _status = "won"
                    if serialized_data["agent_profile"] is not None:
                        if (
                                PosLotteryWinners().check_if_win_is_claimed(
                                    agent_id=serialized_data.get("agent_profile"),
                                    game_id=serialized_data.get("game_play_id"),
                                )
                                is True
                        ):
                            _status = "claimed"

                    data.append(
                        {
                            "game_play_id": serialized_data["game_play_id"],
                            "status": _status,
                            "stake_amount": serialized_data["stake_amount"],
                            "number_of_ticket": serialized_data["number_of_ticket"],
                            "date": serialized_data["date"],
                            "won": True,
                            "lost": False,
                        }
                    )

        else:
            _is_lottery_winner = LottoWinners.objects.filter(game_play_id=serialized_data["game_play_id"]).last()
            if _is_lottery_winner:
                _status = "won"
                if serialized_data["agent_profile"] is not None:
                    if (
                            PosLotteryWinners().check_if_win_is_claimed(
                                agent_id=serialized_data.get("agent_profile"),
                                game_id=serialized_data.get("game_play_id"),
                            )
                            is True
                    ):
                        _status = "claimed"

                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": _status,
                        "stake_amount": serialized_data["stake_amount"],
                        "number_of_ticket": serialized_data["number_of_ticket"],
                        "date": serialized_data["date"],
                        "won": True,
                        "lost": False,
                    }
                )
        print("data", data, "\n\n")
        try:
            return data[0]
        except Exception:
            return data


class PosLottoFilterLostSerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LottoTicket
        fields = [
            "id",
            "user_profile",
            "agent_profile",
            "batch",
            "phone",
            "stake_amount",
            "paid",
            "date",
            "number_of_ticket",
            "channel",
            "game_play_id",
            "lottery_type",
            "ticket",
        ]

    def to_representation(self, obj):
        serialized_data = super(PosLottoFilterLostSerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        is_batch_active = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().is_active

        if is_batch_active:
            if serialized_data["lottery_type"] == "INSTANT_CASHOUT":
                _is_lottery_winner = LottoWinners.objects.filter(game_play_id=serialized_data["game_play_id"]).last()
                if _is_lottery_winner is None:
                    data.append(
                        {
                            "game_play_id": serialized_data["game_play_id"],
                            "status": "lost",
                            "stake_amount": serialized_data["stake_amount"],
                            "number_of_ticket": serialized_data["number_of_ticket"],
                            "date": serialized_data["date"],
                            "won": False,
                            "lost": True,
                        }
                    )

        else:
            _is_lottery_winner = LottoWinners.objects.filter(game_play_id=serialized_data["game_play_id"]).last()
            if _is_lottery_winner is None:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "lost",
                        "stake_amount": serialized_data["stake_amount"],
                        "number_of_ticket": serialized_data["number_of_ticket"],
                        "date": serialized_data["date"],
                        "won": False,
                        "lost": True,
                    }
                )

        try:
            return data[0]
        except Exception:
            return data


class PosLottoFilterPendingDrawSerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LottoTicket
        fields = [
            "id",
            "user_profile",
            "agent_profile",
            "batch",
            "phone",
            "stake_amount",
            "paid",
            "date",
            "number_of_ticket",
            "channel",
            "game_play_id",
            "lottery_type",
            "ticket",
        ]

    def to_representation(self, obj):
        serialized_data = super(PosLottoFilterPendingDrawSerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        data.append(
            {
                "game_play_id": serialized_data["game_play_id"],
                "status": "accepted",
                "stake_amount": serialized_data["stake_amount"],
                "number_of_ticket": serialized_data["number_of_ticket"],
                "date": serialized_data["date"],
                "won": False,
                "lost": False,
            }
        )

        return data[0]


class PosLotteryFilterWinnersSerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LotteryModel
        fields = "__all__"

    def to_representation(self, obj):
        serialized_data = super(PosLotteryFilterWinnersSerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        is_batch_active = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().is_active

        if is_batch_active:
            pass
        else:
            _is_lottery_winner = LotteryWinnersTable.objects.filter(game_play_id=serialized_data["game_play_id"]).last()

            if _is_lottery_winner:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "won",
                        "stake_amount": serialized_data["stake_amount"],
                        "number_of_ticket": "",
                        "unique_id": serialized_data["unique_id"],
                        "date": serialized_data["date"],
                        "won": True,
                        "lost": False,
                    }
                )

        return data[0]


class PosLotteryFilterWinnersSerializer(serializers.ModelSerializer):  # noqa
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:  # noqa
        model = LotteryModel
        fields = "__all__"

    def to_representation(self, obj):
        serialized_data = super(PosLotteryFilterWinnersSerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        is_batch_active = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().is_active

        if is_batch_active:
            pass
        else:
            _is_lottery_winner = LotteryWinnersTable.objects.filter(game_play_id=serialized_data["game_play_id"]).last()

            if _is_lottery_winner:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "won",
                        "stake_amount": serialized_data["stake_amount"],
                        "number_of_ticket": "",
                        "unique_id": serialized_data["unique_id"],
                        "date": serialized_data["date"],
                        "won": True,
                        "lost": False,
                    }
                )

        try:
            return data[0]
        except Exception:
            return data


class PosLotteryFilterLostSerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LotteryModel
        fields = "__all__"

    def to_representation(self, obj):
        serialized_data = super(PosLotteryFilterLostSerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        is_batch_active = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().is_active

        if is_batch_active:
            pass
        else:
            _is_lottery_winner = LotteryWinnersTable.objects.filter(game_play_id=serialized_data["game_play_id"]).last()

            if _is_lottery_winner is None:
                data.append(
                    {
                        "game_play_id": serialized_data["game_play_id"],
                        "status": "lost",
                        "stake_amount": serialized_data["stake_amount"],
                        "number_of_ticket": "",
                        "unique_id": serialized_data["unique_id"],
                        "date": serialized_data["date"],
                        "won": False,
                        "lost": True,
                    }
                )

        try:
            return data[0]
        except Exception:
            return data


class PosLotteryFilterPendingDrawSerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LotteryModel
        fields = "__all__"

    def to_representation(self, obj):
        serialized_data = super(PosLotteryFilterPendingDrawSerializer, self).to_representation(obj)
        # print("serialized_data", serialized_data, "\n\n\n\n\n\n\n\n")

        data = []

        data.append(
            {
                "game_play_id": serialized_data["game_play_id"],
                "status": "accepted",
                "stake_amount": serialized_data["stake_amount"],
                "number_of_ticket": "",
                "unique_id": serialized_data["unique_id"],
                "date": serialized_data["date"],
                "won": False,
                "lost": False,
            }
        )

        return data[0]


class LottoTicketGameResultSerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LottoWinners
        fields = [
            "batch",
            "phone_number",
            "game_play_id",
            "ticket",
            "lotto_type",
            "stake_amount",
            "earning",

        ]

    def to_representation(self, obj):
        serialized_data = super(LottoTicketGameResultSerializer, self).to_representation(obj)

        serialized_data["batch"] = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().batch_uuid
        serialized_data["ticket"] = [int(i) for i in serialized_data["ticket"].split(",")]
        serialized_data["phone_number"] = obj.phone_number

        return serialized_data


class WyseCashGameResultSerializer(serializers.ModelSerializer):
    depth = 1

    # comment = serializers.CharField(required=False, allow_null = True)
    class Meta:
        model = LotteryWinnersTable
        fields = [
            "batch",
            "phone_number",
            "game_play_id",
            "ticket",
            "stake_amount",
            "earning",
        ]

    def to_representation(self, obj):
        serialized_data = super(WyseCashGameResultSerializer, self).to_representation(obj)

        serialized_data["batch"] = LotteryBatch.objects.filter(id=serialized_data["batch"]).last().batch_uuid
        serialized_data["lotto_type"] = "WYSE_CASH"
        serialized_data["phone_number"] = obj.phone_number

        return serialized_data


class WyseCashGameResultSortSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryWinnersTable
        fields = ["id", "ticket", "game_play_id", "batch", "earning"]

    def to_representation(self, obj):
        serialized_data = super(WyseCashGameResultSortSerializer, self).to_representation(obj)
        serialized_data["date"] = obj.date_won.date()
        serialized_data["batch"] = obj.batch.batch_uuid

        return serialized_data


class InstantCashGameResultSortSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LottoWinners
        fields = ["id", "ticket", "game_play_id", "batch", "earning", "lottery"]

    def to_representation(self, obj):
        serialized_data = super(InstantCashGameResultSortSerializer, self).to_representation(obj)

        # print("obj.date_won.date()", obj.date_won.date(), "\n\n\n\n\n\n\n\n")
        serialized_data["date"] = obj.date_won.date()
        serialized_data["batch"] = obj.batch.batch_uuid
        # get ticket instance
        if obj.lottery is not None:
            ticket_instance = LottoTicket.objects.filter(id=obj.lottery.id).last()
            serialized_data["ticket_number"] = [] if ticket_instance is None else [
                [int(i) for i in ticket_instance.system_generated_num.split(",")]]

            # THE REASON WHY I PUT THE SYSTEM PICK NUMBER IN A LIST OF LIST, IS BECAUSE SALARY FOR LIFE DATA LOOKS LIKE THAT
            # SO THAT THE MOBILE TEAM WILL RECEIVE THE SAME DATA FORMAT FOR ALL THE GAMES
        else:
            serialized_data["ticket_number"] = []

        del serialized_data["lottery"]
        return serialized_data


class LotteryBatchSerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = LotteryBatch
        fields = ["id", "lottery_winner_ticket_number", "batch_uuid"]

    def to_representation(self, obj):
        serialized_data = super(LotteryBatchSerializer, self).to_representation(obj)
        serialized_data["game_play_id"] = ""
        serialized_data["earning"] = 5000000
        serialized_data["date"] = obj.created_date.date()

        return serialized_data


class AgentRetailData(serializers.Serializer):
    no_of_lines = serializers.IntegerField()
    quantity = serializers.IntegerField()
    amount = serializers.IntegerField()
    band = serializers.CharField(required=False, allow_null=True)


class LotteryRetailCreationSerializer(serializers.Serializer):
    salary_for_life = serializers.ListSerializer(child=AgentRetailData(), required=False)
    instant_cashout = serializers.ListSerializer(child=AgentRetailData(), required=False)
    wyse_cash = serializers.ListSerializer(child=AgentRetailData(), required=False)
    soccer_cash = serializers.ListSerializer(child=AgentRetailData(), required=False)
    amount = serializers.IntegerField()
    pin = serializers.CharField()
    combined_ticket = serializers.BooleanField(default=False)

    def validate(self, data):
        if len(data["pin"]) < 4:
            raise serializers.ValidationError("Pin must be 4 digits")

        elif len(data["pin"]) > 4:
            raise serializers.ValidationError("Pin must be 4 digits")

        return data


class LotteryRetailCreationViaBonusSerializer(serializers.Serializer):
    salary_for_life = serializers.ListSerializer(child=AgentRetailData(), required=False)
    instant_cashout = serializers.ListSerializer(child=AgentRetailData(), required=False)
    wyse_cash = serializers.ListSerializer(child=AgentRetailData(), required=False)
    soccer_cash = serializers.ListSerializer(child=AgentRetailData(), required=False)


class GetRetailLotteryHistorySerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = BoughtLotteryTickets
        fields = ["id", "game_type", "amount", "game_id", "no_of_tickets", "paid"]

    def to_representation(self, obj):
        serialized_data = super(GetRetailLotteryHistorySerializer, self).to_representation(obj)

        serialized_data["amount"] = obj.amount * obj.no_of_tickets
        serialized_data["status"] = "Successful" if obj.paid is True else "Failed"

        del serialized_data["no_of_tickets"]
        del serialized_data["paid"]

        return serialized_data


class LotteryRetailSoccerCashSerializer(serializers.Serializer):
    pin = serializers.CharField(max_length=4, min_length=4)
    play_type = serializers.CharField(max_length=20)
    amount = serializers.IntegerField()
    single = serializers.BooleanField(default=False)
    game_play = serializers.JSONField()

    def validate(self, data):
        play_type = data.get("play_type")

        if play_type == "PERSONAL":
            for key, value in data.get("game_play").items():
                # print(key,"__________________", len(value))
                if key == "score_prediction":
                    if len(value) != 3:
                        raise serializers.ValidationError(
                            {"play_type": "Total score prediction must be equal to 3 on PERSONAL play type."})
        return data

    # def validate(self, data):

    #     match_selections = data.get("game_play")
    #     if len(match_selections) <= 0:
    #         raise serializers.ValidationError(
    #             # {
    #             #     "game_play": "Ensure this value has at least one object e.g "
    #             #     '{"fixtures_id": "946832","stake_amount": 200,"predictions":'
    #             #     ' [{"home_choice": 1,"away_choice": 2,"freemium": true},'
    #             #     '{"home_choice": 1,"away_choice": 2,"freemium": true}]}.'
    #             # }
    #         )

    # for match in match_selections:

    #     try:

    #         # predictions
    #         if len(match["predictions"]) <= 0:
    #             raise serializers.ValidationError(
    #                 {
    #                     "predictions": "Ensure this value has at least one object e.g "
    #                     "{'home_choice':1, 'away_choice':2, 'freemium':false}."
    #                 }
    #             )

    #         for prediction in match["predictions"]:

    #             try:

    #                 if type(prediction["home_choice"]) is not int:
    #                     raise serializers.ValidationError(
    #                         {"home_choice": "A valid number is required."}
    #                     )

    #                 if type(prediction["away_choice"]) is not int:
    #                     raise serializers.ValidationError(
    #                         {"away_choice": "A valid number is required."}
    #                     )

    #                 # stake amount checks
    #                 if (
    #                     type(prediction["stake_amount"]) is not int
    #                 ):  # validate stake amount as int

    #                     raise serializers.ValidationError(
    #                         {"stake_amount": "A valid number is required."}
    #                     )
    #                 if (
    #                     prediction["stake_amount"] < 200
    #                 ):  # validate value is not less than 200
    #                     raise serializers.ValidationError(
    #                         {
    #                             "stake_amount": "Ensure this value is greater than or equal to 200."
    #                         }
    #                     )

    #             except KeyError:
    #                 raise serializers.ValidationError(
    #                     {"predictions": "Invalid key"}
    #                 )

    #     except KeyError as key:
    #         print(key)
    #         raise serializers.ValidationError({"game_play": f"Invalid key {key}"})

    # return data


class ManuallyPreFundAgentApiViewSerializer(serializers.Serializer):
    phone_or_email = serializers.CharField()


class LibertyPayVFDPayoutVerificationSerializer(serializers.Serializer):
    unique_payout_id = serializers.CharField()


class MultipleLottoPlaySerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=13, min_length=11, allow_null=True, allow_blank=True)
    # pin = serializers.CharField(max_length=4, min_length=4)
    lottery_play = serializers.ListField(child=serializers.JSONField())

    def validate(self, attrs):
        # ============================================================================= validate agent pin
        self.context.get("agent_instance")
        lotto_type = self.context.get("lotto_type")

        # pos_agent_helper = PosAgentHelper(agent_instance=agent, amount=1000)

        print("*******************************************************")
        print("*******************************************************")
        print("*******************************************************")
        print("                                                       ")
        print("                                                       ")
        print("STARTED AGENCY BANKING")
        # if pos_agent_helper.verify_agent_transaction_pin(attrs.get("pin")) is False:
        #     raise serializers.ValidationError({"pin": "Invalid pin"})

        print("                                                       ")
        print("                                                       ")
        print(" FINISHED AGENCY AUTHENTICATION")
        print("*******************************************************")
        print("*******************************************************")
        # ============================================================================= validate agent pin

        lottery_play = attrs.get("lottery_play")
        if len(lottery_play) <= 0:
            raise serializers.ValidationError({"lottery_play": "You must select at least one lottery."})

        for lottery in lottery_play:
            try:
                lines = len(lottery["lottery"])
                if lines <= 0:
                    raise serializers.ValidationError({"lottery": "Lottery cannot be empty"})

                if lines > 7:
                    raise serializers.ValidationError({"lottery": "Lottery cannot be more than 7 numbers"})

                if lotto_type == "QUIKA":
                    stake_amount = lottery["amount"]
                    amount = LOTT0_AMOUNT.get(stake_amount, None)
                    if amount is None:
                        raise serializers.ValidationError({"amount": "Invalid stake amount"})

                    if len(lottery["lottery"]) != 1:
                        raise serializers.ValidationError({"tickets": "you can only have one line"})

                    stake_amount = lottery["amount"]
                    # amount = LOTT0_AMOUNT.get(stake_amount, None)
                    # if amount is None:
                    #     raise serializers.ValidationError(
                    #         {"amount": "Invalid stake amount"}
                    #     )

                    quicka_price_model = QuikaPriceModel()

                    valid_amount = quicka_price_model.get_ticket_price_details_with_stake_amount(channel="POS",
                                                                                                 stake_amount=stake_amount)
                    if valid_amount is None:
                        raise serializers.ValidationError({"amount": "Invalid stake amount"})

                    if len(lottery["lottery"]) != 1:
                        raise serializers.ValidationError({"tickets": "you can only have one line"})
                elif lotto_type == "INSTANT_CASHOUT":
                    # validate instant cash outs lines
                    restricted_lines = ConstantVariable().instant_cash_out_line_restriction()
                    if lines not in restricted_lines:
                        expected_lines = ",".join(map(str, map(int, restricted_lines)))
                        raise serializers.ValidationError(
                            {"lines": f"cannot play restricted line {lines} possible lines are {expected_lines}"})

                    stake_amount = lottery["amount"]

                    # amount = I_CASH_UPDATED_AMOUNT.get(stake_amount, None)
                    # if amount is None:
                    #     raise serializers.ValidationError(
                    #         {"amount": "Invalid stake amount"}
                    #     )

                    instant_cashout_price_model = InstantCashOutPriceModel()
                    valid_amount = instant_cashout_price_model.get_ticket_price_details_with_stake_amount(channel="POS",
                                                                                                          stake_amount=stake_amount)

                    if valid_amount is None:
                        raise serializers.ValidationError({"amount": "Invalid stake amount"})

                for lottery_item in lottery["lottery"]:
                    if len(lottery_item["ticket"]) < 4 or len(lottery_item["ticket"]) > 4:
                        raise serializers.ValidationError({"ticket": "Ticket must be between 4 and 4 numbers"})

            except KeyError as key:
                print(key, "---------------Invalid KeyError")
                raise serializers.ValidationError({"lottery_play": "Invalid key"})

        return attrs


class AmountSerializer(serializers.Serializer):
    amount = serializers.IntegerField()


class IndividualMobileUserWinningWithdrawalSerializer(serializers.Serializer):
    amount = serializers.CharField()
    pin = serializers.CharField(max_length=4, min_length=4)


class ChargeAgentOnAgencyBankingForRemittanceSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    amount = serializers.IntegerField()


class QuikaLottoPlayApiViewSerializer(serializers.Serializer):
    phone_number = serializers.CharField(allow_null=True, allow_blank=True)
    ticket_numbers = serializers.ListField()

    # pin = serializers.CharField(max_length=4, min_length=4)

    def validate(self, attrs):
        self.context.get("agent_instance")

        # pos_agent_helper = PosAgentHelper(agent_instance=agent, amount=0)

        # if pos_agent_helper.verify_agent_transaction_pin(attrs.get("pin")) is False:
        #     raise serializers.ValidationError({"pin": "Invalid pin"})

        ticket_numbers = attrs.get("ticket_numbers")

        if len(ticket_numbers) < 1:
            raise serializers.ValidationError({"ticket_numbers": "You must select at least one lottery."})

        return attrs


class KYC3Serializer(serializers.Serializer):
    verification_unique_id = serializers.CharField()
    agent_name = serializers.CharField()
    agent_phone = serializers.CharField()
    agent_email = serializers.CharField()
    guarantor_name = serializers.CharField()
    guarantor_phone = serializers.CharField()
    guarantor_email = serializers.CharField()
    guarantor_occupation = serializers.CharField()
    guarantor_address = serializers.CharField()


class AgentModelSerializer(serializers.ModelSerializer):
    class Meta:
        model = Agent
        # fields = "__all__"
        exclude = ["icash_flavour_dict", "new_quika_icash_count_to_giver", "old_quika_icash_count_to_giver"]

    def to_representation(self, obj):
        serialized_data = super(AgentModelSerializer, self).to_representation(obj)
        serialized_data["downlines_sharing_commission"] = serialized_data["downlines_sharing_commission_percentage"]

        try:
            del serialized_data["downlines_sharing_commission_percentage"]
        except KeyError:
            pass

        return serialized_data


class AgentDownLinesAccountRestrictionSerializer(serializers.Serializer):
    type_of_restriction = serializers.ChoiceField(choices=TypeOfAgentDownlinesRestriction.choices)
    agent_id = serializers.CharField()


class AgentDownLineCommissionGivingSerializer(serializers.Serializer):
    agent_id = serializers.CharField()
    commission_percentage = serializers.IntegerField()


class ManuallyCreateWinningForATicketSerializer(serializers.Serializer):
    MODEL_CHOICES = [("LOTTO_TICKET", "LOTTO_TICKET")]
    ticket_id = serializers.CharField()
    amount_won = serializers.IntegerField()
    game_play_id = serializers.CharField()
    db_model = serializers.ChoiceField(choices=MODEL_CHOICES)


class MobileUserPayoutTransactionHistory(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = PayoutTransactionTable
        fields = "__all__"

    def to_representation(self, obj):
        serialized_data = super(MobileUserPayoutTransactionHistory, self).to_representation(obj)
        # serialized_data["date"] = obj.created_at.date()
        # serialized_data["status"] = "Successful" if obj.status else "Failed"

        new_data = {}

        status = "PENDING"
        if serialized_data["disbursed"] is True:
            status = "SUCCESSFUL"
        elif serialized_data["is_verified"] is True and serialized_data["disbursed"] is False:
            status = "FAILED"

        new_data = {
            "id": serialized_data["id"],
            "agent_name": serialized_data["name"],
            "agent_phone_number": serialized_data["phone"],
            "agent_email": None,
            "type_of_agent": "OTHER_AGENT",
            "transaction_reference": serialized_data["payout_trans_ref"],
            "unique_transaction_reference": serialized_data["payout_trans_ref"],
            "amount": serialized_data["amount"],
            "bal_before": 0,
            "bal_after": 0,
            "excess_balance": 0.0,
            "game_play_bal_plus_winnings": 0,
            "status": status,
            "transaction_from": serialized_data["type_of_transaction"],
            "game_type": None,
            "game_play_id": serialized_data["game_play_id"],
            "phone_number": serialized_data["phone"],
            "transaction_type": "DEBIT",
            "date_created": serialized_data["date_added"],
            "ticket_instance": None,
            "expected_remittance": 0.0,
            "rewarded_commission": 0.0,
            "show_transaction": True,
            "agent_wallet": 2
        },

        return new_data


class ManuallyCheckAndCreateLottoWinningsSerializer(serializers.Serializer):
    MODEL_CHOICES = [("LOTTO_TICKET", "LOTTO_TICKET")]
    GAME_TYPE = [("SALARY_FOR_LIFE", "SALARY_FOR_LIFE")]

    game_id = serializers.CharField()
    winning_number = serializers.CharField()
    game_play_id = serializers.CharField()
    agent_phone_number = serializers.CharField()
    model_db = serializers.ChoiceField(choices=MODEL_CHOICES)
    game_type = serializers.ChoiceField(choices=GAME_TYPE)


class AgentCommissionWithdrawalSerializer(serializers.Serializer):
    transaction_pin = serializers.CharField()


class FetchTerminalIdDailyTransactionSerializer(serializers.Serializer):
    terminal_serial_number = serializers.CharField()


class SalesDataSerializer(serializers.Serializer):
    total_sales = serializers.FloatField()
    breakdown_per_agent = serializers.DictField()
    datewise_data = serializers.DictField()
    period = serializers.DictField()


class EarningsSerializer(serializers.Serializer):
    total_commission = serializers.FloatField()
    breakdown_per_agent = serializers.DictField()
    period = serializers.DictField()


class TargetAchievementSerializer(serializers.Serializer):
    target_achievement_percent = serializers.FloatField()
    actual_sales = serializers.FloatField()
    expected_target = serializers.FloatField()
    average_sales_per_agent_per_day = serializers.FloatField()
    agent_count = serializers.IntegerField()
    days_in_period = serializers.IntegerField()
    period = serializers.DictField()


class AgentActivitySerializer(serializers.Serializer):
    number_of_absent_agents = serializers.IntegerField
    list_of_absent_agents = serializers.ListField
    number_of_inactive_agents = serializers.IntegerField
    list_of_inactive_agents = serializers.ListField
    number_of_at_risk_agents = serializers.IntegerField
    list_of_at_risk_agents = serializers.ListField
    number_of_declining_agents = serializers.IntegerField
    list_of_declining_agents = serializers.ListField
    number_of_active_agents = serializers.IntegerField
    list_of_active_agents = serializers.ListField


class DashboardSummarySerializer(serializers.Serializer):
    sales_today = serializers.FloatField()
    today_sales_percentage_rate = serializers.FloatField()
    sales_this_week = serializers.FloatField()
    this_week_sales_percentage_rate = serializers.FloatField()
    sales_this_month = serializers.FloatField()
    this_month_sales_percentage_rate = serializers.FloatField()
    agent_activity = serializers.FloatField()
    agent_activity_values = serializers.DictField()
    target_achievement_percent = serializers.FloatField()
    total_agent_count = serializers.IntegerField()
    earnings = serializers.FloatField()


class LottoAgentSalesActivitySerializers(serializers.Serializer):
    class Meta:
        model = LottoAgentSalesActivity
        fields = "__all__"
