import uuid
from datetime import datetime

from celery import shared_task
from django.db.models import Sum

from main.api.api_lottery_helpers import generate_game_play_id
from main.helpers.whisper_sms_managers import send_sms_winning_to_players_from_agent
from main.models import (
    LotteryBatch,
    LotteryGlobalJackPot,
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    UserProfile,
)
from pos_app.models import AgentWallet, BoughtLotteryTickets, PosLotteryWinners
from pos_app.sport_helper.func import generate_ticket_pin
from pos_app.utils import serialize_ticket


def create_retail_lottery(agent, serialized_data, combined_retail_ticket):
    game_ids = []

    global_ticket_pin = generate_ticket_pin()
    global_ussd_code = BoughtLotteryTickets.generate_ussd_code()

    user_profile = UserProfile.objects.filter(phone_number=agent.phone).last()
    if user_profile is None:
        user_profile = UserProfile.objects.create(phone_number=agent.phone)

    for lottery_type, lottery_data in serialized_data.items():
        # print(lottery_type)

        for lottery_item in lottery_data:
            # print("no_of_lines", lottery_item.get("no_of_lines"))
            # print("quantity", lottery_item.get("quantity"))
            # print("amount", lottery_item.get("amount"))

            game_id = BoughtLotteryTickets.create_lottery_ticket(
                agent=agent,
                game_type=str(lottery_type).upper(),
                no_of_lines=lottery_item.get("no_of_lines"),
                quanity=lottery_item.get("quantity"),
                band=lottery_item.get("band"),
                combined_retail_ticket=combined_retail_ticket,
                global_ticket_pin=global_ticket_pin,
                global_ussd_code=global_ussd_code,
            )

            for x in game_id:
                if x is not None:
                    game_ids.append(x)

    result = {"salary_for_life": [], "instant_cashout": [], "wyse_cash": []}

    if game_ids:
        for game_id in game_ids:
            game_qs = BoughtLotteryTickets.objects.filter(game_id=game_id)

            if game_qs.exists():
                game = game_qs.last()

                if game.game_type == "SALARY_FOR_LIFE":
                    data = {
                        "game_play_id": game.game_id,
                        "ussd_code": game.ussd_code,
                        "pin": game.pin,
                        "no_of_lines": game_qs.count(),
                        "amount": game_qs.aggregate(Sum("amount"))["amount__sum"],
                        "pontential_winning": game.pontential_win,
                        "tickets": [],
                    }

                    sal_4_life_default_price = LottoTicket.salary_for_life_stake_amount_pontential_winning(game_qs.count())

                    ticket_data = []

                    identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                    for ticket in game_qs:
                        ticket_data.append(
                            {
                                "ticket": [int(x) for x in ticket.ticket.split(",")],
                            }
                        )

                        # register the game in lottoticket model
                        sal_4_life_batch = LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active=True).last()
                        if sal_4_life_batch is None:
                            sal_4_life_batch = LotteryBatch.objects.create(
                                lottery_type="SALARY_FOR_LIFE",
                                global_jackpot=LotteryGlobalJackPot().get_jackpot_instance(),
                            )

                        lotto_instance = LottoTicket.objects.create(
                            batch=sal_4_life_batch,
                            user_profile=user_profile,
                            agent_profile=agent,
                            phone=agent.phone,
                            stake_amount=sal_4_life_default_price.get("stake_amount") / game_qs.count(),
                            expected_amount=sal_4_life_default_price.get("stake_amount") / game_qs.count(),
                            potential_winning=sal_4_life_default_price.get("total_winning_amount"),
                            number_of_ticket=game.no_of_tickets,
                            channel="POS_AGENT",
                            game_play_id=game.game_id,
                            lottery_type="SALARY_FOR_LIFE",
                            ticket=game.ticket,
                            pin=game.pin,
                            identity_id=identity_id,
                        )

                        lotto_instance.paid = True
                        lotto_instance.amount_paid = sal_4_life_default_price.get("stake_amount") / game_qs.count()
                        lotto_instance.save()

                    data["tickets"] = ticket_data

                    result["salary_for_life"].append(data)

                elif game.game_type == "INSTANT_CASHOUT":
                    data = {
                        "game_play_id": game.game_id,
                        "ussd_code": game.ussd_code,
                        "pin": game.pin,
                        "no_of_lines": game_qs.count(),
                        "amount": game_qs.aggregate(Sum("amount"))["amount__sum"],
                        "pontential_winning": game.pontential_win,
                        "tickets": [],
                    }

                    ticket_data = []

                    instant_cashout_default_price = LottoTicket.instant_stake_amount_pontential_winning(game_qs.count())

                    identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                    for ticket in game_qs:
                        ticket_data.append(
                            {
                                "ticket": [int(x) for x in ticket.ticket.split(",")],
                            }
                        )

                        # register the game in lottoticket model
                        inst_csh_batch = LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT", is_active=True).last()

                        if inst_csh_batch is None:
                            inst_csh_batch = LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT")
                        lotto_instance = LottoTicket.objects.create(
                            batch=inst_csh_batch,
                            user_profile=user_profile,
                            agent_profile=agent,
                            phone=agent.phone,
                            stake_amount=instant_cashout_default_price.get("stake_amount") / game_qs.count(),
                            expected_amount=instant_cashout_default_price.get("stake_amount") / game_qs.count(),
                            potential_winning=instant_cashout_default_price.get("total_winning_amount"),
                            number_of_ticket=game.no_of_tickets,
                            channel="POS_AGENT",
                            game_play_id=game.game_id,
                            lottery_type="INSTANT_CASHOUT",
                            ticket=game.ticket,
                            pin=game.pin,
                            identity_id=identity_id,
                        )

                        lotto_instance.paid = True
                        lotto_instance.amount_paid = instant_cashout_default_price.get("stake_amount") / game_qs.count()
                        lotto_instance.save()

                    data["tickets"] = ticket_data

                    result["instant_cashout"].append(data)

                elif game.game_type == "WYSE_CASH":
                    data = {
                        "game_play_id": game.game_id,
                        "ussd_code": game.ussd_code,
                        "pin": game.pin,
                        "no_of_lines": game_qs.count(),
                        "amount": game_qs.aggregate(Sum("amount"))["amount__sum"],
                        "pontential_winning": game_qs.aggregate(Sum("pontential_win"))["pontential_win__sum"],
                    }

                    ticket_data = []
                    for ticket in game_qs:
                        ticket_data.append(
                            {
                                "ticket": [x for x in ticket.ticket.split(",")],
                            }
                        )

                        # register the game in lottoticket model
                        wyse_cash_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH", is_active=True).last()

                        if wyse_cash_batch is None:
                            wyse_cash_batch = LotteryBatch.objects.create(lottery_type="WYSE_CASH")

                        lotto_instance = LotteryModel.objects.create(
                            batch=wyse_cash_batch,
                            user_profile=user_profile,
                            agent_profile=agent,
                            phone=agent.phone,
                            stake_amount=LotteryModel.wyse_cash_band_price(int(game.pontential_win)),
                            expected_amount=LotteryModel.wyse_cash_band_price(int(game.pontential_win)),
                            band=game.pontential_win,
                            pool=LotteryModel.wyse_cash_pool(int(game.pontential_win)),
                            instance_number=game.no_of_tickets,
                            channel="POS_AGENT",
                            game_play_id=game.game_id,
                            lottery_type="WYSE_CASH",
                            lucky_number=game.ticket,
                            pin=game.pin,
                        )

                        lotto_instance.paid = True
                        lotto_instance.amount_paid = LotteryModel.wyse_cash_band_price(int(game.pontential_win))
                        lotto_instance.save()

                    data["tickets"] = ticket_data

                    result["wyse_cash"].append(data)

    # remove  item with empty list
    result = {k: v for k, v in result.items() if v}
    return result


def create_retail_bonus_lottery(agent):
    agent_wallet = AgentWallet.objects.filter(agent=agent).last()

    # what percentage did, a,b and c use to share 5000 if a = 2300, b = 1400, c = 1300
    # a = 2300/5000 = 0.46
    # b = 1400/5000 = 0.28
    # c = 1300/5000 = 0.26

    instant_cashout_amount = agent_wallet.used_bonus_bal * 0.46
    salary_for_life_amount = agent_wallet.used_bonus_bal * 0.28
    wyse_cash_amount = agent_wallet.used_bonus_bal * 0.26

    # instant_cashout_amount_used = 0
    # # salary_for_life_amount = 0
    # wyse_cash_amount = 0

    game_ids = []

    lottery_types = ["instant_cashout", "salary_for_life", "wyse_cash"]

    for lottery_type in lottery_types:
        if lottery_type == "instant_cashout":
            game_id, amount_used = instant_cashout_lottery(agent, instant_cashout_amount)

            print("game_id", game_id)
            print("amount_used", amount_used)

            for x in game_id:
                if x is not None:
                    game_ids.append(x)

            agent_wallet.used_bonus_bal -= amount_used
            agent_wallet.save()

        elif lottery_type == "salary_for_life":
            game_id, amount_used = salary_for_life_lottery(agent, salary_for_life_amount)

            print("game_id", game_id)
            print("amount_used", amount_used)

            for x in game_id:
                if x is not None:
                    game_ids.append(x)

            agent_wallet.used_bonus_bal -= amount_used
            agent_wallet.save()

        elif lottery_type == "wyse_cash":
            game_id, amount_used = salary_for_life_lottery(agent, wyse_cash_amount)

            print("game_id", game_id)
            print("amount_used", amount_used)

            for x in game_id:
                if x is not None:
                    game_ids.append(x)

            agent_wallet.used_bonus_bal -= amount_used
            agent_wallet.save()

    print("game_ids for instant cashout", game_ids)

    result = {"salary_for_life": [], "instant_cashout": [], "wyse_cash": []}

    if game_ids:
        for game_id in game_ids:
            game_qs = BoughtLotteryTickets.objects.filter(game_id=game_id)

            if game_qs.exists():
                game = game_qs.last()

                if game.game_type == "SALARY_FOR_LIFE":
                    data = {
                        "ussd_code": game.ussd_code,
                        "pin": game.pin,
                        "no_of_lines": game_qs.count(),
                        "amount": game_qs.aggregate(Sum("amount"))["amount__sum"],
                        "pontential_winning": game.pontential_win,
                        "tickets": [],
                    }

                    ticket_data = []
                    for ticket in game_qs:
                        ticket_data.append(
                            {
                                "ticket": [int(x) for x in ticket.ticket.split(",")],
                            }
                        )

                    data["tickets"] = ticket_data

                    result["salary_for_life"].append(data)

                elif game.game_type == "INSTANT_CASHOUT":
                    data = {
                        "ussd_code": game.ussd_code,
                        "pin": game.pin,
                        "no_of_lines": game_qs.count(),
                        "amount": game_qs.aggregate(Sum("amount"))["amount__sum"],
                        "pontential_winning": game.pontential_win,
                        "tickets": [],
                    }

                    ticket_data = []
                    for ticket in game_qs:
                        ticket_data.append(
                            {
                                "ticket": [int(x) for x in ticket.ticket.split(",")],
                            }
                        )

                    data["tickets"] = ticket_data

                    result["instant_cashout"].append(data)

                elif game.game_type == "WYSE_CASH":
                    data = {
                        "ussd_code": game.ussd_code,
                        "pin": game.pin,
                        "no_of_lines": game_qs.count(),
                        "amount": game_qs.aggregate(Sum("amount"))["amount__sum"],
                        "pontential_winning": game_qs.aggregate(Sum("pontential_win"))["pontential_win__sum"],
                    }

                    ticket_data = []
                    for ticket in game_qs:
                        ticket_data.append(
                            {
                                "ticket": [x for x in ticket.ticket.split(",")],
                            }
                        )

                    data["tickets"] = ticket_data

                    result["wyse_cash"].append(data)

    # remove  item with empty list
    result = {k: v for k, v in result.items() if v}
    return result


def instant_cashout_lottery(agent, amount, amount_used=0, instant_cashout_game_ids=[]):
    # decide how many lines to buy
    if LottoTicket.instant_stake_amount_pontential_winning(5).get("stake_amount") > amount:
        if LottoTicket.instant_stake_amount_pontential_winning(4).get("stake_amount") > amount:
            if LottoTicket.instant_stake_amount_pontential_winning(3).get("stake_amount") > amount:
                if LottoTicket.instant_stake_amount_pontential_winning(2).get("stake_amount") > amount:
                    if LottoTicket.instant_stake_amount_pontential_winning(1).get("stake_amount") > amount:
                        no_of_lines = 0
                    else:
                        no_of_lines = 1
                else:
                    no_of_lines = 2
            else:
                no_of_lines = 3
        else:
            no_of_lines = 4
    else:
        no_of_lines = 5

    game_ids = []

    total_amount_used_to_play = 0
    game_id_list = instant_cashout_game_ids

    deducting_amount = amount

    ticket_pin = generate_ticket_pin()

    if no_of_lines > 0:
        for i in range(0, 1):
            game_play_id = generate_game_play_id()
            ussd_code = BoughtLotteryTickets().generate_ussd_code()

            for n in range(0, no_of_lines):
                (
                    stake_amount,
                    pontential_win,
                ) = LottoTicket.instant_stake_amount_pontential_winning(no_of_lines).get(
                    "stake_amount"
                ) / no_of_lines, LottoTicket.instant_stake_amount_pontential_winning(no_of_lines).get("total_winning_amount")

                ticket = serialize_ticket(BoughtLotteryTickets().generate_lottery_ticket(lottery_type="INSTANT_CASHOUT"))

                if deducting_amount < stake_amount:
                    continue

                BoughtLotteryTickets.objects.create(
                    agent=agent,
                    game_type="INSTANT_CASHOUT",
                    amount=stake_amount,
                    pontential_win=pontential_win,
                    game_id=game_play_id,
                    ticket=ticket,
                    no_of_tickets=no_of_lines,
                    ussd_code=ussd_code,
                    paid=True,
                    is_bonus_ticket=True,
                    pin=ticket_pin,
                )

                deducting_amount -= stake_amount

            game_ids.append(game_play_id)

            total_amount_used_to_play += stake_amount * no_of_lines

        game_id_list += game_ids + instant_cashout_game_ids

        if total_amount_used_to_play < amount:
            amount = amount - total_amount_used_to_play
            amount_used = amount_used + total_amount_used_to_play

            return instant_cashout_lottery(
                agent=agent,
                amount=amount,
                amount_used=amount_used,
                instant_cashout_game_ids=game_id_list,
            )

        else:
            return list(set(game_id_list)), amount_used

    else:
        return list(set(game_id_list)), amount_used


def salary_for_life_lottery(agent, amount, amount_used=0, instant_cashout_game_ids=[]):
    # decide how many lines to buy
    if LottoTicket.salary_for_life_stake_amount_pontential_winning(5).get("stake_amount") > amount:
        if LottoTicket.salary_for_life_stake_amount_pontential_winning(4).get("stake_amount") > amount:
            if LottoTicket.salary_for_life_stake_amount_pontential_winning(3).get("stake_amount") > amount:
                if LottoTicket.salary_for_life_stake_amount_pontential_winning(2).get("stake_amount") > amount:
                    if LottoTicket.salary_for_life_stake_amount_pontential_winning(1).get("stake_amount") > amount:
                        no_of_lines = 0
                    else:
                        no_of_lines = 1
                else:
                    no_of_lines = 2
            else:
                no_of_lines = 3
        else:
            no_of_lines = 4
    else:
        no_of_lines = 5

    game_ids = []

    total_amount_used_to_play = 0
    game_id_list = instant_cashout_game_ids

    deducting_amount = amount

    ticket_pin = generate_ticket_pin()

    if no_of_lines > 0:
        for i in range(0, 1):
            game_play_id = generate_game_play_id()
            ussd_code = BoughtLotteryTickets().generate_ussd_code()

            for n in range(0, no_of_lines):
                (
                    stake_amount,
                    pontential_win,
                ) = LottoTicket.salary_for_life_stake_amount_pontential_winning(no_of_lines).get(
                    "stake_amount"
                ) / no_of_lines, LottoTicket.salary_for_life_stake_amount_pontential_winning(no_of_lines).get("total_winning_amount")

                ticket = serialize_ticket(BoughtLotteryTickets().generate_lottery_ticket(lottery_type="SALARY_FOR_LIFE"))

                if deducting_amount < stake_amount:
                    continue

                BoughtLotteryTickets.objects.create(
                    agent=agent,
                    game_type="SALARY_FOR_LIFE",
                    amount=stake_amount,
                    pontential_win=pontential_win,
                    game_id=game_play_id,
                    ticket=ticket,
                    no_of_tickets=no_of_lines,
                    ussd_code=ussd_code,
                    paid=True,
                    is_bonus_ticket=True,
                    pin=ticket_pin,
                )

                deducting_amount -= stake_amount

            game_ids.append(game_play_id)

            total_amount_used_to_play += stake_amount * no_of_lines

        game_id_list += game_ids + instant_cashout_game_ids

        if total_amount_used_to_play < amount:
            amount = amount - total_amount_used_to_play
            amount_used = amount_used + total_amount_used_to_play

            return salary_for_life_lottery(
                agent=agent,
                amount=amount,
                amount_used=amount_used,
                instant_cashout_game_ids=game_id_list,
            )

        else:
            return list(set(game_id_list)), amount_used

    else:
        return list(set(game_id_list)), amount_used


def wyse_cash_lottery(agent, amount, amount_used=0, instant_cashout_game_ids=[]):
    # decide how many lines to buy
    if 500 > amount:
        if 400 > amount:
            if 300 > amount:
                if 200 > amount:
                    if 100 > amount:
                        no_of_lines = 0
                    else:
                        no_of_lines = 1
                else:
                    no_of_lines = 2
            else:
                no_of_lines = 3
        else:
            no_of_lines = 4
    else:
        no_of_lines = 5

    game_ids = []

    deducting_amount = amount

    total_amount_used_to_play = 0
    game_id_list = instant_cashout_game_ids

    ticket_pin = generate_ticket_pin()

    if no_of_lines > 0:
        for i in range(0, 1):
            game_play_id = generate_game_play_id()
            ussd_code = BoughtLotteryTickets().generate_ussd_code()

            for n in range(0, no_of_lines):
                ticket = BoughtLotteryTickets.generate_wyse_cash_ticket()

                stake_amount = 100

                if deducting_amount < stake_amount:
                    continue

                BoughtLotteryTickets.objects.create(
                    agent=agent,
                    game_type="WYSE_CASH",
                    amount=stake_amount,
                    pontential_win=10000,
                    game_id=game_play_id,
                    ticket=ticket,
                    no_of_tickets=no_of_lines,
                    ussd_code=ussd_code,
                    paid=True,
                    is_bonus_ticket=True,
                    pin=ticket_pin,
                )

                deducting_amount -= stake_amount

            game_ids.append(game_play_id)

            total_amount_used_to_play += stake_amount * no_of_lines

        game_id_list += game_ids + instant_cashout_game_ids

        if total_amount_used_to_play < amount:
            amount = amount - total_amount_used_to_play
            amount_used = amount_used + total_amount_used_to_play

            return wyse_cash_lottery(
                agent=agent,
                amount=amount,
                amount_used=amount_used,
                instant_cashout_game_ids=game_id_list,
            )

        else:
            return list(set(game_id_list)), amount_used

    else:
        return list(set(game_id_list)), amount_used


def validate_retail_amount(serialized_data, amount) -> bool:
    total_amount_to_charge = 0

    # print(
    #     f"""
    # serialized_data: {serialized_data}
    # amount: {amount}
    # """
    # )

    for lottery_type, lottery_data in serialized_data.items():
        # print(f"lottery_type: {lottery_type}")
        # print(f"lottery_data: {lottery_data}")
        # print("\n\n\n\n\n\n\n")

        if not isinstance(lottery_data, list):
            continue

        for lottery_item in lottery_data:
            if str(lottery_type).upper() == "INSTANT_CASHOUT".upper():
                for i in range(0, lottery_item.get("quantity")):
                    total_amount_to_charge += LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
                        ticket_count=int(lottery_item.get("no_of_lines"))
                    ).get("stake_amount")

            elif str(lottery_type).upper() == "SALARY_FOR_LIFE".upper():
                for i in range(0, lottery_item.get("quantity")):
                    total_amount_to_charge += LottoTicket.salary_for_life_stake_amount_pontential_winning_for_lotto_agents(
                        ticket_count=int(lottery_item.get("no_of_lines"))
                    ).get("stake_amount")

            elif str(lottery_type).upper() == "WYSE_CASH".upper():
                # print(
                #     f"""

                #       WYSE CASH lottery_item.get("no_of_lines"): {lottery_item.get("no_of_lines")}
                #       lottery_item.get("band"): {lottery_item.get("band")}

                #       """
                # )
                for i in range(0, lottery_item.get("quantity")):
                    # print(
                    #     f"""

                    # WYSE CASH lottery_item.get("no_of_lines"): {lottery_item.get("no_of_lines")}
                    # lottery_item.get("band"): {lottery_item.get("band")}

                    # \n\n\n\n\n\n

                    # """
                    # )
                    try:
                        total_amount_to_charge += LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
                            band=int(lottery_item.get("band")),
                            no_of_line=int(lottery_item.get("no_of_lines")),
                        ).get("stake_amount")
                    except Exception:
                        break

    if total_amount_to_charge == amount:
        return True
    else:
        return False


@shared_task
def check_if_register_ticket_batch_is_active(lotto_instanc_id):
    """
    CHECK IF LOTTO BATCH IS ACTIVE, IF NOT,
    CHECK IF THE GAME WON, IF YES,
    ASSIGN THE PLAYER TO THE WINNING LOTTO
    """

    lotto_instance = LottoTicket.objects.filter(id=lotto_instanc_id).last()
    if lotto_instance is None:
        lotto_instance = LotteryModel.objects.filter(id=lotto_instanc_id).last()
        if lotto_instance is None:
            return {"status": "error", "message": "lotto instance not found"}

    if lotto_instance.batch.is_active is False:
        #  check the type of game
        if lotto_instance.lottery_type == "SALARY_FOR_LIFE" or lotto_instance.lottery_type == "INSTANT_CASHOUT":
            # check if the game won
            lotto_winner_instance = LottoWinners.objects.filter(game_play_id=lotto_instance.game_play_id).last()
            # if found in winners table, let's check if the winning has not be withdrawn
            if lotto_winner_instance is not None:
                pos_winner_instance = PosLotteryWinners.objects.filter(
                    game_id=lotto_instance.game_play_id,
                    is_win_claimed=False,
                    withdrawl_initiated=False,
                )
                if pos_winner_instance.exists():
                    user_profile = UserProfile.objects.filter(phone_number=lotto_instance.user_profile.phone_number).last()
                    if pos_winner_instance.last().agent.phone != user_profile.phone_number:
                        pos_winner_instance.update(player=user_profile)

                        lotto_winner_instance.phone_number = user_profile.phone_number
                        lotto_winner_instance.save()

                        # send pin to player
                        try:
                            send_sms_winning_to_players_from_agent(
                                phone_number=user_profile.phone_number,
                                amount_won=pos_winner_instance.aggregate(Sum("amount_won"))["amount_won__sum"],
                                game_play_id=pos_winner_instance.last().game_id,
                                agent_name=f"{pos_winner_instance.last().agent.name}",
                                pin=pos_winner_instance.last().pin,
                            )
                        except Exception:
                            pass

                        return {
                            "status": True,
                            "message": "Player has been assigned to the winning lottery",
                        }

        elif lotto_instance.lottery_type == "WYSE_CASH":
            lotto_winner_instance = LotteryWinnersTable.objects.filter(game_play_id=lotto_instance.game_play_id).last()

            if lotto_winner_instance is not None:
                pos_winner_instance = PosLotteryWinners.objects.filter(
                    game_id=lotto_instance.game_play_id,
                    is_win_claimed=False,
                    withdrawl_initiated=False,
                )
                if pos_winner_instance.exists():
                    user_profile = UserProfile.objects.filter(phone_number=lotto_instance.user_profile.phone_number).last()
                    if pos_winner_instance.last().agent.phone != user_profile.phone_number:
                        pos_winner_instance.update(player=user_profile)

                        lotto_winner_instance.phone_number = user_profile.phone_number
                        lotto_winner_instance.save()

                        # send pin to player
                        try:
                            send_sms_winning_to_players_from_agent(
                                phone_number=user_profile.phone_number,
                                amount_won=pos_winner_instance.aggregate(Sum("amount_won"))["amount_won__sum"],
                                game_play_id=pos_winner_instance.last().game_id,
                                agent_name=f"{pos_winner_instance.last().agent.name}",
                                pin=pos_winner_instance.last().pin,
                            )
                        except Exception:
                            pass

                        return {
                            "status": True,
                            "message": "Player has been assigned to the winning lottery",
                        }

        return {"status": False, "message": "invalid game type"}

    return {"status": False, "message": "Lottery batch is still active"}
