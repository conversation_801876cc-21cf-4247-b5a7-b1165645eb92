import base64
import io
import json
import random
import re
import uuid
from datetime import datetime, time
from random import randint
from time import sleep

import cloudinary
import cloudinary.api
import cloudinary.uploader
import pytz
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.db.models import Count, Q, Sum, UniqueConstraint
from django.db.models.signals import post_save
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from PIL import Image

from main.helpers.redis_storage import RedisStorage
from main.helpers.whisper_sms_managers import send_sms_winning_to_players_from_agent
from main.helpers.woven_manager import woven_verify_transaction
from main.tasks import celery_tokenize_paystack_card
from overide_print import print
from pos_app import enums
from pos_app.sport_helper.func import generate_ticket_pin
from pos_app.utils import DommyName, serialize_ticket
from retail_metrics.models import (
    GameAnalytics,
    LottoAgentMonthlyAnalytic,
    RetailWalletTransactions,
    ReturnToPlayerAndReturnToOwnerAnalytics,
)
from wallet_app.helpers.payment_gateway import PaymentGateway
from wallet_system.models import TerminalPrefundingMoneyTransfer, Wallet

from .tasks import (
    celery_making_sure_agent_remittance_record_are_updated,
    celery_trigger_agent_reward,
    create_agent_vfd_virtual_wallet,
)


def new_quika_icash_count_to_giver_func():
    return {
        "200": 0,
        "500": 0,
        "750": 0,
        "1000": 0,
        "1250": 0,
        "1300": 0,
        "1500": 0,
    }


def old_quika_icash_count_to_giver_func():
    return {
        "150": 0,
        "300": 0,
        "450": 0,
        "600": 0,
        "750": 0,
        "900": 0,
        "1000": 0,
    }


class Agent(models.Model):
    AGENT_TYPE = (
        ("AGENT", "AGENT"),
        ("MERCHANT", "MERCHANT"),
        ("LOTTO_AGENT", "LOTTO_AGENT"),
        ("PERSONAL", "PERSONAL"),
        ("LIBERTY_RETAIL", "LIBERTY_RETAIL"),
    )

    SHARING = [
        "BLACK",
        "BLACK",
        "WHITE",
        "BLACK",
        "WHITE",
        "BLACK",
        "BLACK",
        "BLACK",
        "BLACK",
        "BLACK",
        "WHITE",
        "BLACK",
        "WHITE",
        "BLACK",
        "WHITE",
    ]

    AGENT_PERFORMANCE_STATUS = (
        ("PERFORMING", "PERFORMING"),
        ("INACTIVE", "INACTIVE"),
        ("SUPER_PERFORMING", "SUPER_PERFORMING"),
        ("HIGH_PERFORMING", "HIGH_PERFORMING"),
        ("TOP_PERFORMING", "TOP_PERFORMING"),
        ("UNDER_PERFORMING", "UNDER_PERFORMING"),
        ("ABSENT", "ABSENT"),
    )

    DRAW_STYLES = (
        ("GLOBAL", "GLOBAL"),
        ("LOCAL", "LOCAL"),
    )

    WAVES = (
        ("WAVE_ONE", "WAVE_ONE"),
        ("WAVE_TWO", "WAVE_TWO"),
    )

    first_name = models.CharField(max_length=300)
    last_name = models.CharField(max_length=300)
    full_name = models.CharField(max_length=300, blank=True, null=True)
    phone = models.CharField(max_length=300)
    personal_phone_number = models.CharField(max_length=300, blank=True, null=True)
    email = models.CharField(max_length=300)
    user_id = models.CharField(max_length=300)
    user_uuid = models.CharField(max_length=500, default=uuid.uuid4)
    has_web_virtual_account = models.BooleanField(default=False)
    has_vfd_virtual_account = models.BooleanField(default=False)
    created_date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    icash2_draw_mode = models.CharField(
        max_length=100,
        choices=DRAW_STYLES,
        default="GLOBAL",
        help_text="Whether to draw instant cashout globally or locally.",
    )
    address = models.CharField(max_length=300, default="YABA, Lagos Nigeria")
    agent_type = models.CharField(max_length=300, choices=AGENT_TYPE, default="AGENT")
    icash_bonus_left = models.IntegerField(default=12)
    wyse_bonuses_received = models.IntegerField(default=0)
    is_suspended = models.BooleanField(default=False)
    agent_icash_sold = models.FloatField(default=0)
    icash_sold = models.IntegerField(default=0)
    icash_sold_amount = models.FloatField(default=0)
    icash_sold_game_ids = models.JSONField(
        default=list,
        blank=True,
        null=True,
    )
    icash_sold_dict = models.JSONField(default=dict)
    icash_flavour_dict = models.JSONField(default=dict, blank=True, null=True)
    giver_deployed = models.BooleanField(default=False)
    count_to_giver = models.IntegerField(default=0)
    new_quika_icash_count_to_giver = models.JSONField(
        default=new_quika_icash_count_to_giver_func,
        help_text="Total icash winnnings before bonus giver",
    )
    old_quika_icash_count_to_giver = models.JSONField(
        default=old_quika_icash_count_to_giver_func,
        help_text="Total icash winnnings before bonus giver",
    )
    icash2_bonus_left = models.FloatField(default=0)
    icash_excesses_from_count_before = models.FloatField(default=0)
    super_agent = models.ForeignKey(
        "LottoSuperAgents",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="super_agent",
    )
    business_development_agent = models.ForeignKey(
        "LottoSuperAgents",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="business_development_agent",
    )
    relationship_manager = models.ForeignKey(
        "LottoSuperAgents",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="business_manager",
    )

    is_super_agent = models.BooleanField(default=False)
    terminal_id = models.CharField(max_length=300, null=True, blank=True)
    terminal_serial_number = models.CharField(max_length=300, null=True, blank=True)
    sales_rep = models.ForeignKey("SalesReps", on_delete=models.CASCADE, blank=True, null=True)
    business_development_agent_phone_number = models.CharField(max_length=300, blank=True, null=True)
    is_sales_rep_counted = models.BooleanField(default=False)
    is_sales_rep = models.BooleanField(default=False)
    suspended_on_agency_banking = models.BooleanField(default=False)
    should_have_super_agent = models.BooleanField(default=False)
    is_winwise_staff_agent = models.BooleanField(default=False)
    suspension_reason = models.CharField(max_length=300, blank=True, null=True)
    un_suspension_reason = models.CharField(max_length=300, blank=True, null=True)
    performance_status = models.CharField(max_length=300, choices=AGENT_PERFORMANCE_STATUS, default="PERFORMING")
    supervisor = models.ForeignKey("Supervisor", on_delete=models.CASCADE, blank=True, null=True)
    is_supervisor = models.BooleanField(default=False)
    has_pre_funding = models.BooleanField(default=False)
    terminal_retrieved = models.BooleanField(default=False)
    terminal_collected = models.BooleanField(default=False)
    has_termina_id = models.BooleanField(default=False)
    can_withdraw = models.BooleanField(default=True)
    downlines_sharing_commission_percentage = models.IntegerField(
        default=0, help_text="Percentage of commission to share with downlines. it should be between 0 and 100"
    )
    wave = models.CharField(max_length=125, choices=WAVES, blank=True, null=True)
    has_settled_pre_funding = models.BooleanField(default=False)
    is_a_vertical_lead = models.BooleanField(default=False)

    suspending_from_task = False

    def __str__(self):
        return self.first_name + " " + self.last_name

    @property
    def unique_tickets_sold(self):
        return len(set(self.icash_sold_game_ids))

    def save(self, *args, **kwargs):
        if not self.icash_sold_dict:
            self.icash_sold_dict = {"150": 0}

        if not self.icash_flavour_dict:
            self.icash_sold_dict = {"150": 0}

        if not self.pk:
            self.full_name = self.first_name + " " + self.last_name

            if "win" in str(self.full_name).casefold() and "wise" in str(self.full_name).casefold():
                self.should_have_super_agent = True
                self.is_winwise_staff_agent = True

            if Agent.objects.filter(user_id=self.user_id).exists():
                return None

        if not self.icash_flavour_dict:
            self.icash_flavour_dict = {
                "150": self.SHARING,
                "300": self.SHARING,
                "450": self.SHARING,
                "600": self.SHARING,
                "750": self.SHARING,
                "900": self.SHARING,
            }

        return super(Agent, self).save(*args, **kwargs)

    @property
    def name(self):
        return self.first_name + " " + self.last_name

    class Meta:
        ordering = ["-created_date"]
        verbose_name = "AGENT"
        verbose_name_plural = "AGENTS"

    # REDUCE NUMBER OF BONUSES LEFT
    def reduce_icash_bonus_left(self):
        self.icash_bonus_left -= 1
        if self.icash_bonus_left > -1:
            self.save()
            return True
        else:
            return False

    def update_giver(self, stake_amount, reset=False):
        from main.models import ConstantVariable

        ConstantVariable.objects.all().last()

        giver_dict = self.new_quika_icash_count_to_giver

        if reset:
            giver_dict[stake_amount] = 0
        else:
            giver_dict[stake_amount] = giver_dict[stake_amount] + 1

        print(giver_dict)
        self.new_quika_icash_count_to_giver = giver_dict
        self.save()

        return True

    @staticmethod
    def check_if_string_contains_special_characters(phone) -> bool:
        special_characters = "!@#$%^&*()_+{}|:<>?[]\;',./`~"  # noqa
        for char in phone:
            if char in special_characters:
                return True
        return False

    @staticmethod
    def check_if_phone_number_is_valid(phone) -> bool:
        # check if phone number contains alphabets
        if re.search("[a-zA-Z]", phone):
            return False
        if len(phone) != 11:
            return False
        if Agent.check_if_string_contains_special_characters(phone):
            return False
        return True

    @staticmethod
    def check_if_email_is_valid(email) -> bool:
        regex = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"

        if re.fullmatch(regex, email):
            return True

        else:
            return False

    @staticmethod
    def check_if_account_number_is_valid(account_number) -> bool:
        if len(account_number) != 10:
            return False
        if Agent.check_if_string_contains_special_characters(account_number):
            return False
        return True

    def get_duration(self):
        """Calculate the duration since the user was created."""
        if not self.created_date:
            return "Unknown"

        current_time = datetime.now()
        created_time = self.created_date.replace(tzinfo=None)  # Ensure timezone compatibility
        delta = relativedelta(current_time, created_time)

        duration_parts = []
        if delta.years:
            duration_parts.append(f"{delta.years} year{'s' if delta.years > 1 else ''}")
        if delta.months:
            duration_parts.append(f"{delta.months} month{'s' if delta.months > 1 else ''}")

        return ", ".join(duration_parts) if duration_parts else "Less than a month"

    def get_supervisor_queryset(self):
        supervisor_query_set = self.objects.filter(
            supervisor="email",
        ).last()

        return supervisor_query_set

    def get_wave(self):
        if self.created_date.year < 2025:
            return "WAVE_ONE"
        else:
            if self.created_date.month > 2:
                return "WAVE_TWO"
            else:
                return "WAVE_ONE"

    @property
    def fetch_supervisor_detail_on_agency_banking_and_update(self):
        from pos_app.pos_helpers import get_agent_sales_rep_super_agent_and_supervisor

        response = get_agent_sales_rep_super_agent_and_supervisor(self.user_id)
        if isinstance(response, dict):
            if response.get("data", {}).get("supervisor") is not None:

                supervisor_data = response.get("data", {}).get("supervisor")

                # check if supervisor exists
                try:
                    supervisor_instance = Supervisor.objects.get(
                        email=supervisor_data.get("email"),
                    )
                except Supervisor.DoesNotExist:
                    supervisor_instance = Supervisor.objects.create(
                        first_name=supervisor_data.get("first_name"),
                        last_name=supervisor_data.get("last_name"),
                        email=supervisor_data.get("email"),
                        phone=supervisor_data.get("phone_number"),
                        user_id=supervisor_data.get("id"),
                    )
                except Supervisor.MultipleObjectsReturned:
                    supervisor_instance = Supervisor.objects.filter(
                        email=supervisor_data.get("email"),
                    ).last()

                supervisor_instance.is_active = True
                supervisor_instance.save()
                supervisor_instance.refresh_from_db()

                self.supervisor = supervisor_instance
                self.save()
                self.refresh_from_db()


class AgentOnBoardingPayload(models.Model):
    phone = models.CharField(max_length=300)
    payload = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class AgentBonus(models.Model):
    LOTTO_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSECASH", "WYSECASH"),
        ("QUIKA", "QUIKA"),
    ]

    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    lottery_type = models.CharField(max_length=150, choices=LOTTO_TYPE, default="GAMETYPE")
    created_at = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    sales_at_bonus = models.IntegerField(default=0)
    icash_bonus_left = models.IntegerField(default=0)
    tickets_at_time = models.IntegerField(default=0)
    amount = models.FloatField(default=0)

    @classmethod
    def create_bonus(
        cls,
        agent,
        lotto_type,
        icash_bonus_left,
        amount,
        tickets_at_time=0,
        sales_at_bonus=0,
    ):
        cls.objects.create(
            agent=agent,
            lottery_type=lotto_type,
            icash_bonus_left=icash_bonus_left,
            amount=amount,
            tickets_at_time=tickets_at_time,
            sales_at_bonus=sales_at_bonus,
        )

        # agent.wyse_bonuses_received += 1
        # agent.save()

    def save(self, *args, **kwargs):
        if not self.id and (self.lottery_type == "SALARY_FOR_LIFE" or self.lottery_type == "WYSECASH"):
            agent = self.agent
            agent.wyse_bonuses_received += 1
            agent.save()

        super(AgentBonus, self).save(*args, **kwargs)


def _create_virtual_account(sender, instance, created, **kwargs):
    from wallet_app.models import DebitCreditRecord, UserWallet

    if created:
        if (instance.terminal_id == "") or (instance.terminal_id is None):
            create_agent_vfd_virtual_wallet.delay(agent_id=instance.id)

        else:
            if instance.agent_type == "AGENT":
                agent_bonus_amount = AgentConstantVariables().get_agent_play_commission()

                AgentWallet.objects.create(
                    agent=instance,
                    agent_name=instance.first_name + " " + instance.last_name,
                    agent_phone_number=instance.phone,
                    agent_email=instance.email,
                )

                if agent_bonus_amount > 0:
                    debit_credit_record = DebitCreditRecord.create_record(
                        phone_number=instance.phone,
                        amount=agent_bonus_amount,
                        channel="POS/MOBILE",
                        reference=f"bonus-{uuid.uuid4()}{datetime.now().timestamp()}",
                        transaction_type="CREDIT",
                    )

                    wallet_payload = {
                        "transaction_from": "BONUS",
                    }

                    UserWallet.fund_wallet(
                        user=instance,
                        amount=agent_bonus_amount,
                        channel="POS",
                        transaction_id=debit_credit_record.reference,
                        user_wallet_type="GAME_PLAY_WALLET",
                        **wallet_payload,
                    )

            elif instance.agent_type == "LOTTO_AGENT":
                lotto_agent_pre_funding_amount = AgentConstantVariables().get_lotto_agent_pre_funding_amount()
                AgentWallet.objects.create(
                    agent=instance,
                    agent_name=instance.first_name + " " + instance.last_name,
                    agent_phone_number=instance.phone,
                    agent_email=instance.email,
                )

                if lotto_agent_pre_funding_amount > 0:
                    if (instance.terminal_id is not None) or (str(instance.terminal_id) != ""):
                        if AgentNoPreFunding.objects.filter(phone_number=instance.phone).exists():
                            pass
                        else:
                            debit_credit_record = DebitCreditRecord.create_record(
                                phone_number=instance.phone,
                                amount=lotto_agent_pre_funding_amount,
                                channel="POS/MOBILE",
                                reference=f"pre_F-{uuid.uuid4()}{datetime.now().timestamp()}",
                                transaction_type="CREDIT",
                            )

                            wallet_payload = {
                                "transaction_from": "PRE_FUNDING",
                            }

                            UserWallet.fund_wallet(
                                user=instance,
                                amount=lotto_agent_pre_funding_amount,
                                channel="POS",
                                transaction_id=debit_credit_record.reference,
                                user_wallet_type="GAME_PLAY_WALLET",
                                **wallet_payload,
                            )

                            try:
                                TerminalPrefundingMoneyTransfer.create_record(
                                    amount=lotto_agent_pre_funding_amount,
                                    agent_phone=instance.phone,
                                    agent_name=instance.full_name,
                                    agent_terminal_id=instance.terminal_id,
                                )
                            except:
                                pass

        if not settings.DEBUG or settings.DEBUG is False:
            pass
        else:
            if instance.is_winwise_staff_agent is False:
                create_agent_vfd_virtual_wallet.delay(agent_id=instance.id)


post_save.connect(_create_virtual_account, sender=Agent)


class ChargeAgentTransaction(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    amount = models.FloatField()
    transaction_ref = models.CharField(max_length=600, unique=True)
    charged = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    payment_initiated = models.BooleanField(default=False)
    commission_percentage = models.FloatField(default=0)
    received_amount = models.FloatField(default=0)
    commission_amount = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.agent.first_name + " " + self.agent.last_name

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CHARGE AGENT TRANSACTION TABLE"
        verbose_name_plural = "CHARGE AGENT TRANSACTION TABLES"

    def save(self, *args, **kwargs):
        if not self.pk:
            pass

            commission_percentage = AgentConstantVariables().get_agent_sales_commission()
            self.commission_percentage = commission_percentage
            self.commission_amount = self.amount * float(commission_percentage)
            self.received_amount = self.amount - self.commission_amount
            self.transaction_ref = f"{self.agent.user_id}-{str(uuid.uuid4())}"

        return super(ChargeAgentTransaction, self).save(*args, **kwargs)


class AgentWallet(models.Model):
    agent = models.ForeignKey(Agent, related_name="agent_wallet", on_delete=models.CASCADE)
    agent_name = models.CharField(max_length=300, blank=True, null=True)
    agent_phone_number = models.CharField(max_length=300, blank=True, null=True)
    agent_email = models.CharField(max_length=300, blank=True, null=True)
    woven_account = models.ForeignKey(
        "main.WovenAccountDetail",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    vfd_account = models.ForeignKey(
        "pos_app.AgentVFDAccountDetail",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    account_ref = models.CharField(max_length=125, null=True, blank=True)
    commission_bal = models.FloatField(default=0)
    commission_rewarded = models.FloatField(default=0)
    bonus_bal = models.FloatField(default=0)
    used_bonus_bal = models.FloatField(default=0)
    is_bonus_available = models.BooleanField(default=False)
    winnings_bal = models.FloatField(db_index=True, default=0)
    withdrawable_available_bal = models.FloatField(default=0)
    fund_wallet_count = models.IntegerField(default=0)
    game_play_bal = models.FloatField(db_index=True, default=0)
    remuneration_bal = models.FloatField(default=0)
    rewarded_remuneration = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    transaction_from = None
    transaction_type = None
    game_type = None
    game_play_id = None
    phone_number = None
    ticket_instance = None
    rewarded_commission = 0

    def __str__(self):
        return self.agent.first_name + " " + self.agent.last_name

    # def __init__(self, *args, **kwargs):
    #     super(AgentWallet, self).__init__(*args, **kwargs)
    #     self.__original_commission_bal = self.commission_bal
    #     self.__original_winnings_bal = self.winnings_bal
    #     self.__original_withdrawable_available_bal = self.withdrawable_available_bal
    #     self.__original_game_play_bal = self.game_play_bal

    class Meta:
        verbose_name = "AGENT WALLET"
        verbose_name_plural = "AGENT WALLETS"

    def save(self, *args, **kwargs):
        self.commission_bal = round(self.commission_bal, 2)
        self.commission_rewarded = round(self.commission_rewarded, 2)
        self.bonus_bal = round(self.bonus_bal, 2)
        self.used_bonus_bal = round(self.used_bonus_bal, 2)
        self.winnings_bal = round(self.winnings_bal, 2)
        self.withdrawable_available_bal = round(self.withdrawable_available_bal, 2)
        self.game_play_bal = round(self.game_play_bal, 2)

        try:
            return super(AgentWallet, self).save(*args, **kwargs)
        except Exception as err:
            print(err)
            # details = f"ALERTTTTTTTTTTT!!\n\nALERTTTTTTTTTTT!!\nUser with email: {self.user.email} tried to save balance in a negative figure and has been suspended."

            # self.user.is_suspended = True
            # self.user.suspension_reason = details
            # self.user.save()

            # notify_admin_group(user=self.user, details=details)

            raise Exception("Negative Balance Error")

    @classmethod
    def agent_wallet_funding_via_paystack(cls, paystack_trans_instance, data):
        paystack_trans_instance.raw_data = data.get("raw_data")
        paystack_trans_instance.bank = data.get("data", {}).get("authorization", {}).get("bank")
        paystack_trans_instance.save()

        transaction_amount = paystack_trans_instance.amount

        # verify payment
        verification_data = PaymentGateway().paystack_verify_payment(paystack_trans_instance.reference)

        if isinstance(verification_data, dict):
            if (
                verification_data.get("data").get("status").casefold() == "success"
                or verification_data.get("message") == "Verification successful"
            ):
                # tokenize card
                celery_tokenize_paystack_card.delay(paystack_trans_instance.user.id, json.dumps(data.get("raw_data")))

                agent_db_id = paystack_trans_instance.reference.split("-")[-1]
                agent_wallet = cls.objects.filter(agent__id=int(agent_db_id)).last()

                print("agent_db_id", agent_db_id, "\n\n\n")
                print("agent_wallet", agent_wallet)

                if not agent_wallet:
                    return {"message": "can't find agent wallet"}

                agent_wallet.game_play_bal += transaction_amount
                agent_wallet.save()

                # update paystack model instance
                paystack_trans_instance.is_verified = True
                paystack_trans_instance.status = "SUCCESSFUL"
                paystack_trans_instance.save()

                return {"message": "agent wallet updated sucessfully"}

            elif "iled" in verification_data.get("data").get("status").casefold():
                # update paystack model instance
                paystack_trans_instance.is_verified = True
                paystack_trans_instance.status = "FAILED"
                paystack_trans_instance.save()

                return {"message": "payment failed"}

        return {"message": "payment not verified"}

    @classmethod
    def wallet_funding_via_woven_virtual_account(cls, request_data):
        from wallet_app.models import DebitCreditRecord, UserWallet

        account_ref = request_data.get("account_reference")
        unique_ref = request_data.get("unique_reference")
        amount = request_data.get("amount")

        wallet_instance = cls.objects.filter(account_ref=account_ref).last()

        agent_instance = Agent.objects.filter(id=wallet_instance.agent_id).last()

        if request_data.get("status") == "ACTIVE":
            raw_funding = RawFundingData.objects.create(
                reference=unique_ref,
                payload=request_data,
                source="WOVEN_FUNDING",
            )

            # Check if transaction is in our database
            check_for_transaction = AgentFundingTable.objects.filter(reference=unique_ref).first()

            if check_for_transaction:
                # Check woven to verify transaction
                verfiy_transaction = woven_verify_transaction(transaction_reference=unique_ref)

                if verfiy_transaction:
                    response = {
                        "message": "agent vfd funding already verified",
                        "status": "Failed",
                    }

                    raw_funding.lotto_response = response
                    raw_funding.save()

            else:
                # Check woven to verify transaction
                verfiy_transaction = woven_verify_transaction(transaction_reference=unique_ref)

                if verfiy_transaction:
                    # Add transaction to database

                    if AgentWalletTransaction.objects.filter(transaction_reference=unique_ref).exists():
                        response = {
                            "message": "Funding Already Processed",
                            "status": "Failed",
                        }

                        raw_funding.lotto_response = response
                        raw_funding.save()

                        return response

                    debit_credit_record = DebitCreditRecord.create_record(
                        phone_number=agent_instance.phone,
                        amount=amount,
                        channel="POS/MOBILE",
                        reference=unique_ref,
                        transaction_type="CREDIT",
                    )

                    AgentFundingTable.objects.create(
                        agent=agent_instance,
                        source="WOVEN",
                        amount=amount,
                        payload=request_data,
                        is_verified=True,
                        reference=unique_ref,
                        verification_response_payload=verfiy_transaction,
                        verification_status="VERIFIED",
                        debit_credit_id=debit_credit_record.debit_credit_record_id,
                    )

                    raw_funding.authorized = True
                    raw_funding.save()

                    try:
                        RetailWalletTransactions.create_credit_record_for_game_play(
                            amount=amount, wallet_value=amount, transaction_ref=debit_credit_record.reference
                        )
                    except:
                        pass

                    payload = {
                        "transaction_from": "WOVEN_FUNDING",
                    }

                    UserWallet.fund_wallet(
                        user=agent_instance,
                        amount=amount,
                        channel="POS",
                        transaction_id=debit_credit_record.debit_credit_record_id,
                        user_wallet_type="GAME_PLAY_WALLET",
                        **payload,
                    )

                    data = {"message": "data received", "status": "Success"}
                    raw_funding.lotto_response = data
                    raw_funding.save()

                    return {"message": "wallet updated successfully"}

        return {"message": "callback received"}

    @classmethod
    def credit_agent_winning_balance(cls, agent_id, amount, phone_number, game_type, game_play_id):
        pass
        # from wallet_app.models import DebitCreditRecord, UserWallet

        # # print("rewarding agent ::::::::::::::::", "\n")
        # agent_wallet = cls.objects.filter(agent__id=agent_id).last()
        # if agent_wallet is None:
        #     agent_instance = Agent.objects.filter(id=agent_id).last()
        #     agent_wallet = cls.objects.create(agent=agent_instance)

        # if agent_wallet:
        #     payload = {
        #         "transaction_from": "WINNINGS",
        #         "game_type": game_type,
        #         "game_play_id": game_play_id,
        #     }

        #     debit_credit_record = DebitCreditRecord.create_record(
        #         phone_number=agent_wallet.agent.phone,
        #         amount=amount,
        #         channel="POS/MOBILE",
        #         reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
        #         transaction_type="CREDIT",
        #     )

        #     UserWallet.fund_wallet(
        #         user=agent_wallet.agent,
        #         amount=amount,
        #         channel="POS",
        #         transaction_id=debit_credit_record.reference,
        #         user_wallet_type="WINNINGS_WALLET",
        #         **payload,
        #     )

        #     # agent_wallet.winnings_bal += amount
        #     # agent_wallet.transaction_from = "WINNINGS"
        #     # agent_wallet.phone_number = phone_number
        #     # agent_wallet.game_type = game_type
        #     # agent_wallet.game_play_id = game_play_id
        #     # agent_wallet.save()

        #     return True
        # else:
        #     agent_instance = Agent.objects.filter(id=agent_id).last()
        #     cls.objects.create(
        #         agent=agent_instance,
        #         winnings_bal=amount,
        #     )

    @classmethod
    def deduct_bonus_wallet(cls, agent_id, amount):
        agent_wallet = cls.objects.filter(agent__id=agent_id).last()
        if agent_wallet:
            agent_wallet.bonus_bal -= amount
            agent_wallet.save()
            return True

    @classmethod
    def fund_agent_play_balance(cls, agent_id, amount):
        agent_wallet = cls.objects.filter(agent__id=agent_id).last()
        if agent_wallet:
            agent_wallet.game_play_bal += amount
            agent_wallet.save()
            # transaction.commit()
            return cls
        else:
            agent_instance = Agent.objects.filter(id=agent_id).last()
            cls.objects.create(
                agent=agent_instance,
                game_play_bal=amount,
            )
            # transaction.commit()
            return cls

    @classmethod
    def check_lotto_winner_fund_play_wallet(cls, agent_id, amount, game_id, narration) -> bool:
        from main.models import PayoutTransactionTable
        from pos_app.pos_helpers import PosAgentHelper

        """
        check if the agent is a lotto agent i.e has prefunding in his wallet
        and also has outstanding remittance.

        if so, credit the money back to his play wallet,
        deduct the money from his winnings and remittance

        """

        agent = Agent.objects.filter(id=agent_id).last()

        aggregate_agent_pre_funding_transaction = (
            AgentWalletTransaction.objects.filter(agent_wallet__agent__id=agent.id, transaction_from="PRE_FUNDING")
            .aggregate(Sum("amount"))
            .get("amount__sum")
        )

        aggragated_pre_funding_amount = (
            aggregate_agent_pre_funding_transaction if aggregate_agent_pre_funding_transaction is not None else 0
        )

        if aggragated_pre_funding_amount == 0:
            return False

        agent_remittance_instance = LottoAgentRemittanceTable.objects.filter(agent=agent, remitted=False).last()

        if agent_remittance_instance is None:
            return False

        agent_wallet = AgentWallet.objects.filter(agent=agent).last()

        # get remittance expected amount.
        # 5000, 1500
        expected_remitance_amount = agent_remittance_instance.amount - agent_remittance_instance.amount_paid
        if amount > expected_remitance_amount:
            excess = amount - expected_remitance_amount

            agent_wallet.game_play_bal += amount
            agent_wallet.save()

            # remove the excess remittance amount from the agent play wallet
            """
                The reason why i'm intializing the agent wallet instance again is because
                i want the amount to be seen as "CREDIT" OR "DEBIT" in the agent wallet transaction table

            """
            agent_wallet_instance = AgentWallet.objects.filter(agent=agent).last()
            agent_wallet_instance.game_play_bal -= excess
            agent_wallet_instance.transaction_from = "EXCESS_REMITTANCE_WITHDRAWAL"
            agent_wallet_instance.save()

            # send the excess to the agent's liberty pay wallet

            # ------------------------------ LIBERTY PAY BUDDY TRANSFER ------------------------------ #

            payload = {
                "from_wallet_type": "COLLECTION",
                "to_wallet_type": "COLLECTION",
                "data": [
                    {
                        "buddy_phone_number": agent.phone,
                        "amount": excess,
                        "narration": narration,
                        "is_beneficiary": "False",
                        "save_beneficiary": "True",
                        "remove_beneficiary": "False",
                        "is_recurring": "False",
                    }
                ],
            }

            reference = "withdraw-{}".format(uuid.uuid4())

            agent_wallet.refresh_from_db()

            payout_instance = PayoutTransactionTable.objects.create(
                source="LIBERTY_PAY",
                amount=amount,
                disbursement_unique_id=reference,
                phone=agent.phone,
                payout_trans_ref=reference,
                channel="POS",
                game_play_id=game_id,
                payout_payload=payload,
                balance_before=agent_wallet.winnings_bal + amount,
                balance_after=agent_wallet.winnings_bal,
                joined_since=agent.get_duration(),
                name=f"{agent.first_name} {agent.last_name}",
            )

            # ------------------------ CREATE RECORD IN AGENT TRANSACTION HISTORY AS CREDIT
            #  BECAUSE WE'RE FUNDING HIS LIBERTY PAY WALLET ------------------------ #
            agent_wallet = AgentWallet.objects.filter(agent=agent).last()
            agent_transaction_insatnce = AgentWalletTransaction.objects.create(
                agent_wallet=agent_wallet,
                amount=amount,
                transaction_from="LIBERTY_PAY_FUDING",
                type_of_agent=("WINWISE_AGENT" if agent_wallet.agent.is_winwise_staff_agent else "OTHER_AGENT"),
                agent_name=agent_wallet.agent.first_name + " " + agent_wallet.agent.last_name,
                agent_phone_number=agent_wallet.agent.phone,
                agent_email=agent_wallet.agent.email,
                terminal_id=agent_wallet.agent.terminal_id,
                terminal_serial_number=agent_wallet.agent.terminal_serial_number,
                wave=agent_wallet.agent.get_wave(),
            )

            # -----------------------------------------

            payload["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN

            pos_agent_helper = PosAgentHelper(agent_instance=agent, amount=amount)

            response = pos_agent_helper.agency_buddy_transfer(**payload)

            if isinstance(response, dict):
                if response.get("message") != "success":
                    agent_transaction_insatnce.status = "FAILED"
                    agent_transaction_insatnce.save()

                else:
                    agent_transaction_insatnce.status = "SUCCESSFUL"
                    agent_transaction_insatnce.save()

            else:
                agent_transaction_insatnce.status = "FAILED"
                agent_transaction_insatnce.save()

            payout_instance.source_response_payload = response
            payout_instance.save()

            return True

        else:
            if agent_wallet:
                agent_wallet.game_play_bal += amount
                agent_wallet.save()

                return True
            else:
                return False

    @classmethod
    def reward_commission(
        cls, agent_id, game_play_amount, commission_type, game_type=None, game_channel="POS_AGENT", rto_amount=0
    ) -> bool:
        """
        Calculate and distribute commissions for game plays based on agent type and relationships.

        This method handles different commission scenarios based on:
        - Agent type (staff, regular agent, etc.)
        - Super agent relationships and their types
        - Game types (instant vs. draw games)

        Args:
            agent_id (int): The ID of the agent receiving the commission
            game_play_amount (float): The monetary amount of the game play
            commission_type (str): Type of commission (e.g., "COMMISSION_ON_GAME_PLAY", "COMMISSION_REWARD")
            game_type (str, optional): Type of game played (e.g., "QUIKA", "INSTANT_CASHOUT"). Defaults to None.
            game_channel (str, optional): Channel through which game was played. Defaults to "POS_AGENT".

        Returns:
            bool: True if commission was successfully processed, False otherwise

        Raises:
            Agent.DoesNotExist: If the specified agent doesn't exist
        """

        # print("CALLED REWARD COMMISSION")

        game_play_amount = round(game_play_amount, 2)

        current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        current_year = current_time.date().year
        current_month = current_time.date().month
        agent_constants = AgentConstantVariables()

        # Only process commission on game play type for now
        if commission_type != "COMMISSION_ON_GAME_PLAY":
            return False

        # Get agent instance
        try:
            agent_instance = Agent.objects.get(id=agent_id, terminal_id__isnull=False)
        except Agent.DoesNotExist:
            return False

        if agent_instance.business_development_agent is not None:
            cls._process_business_development_commission(
                agent_instance, game_play_amount, current_year, current_month, agent_constants, rto=rto_amount
            )

        # Process winwise staff agents
        if agent_instance.is_winwise_staff_agent:
            super_agent_instance = LottoSuperAgents.objects.get(id=2)
            agent_instance.super_agent = super_agent_instance
            agent_instance.save()
            agent_instance.refresh_from_db()

            cls._process_winwise_staff_commission(agent_instance, game_play_amount, game_type, agent_constants)

        # Process regular agents with no super agent
        elif (agent_instance.super_agent is None) and (agent_instance.is_winwise_staff_agent is False):
            return cls._process_regular_agent_commission(
                agent_instance, game_play_amount, game_channel, agent_constants, game_type=game_type
            )

        # Process agents with super agents
        elif agent_instance.super_agent is not None:
            super_agent_type = agent_instance.super_agent.type

            if super_agent_type == enums.TypeOfSuperAgent.RELATIONSHIP_MANAGER:
                return cls._process_relationship_manager_commission(
                    agent_instance, game_play_amount, game_type, current_year, current_month, agent_constants
                )

            # elif super_agent_type == enums.TypeOfSuperAgent.BUSINESS_DEVELOPMENT:
            #     return cls._process_business_development_commission(agent_instance, game_play_amount, current_year, current_month, agent_constants)

            elif super_agent_type == enums.TypeOfSuperAgent.SUPER_AGENT:
                return cls._process_super_agent_commission(
                    agent_instance, game_play_amount, current_year, current_month, agent_constants, game_type=game_type
                )

        return False

    @classmethod
    def _process_winwise_staff_commission(cls, agent_instance, game_play_amount, game_type, agent_constants):

        from africa_lotto.models import AfricaLottoGameType  # noqa

        """
        Process commission for Winwise staff agents.

        Args:
            agent_instance (Agent): The agent object
            game_play_amount (float): The monetary amount of the game play
            game_type (str): Type of game played
            agent_constants (AgentConstantVariables): Constants for commission calculations

        Returns:
            bool: Result of updating the salary record
        """

        from africa_lotto.models import AfricaLottoConstants

        agent_constants: AgentConstantVariables = AgentConstantVariables()
        # Determine commission percentage based on game type
        # if game_type not in ["QUIKA", "INSTANT_CASHOUT"]:
        #     game_commission_percentage = agent_constants.get_draw_game_commission_percentage()

        #     if game_type == AfricaLottoGameType.KENYA_LOTTO:
        #         game_commission_percentage = AfricaLottoConstants.get_agent_commission_percentage()

        # else:
        #     game_commission_percentage = agent_constants.get_instant_game_commission_percentage()

        draw_games = [
            AfricaLottoGameType.KENYA_LOTTO,
            AfricaLottoGameType.GHANA_LOTTO,
            "SALARY_FOR_LIFE",
            "BANKER",
            "WYSE_CASH",
        ]

        if game_type == AfricaLottoGameType.K_NOW:
            game_commission_percentage = AfricaLottoConstants.get_k_now_commission_percentage()
        elif game_type in draw_games:

            game_commission_percentage = AgentConstantVariables.get_draw_game_commission_percentage()
        else:
            game_commission_percentage = AgentConstantVariables.get_instant_game_commission_percentage()

        # Calculate commission amounts
        commission_value = game_play_amount * game_commission_percentage
        company_share_percentage = agent_constants.get_company_commission_percentage_on_relationship_managers()
        company_share = round(commission_value * company_share_percentage, 2)
        agent_share = round(commission_value - company_share, 2)

        GamesDailyActivities.create_record(
            game_type=game_type,
            commission=commission_value,
        )

        Wallet.fund_wallet(wallet_type="COMMISSION", amount=commission_value)

        # Update salary record
        return WinwiseEmployeeSalary.update_salary_record(
            agent_id=agent_instance.id,
            commission_value=commission_value,
            amount_to_be_paid=agent_share,
            perc=company_share_percentage,
            sales_value=game_play_amount,
        )

    @classmethod
    def _process_regular_agent_commission(
        cls, agent_instance, game_play_amount, game_channel, agent_constants, game_type=None
    ):
        """
        Process commission for regular agents without super agents.

        Args:
            agent_instance (Agent): The agent object
            game_play_amount (float): The monetary amount of the game play
            game_channel (str): Channel through which game was played
            agent_constants (AgentConstantVariables): Constants for commission calculations

        Returns:
            bool: Result of funding the wallet
        """

        from africa_lotto.models import AfricaLottoConstants, AfricaLottoGameType
        from wallet_app.models import DebitCreditRecord, UserWallet

        # Only process POS agents with terminal IDs
        if game_channel == "POS_AGENT" and agent_instance.terminal_id:
            # game_commission_percentage = agent_constants.get_agent_sales_commission()
            # if game_type == AfricaLottoGameType.KENYA_LOTTO:
            #     game_commission_percentage = AfricaLottoConstants.get_agent_commission_percentage()

            draw_games = [
                AfricaLottoGameType.KENYA_LOTTO,
                AfricaLottoGameType.GHANA_LOTTO,
                "SALARY_FOR_LIFE",
                "BANKER",
                "WYSE_CASH",
            ]
            if game_type == AfricaLottoGameType.K_NOW:
                game_commission_percentage = AfricaLottoConstants.get_k_now_commission_percentage()
            elif game_type in draw_games:
                game_commission_percentage = AgentConstantVariables.get_draw_game_commission_percentage()
            else:
                game_commission_percentage = AgentConstantVariables.get_instant_game_commission_percentage()

            game_commission_value = round(game_play_amount * game_commission_percentage, 2)

            GamesDailyActivities.create_record(
                game_type=game_type,
                commission=game_commission_value,
            )

            Wallet.fund_wallet(wallet_type="COMMISSION", amount=game_commission_value)

            # Create transaction record
            reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"
            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=agent_instance.phone,
                amount=game_commission_value,
                channel="POS/MOBILE",
                reference=reference,
                transaction_type="CREDIT",
            )

            # Fund the wallet
            payload = {"transaction_from": "COMMISSION_ON_GAME_PLAY"}
            return UserWallet.fund_wallet(
                user=agent_instance,
                amount=debit_credit_record.amount,
                channel="POS",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="COMMISSION_WALLET",
                **payload,
            )

        return False

    @classmethod
    def _process_relationship_manager_commission(
        cls, agent_instance, game_play_amount, game_type, current_year, current_month, agent_constants
    ):

        from africa_lotto.models import AfricaLottoGameType

        """
        Process commission for agents with relationship manager super agents.

        Args:
            agent_instance (Agent): The agent object
            game_play_amount (float): The monetary amount of the game play
            game_type (str): Type of game played
            current_year (int): Current year for allocation records
            current_month (int): Current month for allocation records
            agent_constants (AgentConstantVariables): Constants for commission calculations

        Returns:
            bool: Result of updating the salary record
        """

        from africa_lotto.models import AfricaLottoConstants

        agent_constants: AgentConstantVariables = AgentConstantVariables()

        # Set defaults for game type metrics
        if game_type not in ["QUIKA", "INSTANT_CASHOUT"]:
            # Draw game metrics
            # game_commission_percentage = agent_constants.get_draw_game_commission_percentage()

            # if game_type == AfricaLottoGameType.KENYA_LOTTO:
            #     game_commission_percentage = AfricaLottoConstants.get_agent_commission_percentage()

            draw_games = [
                AfricaLottoGameType.KENYA_LOTTO,
                AfricaLottoGameType.GHANA_LOTTO,
                "SALARY_FOR_LIFE",
                "BANKER",
                "WYSE_CASH",
            ]

            if game_type == AfricaLottoGameType.K_NOW:
                game_commission_percentage = AfricaLottoConstants.get_k_now_commission_percentage()
            elif game_type in draw_games:
                game_commission_percentage = AgentConstantVariables.get_draw_game_commission_percentage()
            else:
                game_commission_percentage = AgentConstantVariables.get_instant_game_commission_percentage()

            total_draw_games = 1
            total_draw_games_value = game_play_amount
            draw_game_commission = game_play_amount * game_commission_percentage
            draw_game_commission_percentage = game_commission_percentage

            total_instant_games = 0
            total_instant_games_value = 0
            instant_game_commission = 0
            instant_game_commission_percentage = 0
        else:
            # Instant game metrics
            game_commission_percentage = agent_constants.get_instant_game_commission_percentage()

            total_draw_games = 0
            total_draw_games_value = 0
            draw_game_commission = 0
            draw_game_commission_percentage = 0

            total_instant_games = 1
            total_instant_games_value = game_play_amount
            instant_game_commission = game_play_amount * game_commission_percentage
            instant_game_commission_percentage = game_commission_percentage

        # Calculate commission shares
        commission_value = game_play_amount * game_commission_percentage
        agent_share_percentage = agent_constants.get_company_commission_percentage_on_relationship_managers()
        super_agent_share = commission_value * agent_share_percentage
        agent_share = commission_value - super_agent_share

        GamesDailyActivities.create_record(
            game_type=game_type,
            commission=commission_value,
        )

        Wallet.fund_wallet(wallet_type="COMMISSION", amount=commission_value)

        # Get or create allocation record
        allocation_instance = cls._get_or_create_super_agent_allocation(
            agent_instance.super_agent, current_year, current_month
        )

        # Update allocation metrics
        allocation_instance.agent_monthtly_sales += game_play_amount
        allocation_instance.total_draw_games += total_draw_games
        allocation_instance.total_draw_games_value += round(total_draw_games_value, 2)
        allocation_instance.draw_game_commission += round(draw_game_commission, 2)
        allocation_instance.draw_game_commission_percentage = draw_game_commission_percentage
        allocation_instance.agent_commission += round(super_agent_share, 2)
        allocation_instance.agent_commission_percentage = agent_share_percentage
        allocation_instance.total_instant_games += total_instant_games
        allocation_instance.total_instant_games_value += round(total_instant_games_value, 2)
        allocation_instance.instant_game_commission += round(instant_game_commission, 2)
        allocation_instance.instant_game_commission_percentage = instant_game_commission_percentage
        allocation_instance.save()

        # Fund super agent wallet
        super_agent_wallet = cls._get_or_create_super_agent_wallet(agent_instance.super_agent)
        super_agent_wallet.commission_balance += round(super_agent_share, 2)
        super_agent_wallet.save()

        # Update salary record
        r = WinwiseEmployeeSalary.update_salary_record(
            agent_id=agent_instance.id,
            commission_value=commission_value,
            amount_to_be_paid=agent_share,
            perc=agent_share_percentage,
            sales_value=game_play_amount,
        )

        print("r", r)

    @classmethod
    def _process_business_development_commission(
        cls, agent_instance, game_play_amount, current_year, current_month, agent_constants, rto
    ):
        """
        Process commission for agents with business development super agents.

        Args:
            agent_instance (Agent): The agent object
            game_play_amount (float): The monetary amount of the game play
            current_year (int): Current year for allocation records
            current_month (int): Current month for allocation records
            agent_constants (AgentConstantVariables): Constants for commission calculations

        Returns:
            bool: True if successful
        """
        # Get or create allocation record
        allocation_instance = cls._get_or_create_super_agent_allocation(
            agent_instance.business_development_agent, current_year, current_month
        )

        agent_constants: AgentConstantVariables = AgentConstantVariables()

        # Calculate commission amounts
        company_commission_percentage = (
            agent_constants.get_company_commission_percentage_on_business_development_agent()
        )
        company_commission = round(rto * company_commission_percentage, 2)

        # Update allocation
        allocation_instance.company_commission += round(company_commission, 2)
        allocation_instance.company_commission_percentage = company_commission_percentage
        allocation_instance.agent_monthtly_sales += game_play_amount
        allocation_instance.save()

        # Fund super agent wallet
        super_agent_wallet = cls._get_or_create_super_agent_wallet(agent_instance.super_agent)
        super_agent_wallet.commission_balance += round(company_commission, 2)
        super_agent_wallet.save()

        return True

    @classmethod
    def _process_super_agent_commission(
        cls, agent_instance, game_play_amount, current_year, current_month, agent_constants, game_type=None
    ):
        from africa_lotto.models import AfricaLottoGameType

        """
        Process commission for agents with super agents of type SUPER_AGENT.

        Args:
            agent_instance (Agent): The agent object
            game_play_amount (float): The monetary amount of the game play
            current_year (int): Current year for allocation records
            current_month (int): Current month for allocation records
            agent_constants (AgentConstantVariables): Constants for commission calculations

        Returns:
            bool: True if successful
        """

        from africa_lotto.models import AfricaLottoConstants
        from wallet_app.models import DebitCreditRecord, UserWallet

        # agent_constants: AgentConstantVariables = AgentConstantVariables()
        # Calculate commission
        # commission_percentage = agent_constants.get_agent_sales_commission()
        # if game_type == AfricaLottoGameType.KENYA_LOTTO:
        #     commission_percentage = AfricaLottoConstants.get_agent_commission_percentage()

        draw_games = [
            AfricaLottoGameType.KENYA_LOTTO,
            AfricaLottoGameType.GHANA_LOTTO,
            "SALARY_FOR_LIFE",
            "BANKER",
            "WYSE_CASH",
        ]
        if game_type == AfricaLottoGameType.K_NOW:  # j
            commission_percentage = AfricaLottoConstants.get_k_now_commission_percentage()
        elif game_type in draw_games:
            commission_percentage = AgentConstantVariables.get_draw_game_commission_percentage()
        else:
            commission_percentage = AgentConstantVariables.get_instant_game_commission_percentage()

        commission_value = round(game_play_amount * commission_percentage, 2)
        agent_share = 0

        GamesDailyActivities.create_record(
            game_type=game_type,
            commission=commission_value,
        )

        Wallet.fund_wallet(wallet_type="COMMISSION", amount=commission_value)

        # Handle downlines sharing if applicable
        if agent_instance.downlines_sharing_commission_percentage > 0:
            downlines_commission_percentage = agent_instance.downlines_sharing_commission_percentage / 100
            agent_share = round(commission_value * downlines_commission_percentage, 2)
            commission_value -= agent_share

            # Fund agent's wallet
            reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"
            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=agent_instance.phone,
                amount=agent_share,
                channel="POS/MOBILE",
                reference=reference,
                transaction_type="CREDIT",
            )

            payload = {"transaction_from": "COMMISSION_ON_GAME_PLAY"}
            UserWallet.fund_wallet(
                user=agent_instance,
                amount=debit_credit_record.amount,
                channel="POS",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="COMMISSION_WALLET",
                **payload,
            )

        # Get or create allocation record and update
        allocation_instance = cls._get_or_create_super_agent_allocation(
            agent_instance.super_agent, current_year, current_month
        )

        allocation_instance.agent_commission += round(commission_value, 2)
        allocation_instance.agent_commission_percentage = commission_percentage
        allocation_instance.save()

        # Fund super agent wallet
        super_agent_wallet = cls._get_or_create_super_agent_wallet(agent_instance.super_agent)
        super_agent_wallet.commission_balance += round(commission_value, 2)
        super_agent_wallet.save()

        return True

    @classmethod
    def _get_or_create_super_agent_allocation(cls, super_agent, year, month):
        """
        Get or create a commission allocation record for a super agent.

        Args:
            super_agent (Agent): The super agent object
            year (int): Current year
            month (int): Current month

        Returns:
            LottoSuperAgentCommissionAllocation: The allocation instance
        """
        try:
            return LottoSuperAgentCommissionAllocation.objects.get(
                Q(super_agent__id=super_agent.id),
                Q(created_at__year=year) | Q(created_at__month=month),
            )
        except Exception:
            return LottoSuperAgentCommissionAllocation.objects.create(
                super_agent=super_agent,
                super_agent_phone_number=super_agent.phone,
                type_of_super_agent=super_agent.type,
            )

    @classmethod
    def _get_or_create_super_agent_wallet(cls, super_agent):
        """
        Get or create a wallet for a super agent.

        Args:
            super_agent (Agent): The super agent object

        Returns:
            LottoSuperAgentWallet: The wallet instance
        """
        try:
            return LottoSuperAgentWallet.objects.get(super_agent__id=super_agent.id)
        except LottoSuperAgentWallet.DoesNotExist:
            return LottoSuperAgentWallet.objects.create(
                super_agent=super_agent,
            )


class AgentWalletTransaction(models.Model):
    TRANSACTION_STATUS_CHOICES = [
        ("PENDING", "PENDING"),
        ("SUCCESSFUL", "SUCCESSFUL"),
        ("FAILED", "FAILED"),
    ]

    # TRANSACTION_TYPE_CHOICES = [
    #     ('CREDIT', 'CREDIT'),
    #     ('DEBIT', 'DEBIT'),
    # ]

    TRANSACTION_FROM = [
        ("COMMISSION", "COMMISSION"),
        ("WINNINGS", "WINNINGS"),
        ("WINNINGS_MANUALLY_UPDATED", "WINNINGS_MANUALLY_UPDATED"),
        ("LIBERTY_PAY_LOTTO_CHARGE", "LIBERTY_PAY_LOTTO_CHARGE"),
        ("LIBERTY_PAY_FUDING", "LIBERTY_PAY_FUDING"),
        ("WINNINGS_WITHDRAW", "WINNINGS_WITHDRAW"),
        ("SUPER_AGENT_COMMISSION", "SUPER_AGENT_COMMISSION"),
        ("WALLET", "WALLET"),
        ("DEBITED_FOR_MANUAL_PRE_FUNDING", "DEBITED_FOR_MANUAL_PRE_FUNDING"),
        ("WOVEN_FUNDING", "WOVEN_FUNDING"),
        ("PRE_FUNDING", "PRE_FUNDING"),
        ("GAME_PLAY", "GAME_PLAY"),
        ("BONUS", "BONUS"),
        ("REMITTANCE", "REMITTANCE"),
        ("REMITTANCE_EXCESS", "REMITTANCE_EXCESS"),
        ("EXCESS_REMITTANCE_WITHDRAWAL", "EXCESS_REMITTANCE_WITHDRAWAL"),
        (
            "LIBERTRY_PAY_LOTTERY_WINNING_COMMISSION_REWARD",
            "LIBERTRY_PAY_LOTTERY_WINNING_COMMISSION_REWARD",
        ),
        (
            "COMMISSION_ON_GAME_PLAY",
            "COMMISSION_ON_GAME_PLAY",
        ),
        (
            "COMMISSION_REWARD",
            "COMMISSION_REWARD",
        ),
        ("VFD_ACCOUNT_FUNDING", "VFD_ACCOUNT_FUNDING"),
        ("SEND_TO_LOTTO_FUNDING", "SEND_TO_LOTTO_FUNDING"),
        ("BONUS_WITHDRAWAL_TO_PLAY_WALLET", "BONUS_WITHDRAWAL_TO_PLAY_WALLET"),
        ("TEST_PURPOSE", "TEST_PURPOSE"),
        ("SPECIAL_CASE", "SPECIAL_CASE"),
        ("WITHDRAWAL", "WITHDRAWAL"),
        ("REVERSAL", "REVERSAL"),
        ("PAYOUT_REVERSAL", "PAYOUT_REVERSAL"),
        ("SUPER_AGENT_COMMISSION_REVERSAL", "SUPER_AGENT_COMMISSION_REVERSAL"),
        ("GAME_PLAY_REVERSAL", "GAME_PLAY_REVERSAL"),
        ("MANCALA_GAME_PLAY_RVSL", "MANCALA_GAME_PLAY_RVSL"),
        ("COMMISSION_REWARD_REVERSAL", "COMMISSION_REWARD_REVERSAL"),
        ("REMUNERATION_CHARGE_ON_REMITTANCE", "REMUNERATION_CHARGE_ON_REMITTANCE"),
        ("WITHDRAWAL_DEBT_DEDUCTION", "WITHDRAWAL_DEBT_DEDUCTION"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW"),
        ("GLOBAL_WINNINGS", "GLOBAL_WINNINGS"),
        ("SEEDS_PENNIES_DRAW_COMMISSION", "SEEDS_PENNIES_DRAW_COMMISSION"),
        ("SEEDS_PENNIES_AGENT_DRAW_COMMISSION", "SEEDS_PENNIES_AGENT_DRAW_COMMISSION"),
        ("SEEDS_PENNIES_AGENT_DRAW_COMMISSION_WITHDRAWAL", "SEEDS_PENNIES_AGENT_DRAW_COMMISSION_WITHDRAWAL"),
    ]

    TRANSACTION_TYPE = [
        ("CREDIT", "CREDIT"),
        ("DEBIT", "DEBIT"),
    ]

    GAME_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("QUIKA", "QUIKA"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("BANKER", "BANKER"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW"),
    ]

    TICKET_INSTANCE = [("RETAIL", "RETAIL"), ("GAME_PLAY", "GAME_PLAY")]

    TYPE_OF_AGENT = [
        ("WINWISE_AGENT", "WINWISE_AGENT"),
        ("OTHER_AGENT", "OTHER_AGENT"),
    ]

    TYPE_OF_USER = [
        ("WINWISE_AGENT", "WINWISE_AGENT"),
        ("OTHER_AGENT", "OTHER_AGENT"),
        ("AGENT", "AGENT"),
        ("MERCHANT", "MERCHANT"),
        ("LOTTO_AGENT", "LOTTO_AGENT"),
        ("PERSONAL", "PERSONAL"),
        ("LIBERTY_RETAIL", "LIBERTY_RETAIL"),
    ]

    agent_wallet = models.ForeignKey(AgentWallet, on_delete=models.SET_NULL, null=True, blank=True)
    terminal_id = models.CharField(max_length=300, blank=True, null=True)
    terminal_serial_number = models.CharField(max_length=300, blank=True, null=True)
    agent_name = models.CharField(max_length=300, blank=True, null=True)
    agent_phone_number = models.CharField(max_length=300, blank=True, null=True)
    agent_email = models.CharField(max_length=300, blank=True, null=True)
    type_of_agent = models.CharField(max_length=125, choices=TYPE_OF_AGENT, default="OTHER_AGENT")
    type_of_user = models.CharField(max_length=125, choices=TYPE_OF_USER, default="OTHER_AGENT")
    wave = models.CharField(max_length=125, blank=True, null=True)
    transaction_reference = models.CharField(max_length=125)
    unique_transaction_reference = models.CharField(max_length=125, blank=True, null=True, unique=True)
    amount = models.FloatField(default=0)
    bal_before = models.FloatField(default=0)
    bal_after = models.FloatField(default=0)
    excess_balance = models.FloatField(default=0)
    game_play_bal_plus_winnings = models.FloatField(default=0)
    status = models.CharField(
        max_length=125,
        default="PENDING",
        choices=TRANSACTION_STATUS_CHOICES,
        null=True,
        blank=True,
    )
    transaction_from = models.CharField(max_length=125, choices=TRANSACTION_FROM)
    game_type = models.CharField(max_length=125, choices=GAME_TYPE, blank=True, null=True)
    game_play_id = models.CharField(max_length=125, blank=True, null=True)
    phone_number = models.CharField(max_length=125, blank=True, null=True)
    transaction_type = models.CharField(max_length=125, default="CREDIT", choices=TRANSACTION_TYPE)

    date_created = models.DateTimeField(auto_now_add=True)
    ticket_instance = models.CharField(max_length=125, choices=TICKET_INSTANCE, blank=True, null=True)
    expected_remittance = models.FloatField(default=0)
    rewarded_commission = models.FloatField(default=0)
    show_transaction = models.BooleanField(default=True)

    def __str__(self):
        return str(self.transaction_reference)

    class Meta:
        verbose_name = "AGENT WALLET TRANSACTION"
        verbose_name_plural = "AGENT WALLET TRANSACTIONS"

        indexes = [
            models.Index(fields=["status", "date_created"]),
            models.Index(fields=["transaction_reference", "status"]),
            models.Index(fields=["agent_name", "agent_phone_number", "agent_email"]),
            models.Index(fields=["terminal_serial_number", "terminal_id"]),
            models.Index(fields=["date_created"], name="pending_tx_date_idx", condition=Q(status="PENDING")),
        ]

    def save(self, *args, **kwargs):
        if not self.pk:
            if self.transaction_from == "COMMISSION":
                pass
            else:
                return super(AgentWalletTransaction, self).save(*args, **kwargs)

        else:
            return super(AgentWalletTransaction, self).save(*args, **kwargs)

    @classmethod
    def create_record(
        cls,
        transaction_reference: str,
        unique_transaction_reference: str,
        agent_wallet: AgentWallet,
        transaction_type: str,
        game_type: str,
        game_play_id: str,
        status: str,
        phone_number: str,
        transaction_from: str,
        agent_phone_number: str,
        agent_email: str,
        type_of_agent: str,
        agent_name: str,
        amount: float = 0,
        bal_before: float = 0,
        bal_after: float = 0,
        excess_balance: float = 0,
        game_play_bal_plus_winnings: float = 0,
        rewarded_commission: float = 0,
        expected_remittance: float = 0,
    ):
        """
        Create a transaction record for an agent's wallet activity.

        This method creates a detailed record of a transaction including wallet balances,
        game details, and agent information.

        Args:
            transaction_reference (str): Unique reference for the transaction
            unique_transaction_reference (str): Secondary unique identifier for the transaction
            agent_wallet (AgentWallet): The agent's wallet instance
            transaction_type (str): Type of transaction being performed
            game_type (str): Type of game associated with the transaction
            game_play_id (str): Unique identifier for the game play
            status (str): Current status of the transaction
            phone_number (str): Customer's phone number
            transaction_from (str): Source of the transaction
            agent_phone_number (str): Agent's phone number
            agent_email (str): Agent's email address
            type_of_agent (str): Classification/type of the agent
            agent_name (str): Name of the agent
            amount (float, optional): Transaction amount. Defaults to 0.
            bal_before (float, optional): Wallet balance before transaction. Defaults to 0.
            bal_after (float, optional): Wallet balance after transaction. Defaults to 0.
            excess_balance (float, optional): Any excess balance amount. Defaults to 0.
            game_play_bal_plus_winnings (float, optional): Combined game play balance and winnings. Defaults to 0.
            rewarded_commission (float, optional): Commission awarded to agent. Defaults to 0.
            expected_remittance (float, optional): Expected remittance amount. Defaults to 0.

        Returns:
            Record instance: The created transaction record

        Note:
            All monetary values (amount, balances, etc.) should be provided in the smallest
            currency unit (e.g., cents for USD).
        """

        return cls.objects.create(
            transaction_reference=transaction_reference,
            unique_transaction_reference=unique_transaction_reference,
            agent_wallet=agent_wallet,
            transaction_type=transaction_type,
            amount=amount,
            status=status,
            transaction_from=transaction_from,
            bal_before=bal_before,
            bal_after=bal_after,
            excess_balance=excess_balance,
            game_type=game_type,
            game_play_id=game_play_id,
            game_play_bal_plus_winnings=game_play_bal_plus_winnings,
            rewarded_commission=rewarded_commission,
            phone_number=phone_number,
            expected_remittance=expected_remittance,
            type_of_agent=type_of_agent,
            agent_name=agent_name,
            agent_phone_number=agent_phone_number,
            agent_email=agent_email,
        )

    @staticmethod
    def create_wallet_transaction(
        agent_wallet,
        transaction_type,
        amount,
        status,
        bal_before=0,
        bal_after=0,
        bal_plus_winnings=0,
    ):
        # sleep(2)
        _instance_trans = AgentWalletTransaction.objects.create(
            agent_wallet=agent_wallet,
            transaction_type=transaction_type,
            amount=amount,
            status=status,
            bal_before=bal_before,
            bal_after=bal_after,
            game_play_bal_plus_winnings=bal_plus_winnings,
            expected_remittance=LottoAgentRemittanceTable().expected_remittance(agent_wallet.agent.id),
            rewarded_commission=agent_wallet.rewarded_commission,
            type_of_agent=("WINWISE_AGENT" if agent_wallet.agent.is_winwise_staff_agent else "OTHER_AGENT"),
            agent_name=agent_wallet.agent.first_name + " " + agent_wallet.agent.last_name,
            agent_phone_number=agent_wallet.agent.phone,
            agent_email=agent_wallet.agent.email,
            terminal_id=agent_wallet.agent.terminal_id,
            terminal_serial_number=agent_wallet.agent.terminal_serial_number,
            wave=agent_wallet.agent.get_wave(),
        )

        if agent_wallet.transaction_from is not None:
            _instance_trans.transaction_from = agent_wallet.transaction_from
            _instance_trans.save()

        if agent_wallet.transaction_type is not None:
            _instance_trans.transaction_type = agent_wallet.transaction_type
            _instance_trans.save()

        if agent_wallet.game_type is not None:
            _instance_trans.game_type = agent_wallet.game_type
            _instance_trans.save()

        if agent_wallet.game_play_id is not None:
            _instance_trans.game_play_id = agent_wallet.game_play_id
            _instance_trans.save()

        if agent_wallet.ticket_instance is not None:
            _instance_trans.ticket_instance = agent_wallet.ticket_instance
            _instance_trans.save()
        if agent_wallet.phone_number is not None:
            _instance_trans.phone_number = "*************"
            _instance_trans.save()


class PosLotteryWinners(models.Model):
    WIN_FLAVOUR_CHOICES = [
        ("BLACK", "BLACK"),
        ("WHITE", "WHITE"),
        ("CASHBACK", "CASHBACK"),
    ]

    LOTTERY_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("BANKER", "BANKER"),
        ("QUIKA", "QUIKA"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("NIGERIA_LOTTO", "NIGERIA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW"),
    ]

    agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, blank=True)
    claimant = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, blank=True, related_name="claimant")
    player = models.ForeignKey("main.UserProfile", on_delete=models.CASCADE)
    game_id = models.CharField(max_length=300)
    unique_game_id = models.CharField(max_length=300, null=True, blank=True, unique=True)
    amount_won = models.FloatField(default=0)
    pin = models.CharField(max_length=300)
    is_win_claimed = models.BooleanField(default=False)
    withdrawl_initiated = models.BooleanField(default=False)
    payout_successful = models.BooleanField(default=False)
    payout_verified = models.BooleanField(default=False)
    payout_ref = models.CharField(max_length=300, blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    jackpot = models.BooleanField(default=False)
    win_flavour = models.CharField(max_length=125, choices=WIN_FLAVOUR_CHOICES, blank=True, null=True)
    lottery_type = models.CharField(max_length=125, choices=LOTTERY_TYPE_CHOICES, blank=True, null=True)
    expiry_date = models.DateField(blank=True, null=True)
    expired = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        if not self.pk:
            if self.pin is None:
                self.pin = "123452"

            self.unique_game_id = self.game_id

        return super(PosLotteryWinners, self).save(*args, **kwargs)

    def __str__(self) -> str:
        return super().__str__()

    class Meta:
        verbose_name = "POS LOTTERY WINNER"
        verbose_name_plural = "POS LOTTERY WINNERS"

    @classmethod
    def get_expiry_date(cls):
        todays_date = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        expiry_date = todays_date + relativedelta(days=7)
        return expiry_date.date()

    @property
    def has_expired(self):
        todays_date = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()

        if self.expired == True:
            return True

        if self.expiry_date is None:
            return False

        if self.expiry_date < todays_date:
            self.expired = True
            self.save()
            return True

        return False

    @property
    def is_late_withdrawal(self):
        todays_date = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date()
        days_difference = (todays_date - self.date_created.date()).days

        if days_difference > AgentConstantVariables.get_late_withdrawal_threshold_days():
            return True

        return False

    @classmethod
    def validate_pin(cls, agent, pin, game_id, player_phone):
        return cls.objects.filter(
            agent=agent,
            is_win_claimed=False,
            pin=pin,
            game_id=game_id,
            player__phone_number=player_phone,
        ).exists()

    @classmethod
    def generate_transaction_ref(cls):
        from main.models import PayoutTransactionTable

        ref = "withdraw-{}".format(uuid.uuid4())

        if ref in cls.objects.values_list("payout_ref", flat=True):
            return cls.generate_transaction_ref()

        if ref in PayoutTransactionTable.objects.values_list("payout_trans_ref", flat=True):
            return cls.generate_transaction_ref()

        return ref

    # f

    @classmethod
    def create_winners(
        cls,
        agent,
        player,
        game_id,
        amount_won,
        win_flavour,
        lottery_type,
        jackpot=False,
        winner_instance=None,
        pin=None,
    ):
        from main.models import LottoTicket
        from wallet_app.models import DebitCreditRecord, UserWallet

        # check game has been record already
        if cls.objects.filter(
            agent=agent,
            game_id=game_id,
        ).exists():
            return

        # print("create_winners")
        if pin is None:
            pin = cls.generate_pin()

        # if jackpot is False:
        #     winner_instance.lottery.pin

        payout_ref = cls.generate_transaction_ref()

        if winner_instance is None:
            if agent.phone != player.phone_number:
                if AgentConstantVariables().allow_mobile_users_to_withdrwal_from_different_flow() is True:
                    if agent.terminal_id is None:
                        cls.objects.create(
                            agent=agent,
                            player=player,
                            game_id=game_id,
                            amount_won=amount_won,
                            pin=pin,
                            payout_ref=payout_ref,
                            jackpot=jackpot,
                            win_flavour=win_flavour,
                            lottery_type=lottery_type,
                            is_win_claimed=True,
                            payout_successful=True,
                            payout_verified=True,
                            withdrawl_initiated=True,
                            expiry_date=cls.get_expiry_date(),
                        )
                    else:
                        cls.objects.create(
                            agent=agent,
                            player=player,
                            game_id=game_id,
                            amount_won=amount_won,
                            pin=pin,
                            payout_ref=payout_ref,
                            jackpot=jackpot,
                            win_flavour=win_flavour,
                            lottery_type=lottery_type,
                            expiry_date=cls.get_expiry_date(),
                        )
                else:
                    cls.objects.create(
                        agent=agent,
                        player=player,
                        game_id=game_id,
                        amount_won=amount_won,
                        pin=pin,
                        payout_ref=payout_ref,
                        jackpot=jackpot,
                        win_flavour=win_flavour,
                        lottery_type=lottery_type,
                        expiry_date=cls.get_expiry_date(),
                    )

                try:
                    # send pin to player
                    if agent.terminal_id is None:
                        pass
                    else:
                        send_sms_winning_to_players_from_agent(
                            phone_number=player.phone_number,
                            amount_won=amount_won,
                            game_play_id=game_id,
                            agent_name=f"{agent.first_name} {agent.last_name}",
                            pin=pin,
                        )
                except Exception:
                    pass

            else:
                if AgentConstantVariables().allow_mobile_users_to_withdrwal_from_different_flow() is True:
                    if agent.terminal_id is None:
                        cls.objects.create(
                            agent=agent,
                            player=player,
                            game_id=game_id,
                            amount_won=amount_won,
                            pin=pin,
                            payout_ref=payout_ref,
                            jackpot=jackpot,
                            win_flavour=win_flavour,
                            lottery_type=lottery_type,
                            is_win_claimed=True,
                            payout_successful=True,
                            payout_verified=True,
                            withdrawl_initiated=True,
                            expiry_date=cls.get_expiry_date(),
                        )
                    else:
                        cls.objects.create(
                            agent=agent,
                            player=player,
                            game_id=game_id,
                            amount_won=amount_won,
                            pin=pin,
                            payout_ref=payout_ref,
                            jackpot=jackpot,
                            win_flavour=win_flavour,
                            lottery_type=lottery_type,
                            expiry_date=cls.get_expiry_date(),
                        )
                else:
                    cls.objects.create(
                        agent=agent,
                        player=player,
                        game_id=game_id,
                        amount_won=amount_won,
                        pin=pin,
                        payout_ref=payout_ref,
                        jackpot=jackpot,
                        win_flavour=win_flavour,
                        lottery_type=lottery_type,
                        expiry_date=cls.get_expiry_date(),
                    )

        else:
            if agent.phone != player.phone_number:
                try:
                    # send pin to player
                    if agent.terminal_id is None:
                        pass
                    else:
                        send_sms_winning_to_players_from_agent(
                            phone_number=player.phone_number,
                            amount_won=amount_won,
                            game_play_id=game_id,
                            agent_name=f"{agent.first_name} {agent.last_name}",
                            pin=pin,
                        )
                except Exception:
                    pass

            if (winner_instance.lottery.pin is not None) or (winner_instance.lottery.pin != ""):
                if AgentConstantVariables().allow_mobile_users_to_withdrwal_from_different_flow() is True:
                    if agent.terminal_id is None:
                        cls.objects.create(
                            agent=agent,
                            player=player,
                            game_id=game_id,
                            amount_won=amount_won,
                            pin=pin,
                            payout_ref=payout_ref,
                            jackpot=jackpot,
                            win_flavour=win_flavour,
                            lottery_type=lottery_type,
                            is_win_claimed=True,
                            payout_successful=True,
                            payout_verified=True,
                            withdrawl_initiated=True,
                            expiry_date=cls.get_expiry_date(),
                        )
                    else:
                        cls.objects.create(
                            agent=agent,
                            player=player,
                            game_id=game_id,
                            amount_won=amount_won,
                            pin=pin,
                            payout_ref=payout_ref,
                            jackpot=jackpot,
                            win_flavour=win_flavour,
                            lottery_type=lottery_type,
                            expiry_date=cls.get_expiry_date(),
                        )
                else:
                    cls.objects.create(
                        agent=agent,
                        player=player,
                        game_id=game_id,
                        amount_won=amount_won,
                        pin=pin,
                        payout_ref=payout_ref,
                        jackpot=jackpot,
                        win_flavour=win_flavour,
                        lottery_type=lottery_type,
                        expiry_date=cls.get_expiry_date(),
                    )
            else:
                LottoTicket().generate_ticket_pin(winner_instance.lottery.game_play_id)
                sleep(1)

                if AgentConstantVariables().allow_mobile_users_to_withdrwal_from_different_flow() is True:
                    if agent.terminal_id is None:
                        cls.objects.create(
                            agent=agent,
                            player=player,
                            game_id=game_id,
                            amount_won=amount_won,
                            pin=pin,
                            payout_ref=payout_ref,
                            jackpot=jackpot,
                            win_flavour=win_flavour,
                            lottery_type=lottery_type,
                            is_win_claimed=True,
                            payout_successful=True,
                            payout_verified=True,
                            withdrawl_initiated=True,
                            expiry_date=cls.get_expiry_date(),
                        )
                    else:
                        cls.objects.create(
                            agent=agent,
                            player=player,
                            game_id=game_id,
                            amount_won=amount_won,
                            pin=pin,
                            payout_ref=payout_ref,
                            jackpot=jackpot,
                            win_flavour=win_flavour,
                            lottery_type=lottery_type,
                            expiry_date=cls.get_expiry_date(),
                        )
                else:
                    cls.objects.create(
                        agent=agent,
                        player=player,
                        game_id=game_id,
                        amount_won=amount_won,
                        pin=pin,
                        payout_ref=payout_ref,
                        jackpot=jackpot,
                        win_flavour=win_flavour,
                        lottery_type=lottery_type,
                        expiry_date=cls.get_expiry_date(),
                    )

        # create wallet transaction
        payload = {
            "transaction_from": "WINNINGS",
            "game_type": lottery_type,
            "game_play_id": game_id,
        }

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=agent.phone,
            amount=amount_won,
            channel="POS/MOBILE",
            reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
            transaction_type="CREDIT",
        )

        UserWallet.fund_wallet(
            user=agent,
            amount=amount_won,
            channel="POS",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **payload,
        )

        try:
            from_lotto_agent = False
            if agent.terminal_id is not None or agent.terminal_id != "":
                from_lotto_agent = True

            GamesDailyActivities.create_record(
                game_type=lottery_type,
                winnings=amount_won,
                from_lotto_agent=from_lotto_agent,
                game_id=game_id,
            )
        except:  # noqa
            pass

    @classmethod
    def generate_pin(cls):
        return str(random.randint(0, 999999)).zfill(6)

    @classmethod
    def get_winning_details(cls, player_num):
        phone_no = player_num[1:] if player_num.startswith("+") else player_num
        winning = cls.objects.filter(player__phone_number=phone_no, is_win_claimed=False)

        if winning.count() > 0:
            winning_obj = winning.last()
            response = "END Congratulations\n"
            response += f"Game id: {winning_obj.game_id}\n"
            response += f"Amount won: {winning_obj.amount_won}\n"
            response += f"Pin: {winning_obj.pin}\n"

        else:
            response = "END You currently have\nno winnings"

        return response

    @classmethod
    def check_if_win_is_claimed(cls, agent_id, game_id):
        return cls.objects.filter(agent__id=agent_id, is_win_claimed=True, game_id=game_id).exists()

    @classmethod
    def check_if_win_is_claimed_by_agent_phone_number(cls, agent_phone, game_id):
        return cls.objects.filter(agent__phone=agent_phone, is_win_claimed=True, game_id=game_id).exists()


class AgentPayoutBeneficiary(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    beneficiary_name = models.CharField(max_length=300, null=True, blank=True)
    beneficiary_account_number = models.CharField(max_length=300, null=True, blank=True)
    beneficiary_bank = models.CharField(max_length=300, null=True, blank=True)
    beneficiary_bank_code = models.CharField(max_length=300, null=True, blank=True)
    beneficiary_bank_logo = models.CharField(max_length=300, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return super().__str__()

    class Meta:
        verbose_name = "AGENT PAYOUT BENEFICIARY"
        verbose_name_plural = "AGENT PAYOUT BENEFICIARIES"


class BoughtLotteryTickets(models.Model):
    GAME_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
    ]

    agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, blank=True)
    ussd_code = models.CharField(max_length=300)
    game_type = models.CharField(max_length=300, choices=GAME_TYPE)
    amount = models.FloatField(default=0)
    pontential_win = models.FloatField(default=0)
    game_id = models.CharField(max_length=300)
    paid = models.BooleanField(default=False)
    is_available = models.BooleanField(default=True)
    ticket = models.CharField(max_length=300)
    is_bonus_ticket = models.BooleanField(default=False)
    no_of_tickets = models.IntegerField(default=0)
    # ========================================================= soccer cash item
    football_table = models.ForeignKey(
        "sport_app.FootballTable",
        on_delete=models.CASCADE,
        related_name="pos_soccer_score",
        null=True,
        blank=True,
    )
    goal_scorer = models.ForeignKey(
        "sport_app.GoalScorer",
        on_delete=models.CASCADE,
        related_name="pos_goal_scorer",
        null=True,
        blank=True,
    )
    dual_team_finalist = models.ForeignKey(
        "sport_app.DualTeamFinalist", on_delete=models.CASCADE, null=True, blank=True
    )
    team_final_list = models.ForeignKey("sport_app.TeamFinalist", on_delete=models.CASCADE, null=True, blank=True)
    goal = models.PositiveIntegerField(null=True, blank=True)
    band_played = models.CharField(max_length=150, null=True, blank=True)
    home_choice = models.PositiveIntegerField(null=True, blank=True)
    away_choice = models.PositiveIntegerField(null=True, blank=True)
    game_fixture_id = models.CharField(max_length=150, null=True, blank=True)
    pin = models.CharField(max_length=6, null=True, blank=True, editable=False)
    # =========================================================================
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.agent.email}"

    class Meta:
        verbose_name = "BOUGHT LOTTERY TICKET"
        verbose_name_plural = "BOUGHT LOTTERY TICKETS"

    @classmethod
    def create_lottery_ticket(
        cls,
        agent,
        game_type,
        no_of_lines,
        quanity,
        band=None,
        combined_retail_ticket=False,
        global_ticket_pin=None,
        global_ussd_code=None,
    ):
        """

        args: combined_retail_ticket:
                BASICALLY, WHAT THIS THOSE IS TO GENERATE AND COMBINE RETAIL TICKET
                SO THAT ONE USSD DAIL WILL PLAY ALL THE TICKET COMBINED FOR A USER

        """

        from main.models import LotteryModel, LottoTicket

        # print("create_lottery_ticket combined_retail_ticket", combined_retail_ticket)
        game_ids = []
        # return game_ids

        if combined_retail_ticket is True:
            ussd_code = global_ussd_code
            ticket_pin = global_ticket_pin

            print(
                f"""

                  global_ussd_code: {global_ussd_code}
                  global_ticket_pin: {global_ticket_pin}

                  """
            )

        if game_type == "SALARY_FOR_LIFE":
            for i in range(0, quanity):
                from main.api.api_lottery_helpers import generate_game_play_id

                game_play_id = generate_game_play_id()

                if combined_retail_ticket is False:
                    print("combined_retail_ticket is False")
                    game_play_id = generate_game_play_id()
                    ussd_code = cls.generate_ussd_code()
                    ticket_pin = generate_ticket_pin()

                for n in range(0, no_of_lines):
                    (
                        amount,
                        pontential_win,
                    ) = LottoTicket.salary_for_life_stake_amount_pontential_winning_for_lotto_agents(no_of_lines).get(
                        "stake_amount"
                    ) / no_of_lines, LottoTicket.salary_for_life_stake_amount_pontential_winning_for_lotto_agents(
                        no_of_lines
                    ).get(
                        "total_winning_amount"
                    )

                    ticket = serialize_ticket(
                        cls.generate_lottery_ticket(
                            lottery_type="SALARY_FOR_LIFE",
                        )
                    )

                    cls.objects.create(
                        agent=agent,
                        game_type=game_type,
                        amount=amount,
                        pontential_win=pontential_win,
                        game_id=game_play_id,
                        ticket=ticket,
                        no_of_tickets=no_of_lines,
                        ussd_code=ussd_code,
                        paid=True,
                        pin=ticket_pin,
                    )

                game_ids.append(game_play_id)

            return game_ids

        elif game_type == "INSTANT_CASHOUT":
            for i in range(0, quanity):
                game_play_id = generate_game_play_id()

                if combined_retail_ticket is False:
                    ussd_code = cls.generate_ussd_code()
                    ticket_pin = generate_ticket_pin()

                for n in range(0, no_of_lines):
                    (
                        amount,
                        pontential_win,
                    ) = LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(no_of_lines).get(
                        "stake_amount"
                    ) / no_of_lines, LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
                        no_of_lines
                    ).get(
                        "total_winning_amount"
                    )

                    ticket = serialize_ticket(cls.generate_lottery_ticket(lottery_type="INSTANT_CASHOUT"))

                    cls.objects.create(
                        agent=agent,
                        game_type=game_type,
                        amount=amount,
                        pontential_win=pontential_win,
                        game_id=game_play_id,
                        ticket=ticket,
                        no_of_tickets=no_of_lines,
                        ussd_code=ussd_code,
                        paid=True,
                        pin=ticket_pin,
                    )

                game_ids.append(game_play_id)

            return game_ids

        elif game_type == "WYSE_CASH":
            for i in range(0, quanity):
                game_play_id = generate_game_play_id()

                if combined_retail_ticket is False:
                    ussd_code = cls.generate_ussd_code()
                    ticket_pin = generate_ticket_pin()

                for n in range(0, no_of_lines):
                    if band == "10000":
                        amount = (
                            LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
                                band=10000, no_of_line=no_of_lines
                            ).get("stake_amount")
                            / no_of_lines
                        )
                        pontential_win = LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
                            band=10000, no_of_line=no_of_lines
                        ).get("total_winning_amount")

                    elif band == "50000":
                        amount = (
                            LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
                                band=50000, no_of_line=no_of_lines
                            ).get("stake_amount")
                            / no_of_lines
                        )
                        pontential_win = LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
                            band=50000, no_of_line=no_of_lines
                        ).get("total_winning_amount")

                    elif band == "100000":
                        amount = (
                            LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
                                band=100000, no_of_line=no_of_lines
                            ).get("stake_amount")
                            / no_of_lines
                        )
                        pontential_win = LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
                            band=100000, no_of_line=no_of_lines
                        ).get("total_winning_amount")

                    elif band == "200000":
                        amount = (
                            LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
                                band=200000, no_of_line=no_of_lines
                            ).get("stake_amount")
                            / no_of_lines
                        )
                        pontential_win = LotteryModel.wyse_cash_stake_amount_pontential_winning_for_lotto_agents(
                            band=200000, no_of_line=no_of_lines
                        ).get("total_winning_amount")

                    ticket = cls.generate_wyse_cash_ticket()

                    cls.objects.create(
                        agent=agent,
                        game_type=game_type,
                        amount=amount,
                        pontential_win=pontential_win,
                        game_id=game_play_id,
                        ticket=ticket,
                        no_of_tickets=no_of_lines,
                        ussd_code=ussd_code,
                        paid=True,
                        pin=ticket_pin,
                    )

                game_ids.append(game_play_id)

            return game_ids

        return game_ids

    @classmethod
    def generate_ussd_code(cls):
        range_start = 10 ** (6 - 1)
        range_end = (10**6) - 1

        ussd_code = f"*347*800*{randint(range_start, range_end)}#"
        if cls.objects.filter(ussd_code=ussd_code).exists():
            return cls.generate_ussd_code()

        return ussd_code

    @classmethod
    def generate_lottery_ticket(cls, lottery_type):
        if lottery_type == "SALARY_FOR_LIFE":
            ticket = []
            while len(ticket) < 5:
                pick = randint(1, 49)
                if pick not in ticket:
                    ticket.append(pick)

            return ticket

        elif lottery_type == "INSTANT_CASHOUT":
            ticket = []
            while len(ticket) < 4:
                pick = randint(1, 40)
                if pick not in ticket:
                    ticket.append(pick)

            return ticket

    @classmethod
    def generate_wyse_cash_ticket(cls):
        from main.models import LotteryModel

        name = DommyName(6).generate_name()

        range_start = 10 ** (6 - 1)
        range_end = (10**6) - 1
        generated_number = randint(range_start, range_end)

        splited_names = name.split()

        first_name = splited_names[0] if len(splited_names) > 0 else ""
        last_name = splited_names[1] if len(splited_names) > 1 else ""

        ticket = f"{first_name[0]}{last_name[0]}-{generated_number}"

        if cls.objects.filter(ticket=ticket).exists():
            return cls.generate_wyse_cash_ticket()

        if LotteryModel.objects.filter(lucky_number=ticket).exists():
            return cls.generate_wyse_cash_ticket()

        return ticket

    @classmethod
    def register_bonus_lottery(cls, phone, ussd_code):
        # print(ussd_code)
        from pos_app.sport_helper.soccer_reg import verify_ticket
        from wyse_ussd.models import create_wyse_ussd_user

        bought_tickets_instance = cls.objects.filter(ussd_code=ussd_code, paid=True).last()

        create_wyse_ussd_user(phone)

        if bought_tickets_instance is None:
            return {"status": False, "message": "Invalid USSD Code"}

        if bought_tickets_instance.is_available is False:
            return {"status": False, "message": "Ticket has been used"}

        if bought_tickets_instance.game_type == "SOCCER_CASH":
            # print("I AM SOCCER CASH")
            return verify_ticket(bought_tickets_instance.game_id)

        else:
            return {"status": True, "message": "CON Enter Ticket Pin: \n"}

    @staticmethod
    def create_soccer_cash_data(cls, agent):
        pass

    @classmethod
    def soccer_cash_prediction_retail_response(cls, bought_ticket_query_set):
        pass


class GamesTable(models.Model):
    GAME_TYPE = (
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("BANKER", "BANKER"),
        ("AWOOF", "AWOOF"),
        ("QUIKA", "QUIKA"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
    )
    game_type = models.CharField(max_length=100, choices=GAME_TYPE, default="SOCCER_CASH", unique=True)

    class Meta:
        verbose_name = "GAME TYPE"
        verbose_name_plural = "GAME TYPES"

    def __str__(self):
        return f"{self.game_type}"


def retail_salary_for_life_price_multiplier_dict():
    return {
        1: 1,
        2: 4,
        3: 9,
        4: 16,
        5: 50,
        6: 100,
    }


class AgentConstantVariables(models.Model):
    agent_sales_commission = models.FloatField(default=0)
    agent_win_commission = models.FloatField(default=0)
    agent_play_commission = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    avialable_games = models.ManyToManyField(GamesTable, related_name="agent_games")
    lotto_agent_pre_funding_amount = models.FloatField(default=0)
    phone_number_is_required = models.BooleanField(default=True)
    is_instant_cashout_enabled = models.BooleanField(default=False)
    referral_amount = models.FloatField(default=0)
    num_of_referral_to_get_bonus = models.IntegerField(default=3)
    unable_to_remit_but_allow_to_access_lotto = models.BooleanField(default=False)
    expected_sales_global = models.FloatField(default=30000)
    expected_sales_local = models.FloatField(default=10500)
    unable_to_remit_but_allow_to_access_lotto = models.BooleanField(default=False)
    allow_mobile_users_seperate_withdrawal = models.BooleanField(default=False)
    unable_to_remit_but_allow_to_access_lotto = models.BooleanField(default=False)
    expected_sales_global = models.FloatField(default=30000)
    expected_sales_local = models.FloatField(default=10500)
    seed_ratio_constant = models.FloatField(default=1.7, help_text="Used to determine the end of a seeder.")
    active_rapid_fire_batch = models.CharField(max_length=255)
    active_seeder_batch_data = models.JSONField(default=dict)
    seeder_burst_amount = models.FloatField(default=1000, help_text="The rapid fire burst value")
    rapid_fire_release_after = models.PositiveIntegerField(
        default=5, help_text="This is the number of tickets after which a rapid fire pending winning can be releaded"
    )
    break_after = models.PositiveIntegerField(
        default=5,
        help_text="This is the number of rapid fire winnings after which a realing winnings should be paused",
    )
    rapid_fire_only = models.BooleanField(default=False)
    seed_amount = models.FloatField(default=125000)
    rapid_fire_amount = models.FloatField(default=50000)
    rapid_fire_release_threshold = models.FloatField(
        default=77, help_text="The threshold at which rapid fire should be released to create available winnings"
    )
    ignore_rapid_fire_threshold = models.BooleanField(
        default=False, help_text="Whether to ignore the rapid fire release threshold"
    )
    company_commission_percentage_on_business_development_agent = models.FloatField(default=20)
    business_development_agent_commission_percentage = models.FloatField(default=2)
    instant_game_commission_percentage = models.FloatField(default=15)
    draw_game_commission_percentage = models.FloatField(default=30)
    company_commission_percentage_on_relationship_managers = models.FloatField(default=25)
    running_promotion = models.BooleanField(default=False)
    promotion_games = models.ManyToManyField("PromotionGames", related_name="promotion_games")
    salary_for_life_line_restriction = models.IntegerField(default=3)
    retail_salary_for_life_price_multiplier = models.JSONField(
        default=retail_salary_for_life_price_multiplier_dict,
        help_text="",
    )
    late_withdrawal_threshold_days = models.PositiveIntegerField(default=7)
    vertical_lead_commission_percentage = models.FloatField(default=0.08)
    supervisor_commission_percentage = models.FloatField(default=0.05)

    def __str__(self):
        return "Agent commission"

    def save(self, *args, **kwargs):
        if self.pk:
            __original_agent_sales_commission = 0
            __original_expected_sales_global = False
            __original_expected_sales_local = False

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "agent_sales_commission":
                    __original_agent_sales_commission = field.value_from_object(old)

                if field.name == "expected_sales_global":
                    __original_expected_sales_global = field.value_from_object(old)

                if field.name == "expected_sales_local":
                    __original_expected_sales_local = field.value_from_object(old)

            if __original_agent_sales_commission != self.agent_sales_commission:
                redis_db = RedisStorage(redis_key="agent_sales_commission")
                redis_db.set_data(self.agent_sales_commission)

            if __original_expected_sales_global != self.expected_sales_global:
                redis_db = RedisStorage(redis_key="expected_sales_global")
                redis_db.set_data(self.expected_sales_global)

            if __original_expected_sales_local != self.expected_sales_local:
                redis_db = RedisStorage(redis_key="expected_sales_local")
                redis_db.set_data(self.expected_sales_local)

        return super(AgentConstantVariables, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "AGENT CONSTANT VARIABLES"
        verbose_name_plural = "AGENT CONSTANT VARIABLES"

    @classmethod
    def get_agent_sales_commission(cls):
        agent_sales_commission_per = cls.objects.last().agent_sales_commission if cls.objects.exists() else 0.0
        redis_db = RedisStorage(redis_key="agent_sales_commission")
        redis_db.set_data(agent_sales_commission_per)
        return agent_sales_commission_per

    @classmethod
    def get_win_agent_commission(cls):
        return cls.objects.last().agent_win_commission if cls.objects.exists() else 0.0

    @classmethod
    def get_available_games(cls):
        return cls.objects.last().avialable_games.all() if cls.objects.exists() else None

    @classmethod
    def get_available_games_list(cls):
        return (
            cls.objects.last().avialable_games.all().values_list("game_type", flat=True)
            if cls.objects.exists()
            else None
        )

    @classmethod
    def get_agent_play_commission(cls):
        return cls.objects.last().agent_play_commission if cls.objects.exists() else 0.0

    @classmethod
    def get_lotto_agent_pre_funding_amount(cls):
        return cls.objects.last().lotto_agent_pre_funding_amount if cls.objects.exists() else 0.0

    @classmethod
    def get_phone_number_is_required(cls):
        return cls.objects.last().phone_number_is_required if cls.objects.exists() else False

    @classmethod
    def get_is_instant_cashout_enabled(cls):
        return cls.objects.last().is_instant_cashout_enabled if cls.objects.exists() else True

    @classmethod
    def get_referral_amount(cls):
        return cls.objects.last().referral_amount if cls.objects.exists() else 0.0

    @classmethod
    def get_num_of_referral_to_get_bonus(cls):
        return cls.objects.last().num_of_referral_to_get_bonus if cls.objects.exists() else 3

    @classmethod
    def get_unable_to_remit_but_allow_to_access_lotto(cls):
        return cls.objects.last().unable_to_remit_but_allow_to_access_lotto if cls.objects.exists() else False

    @classmethod
    def get_expected_sales(cls):
        if cls.objects.exists():
            g_redis_db = RedisStorage(redis_key="expected_sales_global")
            if g_redis_db.get_data() is None:
                amount = cls.objects.last()

                RedisStorage(redis_key="expected_sales_global").set_data(amount.expected_sales_global)
                RedisStorage(redis_key="expected_sales_local").set_data(amount.expected_sales_local)

                return {
                    "global": amount.expected_sales_global,
                    "local": amount.expected_sales_local,
                }

            l_redis_db = RedisStorage(redis_key="expected_sales_local")
            if l_redis_db.get_data() is None:
                amount = cls.objects.last()

                RedisStorage(redis_key="expected_sales_global").set_data(amount.expected_sales_global)
                RedisStorage(redis_key="expected_sales_local").set_data(amount.expected_sales_local)

                return {
                    "global": amount.expected_sales_global,
                    "local": amount.expected_sales_local,
                }

            return {
                "global": float(g_redis_db.get_data()),
                "local": float(l_redis_db.get_data()),
            }

        return False

    @classmethod
    def allow_mobile_users_to_withdrwal_from_different_flow(cls):
        return cls.objects.last().allow_mobile_users_seperate_withdrawal if cls.objects.exists() else False

    @classmethod
    def get_instant_game_commission_percentage(cls):
        return cls.objects.last().instant_game_commission_percentage / 100 if cls.objects.exists() else 15 / 100

    @classmethod
    def get_draw_game_commission_percentage(cls):
        return cls.objects.last().draw_game_commission_percentage / 100 if cls.objects.exists() else 30 / 100

    @classmethod
    def get_company_commission_percentage_on_relationship_managers(cls):
        return (
            cls.objects.last().company_commission_percentage_on_relationship_managers / 100
            if cls.objects.exists()
            else 25 / 100
        )

    @classmethod
    def get_company_commission_percentage_on_business_development_agent(cls):
        return (
            cls.objects.last().company_commission_percentage_on_business_development_agent / 100
            if cls.objects.exists()
            else 20 / 100
        )

    @classmethod
    def get_business_development_agent_commission_percentage(cls):
        return (
            cls.objects.last().business_development_agent_commission_percentage / 100
            if cls.objects.exists()
            else 2 / 100
        )

    @classmethod
    def is_promotion_running(cls):
        return cls.objects.last().running_promotion if cls.objects.exists() else False

    @classmethod
    def get_promotion_games(cls):
        return cls.objects.last().promotion_games.all() if cls.objects.exists() else None

    @classmethod
    def get_salary_for_life_line_restriction(cls):
        return cls.objects.last().salary_for_life_line_restriction if cls.objects.last() else 5

    @classmethod
    def get_salary_for_life_price_multiplier(cls):
        return (
            cls.objects.last().retail_salary_for_life_price_multiplier
            if cls.objects.last()
            else retail_salary_for_life_price_multiplier_dict()
        )

    @classmethod
    def get_late_withdrawal_threshold_days(cls):
        return cls.objects.last().late_withdrawal_threshold_days if cls.objects.last() else 7

    @classmethod
    def get_vertical_lead_commission_percentage(cls):
        return cls.objects.last().vertical_lead_commission_percentage if cls.objects.last() else 0.08

    @classmethod
    def get_supervisor_commission_percentage(cls):
        return cls.objects.last().supervisor_commission_percentage if cls.objects.last() else 0.05


class AgentFundingTable(models.Model):
    FUNDING_SOURCE = (
        ("WOVEN", "WOVEN"),
        ("AGENCY_BANKING", "AGENCY BANKING"),
        ("AGENCY_BANKING_VFD", "AGENCY BANKING VFD"),
    )

    VERIFICATION_STATUS = (
        ("PENDING", "PENDING"),
        ("VERIFIED", "VERIFIED"),
        ("FAILED", "FAILED"),
    )

    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, related_name="agent_funding")
    amount = models.FloatField(default=0)
    source = models.CharField(max_length=50, choices=FUNDING_SOURCE, default="WOVEN")
    reference = models.CharField(max_length=300, blank=True, null=True)
    payload = models.TextField(blank=True, null=True)
    is_bonus = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    verification_status = models.CharField(max_length=50, choices=VERIFICATION_STATUS, default="PENDING")
    posted_to_agent_wallet = models.BooleanField(default=False)
    verification_response_payload = models.TextField(blank=True, null=True)
    debit_credit_id = models.CharField(max_length=300, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AGENT FUNDING TABLE"
        verbose_name_plural = "AGENT FUNDING TABLES"

    def __str__(self):
        return f"{self.agent} - {self.amount}"

    @classmethod
    def get_agent_funding(cls, agent):
        return (
            cls.objects.filter(agent=agent, is_verified=True).aggregate(Sum("amount"))["amount__sum"]
            if cls.objects.filter(agent=agent, is_verified=True).exists()
            else 0.0
        )

    @classmethod
    def get_agent_bonus_funding(cls, agent):
        return (
            cls.objects.filter(agent=agent, is_verified=True, is_bonus=True).aggregate(Sum("amount"))["amount__sum"]
            if cls.objects.filter(agent=agent, is_verified=True, is_bonus=True).exists()
            else 0.0
        )

    @classmethod
    def get_agent_funding_history(cls, agent):
        return cls.objects.filter(agent=agent, is_verified=True)

    @classmethod
    def get_agent_bonus_funding_history(cls, agent):
        return cls.objects.filter(agent=agent, is_verified=True, is_bonus=True)

    @classmethod
    def get_agent_funding_history_by_date(cls, agent, start_date, end_date):
        return cls.objects.filter(agent=agent, is_verified=True, created_at__range=(start_date, end_date))

    @classmethod
    def get_agent_bonus_funding_history_by_date(cls, agent, start_date, end_date):
        return cls.objects.filter(
            agent=agent,
            is_verified=True,
            is_bonus=True,
            created_at__range=(start_date, end_date),
        )

    @classmethod
    def get_agent_funding_history_by_date_and_type(cls, agent, start_date, end_date, is_bonus):
        return cls.objects.filter(
            agent=agent,
            is_verified=True,
            is_bonus=is_bonus,
            created_at__range=(start_date, end_date),
        )

    @classmethod
    def get_agent_funding_history_by_type(cls, agent, is_bonus):
        return cls.objects.filter(agent=agent, is_verified=True, is_bonus=is_bonus)

    @classmethod
    def get_agent_funding_history_by_date_and_source(cls, agent, source):
        return cls.objects.filter(agent=agent, is_verified=True, source=source)


class LottoAgentRemittanceTable(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True, blank=True)
    amount = models.FloatField(default=0)
    amount_paid = models.FloatField(default=0)
    excess_amount = models.FloatField(default=0)
    remitted = models.BooleanField(default=False)
    due = models.BooleanField(default=False)
    days_due = models.IntegerField(default=0)
    terminal_retrieved = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    comment = models.CharField(max_length=300, null=True, blank=True)

    def __str__(self) -> str:
        try:
            return f"{self.agent.first_name} {self.agent.last_name}"
        except Exception:
            return str(self.amount)

    @classmethod
    def add_create_remittance(cls, agent, amount, agent_wallet_balance):
        agent_wallet_balance += amount

        try:
            remittance_instance = cls.objects.get(agent=agent, remitted=False)
        except Exception:
            remittance_instance = cls.objects.filter(agent=agent, remitted=False).first()

            if remittance_instance is None:
                agent_pre_funding = AgentWalletTransaction.objects.filter(
                    agent_wallet__agent__id=agent.id, transaction_from="PRE_FUNDING"
                ).aggregate(Sum("amount"))["amount__sum"]
                if agent_pre_funding is None:
                    agent_pre_funding = 0

                if agent_pre_funding < 1:
                    return None

                else:
                    # print("agent_wallet_balance", agent_wallet_balance)
                    # print("agent_pre_funding", agent_pre_funding)

                    if agent_wallet_balance > agent_pre_funding:
                        if (agent_wallet_balance - agent_pre_funding) >= amount:
                            # agent is playing from excess
                            return None
                        else:
                            # print("SUPPOSED TO BE HERE")
                            excess = agent_wallet_balance - agent_pre_funding

                            amount = amount - excess

                            # print("amount", amount)

                            cls.objects.create(agent=agent, amount=amount)

                            return None
                    else:
                        cls.objects.create(agent=agent, amount=amount)

                        return None
            else:
                remittance_instance.amount += amount
                remittance_instance.save()

        remittance_instance.amount += amount
        remittance_instance.save()

        return

    @classmethod
    def deduct_remittance(cls, agent, amount):
        try:
            remittance_instance = cls.objects.get(agent__id=agent.id, remitted=False)
        except Exception:
            agent_transaction = AgentWalletTransaction.objects.filter(
                agent_wallet__agent__id=agent.id, transaction_from="PRE_FUNDING"
            ).first()
            if agent_transaction is None:
                return None
            else:
                data = {
                    "has_excess": True,
                    "excess_amount": amount,
                    "amount": amount,
                    "expected_remittance": 0,
                    "has_remittance": False,
                }

                return data

        remittance_instance.refresh_from_db()

        if remittance_instance.amount_paid + amount <= remittance_instance.amount:
            # remittance_instance.amount_paid += amount
            # remittance_instance.save()
            previous_amount_paid = remittance_instance.amount_paid

            _amount_paid = remittance_instance.amount_paid + amount

            if remittance_instance.amount_paid + amount == remittance_instance.amount:
                remittance_instance.amount_paid = _amount_paid
                remittance_instance.remitted = True
                remittance_instance.due = False
                remittance_instance.save()

                cls.objects.filter(id=remittance_instance.id).update(
                    amount_paid=_amount_paid, remitted=True, due=False
                )
                celery_making_sure_agent_remittance_record_are_updated.delay(
                    remittance_instance.agent.id,
                    amount,
                    previous_amount_paid + amount,
                )
                celery_trigger_agent_reward.delay(remittance_instance.agent.id)

            else:
                remittance_instance.amount_paid = _amount_paid
                remittance_instance.save()

                cls.objects.filter(id=remittance_instance.id).update(amount_paid=_amount_paid)

                celery_making_sure_agent_remittance_record_are_updated.delay(
                    remittance_instance.agent.id,
                    amount,
                    previous_amount_paid + amount,
                )

            return {
                "has_excess": False,
                "excess_amount": 0,
                "amount": amount,
                "expected_remittance": remittance_instance.amount - remittance_instance.amount_paid,
            }

        else:
            amount_diff = (remittance_instance.amount_paid + amount) - remittance_instance.amount

            data = {
                "has_excess": True,
                "excess_amount": amount_diff,
                "amount": amount,
                "expected_remittance": 0,
            }

            # remittance_instance.amount_paid += amount
            # remittance_instance.excess_amount += amount_diff
            # remittance_instance.save()
            previous_amount_paid = remittance_instance.amount_paid

            _amount_paid = remittance_instance.amount_paid + amount

            remittance_instance.amount_paid = _amount_paid
            remittance_instance.excess_amount += amount_diff
            remittance_instance.remitted = True
            remittance_instance.due = False
            remittance_instance.save()

            cls.objects.filter(id=remittance_instance.id).update(
                amount_paid=_amount_paid,
                excess_amount=amount_diff,
                remitted=True,
                due=False,
            )
            celery_trigger_agent_reward.delay(remittance_instance.agent.id)

            celery_making_sure_agent_remittance_record_are_updated.delay(
                remittance_instance.agent.id,
                amount,
                previous_amount_paid + amount,
            )

            return data

    @classmethod
    def expected_remittance(cls, agent_id):
        try:
            get_agent_remittance = cls.objects.get(agent__id=agent_id, remitted=False)
        except Exception:
            return 0

        return get_agent_remittance.amount - get_agent_remittance.amount_paid

    @classmethod
    def is_remittance_due(cls, agent_id):
        if AgentConstantVariables().get_unable_to_remit_but_allow_to_access_lotto() is True:
            return {"status": False, "amount": 0}

        get_agent_remittance = cls.objects.filter(agent__id=agent_id, remitted=False).last()

        if get_agent_remittance is None:
            return {"status": False, "amount": 0}

        if get_agent_remittance.due is True:
            return {"status": True, "amount": cls.expected_remittance(agent_id)}

        if get_agent_remittance.created_at.date() == timezone.now().date():
            if get_agent_remittance.created_at.time() >= time(hour=16):
                return {"status": False, "amount": 0}
            else:
                four_pm = datetime.combine(datetime.today(), time(hour=16))

                _timezone = pytz.timezone("Africa/Lagos")
                aware_datetime = _timezone.localize(four_pm)

                if aware_datetime < get_agent_remittance.created_at:
                    return {"status": False, "amount": 0}

                ten_am = datetime.combine(datetime.today(), time(hour=10))
                _now = datetime.today()

                if ((_now.hour - ten_am.hour) >= 6) and (get_agent_remittance.created_at.hour <= 16):
                    get_agent_remittance.due = True
                    get_agent_remittance.save()

                    return {"status": True, "amount": cls.expected_remittance(agent_id)}

                else:
                    return {"status": False, "amount": 0}

        if get_agent_remittance.due is True:
            return {"status": True, "amount": cls.expected_remittance(agent_id)}

        else:
            now = datetime.now()
            if now.hour >= 10:
                return {"status": True, "amount": cls.expected_remittance(agent_id)}

            else:
                return {"status": False, "amount": 0}

    @classmethod
    def update_due_remittance(cls):
        all_un_remitted_remittance = cls.objects.filter(remitted=False, created_at__date__lt=timezone.now().date())

        for remittance in all_un_remitted_remittance:
            if remittance.created_at.date() == timezone.now().date():
                pass
            else:
                if remittance.due is True:
                    now = datetime.now()
                    days_due = (now.date() - remittance.created_at.date()).days
                    remittance.days_due = days_due
                    remittance.save()

                else:
                    now = datetime.now()
                    if now.hour >= 10:
                        days_due = (now.date() - remittance.created_at.date()).days
                        remittance.days_due = days_due
                        remittance.due = True
                        remittance.save()

    @classmethod
    def valuate_amount_to_be_added(cls, amount, agent) -> dict:
        get_agent_remittance = cls.objects.filter(agent=agent, remitted=False).last()

        if get_agent_remittance is None:
            return {"is_amount_much": False, "amount": amount}

        else:
            remittance_amount_plus_amount = get_agent_remittance.amount + amount

            get_agent_total_pre_funding = (
                AgentWalletTransaction.objects.filter(
                    agent_wallet__agent=agent, transaction_from="PRE_FUNDING"
                ).aggregate(Sum("amount"))["amount__sum"]
                or 0.0
            )

            if (remittance_amount_plus_amount > get_agent_total_pre_funding) and get_agent_total_pre_funding != 0.0:
                outstanding_remittance = get_agent_remittance.amount - get_agent_remittance.amount_paid
                return {"is_amount_much": True, "amount": outstanding_remittance}
            else:
                return {"is_amount_much": False, "amount": amount}

    class Meta:
        verbose_name = "LOTTO AGENT REMITANCE TABLE"
        verbose_name_plural = "LOTTO AGENT REMITANCE TABLES"

    @classmethod
    def agent_remittance_balance(cls, agent) -> float:
        remittance = cls.objects.filter(agent=agent, remitted=False)

        if remittance.count() > 0:
            return remittance.aggregate(Sum("amount"))["amount__sum"]

        else:
            return 0.0


class LottoAgentGuarantorDetail(models.Model):
    from pos_app.utils import unique_code_generator

    VERIFICATION_CHOICES = [
        ("NIN", "NIN"),
        ("BVN", "BVN"),
        ("VOTER_CARD", "VOTER_CARD"),
        ("INTERNATIONAL_PASSPORT", "INTERNATIONAL_PASSPORT"),
    ]

    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    verification_id = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        editable=False,
        verbose_name="request id",
        help_text="LibertyPay's unique identifier for KYC3.",
    )
    agent_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name="agent fullname",
    )
    agent_phone = models.CharField(
        max_length=25,
        null=True,
        blank=True,
        verbose_name="agent phone number",
    )
    agent_email = models.EmailField(
        max_length=255,
        null=True,
        blank=True,
    )
    guarantor_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name="guarantor fullname",
    )
    guarantor_phone = models.CharField(
        max_length=25,
        null=True,
        blank=True,
        verbose_name="guarantor phone number",
    )
    guarantor_email = models.EmailField(max_length=255, null=True, blank=True)
    guarantor_occupation = models.CharField(max_length=125, null=True, blank=True)
    guarantor_address = models.TextField(null=True, blank=True)
    unique_code = models.CharField(
        max_length=25,
        default=unique_code_generator,
        verbose_name="guarantor ussd code",
        help_text="The unique identifier for the USSD code.",
    )
    means_of_verification = models.CharField(
        max_length=255,
        choices=VERIFICATION_CHOICES,
        null=True,
        blank=True,
        verbose_name="verification choice",
    )
    verified = models.BooleanField(default=False)
    guarantor_id_number = models.CharField(max_length=255, null=True, blank=True)
    guarantor_verified_first_name = models.CharField(max_length=255, null=True, blank=True)
    guarantor_verified_last_name = models.CharField(max_length=255, null=True, blank=True)
    guarantor_verified_address = models.TextField(null=True, blank=True)
    libertypay_request = models.TextField(null=True, blank=True, verbose_name="libertypay request payload")
    event_sent = models.BooleanField(default=False)
    libertypay_response = models.TextField(null=True, blank=True, verbose_name="libertypay event response")
    payload = models.TextField(null=True, blank=True, verbose_name="redbiller verification response")

    class Meta:
        ordering = ["-date_created"]
        verbose_name = "GUARANTOR DETAIL / KYC3"
        verbose_name_plural = "GUARANTOR DETAILS / KYC3"

    def __str__(self) -> str:
        return f"Agent: {self.agent_email} is guaranteed by {self.guarantor_email}"

    @property
    def get_agent_first_name(self):
        if self.agent_name is not None:
            return self.agent_name.split(" ")[0]

    @property
    def get_guarantor_first_name(self):
        if self.guarantor_name is not None:
            return self.guarantor_name.split(" ")[0]

    @property
    def get_guarantor_last_name(self):
        if self.guarantor_name is not None:
            return self.guarantor_name.split(" ")[-1]

    @classmethod
    def create_agent_record(
        cls,
        verification_unique_id: str,
        agent_name: str,
        agent_phone: str,
        agent_email: str,
        guarantor_name: str,
        guarantor_phone: str,
        guarantor_email: str,
        guarantor_occupation: str,
        guarantor_address: str,
        libertypay_request: json,
    ):
        guarantor_details = cls.objects.create(
            verification_id=verification_unique_id,
            agent_name=agent_name,
            agent_phone=agent_phone,
            agent_email=agent_email,
            guarantor_name=guarantor_name,
            guarantor_phone=guarantor_phone,
            guarantor_email=guarantor_email,
            guarantor_occupation=guarantor_occupation,
            guarantor_address=guarantor_address,
            libertypay_request=json.dumps(libertypay_request),
        )
        return guarantor_details


class AgentVFDAccountDetail(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    vnuban = models.CharField(max_length=150, null=True, blank=True)
    acct_name = models.CharField(max_length=150, null=True, blank=True)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    account_type = models.CharField(max_length=150, null=True, blank=True)
    bank_code = models.CharField(max_length=150, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    response = models.TextField()
    date = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return str(self.vnuban)

    class Meta:
        verbose_name = "Agent VFD ACCOUNT DETAIL"
        verbose_name_plural = "Agent VFD ACCOUNT DETAILS"


class CreateAgentLogs(models.Model):
    phone = models.CharField(max_length=25)
    payload = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    upddated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Create Agent Logs"
        verbose_name_plural = "Create Agent Logs"


class AgencyBankingLogs(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    level_description = models.CharField(max_length=500, null=True, blank=True)
    token = models.TextField(null=True, blank=True)
    payload = models.TextField(null=True, blank=True)
    response = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    upddated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Agency Banking Call Log"
        verbose_name_plural = "Agency Banking Call Logs"


class PosWithdrawalRequest(models.Model):
    STAGES = (
        ("STAGE1", "STAGE1"),
        ("STAGE2", "STAGE2"),
    )
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    game_id = models.CharField(max_length=300)
    stage_one_payload = models.TextField(blank=True, null=True)
    stage_two_payload = models.TextField(blank=True, null=True)
    stage = models.CharField(max_length=25, choices=STAGES, default="STAGE1")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class LottoSuperAgents(models.Model):
    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    full_name = models.CharField(max_length=255)
    phone = models.CharField(max_length=300)
    user_id = models.CharField(max_length=300)
    email = models.CharField(max_length=300, null=True, blank=True)
    user_uuid = models.CharField(max_length=500, default=uuid.uuid4)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    address = models.CharField(max_length=300, default="YABA, Lagos Nigeria")
    type = models.CharField(
        max_length=300, choices=enums.TypeOfSuperAgent.choices, default=enums.TypeOfSuperAgent.SUPER_AGENT
    )

    def __str__(self):
        return self.first_name + " " + self.last_name

    def save(self, *args, **kwargs):
        if not self.pk:
            self.full_name = self.first_name + " " + self.last_name
        return super(LottoSuperAgents, self).save(*args, **kwargs)

    @property
    def name(self):
        return self.first_name + " " + self.last_name

    def get_duration(self):
        """Calculate the duration since the user was created."""
        if not self.created_at:
            return "Unknown"

        current_time = datetime.now()
        created_time = self.created_at.replace(tzinfo=None)  # Ensure timezone compatibility
        delta = relativedelta(current_time, created_time)

        duration_parts = []
        if delta.years:
            duration_parts.append(f"{delta.years} year{'s' if delta.years > 1 else ''}")
        if delta.months:
            duration_parts.append(f"{delta.months} month{'s' if delta.months > 1 else ''}")

        return ", ".join(duration_parts) if duration_parts else "Less than a month"

    class Meta:
        verbose_name = "SUPER AGENT"
        verbose_name_plural = "SUPER AGENTS"


class LottoSuperAgentWallet(models.Model):
    super_agent = models.ForeignKey(LottoSuperAgents, on_delete=models.CASCADE)
    commission_balance = models.FloatField(default=0)
    rewarded_commission_balance = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    transaction_type = None

    def __str__(self):
        return f"{self.super_agent.full_name}"

    def save(self, *args, **kwargs):
        if self.pk:
            __original_commission_bal = 0
            __original_rewarded_commission_bal = 0

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "commission_balance":
                    __original_commission_bal = field.value_from_object(old)

                elif field.name == "rewarded_commission_balance":
                    __original_rewarded_commission_bal = field.value_from_object(old)

            if self.commission_balance != __original_commission_bal:
                if self.commission_balance > __original_commission_bal:
                    if self.transaction_type is None:
                        LottoSuperAgentWalletTransaction().create_wallet_transaction(
                            wallet=self,
                            amount=self.commission_balance - __original_commission_bal,
                            transaction_type="CREDIT",
                            transaction_from="COMMISSION",
                        )
                    else:
                        LottoSuperAgentWalletTransaction().create_wallet_transaction(
                            wallet=self,
                            amount=self.commission_balance - __original_commission_bal,
                            transaction_type=self.transaction_type,
                            transaction_from="COMMISSION",
                        )

                elif self.commission_balance < __original_commission_bal:
                    LottoSuperAgentWalletTransaction().create_wallet_transaction(
                        wallet=self,
                        amount=__original_commission_bal - self.commission_balance,
                        transaction_type="DEBIT",
                        transaction_from="COMMISSION",
                    )

            if self.rewarded_commission_balance != __original_rewarded_commission_bal:
                if self.rewarded_commission_balance > __original_rewarded_commission_bal:
                    LottoSuperAgentWalletTransaction().create_wallet_transaction(
                        wallet=self,
                        amount=self.rewarded_commission_balance - __original_rewarded_commission_bal,
                        transaction_type="CREDIT",
                        transaction_from="COMMISSION_REWARDED",
                    )

                elif self.rewarded_commission_balance < __original_rewarded_commission_bal:
                    LottoSuperAgentWalletTransaction().create_wallet_transaction(
                        wallet=self,
                        amount=__original_rewarded_commission_bal - self.rewarded_commission_balance,
                        transaction_type="DEBIT",
                        transaction_from="COMMISSION_REWARDED",
                    )

        return super(LottoSuperAgentWallet, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "SUPER AGENT WALLET"
        verbose_name_plural = "SUPER AGENT WALLETS"


class LottoSuperAgentWalletTransaction(models.Model):
    TRANSACTION_TYPE = (
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
        ("REVERSAL", "REVERSAL"),
    )

    TRANSACTION_FROM = (
        ("COMMISSION", "COMMISSION"),
        ("COMMISSION_REWARDED", "COMMISSION_REWARDED"),
    )

    wallet = models.ForeignKey(LottoSuperAgentWallet, on_delete=models.CASCADE)
    amount = models.FloatField(default=0)
    transactton_type = models.CharField(max_length=25, choices=TRANSACTION_TYPE)
    transaction_from = models.CharField(max_length=25, choices=TRANSACTION_FROM)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def create_wallet_transaction(cls, wallet, amount, transaction_type, transaction_from):
        cls.objects.create(
            wallet=wallet,
            amount=amount,
            transactton_type=transaction_type,
            transaction_from=transaction_from,
        )
        return True

    class Meta:
        verbose_name = "SUPER AGENT WALLET TRANSACTION"
        verbose_name_plural = "SUPER AGENT WALLET TRANSACTIONS"


class RetailTicketRequestLogs(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    request_payload = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.agent.name


class SalesReps(models.Model):
    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    full_name = models.CharField(max_length=255)
    phone = models.CharField(max_length=300)
    user_id = models.CharField(max_length=300)
    email = models.CharField(max_length=300, null=True, blank=True)
    user_uuid = models.CharField(max_length=500, default=uuid.uuid4)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    address = models.CharField(max_length=300, default="YABA, Lagos Nigeria")
    count = models.IntegerField(default=0)
    number_of_referred = models.IntegerField(default=0)

    def __str__(self):
        return self.first_name + " " + self.last_name

    def save(self, *args, **kwargs):
        if not self.pk:
            self.full_name = self.first_name + " " + self.last_name
        else:
            if (self.count >= AgentConstantVariables().get_num_of_referral_to_get_bonus()) and (
                AgentConstantVariables().get_num_of_referral_to_get_bonus() > 0
            ):
                self.number_of_referred += self.count
                self.count = 0
                SalesRepWallet().reward_bonus(sales_rep_id=self.id)

        return super(SalesReps, self).save(*args, **kwargs)

    @property
    def name(self):
        return self.first_name + " " + self.last_name

    class Meta:
        verbose_name = "SALES REP"
        verbose_name_plural = "SALES REPS"

    @classmethod
    def add_to_count(cls, sales_rep_id):
        sales_rep = cls.objects.filter(id=sales_rep_id).last()
        if sales_rep:
            get_sales_rep_wallet = SalesRepWallet.objects.filter(
                sales_rep__id=sales_rep_id, bonus_balance__lt=1
            ).last()
            if get_sales_rep_wallet:
                if AgentConstantVariables().get_referral_amount() > 0:
                    get_sales_rep_wallet.bonus_balance = AgentConstantVariables().get_referral_amount()
                    get_sales_rep_wallet.save()

            sales_rep.count += 1
            sales_rep.save()


class SalesRepWallet(models.Model):
    sales_rep = models.ForeignKey(SalesReps, on_delete=models.CASCADE)
    bonus_balance = models.FloatField(default=0)
    rewarded_bonus = models.FloatField(default=0)
    not_rewarded_bonus = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    transaction_type = None

    def __str__(self):
        return f"{self.sales_rep.full_name}"

    def save(self, *args, **kwargs):
        if self.pk:
            __original_commission_bal = 0
            __original_rewarded_commission_bal = 0

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "bonus_balance":
                    __original_commission_bal = field.value_from_object(old)

                elif field.name == "rewarded_bonus":
                    __original_rewarded_commission_bal = field.value_from_object(old)

            if self.bonus_balance != __original_commission_bal:
                if self.bonus_balance > __original_commission_bal:
                    if self.transaction_type is None:
                        SalesRepWalletTransaction().create_wallet_transaction(
                            wallet=self,
                            amount=self.bonus_balance - __original_commission_bal,
                            transaction_type="CREDIT",
                            transaction_from="REFERRAL_COMMISSION",
                        )
                    else:
                        SalesRepWalletTransaction().create_wallet_transaction(
                            wallet=self,
                            amount=self.bonus_balance - __original_commission_bal,
                            transaction_type=self.transaction_type,
                            transaction_from="REFERRAL_COMMISSION",
                        )

                elif self.bonus_balance < __original_commission_bal:
                    SalesRepWalletTransaction().create_wallet_transaction(
                        wallet=self,
                        amount=__original_commission_bal - self.bonus_balance,
                        transaction_type="DEBIT",
                        transaction_from="REFERRAL_COMMISSION",
                    )

            if self.rewarded_bonus != __original_rewarded_commission_bal:
                if self.rewarded_bonus > __original_rewarded_commission_bal:
                    SalesRepWalletTransaction().create_wallet_transaction(
                        wallet=self,
                        amount=self.rewarded_bonus - __original_rewarded_commission_bal,
                        transaction_type="CREDIT",
                        transaction_from="REFERRAL_COMMISSION_REWARDED",
                    )

                elif self.rewarded_bonus < __original_rewarded_commission_bal:
                    SalesRepWalletTransaction().create_wallet_transaction(
                        wallet=self,
                        amount=__original_rewarded_commission_bal - self.rewarded_bonus,
                        transaction_type="DEBIT",
                        transaction_from="REFERRAL_COMMISSION_REWARDED",
                    )

        return super(SalesRepWallet, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "SALE REP WALLET"
        verbose_name_plural = "SALE REP WALLETS"

    @classmethod
    def reward_bonus(cls, sales_rep_id):
        get_sales_rep_wallet = cls.objects.filter(sales_rep__id=sales_rep_id).last()
        if get_sales_rep_wallet is not None:
            if get_sales_rep_wallet.bonus_balance > 0:
                agent_wallet = AgentWallet.objects.filter(agent__user_id=get_sales_rep_wallet.sales_rep.user_id).last()
                if agent_wallet is not None:
                    agent_wallet.bonus_bal += get_sales_rep_wallet.bonus_balance
                    agent_wallet.save()

                    get_sales_rep_wallet.rewarded_bonus += get_sales_rep_wallet.bonus_balance
                    get_sales_rep_wallet.bonus_balance = 0
                    get_sales_rep_wallet.save()


class SalesRepWalletTransaction(models.Model):
    TRANSACTION_TYPE = (
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
        ("REVERSAL", "REVERSAL"),
    )

    TRANSACTION_FROM = (
        ("REFERRAL_COMMISSION", "REFERRAL_COMMISSION"),
        ("REFERRAL_COMMISSION_REWARDED", "REFERRAL_COMMISSION_REWARDED"),
    )

    wallet = models.ForeignKey(SalesRepWallet, on_delete=models.CASCADE)
    amount = models.FloatField(default=0)
    transactton_type = models.CharField(max_length=300, choices=TRANSACTION_TYPE)
    transaction_from = models.CharField(max_length=300, choices=TRANSACTION_FROM)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def create_wallet_transaction(cls, wallet, amount, transaction_type, transaction_from):
        cls.objects.create(
            wallet=wallet,
            amount=amount,
            transactton_type=transaction_type,
            transaction_from=transaction_from,
        )
        return True

    class Meta:
        verbose_name = "SALES REP WALLET TRANSACTION"
        verbose_name_plural = "SALES REP WALLET TRANSACTIONS"


class FailedRemittanceAgencyWalletCharge(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    outstanding_remittance = models.FloatField(default=0)
    amount_charge = models.FloatField(default=0)
    payload = models.TextField()
    response_payload = models.TextField(blank=True, null=True)
    trans_ref = models.CharField(max_length=300, blank=True, null=True)
    successfully_charged = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "FAILED REMITTANCE AGENCY WALLET CHARGE"
        verbose_name_plural = "FAILED REMITTANCE AGENCY WALLET CHARGES"

    @classmethod
    def generate_transaction_ref(cls):
        from main.models import PayoutTransactionTable

        ref = "RC-{}".format(uuid.uuid4())

        if ref in cls.objects.values_list("trans_ref", flat=True):
            return cls.generate_transaction_ref()

        if ref in PayoutTransactionTable.objects.values_list("payout_trans_ref", flat=True):
            return cls.generate_transaction_ref()

        return ref


class AgentSuspensionRequestToAgencyBanking(models.Model):
    STATUS = (
        ("PENDING", "PENDING"),
        ("APPROVED", "APPROVED"),
    )

    TYPE_OF_REQUEST = (
        ("SUSPEND", "SUSPEND"),
        ("UNSUSPEND", "UNSUSPEND"),
    )

    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    reason = models.TextField(blank=True, null=True)
    payload = models.TextField(blank=True, null=True)
    response_payload = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=300, choices=STATUS, default="PENDING")
    type_of_request = models.CharField(max_length=300, choices=TYPE_OF_REQUEST, default="SUSPEND")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AGENT SUSPENSION/UNSUSPENSION REQUEST TO AGENCY BANKING"
        verbose_name_plural = "AGENT SUSPENSION/UNSUSPENSION REQUESTS TO AGENCY BANKING"

    @classmethod
    def request_from_admin_dashbaord(cls, agent_instance, reason, type_of_request):
        from pos_app.pos_helpers import PosAgentHelper

        pos_agent_helper = PosAgentHelper(
            agent_instance=agent_instance,
            amount=0,
            pin=0,
        )

        if type_of_request == "SUSPEND":
            payload = {
                "user_id": agent_instance.user_id,
                "suspend_or_unsuspend_reason": reason,
                "suspend": "true",
            }

            instance = cls.objects.create(
                agent=agent_instance,
                reason=reason,
                payload=json.dumps(payload),
                type_of_request=type_of_request,
            )

            suspension_response = pos_agent_helper.suspend_lotto_agent_on_agency_banking(**payload)

            instance.response_payload = suspension_response
            instance.save()

            if isinstance(suspension_response, dict):
                if str(suspension_response.get("status")).lower() == "success":
                    return True

                elif suspension_response.get("error") == "478":
                    return True

                else:
                    return False
            else:
                return False

        elif type_of_request == "UNSUSPEND":
            payload = {
                "user_id": agent_instance.user_id,
                "suspend_or_unsuspend_reason": reason,
                "suspend": "false",
            }

            instance = cls.objects.create(
                agent=agent_instance,
                reason=reason,
                payload=json.dumps(payload),
                type_of_request=type_of_request,
            )

            unsuspension_response = pos_agent_helper.suspend_lotto_agent_on_agency_banking(**payload)

            instance.response_payload = unsuspension_response
            instance.save()

            if isinstance(unsuspension_response, dict):
                if str(unsuspension_response.get("status")).lower() == "success":
                    return True

                elif unsuspension_response.get("error") == "478":
                    return True

                elif unsuspension_response.get("error") == "471":
                    return True

                else:
                    return False
            else:
                return False
        else:
            return False


class WinWiseEmployeeTable(models.Model):
    title = models.CharField(max_length=1000, null=True, blank=True)
    agent_head_shot_photograph = models.URLField(null=True, blank=True)
    agent_last_name = models.CharField(max_length=1000, null=True, blank=True)
    agent_first_name = models.CharField(max_length=1000, null=True, blank=True)
    bank_verification_no = models.CharField(max_length=1000, null=True, blank=True)
    mobile_number = models.CharField(max_length=1000, null=True, blank=True)
    residential_address = models.CharField(max_length=1000, null=True, blank=True)
    business_address = models.CharField(max_length=1000, null=True, blank=True)
    date_of_birth = models.DateField(max_length=1000, null=True, blank=True)
    gender = models.CharField(max_length=1000, null=True, blank=True)
    email = models.EmailField(unique=True, null=True, blank=True)
    state_of_residence = models.CharField(max_length=1000, null=True, blank=True)
    lga = models.CharField(max_length=1000, null=True, blank=True)
    supervisor = models.CharField(max_length=1000, null=True, blank=True)
    language_spoken = models.CharField(max_length=1000, null=True, blank=True)
    place_of_birth = models.CharField(max_length=1000, null=True, blank=True)
    marital_status = models.CharField(max_length=1000, null=True, blank=True)
    photo_of_vlaid_id = models.URLField(null=True, blank=True)
    photo_of_outlet = models.URLField(null=True, blank=True)
    photo_of_agent_location = models.URLField(null=True, blank=True)
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, null=True, blank=True)
    guarantor_name = models.CharField(max_length=1000, null=True, blank=True)
    nick_name = models.CharField(max_length=1000, null=True, blank=True)
    phone_number = models.CharField(max_length=1000, null=True, blank=True)
    guarantor_gender = models.CharField(max_length=1000, null=True, blank=True)
    bvn = models.CharField(max_length=1000, null=True, blank=True)
    home_address = models.CharField(max_length=1000, null=True, blank=True)
    company_or_business_name = models.CharField(max_length=1000, null=True, blank=True)
    company_or_business_address = models.CharField(max_length=1000, null=True, blank=True)
    guarantor_employment_type = models.CharField(max_length=1000, null=True, blank=True)
    signature = models.URLField(null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)

    @classmethod
    def create_user_data(cls, resp):
        agent_head_shot_photograph = resp.get("q38_typeA38")
        photo_of_vlaid_id = resp.get("q39_typeA39")
        photo_of_outlet = resp.get("q54_typeA54")
        photo_of_agent_location = resp.get("q55_typeA55")
        signature = resp.get("q49_applicantsSignature")

        agent_head_shot_photograph_str = f"{agent_head_shot_photograph[24:]}"
        photo_of_vlaid_id_str = f"{photo_of_vlaid_id[24:]}"
        photo_of_outlet_str = f"{photo_of_outlet[23:]}"
        photo_of_agent_location_str = f"{photo_of_agent_location[24:]}"
        signature_str = f"{signature[23:]}"

        fields = [
            {
                "name": "field1",
                "image_data": agent_head_shot_photograph_str,
                "upload_preset": "field1_upload_preset",
            },
            {
                "name": "field2",
                "image_data": photo_of_vlaid_id_str,
                "upload_preset": "field2_upload_preset",
            },
            {
                "name": "field3",
                "image_data": photo_of_outlet_str,
                "upload_preset": "field3_upload_preset",
            },
            {
                "name": "field4",
                "image_data": photo_of_agent_location_str,
                "upload_preset": "field4_upload_preset",
            },
            {
                "name": "field5",
                "image_data": signature_str,
                "upload_preset": "field5_upload_preset",
            },
        ]

        cloudinary.config(
            cloud_name="de2rwazpa",
            api_key="943491247866547",
            api_secret="Nkvx_M0-ETMfFxSHAflV3oISm0k",
        )

        image_urls = {}

        _image_urls = []

        for field in fields:
            field_name = field["name"]
            image_data = field["image_data"]
            upload_preset = field["upload_preset"]

            try:
                # Check if the upload preset exists
                cloudinary.api.upload_preset(upload_preset)
            except cloudinary.exceptions.NotFound:
                print(f"Upload preset '{upload_preset}' does not exist. Creating the upload preset...")
                upload_preset_params = {
                    "name": upload_preset,
                    "unsigned": False,
                }
                cloudinary.api.create_upload_preset(**upload_preset_params)

            image = Image.open(io.BytesIO(base64.decodebytes(bytes(image_data, "utf-8"))))

            # Check the image format
            image_format = image.format
            if image_format not in ["JPEG", "PNG"]:
                print(f"Invalid image format for {field_name}. Only JPEG and PNG are supported.")
                continue

            # Convert the image object to bytes
            image_bytes = io.BytesIO()
            image.save(image_bytes, format=image_format)
            image_bytes.seek(0)

            result = cloudinary.uploader.upload(image_bytes, upload_preset=upload_preset, format=image_format.lower())

            image_url = result["secure_url"]
            _image_urls.append(image_url)

            # Access the image URLs for each field
            for field_name, image_url in image_urls.items():
                print(f"{field_name}: {image_url}")

        date_of_birth = f"{resp.get('q13_date')['year']}-{resp.get('q13_date')['month']}-{resp.get('q13_date')['day']}"
        cls.objects.create(
            title=resp.get("q57_title57"),
            agent_head_shot_photograph=_image_urls[0],
            agent_last_name=resp.get("q62_agentSurname"),
            agent_first_name=resp.get("q6_agentFirst"),
            bank_verification_no=resp.get("q5_bankVerification"),
            mobile_number=resp.get("q12_mobileNumber", {}).get("full"),
            residential_address=resp.get("q11_residentialAddress"),
            business_address=resp.get("q9_businessAddress"),
            date_of_birth=date_of_birth,
            gender=resp.get("q15_gender"),
            email=resp.get("q14_email"),
            state_of_residence=resp.get("q19_stateOf"),
            lga=resp.get("q20_typeA20"),
            supervisor=resp.get("q70_supervisor"),
            language_spoken=resp.get("q41_typeA")[0],
            place_of_birth=resp.get("q36_placeOf"),
            marital_status=resp.get("q40_maritalStatus40"),
            photo_of_vlaid_id=_image_urls[1],
            photo_of_outlet=_image_urls[2],
            photo_of_agent_location=_image_urls[3],
            guarantor_name=resp.get("q25_guarantorsName"),
            nick_name=resp.get("q28_nickName"),
            phone_number=resp.get("q64_guarantorsEmployment64"),
            guarantor_gender=resp.get("q69_gender69"),
            bvn=resp.get("q67_bvn67"),
            home_address=resp.get("q30_homeAddress"),
            company_or_business_name=resp.get("q31_company"),
            company_or_business_address=resp.get("q63_company63"),
            guarantor_employment_type=resp.get("q65_employmentType65"),
            signature=_image_urls[4],
        )
        return True


class Supervisor(models.Model):
    SUPERVISOR_PERFORMANCE_STATUS = (
        (
            "PERFORMING",
            "PERFORMING",
        ),  # 7k>x<10k ---> 70% & NOT MORE THAN 10% are UNDER_PERFORMING
        (
            "INACTIVE",
            "INACTIVE",
        ),  # active days | inactive below 60% & <7k avg sales ---> IF REMITTANCE is less than 90% agents
        ("SUPER_PERFORMING", "SUPER_PERFORMING"),  # <30k
        (
            "HIGH_PERFORMING",
            "HIGH_PERFORMING",
        ),  # 20k|30k ----> 50% THEN SUPERVIOR is HIGH PERFORMING
        (
            "TOP_PERFORMING",
            "TOP_PERFORMING",
        ),  # 10k|20k -----> 70% FOR Top performing supervisor & NOT MORE THAN 5% are UNDER_PERFORMING
        (
            "UNDER_PERFORMING",
            "UNDER_PERFORMING",
        ),  # <7k ------> 50% or 50% PERFORMING for underperforming supervisor
    )  # New agents are PERFORMING by default --->

    # @classmethod
    # def user_model(cls):
    #     from django.contrib.auth import get_user_model
    #     User = get_user_model()
    #     return User

    first_name = models.CharField(max_length=300)
    last_name = models.CharField(max_length=300)
    full_name = models.CharField(max_length=300)
    phone = models.CharField(max_length=300)
    user_id = models.CharField(max_length=300)
    email = models.CharField(max_length=300, null=True, blank=True)
    user_uuid = models.CharField(max_length=500, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    address = models.CharField(max_length=300, default="YABA, Lagos Nigeria")
    location = models.ForeignKey("SupervisorLocation", blank=True, null=True, on_delete=models.CASCADE)
    performance_status = models.CharField(max_length=125, choices=SUPERVISOR_PERFORMANCE_STATUS, default="PERFORMING")
    vertical_lead = models.ForeignKey("LottoVerticalLead", on_delete=models.SET_NULL, null=True, blank=True)
    is_active = models.BooleanField(default=True)

    # current_user = models.ForeignKey("account.User", on_delete=models.CASCADE, null=True, blank=True, related_name="supervisor_profile")

    def __str__(self):
        return self.first_name + " " + self.last_name

    def save(self, *args, **kwargs):
        if not self.pk:
            self.full_name = self.first_name + " " + self.last_name

        return super(Supervisor, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "SUPERVISOR"
        verbose_name_plural = "SUPERVISORS"

    def can_withdraw_commission(self):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        agents_monthly_activities = LottoAgentMonthlyAnalytic.objects.filter(
            supervisor_phone=self.phone,
            created_at__year=current_datetime.year,
            created_at__month=current_datetime.month,
        )

        if len(agents_monthly_activities) == 0:
            return False, "No agent activity for this month"

        total_agents = len(agents_monthly_activities)

        active_agents = agents_monthly_activities.filter(status="ACTIVE")

        active_agents_count = len(active_agents)

        active_percentage = (active_agents_count / total_agents) * 100

        can_withdraw = active_percentage >= 85

        if can_withdraw is True:
            return True, "Can withdraw"

        return (
            False,
            f"you cannot withdraw commission at this time, your agent activity for this month is {active_percentage}%",
        )

    def get_duration(self):
        """Calculate the duration since the user was created."""
        if not self.created_at:
            return "Unknown"

        current_time = datetime.now()
        created_time = self.created_at.replace(tzinfo=None)  # Ensure timezone compatibility
        delta = relativedelta(current_time, created_time)

        duration_parts = []
        if delta.years:
            duration_parts.append(f"{delta.years} year{'s' if delta.years > 1 else ''}")
        if delta.months:
            duration_parts.append(f"{delta.months} month{'s' if delta.months > 1 else ''}")

        return ", ".join(duration_parts) if duration_parts else "Less than a month"


class SupervisorWallet(models.Model):
    supervisor = models.ForeignKey("Supervisor", on_delete=models.CASCADE)
    commission_balance = models.FloatField(default=0)
    rewarded_commission_balance = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO VERTICAL LEAD WALLET"
        verbose_name_plural = "LOTTO VERTICAL LEAD WALLETS"

    @classmethod
    def fund_commission_wallet(cls, supervisor_phone_number, amount, transaction_type, lottery_type=None):

        try:
            user_instance = Supervisor.objects.get(phone=supervisor_phone_number)
        except Supervisor.DoesNotExist:
            return

        wallet_instance, _ = cls.objects.get_or_create(supervisor=user_instance)

        before = wallet_instance.commission_balance
        after = before + amount

        wallet_instance.commission_balance = round(after, 2)
        wallet_instance.save()
        wallet_instance.refresh_from_db()

        SupervisorWalletTransaction.objects.create(
            wallet=wallet_instance,
            amount=amount,
            balance_before=before,
            balance_after=after,
            transactton_type=transaction_type,
            transaction_from="COMMISSION",
        )

        if transaction_type == "CREDIT":
            Wallet.fund_wallet(
                amount=amount, wallet_type="COMMISSION", game_type=lottery_type, is_supervisor_commission=True
            )

    @classmethod
    def deduct_commission_wallet(cls, supervisor_phone_number, amount):
        try:
            user_instance = Supervisor.objects.get(phone=supervisor_phone_number)
        except Supervisor.DoesNotExist:
            return

        wallet_instance, _ = cls.objects.get_or_create(supervisor=user_instance)

        before = wallet_instance.commission_balance

        if wallet_instance.commission_balance < amount:
            return

        after = before - amount

        wallet_instance.commission_balance = after
        wallet_instance.save()
        wallet_instance.refresh_from_db()

        SupervisorWalletTransaction.objects.create(
            wallet=wallet_instance,
            amount=amount,
            balance_before=before,
            balance_after=after,
            transactton_type="DEBIT",
            transaction_from="COMMISSION_REWARDED",
        )


class SupervisorWalletTransaction(models.Model):
    TRANSACTION_TYPE = (
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
        ("REVERSAL", "REVERSAL"),
    )

    TRANSACTION_FROM = (
        ("COMMISSION", "COMMISSION"),
        ("COMMISSION_REWARDED", "COMMISSION_REWARDED"),
    )

    wallet = models.ForeignKey(SupervisorWallet, on_delete=models.CASCADE)
    amount = models.FloatField(default=0)
    balance_before = models.FloatField(default=0)
    balance_after = models.FloatField(default=0)
    transactton_type = models.CharField(max_length=25, choices=TRANSACTION_TYPE)
    transaction_from = models.CharField(max_length=25, choices=TRANSACTION_FROM)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO VERTICAL LEAD WALLET TRANSACTION"
        verbose_name_plural = "LOTTO VERTICAL LEAD WALLET TRANSACTIONS"


class RawFundingData(models.Model):
    reference = models.CharField(max_length=300, blank=True, null=True)
    payload = models.JSONField()
    source = models.CharField(max_length=150, null=True, blank=True)
    authorized = models.BooleanField(default=False)
    lotto_response = models.JSONField(null=True, blank=True)
    verification_response_payload = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RAW FUNDING DATA"
        verbose_name_plural = "RAW FUNDING DATA"


class SupervisorLocation(models.Model):
    name = models.CharField(max_length=500)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "SUPERVISOR LOCATION"
        verbose_name_plural = "SUPERVISOR LOCATIONS"


class SalesRepAgencyDumpData(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class SupervisorAgencyDumpData(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SUPERVISOR AGENCY DUMP DATA"
        verbose_name_plural = "SUPERVISOR AGENCY DUMP DATA"


class SuperAgentAgencyDumpData(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    data = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SUPER AGENT AGENCY DUMP DATA"
        verbose_name_plural = "SUPER AGENT AGENCY DUMP DATA"


class RemunerationChargeRemittance(models.Model):
    STATUS = (
        ("PENDING", "PENDING"),
        ("FAILED", "FAILED"),
        ("SUCCESSFUL", "SUCCESSFUL"),
    )

    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    amount = models.FloatField(default=0)
    reference = models.CharField(max_length=300, unique=True)
    payload = models.TextField(null=True, blank=True)
    charges_inquiry_response = models.TextField(null=True, blank=True)
    response_payload = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=300, default="PENDING", choices=STATUS)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "REMUNERATION CHARGE REMITTANCE"
        verbose_name_plural = "REMUNERATION CHARGE REMITTANCES"

    @classmethod
    def handle_remittance_charge_remuneration(cls, agent_id, amount):
        from pos_app.pos_helpers import PosAgentHelper
        from wallet_app.models import DebitCreditRecord, UserWallet

        agent_remittance = LottoAgentRemittanceTable.objects.filter(agent__id=agent_id, remitted=False).last()

        if agent_remittance:
            pos_agent_helper = PosAgentHelper(agent_remittance.agent, amount, 0)
            reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

            amount_charges_response = pos_agent_helper.check_deposit_charge_on_agency_banking()

            agent_cash_back_model_instance = cls.objects.create(
                agent=agent_remittance.agent,
                amount=amount,
                reference=reference,
                charges_inquiry_response=amount_charges_response,
            )

            if not isinstance(amount_charges_response, dict):
                agent_cash_back_model_instance.status = "FAILED"
                agent_cash_back_model_instance.save()
                return False
            else:
                get_deposit_fee = amount_charges_response.get("charge", 0)

                if get_deposit_fee < 0:
                    agent_cash_back_model_instance.status = "FAILED"
                    agent_cash_back_model_instance.save()
                    return False
                else:
                    debit_credit_record = DebitCreditRecord.create_record(
                        phone_number=agent_remittance.agent.phone,
                        amount=get_deposit_fee,
                        channel="POS/MOBILE",
                        reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                        transaction_type="CREDIT",
                    )

                    transaction_payload = {"transaction_from": "REMUNERATION_CHARGE_ON_REMITTANCE"}

                    UserWallet.fund_wallet(
                        user=agent_remittance.agent,
                        amount=get_deposit_fee,
                        channel="POS",
                        transaction_id=debit_credit_record.reference,
                        user_wallet_type="REMUNERATION_WALLET",
                        **transaction_payload,
                    )

                    agent_wallet = AgentWallet.objects.filter(agent__id=agent_remittance.agent.id).last()
                    if agent_wallet:
                        if agent_wallet.remuneration_bal <= 0:
                            agent_cash_back_model_instance.status = "FAILED"
                            agent_cash_back_model_instance.save()
                            return False

                        else:
                            new_amount = agent_wallet.remuneration_bal

                            debit_credit_record = DebitCreditRecord.create_record(
                                phone_number=agent_remittance.agent.phone,
                                amount=new_amount,
                                channel="POS/MOBILE",
                                reference=agent_cash_back_model_instance.reference,
                                transaction_type="DEBIT",
                            )

                            transaction_payload = {"transaction_from": "REMUNERATION_CHARGE_ON_REMITTANCE"}

                            UserWallet.deduct_wallet(
                                user=agent_remittance.agent,
                                amount=new_amount,
                                channel="POS",
                                transaction_id=debit_credit_record.reference,
                                user_wallet_type="REMUNERATION_WALLET",
                                **transaction_payload,
                            )

                            payload = {
                                "from_wallet_type": "COLLECTION",
                                "to_wallet_type": "COLLECTION",
                                "data": [
                                    {
                                        "buddy_phone_number": agent_remittance.agent.phone,
                                        "amount": new_amount,
                                        "narration": "REMUNERATION_CHARGE_ON_REMITTANCE",
                                        "is_beneficiary": "False",
                                        "save_beneficiary": "True",
                                        "remove_beneficiary": "False",
                                        "is_recurring": "False",
                                        "customer_reference": agent_cash_back_model_instance.reference,
                                    }
                                ],
                            }

                            agent_cash_back_model_instance.payload = payload
                            agent_cash_back_model_instance.save()

                            response = pos_agent_helper.agency_buddy_transfer(**payload)

                            agent_cash_back_model_instance.response_payload = response
                            agent_cash_back_model_instance.save()

                            if isinstance(response, dict):
                                if response.get("message") != "success":
                                    agent_cash_back_model_instance.status = "FAILED"
                                    agent_cash_back_model_instance.save()
                                    return False

                                else:
                                    agent_cash_back_model_instance.status = "SUCCESSFUL"
                                    agent_cash_back_model_instance.save()
                                    return True

                            else:
                                agent_cash_back_model_instance.status = "FAILED"
                                agent_cash_back_model_instance.save()
                                return False

                    else:
                        agent_cash_back_model_instance.status = "FAILED"
                        agent_cash_back_model_instance.save()
                        return False


class WinwiseEmployeeSalary(models.Model):
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    agent_email = models.CharField(max_length=300, null=True, blank=True)
    agent_phone = models.CharField(max_length=300, null=True, blank=True)
    total_commission = models.FloatField(default=0)
    amount_to_be_paid = models.FloatField(default=0)
    percenatage_to_be_paid = models.CharField(max_length=300)
    total_sales = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "WINWISE EMPLOYEE SALARY"
        verbose_name_plural = "WINWISE EMPLOYEE SALARIES"

    def __str__(self):
        return self.agent.full_name

    def save(self, *args, **kwargs):
        self.total_commission = round(self.total_commission, 2)
        self.amount_to_be_paid = round(self.amount_to_be_paid, 2)
        self.total_sales = round(self.total_sales, 2)

        return super(WinwiseEmployeeSalary, self).save(*args, **kwargs)

    """
    THE SALARY IS BASE ON TICKET SALES,
    AND THEY GET REWARD OF 15 PERCENT OF EACH TICKET SALES.
    BASICALLY, THEIR SLARY ARE BASE ON COMMISSION WHICH IS 15 OF EACH TICKET SALES.
    AND THE COMPANY WANTS TO REWARD 9 PERCENT OF THE COMMISSION AS SALARY TO THE EMPLOYEE.

    TO GET THE ACTUAL PERENT THEY'LL GET FROM THE COMMISSION, THE SYSTEM DIVIDE 9 BY 15 TO ARRIVE AT 0.6.

    THAT MEANS, THE EMPLOYEE WILL GET 60 PERCENT OF THE COMMISSION AS SALARY.
    """

    @classmethod
    def update_salary_record(
        cls,
        agent_id,
        commission_value,
        amount_to_be_paid,
        perc,
        sales_value,
    ):
        get_current_year = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date().year
        get_current_month = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)).date().month

        agent = Agent.objects.get(id=agent_id)

        try:
            get_agent_salary = cls.objects.get(
                Q(agent__id=agent_id),
                Q(created_at__year=get_current_year) | Q(created_at__month=get_current_month),
            )

        except cls.DoesNotExist:
            get_agent_salary = cls.objects.create(
                agent=agent,
                total_commission=round(commission_value, 2),
                amount_to_be_paid=round(amount_to_be_paid, 2),
                percenatage_to_be_paid=perc,
                agent_email=agent.email,
                agent_phone=agent.phone,
                total_sales=round(sales_value, 2),
            )
            return get_agent_salary

        else:
            get_agent_salary.total_commission += round(commission_value, 2)
            get_agent_salary.amount_to_be_paid += round(amount_to_be_paid, 2)
            get_agent_salary.percenatage_to_be_paid = perc
            get_agent_salary.total_sales += round(sales_value, 2)
            get_agent_salary.save()

            return get_agent_salary


class AgentNoPreFunding(models.Model):
    phone_number = models.CharField(max_length=300)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class LottoSuperAgentCommissionAllocation(models.Model):
    super_agent = models.ForeignKey(LottoSuperAgents, on_delete=models.PROTECT)
    type_of_super_agent = models.CharField(
        max_length=300, choices=enums.TypeOfSuperAgent.choices, default=enums.TypeOfSuperAgent.SUPER_AGENT
    )
    super_agent_phone_number = models.CharField(max_length=300)
    agent_monthtly_sales = models.FloatField(default=0)
    company_commission = models.FloatField(default=0)
    company_commission_percentage = models.FloatField(default=0)
    total_draw_games = models.IntegerField(default=0)
    total_draw_games_value = models.FloatField(default=0)
    draw_game_commission = models.FloatField(default=0)
    draw_game_commission_percentage = models.FloatField(default=0)
    total_instant_games = models.IntegerField(default=0)
    total_instant_games_value = models.FloatField(default=0)
    instant_game_commission = models.FloatField(default=0)
    instant_game_commission_percentage = models.FloatField(default=0)
    agent_commission = models.FloatField(default=0)
    agent_commission_percentage = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SUPER AGENT COMMISSION ALLOCATION"
        verbose_name_plural = "SUPER AGENT COMMISSION ALLOCATIONS"


class PromotionGames(models.Model):
    GAME_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("QUIKA", "QUIKA"),
        ("BANKER", "BANKER"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW"),
    ]
    game_type = models.CharField(max_length=300, choices=GAME_TYPE)
    image_url = models.CharField(max_length=300, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "PROMOTION GAME"
        verbose_name_plural = "PROMOTION GAMES"

    def __str__(self):
        return self.game_type


class LottoAgentSalesActivity(models.Model):
    agent_name = models.CharField(max_length=300)
    agent_email = models.CharField(max_length=300)
    agent_phone_number = models.CharField(max_length=300)
    agent_terminal_id = models.CharField(max_length=300)
    sales_for_the_week = models.FloatField(default=0)
    average_sales = models.FloatField(default=0)
    winnings = models.FloatField(default=0)
    week_date_range = models.CharField(max_length=300)
    activity_status = models.CharField(max_length=300, choices=enums.AgentActivityStatus.choices)
    wave = models.CharField(max_length=125, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO AGENT SALES ACTIVITY"
        verbose_name_plural = "LOTTO AGENT SALES ACTIVITIES"

    def __str__(self):
        return f"{self.agent_name} - {self.week_date_range}"

    @classmethod
    def create_agent_sales_activity(
        cls, agent_name, agent_email, agent_phone_number, terminal_id, sales=0, winnings=0, agent_instance=None
    ):
        from pos_app.pos_helpers import get_week_info

        # target is

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        # TODAYS_DATE = TODAY.date()

        week_num, week_range = get_week_info(TODAY)

        week_date_range = f"Week {week_num}: {week_range}"

        # DAILY_SALES_TARGET = 30000

        if sales > 0:
            DailyInactiveAgents.objects.filter(
                agent_phone_number=agent_phone_number, created_at__date=TODAY.date()
            ).delete()

        try:
            agent_sales_activity = cls.objects.get(
                agent_phone_number=agent_phone_number, week_date_range=week_date_range
            )
            agent_sales_activity.sales_for_the_week += sales
            agent_sales_activity.winnings += winnings
            agent_sales_activity.average_sales = round(agent_sales_activity.sales_for_the_week / 7, 2)
        except cls.DoesNotExist:
            agent_sales_activity = cls.objects.create(
                agent_name=agent_name,
                agent_email=agent_email,
                agent_phone_number=agent_phone_number,
                sales_for_the_week=sales,
                winnings=winnings,
                week_date_range=week_date_range,
                agent_terminal_id=terminal_id,
            )
            agent_sales_activity.average_sales = round(sales / 7, 2)

        except cls.MultipleObjectsReturned:

            cls.objects.filter(agent_phone_number=agent_phone_number, week_date_range=week_date_range).delete()

            return cls.create_agent_sales_activity(
                agent_name=agent_name,
                agent_email=agent_email,
                agent_phone_number=agent_phone_number,
                terminal_id=terminal_id,
                sales=sales,
                winnings=winnings,
            )

        if agent_sales_activity.average_sales < 5000:
            agent_sales_activity.activity_status = enums.AgentActivityStatus.INACTIVE

        elif agent_sales_activity.average_sales > 5000 and agent_sales_activity.average_sales <= 10000:
            agent_sales_activity.activity_status = enums.AgentActivityStatus.UNDER_PERFORMING

        elif agent_sales_activity.average_sales > 10000 and agent_sales_activity.average_sales < 30000:
            agent_sales_activity.activity_status = enums.AgentActivityStatus.PARTIALLY_ACTIVE

        elif agent_sales_activity.average_sales >= 30000:
            agent_sales_activity.activity_status = enums.AgentActivityStatus.ACTIVE

        if agent_instance != None:
            agent_created_date = agent_instance.created_date

            agent_sales_activity.wave = agent_instance.get_wave()

            days_existing = (TODAY - agent_created_date).days

            if days_existing <= 7:
                agent_sales_activity.activity_status = enums.AgentActivityStatus.ACTIVE

        agent_sales_activity.save()

        if agent_instance != None:
            LottoAgentDailySalesActivity.create_record(
                agent_name=agent_name,
                agent_email=agent_email,
                agent_phone_number=agent_phone_number,
                sales=sales,
                winnings=winnings,
                terminal_id=terminal_id,
                wave=agent_instance.get_wave(),
            )
        else:
            LottoAgentDailySalesActivity.create_record(
                agent_name=agent_name,
                agent_email=agent_email,
                agent_phone_number=agent_phone_number,
                sales=sales,
                winnings=winnings,
                terminal_id=terminal_id,
            )

    @classmethod
    def update_agent_sales_activity(
        cls, agent_name, agent_email, agent_phone_number, terminal_id, sales=0, winnings=0, agent_instance=None
    ):
        from pos_app.pos_helpers import get_week_info

        # target is

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        # TODAYS_DATE = TODAY.date()

        week_num, week_range = get_week_info(TODAY)

        week_date_range = f"Week {week_num}: {week_range}"

        # DAILY_SALES_TARGET = 30000

        if sales > 0:
            DailyInactiveAgents.objects.filter(
                agent_phone_number=agent_phone_number, created_at__date=TODAY.date()
            ).delete()

        try:
            agent_sales_activity = cls.objects.get(
                agent_phone_number=agent_phone_number, week_date_range=week_date_range
            )
            agent_sales_activity.sales_for_the_week = sales
            agent_sales_activity.winnings = winnings
            agent_sales_activity.average_sales = round(agent_sales_activity.sales_for_the_week / 7, 2)
        except cls.DoesNotExist:
            agent_sales_activity = cls.objects.create(
                agent_name=agent_name,
                agent_email=agent_email,
                agent_phone_number=agent_phone_number,
                sales_for_the_week=sales,
                winnings=winnings,
                week_date_range=week_date_range,
                agent_terminal_id=terminal_id,
            )
            agent_sales_activity.average_sales = round(sales / 7, 2)

        except cls.MultipleObjectsReturned:

            cls.objects.filter(agent_phone_number=agent_phone_number, week_date_range=week_date_range).delete()

            return cls.create_agent_sales_activity(
                agent_name=agent_name,
                agent_email=agent_email,
                agent_phone_number=agent_phone_number,
                terminal_id=terminal_id,
                sales=sales,
                winnings=winnings,
            )

        if agent_sales_activity.average_sales < 5000:
            agent_sales_activity.activity_status = enums.AgentActivityStatus.INACTIVE

        elif agent_sales_activity.average_sales > 5000 and agent_sales_activity.average_sales <= 10000:
            agent_sales_activity.activity_status = enums.AgentActivityStatus.UNDER_PERFORMING

        elif agent_sales_activity.average_sales > 10000 and agent_sales_activity.average_sales < 30000:
            agent_sales_activity.activity_status = enums.AgentActivityStatus.PARTIALLY_ACTIVE

        elif agent_sales_activity.average_sales >= 30000:
            agent_sales_activity.activity_status = enums.AgentActivityStatus.ACTIVE

        if agent_instance != None:
            agent_created_date = agent_instance.created_date

            days_existing = (TODAY - agent_created_date).days

            if days_existing <= 7:
                agent_sales_activity.activity_status = enums.AgentActivityStatus.ACTIVE

        agent_sales_activity.save()

        LottoAgentDailySalesActivity.update_record(
            agent_name=agent_name,
            agent_email=agent_email,
            agent_phone_number=agent_phone_number,
            sales=sales,
            winnings=winnings,
            terminal_id=terminal_id,
        )


class LottoAgentDailySalesActivity(models.Model):
    agent_name = models.CharField(max_length=300)
    agent_email = models.CharField(max_length=300)
    agent_phone_number = models.CharField(max_length=300)
    agent_terminal_id = models.CharField(max_length=300)
    sales = models.FloatField(default=0)
    winnings = models.FloatField(default=0)
    wave = models.CharField(max_length=125, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO AGENT DAILY SALES ACTIVITY"
        verbose_name_plural = "LOTTO AGENT DAILY SALES ACTIVITIES"

    @classmethod
    def create_record(cls, agent_name, agent_email, agent_phone_number, terminal_id, sales=0, winnings=0, wave=None):

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        # TODAYS_DATE = TODAY.date()

        try:
            agent_sales_activity = cls.objects.get(
                agent_phone_number=agent_phone_number, created_at__date=TODAY.date()
            )
            agent_sales_activity.sales += sales
            agent_sales_activity.winnings += winnings

        except cls.DoesNotExist:
            agent_sales_activity = cls.objects.create(
                agent_name=agent_name,
                agent_email=agent_email,
                agent_phone_number=agent_phone_number,
                sales=sales,
                winnings=winnings,
                agent_terminal_id=terminal_id,
                wave=wave,
            )

        except cls.MultipleObjectsReturned:
            agent_sales_activity = cls.objects.filter(
                agent_phone_number=agent_phone_number, created_at__date=TODAY.date()
            ).last()
            agent_sales_activity.sales += sales
            agent_sales_activity.winnings += winnings
            agent_sales_activity.agent_terminal_id = terminal_id

        agent_sales_activity.save()

    @classmethod
    def update_record(cls, agent_name, agent_email, agent_phone_number, terminal_id, sales=0, winnings=0):

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        # TODAYS_DATE = TODAY.date()

        try:
            agent_sales_activity = cls.objects.get(
                agent_phone_number=agent_phone_number, created_at__date=TODAY.date()
            )
            agent_sales_activity.sales = sales
            agent_sales_activity.winnings = winnings

        except cls.DoesNotExist:
            agent_sales_activity = cls.objects.create(
                agent_name=agent_name,
                agent_email=agent_email,
                agent_phone_number=agent_phone_number,
                sales=sales,
                winnings=winnings,
                agent_terminal_id=terminal_id,
            )

        except cls.MultipleObjectsReturned:
            agent_sales_activity = cls.objects.filter(
                agent_phone_number=agent_phone_number, created_at__date=TODAY.date()
            ).last()
            agent_sales_activity.sales = sales
            agent_sales_activity.winnings = winnings
            agent_sales_activity.agent_terminal_id = terminal_id

        agent_sales_activity.save()


class DailyInactiveAgents(models.Model):
    agent_name = models.CharField(max_length=300)
    agent_email = models.CharField(max_length=300)
    agent_phone_number = models.CharField(max_length=300)
    agent_terminal_id = models.CharField(max_length=300)
    wave = models.CharField(max_length=125, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "DAILY INACTIVE AGENTS"
        verbose_name_plural = "DAILY INACTIVE AGENTS"


class GamesDailyActivities(models.Model):
    LOTTERY_TYPE_CHOICES = [
        ("TOTAL", "TOTAL"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("BANKER", "BANKER"),
        ("QUIKA", "QUIKA"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("NIGERIA_LOTTO", "NIGERIA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW"),
    ]

    game_type = models.CharField(max_length=300, choices=LOTTERY_TYPE_CHOICES)
    sales = models.FloatField(default=0)
    winnings = models.FloatField(default=0)
    lotto_agent_sales = models.FloatField(default=0)
    lotto_agent_winnings = models.FloatField(default=0)
    other_sales = models.FloatField(default=0)
    other_winnings = models.FloatField(default=0)
    commission = models.FloatField(default=0)
    rtp = models.FloatField(default=0)
    rto = models.FloatField(default=0)
    excess_winnings = models.FloatField(default=0)
    daily_net = models.FloatField(default=0)
    running_balance = models.FloatField(blank=True, null=True)
    percentage_of_winnings_to_sales = models.FloatField(default=0)
    percentage_of_winnings_to_sales_for_lotto_agents = models.FloatField(default=0)
    percentage_of_winnings_to_sales_for_other_agents = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GAMES DAILY ACTIVITIES"
        verbose_name_plural = "GAMES DAILY ACTIVITIES"

    @classmethod
    def create_record(
        cls,
        game_type,
        sales=0,
        winnings=0,
        commission=0,
        rtp=0,
        rto=0,
        from_lotto_agent=True,
        game_id=None,
        agent_phone_number=None,
    ):

        return None

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        # check if there's game id
        if game_id != None:
            general_retail_lotto_games_instance = GeneralRetailLottoGames.objects.filter(game_play_id=game_id).last()
            if general_retail_lotto_games_instance:
                TODAY = general_retail_lotto_games_instance.created_at

        # create total instance

        try:
            total_instance = cls.objects.get(game_type="TOTAL", created_at__date=TODAY.date())
        except:
            total_instance = cls.objects.create(game_type="TOTAL")

        try:
            instance = cls.objects.get(game_type=game_type, created_at__date=TODAY.date())
        except:
            instance = cls.objects.create(game_type=game_type)

        total_activities_qs = cls.objects.filter(created_at__date=TODAY.date()).filter(~Q(game_type="TOTAL"))

        # print("LEN OF TOTAL QS", len(total_activities_qs))

        try:
            GameAnalytics.add_or_create_record(game_type=game_type, sales_amount=sales, winning_amount=winnings)
        except:
            pass

        instance.sales += sales
        instance.winnings += winnings
        instance.commission += commission
        instance.rtp += rtp
        instance.rtp = round(instance.rtp, 2)
        instance.rto += rto
        instance.rto = round(instance.rto, 2)
        if instance.winnings > 0:
            instance.excess_winnings = round(instance.rtp - instance.winnings, 2)
        instance.daily_net = round(instance.sales - instance.winnings - instance.commission, 2)

        # calculate the percentage of winnings to sales
        if instance.sales > 0:
            percentage_of_winnings_to_sales = (instance.winnings / instance.sales) * 100
        else:
            percentage_of_winnings_to_sales = 0

        instance.percentage_of_winnings_to_sales = round(percentage_of_winnings_to_sales, 2)

        try:
            ReturnToPlayerAndReturnToOwnerAnalytics.add_or_create_record(game_type=game_type, rtp=rtp, rto=rto)
        except:
            pass

        if from_lotto_agent is True:
            instance.lotto_agent_sales += sales
            instance.lotto_agent_winnings += winnings

            # calculate the percentage of winnings to sales
            if instance.lotto_agent_sales > 0:
                percentage_of_winnings_to_sales = (instance.lotto_agent_winnings / instance.lotto_agent_sales) * 100
            else:
                percentage_of_winnings_to_sales = 0

            instance.percentage_of_winnings_to_sales_for_lotto_agents = round(percentage_of_winnings_to_sales, 2)

        else:
            instance.other_sales += sales
            instance.other_winnings += winnings

            # calculate the percentage of winnings to sales
            if instance.other_sales > 0:
                percentage_of_winnings_to_sales = (instance.other_winnings / instance.other_sales) * 100
            else:
                percentage_of_winnings_to_sales = 0

            instance.percentage_of_winnings_to_sales_for_other_agents = round(percentage_of_winnings_to_sales, 2)

        instance.save()
        instance.refresh_from_db()

        total_sales = total_activities_qs.aggregate(Sum("sales")).get("sales__sum", 0)
        total_winnings = total_activities_qs.aggregate(Sum("winnings")).get("winnings__sum", 0)
        total_lotto_agent_sales = total_activities_qs.aggregate(Sum("lotto_agent_sales")).get(
            "lotto_agent_sales__sum", 0
        )
        total_lotto_agent_winnings = total_activities_qs.aggregate(Sum("lotto_agent_winnings")).get(
            "lotto_agent_winnings__sum", 0
        )
        total_other_sales = total_activities_qs.aggregate(Sum("other_sales")).get("other_sales__sum", 0)
        total_other_winnings = total_activities_qs.aggregate(Sum("other_winnings")).get("other_winnings__sum", 0)
        total_commission = total_activities_qs.aggregate(Sum("commission")).get("commission__sum", 0)
        total_rtp = total_activities_qs.aggregate(Sum("rtp")).get("rtp__sum", 0)
        total_rto = total_activities_qs.aggregate(Sum("rto")).get("rto__sum", 0)

        if total_sales > 0:
            total_percentage_to_winnings = (total_winnings / total_sales) * 100
            total_percentage_to_winnings = round(total_percentage_to_winnings, 2)
        else:
            total_percentage_to_winnings = 0

        if total_lotto_agent_sales > 0:
            total_lotto_agent_sales_percentage_to_winnings = (
                total_lotto_agent_winnings / total_lotto_agent_sales
            ) * 100
            total_lotto_agent_sales_percentage_to_winnings = round(total_lotto_agent_sales_percentage_to_winnings, 2)
        else:
            total_lotto_agent_sales_percentage_to_winnings = 0

        if total_other_sales > 0:
            total_other_sales_percentage_to_winnings = (total_other_winnings / total_other_sales) * 100
            total_other_sales_percentage_to_winnings = round(total_other_sales_percentage_to_winnings, 2)
        else:
            total_other_sales_percentage_to_winnings = 0

        total_instance.sales = round(total_sales, 2)
        total_instance.winnings = round(total_winnings, 2)
        total_instance.lotto_agent_sales = round(total_lotto_agent_sales, 2)
        total_instance.lotto_agent_winnings = round(total_lotto_agent_winnings, 2)
        total_instance.other_sales = round(total_other_sales, 2)
        total_instance.other_winnings = round(total_other_winnings, 2)
        total_instance.commission = round(total_commission, 2)
        total_instance.rtp = round(total_rtp, 2)
        total_instance.rto = round(total_rto, 2)
        total_instance.percentage_of_winnings_to_sales = total_percentage_to_winnings
        total_instance.percentage_of_winnings_to_sales_for_lotto_agents = (
            total_lotto_agent_sales_percentage_to_winnings
        )
        total_instance.percentage_of_winnings_to_sales_for_other_agents = total_other_sales_percentage_to_winnings
        if total_instance.winnings > 0:
            total_instance.excess_winnings = round(total_instance.rtp - total_instance.winnings, 2)
        total_instance.daily_net = round(total_instance.sales - total_instance.winnings - total_instance.commission, 2)
        total_instance.save()

        total_instance.refresh_from_db()

        if agent_phone_number is not None:
            try:
                agent = Agent.objects.get(phone=agent_phone_number, terminal_id__isnull=False)
            except:
                return
            LottoAgentSalesActivity.create_agent_sales_activity(
                agent_name=f"{agent.first_name} {agent.last_name}",
                agent_email=agent.email,
                agent_phone_number=agent.phone,
                terminal_id=agent.terminal_id,
                sales=sales,
                winnings=winnings,
                agent_instance=agent,
            )

    @classmethod
    def update_running_balance(cls, game_type, running_balance=0):
        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        try:
            cls.objects.get(game_type="TOTAL", created_at__date=TODAY.date())
        except:
            cls.objects.create(game_type="TOTAL")

        try:
            instance = cls.objects.get(game_type=game_type, created_at__date=TODAY.date())
        except:
            instance = cls.objects.create(game_type=game_type)

        instance.running_balance = running_balance
        instance.save()
        instance.refresh_from_db()


class LottoAgentFailedTransactionLog(models.Model):
    agent_name = models.CharField(max_length=300)
    agent_email = models.CharField(max_length=300)
    agent_phone_number = models.CharField(max_length=300)
    payload = models.TextField(blank=True, null=True)
    response_payload = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO AGENT FAILED TRANSACTION"
        verbose_name_plural = "LOTTO AGENT FAILED TRANSACTIONS"


class TerminalIdUnAssignmentRequestLogs(models.Model):
    agent_name = models.CharField(max_length=300, null=True, blank=True)
    agent_email = models.CharField(max_length=300, null=True, blank=True)
    agent_phone_number = models.CharField(max_length=300, null=True, blank=True)
    payload = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TERMINAL ID UNASSIGNMENT REQUEST LOG"
        verbose_name_plural = "TERMINAL ID UNASSIGNMENT REQUEST LOGS"


import random


class GeneralLotteryType(models.TextChoices):
    NAP2 = "NAP2", _("NAP2")
    NAP3 = "NAP3", _("NAP3")
    NAP4 = "NAP4", _("NAP4")
    NAP5 = "NAP5", _("NAP5")
    PERM2 = "PERM2", _("PERM2")
    PERM3 = "PERM3", _("PERM3")
    PERM4 = "PERM4", _("PERM4")
    PERM5 = "PERM5", _("PERM5")
    BANKER = "1BANKER", _("1BANKER")
    AGAINST = "AGAINST", _("AGAINST")


class GeneralRetailLottoGames(models.Model):
    DRAWN_FOR_CHOICES = [
        ("GLOBAL", "GLOBAL"),
        ("LOCAL", "LOCAL"),
    ]

    LOTTO_GAMES_TYPES = [
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("NIGERIA_LOTTO", "NIGERIA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("QUIKA", "QUIKA"),
        ("BANKER", "BANKER"),
    ]

    AGENT_TYPE = [
        ("AGENT", "AGENT"),
        ("MERCHANT", "MERCHANT"),
        ("LOTTO_AGENT", "LOTTO_AGENT"),
        ("PERSONAL", "PERSONAL"),
        ("LIBERTY_RETAIL", "LIBERTY_RETAIL"),
    ]

    agent_phone_number = models.CharField(max_length=20, blank=True, null=True)
    agent_name = models.CharField(max_length=100, blank=True, null=True)
    agent_email = models.CharField(max_length=100, blank=True, null=True)
    batch_uuid = models.CharField(max_length=300, blank=True, null=True)
    game_play_id = models.CharField(max_length=100)
    game_pin = models.CharField(max_length=100)
    lucky_number = models.CharField(max_length=100)
    bottom_ticket = models.CharField(max_length=100, blank=True, null=True)
    purchase_amount = models.FloatField(default=0)
    potential_winnings = models.FloatField(default=0)
    multiplier = models.CharField(max_length=50, blank=True, null=True)
    winning_scenarios = models.TextField(blank=True, null=True)
    rtp = models.FloatField(default=0)
    rto = models.FloatField(default=0)
    commission_percentage = models.FloatField(default=0)
    commission = models.FloatField(default=0)
    pool = models.FloatField(default=0)
    type_of_agent = models.CharField(max_length=50, choices=AGENT_TYPE)
    ticket_type = models.CharField(max_length=50, choices=GeneralLotteryType.choices, blank=True, null=True)
    lotto_game_type = models.CharField(max_length=50, choices=LOTTO_GAMES_TYPES)
    paid = models.BooleanField(default=False)
    number_of_ticket = models.IntegerField(default=1)
    won = models.BooleanField(default=False)
    exempted_from_draw = models.BooleanField(default=False)
    drawn_for = models.CharField(max_length=25, choices=DRAWN_FOR_CHOICES, default="GLOBAL")
    double_chance = models.BooleanField(default=False)
    lotto_db_id = models.CharField(max_length=300, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GENERAL RETAIL LOTTO GAME"
        verbose_name_plural = "GENERAL RETAIL LOTTO GAMES"

    @classmethod
    def create_record(
        cls,
        agent_phone_number,
        agent_name,
        agent_email,
        batch_uuid,
        game_play_id,
        game_pin,
        lucky_number,
        purchase_amount,
        potential_winnings,
        type_of_agent,
        lotto_game_type,
        lotto_db_id,
        paid=False,
        number_of_ticket=1,
        exempted_from_draw=False,
        multiplier=None,
        drawn_for="GLOBAL",
        double_chance=False,
        bottom_ticket=None,
        winning_scenarios=None,
        rtp=0,
        rto=0,
        commission_percentage=0,
        commission=0,
        ticket_type=None,
        pool=0,
    ):
        cls.objects.create(
            agent_phone_number=agent_phone_number,
            agent_name=agent_name,
            agent_email=agent_email,
            batch_uuid=batch_uuid,
            game_play_id=game_play_id,
            game_pin=game_pin,
            lucky_number=lucky_number,
            purchase_amount=purchase_amount,
            potential_winnings=potential_winnings,
            type_of_agent=type_of_agent,
            ticket_type=ticket_type,
            lotto_game_type=lotto_game_type,
            paid=paid,
            number_of_ticket=number_of_ticket,
            won=False,
            exempted_from_draw=exempted_from_draw,
            drawn_for=drawn_for,
            double_chance=double_chance,
            bottom_ticket=bottom_ticket,
            multiplier=multiplier,
            winning_scenarios=winning_scenarios,
            rtp=rtp,
            rto=rto,
            commission_percentage=commission_percentage,
            commission=commission,
            pool=pool,
            lotto_db_id=lotto_db_id,
        )


class AgencyBankingToken(models.Model):
    ACCOUNTS = [
        ("VFD_DEFAULT_ACCOUNT", "VFD_DEFAULT_ACCOUNT"),
        ("NON_RETAIL_WALLET", "NON_RETAIL_WALLET"),
        ("RETAIL_RTP_WALLET", "RETAIL_RTP_WALLET"),
        ("GHANA_RTP_WALLET", "GHANA_RTP_WALLET"),
        ("RTO_WALLET", "RTO_WALLET"),
        ("RETAIL_COMMISSION_WALLET", "RETAIL_COMMISSION_WALLET"),
        ("PENDING_LATE_WITHDRAWAL_WALLET", "PENDING_LATE_WITHDRAWAL_WALLET"),
        ("EXCESS_WALLET", "EXCESS_WALLET"),
        ("PRE_FUNDING_WALLET", "PRE_FUNDING_WALLET"),
    ]

    token = models.CharField(max_length=5000)
    account = models.CharField(max_length=300, choices=ACCOUNTS, default="VFD_DEFAULT_ACCOUNT")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AGENCY BANKING TOKEN"
        verbose_name_plural = "AGENCY BANKING TOKENS"

    def __str__(self):
        return f"{self.account} - {self.token}"

    @classmethod
    def retrieve_token(cls, account):
        from pos_app.pos_helpers import (
            agent_login,
            agent_retail_wallet_login,
            excess_wallet_login,
            ghana_lotto_wallet_login,
            non_retail_wallet_login,
            pending_late_withdrawal_wallet_login,
            pre_funding_wallet_login,
            retail_commission_wallet_login,
            rto_wallet_login,
        )

        try:
            recent_token = cls.objects.filter(account=account).latest("created_at")
            if recent_token.token_age_in_minutes >= 5:
                recent_token.delete()

                # login
                if account == "VFD_DEFAULT_ACCOUNT":
                    token = agent_login()
                    if token:
                        cls.create_token(token, account)
                    return token
                elif account == "NON_RETAIL_WALLET":
                    token = non_retail_wallet_login()
                    if token:
                        cls.create_token(token, account)
                    return token

                elif account == "RETAIL_RTP_WALLET":
                    token = agent_retail_wallet_login()
                    if token:
                        cls.create_token(token, account)
                    return token

                elif account == "GHANA_RTP_WALLET":
                    token = ghana_lotto_wallet_login()
                    if token:
                        cls.create_token(token, account)
                    return token

                elif account == "RTO_WALLET":
                    token = rto_wallet_login()
                    if token:
                        cls.create_token(token, account)
                    return token
                elif account == "RETAIL_COMMISSION_WALLET":
                    token = retail_commission_wallet_login()
                    if token:
                        cls.create_token(token, account)
                    return token

                elif account == "PENDING_LATE_WITHDRAWAL_WALLET":
                    token = pending_late_withdrawal_wallet_login()
                    if token:
                        cls.create_token(token, account)
                    return token

                elif account == "EXCESS_WALLET":
                    token = excess_wallet_login()
                    if token:
                        cls.create_token(token, account)
                    return token

                elif account == "PRE_FUNDING_WALLET":
                    token = pre_funding_wallet_login()
                    if token:
                        cls.create_token(token, account)
                    return token

                else:
                    return ""

            else:
                return recent_token.token

        except cls.DoesNotExist:
            # login
            if account == "VFD_DEFAULT_ACCOUNT":
                token = agent_login()
                if token:
                    cls.create_token(token, account)
                return token
            elif account == "NON_RETAIL_WALLET":
                token = non_retail_wallet_login()
                if token:
                    cls.create_token(token, account)
                return token

            elif account == "RETAIL_RTP_WALLET":
                token = agent_retail_wallet_login()
                if token:
                    cls.create_token(token, account)
                return token

            elif account == "GHANA_RTP_WALLET":
                token = ghana_lotto_wallet_login()
                if token:
                    cls.create_token(token, account)
                return token

            elif account == "RTO_WALLET":
                token = rto_wallet_login()
                if token:
                    cls.create_token(token, account)
                return token
            elif account == "RETAIL_COMMISSION_WALLET":
                token = retail_commission_wallet_login()
                if token:
                    cls.create_token(token, account)
                return token

            elif account == "PENDING_LATE_WITHDRAWAL_WALLET":
                token = pending_late_withdrawal_wallet_login()
                if token:
                    cls.create_token(token, account)
                return token

            elif account == "EXCESS_WALLET":
                token = excess_wallet_login()
                if token:
                    cls.create_token(token, account)
                return token

            elif account == "PRE_FUNDING_WALLET":
                token = pre_funding_wallet_login()
                if token:
                    cls.create_token(token, account)
                return token

            else:
                return ""

        except AgencyBankingToken.MultipleObjectsReturned:
            cls.objects.filter(account=account).delete()
            return cls.retrieve_token(account)

    @classmethod
    def create_token(cls, token, account):
        new_token = cls.objects.create(token=token, account=account)
        new_token.token

    @property
    def token_age_in_minutes(self):
        created_at = self.created_at
        time_now = timezone.now()
        time_difference = (time_now - created_at).seconds / 60
        return time_difference


class AgencyBankingLoginAttempt(models.Model):
    ACCOUNTS = [
        ("VFD_DEFAULT_ACCOUNT", "VFD_DEFAULT_ACCOUNT"),
        ("NON_RETAIL_WALLET", "NON_RETAIL_WALLET"),
        ("RETAIL_RTP_WALLET", "RETAIL_RTP_WALLET"),
        ("GHANA_RTP_WALLET", "GHANA_RTP_WALLET"),
        ("RTO_WALLET", "RTO_WALLET"),
        ("RETAIL_COMMISSION_WALLET", "RETAIL_COMMISSION_WALLET"),
    ]

    account = models.CharField(max_length=300, choices=ACCOUNTS, default="VFD_DEFAULT_ACCOUNT")
    response = models.TextField(null=True, blank=True)
    unsuspension_response = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AGENCY BANKING LOGIN ATTEMPT"
        verbose_name_plural = "AGENCY BANKING LOGIN ATTEMPTS"


class SupervisorTerminalRetrieval(models.Model):
    retrieved = models.BooleanField(default=True)
    collected = models.BooleanField(default=False)
    supervisor_name = models.CharField(max_length=255, null=True, blank=True)
    supervisor_phone = models.CharField(max_length=255, null=True, blank=True)
    supervisor_email = models.CharField(max_length=255, null=True, blank=True)
    terminal_id = models.CharField(max_length=255, null=True, blank=True)
    terminal_serial_number = models.CharField(max_length=255, null=True, blank=True)
    date_retrieved = models.CharField(max_length=255, null=True, blank=True)
    date_deployed = models.CharField(max_length=255, null=True, blank=True)
    agent_name = models.CharField(max_length=255, null=True, blank=True)
    agent_phone = models.CharField(max_length=255, null=True, blank=True)
    agent_email = models.CharField(max_length=255, null=True, blank=True)
    location = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"AGENT_NAME: {self.agent_phone}"

    class Meta:
        verbose_name = "SUPERVISOR TERMINAL RETRIEVAL"
        verbose_name_plural = "SUPERVISOR TERMINAL RETRIEVALS"

    @classmethod
    def create_remote_sheet_data(cls, validated_data):
        date_retrieved = None
        date_deployed = None

        raw_date_retrieved = validated_data.get("DATE RETRIEVED")
        if raw_date_retrieved:
            date_retrieved = datetime.fromisoformat(str(raw_date_retrieved).replace("Z", "+00:00")).date()
        else:
            pass

        raw_date_deployed = validated_data.get("DATE DEPLOYED")
        if raw_date_deployed:
            date_deployed = datetime.fromisoformat(str(raw_date_deployed).replace("Z", "+00:00")).date()
        else:
            pass

        new_data = cls.objects.create(
            supervisor_name=validated_data.get("agent_instance").supervisor.full_name,
            supervisor_phone=validated_data.get("agent_instance").supervisor.phone,
            supervisor_email=validated_data.get("agent_instance").supervisor.email,
            terminal_id=validated_data.get("agent_instance").terminal_id,
            terminal_serial_number=validated_data.get("TERMINAL SERIAL NUMBER"),
            agent_name=validated_data.get("AGENT NAME"),
            agent_phone=validated_data.get("AGENT PHONE NUMBER"),
            agent_email=validated_data.get("AGENT EMAIL"),
            date_retrieved=date_retrieved,
            date_deployed=date_deployed,
            location=validated_data.get("LOCATION"),
        )
        return new_data


class LottoVerticalLead(models.Model):
    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    full_name = models.CharField(max_length=255)
    phone = models.CharField(max_length=300)
    user_id = models.CharField(max_length=300)
    email = models.CharField(max_length=300, null=True, blank=True)
    user_uuid = models.CharField(max_length=500, default=uuid.uuid4)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    address = models.CharField(max_length=300, default="YABA, Lagos Nigeria")

    class Meta:
        verbose_name = "LOTTO VERTICAL LEAD"
        verbose_name_plural = "LOTTO VERTICAL LEADS"

    def can_withdraw_commission(self):
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)

        agents_monthly_activities = LottoAgentMonthlyAnalytic.objects.filter(
            vertical_lead_phone=self.phone,
            created_at__year=current_datetime.year,
            created_at__month=current_datetime.month,
        )

        if len(agents_monthly_activities) == 0:
            return False, "No agent activity for this month"

        total_agents = len(agents_monthly_activities)

        active_agents = agents_monthly_activities.filter(status="ACTIVE")

        active_agents_count = len(active_agents)

        active_percentage = (active_agents_count / total_agents) * 100

        can_withdraw = active_percentage >= 85

        if can_withdraw is True:
            return True, "Can withdraw"

        return (
            False,
            f"you cannot withdraw commission at this time, your agent activity for this month is {active_percentage}%",
        )

    def get_duration(self):
        """Calculate the duration since the user was created."""
        if not self.created_at:
            return "Unknown"

        current_time = datetime.now()
        created_time = self.created_at.replace(tzinfo=None)  # Ensure timezone compatibility
        delta = relativedelta(current_time, created_time)

        duration_parts = []
        if delta.years:
            duration_parts.append(f"{delta.years} year{'s' if delta.years > 1 else ''}")
        if delta.months:
            duration_parts.append(f"{delta.months} month{'s' if delta.months > 1 else ''}")

        return ", ".join(duration_parts) if duration_parts else "Less than a month"


class LottoVerticalLeadWallet(models.Model):
    vertical_lead = models.ForeignKey(LottoVerticalLead, on_delete=models.CASCADE)
    commission_balance = models.FloatField(default=0)
    rewarded_commission_balance = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO VERTICAL LEAD WALLET"
        verbose_name_plural = "LOTTO VERTICAL LEAD WALLETS"

    @classmethod
    def fund_commission_wallet(cls, vertical_lead_phone_number, amount, transaction_type, lottery_type=None):
        try:
            user_instance = LottoVerticalLead.objects.get(phone=vertical_lead_phone_number)
        except LottoVerticalLead.DoesNotExist:
            return

        wallet_instance, _ = cls.objects.get_or_create(vertical_lead=user_instance)

        before = wallet_instance.commission_balance
        after = before + amount

        wallet_instance.commission_balance = round(after, 2)
        wallet_instance.save()
        wallet_instance.refresh_from_db()

        LottoVerticalLeadWalletTransaction.objects.create(
            wallet=wallet_instance,
            amount=amount,
            balance_before=before,
            balance_after=after,
            transactton_type=transaction_type,
            transaction_from="COMMISSION",
        )

        if transaction_type == "CREDIT":
            Wallet.fund_wallet(
                amount=amount, wallet_type="COMMISSION", game_type=lottery_type, is_vertical_lead_commission=True
            )

    @classmethod
    def deduct_commission_wallet(cls, vertical_lead_phone_number, amount):
        try:
            user_instance = LottoVerticalLead.objects.get(phone=vertical_lead_phone_number)
        except LottoVerticalLead.DoesNotExist:
            return

        wallet_instance, _ = cls.objects.get_or_create(vertical_lead=user_instance)

        before = wallet_instance.commission_balance

        if wallet_instance.commission_balance < amount:
            return

        after = before - amount

        wallet_instance.commission_balance = after
        wallet_instance.save()
        wallet_instance.refresh_from_db()

        LottoVerticalLeadWalletTransaction.objects.create(
            wallet=wallet_instance,
            amount=amount,
            balance_before=before,
            balance_after=after,
            transactton_type="DEBIT",
            transaction_from="COMMISSION_REWARDED",
        )


class LottoVerticalLeadWalletTransaction(models.Model):
    TRANSACTION_TYPE = (
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
        ("REVERSAL", "REVERSAL"),
    )

    TRANSACTION_FROM = (
        ("COMMISSION", "COMMISSION"),
        ("COMMISSION_REWARDED", "COMMISSION_REWARDED"),
    )

    wallet = models.ForeignKey(LottoVerticalLeadWallet, on_delete=models.CASCADE)
    amount = models.FloatField(default=0)
    balance_before = models.FloatField(default=0)
    balance_after = models.FloatField(default=0)
    transactton_type = models.CharField(max_length=25, choices=TRANSACTION_TYPE)
    transaction_from = models.CharField(max_length=25, choices=TRANSACTION_FROM)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "LOTTO VERTICAL LEAD WALLET TRANSACTION"
        verbose_name_plural = "LOTTO VERTICAL LEAD WALLET TRANSACTIONS"


class SupervisorVerticalLeadAssignmentDataSync(models.Model):
    data = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SUPERVISOR, SUPER AGENT AND VERTICAL LEAD ASSIGNMENT NOTIFICATION"
        verbose_name_plural = "SUPERVISOR, SUPER AGENT AND VERTICAL LEAD ASSIGNMENT NOTIFICATIONS"


class TerminalRecoverySheetData(models.Model):
    data = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TERMINAL RECOVERY SHEET DATA"
        verbose_name_plural = "TERMINAL RECOVERY SHEET DATA"
