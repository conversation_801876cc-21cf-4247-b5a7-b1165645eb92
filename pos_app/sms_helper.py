import json

import requests
from django.conf import settings


def winners_notifcation(phone_number, game_id, amount_won, pin):
    """
    Send notification to winners with the winning numbers and pin to claim the prize
    """
    if settings.DEBUG or settings.DEBUG is True:
        return {"message": "success"}

    url = "https://whispersms.xyz/transactional/send"
    headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }
    payload = json.dumps(
        {
            # "receiver": f"{phone_number}",
            "receiver": "2347039115243",
            "template": "da6d61a9-1032-4dcc-866d-2df5b3c93b87",
            "place_holders": {
                "game_play_id": f"{game_id}",
                "amount_won": f"{amount_won}",
                "pin": f"{pin}",
            },
        }
    )

    res = requests.request("POST", url, headers=headers, data=payload)
    print("res.text", "pos winners notification", res.text)
    try:
        return res.json()
    except Exception:
        return res.text
