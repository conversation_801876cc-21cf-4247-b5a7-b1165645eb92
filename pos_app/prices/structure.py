# from pprint import pprint


from prices.game_price import (
    InstantCashOutPriceModel,
    QuikaPriceModel,
    VirtualSoccerPriceModel,
    WyseCashPriceModel,
)

LOTT0_AMOUNT = {
    300: {
        "ticket_line": 1,
        "ticket_count": 1,
        "stake_amount": 300,
        "winning_amount": 1000,
    },
    600: {
        "ticket_line": 1,
        "ticket_count": 2,
        "stake_amount": 600,
        "winning_amount": 1666.67,
    },
    # 850: {
    #     "ticket_line": 1,
    #     "ticket_count": 3,
    #     "stake_amount": 850,
    #     "winning_amount": 1000
    # },
    1200: {
        "ticket_line": 1,
        "ticket_count": 4,
        "stake_amount": 1200,
        "winning_amount": 2400,
    },
    # 1350: {
    #     "ticket_line": 1,
    #     "ticket_count": 5,
    #     "stake_amount": 1350,
    #     "winning_amount": 1000
    # },
    # 1400: {
    #     "ticket_line": 1,
    #     "ticket_count": 6,
    #     "stake_amount": 1400,
    #     "winning_amount": 1000
    # },
    # 1600: {
    #     "ticket_line": 1,
    #     "ticket_count": 7,
    #     "stake_amount": 1600,
    #     "winning_amount": 1000
    # },
}

quika_i_cash_lines_amount = {
    1: {"stake_amount": 300, "reg_amount": 200, "illusion": 100},
    2: {"stake_amount": 600, "reg_amount": 500, "illusion": 100},
    3: {"stake_amount": 850, "reg_amount": 750, "illusion": 100},
    4: {"stake_amount": 1200, "reg_amount": 800, "illusion": 400},
    5: {"stake_amount": 1450, "reg_amount": 1250, "illusion": 200},
    6: {"stake_amount": 1500, "reg_amount": 1300, "illusion": 200},
    7: {"stake_amount": 1700, "reg_amount": 1400, "illusion": 300},
}

I_CASH_ILLUSION_REG_EXPECTED_AMOUNT = {
    300: {"winning_amount": 1000},
    600: {"winning_amount": 1666.67},
    850: {"winning_amount": 2000},
    1200: {"winning_amount": 2400},
    1450: {"winning_amount": 2666.67},
    1500: {"winning_amount": 3000},
    1700: {"winning_amount": 3333.33},
}

I_CASH_UPDATED_AMOUNT = {
    50: {"winning_amount": 1000},
    100: {"winning_amount": 1000},
    200: {"winning_amount": 1000},
    500: {"winning_amount": 1666.67},
    750: {"winning_amount": 2000},
    800: {"winning_amount": 2400},
    1250: {"winning_amount": 2666.67},
    1300: {"winning_amount": 3000},
    1400: {"winning_amount": 3333.33},
}


def quick_pricing_update():
    return LOTT0_AMOUNT.values()


class LottoObjectConverter:
    def __init__(self, data):
        self.data = data

    def to_list_of_dictionary(self):
        values = []
        count = 1
        for item in self.data.values():
            item["line_number"] = count
            item["winning_amount"] = item.pop("total_winning_amount")
            values.append(item)
            count += 1
        return values

    def i_cash_pricing(self):
        pass

        # update_amount = []
        # for count, (key, values) in enumerate(self.data.items()):
        #     count += 1
        #     object_ref = {
        #         "stake_amount": key,
        #         "line_number": count,
        #         "winning_amount": values["max_win"]
        #     }
        #     update_amount.append(object_ref)
        # return update_amount

        instant_cashout_price_model = InstantCashOutPriceModel()

        price_data = instant_cashout_price_model.ticket_price(channel="POS")

        values = []

        for item in price_data.values():
            values.append(
                {
                    "stake_amount": item.get("ticket_price")
                    + item.get("illusion_price", 0)
                    + item.get("woven_service_charge", 0)
                    + item.get("africastalking_charge", 0),
                    "line_number": item.get("line_number"),
                    "winning_amount": item.get("potential_winning"),
                }
            )

        return values

    def web_i_cash_pricing(self):
        from main.models import ConstantVariable

        # web_pricing = []
        # for count, (key, values) in enumerate(self.data.items()):
        #     count += 1
        #     object_ref = {
        #         "stake_amount": quika_i_cash_lines_amount[count]["stake_amount"],
        #         "line_number": count,
        #         "winning_amount": values["max_win"],
        #     }
        #     web_pricing.append(object_ref)
        # return web_pricing

        instant_cashout_price_model = InstantCashOutPriceModel()
        ConstantVariable.objects.first()

        price_data = instant_cashout_price_model.ticket_price_with_other_service_price(channel="WEB")

        values = []

        for item in price_data.values():
            values.append(
                {
                    "stake_amount": item.get("ticket_price")
                    + item.get("illusion_price", 0)
                    + item.get("woven_service_charge", 0)
                    + item.get("africastalking_charge", 0),
                    "line_number": item.get("line_number"),
                    "winning_amount": item.get("potential_winning"),
                }
            )

        return values

    def virtual_soccer_pricing(self, channel):
        pass

        instant_cashout_price_model = VirtualSoccerPriceModel()

        price_data = instant_cashout_price_model.ticket_price(channel=channel)

        values = []

        for item in price_data.values():
            values.append(
                {
                    "stake_amount": item.get("ticket_price")
                    + item.get("illusion_price", 0)
                    + item.get("woven_service_charge", 0)
                    + item.get("africastalking_charge", 0),
                    "line_number": item.get("line_number"),
                    "winning_amount": item.get("potential_winning"),
                }
            )

        return values

    def quika_pricing(self):
        update_amount = []
        for count, (key, values) in enumerate(self.data.items()):
            # count += 1
            object_ref = {
                "stake_amount": key + 100,
                "line_number": 1,
                "winning_amount": values["max_win"],
            }
            update_amount.append(object_ref)

        return update_amount


class WyseCashObjConverter:
    def __init__(self, data):
        self.data = data

    def convert_to_list_of_object(self, channel):
        # final_result = []
        # for band, band_data in self.data.items():
        #     lines = []

        #     for line, line_data in band_data.items():
        #         line_data["line_number"] = line
        #         line_data["winning_amount"] = line_data.pop("total_winning_amount")
        #         lines.append(line_data)

        #     to_add = {"band": str(band), "lines": lines}

        #     final_result.append(to_add)

        # return final_result

        wyse_cash_price_model = WyseCashPriceModel()
        mobile_wyse_cash_game_price = []

        mobile_price_data = wyse_cash_price_model.ticket_price(channel=channel)

        for band, band_data in mobile_price_data.items():
            lines = []

            for line, line_data in band_data.items():
                line_data["line_number"] = line
                # line_data["winning_amount"] = line_data.pop("total_winning_amount")
                lines.append(
                    {
                        "stake_amount": line_data.get("stake_amount")
                        + line_data.get("woven_service_charge", 0)
                        + line_data.get("africastalking_charge", 0),
                        "line_number": line_data.get("line_number"),
                        "winning_amount": line_data.get("total_winning_amount"),
                    }
                )

            to_add = {"band": str(band), "lines": lines}

            mobile_wyse_cash_game_price.append(to_add)

        return mobile_wyse_cash_game_price


class Quika:
    def __init__(self, lines, stake_amount, total_winning):
        self.lines = lines
        self.stake_amount = stake_amount
        self.total_winning = total_winning

    def prices(self):
        pricing = {}
        for i in range(len(self.lines)):
            lines_and_amount = {
                int(self.lines[i]): {
                    "stake_amount": int(self.stake_amount[i]),
                    "total_winning_amount": int(self.total_winning[i]),
                    "line": int(self.lines[i]),
                }
            }
            pricing.update(lines_and_amount)

        # print(pricing)
        """sample results:
        {
           "1":{
              "stake_amount":100,
              "total_winning_amount":500,
              "line":1
           },
           "2":{
              "stake_amount":200,
              "total_winning_amount":1000,
              "line":2
           },
           "3":{
              "stake_amount":400,
              "total_winning_amount":2000,
              "line":3
           },
           "4":{
              "stake_amount":500,
              "total_winning_amount":2500,
              "line":4
           },
           "5":{
              "stake_amount":600,
              "total_winning_amount":3000,
              "line":5
           },
           "6":{
              "stake_amount":800,
              "total_winning_amount":4000,
              "line":6
           },
           "7":{
              "stake_amount":1000,
              "total_winning_amount":5000,
              "line":7
           }
        }
        """
        return pricing


def quika_pricing_and_reg_count(stake_amount, channel=None) -> dict:
    # ticket_count_and_amount_helper = LOTT0_AMOUNT[int(stake_amount)]
    # ticket_reg_count = ticket_count_and_amount_helper["ticket_count"]
    # potential_winning = ticket_count_and_amount_helper["winning_amount"]
    # in_excess_deduction = 200 if stake_amount >= 1000 else 100
    # main_stake_amount = ticket_count_and_amount_helper["stake_amount"]
    # ticket_amount = main_stake_amount - in_excess_deduction
    # expected_reg_amount_per_ticket = ticket_amount / ticket_reg_count

    quicka_price_model = QuikaPriceModel()

    if "POS" in str(channel).upper():
        _channel = "POS"
    else:
        _channel = "WEB"

    print("stake_amount", stake_amount, "channel", _channel, "\n\n\n")
    ticket_count_and_amount_helper = quicka_price_model.get_ticket_price_details_with_stake_amount(channel=_channel, stake_amount=stake_amount)
    ticket_reg_count = ticket_count_and_amount_helper.get("line_number", 1)
    potential_winning = ticket_count_and_amount_helper.get("potential_winning", 0)
    in_excess_deduction = ticket_count_and_amount_helper.get("illusion_price", 0)
    main_stake_amount = ticket_count_and_amount_helper.get("total_stake_amount", 0)
    ticket_amount = main_stake_amount - in_excess_deduction
    expected_reg_amount_per_ticket = ticket_amount / ticket_reg_count

    amounts = {
        "reg_count": ticket_reg_count,
        "reg_amount": expected_reg_amount_per_ticket,
        "main_stake_amount": main_stake_amount,
        "potential_winning": potential_winning,
    }

    return amounts


if __name__ == "__main__":
    # lines = ["1", 3, 5]
    # stake_amount = [100, 300, 400]
    # total_winning = [10000, 20000, 50000]
    # Quika(lines, stake_amount, total_winning).prices()
    # print(quika_pricing_update())
    print(quika_i_cash_lines_amount.get(1)["stake_amount"])
