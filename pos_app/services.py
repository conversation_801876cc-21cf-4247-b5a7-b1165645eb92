import datetime
# from decimal import Decimal

from django.db.models import Sum
from django.utils import timezone
from operator import itemgetter

from .models import (
    Agent, Supervisor, AgentWalletTransaction
)
from .serializers import AgentModelSerializer


class SupervisorDashboardService:
    """Service class for supervisor dashboard operations"""

    @staticmethod
    def get_date_range(period, start_date=None, end_date=None):
        """Get date range based on period"""
        today = timezone.now().date()

        if period == 'today':
            return today, today
        elif period == 'yesterday':
            yesterday = today - datetime.timedelta(days=1)
            return yesterday, yesterday
        elif period == 'week':
            start_of_week = today - datetime.timedelta(days=today.weekday())
            return start_of_week, today
        elif period == 'last_week':
            # Get start of current week, then go back 7 days to get start of last week
            start_of_current_week = today - datetime.timedelta(days=today.weekday())
            start_of_last_week = start_of_current_week - datetime.timedelta(days=7)
            end_of_last_week = start_of_current_week - datetime.timedelta(days=1)
            return start_of_last_week, end_of_last_week
        elif period == 'month':
            start_of_month = today.replace(day=1)
            return start_of_month, today
        elif period == 'last_month':
            # Get first day of current month, subtract 1 day to get last day of previous month
            first_day_current_month = today.replace(day=1)
            last_day_previous_month = first_day_current_month - datetime.timedelta(days=1)
            # Get first day of previous month
            first_day_previous_month = last_day_previous_month.replace(day=1)
            return first_day_previous_month, last_day_previous_month
        elif period == 'custom' and start_date and end_date:
            return start_date, end_date
        else:
            # Default to month
            start_of_month = today.replace(day=1)
            return start_of_month, today

    @staticmethod
    def compute_sales(supervisor_id, period, start_date=None, end_date=None):
        """Compute sales for a supervisor"""
        start_date, end_date = SupervisorDashboardService.get_date_range(period, start_date, end_date)

        supervisor = Supervisor.objects.get(id=supervisor_id)

        # Get all agents under this supervisor
        agents = Agent.objects.filter(supervisor=supervisor)

        # Get all transactions for these agents in the date range
        transactions = AgentWalletTransaction.objects.filter(
            agent_wallet__agent__in=agents,
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            # transaction_from='GAME_PLAY'  # Only count game play transactions
        )

        # Calculate total sales
        total_sales = transactions.aggregate(total=Sum('amount'))['total'] or float('0.00')

        # Calculate sales per agent
        sales_per_agent = {}
        for agent in agents:
            agent_sales = transactions.filter(agent_wallet__agent=agent).aggregate(total=Sum('amount'))['total'] or float(
                '0.00')
            sales_per_agent[str(agent.id)] = {
                'agent_name': f"{agent.first_name} {agent.last_name}",
                'sales_amount': float(agent_sales)
            }

        # # Calculate sales per terminal
        # terminals = Terminal.objects.filter(agent__in=agents)
        # sales_per_terminal = {}
        # for terminal in terminals:
        #     terminal_sales = transactions.filter(agent=terminal.agent).aggregate(total=Sum('amount'))[
        #                          'total'] or float('0.00')
        #     sales_per_terminal[str(terminal.id)] = {
        #         'terminal_id': terminal.terminal_id,
        #         'sales_amount': float(terminal_sales)
        #     }

        # Calculate daily sales
        daily_sales = {}
        current_date = start_date
        while current_date <= end_date:
            day_sales = transactions.filter(date_created__date=current_date).aggregate(total=Sum('amount'))[
                            'total'] or float('0.00')
            daily_sales[current_date.strftime('%Y-%m-%d')] = float(day_sales)
            current_date += datetime.timedelta(days=1)

        return {
            'total_sales': float(total_sales),
            'breakdown_per_agent': sales_per_agent,
            'datewise_data': daily_sales,
            'period': {
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d')
            }
        }

    @staticmethod
    def compute_target_achievement(supervisor_id, period, start_date=None, end_date=None):
        """Compute target achievement for a supervisor"""
        start_date, end_date = SupervisorDashboardService.get_date_range(period, start_date, end_date)


        supervisor = Supervisor.objects.get(id=supervisor_id)

        # Get all agents under this supervisor
        agents = Agent.objects.filter(supervisor=supervisor)

        # Get all terminals assigned to these agents that are not retrieved
        agent_count = agents.count()

        # Calculate days in period
        days_in_period = (end_date - start_date).days + 1

        # Calculate expected target (15,000 per terminal per day)
        daily_target_per_agent = float('15000.00')
        expected_target = agent_count * days_in_period * daily_target_per_agent

        # Calculate actual sales
        transactions = AgentWalletTransaction.objects.filter(
            agent_wallet__agent__in=agents,
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            # transaction_from='GAME_PLAY'
        )
        actual_sales = transactions.aggregate(total=Sum('amount'))['total'] or float('0.00')

        # Calculate target achievement percentage
        target_achievement_percent = (actual_sales / expected_target) * 100 if expected_target > 0 else 0

        # Calculate average sales per terminal per day
        avg_sales_per_agent_per_day = actual_sales / (
                agent_count * days_in_period) if agent_count > 0 and days_in_period > 0 else 0

        return {
            'target_achievement_percent': float(target_achievement_percent),
            'actual_sales': float(actual_sales),
            'expected_target': float(expected_target),
            'average_sales_per_agent_per_day': float(avg_sales_per_agent_per_day),
            'agent_count': agent_count,
            'days_in_period': days_in_period,
            'period': {
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d')
            }
        }

    @staticmethod
    def calculate_agent_status(supervisor_id):
        """Calculate agent performance status based on sales data"""
        absent_agents = []
        inactive_agents = []
        at_risk_agents = []
        declining_agents = []
        active_agents = []

        
        supervisor = Supervisor.objects.get(id=supervisor_id)

        # Get all agents under this supervisor
        agents = Agent.objects.filter(supervisor=supervisor, terminal_retrieved = False)
        total_number_of_agents = agents.count()
        # agent = Agent.objects.get(id=agent_id)
        today = timezone.now().date()

        # Calculate 14-day average
        fourteen_days_ago = today - datetime.timedelta(days=14)

        for agent in agents:
            # Get transactions for last 14 days
            transactions_14d = AgentWalletTransaction.objects.filter(
                agent_wallet__agent=agent,
                date_created__date__gte=fourteen_days_ago,
                date_created__date__lte=today,
                # transaction_from='GAME_PLAY'
            )

            # Group by day to count active days
            active_days_14d = transactions_14d.values('date_created__date').distinct().count()

            # Calculate average daily sales
            if active_days_14d == 0:
                avg_14d = float('0.00')
            else:
                total_sales_14d = transactions_14d.aggregate(total=Sum('amount'))['total'] or float('0.00')
                avg_14d = total_sales_14d / active_days_14d

            # Calculate 30-day average
            thirty_days_ago = today - datetime.timedelta(days=30)

            # Get transactions for last 30 days
            transactions_30d = AgentWalletTransaction.objects.filter(
                agent_wallet__agent=agent,
                date_created__date__gte=thirty_days_ago,
                date_created__date__lte=today,
                # transaction_from='GAME_PLAY'
            )

            # Group by day to count active days
            active_days_30d = transactions_30d.values('date_created__date').distinct().count()

            # Calculate average daily sales
            if active_days_30d == 0:
                avg_30d = float('0.00')
            else:
                total_sales_30d = transactions_30d.aggregate(total=Sum('amount'))['total'] or float('0.00')
                avg_30d = total_sales_30d / active_days_30d

            # Check for sales in the last 2 days
            two_days_ago = today - datetime.timedelta(days=2)
            has_recent_sales = AgentWalletTransaction.objects.filter(
                agent_wallet__agent=agent,
                date_created__date__gte=two_days_ago,
                # transaction_from='GAME_PLAY'
            ).exists()

            # Determine status based on criteria (in priority order)
            agent_data = AgentModelSerializer(agent).data

            if avg_14d < 1000 or not has_recent_sales:
                absent_agents.append(agent_data)
            elif avg_14d < 4000:
                inactive_agents.append(agent_data)
            elif avg_14d < 8000:
                at_risk_agents.append(agent_data)
            elif (avg_30d > 8000) and (avg_14d < 8000):
                declining_agents.append(agent_data)
            elif avg_14d >= 8000:  # avg_14d >= 8000
                active_agents.append(agent_data)

            print(f"HERE'S YOUR RESPONSE |=>|=>|=>|=>|=>|=>|=>|=>|=> \n\n\n YAY! \n\n\n")
        number_of_active_agents = len(active_agents)
        try:
            agent_activity_rate = float((number_of_active_agents / total_number_of_agents) * 100)
        except ZeroDivisionError:
            agent_activity_rate = 0
        return {
            "agent_activity_rate": agent_activity_rate,
            'number_of_absent_agents': len(absent_agents),
            'list_of_absent_agents': absent_agents,
            'number_of_inactive_agents': len(inactive_agents),
            'list_of_inactive_agents': inactive_agents,
            'number_of_at_risk_agents': len(at_risk_agents),
            'list_of_at_risk_agents': at_risk_agents,
            'number_of_declining_agents': len(declining_agents),
            'list_of_declining_agents': declining_agents,
            'number_of_active_agents': number_of_active_agents,
            'list_of_active_agents': active_agents
        }

    @staticmethod
    def filter_calculate_agent_status(supervisor_id, filter_query):
        """Calculate agent performance status based on sales data"""
        absent_agents = []
        inactive_agents = []
        at_risk_agents = []
        declining_agents = []
        active_agents = []

        supervisor = Supervisor.objects.get(id=supervisor_id)
        # Get all agents under this supervisor
        agents = Agent.objects.filter(supervisor=supervisor)

        today = timezone.now().date()
        # Check for sales in the last 2 days
        two_days_ago = today - datetime.timedelta(days=2)
        # Calculate 14-day average
        fourteen_days_ago = today - datetime.timedelta(days=14)
        # Calculate 30-day average
        thirty_days_ago = today - datetime.timedelta(days=30)

        if filter_query == 'absent_agents':
            print('ABSENT \n\n\n')
            for agent in agents:
                transactions_14d = AgentWalletTransaction.objects.filter(
                    agent_wallet__agent=agent,
                    date_created__date__gte=fourteen_days_ago,
                    date_created__date__lte=today,
                )

                # Group by day to count active days
                active_days_14d = transactions_14d.values('date_created__date').distinct().count()
                # Calculate average daily sales
                if active_days_14d == 0:
                    avg_14d = float('0.00')
                else:
                    total_sales_14d = transactions_14d.aggregate(total=Sum('amount'))['total'] or float('0.00')
                    avg_14d = total_sales_14d / active_days_14d

                has_recent_sales = AgentWalletTransaction.objects.filter(
                    agent_wallet__agent=agent,
                    date_created__date__gte=two_days_ago,
                ).exists()

                if avg_14d < 1000 or not has_recent_sales:
                    transactions_for_today = AgentWalletTransaction.objects.filter(
                        agent_wallet__agent=agent,
                        date_created__date__gte=today,
                        date_created__date__lte=today,
                    )
                    count_of_transactions_performed_today = transactions_for_today.count()
                    total_sales_made_today = transactions_for_today.aggregate(total=Sum('amount'))['total'] or float(
                        '0.00')

                    start_date, end_date = SupervisorDashboardService.get_date_range(period='month', start_date=None,
                                                                                     end_date=None)
                    transactions_for_the_month = AgentWalletTransaction.objects.filter(
                        agent_wallet__agent=agent,
                        date_created__date__gte=start_date,
                        date_created__date__lte=end_date,
                    )
                    count_of_transactions_for_the_month = transactions_for_the_month.count()
                    total_sales_made_this_month = transactions_for_the_month.aggregate(total=Sum('amount'))[
                                                      'total'] or float('0.00')

                    # Determine status based on criteria (in priority order)
                    agent_data = AgentModelSerializer(agent).data
                    agent_data['amount_of_sales_today'] = total_sales_made_today
                    agent_data['count_of_sales_today'] = count_of_transactions_performed_today
                    agent_data['amount_of_sales_made_this_month'] = total_sales_made_this_month
                    agent_data['count_of_sales_made_this_month'] = count_of_transactions_for_the_month
                    absent_agents.append(agent_data)

            sorted_agents = sorted(absent_agents, key=itemgetter('amount_of_sales_today'), reverse=True)
            return {
                'agents': sorted_agents,
                'number_of_agents': len(absent_agents),

            }

        elif filter_query == 'inactive_agents':
            print('INACTIVE_AGENTS \n\n\n')

            for agent in agents:
                transactions_14d = AgentWalletTransaction.objects.filter(
                    agent_wallet__agent=agent,
                    date_created__date__gte=fourteen_days_ago,
                    date_created__date__lte=today,
                    # transaction_from='GAME_PLAY'
                )

                # Group by day to count active days
                active_days_14d = transactions_14d.values('date_created__date').distinct().count()
                # Calculate average daily sales
                if active_days_14d == 0:
                    avg_14d = float('0.00')
                else:
                    total_sales_14d = transactions_14d.aggregate(total=Sum('amount'))['total'] or float('0.00')
                    avg_14d = total_sales_14d / active_days_14d

                if avg_14d < 4000:
                    transactions_for_today = AgentWalletTransaction.objects.filter(
                        agent_wallet__agent=agent,
                        date_created__date__gte=today,
                        date_created__date__lte=today,
                    )
                    count_of_transactions_performed_today = transactions_for_today.count()
                    total_sales_made_today = transactions_for_today.aggregate(total=Sum('amount'))['total'] or float(
                        '0.00')

                    start_date, end_date = SupervisorDashboardService.get_date_range(period='month', start_date=None,
                                                                                     end_date=None)
                    transactions_for_the_month = AgentWalletTransaction.objects.filter(
                        agent_wallet__agent=agent,
                        date_created__date__gte=start_date,
                        date_created__date__lte=end_date,
                    )
                    count_of_transactions_for_the_month = transactions_for_the_month.count()
                    total_sales_made_this_month = transactions_for_the_month.aggregate(total=Sum('amount'))[
                                                      'total'] or float('0.00')

                    # Determine status based on criteria (in priority order)
                    agent_data = AgentModelSerializer(agent).data
                    agent_data['amount_of_sales_today'] = total_sales_made_today
                    agent_data['count_of_sales_today'] = count_of_transactions_performed_today
                    agent_data['amount_of_sales_made_this_month'] = total_sales_made_this_month
                    agent_data['count_of_sales_made_this_month'] = count_of_transactions_for_the_month
                    inactive_agents.append(agent_data)

            sorted_agents = sorted(inactive_agents, key=itemgetter('amount_of_sales_today'), reverse=True)
            return {
                'agents': sorted_agents,
                'number_of_agents': len(inactive_agents),
            }

        elif filter_query == 'at_risk_agents':
            print('AT_RISK_AGENTS \n\n\n')

            for agent in agents:
                transactions_14d = AgentWalletTransaction.objects.filter(
                    agent_wallet__agent=agent,
                    date_created__date__gte=fourteen_days_ago,
                    date_created__date__lte=today,
                    # transaction_from='GAME_PLAY'
                )

                # Group by day to count active days
                active_days_14d = transactions_14d.values('date_created__date').distinct().count()
                # Calculate average daily sales
                if active_days_14d == 0:
                    avg_14d = float('0.00')
                else:
                    total_sales_14d = transactions_14d.aggregate(total=Sum('amount'))['total'] or float('0.00')
                    avg_14d = total_sales_14d / active_days_14d

                if avg_14d < 8000:
                    transactions_for_today = AgentWalletTransaction.objects.filter(
                        agent_wallet__agent=agent,
                        date_created__date__gte=today,
                        date_created__date__lte=today,
                    )
                    count_of_transactions_performed_today = transactions_for_today.count()
                    total_sales_made_today = transactions_for_today.aggregate(total=Sum('amount'))['total'] or float(
                        '0.00')

                    start_date, end_date = SupervisorDashboardService.get_date_range(period='month', start_date=None,
                                                                                     end_date=None)
                    transactions_for_the_month = AgentWalletTransaction.objects.filter(
                        agent_wallet__agent=agent,
                        date_created__date__gte=start_date,
                        date_created__date__lte=end_date,
                    )
                    count_of_transactions_for_the_month = transactions_for_the_month.count()
                    total_sales_made_this_month = transactions_for_the_month.aggregate(total=Sum('amount'))[
                                                      'total'] or float('0.00')

                    # Determine status based on criteria (in priority order)
                    agent_data = AgentModelSerializer(agent).data
                    agent_data['amount_of_sales_today'] = total_sales_made_today
                    agent_data['count_of_sales_today'] = count_of_transactions_performed_today
                    agent_data['amount_of_sales_made_this_month'] = total_sales_made_this_month
                    agent_data['count_of_sales_made_this_month'] = count_of_transactions_for_the_month
                    at_risk_agents.append(agent_data)

            sorted_agents = sorted(at_risk_agents, key=itemgetter('amount_of_sales_today'), reverse=True)
            return {
                'agents': sorted_agents,
                'number_of_agents': len(at_risk_agents),

            }

        elif filter_query == 'declining_agents':
            print('DECLINING_AGENTS \n\n\n')

            for agent in agents:
                transactions_14d = AgentWalletTransaction.objects.filter(
                    agent_wallet__agent=agent,
                    date_created__date__gte=fourteen_days_ago,
                    date_created__date__lte=today,
                    # transaction_from='GAME_PLAY'
                )

                # Group by day to count active days
                active_days_14d = transactions_14d.values('date_created__date').distinct().count()
                # Calculate average daily sales
                if active_days_14d == 0:
                    avg_14d = float('0.00')
                else:
                    total_sales_14d = transactions_14d.aggregate(total=Sum('amount'))['total'] or float('0.00')
                    avg_14d = total_sales_14d / active_days_14d

                # Get transactions for last 30 days
                transactions_30d = AgentWalletTransaction.objects.filter(
                    agent_wallet__agent=agent,
                    date_created__date__gte=thirty_days_ago,
                    date_created__date__lte=today,
                    # transaction_from='GAME_PLAY'
                )
                # Group by day to count active days
                active_days_30d = transactions_30d.values('date_created__date').distinct().count()

                # Calculate average daily sales
                if active_days_30d == 0:
                    avg_30d = float('0.00')
                else:
                    total_sales_30d = transactions_30d.aggregate(total=Sum('amount'))['total'] or float('0.00')
                    avg_30d = total_sales_30d / active_days_30d
                if (avg_30d > 8000) and (avg_14d < 8000):
                    transactions_for_today = AgentWalletTransaction.objects.filter(
                        agent_wallet__agent=agent,
                        date_created__date__gte=today,
                        date_created__date__lte=today,
                    )
                    count_of_transactions_performed_today = transactions_for_today.count()
                    total_sales_made_today = transactions_for_today.aggregate(total=Sum('amount'))['total'] or float(
                        '0.00')

                    start_date, end_date = SupervisorDashboardService.get_date_range(period='month', start_date=None,
                                                                                     end_date=None)
                    transactions_for_the_month = AgentWalletTransaction.objects.filter(
                        agent_wallet__agent=agent,
                        date_created__date__gte=start_date,
                        date_created__date__lte=end_date,
                    )
                    count_of_transactions_for_the_month = transactions_for_the_month.count()
                    total_sales_made_this_month = transactions_for_the_month.aggregate(total=Sum('amount'))[
                                                      'total'] or float('0.00')

                    # Determine status based on criteria (in priority order)
                    agent_data = AgentModelSerializer(agent).data
                    agent_data['amount_of_sales_today'] = total_sales_made_today
                    agent_data['count_of_sales_today'] = count_of_transactions_performed_today
                    agent_data['amount_of_sales_made_this_month'] = total_sales_made_this_month
                    agent_data['count_of_sales_made_this_month'] = count_of_transactions_for_the_month
                    declining_agents.append(agent_data)

            sorted_agents = sorted(declining_agents, key=itemgetter('amount_of_sales_today'), reverse=True)
            return {
                'agents': sorted_agents,
                'number_of_agents': len(declining_agents),

            }

        elif filter_query == 'active_agents':
            print('ACTIVE_AGENTS \n\n\n')

            for agent in agents:
                transactions_14d = AgentWalletTransaction.objects.filter(
                    agent_wallet__agent=agent,
                    date_created__date__gte=fourteen_days_ago,
                    date_created__date__lte=today,
                    # transaction_from='GAME_PLAY'
                )

                # Group by day to count active days
                active_days_14d = transactions_14d.values('date_created__date').distinct().count()
                # Calculate average daily sales
                if active_days_14d == 0:
                    avg_14d = float('0.00')
                else:
                    total_sales_14d = transactions_14d.aggregate(total=Sum('amount'))['total'] or float('0.00')
                    avg_14d = total_sales_14d / active_days_14d

                if avg_14d >= 8000:
                    transactions_for_today = AgentWalletTransaction.objects.filter(
                        agent_wallet__agent=agent,
                        date_created__date__gte=today,
                        date_created__date__lte=today,
                    )
                    count_of_transactions_performed_today = transactions_for_today.count()
                    total_sales_made_today = transactions_for_today.aggregate(total=Sum('amount'))['total'] or float(
                        '0.00')

                    start_date, end_date = SupervisorDashboardService.get_date_range(period='month', start_date=None,
                                                                                     end_date=None)
                    transactions_for_the_month = AgentWalletTransaction.objects.filter(
                        agent_wallet__agent=agent,
                        date_created__date__gte=start_date,
                        date_created__date__lte=end_date,
                    )
                    count_of_transactions_for_the_month = transactions_for_the_month.count()
                    total_sales_made_this_month = transactions_for_the_month.aggregate(total=Sum('amount'))[
                                                      'total'] or float('0.00')

                    # Determine status based on criteria (in priority order)
                    agent_data = AgentModelSerializer(agent).data
                    print(f"AGENT DATA ==> <> ==> <> ==> <> ==> {agent_data} \n\n\n")
                    agent_data['amount_of_sales_today'] = total_sales_made_today
                    agent_data['count_of_sales_today'] = count_of_transactions_performed_today
                    agent_data['amount_of_sales_made_this_month'] = total_sales_made_this_month
                    agent_data['count_of_sales_made_this_month'] = count_of_transactions_for_the_month
                    print(f"AGENT DATA ==> <> ==> <> ==> <> ==> {agent_data} \n\n\n")
                    active_agents.append(agent_data)

            sorted_agents = sorted(active_agents, key=itemgetter('amount_of_sales_today'), reverse=True)
            return {
                'agents': sorted_agents,
                'number_of_agents': len(active_agents),
            }

    @staticmethod
    def compute_earnings(supervisor_id, period, start_date=None, end_date=None):
        """Compute earnings for a supervisor"""
        start_date, end_date = SupervisorDashboardService.get_date_range(period, start_date, end_date)

        supervisor = Supervisor.objects.get(id=supervisor_id)

        # Get all agents under this supervisor
        agents = Agent.objects.filter(supervisor=supervisor)

        # Get all commission transactions
        transactions = AgentWalletTransaction.objects.filter(
            agent_wallet__agent__in=agents,
            date_created__date__gte=start_date,
            date_created__date__lte=end_date,
            transaction_type='COMMISSION'  # Assuming COMMISSION transactions represent earnings
        )

        # Calculate total commission
        total_commission = transactions.aggregate(total=Sum('amount'))['total'] or float('0.00')

        # Calculate commission per agent
        commission_per_agent = {}
        for agent in agents:
            agent_commission = transactions.filter(agent_wallet__agent=agent).aggregate(total=Sum('amount'))['total'] or float(
                '0.00')

            commission_per_agent[str(agent.id)] = {
                'agent_name': f"{agent.first_name} {agent.last_name}",
                'commission': float(agent_commission)
            }

        return {
            'total_commission': float(total_commission),
            'breakdown_per_agent': commission_per_agent,
            'period': {
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d')
            }
        }

    @staticmethod
    def get_dashboard_summary(supervisor_id):
        """Get dashboard summary for a supervisor"""
        try:
            # Get sales data
            sales_yesterday = SupervisorDashboardService.compute_sales(supervisor_id, 'yesterday')
            sales_today = SupervisorDashboardService.compute_sales(supervisor_id, 'today')
            try:
                today_sales_percentage_rate = (((sales_today['total_sales'] / sales_yesterday['total_sales']) * 100) - 100)
            except ZeroDivisionError:
                today_sales_percentage_rate = 0

            sales_last_week = SupervisorDashboardService.compute_sales(supervisor_id, 'last_week')
            sales_this_week = SupervisorDashboardService.compute_sales(supervisor_id, 'week')
            try:
                this_week_sales_percentage_rate = (((sales_this_week['total_sales'] / sales_last_week['total_sales']) * 100) - 100)
            except ZeroDivisionError:
                this_week_sales_percentage_rate = 0

            sales_last_month = SupervisorDashboardService.compute_sales(supervisor_id, 'last_month')
            sales_this_month = SupervisorDashboardService.compute_sales(supervisor_id, 'month')
            try:
                this_month_sales_percentage_rate = (((sales_this_month['total_sales'] / sales_last_month['total_sales']) * 100) - 100)
            except ZeroDivisionError:
                this_month_sales_percentage_rate = 0

            # Get agent activity rate
            # agent_activity = SupervisorDashboardService.compute_agent_activity_rate(supervisor_id)
            agent_activity = SupervisorDashboardService.calculate_agent_status(supervisor_id)

            # Get target achievement
            target_achievement = SupervisorDashboardService.compute_target_achievement(supervisor_id, 'month')
            # Get earnings
            earnings = SupervisorDashboardService.compute_earnings(supervisor_id, 'month')
            print(f"HERE'S YOUR RESPONSE |=>|=>|=>|=>|=>|=>|=>|=>|=> \n\n\n AT LAST 👍 \n\n\n")
            # # Get terminal counts
            # supervisor = Supervisor.objects.get(id=supervisor_id)
            # agents = Agent.objects.filter(supervisor=supervisor)
            # terminals = Terminal.objects.filter(agent__in=agents)

            # Update status for all terminals
            # for terminal in terminals:
            #     SupervisorDashboardService.update_terminal_status(terminal.id)

            # active_terminals = terminals.filter(status='active').count()
            # inactive_terminals = terminals.filter(status='inactive').count()
            # absent_terminals = terminals.filter(status='absent').count()

            return {
                'sales_today': sales_today['total_sales'],
                'today_sales_percentage_rate': today_sales_percentage_rate,
                'sales_this_week': sales_this_week['total_sales'],
                'this_week_sales_percentage_rate': this_week_sales_percentage_rate,
                'sales_this_month': sales_this_month['total_sales'],
                'this_month_sales_percentage_rate': this_month_sales_percentage_rate,
                # 'agent_activity_rate': agent_activity['activity_rate'],
                # 'total_agents': agent_activity['total_agents'],
                'agent_activity': 0.0,
                'agent_activity_values': {
                    'number_of_absent_agents': agent_activity['number_of_absent_agents'],
                    'number_of_active_agents': agent_activity['number_of_active_agents'],
                    'number_of_at_risk_agents': agent_activity['number_of_at_risk_agents'],
                    'number_of_declining_agents': agent_activity['number_of_declining_agents'],
                    'number_of_inactive_agents': agent_activity['number_of_inactive_agents']
                    # 'absent_agents': agent_activity['list_of_absent_agents'],
                    # 'active_agents': agent_activity['list_of_active_agents'],
                    # 'at_risk_agents': agent_activity['list_of_at_risk_agents'],
                    # 'declining_agents': agent_activity['list_of_declining_agents'],
                    # 'inactive_agents': agent_activity['list_of_inactive_agents'],
                },
                'target_achievement_percent': target_achievement['target_achievement_percent'],
                'total_agent_count': target_achievement['agent_count'],
                'earnings': earnings['total_commission'],
                # 'active_terminals': active_terminals,
                # 'inactive_terminals': inactive_terminals,
                # 'absent_terminals': absent_terminals
            }
        except Supervisor.DoesNotExist:
            raise Supervisor.DoesNotExist(f"Supervisor with ID {supervisor_id} does not exist")
