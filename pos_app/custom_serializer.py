from dataclasses import dataclass

from rest_framework import status

from main.api.exceptions.execptions import CustomErrorValidation
from pos_app.models import AgentConstantVariables
from prices.game_price import InstantCashOutPriceModel, SalaryForLifePriceModel
from collections import Counter

@dataclass
class CustomLottoSerializer:
    data: dict

    def is_valid(self, lottery_type, raise_exception=False):
        if raise_exception:
            _data_keys = self.data.keys()
            fields = self.serilaizing_fields()

            error_response = {}

            for key in fields.keys():
                if key not in _data_keys:
                    if key == "phone_number":
                        if AgentConstantVariables().get_phone_number_is_required() is True:
                            error_response[key] = "This field is required"
                        else:
                            pass
                    else:
                        error_response[key] = "This field is required"

                if self.data.get(key) is None or self.data.get(key) == "":
                    print(f"Here >>>>>>>>>>>>>>>>>>>>>>>>>>>> {self.data.get(key)}")

                    if key == "phone_number":
                        if AgentConstantVariables().get_phone_number_is_required() is False:
                            pass
                        else:
                            error_response[key] = "This field is required"
                    else:
                        error_response[key] = "This field is required"

                if not isinstance(self.data.get("lottery"), list):
                    error_response["lottery"] = "This field is required as list field"

                if isinstance(self.data.get("lottery"), list):
                    for i in self.data.get("lottery"):
                        if i.get("ticket") is None or i.get("ticket") == "":
                            error_response["lottery"] = "a ticket field is required in the lottery field as child field"

                    if lottery_type.upper() == "SALARY_FOR_LIFE":
                        # line restriction
                        line_restriction = AgentConstantVariables.get_salary_for_life_line_restriction()
                        if len(self.data.get("lottery")) > line_restriction:
                            error_response[key] = f"sorry, you cannot play more than {line_restriction} lines"

                        

                        for i in self.data.get("lottery"):
                            if i.get("ticket") is not None and i.get("ticket") != "":
                                if len(i.get("ticket")) != 5:
                                    error_response["lottery"] = "ticket field must be 5 characters long"

                        
                            # check if the ticket appeared more than 3 times
                            counter = Counter(i.get("ticket"))
                            
                            duplicates = {num: count for num, count in counter.items() if count > 1}
                            if duplicates:
                                # error_response["lottery"] = "You cannot play the same number more than 2 times"
                                error_response["lottery"] = "you cannot repate the same number more than 2 times"
                            


                        
                    
                        
                                
                    elif lottery_type.upper() == "INSTANT_CASHOUT":
                        for i in self.data.get("lottery"):
                            if i.get("ticket") is not None and i.get("ticket") != "":
                                if len(i.get("ticket")) != 4:
                                    error_response["lottery"] = "ticket field must be 4 characters long"
                if key == "amount":
                    # check if amount contains special characters
                    if not self.data.get(key).isnumeric():
                        error_response[key] = "This field must be a number"

            if error_response:
                raise CustomErrorValidation(error_response, status_code=status.HTTP_400_BAD_REQUEST)
            #    raise ValidationError(error_response)

        _validated_data = self.validated_data(lottery_type.upper())
        if _validated_data.get("status") == "error":
            raise CustomErrorValidation(_validated_data.get("message"), status_code=status.HTTP_400_BAD_REQUEST)
            # raise ValidationError(_validated_data.get("message"))

    def serilaizing_fields(self):
        fields = {
            "phone_number": "",
            "amount": "",
            "lottery": [{"ticket_number": ""}],
        }

        return fields

    def validated_data(self, lottery_type):
        # validate phone number
        if (
            AgentConstantVariables().get_phone_number_is_required() is True
            and self.data.get("phone_number") is None
            and self.data.get("phone_number") != ""
        ):
            return {"status": "error", "message": "phone number field is required"}
        if (
            AgentConstantVariables().get_phone_number_is_required() is False
            and self.data.get("phone_number") is None
            or self.data.get("phone_number") == ""
        ):
            pass
        else:
            if len(self.data.get("phone_number")) < 11:
                return {"status": "error", "message": "phone number must be 11 digits"}

            # check if phone number has special characters
            if not self.data.get("phone_number").isnumeric():
                return {
                    "status": "error",
                    "message": "phone number must be a valid phone number",
                }

        if lottery_type == "SALARY_FOR_LIFE":
            if len(self.data.get("lottery")) > 10:
                return {
                    "status": "error",
                    "message": "You can only select 10 tickets for salary for life lottery",
                }

            for i in self.data.get("lottery"):
                if len(i.get("ticket")) != 5:
                    return {
                        "status": "error",
                        "message": "ticket field must be 5 characters long",
                    }

                if not i.get("ticket"):
                    return {"status": "error", "message": "ticket field is required"}

                for j in i.get("ticket"):
                    if not isinstance(j, int):
                        return {
                            "status": "error",
                            "message": f"please change this `{j}` to a digit",
                        }
                    elif isinstance(j, int) and int(j) > 49:
                        return {
                            "status": "error",
                            "message": f"please change this `{j}` to a digit less than 50",
                        }

            # check for stake amount
            amount = int(self.data.get("amount"))
            # if (
            #     LottoTicket.salary_for_life_stake_amount_pontential_winning_for_lotto_agents(
            #         len(self.data.get("lottery"))
            #     ).get(
            #         "stake_amount"
            #     )
            #     != amount
            # ):
            #     return {"status": "error", "message": "stake amount is invalid"}

            sal4_life_price_model = SalaryForLifePriceModel()
            valid_amount = sal4_life_price_model.get_ticket_price_details_with_stake_amount(channel="POS", stake_amount=amount)

            if valid_amount is None:
                return {"status": "error", "message": "stake amount is invalid"}

        elif lottery_type == "INSTANT_CASHOUT":
            if len(self.data.get("lottery")) > 7:
                return {
                    "status": "error",
                    "message": "You can only select 7 tickets for instant cashout lottery",
                }

            for i in self.data.get("lottery"):
                if len(i.get("ticket")) != 4:
                    return {
                        "status": "error",
                        "message": "ticket field must be 4 characters long",
                    }

                if not i.get("ticket"):
                    return {"status": "error", "message": "ticket field is required"}

                for j in i.get("ticket"):
                    if not isinstance(j, int):
                        return {
                            "status": "error",
                            "message": f"please change this `{j}` to a digit",
                        }

                    elif isinstance(j, int) and int(j) > 40:
                        return {
                            "status": "error",
                            "message": f"please change this `{j}` to a digit less than 40",
                        }

            # check for stake amount
            amount = int(self.data.get("amount"))
            # print("amount ---------------------------->", amount)
            # valid_amount = I_CASH_UPDATED_AMOUNT.get(amount, None)

            instant_cashout_price_model = InstantCashOutPriceModel()
            valid_amount = instant_cashout_price_model.get_ticket_price_details_with_stake_amount(channel="POS", stake_amount=amount)

            if valid_amount is None:
                return {"status": "error", "message": "stake amount is invalid"}

            # if (
            #     LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(
            #         len(self.data.get("lottery"))
            #     ).get(
            #         "stake_amount"
            #     )
            #     != amount
            # ):
            #     return {"status": "error", "message": "stake amount is invalid"}

        return {"status": "success", "message": "validated successfully"}
