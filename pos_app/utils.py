import calendar
import os
import random
import string
from dataclasses import dataclass
from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path
from typing import Optional

import pandas as pd
from django.conf import settings
from django.db.models import Subquery, Sum

remittance_email_list = settings.REMITTANCE_EMAIL_LIST


def serialize_ticket(number_list):
    return ",".join(map(str, map(int, number_list)))


def unique_code_generator(size=6, chars=string.digits):
    return "".join(random.choice(chars) for char in range(size))


def make_file(report_category: str, report_type: str, file_name: str):
    """
    Returns:
        - The file path & the file.
    """
    file_path = os.path.join(settings.BASE_DIR, f"media/{report_category}/{report_type}_report")
    file = f"{file_path}/{file_name}"
    return {"file_path": file_path, "file": file}


class AgentsRemittanceReport:
    """
    Generates Daily, Weekly, & Monthly Remittance Report for Lotto agent(s).
    """

    @staticmethod
    def process_games_sales_and_winnings(
        awoof_sales,
        lotto_ticket_sales,
        lotto_winners,
        soccer_cash_sales,
        soccer_cash_winners,
        wyse_cash_sales,
        wyse_cash_winners,
    ) -> dict:
        """
        This function accepts querysets as params.
        Returns:
            - game(s) sales & winnings.
            - total sales & total winnings.
        """
        # SalaryForLife sale(s).
        salary_for_life_sales = lotto_ticket_sales.filter(lottery_type="SALARY_FOR_LIFE")
        sales_salary_for_life = salary_for_life_sales.aggregate(amount=Sum("amount_paid"))["amount"]
        total_sales_salary_for_life = sales_salary_for_life if sales_salary_for_life is not None else 0.0
        # SalaryForLife winning(s).
        salary_for_life_winnings = lotto_winners.filter(game_play_id__in=Subquery(salary_for_life_sales.values("game_play_id")))
        winnings_salary_for_life = salary_for_life_winnings.aggregate(amount=Sum("earning"))["amount"]
        total_winnings_salary_for_life = winnings_salary_for_life if winnings_salary_for_life is not None else 0.0
        # InstantCashout sale(s).
        instant_cashout_sales = lotto_ticket_sales.filter(lottery_type="INSTANT_CASHOUT")
        sales_instant_cashout = instant_cashout_sales.aggregate(amount=Sum("amount_paid"))["amount"]
        total_sales_instant_cashout = sales_instant_cashout if sales_instant_cashout is not None else 0.0
        # InstantCashout winnings(s).
        instant_cashout_winnings = lotto_winners.filter(game_play_id__in=Subquery(instant_cashout_sales.values("game_play_id")))
        winnings_instant_cashout = instant_cashout_winnings.aggregate(amount=Sum("earning"))["amount"]
        total_winnings_instant_cashout = winnings_instant_cashout if winnings_instant_cashout is not None else 0.0
        # Quika sale(s).
        quika_sales = lotto_ticket_sales.filter(lottery_type="QUIKA")
        sales_quika = quika_sales.aggregate(amount=Sum("amount_paid"))["amount"]
        total_sales_quika = sales_quika if sales_quika is not None else 0.0
        # Quika winning(s).
        quika_winnings = lotto_winners.filter(game_play_id__in=Subquery(quika_sales.values("game_play_id")))
        winnings_quika = quika_winnings.aggregate(amount=Sum("earning"))["amount"]
        total_winnings_quika = winnings_quika if winnings_quika is not None else 0.0
        # VirtualSoccer sale(s).
        virtual_soccer_sales = lotto_ticket_sales.filter(lottery_type="VIRTUAL_SOCCER")
        sales_virtual_soccer = virtual_soccer_sales.aggregate(amount=Sum("amount_paid"))["amount"]
        total_sales_virtual_soccer = sales_virtual_soccer if sales_virtual_soccer is not None else 0.0
        # VirtualSoccer winning(s)
        virtual_soccer_winnings = lotto_winners.filter(game_play_id__in=Subquery(virtual_soccer_sales.values("game_play_id")))
        winnings_virtual_soccer = virtual_soccer_winnings.aggregate(amount=Sum("earning"))["amount"]
        total_winnings_virtual_soccer = winnings_virtual_soccer if winnings_virtual_soccer is not None else 0.0
        # Banker sale(s).
        banker_sales = lotto_ticket_sales.filter(lottery_type="BANKER")
        sales_banker = banker_sales.aggregate(amount=Sum("amount_paid"))["amount"]
        total_sales_banker = sales_banker if sales_banker is not None else 0.0
        # Banker winning(s).
        banker_winnings = lotto_winners.filter(game_play_id__in=Subquery(banker_sales.values("game_play_id")))
        winnings_banker = banker_winnings.aggregate(amount=Sum("earning"))["amount"]
        total_winnings_banker = winnings_banker if winnings_banker is not None else 0.0
        # SoccerCash sale(s).
        sales_soccer_cash = soccer_cash_sales.aggregate(amount=Sum("amount_paid"))["amount"]
        total_sales_soccer_cash = sales_soccer_cash if sales_soccer_cash is not None else 0.0
        # Soccer Cash winning(s).
        winnings_soccer_cash = soccer_cash_winners.aggregate(amount=Sum("earning"))["amount"]
        total_winnings_soccer_cash = winnings_soccer_cash if winnings_soccer_cash is not None else 0.0
        # WyseCash sale(s).
        sales_wyse_cash = wyse_cash_sales.aggregate(amount=Sum("amount_paid"))["amount"]
        total_sales_wyse_cash = sales_wyse_cash if sales_wyse_cash is not None else 0.0
        # WyseCash winning(s).
        winnings_wyse_cash = wyse_cash_winners.aggregate(amount=Sum("earning"))["amount"]
        total_winnings_wyse_cash = winnings_wyse_cash if winnings_wyse_cash is not None else 0.0
        # Awoof sale(s).
        sales_awoof = awoof_sales.aggregate(amount=Sum("amount_paid"))["amount"]
        total_sales_awoof = sales_awoof if sales_awoof is not None else 0.0
        # Awoof winning(s).
        total_winnings_awoof = 0.0

        total_sales = sum(
            [
                total_sales_salary_for_life,
                total_sales_instant_cashout,
                total_sales_quika,
                total_sales_virtual_soccer,
                total_sales_banker,
                total_sales_soccer_cash,
                total_sales_wyse_cash,
                total_sales_awoof,
            ]
        )
        total_winnings = sum(
            [
                total_winnings_salary_for_life,
                total_winnings_instant_cashout,
                total_winnings_quika,
                total_winnings_virtual_soccer,
                total_winnings_banker,
                total_winnings_soccer_cash,
                total_winnings_wyse_cash,
                total_winnings_awoof,
            ]
        )
        return {
            "total_sales_salary_for_life": total_sales_salary_for_life,
            "total_winnings_salary_for_life": total_winnings_salary_for_life,
            "total_sales_instant_cashout": total_sales_instant_cashout,
            "total_winnings_instant_cashout": total_winnings_instant_cashout,
            "total_sales_quika": total_sales_quika,
            "total_winnings_quika": total_winnings_quika,
            "total_sales_virtual_soccer": total_sales_virtual_soccer,
            "total_winnings_virtual_soccer": total_winnings_virtual_soccer,
            "total_sales_banker": total_sales_banker,
            "total_winnings_banker": total_winnings_banker,
            "total_sales_soccer_cash": total_sales_soccer_cash,
            "total_winnings_soccer_cash": total_winnings_soccer_cash,
            "total_sales_wyse_cash": total_sales_wyse_cash,
            "total_winnings_wyse_cash": total_winnings_wyse_cash,
            "total_sales_awoof": total_sales_awoof,
            "total_winnings_awoof": total_winnings_awoof,
            "total_sales": total_sales,
            "total_winnings": total_winnings,
        }

    @staticmethod
    def generate_report(report_type: str, start_date: object, end_date: Optional[object] = None):
        from awoof_app.models import AwoofGameTable
        from main.models import (
            LotteryModel,
            LotteryWinnersTable,
            LottoTicket,
            LottoWinners,
        )
        from main.ussd.helpers import Utility
        from pos_app.models import Agent, LottoAgentRemittanceTable
        from pos_app.tasks import email_sender
        from sport_app.models import SoccerCashWinner
        from wyse_ussd.models import SoccerPrediction

        agents = Agent.objects.filter(
            agent_type="LOTTO_AGENT",
            terminal_id__isnull=False,
            terminal_retrieved=False,
        )
        total_agents = agents.count()
        total_win_wise_agents = agents.filter(is_winwise_staff_agent=True).count()
        remittance = LottoAgentRemittanceTable.objects.all()
        all_due_remittance = remittance.filter(due=True)

        if report_type == "Daily":
            daily_remittance = remittance.filter(
                created_at__date=start_date,
            ).values(
                "agent",
                "amount",
                "amount_paid",
                "remitted",
                "due",
                "days_due",
                "created_at",
            )
            daily_due_remittance = daily_remittance.filter(due=True)

            lotto_ticket_sales = LottoTicket.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date=start_date,
            )
            lotto_winners = LottoWinners.objects.filter(
                game_play_id__in=Subquery(lotto_ticket_sales.values("game_play_id")),
            )
            soccer_cash_sales = SoccerPrediction.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date=start_date,
            )
            soccer_cash_winners = SoccerCashWinner.objects.filter(
                game_play_id__in=Subquery(soccer_cash_sales.values("game_id")),
            )
            wyse_cash_sales = LotteryModel.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date=start_date,
            )
            wyse_cash_winners = LotteryWinnersTable.objects.filter(
                game_play_id__in=Subquery(wyse_cash_sales.values("game_play_id")),
            )
            awoof_sales = AwoofGameTable.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date_created__date=start_date,
            )
            # Process all game(s) sales & winnings.
            get_games_sales_and_winnings = AgentsRemittanceReport.process_games_sales_and_winnings(
                lotto_ticket_sales=lotto_ticket_sales,
                lotto_winners=lotto_winners,
                soccer_cash_sales=soccer_cash_sales,
                soccer_cash_winners=soccer_cash_winners,
                wyse_cash_sales=wyse_cash_sales,
                wyse_cash_winners=wyse_cash_winners,
                awoof_sales=awoof_sales,
            )
            total_sales_salary_for_life = get_games_sales_and_winnings.get("total_sales_salary_for_life")
            total_winnings_salary_for_life = get_games_sales_and_winnings.get("total_winnings_salary_for_life")
            total_sales_instant_cashout = get_games_sales_and_winnings.get("total_sales_instant_cashout")
            total_winnings_instant_cashout = get_games_sales_and_winnings.get("total_winnings_instant_cashout")
            total_sales_quika = get_games_sales_and_winnings.get("total_sales_quika")
            total_winnings_quika = get_games_sales_and_winnings.get("total_winnings_quika")
            total_sales_virtual_soccer = get_games_sales_and_winnings.get("total_sales_virtual_soccer")
            total_winnings_virtual_soccer = get_games_sales_and_winnings.get("total_winnings_virtual_soccer")
            total_sales_banker = get_games_sales_and_winnings.get("total_sales_banker")
            total_winnings_banker = get_games_sales_and_winnings.get("total_winnings_banker")
            total_sales_soccer_cash = get_games_sales_and_winnings.get("total_sales_soccer_cash")
            total_winnings_soccer_cash = get_games_sales_and_winnings.get("total_winnings_soccer_cash")
            total_sales_wyse_cash = get_games_sales_and_winnings.get("total_sales_wyse_cash")
            total_winnings_wyse_cash = get_games_sales_and_winnings.get("total_winnings_wyse_cash")
            total_sales_awoof = get_games_sales_and_winnings.get("total_sales_awoof")
            total_winnings_awoof = get_games_sales_and_winnings.get("total_winnings_awoof")

            total_sales = get_games_sales_and_winnings.get("total_sales")
            total_winnings = get_games_sales_and_winnings.get("total_winnings")

            if daily_remittance.exists():
                report = []
                # Format data and get agent detail(s).
                for data in daily_remittance:
                    try:
                        agent = agents.get(id=data.get("agent"))
                    except Agent.DoesNotExist:
                        agent = None

                    formatted_data = {
                        "agent": agent.name if agent is not None else None,
                        "agent phone": agent.phone if agent is not None else None,
                        "amount to remit": data.get("amount"),
                        "amount remitted": data.get("amount_paid"),
                        "has remitted": data.get("remitted"),
                        "is due": data.get("due"),
                        "days due": data.get("days_due"),
                        "date": data.get("created_at").date(),
                    }
                    report.append(formatted_data)
                    # Report computation(s).
                    daily_remit_value = daily_remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    daily_remitted_value = daily_remittance.aggregate(amount=Sum("amount_paid"))["amount"] or 0.0
                    daily_due_value = daily_due_remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    total_remit_value = remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    total_due_value = all_due_remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    percentage_due = (total_due_value / total_remit_value) * 100

                # Create file to be saved.
                file_name = f"{start_date}_report.xlsx"
                create_file_directory = make_file(
                    report_category="remittance",
                    report_type="daily",
                    file_name=file_name,
                )
                file_path = create_file_directory.get("file_path")
                file = create_file_directory.get("file")
                # Check directory exists and write to file.
                df = pd.DataFrame.from_records(report)
                if os.path.exists(file_path):
                    df.to_excel(file, index=False)

                    for email in remittance_email_list:
                        email_sender(
                            recipient=[email],
                            subject=f"""
Lotto Agents Remittance - Daily Report for {calendar.day_name[start_date.weekday()]} [{start_date}]
                            """,
                            template_dir="report.html",
                            file=file,
                            file_name=file_name,
                            remittance=Utility.currency_formatter(daily_remit_value),
                            remitted=Utility.currency_formatter(daily_remitted_value),
                            report_type="Daily",
                            due=Utility.currency_formatter(daily_due_value),
                            all_due=Utility.currency_formatter(total_due_value),
                            all_remittance=Utility.currency_formatter(total_remit_value),
                            percentage_due=f"{Utility.generate_round_figure(decimal=percentage_due)} percent",
                            sales=Utility.currency_formatter(total_sales),
                            winnings=Utility.currency_formatter(total_winnings),
                            total_sales_salary_for_life=Utility.currency_formatter(total_sales_salary_for_life),
                            total_sales_instant_cashout=Utility.currency_formatter(total_sales_instant_cashout),
                            total_sales_quika=Utility.currency_formatter(total_sales_quika),
                            total_sales_virtual_soccer=Utility.currency_formatter(total_sales_virtual_soccer),
                            total_sales_banker=Utility.currency_formatter(total_sales_banker),
                            total_sales_soccer_cash=Utility.currency_formatter(total_sales_soccer_cash),
                            total_sales_wyse_cash=Utility.currency_formatter(total_sales_wyse_cash),
                            total_sales_awoof=Utility.currency_formatter(total_sales_awoof),
                            total_winnings_salary_for_life=Utility.currency_formatter(total_winnings_salary_for_life),
                            total_winnings_instant_cashout=Utility.currency_formatter(total_winnings_instant_cashout),
                            total_winnings_quika=Utility.currency_formatter(total_winnings_quika),
                            total_winnings_virtual_soccer=Utility.currency_formatter(total_winnings_virtual_soccer),
                            total_winnings_banker=Utility.currency_formatter(total_winnings_banker),
                            total_winnings_soccer_cash=Utility.currency_formatter(total_winnings_soccer_cash),
                            total_winnings_wyse_cash=Utility.currency_formatter(total_winnings_wyse_cash),
                            total_winnings_awoof=Utility.currency_formatter(total_winnings_awoof),
                            number_of_agents=total_agents,
                            win_wise_agents=total_win_wise_agents,
                        )
                else:
                    # Create directory for file(s).
                    directory = Path(file_path)
                    directory.mkdir(parents=True, exist_ok=True)

                    df.to_excel(file, index=False)

                    for email in remittance_email_list:
                        email_sender(
                            recipient=[email],
                            subject=f"""
Lotto Agents Remittance - Daily Report for {calendar.day_name[start_date.weekday()]} [{start_date}]
                            """,
                            template_dir="report.html",
                            file=file,
                            file_name=file_name,
                            remittance=Utility.currency_formatter(daily_remit_value),
                            remitted=Utility.currency_formatter(daily_remitted_value),
                            report_type="Daily",
                            due=Utility.currency_formatter(daily_due_value),
                            all_due=Utility.currency_formatter(total_due_value),
                            all_remittance=Utility.currency_formatter(total_remit_value),
                            percentage_due=f"{Utility.generate_round_figure(decimal=percentage_due)} percent",
                            sales=Utility.currency_formatter(total_sales),
                            winnings=Utility.currency_formatter(total_winnings),
                            total_sales_salary_for_life=Utility.currency_formatter(total_sales_salary_for_life),
                            total_sales_instant_cashout=Utility.currency_formatter(total_sales_instant_cashout),
                            total_sales_quika=Utility.currency_formatter(total_sales_quika),
                            total_sales_virtual_soccer=Utility.currency_formatter(total_sales_virtual_soccer),
                            total_sales_banker=Utility.currency_formatter(total_sales_banker),
                            total_sales_soccer_cash=Utility.currency_formatter(total_sales_soccer_cash),
                            total_sales_wyse_cash=Utility.currency_formatter(total_sales_wyse_cash),
                            total_sales_awoof=Utility.currency_formatter(total_sales_awoof),
                            total_winnings_salary_for_life=Utility.currency_formatter(total_winnings_salary_for_life),
                            total_winnings_instant_cashout=Utility.currency_formatter(total_winnings_instant_cashout),
                            total_winnings_quika=Utility.currency_formatter(total_winnings_quika),
                            total_winnings_virtual_soccer=Utility.currency_formatter(total_winnings_virtual_soccer),
                            total_winnings_banker=Utility.currency_formatter(total_winnings_banker),
                            total_winnings_soccer_cash=Utility.currency_formatter(total_winnings_soccer_cash),
                            total_winnings_wyse_cash=Utility.currency_formatter(total_winnings_wyse_cash),
                            total_winnings_awoof=Utility.currency_formatter(total_winnings_awoof),
                            number_of_agents=total_agents,
                            win_wise_agents=total_win_wise_agents,
                        )
            else:
                return f"NO REMITTANCE WAS MADE ON {start_date}."

        if report_type == "Weekly":
            range_end_date = end_date + timedelta(days=1)

            weekly_remittance = remittance.filter(
                created_at__date__range=[start_date, range_end_date],
            ).values(
                "agent",
                "amount",
                "amount_paid",
                "remitted",
                "due",
                "days_due",
                "created_at",
            )
            weekly_due_remittance = weekly_remittance.filter(due=True)

            lotto_ticket_sales = LottoTicket.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            lotto_winners = LottoWinners.objects.filter(
                game_play_id__in=Subquery(lotto_ticket_sales.values("game_play_id")),
            )
            soccer_cash_sales = SoccerPrediction.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            soccer_cash_winners = SoccerCashWinner.objects.filter(
                game_play_id__in=Subquery(soccer_cash_sales.values("game_id")),
            )
            wyse_cash_sales = LotteryModel.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            wyse_cash_winners = LotteryWinnersTable.objects.filter(
                game_play_id__in=Subquery(wyse_cash_sales.values("game_play_id")),
            )
            awoof_sales = AwoofGameTable.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date_created__date__range=[start_date, range_end_date],
            )

            # Process all game(s) sales & winnings.
            get_games_sales_and_winnings = AgentsRemittanceReport.process_games_sales_and_winnings(
                lotto_ticket_sales=lotto_ticket_sales,
                lotto_winners=lotto_winners,
                soccer_cash_sales=soccer_cash_sales,
                soccer_cash_winners=soccer_cash_winners,
                wyse_cash_sales=wyse_cash_sales,
                wyse_cash_winners=wyse_cash_winners,
                awoof_sales=awoof_sales,
            )
            total_sales_salary_for_life = get_games_sales_and_winnings.get("total_sales_salary_for_life")
            total_winnings_salary_for_life = get_games_sales_and_winnings.get("total_winnings_salary_for_life")
            total_sales_instant_cashout = get_games_sales_and_winnings.get("total_sales_instant_cashout")
            total_winnings_instant_cashout = get_games_sales_and_winnings.get("total_winnings_instant_cashout")
            total_sales_quika = get_games_sales_and_winnings.get("total_sales_quika")
            total_winnings_quika = get_games_sales_and_winnings.get("total_winnings_quika")
            total_sales_virtual_soccer = get_games_sales_and_winnings.get("total_sales_virtual_soccer")
            total_winnings_virtual_soccer = get_games_sales_and_winnings.get("total_winnings_virtual_soccer")
            total_sales_banker = get_games_sales_and_winnings.get("total_sales_banker")
            total_winnings_banker = get_games_sales_and_winnings.get("total_winnings_banker")
            total_sales_soccer_cash = get_games_sales_and_winnings.get("total_sales_soccer_cash")
            total_winnings_soccer_cash = get_games_sales_and_winnings.get("total_winnings_soccer_cash")
            total_sales_wyse_cash = get_games_sales_and_winnings.get("total_sales_wyse_cash")
            total_winnings_wyse_cash = get_games_sales_and_winnings.get("total_winnings_wyse_cash")
            total_sales_awoof = get_games_sales_and_winnings.get("total_sales_awoof")
            total_winnings_awoof = get_games_sales_and_winnings.get("total_winnings_awoof")

            total_sales = get_games_sales_and_winnings.get("total_sales")
            total_winnings = get_games_sales_and_winnings.get("total_winnings")

            if weekly_remittance.exists():
                report = []

                # Format data and get agent detail(s).
                for data in weekly_remittance:
                    try:
                        agent = agents.get(id=data.get("agent"))
                    except Agent.DoesNotExist:
                        agent = None

                    formatted_data = {
                        "agent": agent.name if agent is not None else None,
                        "agent phone": agent.phone if agent is not None else None,
                        "amount to remit": data.get("amount"),
                        "amount remitted": data.get("amount_paid"),
                        "has remitted": data.get("remitted"),
                        "is due": data.get("due"),
                        "days due": data.get("days_due"),
                        "date": data.get("created_at").date(),
                    }
                    report.append(formatted_data)
                    # Report computation(s).
                    weekly_remit_value = weekly_remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    weekly_remitted_value = weekly_remittance.aggregate(amount=Sum("amount_paid"))["amount"] or 0.0
                    weekly_due_value = weekly_due_remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    total_remit_value = remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    total_due_value = all_due_remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    percentage_due = (total_due_value / total_remit_value) * 100

                # Create file to be saved.
                file_name = f"{start_date}_{end_date}_report.xlsx"
                create_file_directory = make_file(
                    report_category="remittance",
                    report_type="weekly",
                    file_name=file_name,
                )
                file_path = create_file_directory.get("file_path")
                file = create_file_directory.get("file")
                # Check directory exists and write to file.
                df = pd.DataFrame.from_records(report)
                if os.path.exists(file_path):
                    df.to_excel(file, index=False)

                    for email in remittance_email_list:
                        email_sender(
                            recipient=[email],
                            subject=f"""
Lotto Agents Remittance - Weekly Report for \
{calendar.day_name[start_date.weekday()]} to {calendar.day_name[end_date.weekday()]} [{start_date} to {end_date}]
                            """,
                            template_dir="report.html",
                            file=file,
                            file_name=file_name,
                            remittance=Utility.currency_formatter(weekly_remit_value),
                            remitted=Utility.currency_formatter(weekly_remitted_value),
                            report_type="Weekly",
                            due=Utility.currency_formatter(weekly_due_value),
                            all_due=Utility.currency_formatter(total_due_value),
                            all_remittance=Utility.currency_formatter(total_remit_value),
                            percentage_due=f"{Utility.generate_round_figure(decimal=percentage_due)} percent",
                            sales=Utility.currency_formatter(total_sales),
                            winnings=Utility.currency_formatter(total_winnings),
                            total_sales_salary_for_life=Utility.currency_formatter(total_sales_salary_for_life),
                            total_sales_instant_cashout=Utility.currency_formatter(total_sales_instant_cashout),
                            total_sales_quika=Utility.currency_formatter(total_sales_quika),
                            total_sales_virtual_soccer=Utility.currency_formatter(total_sales_virtual_soccer),
                            total_sales_banker=Utility.currency_formatter(total_sales_banker),
                            total_sales_soccer_cash=Utility.currency_formatter(total_sales_soccer_cash),
                            total_sales_wyse_cash=Utility.currency_formatter(total_sales_wyse_cash),
                            total_sales_awoof=Utility.currency_formatter(total_sales_awoof),
                            total_winnings_salary_for_life=Utility.currency_formatter(total_winnings_salary_for_life),
                            total_winnings_instant_cashout=Utility.currency_formatter(total_winnings_instant_cashout),
                            total_winnings_quika=Utility.currency_formatter(total_winnings_quika),
                            total_winnings_virtual_soccer=Utility.currency_formatter(total_winnings_virtual_soccer),
                            total_winnings_banker=Utility.currency_formatter(total_winnings_banker),
                            total_winnings_soccer_cash=Utility.currency_formatter(total_winnings_soccer_cash),
                            total_winnings_wyse_cash=Utility.currency_formatter(total_winnings_wyse_cash),
                            total_winnings_awoof=Utility.currency_formatter(total_winnings_awoof),
                            number_of_agents=total_agents,
                            win_wise_agents=total_win_wise_agents,
                        )
                else:
                    # Create directory for file(s).
                    directory = Path(file_path)
                    directory.mkdir(parents=True, exist_ok=True)

                    df.to_excel(file, index=False)

                    for email in remittance_email_list:
                        email_sender(
                            recipient=[email],
                            subject=f"""
Lotto Agents Remittance - Weekly Report for \
{calendar.day_name[start_date.weekday()]} to {calendar.day_name[end_date.weekday()]} [{start_date} to {end_date}]
                            """,
                            template_dir="report.html",
                            file=file,
                            file_name=file_name,
                            remittance=Utility.currency_formatter(weekly_remit_value),
                            remitted=Utility.currency_formatter(weekly_remitted_value),
                            report_type="Weekly",
                            due=Utility.currency_formatter(weekly_due_value),
                            all_due=Utility.currency_formatter(total_due_value),
                            all_remittance=Utility.currency_formatter(total_remit_value),
                            percentage_due=f"{Utility.generate_round_figure(decimal=percentage_due)} percent",
                            sales=Utility.currency_formatter(total_sales),
                            winnings=Utility.currency_formatter(total_winnings),
                            total_sales_salary_for_life=Utility.currency_formatter(total_sales_salary_for_life),
                            total_sales_instant_cashout=Utility.currency_formatter(total_sales_instant_cashout),
                            total_sales_quika=Utility.currency_formatter(total_sales_quika),
                            total_sales_virtual_soccer=Utility.currency_formatter(total_sales_virtual_soccer),
                            total_sales_banker=Utility.currency_formatter(total_sales_banker),
                            total_sales_soccer_cash=Utility.currency_formatter(total_sales_soccer_cash),
                            total_sales_wyse_cash=Utility.currency_formatter(total_sales_wyse_cash),
                            total_sales_awoof=Utility.currency_formatter(total_sales_awoof),
                            total_winnings_salary_for_life=Utility.currency_formatter(total_winnings_salary_for_life),
                            total_winnings_instant_cashout=Utility.currency_formatter(total_winnings_instant_cashout),
                            total_winnings_quika=Utility.currency_formatter(total_winnings_quika),
                            total_winnings_virtual_soccer=Utility.currency_formatter(total_winnings_virtual_soccer),
                            total_winnings_banker=Utility.currency_formatter(total_winnings_banker),
                            total_winnings_soccer_cash=Utility.currency_formatter(total_winnings_soccer_cash),
                            total_winnings_wyse_cash=Utility.currency_formatter(total_winnings_wyse_cash),
                            total_winnings_awoof=Utility.currency_formatter(total_winnings_awoof),
                            number_of_agents=total_agents,
                            win_wise_agents=total_win_wise_agents,
                        )
            else:
                return f"NO REMITTANCE WAS MADE BETWEEN {start_date} - {end_date}"

        if report_type == "Monthly":
            range_end_date = end_date + timedelta(days=1)

            monthly_remittance = remittance.filter(
                created_at__date__range=[start_date, range_end_date],
            ).values(
                "agent",
                "amount",
                "amount_paid",
                "remitted",
                "due",
                "days_due",
                "created_at",
            )
            monthly_due_remittance = monthly_remittance.filter(due=True)

            lotto_ticket_sales = LottoTicket.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            lotto_winners = LottoWinners.objects.filter(
                channel_played_from="POS_AGENT",
                date_won__date__range=[start_date, range_end_date],
            )
            soccer_cash_sales = SoccerPrediction.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            soccer_cash_winners = SoccerCashWinner.objects.filter(
                game_play_id__in=Subquery(soccer_cash_sales.values("game_id")),
                date_won__date__range=[start_date, range_end_date],
            )
            wyse_cash_sales = LotteryModel.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            wyse_cash_winners = LotteryWinnersTable.objects.filter(
                game_play_id__in=Subquery(wyse_cash_sales.values("game_play_id")),
                date_won__date__range=[start_date, range_end_date],
            )
            awoof_sales = AwoofGameTable.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date_created__date__range=[start_date, range_end_date],
            )

            # Process all game(s) sales & winnings.
            get_games_sales_and_winnings = AgentsRemittanceReport.process_games_sales_and_winnings(
                lotto_ticket_sales=lotto_ticket_sales,
                lotto_winners=lotto_winners,
                soccer_cash_sales=soccer_cash_sales,
                soccer_cash_winners=soccer_cash_winners,
                wyse_cash_sales=wyse_cash_sales,
                wyse_cash_winners=wyse_cash_winners,
                awoof_sales=awoof_sales,
            )
            total_sales_salary_for_life = get_games_sales_and_winnings.get("total_sales_salary_for_life")
            total_winnings_salary_for_life = get_games_sales_and_winnings.get("total_winnings_salary_for_life")
            total_sales_instant_cashout = get_games_sales_and_winnings.get("total_sales_instant_cashout")
            total_winnings_instant_cashout = get_games_sales_and_winnings.get("total_winnings_instant_cashout")
            total_sales_quika = get_games_sales_and_winnings.get("total_sales_quika")
            total_winnings_quika = get_games_sales_and_winnings.get("total_winnings_quika")
            total_sales_virtual_soccer = get_games_sales_and_winnings.get("total_sales_virtual_soccer")
            total_winnings_virtual_soccer = get_games_sales_and_winnings.get("total_winnings_virtual_soccer")
            total_sales_banker = get_games_sales_and_winnings.get("total_sales_banker")
            total_winnings_banker = get_games_sales_and_winnings.get("total_winnings_banker")
            total_sales_soccer_cash = get_games_sales_and_winnings.get("total_sales_soccer_cash")
            total_winnings_soccer_cash = get_games_sales_and_winnings.get("total_winnings_soccer_cash")
            total_sales_wyse_cash = get_games_sales_and_winnings.get("total_sales_wyse_cash")
            total_winnings_wyse_cash = get_games_sales_and_winnings.get("total_winnings_wyse_cash")
            total_sales_awoof = get_games_sales_and_winnings.get("total_sales_awoof")
            total_winnings_awoof = get_games_sales_and_winnings.get("total_winnings_awoof")

            total_sales = get_games_sales_and_winnings.get("total_sales")
            total_winnings = get_games_sales_and_winnings.get("total_winnings")

            if monthly_remittance.exists():
                report = []

                # Format data and get agent detail(s).
                for data in monthly_remittance:
                    try:
                        agent = agents.get(id=data.get("agent"))
                    except Agent.DoesNotExist:
                        agent = None

                    formatted_data = {
                        "agent": agent.name if agent is not None else None,
                        "agent phone": agent.phone if agent is not None else None,
                        "amount to remit": data.get("amount"),
                        "amount remitted": data.get("amount_paid"),
                        "has remitted": data.get("remitted"),
                        "is due": data.get("due"),
                        "days due": data.get("days_due"),
                        "date": data.get("created_at").date(),
                    }
                    report.append(formatted_data)

                    # Report computation(s).
                    monthly_remit = monthly_remittance.aggregate(Sum("amount"))
                    monthly_remit_value = monthly_remit["amount__sum"] if not None else 0.0
                    monthly_remitted = monthly_remittance.aggregate(Sum("amount_paid"))
                    monthly_remitted_value = monthly_remitted["amount_paid__sum"] if not None else 0.0
                    monthly_due_value = monthly_due_remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    total_remit_value = remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    total_due_value = all_due_remittance.aggregate(amount=Sum("amount"))["amount"] or 0.0
                    percentage_due = (total_due_value / total_remit_value) * 100

                # Create file to be saved.
                file_name = f"{start_date}_{end_date}_report.xlsx"
                create_file_directory = make_file(
                    report_category="remittance",
                    report_type="monthly",
                    file_name=file_name,
                )
                file_path = create_file_directory.get("file_path")
                file = create_file_directory.get("file")
                # Check directory exists and write to file.
                df = pd.DataFrame.from_records(report)
                if os.path.exists(file_path):
                    df.to_excel(file, index=False)

                    for email in remittance_email_list:
                        email_sender(
                            recipient=[email],
                            subject=f"""
Lotto Agents Remittance - Monthly Report for {calendar.month_name[start_date.month]} [{start_date} to {end_date}]
                            """,
                            template_dir="report.html",
                            file=file,
                            file_name=file_name,
                            remittance=Utility.currency_formatter(monthly_remit_value),
                            remitted=Utility.currency_formatter(monthly_remitted_value),
                            report_type="Monthly",
                            due=Utility.currency_formatter(monthly_due_value),
                            all_due=Utility.currency_formatter(total_due_value),
                            all_remittance=Utility.currency_formatter(total_remit_value),
                            percentage_due=f"{Utility.generate_round_figure(decimal=percentage_due)} percent",
                            sales=Utility.currency_formatter(total_sales),
                            winnings=Utility.currency_formatter(total_winnings),
                            total_sales_salary_for_life=Utility.currency_formatter(total_sales_salary_for_life),
                            total_sales_instant_cashout=Utility.currency_formatter(total_sales_instant_cashout),
                            total_sales_quika=Utility.currency_formatter(total_sales_quika),
                            total_sales_virtual_soccer=Utility.currency_formatter(total_sales_virtual_soccer),
                            total_sales_banker=Utility.currency_formatter(total_sales_banker),
                            total_sales_soccer_cash=Utility.currency_formatter(total_sales_soccer_cash),
                            total_sales_wyse_cash=Utility.currency_formatter(total_sales_wyse_cash),
                            total_sales_awoof=Utility.currency_formatter(total_sales_awoof),
                            total_winnings_salary_for_life=Utility.currency_formatter(total_winnings_salary_for_life),
                            total_winnings_instant_cashout=Utility.currency_formatter(total_winnings_instant_cashout),
                            total_winnings_quika=Utility.currency_formatter(total_winnings_quika),
                            total_winnings_virtual_soccer=Utility.currency_formatter(total_winnings_virtual_soccer),
                            total_winnings_banker=Utility.currency_formatter(total_winnings_banker),
                            total_winnings_soccer_cash=Utility.currency_formatter(total_winnings_soccer_cash),
                            total_winnings_wyse_cash=Utility.currency_formatter(total_winnings_wyse_cash),
                            total_winnings_awoof=Utility.currency_formatter(total_winnings_awoof),
                            number_of_agents=total_agents,
                            win_wise_agents=total_win_wise_agents,
                        )
                else:
                    # Create directory for file(s).
                    directory = Path(file_path)
                    directory.mkdir(parents=True, exist_ok=True)

                    df.to_excel(file, index=False)

                    for email in remittance_email_list:
                        email_sender(
                            recipient=[email],
                            subject=f"""
Lotto Agents Remittance - Monthly Report for {calendar.month_name[start_date.month]} [{start_date} to {end_date}]
                            """,
                            template_dir="report.html",
                            file=file,
                            file_name=file_name,
                            remittance=Utility.currency_formatter(monthly_remit_value),
                            remitted=Utility.currency_formatter(monthly_remitted_value),
                            report_type="Monthly",
                            due=Utility.currency_formatter(monthly_due_value),
                            all_due=Utility.currency_formatter(total_due_value),
                            all_remittance=Utility.currency_formatter(total_remit_value),
                            percentage_due=f"{Utility.generate_round_figure(decimal=percentage_due)} percent",
                            sales=Utility.currency_formatter(total_sales),
                            winnings=Utility.currency_formatter(total_winnings),
                            total_sales_salary_for_life=Utility.currency_formatter(total_sales_salary_for_life),
                            total_sales_instant_cashout=Utility.currency_formatter(total_sales_instant_cashout),
                            total_sales_quika=Utility.currency_formatter(total_sales_quika),
                            total_sales_virtual_soccer=Utility.currency_formatter(total_sales_virtual_soccer),
                            total_sales_banker=Utility.currency_formatter(total_sales_banker),
                            total_sales_soccer_cash=Utility.currency_formatter(total_sales_soccer_cash),
                            total_sales_wyse_cash=Utility.currency_formatter(total_sales_wyse_cash),
                            total_sales_awoof=Utility.currency_formatter(total_sales_awoof),
                            total_winnings_salary_for_life=Utility.currency_formatter(total_winnings_salary_for_life),
                            total_winnings_instant_cashout=Utility.currency_formatter(total_winnings_instant_cashout),
                            total_winnings_quika=Utility.currency_formatter(total_winnings_quika),
                            total_winnings_virtual_soccer=Utility.currency_formatter(total_winnings_virtual_soccer),
                            total_winnings_banker=Utility.currency_formatter(total_winnings_banker),
                            total_winnings_soccer_cash=Utility.currency_formatter(total_winnings_soccer_cash),
                            total_winnings_wyse_cash=Utility.currency_formatter(total_winnings_wyse_cash),
                            total_winnings_awoof=Utility.currency_formatter(total_winnings_awoof),
                            number_of_agents=total_agents,
                            win_wise_agents=total_win_wise_agents,
                        )
            else:
                return f"NO REMITTANCE WAS MADE BETWEEN {start_date} - {end_date}"


class AgentsPerformancesReport:
    """
    Generates Daily, Weekly, & Monthly Performance/Scoreboard Report for Lotto Agent(s).
    """

    @staticmethod
    def process_agent_total_sales_and_winnings(
        agent,
        awoof_sales,
        lotto_ticket_sales,
        soccer_cash_sales,
        wyse_cash_sales,
        lotto_winners=None,
        soccer_cash_winners=None,
        wyse_cash_winners=None,
    ) -> dict:
        """
        This function accepts querysets as params.
        Returns:
            Agent's transaction details as follows:
            - number of plays
            - number of wins
            - total value of plays
            - total value of wins
        """
        # SalaryForLife sale(s).
        salary_for_life_sales = lotto_ticket_sales.filter(lottery_type="SALARY_FOR_LIFE", agent_profile=agent)
        total_sales_salary_for_life = salary_for_life_sales.aggregate(amount=Sum("amount_paid"))["amount"] or 0.0
        # SalaryForLife winning(s).
        total_salary_for_life_winnings = 0
        if lotto_winners is not None:
            total_salary_for_life_winnings = (
                lotto_winners.filter(game_play_id__in=Subquery(salary_for_life_sales.values("game_play_id"))).aggregate(amount=Sum("earning"))[
                    "amount"
                ]
                or 0.0
            )

        # InstantCashout sale(s).
        instant_cashout_sales = lotto_ticket_sales.filter(lottery_type="INSTANT_CASHOUT", agent_profile=agent)
        total_sales_instant_cashout = instant_cashout_sales.aggregate(amount=Sum("amount_paid"))["amount"] or 0.0
        # InstantCashout winnings(s).
        if lotto_winners is not None:
            total_instant_cashout_winnings = (
                lotto_winners.filter(game_play_id__in=Subquery(instant_cashout_sales.values("game_play_id"))).aggregate(amount=Sum("earning"))[
                    "amount"
                ]
                or 0.0
            )
        else:
            total_instant_cashout_winnings = 0

        # Quika sale(s).
        quika_sales = lotto_ticket_sales.filter(lottery_type="QUIKA", agent_profile=agent)
        total_sales_quika = quika_sales.aggregate(amount=Sum("amount_paid"))["amount"] or 0.0
        # Quika winning(s).
        if lotto_winners is not None:
            total_quika_winnings = (
                lotto_winners.filter(game_play_id__in=Subquery(quika_sales.values("game_play_id"))).aggregate(amount=Sum("earning"))["amount"] or 0.0
            )

        # VirtualSoccer sale(s).
        virtual_soccer_sales = lotto_ticket_sales.filter(lottery_type="VIRTUAL_SOCCER", agent_profile=agent)
        total_sales_virtual_soccer = virtual_soccer_sales.aggregate(amount=Sum("amount_paid"))["amount"] or 0.0
        # VirtualSoccer winning(s).
        if lotto_winners is not None:
            total_virtual_soccer_winnings = (
                lotto_winners.filter(game_play_id__in=Subquery(virtual_soccer_sales.values("game_play_id"))).aggregate(amount=Sum("earning"))[
                    "amount"
                ]
                or 0.0
            )

        # Banker sale(s).
        banker_sales = lotto_ticket_sales.filter(lottery_type="BANKER", agent_profile=agent)
        total_sales_banker = banker_sales.aggregate(amount=Sum("amount_paid"))["amount"] or 0.0
        # Banker winning(s).
        if lotto_winners is not None:
            total_banker_winnings = (
                lotto_winners.filter(game_play_id__in=Subquery(banker_sales.values("game_play_id"))).aggregate(amount=Sum("earning"))["amount"] or 0.0
            )

        # SoccerCash sale(s).
        soccer_cash_sales = soccer_cash_sales.filter(agent=agent)
        total_sales_soccer_cash = soccer_cash_sales.aggregate(amount=Sum("amount_paid"))["amount"] or 0.0
        # Soccer Cash winning(s).
        if soccer_cash_winners is not None:
            total_soccer_cash_winnings = (
                soccer_cash_winners.filter(game_play_id__in=Subquery(soccer_cash_sales.values("game_id"))).aggregate(amount=Sum("earning"))["amount"]
                or 0.0
            )

        # WyseCash sale(s).
        wyse_cash_sales = wyse_cash_sales.filter(agent_profile=agent)
        total_sales_wyse_cash = wyse_cash_sales.aggregate(amount=Sum("amount_paid"))["amount"] or 0.0
        # WyseCash winning(s).
        if wyse_cash_winners is not None:
            total_wyse_cash_winnings = (
                wyse_cash_winners.filter(game_play_id__in=Subquery(wyse_cash_sales.values("game_play_id"))).aggregate(amount=Sum("earning"))["amount"]
                or 0.0
            )

        # Awoof sale(s).
        awoof_sales = awoof_sales.filter(agent_profile=agent)
        total_sales_awoof = awoof_sales.aggregate(amount=Sum("amount_paid"))["amount"] or 0.0

        total_sales = sum(
            [
                total_sales_salary_for_life,
                total_sales_instant_cashout,
                total_sales_quika,
                total_sales_virtual_soccer,
                total_sales_banker,
                total_sales_soccer_cash,
                total_sales_wyse_cash,
                total_sales_awoof,
            ]
        )
        total_winnings = sum(
            [
                total_salary_for_life_winnings,
                total_instant_cashout_winnings,
                total_quika_winnings,
                total_virtual_soccer_winnings,
                total_banker_winnings,
                total_soccer_cash_winnings,
                total_wyse_cash_winnings,
            ]
        )
        return {
            "total_sales_salary_for_life": total_sales_salary_for_life,
            "total_sales_instant_cashout": total_sales_instant_cashout,
            "total_sales_quika": total_sales_quika,
            "total_sales_virtual_soccer": total_sales_virtual_soccer,
            "total_sales_banker": total_sales_banker,
            "total_sales_soccer_cash": total_sales_soccer_cash,
            "total_sales_wyse_cash": total_sales_wyse_cash,
            "total_sales_awoof": total_sales_awoof,
            "total_sales": total_sales,
            "total_winnings": total_winnings,
        }

    @staticmethod
    def generate_report(
        report_type: str,
        start_date: object,
        end_date: Optional[object] = None,
        time_threshold: Optional[int] = None,
    ):
        from awoof_app.models import AwoofGameTable
        from main.models import (
            LotteryModel,
            LotteryWinnersTable,
            LottoTicket,
            LottoWinners,
        )
        from main.ussd.helpers import Utility
        from pos_app.models import (
            Agent,
            AgentConstantVariables,
            LottoAgentRemittanceTable,
            Supervisor,
        )
        from pos_app.tasks import email_sender
        from sport_app.models import SoccerCashWinner
        from wyse_ussd.models import SoccerPrediction

        agents = Agent.objects.filter(
            agent_type="LOTTO_AGENT",
            terminal_id__isnull=False,
            terminal_retrieved=False,
        )
        remittance = LottoAgentRemittanceTable.objects.all()
        supervisors = Supervisor.objects.all()
        number_of_agents = agents.count()
        total_win_wise_agents = agents.filter(is_winwise_staff_agent=True).count()
        expected_sales_amount = AgentConstantVariables.get_expected_sales()
        global_amount = int(expected_sales_amount.get("global"))
        local_amount = int(expected_sales_amount.get("local"))

        if report_type == "Daily":
            all_expected_sales_global = number_of_agents * global_amount
            all_expected_sales_local = number_of_agents * local_amount
            win_wise_expected_sales_global = total_win_wise_agents * global_amount
            win_wise_expected_sales_local = total_win_wise_agents * local_amount

            lotto_ticket_sales = LottoTicket.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date=start_date,
            )
            lotto_winners = LottoWinners.objects.filter(
                game_play_id__in=Subquery(lotto_ticket_sales.values("game_play_id")),
            )
            soccer_cash_sales = SoccerPrediction.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date=start_date,
            )
            soccer_cash_winners = SoccerCashWinner.objects.filter(
                game_play_id__in=Subquery(soccer_cash_sales.values("game_id")),
            )
            wyse_cash_sales = LotteryModel.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date=start_date,
            )
            wyse_cash_winners = LotteryWinnersTable.objects.filter(
                game_play_id__in=Subquery(wyse_cash_sales.values("game_play_id")),
            )
            awoof_sales = AwoofGameTable.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date_created__date=start_date,
            )
            # Process total sales & winnings.
            get_games_sales_and_winnings = AgentsRemittanceReport.process_games_sales_and_winnings(
                lotto_ticket_sales=lotto_ticket_sales,
                lotto_winners=lotto_winners,
                soccer_cash_sales=soccer_cash_sales,
                soccer_cash_winners=soccer_cash_winners,
                wyse_cash_sales=wyse_cash_sales,
                wyse_cash_winners=wyse_cash_winners,
                awoof_sales=awoof_sales,
            )
            games_total_sales = get_games_sales_and_winnings.get("total_sales")
            games_total_winnings = get_games_sales_and_winnings.get("total_winnings")
            report = []
            for agent in agents:
                # All game(s) sales.
                get_agent_sales = AgentsPerformancesReport.process_agent_total_sales_and_winnings(
                    agent=agent,
                    awoof_sales=awoof_sales,
                    lotto_ticket_sales=lotto_ticket_sales,
                    lotto_winners=lotto_winners,
                    soccer_cash_sales=soccer_cash_sales,
                    soccer_cash_winners=soccer_cash_winners,
                    wyse_cash_sales=wyse_cash_sales,
                    wyse_cash_winners=wyse_cash_winners,
                )
                total_sales_salary_for_life = get_agent_sales.get("total_sales_salary_for_life")
                total_sales_instant_cashout = get_agent_sales.get("total_sales_instant_cashout")
                total_sales_quika = get_agent_sales.get("total_sales_quika")
                total_sales_virtual_soccer = get_agent_sales.get("total_sales_virtual_soccer")
                total_sales_banker = get_agent_sales.get("total_sales_banker")
                total_sales_soccer_cash = get_agent_sales.get("total_sales_soccer_cash")
                total_sales_wyse_cash = get_agent_sales.get("total_sales_wyse_cash")
                total_sales_awoof = get_agent_sales.get("total_sales_awoof")
                total_sales = get_agent_sales.get("total_sales")
                total_winnings = get_agent_sales.get("total_winnings")
                # Agent status.
                status = "ACTIVE" if total_sales != 0.0 else "INACTIVE"
                # Agent's due remittance(s).
                due_remittance = remittance.filter(agent=agent, due=True).aggregate(amount=Sum("amount"))["amount"]
                agent_due_remittance = due_remittance if due_remittance is not None else 0.0
                # Supervisor information.
                if agent.supervisor is not None:
                    supervisor = supervisors.filter(id=agent.supervisor.id).last()
                    supervisor_fullname = supervisor.full_name
                    supervisor_phone = supervisor.phone
                else:
                    supervisor_fullname = None
                    supervisor_phone = None
                # Calculate P2W for the agent.
                try:
                    play_2_win_ratio = round(((total_winnings / total_sales) * 100), 2)
                except Exception:
                    play_2_win_ratio = 0

                formatted_data = {
                    "supervisor": supervisor_fullname,
                    "supervisor phone": supervisor_phone,
                    "agent": agent.name,
                    "agent phone": agent.phone,
                    "total sales salary for life": total_sales_salary_for_life,
                    "total sales instant cashout": total_sales_instant_cashout,
                    "total sale quika": total_sales_quika,
                    "total sales virtual soccer": total_sales_virtual_soccer,
                    "total sales banker": total_sales_banker,
                    "total sales soccer cash": total_sales_soccer_cash,
                    "total sales wyse cash": total_sales_wyse_cash,
                    "total sales awoof": total_sales_awoof,
                    "total sales all games": total_sales,
                    "total winnings all games": total_winnings,
                    "agent status": status,
                    "agent due remittance": agent_due_remittance,
                    "p2w": play_2_win_ratio,
                }
                report.append(formatted_data)

            sorted_report = sorted(report, key=lambda x: x["total sales all games"], reverse=True)
            performing_agents_global = [agent for agent in sorted_report if agent.get("total sales all games") >= global_amount]
            performing_agents_local = [agent for agent in sorted_report if agent.get("total sales all games") >= local_amount]
            agents_performing_global = len(performing_agents_global)
            agents_performing_local = len(performing_agents_local)
            # Performance email list
            supervisor_emails = [supervisor.email for supervisor in supervisors if not None]
            performance_email_list = remittance_email_list + supervisor_emails
            # Create file to be saved.
            file_name = f"{start_date}_report.xlsx"
            create_file_directory = make_file(
                report_category="performance",
                report_type="daily",
                file_name=file_name,
            )
            file_path = create_file_directory.get("file_path")
            file = create_file_directory.get("file")
            # Check directory exists and write to file.
            df = pd.DataFrame.from_records(sorted_report)
            if os.path.exists(file_path):
                df.to_excel(file, index=False)

                for email in performance_email_list:
                    email_sender(
                        recipient=[email],
                        subject=f"""
Lotto Agents Performance - Daily Report for {calendar.day_name[start_date.weekday()]} [{start_date}]
                        """,
                        template_dir="performance.html",
                        file=file,
                        file_name=file_name,
                        report_type="Daily",
                        win_wise_agents=total_win_wise_agents,
                        number_of_agents=number_of_agents,
                        win_wise_expected_sales=(
                            Utility.currency_formatter(win_wise_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(win_wise_expected_sales_global)
                        ),
                        all_expected_sales=(
                            Utility.currency_formatter(all_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(all_expected_sales_global)
                        ),
                        actual_sales=Utility.currency_formatter(games_total_sales),
                        total_winnings=Utility.currency_formatter(games_total_winnings),
                        number_of_active_agents=(agents_performing_local if email not in supervisor_emails else agents_performing_global),
                        agent1=sorted_report[0].get("agent"),
                        agent1_sales=Utility.currency_formatter(sorted_report[0].get("total sales all games")),
                        agent1_play_to_win_ratio=f"{sorted_report[0].get('p2w')}%",
                        agent2=sorted_report[1].get("agent"),
                        agent2_sales=Utility.currency_formatter(sorted_report[1].get("total sales all games")),
                        agent2_play_to_win_ratio=f"{sorted_report[1].get('p2w')}%",
                        agent3=sorted_report[2].get("agent"),
                        agent3_sales=Utility.currency_formatter(sorted_report[2].get("total sales all games")),
                        agent3_play_to_win_ratio=f"{sorted_report[2].get('p2w')}%",
                        agent4=sorted_report[3].get("agent"),
                        agent4_sales=Utility.currency_formatter(sorted_report[3].get("total sales all games")),
                        agent4_play_to_win_ratio=f"{sorted_report[3].get('p2w')}%",
                        agent5=sorted_report[4].get("agent"),
                        agent5_sales=Utility.currency_formatter(sorted_report[4].get("total sales all games")),
                        agent5_play_to_win_ratio=f"{sorted_report[4].get('p2w')}%",
                        agent6=sorted_report[5].get("agent"),
                        agent6_sales=Utility.currency_formatter(sorted_report[5].get("total sales all games")),
                        agent6_play_to_win_ratio=f"{sorted_report[5].get('p2w')}%",
                        agent7=sorted_report[6].get("agent"),
                        agent7_sales=Utility.currency_formatter(sorted_report[6].get("total sales all games")),
                        agent7_play_to_win_ratio=f"{sorted_report[6].get('p2w')}%",
                        agent8=sorted_report[7].get("agent"),
                        agent8_sales=Utility.currency_formatter(sorted_report[7].get("total sales all games")),
                        agent8_play_to_win_ratio=f"{sorted_report[7].get('p2w')}%",
                        agent9=sorted_report[8].get("agent"),
                        agent9_sales=Utility.currency_formatter(sorted_report[8].get("total sales all games")),
                        agent9_play_to_win_ratio=f"{sorted_report[8].get('p2w')}%",
                        agent10=sorted_report[9].get("agent"),
                        agent10_sales=Utility.currency_formatter(sorted_report[9].get("total sales all games")),
                        agent10_play_to_win_ratio=f"{sorted_report[9].get('p2w')}%",
                    )
                return "REPORT SENT SUCCESSFULLY."
            else:
                # Create directory for file(s).
                directory = Path(file_path)
                directory.mkdir(parents=True, exist_ok=True)

                df.to_excel(file, index=False)

                for email in remittance_email_list:
                    email_sender(
                        recipient=[email],
                        subject=f"""
Lotto Agents Performance - Daily Report for {calendar.day_name[start_date.weekday()]} [{start_date}]
                        """,
                        template_dir="performance.html",
                        file=file,
                        file_name=file_name,
                        report_type="Daily",
                        win_wise_agents=total_win_wise_agents,
                        number_of_agents=number_of_agents,
                        win_wise_expected_sales=(
                            Utility.currency_formatter(win_wise_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(win_wise_expected_sales_global)
                        ),
                        all_expected_sales=(
                            Utility.currency_formatter(all_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(all_expected_sales_global)
                        ),
                        actual_sales=Utility.currency_formatter(games_total_sales),
                        total_winnings=Utility.currency_formatter(games_total_winnings),
                        number_of_active_agents=(agents_performing_local if email not in supervisor_emails else agents_performing_global),
                        agent1=sorted_report[0].get("agent"),
                        agent1_sales=Utility.currency_formatter(sorted_report[0].get("total sales all games")),
                        agent1_play_to_win_ratio=f"{sorted_report[0].get('p2w')}%",
                        agent2=sorted_report[1].get("agent"),
                        agent2_sales=Utility.currency_formatter(sorted_report[1].get("total sales all games")),
                        agent2_play_to_win_ratio=f"{sorted_report[1].get('p2w')}%",
                        agent3=sorted_report[2].get("agent"),
                        agent3_sales=Utility.currency_formatter(sorted_report[2].get("total sales all games")),
                        agent3_play_to_win_ratio=f"{sorted_report[2].get('p2w')}%",
                        agent4=sorted_report[3].get("agent"),
                        agent4_sales=Utility.currency_formatter(sorted_report[3].get("total sales all games")),
                        agent4_play_to_win_ratio=f"{sorted_report[3].get('p2w')}%",
                        agent5=sorted_report[4].get("agent"),
                        agent5_sales=Utility.currency_formatter(sorted_report[4].get("total sales all games")),
                        agent5_play_to_win_ratio=f"{sorted_report[4].get('p2w')}%",
                        agent6=sorted_report[5].get("agent"),
                        agent6_sales=Utility.currency_formatter(sorted_report[5].get("total sales all games")),
                        agent6_play_to_win_ratio=f"{sorted_report[5].get('p2w')}%",
                        agent7=sorted_report[6].get("agent"),
                        agent7_sales=Utility.currency_formatter(sorted_report[6].get("total sales all games")),
                        agent7_play_to_win_ratio=f"{sorted_report[6].get('p2w')}%",
                        agent8=sorted_report[7].get("agent"),
                        agent8_sales=Utility.currency_formatter(sorted_report[7].get("total sales all games")),
                        agent8_play_to_win_ratio=f"{sorted_report[7].get('p2w')}%",
                        agent9=sorted_report[8].get("agent"),
                        agent9_sales=Utility.currency_formatter(sorted_report[8].get("total sales all games")),
                        agent9_play_to_win_ratio=f"{sorted_report[8].get('p2w')}%",
                        agent10=sorted_report[9].get("agent"),
                        agent10_sales=Utility.currency_formatter(sorted_report[9].get("total sales all games")),
                        agent10_play_to_win_ratio=f"{sorted_report[9].get('p2w')}%",
                    )
                return "REPORT SENT SUCCESSFULLY."

        if report_type == "Weekly":
            range_end_date = end_date + timedelta(days=1)
            weekly_global_expected_sales_per_agent = global_amount * 7
            weekly_local_expected_sales_per_agent = local_amount * 7
            all_expected_sales_global = number_of_agents * weekly_global_expected_sales_per_agent
            all_expected_sales_local = number_of_agents * weekly_local_expected_sales_per_agent
            win_wise_expected_sales_global = total_win_wise_agents * weekly_global_expected_sales_per_agent
            win_wise_expected_sales_local = total_win_wise_agents * weekly_local_expected_sales_per_agent

            lotto_ticket_sales = LottoTicket.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            lotto_winners = LottoWinners.objects.filter(
                game_play_id__in=Subquery(lotto_ticket_sales.values("game_play_id")),
            )
            soccer_cash_sales = SoccerPrediction.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            soccer_cash_winners = SoccerCashWinner.objects.filter(
                game_play_id__in=Subquery(soccer_cash_sales.values("game_id")),
            )
            wyse_cash_sales = LotteryModel.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            wyse_cash_winners = LotteryWinnersTable.objects.filter(
                game_play_id__in=Subquery(wyse_cash_sales.values("game_play_id")),
            )
            awoof_sales = AwoofGameTable.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date_created__date__range=[start_date, range_end_date],
            )
            # Process total sales & winnings.
            get_games_sales_and_winnings = AgentsRemittanceReport.process_games_sales_and_winnings(
                lotto_ticket_sales=lotto_ticket_sales,
                lotto_winners=lotto_winners,
                soccer_cash_sales=soccer_cash_sales,
                soccer_cash_winners=soccer_cash_winners,
                wyse_cash_sales=wyse_cash_sales,
                wyse_cash_winners=wyse_cash_winners,
                awoof_sales=awoof_sales,
            )
            games_total_sales = get_games_sales_and_winnings.get("total_sales")
            games_total_winnings = get_games_sales_and_winnings.get("total_winnings")
            report = []

            for agent in agents:
                # All game(s) sales.
                get_agent_sales = AgentsPerformancesReport.process_agent_total_sales_and_winnings(
                    agent=agent,
                    awoof_sales=awoof_sales,
                    lotto_ticket_sales=lotto_ticket_sales,
                    lotto_winners=lotto_winners,
                    soccer_cash_sales=soccer_cash_sales,
                    soccer_cash_winners=soccer_cash_winners,
                    wyse_cash_sales=wyse_cash_sales,
                    wyse_cash_winners=wyse_cash_winners,
                )
                total_sales_salary_for_life = get_agent_sales.get("total_sales_salary_for_life")
                total_sales_instant_cashout = get_agent_sales.get("total_sales_instant_cashout")
                total_sales_quika = get_agent_sales.get("total_sales_quika")
                total_sales_virtual_soccer = get_agent_sales.get("total_sales_virtual_soccer")
                total_sales_banker = get_agent_sales.get("total_sales_banker")
                total_sales_soccer_cash = get_agent_sales.get("total_sales_soccer_cash")
                total_sales_wyse_cash = get_agent_sales.get("total_sales_wyse_cash")
                total_sales_awoof = get_agent_sales.get("total_sales_awoof")
                total_sales = get_agent_sales.get("total_sales")
                total_winnings = get_agent_sales.get("total_winnings")
                # Agent status.
                status = "ACTIVE" if total_sales != 0.0 else "INACTIVE"
                # Due remittance(s).
                due_remittance = remittance.filter(agent=agent, due=True).aggregate(amount=Sum("amount"))["amount"]
                agent_due_remittance = due_remittance if due_remittance is not None else 0.0
                # Supervisor information.
                if agent.supervisor is not None:
                    supervisor = supervisors.filter(id=agent.supervisor.id).last()
                    supervisor_fullname = supervisor.full_name
                    supervisor_phone = supervisor.phone
                else:
                    supervisor_fullname = None
                    supervisor_phone = None
                # Calculate P2W for the agent.
                try:
                    play_2_win_ratio = round(((total_winnings / total_sales) * 100), 2)
                except Exception:
                    play_2_win_ratio = 0

                formatted_data = {
                    "supervisor": supervisor_fullname,
                    "supervisor phone": supervisor_phone,
                    "agent": agent.name,
                    "agent phone": agent.phone,
                    "total sales salary for life": total_sales_salary_for_life,
                    "total sales instant cashout": total_sales_instant_cashout,
                    "total sale quika": total_sales_quika,
                    "total sales virtual soccer": total_sales_virtual_soccer,
                    "total sales banker": total_sales_banker,
                    "total sales soccer cash": total_sales_soccer_cash,
                    "total sales wyse cash": total_sales_wyse_cash,
                    "total sales awoof": total_sales_awoof,
                    "total sales all games": total_sales,
                    "total winnings all games": total_winnings,
                    "agent status": status,
                    "agent due remittance": agent_due_remittance,
                    "p2w": play_2_win_ratio,
                }
                report.append(formatted_data)

            sorted_report = sorted(report, key=lambda x: x["total sales all games"], reverse=True)
            performing_agents_global = [
                agent for agent in sorted_report if agent.get("total sales all games") >= weekly_global_expected_sales_per_agent
            ]
            performing_agents_local = [
                agent for agent in sorted_report if agent.get("total sales all games") >= weekly_local_expected_sales_per_agent
            ]
            agents_performing_global = len(performing_agents_global)
            agents_performing_local = len(performing_agents_local)
            # Performance email list
            supervisor_emails = [supervisor.email for supervisor in supervisors if not None]
            performance_email_list = remittance_email_list + supervisor_emails
            # Create file to be saved.
            file_name = f"{start_date}_{end_date}_report.xlsx"
            create_file_directory = make_file(report_category="performance", report_type="weekly", file_name=file_name)
            file_path = create_file_directory.get("file_path")
            file = create_file_directory.get("file")

            # Check directory exists and write to file.
            df = pd.DataFrame.from_records(sorted_report)
            if os.path.exists(file_path):
                df.to_excel(file, index=False)

                for email in performance_email_list:
                    email_sender(
                        recipient=[email],
                        subject=f"""
Lotto Agents Performance - Weekly Report for \
{calendar.day_name[start_date.weekday()]} to {calendar.day_name[end_date.weekday()]} [{start_date} to {end_date}]
                        """,
                        template_dir="performance.html",
                        file=file,
                        file_name=file_name,
                        report_type="Weekly",
                        win_wise_agents=total_win_wise_agents,
                        number_of_agents=number_of_agents,
                        win_wise_expected_sales=(
                            Utility.currency_formatter(win_wise_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(win_wise_expected_sales_global)
                        ),
                        all_expected_sales=(
                            Utility.currency_formatter(all_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(all_expected_sales_global)
                        ),
                        actual_sales=Utility.currency_formatter(games_total_sales),
                        total_winnings=Utility.currency_formatter(games_total_winnings),
                        number_of_active_agents=(agents_performing_local if email not in supervisor_emails else agents_performing_global),
                        agent1=sorted_report[0].get("agent"),
                        agent1_sales=Utility.currency_formatter(sorted_report[0].get("total sales all games")),
                        agent1_play_to_win_ratio=f"{sorted_report[0].get('p2w')}%",
                        agent2=sorted_report[1].get("agent"),
                        agent2_sales=Utility.currency_formatter(sorted_report[1].get("total sales all games")),
                        agent2_play_to_win_ratio=f"{sorted_report[1].get('p2w')}%",
                        agent3=sorted_report[2].get("agent"),
                        agent3_sales=Utility.currency_formatter(sorted_report[2].get("total sales all games")),
                        agent3_play_to_win_ratio=f"{sorted_report[2].get('p2w')}%",
                        agent4=sorted_report[3].get("agent"),
                        agent4_sales=Utility.currency_formatter(sorted_report[3].get("total sales all games")),
                        agent4_play_to_win_ratio=f"{sorted_report[3].get('p2w')}%",
                        agent5=sorted_report[4].get("agent"),
                        agent5_sales=Utility.currency_formatter(sorted_report[4].get("total sales all games")),
                        agent5_play_to_win_ratio=f"{sorted_report[4].get('p2w')}%",
                        agent6=sorted_report[5].get("agent"),
                        agent6_sales=Utility.currency_formatter(sorted_report[5].get("total sales all games")),
                        agent6_play_to_win_ratio=f"{sorted_report[5].get('p2w')}%",
                        agent7=sorted_report[6].get("agent"),
                        agent7_sales=Utility.currency_formatter(sorted_report[6].get("total sales all games")),
                        agent7_play_to_win_ratio=f"{sorted_report[6].get('p2w')}%",
                        agent8=sorted_report[7].get("agent"),
                        agent8_sales=Utility.currency_formatter(sorted_report[7].get("total sales all games")),
                        agent8_play_to_win_ratio=f"{sorted_report[7].get('p2w')}%",
                        agent9=sorted_report[8].get("agent"),
                        agent9_sales=Utility.currency_formatter(sorted_report[8].get("total sales all games")),
                        agent9_play_to_win_ratio=f"{sorted_report[8].get('p2w')}%",
                        agent10=sorted_report[9].get("agent"),
                        agent10_sales=Utility.currency_formatter(sorted_report[9].get("total sales all games")),
                        agent10_play_to_win_ratio=f"{sorted_report[9].get('p2w')}%",
                    )
                return "REPORT SENT SUCCESSFULLY."
            else:
                # Create directory for file(s).
                directory = Path(file_path)
                directory.mkdir(parents=True, exist_ok=True)

                df.to_excel(file, index=False)

                for email in performance_email_list:
                    email_sender(
                        recipient=[email],
                        subject=f"""
Lotto Agents Performance - Weekly Report for \
{calendar.day_name[start_date.weekday()]} to {calendar.day_name[end_date.weekday()]} [{start_date} to {end_date}]
                        """,
                        template_dir="performance.html",
                        file=file,
                        file_name=file_name,
                        report_type="Weekly",
                        win_wise_agents=total_win_wise_agents,
                        number_of_agents=number_of_agents,
                        win_wise_expected_sales=(
                            Utility.currency_formatter(win_wise_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(win_wise_expected_sales_global)
                        ),
                        all_expected_sales=(
                            Utility.currency_formatter(all_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(all_expected_sales_global)
                        ),
                        actual_sales=Utility.currency_formatter(games_total_sales),
                        total_winnings=Utility.currency_formatter(games_total_winnings),
                        number_of_active_agents=(agents_performing_local if email not in supervisor_emails else agents_performing_global),
                        agent1=sorted_report[0].get("agent"),
                        agent1_sales=Utility.currency_formatter(sorted_report[0].get("total sales all games")),
                        agent1_play_to_win_ratio=f"{sorted_report[0].get('p2w')}%",
                        agent2=sorted_report[1].get("agent"),
                        agent2_sales=Utility.currency_formatter(sorted_report[1].get("total sales all games")),
                        agent2_play_to_win_ratio=f"{sorted_report[1].get('p2w')}%",
                        agent3=sorted_report[2].get("agent"),
                        agent3_sales=Utility.currency_formatter(sorted_report[2].get("total sales all games")),
                        agent3_play_to_win_ratio=f"{sorted_report[2].get('p2w')}%",
                        agent4=sorted_report[3].get("agent"),
                        agent4_sales=Utility.currency_formatter(sorted_report[3].get("total sales all games")),
                        agent4_play_to_win_ratio=f"{sorted_report[3].get('p2w')}%",
                        agent5=sorted_report[4].get("agent"),
                        agent5_sales=Utility.currency_formatter(sorted_report[4].get("total sales all games")),
                        agent5_play_to_win_ratio=f"{sorted_report[4].get('p2w')}%",
                        agent6=sorted_report[5].get("agent"),
                        agent6_sales=Utility.currency_formatter(sorted_report[5].get("total sales all games")),
                        agent6_play_to_win_ratio=f"{sorted_report[5].get('p2w')}%",
                        agent7=sorted_report[6].get("agent"),
                        agent7_sales=Utility.currency_formatter(sorted_report[6].get("total sales all games")),
                        agent7_play_to_win_ratio=f"{sorted_report[6].get('p2w')}%",
                        agent8=sorted_report[7].get("agent"),
                        agent8_sales=Utility.currency_formatter(sorted_report[7].get("total sales all games")),
                        agent8_play_to_win_ratio=f"{sorted_report[7].get('p2w')}%",
                        agent9=sorted_report[8].get("agent"),
                        agent9_sales=Utility.currency_formatter(sorted_report[8].get("total sales all games")),
                        agent9_play_to_win_ratio=f"{sorted_report[8].get('p2w')}%",
                        agent10=sorted_report[9].get("agent"),
                        agent10_sales=Utility.currency_formatter(sorted_report[9].get("total sales all games")),
                        agent10_play_to_win_ratio=f"{sorted_report[9].get('p2w')}%",
                    )
                return "REPORT SENT SUCCESSFULLY."

        if report_type == "Monthly":
            range_end_date = end_date + timedelta(days=1)
            monthly_global_expected_sales_per_agent = global_amount * time_threshold
            monthly_local_expected_sales_per_agent = local_amount * time_threshold
            all_expected_sales_global = number_of_agents * monthly_global_expected_sales_per_agent
            all_expected_sales_local = number_of_agents * monthly_local_expected_sales_per_agent
            win_wise_expected_sales_global = total_win_wise_agents * monthly_global_expected_sales_per_agent
            win_wise_expected_sales_local = total_win_wise_agents * monthly_local_expected_sales_per_agent

            lotto_ticket_sales = LottoTicket.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            lotto_winners = LottoWinners.objects.filter(
                game_play_id__in=Subquery(lotto_ticket_sales.values("game_play_id")),
            )
            soccer_cash_sales = SoccerPrediction.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            soccer_cash_winners = SoccerCashWinner.objects.filter(
                game_play_id__in=Subquery(soccer_cash_sales.values("game_id")),
            )
            wyse_cash_sales = LotteryModel.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            wyse_cash_winners = LotteryWinnersTable.objects.filter(
                game_play_id__in=Subquery(wyse_cash_sales.values("game_play_id")),
            )
            awoof_sales = AwoofGameTable.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date_created__date__range=[start_date, range_end_date],
            )
            # Process total sales & winnings.
            get_games_sales_and_winnings = AgentsRemittanceReport.process_games_sales_and_winnings(
                lotto_ticket_sales=lotto_ticket_sales,
                lotto_winners=lotto_winners,
                soccer_cash_sales=soccer_cash_sales,
                soccer_cash_winners=soccer_cash_winners,
                wyse_cash_sales=wyse_cash_sales,
                wyse_cash_winners=wyse_cash_winners,
                awoof_sales=awoof_sales,
            )
            games_total_sales = get_games_sales_and_winnings.get("total_sales")
            games_total_winnings = get_games_sales_and_winnings.get("total_winnings")
            report = []

            for agent in agents:
                # All game(s) sales.
                get_agent_sales = AgentsPerformancesReport.process_agent_total_sales_and_winnings(
                    agent=agent,
                    awoof_sales=awoof_sales,
                    lotto_ticket_sales=lotto_ticket_sales,
                    lotto_winners=lotto_winners,
                    soccer_cash_sales=soccer_cash_sales,
                    soccer_cash_winners=soccer_cash_winners,
                    wyse_cash_sales=wyse_cash_sales,
                    wyse_cash_winners=wyse_cash_winners,
                )
                total_sales_salary_for_life = get_agent_sales.get("total_sales_salary_for_life")
                total_sales_instant_cashout = get_agent_sales.get("total_sales_instant_cashout")
                total_sales_quika = get_agent_sales.get("total_sales_quika")
                total_sales_virtual_soccer = get_agent_sales.get("total_sales_virtual_soccer")
                total_sales_banker = get_agent_sales.get("total_sales_banker")
                total_sales_soccer_cash = get_agent_sales.get("total_sales_soccer_cash")
                total_sales_wyse_cash = get_agent_sales.get("total_sales_wyse_cash")
                total_sales_awoof = get_agent_sales.get("total_sales_awoof")
                total_sales = get_agent_sales.get("total_sales")
                total_winnings = get_agent_sales.get("total_winnings")
                # Agent status.
                status = "ACTIVE" if total_sales != 0.0 else "INACTIVE"
                # Due remittance(s).
                due_remittance = remittance.filter(agent=agent, due=True).aggregate(amount=Sum("amount"))["amount"]
                agent_due_remittance = due_remittance if due_remittance is not None else 0.0
                # Supervisor information.
                if agent.supervisor is not None:
                    supervisor = supervisors.filter(id=agent.supervisor.id).last()
                    supervisor_fullname = supervisor.full_name
                    supervisor_phone = supervisor.phone
                else:
                    supervisor_fullname = None
                    supervisor_phone = None
                # Calculate P2W for the agent.
                try:
                    play_2_win_ratio = round(((total_winnings / total_sales) * 100), 2)
                except ZeroDivisionError:
                    play_2_win_ratio = 0

                formatted_data = {
                    "supervisor": supervisor_fullname,
                    "supervisor phone": supervisor_phone,
                    "agent": agent.name,
                    "agent phone": agent.phone,
                    "total sales salary for life": total_sales_salary_for_life,
                    "total sales instant cashout": total_sales_instant_cashout,
                    "total sale quika": total_sales_quika,
                    "total sales virtual soccer": total_sales_virtual_soccer,
                    "total sales banker": total_sales_banker,
                    "total sales soccer cash": total_sales_soccer_cash,
                    "total sales wyse cash": total_sales_wyse_cash,
                    "total sales awoof": total_sales_awoof,
                    "total sales all games": total_sales,
                    "total winnings all games": total_winnings,
                    "agent status": status,
                    "agent due remittance": agent_due_remittance,
                    "p2w": play_2_win_ratio,
                }
                report.append(formatted_data)

            sorted_report = sorted(report, key=lambda x: x["total sales all games"], reverse=True)
            performing_agents_global = [
                agent for agent in sorted_report if agent.get("total sales all games") >= monthly_global_expected_sales_per_agent
            ]
            performing_agents_local = [
                agent for agent in sorted_report if agent.get("total sales all games") >= monthly_local_expected_sales_per_agent
            ]
            agents_performing_global = len(performing_agents_global)
            agents_performing_local = len(performing_agents_local)
            # Performance email list
            supervisor_emails = [supervisor.email for supervisor in supervisors if not None]
            performance_email_list = remittance_email_list + supervisor_emails
            # Create file to be saved.
            file_name = f"{start_date}_{end_date}_report.xlsx"
            create_file_directory = make_file(
                report_category="performance",
                report_type="monthly",
                file_name=file_name,
            )
            file_path = create_file_directory.get("file_path")
            file = create_file_directory.get("file")

            # Check directory exists and write to file.
            df = pd.DataFrame.from_records(sorted_report)
            if os.path.exists(file_path):
                df.to_excel(file, index=False)

                for email in performance_email_list:
                    email_sender(
                        recipient=[email],
                        subject=f"""
Lotto Agents Performance - Monthly Report for {calendar.month_name[start_date.month]} [{start_date} to {end_date}]
                        """,
                        template_dir="performance.html",
                        file=file,
                        file_name=file_name,
                        report_type="Monthly",
                        win_wise_agents=total_win_wise_agents,
                        number_of_agents=number_of_agents,
                        win_wise_expected_sales=(
                            Utility.currency_formatter(win_wise_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(win_wise_expected_sales_global)
                        ),
                        all_expected_sales=(
                            Utility.currency_formatter(all_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(all_expected_sales_global)
                        ),
                        actual_sales=Utility.currency_formatter(games_total_sales),
                        total_winnings=Utility.currency_formatter(games_total_winnings),
                        number_of_active_agents=(agents_performing_local if email not in supervisor_emails else agents_performing_global),
                        agent1=sorted_report[0].get("agent"),
                        agent1_sales=Utility.currency_formatter(sorted_report[0].get("total sales all games")),
                        agent1_play_to_win_ratio=f"{sorted_report[0].get('p2w')}%",
                        agent2=sorted_report[1].get("agent"),
                        agent2_sales=Utility.currency_formatter(sorted_report[1].get("total sales all games")),
                        agent2_play_to_win_ratio=f"{sorted_report[1].get('p2w')}%",
                        agent3=sorted_report[2].get("agent"),
                        agent3_sales=Utility.currency_formatter(sorted_report[2].get("total sales all games")),
                        agent3_play_to_win_ratio=f"{sorted_report[2].get('p2w')}%",
                        agent4=sorted_report[3].get("agent"),
                        agent4_sales=Utility.currency_formatter(sorted_report[3].get("total sales all games")),
                        agent4_play_to_win_ratio=f"{sorted_report[3].get('p2w')}%",
                        agent5=sorted_report[4].get("agent"),
                        agent5_sales=Utility.currency_formatter(sorted_report[4].get("total sales all games")),
                        agent5_play_to_win_ratio=f"{sorted_report[4].get('p2w')}%",
                        agent6=sorted_report[5].get("agent"),
                        agent6_sales=Utility.currency_formatter(sorted_report[5].get("total sales all games")),
                        agent6_play_to_win_ratio=f"{sorted_report[5].get('p2w')}%",
                        agent7=sorted_report[6].get("agent"),
                        agent7_sales=Utility.currency_formatter(sorted_report[6].get("total sales all games")),
                        agent7_play_to_win_ratio=f"{sorted_report[6].get('p2w')}%",
                        agent8=sorted_report[7].get("agent"),
                        agent8_sales=Utility.currency_formatter(sorted_report[7].get("total sales all games")),
                        agent8_play_to_win_ratio=f"{sorted_report[7].get('p2w')}%",
                        agent9=sorted_report[8].get("agent"),
                        agent9_sales=Utility.currency_formatter(sorted_report[8].get("total sales all games")),
                        agent9_play_to_win_ratio=f"{sorted_report[8].get('p2w')}%",
                        agent10=sorted_report[9].get("agent"),
                        agent10_sales=Utility.currency_formatter(sorted_report[9].get("total sales all games")),
                        agent10_play_to_win_ratio=f"{sorted_report[9].get('p2w')}%",
                    )
                return "REPORT SENT SUCCESSFULLY."
            else:
                # Create directory for file(s).
                directory = Path(file_path)
                directory.mkdir(parents=True, exist_ok=True)

                df.to_excel(file, index=False)

                for email in performance_email_list:
                    email_sender(
                        recipient=[email],
                        subject=f"""
Lotto Agents Performance - Monthly Report for {calendar.month_name[start_date.month]} [{start_date} to {end_date}]
                        """,
                        template_dir="performance.html",
                        file=file,
                        file_name=file_name,
                        report_type="Monthly",
                        win_wise_agents=total_win_wise_agents,
                        number_of_agents=number_of_agents,
                        win_wise_expected_sales=(
                            Utility.currency_formatter(win_wise_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(win_wise_expected_sales_global)
                        ),
                        all_expected_sales=(
                            Utility.currency_formatter(all_expected_sales_local)
                            if email not in supervisor_emails
                            else Utility.currency_formatter(all_expected_sales_global)
                        ),
                        actual_sales=Utility.currency_formatter(games_total_sales),
                        total_winnings=Utility.currency_formatter(games_total_winnings),
                        number_of_active_agents=(agents_performing_local if email not in supervisor_emails else agents_performing_global),
                        agent1=sorted_report[0].get("agent"),
                        agent1_sales=Utility.currency_formatter(sorted_report[0].get("total sales all games")),
                        agent1_play_to_win_ratio=f"{sorted_report[0].get('p2w')}%",
                        agent2=sorted_report[1].get("agent"),
                        agent2_sales=Utility.currency_formatter(sorted_report[1].get("total sales all games")),
                        agent2_play_to_win_ratio=f"{sorted_report[1].get('p2w')}%",
                        agent3=sorted_report[2].get("agent"),
                        agent3_sales=Utility.currency_formatter(sorted_report[2].get("total sales all games")),
                        agent3_play_to_win_ratio=f"{sorted_report[2].get('p2w')}%",
                        agent4=sorted_report[3].get("agent"),
                        agent4_sales=Utility.currency_formatter(sorted_report[3].get("total sales all games")),
                        agent4_play_to_win_ratio=f"{sorted_report[3].get('p2w')}%",
                        agent5=sorted_report[4].get("agent"),
                        agent5_sales=Utility.currency_formatter(sorted_report[4].get("total sales all games")),
                        agent5_play_to_win_ratio=f"{sorted_report[4].get('p2w')}%",
                        agent6=sorted_report[5].get("agent"),
                        agent6_sales=Utility.currency_formatter(sorted_report[5].get("total sales all games")),
                        agent6_play_to_win_ratio=f"{sorted_report[5].get('p2w')}%",
                        agent7=sorted_report[6].get("agent"),
                        agent7_sales=Utility.currency_formatter(sorted_report[6].get("total sales all games")),
                        agent7_play_to_win_ratio=f"{sorted_report[6].get('p2w')}%",
                        agent8=sorted_report[7].get("agent"),
                        agent8_sales=Utility.currency_formatter(sorted_report[7].get("total sales all games")),
                        agent8_play_to_win_ratio=f"{sorted_report[7].get('p2w')}%",
                        agent9=sorted_report[8].get("agent"),
                        agent9_sales=Utility.currency_formatter(sorted_report[8].get("total sales all games")),
                        agent9_play_to_win_ratio=f"{sorted_report[8].get('p2w')}%",
                        agent10=sorted_report[9].get("agent"),
                        agent10_sales=Utility.currency_formatter(sorted_report[9].get("total sales all games")),
                        agent10_play_to_win_ratio=f"{sorted_report[9].get('p2w')}%",
                    )
                return "REPORT SENT SUCCESSFULLY."

    @staticmethod
    def generate_super_agent_report(
        report_type: str,
        start_date: object,
        end_date: Optional[object] = None,
        time_threshold: Optional[int] = None,
    ):
        from awoof_app.models import AwoofGameTable
        from main.models import LotteryModel, LottoTicket
        from main.ussd.helpers import Utility
        from pos_app.models import (
            Agent,
            AgentConstantVariables,
            LottoAgentRemittanceTable,
            LottoSuperAgents,
        )
        from pos_app.tasks import email_sender
        from wyse_ussd.models import SoccerPrediction

        super_agents = LottoSuperAgents.objects.all()
        expected_sales_amount = AgentConstantVariables.get_expected_sales()
        global_amount = int(expected_sales_amount.get("global"))
        remittance = LottoAgentRemittanceTable.objects.all()

        if report_type == "Daily":
            lotto_ticket_sales = LottoTicket.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date=start_date,
            )
            soccer_cash_sales = SoccerPrediction.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date=start_date,
            )
            wyse_cash_sales = LotteryModel.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date=start_date,
            )
            awoof_sales = AwoofGameTable.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date_created__date=start_date,
            )
            # Identify individual super agent and generate associated agent report.
            report = []

            for super_agent in super_agents:
                if super_agent.email is None:
                    pass

                # Retrieve super-agent agents.
                agents = Agent.objects.filter(
                    super_agent=super_agent,
                    agent_type="LOTTO_AGENT",
                    terminal_id__isnull=False,
                    terminal_retrieved=False,
                )
                if not agents.exists():
                    pass

                total_agents = agents.count()
                all_expected_sales = global_amount * total_agents
                actual_agents_sales = 0

                for agent in agents:
                    # All game(s) sales.
                    get_agent_sales = AgentsPerformancesReport.process_agent_total_sales_and_winnings(
                        agent=agent,
                        lotto_ticket_sales=lotto_ticket_sales,
                        soccer_cash_sales=soccer_cash_sales,
                        wyse_cash_sales=wyse_cash_sales,
                        awoof_sales=awoof_sales,
                    )
                    total_sales_salary_for_life = get_agent_sales.get("total_sales_salary_for_life")
                    total_sales_instant_cashout = get_agent_sales.get("total_sales_instant_cashout")
                    total_sales_quika = get_agent_sales.get("total_sales_quika")
                    total_sales_virtual_soccer = get_agent_sales.get("total_sales_virtual_soccer")
                    total_sales_banker = get_agent_sales.get("total_sales_banker")
                    total_sales_soccer_cash = get_agent_sales.get("total_sales_soccer_cash")
                    total_sales_wyse_cash = get_agent_sales.get("total_sales_wyse_cash")
                    total_sales_awoof = get_agent_sales.get("total_sales_awoof")
                    total_sales = get_agent_sales.get("total_sales")

                    # Actual sales for all agents
                    actual_agents_sales += total_sales
                    # Agent status.
                    status = "ACTIVE" if total_sales != 0.0 else "INACTIVE"
                    # Due remittance(s).
                    due_remittance = remittance.filter(agent=agent, due=True).aggregate(amount=Sum("amount"))["amount"]
                    agent_due_remittance = due_remittance if due_remittance is not None else 0.0
                    formatted_data = {
                        "agent": agent.name,
                        "agent phone": agent.phone,
                        "total sales salary for life": total_sales_salary_for_life,
                        "total sales instant cashout": total_sales_instant_cashout,
                        "total sale quika": total_sales_quika,
                        "total sales virtual soccer": total_sales_virtual_soccer,
                        "total sales banker": total_sales_banker,
                        "total sales soccer cash": total_sales_soccer_cash,
                        "total sales wyse cash": total_sales_wyse_cash,
                        "total sales awoof": total_sales_awoof,
                        "total sales all games": total_sales,
                        "agent status": status,
                        "agent due remittance": agent_due_remittance,
                    }
                    report.append(formatted_data)

                sorted_report = sorted(report, key=lambda x: x["total sales all games"], reverse=True)
                top_ten_agents = sorted_report[:10]
                bottom_ten_agents = sorted_report[-10:]
                performing_agents = [agent for agent in sorted_report if agent.get("total sales all games") >= global_amount]
                # Create file to be saved.
                file_name = f"{start_date}_report.xlsx"
                create_file_directory = make_file(
                    report_category="super_agents",
                    report_type="daily",
                    file_name=file_name,
                )
                file_path = create_file_directory.get("file_path")
                file = create_file_directory.get("file")
                # Check directory exists and write to file.
                df = pd.DataFrame.from_records(sorted_report)
                if os.path.exists(file_path):
                    df.to_excel(file, index=False)

                    email_sender(
                        recipient=[super_agent.email],
                        subject=f"""
    Lotto Agents Performance - Daily Report for {calendar.day_name[start_date.weekday()]} [{start_date}]
                        """,
                        template_dir="super_agents.html",
                        file=file,
                        file_name=file_name,
                        report_type="Daily",
                        number_of_agents=total_agents,
                        agents_expected_sales=Utility.currency_formatter(global_amount),
                        all_expected_sales=Utility.currency_formatter(all_expected_sales),
                        number_of_active_agents=len(performing_agents),
                        actual_sales=actual_agents_sales,
                        agent1=(top_ten_agents[0].get("agent") if 0 <= 0 < len(top_ten_agents) else None),
                        agent1_sales=(
                            Utility.currency_formatter(top_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(top_ten_agents) else None
                        ),
                        agent2=(top_ten_agents[1].get("agent") if 0 <= 1 < len(top_ten_agents) else None),
                        agent2_sales=(
                            Utility.currency_formatter(top_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(top_ten_agents) else None
                        ),
                        agent3=(top_ten_agents[2].get("agent") if 0 <= 2 < len(top_ten_agents) else None),
                        agent3_sales=(
                            Utility.currency_formatter(top_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(top_ten_agents) else None
                        ),
                        agent4=(top_ten_agents[3].get("agent") if 0 <= 3 < len(top_ten_agents) else None),
                        agent4_sales=(
                            Utility.currency_formatter(top_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(top_ten_agents) else None
                        ),
                        agent5=(top_ten_agents[4].get("agent") if 0 <= 4 < len(top_ten_agents) else None),
                        agent5_sales=(
                            Utility.currency_formatter(top_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(top_ten_agents) else None
                        ),
                        agent6=(top_ten_agents[5].get("agent") if 0 <= 5 < len(top_ten_agents) else None),
                        agent6_sales=(
                            Utility.currency_formatter(top_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(top_ten_agents) else None
                        ),
                        agent7=(top_ten_agents[6].get("agent") if 0 <= 6 < len(top_ten_agents) else None),
                        agent7_sales=(
                            Utility.currency_formatter(top_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(top_ten_agents) else None
                        ),
                        agent8=(top_ten_agents[7].get("agent") if 0 <= 7 < len(top_ten_agents) else None),
                        agent8_sales=(
                            Utility.currency_formatter(top_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(top_ten_agents) else None
                        ),
                        agent9=(top_ten_agents[8].get("agent") if 0 <= 8 < len(top_ten_agents) else None),
                        agent9_sales=(
                            Utility.currency_formatter(top_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(top_ten_agents) else None
                        ),
                        agent10=(top_ten_agents[9].get("agent") if 0 <= 9 < len(top_ten_agents) else None),
                        agent10_sales=(
                            Utility.currency_formatter(top_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(top_ten_agents) else None
                        ),
                        agent11=(bottom_ten_agents[0].get("agent") if 0 <= 0 < len(bottom_ten_agents) else None),
                        agent11_sales=(
                            Utility.currency_formatter(bottom_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(bottom_ten_agents) else None
                        ),
                        agent12=(bottom_ten_agents[1].get("agent") if 0 <= 1 < len(bottom_ten_agents) else None),
                        agent12_sales=(
                            Utility.currency_formatter(bottom_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(bottom_ten_agents) else None
                        ),
                        agent13=(bottom_ten_agents[2].get("agent") if 0 <= 2 < len(bottom_ten_agents) else None),
                        agent13_sales=(
                            Utility.currency_formatter(bottom_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(bottom_ten_agents) else None
                        ),
                        agent14=(bottom_ten_agents[3].get("agent") if 0 <= 3 < len(bottom_ten_agents) else None),
                        agent14_sales=(
                            Utility.currency_formatter(bottom_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(bottom_ten_agents) else None
                        ),
                        agent15=(bottom_ten_agents[4].get("agent") if 0 <= 4 < len(bottom_ten_agents) else None),
                        agent15_sales=(
                            Utility.currency_formatter(bottom_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(bottom_ten_agents) else None
                        ),
                        agent16=(bottom_ten_agents[5].get("agent") if 0 <= 5 < len(bottom_ten_agents) else None),
                        agent16_sales=(
                            Utility.currency_formatter(bottom_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(bottom_ten_agents) else None
                        ),
                        agent17=(bottom_ten_agents[6].get("agent") if 0 <= 6 < len(bottom_ten_agents) else None),
                        agent17_sales=(
                            Utility.currency_formatter(bottom_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(bottom_ten_agents) else None
                        ),
                        agent18=(bottom_ten_agents[7].get("agent") if 0 <= 7 < len(bottom_ten_agents) else None),
                        agent18_sales=(
                            Utility.currency_formatter(bottom_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(bottom_ten_agents) else None
                        ),
                        agent19=(bottom_ten_agents[8].get("agent") if 0 <= 8 < len(bottom_ten_agents) else None),
                        agent19_sales=(
                            Utility.currency_formatter(bottom_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(bottom_ten_agents) else None
                        ),
                        agent20=(bottom_ten_agents[9].get("agent") if 0 <= 9 < len(bottom_ten_agents) else None),
                        agent20_sales=(
                            Utility.currency_formatter(bottom_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(bottom_ten_agents) else None
                        ),
                    )
                else:
                    # Create directory for file(s).
                    directory = Path(file_path)
                    directory.mkdir(parents=True, exist_ok=True)

                    df.to_excel(file, index=False)

                    email_sender(
                        recipient=[super_agent.email],
                        subject=f"""
    Lotto Agents Performance - Daily Report for {calendar.day_name[start_date.weekday()]} [{start_date}]
                        """,
                        template_dir="super_agents.html",
                        file=file,
                        file_name=file_name,
                        report_type="Daily",
                        number_of_agents=total_agents,
                        agents_expected_sales=Utility.currency_formatter(global_amount),
                        all_expected_sales=Utility.currency_formatter(all_expected_sales),
                        number_of_active_agents=len(performing_agents),
                        actual_sales=actual_agents_sales,
                        agent1=(top_ten_agents[0].get("agent") if 0 <= 0 < len(top_ten_agents) else None),
                        agent1_sales=(
                            Utility.currency_formatter(top_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(top_ten_agents) else None
                        ),
                        agent2=(top_ten_agents[1].get("agent") if 0 <= 1 < len(top_ten_agents) else None),
                        agent2_sales=(
                            Utility.currency_formatter(top_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(top_ten_agents) else None
                        ),
                        agent3=(top_ten_agents[2].get("agent") if 0 <= 2 < len(top_ten_agents) else None),
                        agent3_sales=(
                            Utility.currency_formatter(top_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(top_ten_agents) else None
                        ),
                        agent4=(top_ten_agents[3].get("agent") if 0 <= 3 < len(top_ten_agents) else None),
                        agent4_sales=(
                            Utility.currency_formatter(top_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(top_ten_agents) else None
                        ),
                        agent5=(top_ten_agents[4].get("agent") if 0 <= 4 < len(top_ten_agents) else None),
                        agent5_sales=(
                            Utility.currency_formatter(top_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(top_ten_agents) else None
                        ),
                        agent6=(top_ten_agents[5].get("agent") if 0 <= 5 < len(top_ten_agents) else None),
                        agent6_sales=(
                            Utility.currency_formatter(top_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(top_ten_agents) else None
                        ),
                        agent7=(top_ten_agents[6].get("agent") if 0 <= 6 < len(top_ten_agents) else None),
                        agent7_sales=(
                            Utility.currency_formatter(top_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(top_ten_agents) else None
                        ),
                        agent8=(top_ten_agents[7].get("agent") if 0 <= 7 < len(top_ten_agents) else None),
                        agent8_sales=(
                            Utility.currency_formatter(top_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(top_ten_agents) else None
                        ),
                        agent9=(top_ten_agents[8].get("agent") if 0 <= 8 < len(top_ten_agents) else None),
                        agent9_sales=(
                            Utility.currency_formatter(top_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(top_ten_agents) else None
                        ),
                        agent10=(top_ten_agents[9].get("agent") if 0 <= 9 < len(top_ten_agents) else None),
                        agent10_sales=(
                            Utility.currency_formatter(top_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(top_ten_agents) else None
                        ),
                        agent11=(bottom_ten_agents[0].get("agent") if 0 <= 0 < len(bottom_ten_agents) else None),
                        agent11_sales=(
                            Utility.currency_formatter(bottom_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(bottom_ten_agents) else None
                        ),
                        agent12=(bottom_ten_agents[1].get("agent") if 0 <= 1 < len(bottom_ten_agents) else None),
                        agent12_sales=(
                            Utility.currency_formatter(bottom_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(bottom_ten_agents) else None
                        ),
                        agent13=(bottom_ten_agents[2].get("agent") if 0 <= 2 < len(bottom_ten_agents) else None),
                        agent13_sales=(
                            Utility.currency_formatter(bottom_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(bottom_ten_agents) else None
                        ),
                        agent14=(bottom_ten_agents[3].get("agent") if 0 <= 3 < len(bottom_ten_agents) else None),
                        agent14_sales=(
                            Utility.currency_formatter(bottom_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(bottom_ten_agents) else None
                        ),
                        agent15=(bottom_ten_agents[4].get("agent") if 0 <= 4 < len(bottom_ten_agents) else None),
                        agent15_sales=(
                            Utility.currency_formatter(bottom_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(bottom_ten_agents) else None
                        ),
                        agent16=(bottom_ten_agents[5].get("agent") if 0 <= 5 < len(bottom_ten_agents) else None),
                        agent16_sales=(
                            Utility.currency_formatter(bottom_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(bottom_ten_agents) else None
                        ),
                        agent17=(bottom_ten_agents[6].get("agent") if 0 <= 6 < len(bottom_ten_agents) else None),
                        agent17_sales=(
                            Utility.currency_formatter(bottom_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(bottom_ten_agents) else None
                        ),
                        agent18=(bottom_ten_agents[7].get("agent") if 0 <= 7 < len(bottom_ten_agents) else None),
                        agent18_sales=(
                            Utility.currency_formatter(bottom_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(bottom_ten_agents) else None
                        ),
                        agent19=(bottom_ten_agents[8].get("agent") if 0 <= 8 < len(bottom_ten_agents) else None),
                        agent19_sales=(
                            Utility.currency_formatter(bottom_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(bottom_ten_agents) else None
                        ),
                        agent20=(bottom_ten_agents[9].get("agent") if 0 <= 9 < len(bottom_ten_agents) else None),
                        agent20_sales=(
                            Utility.currency_formatter(bottom_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(bottom_ten_agents) else None
                        ),
                    )
            return "REPORT SENT SUCCESSFULLY."

        if report_type == "Weekly":
            range_end_date = end_date + timedelta(days=1)
            weekly_expected_sales_per_agent = global_amount * 7

            lotto_ticket_sales = LottoTicket.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            soccer_cash_sales = SoccerPrediction.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            wyse_cash_sales = LotteryModel.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            awoof_sales = AwoofGameTable.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date_created__date__range=[start_date, range_end_date],
            )
            # Identify individual super agent and generate associated agent report.
            report = []

            for super_agent in super_agents:
                if super_agent.email is None:
                    pass

                # Retrieve super-agent agents.
                agents = Agent.objects.filter(
                    super_agent=super_agent,
                    agent_type="LOTTO_AGENT",
                    terminal_id__isnull=False,
                    terminal_retrieved=False,
                )
                if not agents.exists():
                    pass

                total_agents = agents.count()
                all_expected_sales = weekly_expected_sales_per_agent * total_agents
                actual_agents_sales = 0

                for agent in agents:
                    # All game(s) sales.
                    get_agent_sales = AgentsPerformancesReport.process_agent_total_sales_and_winnings(
                        agent=agent,
                        lotto_ticket_sales=lotto_ticket_sales,
                        soccer_cash_sales=soccer_cash_sales,
                        wyse_cash_sales=wyse_cash_sales,
                        awoof_sales=awoof_sales,
                    )
                    total_sales_salary_for_life = get_agent_sales.get("total_sales_salary_for_life")
                    total_sales_instant_cashout = get_agent_sales.get("total_sales_instant_cashout")
                    total_sales_quika = get_agent_sales.get("total_sales_quika")
                    total_sales_virtual_soccer = get_agent_sales.get("total_sales_virtual_soccer")
                    total_sales_banker = get_agent_sales.get("total_sales_banker")
                    total_sales_soccer_cash = get_agent_sales.get("total_sales_soccer_cash")
                    total_sales_wyse_cash = get_agent_sales.get("total_sales_wyse_cash")
                    total_sales_awoof = get_agent_sales.get("total_sales_awoof")
                    total_sales = get_agent_sales.get("total_sales")

                    # Actual sales for all agents
                    actual_agents_sales += total_sales
                    # Agent status.
                    status = "ACTIVE" if total_sales != 0.0 else "INACTIVE"
                    # Due remittance(s).
                    due_remittance = remittance.filter(agent=agent, due=True).aggregate(amount=Sum("amount"))["amount"]
                    agent_due_remittance = due_remittance if due_remittance is not None else 0.0
                    formatted_data = {
                        "agent": agent.name,
                        "agent phone": agent.phone,
                        "total sales salary for life": total_sales_salary_for_life,
                        "total sales instant cashout": total_sales_instant_cashout,
                        "total sale quika": total_sales_quika,
                        "total sales virtual soccer": total_sales_virtual_soccer,
                        "total sales banker": total_sales_banker,
                        "total sales soccer cash": total_sales_soccer_cash,
                        "total sales wyse cash": total_sales_wyse_cash,
                        "total sales awoof": total_sales_awoof,
                        "total sales all games": total_sales,
                        "agent status": status,
                        "agent due remittance": agent_due_remittance,
                    }
                    report.append(formatted_data)

                sorted_report = sorted(report, key=lambda x: x["total sales all games"], reverse=True)
                top_ten_agents = sorted_report[:10]
                bottom_ten_agents = sorted_report[-10:]
                performing_agents = [agent for agent in sorted_report if agent.get("total sales all games") >= weekly_expected_sales_per_agent]
                # Create file to be saved.
                file_name = f"{start_date}_report.xlsx"
                create_file_directory = make_file(
                    report_category="super_agents",
                    report_type="weekly",
                    file_name=file_name,
                )
                file_path = create_file_directory.get("file_path")
                file = create_file_directory.get("file")
                # Check directory exists and write to file.
                df = pd.DataFrame.from_records(sorted_report)
                if os.path.exists(file_path):
                    df.to_excel(file, index=False)

                    email_sender(
                        recipient=[super_agent.email],
                        subject=f"""
Lotto Agents Performance - Weekly Report for \
{calendar.day_name[start_date.weekday()]} to {calendar.day_name[end_date.weekday()]} [{start_date} to {end_date}]
                        """,
                        template_dir="super_agents.html",
                        file=file,
                        file_name=file_name,
                        report_type="Weekly",
                        number_of_agents=total_agents,
                        agents_expected_sales=Utility.currency_formatter(global_amount),
                        all_expected_sales=Utility.currency_formatter(all_expected_sales),
                        number_of_active_agents=len(performing_agents),
                        actual_sales=actual_agents_sales,
                        agent1=(top_ten_agents[0].get("agent") if 0 <= 0 < len(top_ten_agents) else None),
                        agent1_sales=(
                            Utility.currency_formatter(top_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(top_ten_agents) else None
                        ),
                        agent2=(top_ten_agents[1].get("agent") if 0 <= 1 < len(top_ten_agents) else None),
                        agent2_sales=(
                            Utility.currency_formatter(top_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(top_ten_agents) else None
                        ),
                        agent3=(top_ten_agents[2].get("agent") if 0 <= 2 < len(top_ten_agents) else None),
                        agent3_sales=(
                            Utility.currency_formatter(top_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(top_ten_agents) else None
                        ),
                        agent4=(top_ten_agents[3].get("agent") if 0 <= 3 < len(top_ten_agents) else None),
                        agent4_sales=(
                            Utility.currency_formatter(top_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(top_ten_agents) else None
                        ),
                        agent5=(top_ten_agents[4].get("agent") if 0 <= 4 < len(top_ten_agents) else None),
                        agent5_sales=(
                            Utility.currency_formatter(top_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(top_ten_agents) else None
                        ),
                        agent6=(top_ten_agents[5].get("agent") if 0 <= 5 < len(top_ten_agents) else None),
                        agent6_sales=(
                            Utility.currency_formatter(top_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(top_ten_agents) else None
                        ),
                        agent7=(top_ten_agents[6].get("agent") if 0 <= 6 < len(top_ten_agents) else None),
                        agent7_sales=(
                            Utility.currency_formatter(top_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(top_ten_agents) else None
                        ),
                        agent8=(top_ten_agents[7].get("agent") if 0 <= 7 < len(top_ten_agents) else None),
                        agent8_sales=(
                            Utility.currency_formatter(top_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(top_ten_agents) else None
                        ),
                        agent9=(top_ten_agents[8].get("agent") if 0 <= 8 < len(top_ten_agents) else None),
                        agent9_sales=(
                            Utility.currency_formatter(top_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(top_ten_agents) else None
                        ),
                        agent10=(top_ten_agents[9].get("agent") if 0 <= 9 < len(top_ten_agents) else None),
                        agent10_sales=(
                            Utility.currency_formatter(top_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(top_ten_agents) else None
                        ),
                        agent11=(bottom_ten_agents[0].get("agent") if 0 <= 0 < len(bottom_ten_agents) else None),
                        agent11_sales=(
                            Utility.currency_formatter(bottom_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(bottom_ten_agents) else None
                        ),
                        agent12=(bottom_ten_agents[1].get("agent") if 0 <= 1 < len(bottom_ten_agents) else None),
                        agent12_sales=(
                            Utility.currency_formatter(bottom_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(bottom_ten_agents) else None
                        ),
                        agent13=(bottom_ten_agents[2].get("agent") if 0 <= 2 < len(bottom_ten_agents) else None),
                        agent13_sales=(
                            Utility.currency_formatter(bottom_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(bottom_ten_agents) else None
                        ),
                        agent14=(bottom_ten_agents[3].get("agent") if 0 <= 3 < len(bottom_ten_agents) else None),
                        agent14_sales=(
                            Utility.currency_formatter(bottom_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(bottom_ten_agents) else None
                        ),
                        agent15=(bottom_ten_agents[4].get("agent") if 0 <= 4 < len(bottom_ten_agents) else None),
                        agent15_sales=(
                            Utility.currency_formatter(bottom_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(bottom_ten_agents) else None
                        ),
                        agent16=(bottom_ten_agents[5].get("agent") if 0 <= 5 < len(bottom_ten_agents) else None),
                        agent16_sales=(
                            Utility.currency_formatter(bottom_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(bottom_ten_agents) else None
                        ),
                        agent17=(bottom_ten_agents[6].get("agent") if 0 <= 6 < len(bottom_ten_agents) else None),
                        agent17_sales=(
                            Utility.currency_formatter(bottom_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(bottom_ten_agents) else None
                        ),
                        agent18=(bottom_ten_agents[7].get("agent") if 0 <= 7 < len(bottom_ten_agents) else None),
                        agent18_sales=(
                            Utility.currency_formatter(bottom_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(bottom_ten_agents) else None
                        ),
                        agent19=(bottom_ten_agents[8].get("agent") if 0 <= 8 < len(bottom_ten_agents) else None),
                        agent19_sales=(
                            Utility.currency_formatter(bottom_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(bottom_ten_agents) else None
                        ),
                        agent20=(bottom_ten_agents[9].get("agent") if 0 <= 9 < len(bottom_ten_agents) else None),
                        agent20_sales=(
                            Utility.currency_formatter(bottom_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(bottom_ten_agents) else None
                        ),
                    )
                else:
                    # Create directory for file(s).
                    directory = Path(file_path)
                    directory.mkdir(parents=True, exist_ok=True)

                    df.to_excel(file, index=False)

                    email_sender(
                        recipient=[super_agent.email],
                        subject=f"""
    Lotto Agents Performance - Weekly Report for \
    {calendar.day_name[start_date.weekday()]} to {calendar.day_name[end_date.weekday()]} [{start_date} to {end_date}]
                        """,
                        template_dir="super_agents.html",
                        file=file,
                        file_name=file_name,
                        report_type="Weekly",
                        number_of_agents=total_agents,
                        agents_expected_sales=Utility.currency_formatter(global_amount),
                        all_expected_sales=Utility.currency_formatter(all_expected_sales),
                        number_of_active_agents=len(performing_agents),
                        actual_sales=actual_agents_sales,
                        agent1=(top_ten_agents[0].get("agent") if 0 <= 0 < len(top_ten_agents) else None),
                        agent1_sales=(
                            Utility.currency_formatter(top_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(top_ten_agents) else None
                        ),
                        agent2=(top_ten_agents[1].get("agent") if 0 <= 1 < len(top_ten_agents) else None),
                        agent2_sales=(
                            Utility.currency_formatter(top_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(top_ten_agents) else None
                        ),
                        agent3=(top_ten_agents[2].get("agent") if 0 <= 2 < len(top_ten_agents) else None),
                        agent3_sales=(
                            Utility.currency_formatter(top_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(top_ten_agents) else None
                        ),
                        agent4=(top_ten_agents[3].get("agent") if 0 <= 3 < len(top_ten_agents) else None),
                        agent4_sales=(
                            Utility.currency_formatter(top_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(top_ten_agents) else None
                        ),
                        agent5=(top_ten_agents[4].get("agent") if 0 <= 4 < len(top_ten_agents) else None),
                        agent5_sales=(
                            Utility.currency_formatter(top_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(top_ten_agents) else None
                        ),
                        agent6=(top_ten_agents[5].get("agent") if 0 <= 5 < len(top_ten_agents) else None),
                        agent6_sales=(
                            Utility.currency_formatter(top_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(top_ten_agents) else None
                        ),
                        agent7=(top_ten_agents[6].get("agent") if 0 <= 6 < len(top_ten_agents) else None),
                        agent7_sales=(
                            Utility.currency_formatter(top_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(top_ten_agents) else None
                        ),
                        agent8=(top_ten_agents[7].get("agent") if 0 <= 7 < len(top_ten_agents) else None),
                        agent8_sales=(
                            Utility.currency_formatter(top_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(top_ten_agents) else None
                        ),
                        agent9=(top_ten_agents[8].get("agent") if 0 <= 8 < len(top_ten_agents) else None),
                        agent9_sales=(
                            Utility.currency_formatter(top_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(top_ten_agents) else None
                        ),
                        agent10=(top_ten_agents[9].get("agent") if 0 <= 9 < len(top_ten_agents) else None),
                        agent10_sales=(
                            Utility.currency_formatter(top_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(top_ten_agents) else None
                        ),
                        agent11=(bottom_ten_agents[0].get("agent") if 0 <= 0 < len(bottom_ten_agents) else None),
                        agent11_sales=(
                            Utility.currency_formatter(bottom_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(bottom_ten_agents) else None
                        ),
                        agent12=(bottom_ten_agents[1].get("agent") if 0 <= 1 < len(bottom_ten_agents) else None),
                        agent12_sales=(
                            Utility.currency_formatter(bottom_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(bottom_ten_agents) else None
                        ),
                        agent13=(bottom_ten_agents[2].get("agent") if 0 <= 2 < len(bottom_ten_agents) else None),
                        agent13_sales=(
                            Utility.currency_formatter(bottom_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(bottom_ten_agents) else None
                        ),
                        agent14=(bottom_ten_agents[3].get("agent") if 0 <= 3 < len(bottom_ten_agents) else None),
                        agent14_sales=(
                            Utility.currency_formatter(bottom_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(bottom_ten_agents) else None
                        ),
                        agent15=(bottom_ten_agents[4].get("agent") if 0 <= 4 < len(bottom_ten_agents) else None),
                        agent15_sales=(
                            Utility.currency_formatter(bottom_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(bottom_ten_agents) else None
                        ),
                        agent16=(bottom_ten_agents[5].get("agent") if 0 <= 5 < len(bottom_ten_agents) else None),
                        agent16_sales=(
                            Utility.currency_formatter(bottom_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(bottom_ten_agents) else None
                        ),
                        agent17=(bottom_ten_agents[6].get("agent") if 0 <= 6 < len(bottom_ten_agents) else None),
                        agent17_sales=(
                            Utility.currency_formatter(bottom_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(bottom_ten_agents) else None
                        ),
                        agent18=(bottom_ten_agents[7].get("agent") if 0 <= 7 < len(bottom_ten_agents) else None),
                        agent18_sales=(
                            Utility.currency_formatter(bottom_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(bottom_ten_agents) else None
                        ),
                        agent19=(bottom_ten_agents[8].get("agent") if 0 <= 8 < len(bottom_ten_agents) else None),
                        agent19_sales=(
                            Utility.currency_formatter(bottom_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(bottom_ten_agents) else None
                        ),
                        agent20=(bottom_ten_agents[9].get("agent") if 0 <= 9 < len(bottom_ten_agents) else None),
                        agent20_sales=(
                            Utility.currency_formatter(bottom_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(bottom_ten_agents) else None
                        ),
                    )
            return "REPORT SENT SUCCESSFULLY."

        if report_type == "Monthly":
            range_end_date = end_date + timedelta(days=1)
            monthly_expected_sales_per_agent = global_amount * time_threshold

            lotto_ticket_sales = LottoTicket.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            soccer_cash_sales = SoccerPrediction.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            wyse_cash_sales = LotteryModel.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date__date__range=[start_date, range_end_date],
            )
            awoof_sales = AwoofGameTable.objects.filter(
                channel="POS_AGENT",
                paid=True,
                date_created__date__range=[start_date, range_end_date],
            )
            # Identify individual super agent and generate associated agent report.
            report = []

            for super_agent in super_agents:
                if super_agent.email is None:
                    pass

                # Retrieve super-agent agents.
                agents = Agent.objects.filter(
                    super_agent=super_agent,
                    agent_type="LOTTO_AGENT",
                    terminal_id__isnull=False,
                    terminal_retrieved=False,
                )
                if not agents.exists():
                    pass

                total_agents = agents.count()
                all_expected_sales = monthly_expected_sales_per_agent * total_agents
                actual_agents_sales = 0

                for agent in agents:
                    # All game(s) sales.
                    get_agent_sales = AgentsPerformancesReport.process_agent_total_sales_and_winnings(
                        agent=agent,
                        lotto_ticket_sales=lotto_ticket_sales,
                        soccer_cash_sales=soccer_cash_sales,
                        wyse_cash_sales=wyse_cash_sales,
                        awoof_sales=awoof_sales,
                    )
                    total_sales_salary_for_life = get_agent_sales.get("total_sales_salary_for_life")
                    total_sales_instant_cashout = get_agent_sales.get("total_sales_instant_cashout")
                    total_sales_quika = get_agent_sales.get("total_sales_quika")
                    total_sales_virtual_soccer = get_agent_sales.get("total_sales_virtual_soccer")
                    total_sales_banker = get_agent_sales.get("total_sales_banker")
                    total_sales_soccer_cash = get_agent_sales.get("total_sales_soccer_cash")
                    total_sales_wyse_cash = get_agent_sales.get("total_sales_wyse_cash")
                    total_sales_awoof = get_agent_sales.get("total_sales_awoof")
                    total_sales = get_agent_sales.get("total_sales")

                    # Actual sales for all agents
                    actual_agents_sales += total_sales
                    # Agent status.
                    status = "ACTIVE" if total_sales != 0.0 else "INACTIVE"
                    # Due remittance(s).
                    due_remittance = remittance.filter(agent=agent, due=True).aggregate(amount=Sum("amount"))["amount"]
                    agent_due_remittance = due_remittance if due_remittance is not None else 0.0
                    formatted_data = {
                        "agent": agent.name,
                        "agent phone": agent.phone,
                        "total sales salary for life": total_sales_salary_for_life,
                        "total sales instant cashout": total_sales_instant_cashout,
                        "total sale quika": total_sales_quika,
                        "total sales virtual soccer": total_sales_virtual_soccer,
                        "total sales banker": total_sales_banker,
                        "total sales soccer cash": total_sales_soccer_cash,
                        "total sales wyse cash": total_sales_wyse_cash,
                        "total sales awoof": total_sales_awoof,
                        "total sales all games": total_sales,
                        "agent status": status,
                        "agent due remittance": agent_due_remittance,
                    }
                    report.append(formatted_data)

                sorted_report = sorted(report, key=lambda x: x["total sales all games"], reverse=True)
                top_ten_agents = sorted_report[:10]
                bottom_ten_agents = sorted_report[-10:]
                performing_agents = [agent for agent in sorted_report if agent.get("total sales all games") >= monthly_expected_sales_per_agent]
                # Create file to be saved.
                file_name = f"{start_date}_report.xlsx"
                create_file_directory = make_file(
                    report_category="super_agents",
                    report_type="monthly",
                    file_name=file_name,
                )
                file_path = create_file_directory.get("file_path")
                file = create_file_directory.get("file")
                # Check directory exists and write to file.
                df = pd.DataFrame.from_records(sorted_report)
                if os.path.exists(file_path):
                    df.to_excel(file, index=False)

                    email_sender(
                        recipient=[super_agent.email],
                        subject=f"""
Lotto Agents Performance - Monthly Report for {calendar.month_name[start_date.month]} [{start_date} to {end_date}]
                        """,
                        template_dir="super_agents.html",
                        file=file,
                        file_name=file_name,
                        report_type="Monthly",
                        number_of_agents=total_agents,
                        agents_expected_sales=Utility.currency_formatter(global_amount),
                        all_expected_sales=Utility.currency_formatter(all_expected_sales),
                        number_of_active_agents=len(performing_agents),
                        actual_sales=actual_agents_sales,
                        agent1=(top_ten_agents[0].get("agent") if 0 <= 0 < len(top_ten_agents) else None),
                        agent1_sales=(
                            Utility.currency_formatter(top_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(top_ten_agents) else None
                        ),
                        agent2=(top_ten_agents[1].get("agent") if 0 <= 1 < len(top_ten_agents) else None),
                        agent2_sales=(
                            Utility.currency_formatter(top_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(top_ten_agents) else None
                        ),
                        agent3=(top_ten_agents[2].get("agent") if 0 <= 2 < len(top_ten_agents) else None),
                        agent3_sales=(
                            Utility.currency_formatter(top_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(top_ten_agents) else None
                        ),
                        agent4=(top_ten_agents[3].get("agent") if 0 <= 3 < len(top_ten_agents) else None),
                        agent4_sales=(
                            Utility.currency_formatter(top_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(top_ten_agents) else None
                        ),
                        agent5=(top_ten_agents[4].get("agent") if 0 <= 4 < len(top_ten_agents) else None),
                        agent5_sales=(
                            Utility.currency_formatter(top_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(top_ten_agents) else None
                        ),
                        agent6=(top_ten_agents[5].get("agent") if 0 <= 5 < len(top_ten_agents) else None),
                        agent6_sales=(
                            Utility.currency_formatter(top_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(top_ten_agents) else None
                        ),
                        agent7=(top_ten_agents[6].get("agent") if 0 <= 6 < len(top_ten_agents) else None),
                        agent7_sales=(
                            Utility.currency_formatter(top_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(top_ten_agents) else None
                        ),
                        agent8=(top_ten_agents[7].get("agent") if 0 <= 7 < len(top_ten_agents) else None),
                        agent8_sales=(
                            Utility.currency_formatter(top_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(top_ten_agents) else None
                        ),
                        agent9=(top_ten_agents[8].get("agent") if 0 <= 8 < len(top_ten_agents) else None),
                        agent9_sales=(
                            Utility.currency_formatter(top_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(top_ten_agents) else None
                        ),
                        agent10=(top_ten_agents[9].get("agent") if 0 <= 9 < len(top_ten_agents) else None),
                        agent10_sales=(
                            Utility.currency_formatter(top_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(top_ten_agents) else None
                        ),
                        agent11=(bottom_ten_agents[0].get("agent") if 0 <= 0 < len(bottom_ten_agents) else None),
                        agent11_sales=(
                            Utility.currency_formatter(bottom_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(bottom_ten_agents) else None
                        ),
                        agent12=(bottom_ten_agents[1].get("agent") if 0 <= 1 < len(bottom_ten_agents) else None),
                        agent12_sales=(
                            Utility.currency_formatter(bottom_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(bottom_ten_agents) else None
                        ),
                        agent13=(bottom_ten_agents[2].get("agent") if 0 <= 2 < len(bottom_ten_agents) else None),
                        agent13_sales=(
                            Utility.currency_formatter(bottom_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(bottom_ten_agents) else None
                        ),
                        agent14=(bottom_ten_agents[3].get("agent") if 0 <= 3 < len(bottom_ten_agents) else None),
                        agent14_sales=(
                            Utility.currency_formatter(bottom_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(bottom_ten_agents) else None
                        ),
                        agent15=(bottom_ten_agents[4].get("agent") if 0 <= 4 < len(bottom_ten_agents) else None),
                        agent15_sales=(
                            Utility.currency_formatter(bottom_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(bottom_ten_agents) else None
                        ),
                        agent16=(bottom_ten_agents[5].get("agent") if 0 <= 5 < len(bottom_ten_agents) else None),
                        agent16_sales=(
                            Utility.currency_formatter(bottom_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(bottom_ten_agents) else None
                        ),
                        agent17=(bottom_ten_agents[6].get("agent") if 0 <= 6 < len(bottom_ten_agents) else None),
                        agent17_sales=(
                            Utility.currency_formatter(bottom_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(bottom_ten_agents) else None
                        ),
                        agent18=(bottom_ten_agents[7].get("agent") if 0 <= 7 < len(bottom_ten_agents) else None),
                        agent18_sales=(
                            Utility.currency_formatter(bottom_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(bottom_ten_agents) else None
                        ),
                        agent19=(bottom_ten_agents[8].get("agent") if 0 <= 8 < len(bottom_ten_agents) else None),
                        agent19_sales=(
                            Utility.currency_formatter(bottom_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(bottom_ten_agents) else None
                        ),
                        agent20=(bottom_ten_agents[9].get("agent") if 0 <= 9 < len(bottom_ten_agents) else None),
                        agent20_sales=(
                            Utility.currency_formatter(bottom_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(bottom_ten_agents) else None
                        ),
                    )
                else:
                    # Create directory for file(s).
                    directory = Path(file_path)
                    directory.mkdir(parents=True, exist_ok=True)

                    df.to_excel(file, index=False)

                    email_sender(
                        recipient=[super_agent.email],
                        subject=f"""
    Lotto Agents Performance - Monthly Report for {calendar.month_name[start_date.month]} [{start_date} to {end_date}]
                        """,
                        template_dir="super_agents.html",
                        file=file,
                        file_name=file_name,
                        report_type="Monthly",
                        number_of_agents=total_agents,
                        agents_expected_sales=Utility.currency_formatter(global_amount),
                        all_expected_sales=Utility.currency_formatter(all_expected_sales),
                        number_of_active_agents=len(performing_agents),
                        actual_sales=actual_agents_sales,
                        agent1=(top_ten_agents[0].get("agent") if 0 <= 0 < len(top_ten_agents) else None),
                        agent1_sales=(
                            Utility.currency_formatter(top_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(top_ten_agents) else None
                        ),
                        agent2=(top_ten_agents[1].get("agent") if 0 <= 1 < len(top_ten_agents) else None),
                        agent2_sales=(
                            Utility.currency_formatter(top_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(top_ten_agents) else None
                        ),
                        agent3=(top_ten_agents[2].get("agent") if 0 <= 2 < len(top_ten_agents) else None),
                        agent3_sales=(
                            Utility.currency_formatter(top_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(top_ten_agents) else None
                        ),
                        agent4=(top_ten_agents[3].get("agent") if 0 <= 3 < len(top_ten_agents) else None),
                        agent4_sales=(
                            Utility.currency_formatter(top_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(top_ten_agents) else None
                        ),
                        agent5=(top_ten_agents[4].get("agent") if 0 <= 4 < len(top_ten_agents) else None),
                        agent5_sales=(
                            Utility.currency_formatter(top_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(top_ten_agents) else None
                        ),
                        agent6=(top_ten_agents[5].get("agent") if 0 <= 5 < len(top_ten_agents) else None),
                        agent6_sales=(
                            Utility.currency_formatter(top_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(top_ten_agents) else None
                        ),
                        agent7=(top_ten_agents[6].get("agent") if 0 <= 6 < len(top_ten_agents) else None),
                        agent7_sales=(
                            Utility.currency_formatter(top_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(top_ten_agents) else None
                        ),
                        agent8=(top_ten_agents[7].get("agent") if 0 <= 7 < len(top_ten_agents) else None),
                        agent8_sales=(
                            Utility.currency_formatter(top_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(top_ten_agents) else None
                        ),
                        agent9=(top_ten_agents[8].get("agent") if 0 <= 8 < len(top_ten_agents) else None),
                        agent9_sales=(
                            Utility.currency_formatter(top_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(top_ten_agents) else None
                        ),
                        agent10=(top_ten_agents[9].get("agent") if 0 <= 9 < len(top_ten_agents) else None),
                        agent10_sales=(
                            Utility.currency_formatter(top_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(top_ten_agents) else None
                        ),
                        agent11=(bottom_ten_agents[0].get("agent") if 0 <= 0 < len(bottom_ten_agents) else None),
                        agent11_sales=(
                            Utility.currency_formatter(bottom_ten_agents[0].get("total sales all games")) if 0 <= 0 < len(bottom_ten_agents) else None
                        ),
                        agent12=(bottom_ten_agents[1].get("agent") if 0 <= 1 < len(bottom_ten_agents) else None),
                        agent12_sales=(
                            Utility.currency_formatter(bottom_ten_agents[1].get("total sales all games")) if 0 <= 1 < len(bottom_ten_agents) else None
                        ),
                        agent13=(bottom_ten_agents[2].get("agent") if 0 <= 2 < len(bottom_ten_agents) else None),
                        agent13_sales=(
                            Utility.currency_formatter(bottom_ten_agents[2].get("total sales all games")) if 0 <= 2 < len(bottom_ten_agents) else None
                        ),
                        agent14=(bottom_ten_agents[3].get("agent") if 0 <= 3 < len(bottom_ten_agents) else None),
                        agent14_sales=(
                            Utility.currency_formatter(bottom_ten_agents[3].get("total sales all games")) if 0 <= 3 < len(bottom_ten_agents) else None
                        ),
                        agent15=(bottom_ten_agents[4].get("agent") if 0 <= 4 < len(bottom_ten_agents) else None),
                        agent15_sales=(
                            Utility.currency_formatter(bottom_ten_agents[4].get("total sales all games")) if 0 <= 4 < len(bottom_ten_agents) else None
                        ),
                        agent16=(bottom_ten_agents[5].get("agent") if 0 <= 5 < len(bottom_ten_agents) else None),
                        agent16_sales=(
                            Utility.currency_formatter(bottom_ten_agents[5].get("total sales all games")) if 0 <= 5 < len(bottom_ten_agents) else None
                        ),
                        agent17=(bottom_ten_agents[6].get("agent") if 0 <= 6 < len(bottom_ten_agents) else None),
                        agent17_sales=(
                            Utility.currency_formatter(bottom_ten_agents[6].get("total sales all games")) if 0 <= 6 < len(bottom_ten_agents) else None
                        ),
                        agent18=(bottom_ten_agents[7].get("agent") if 0 <= 7 < len(bottom_ten_agents) else None),
                        agent18_sales=(
                            Utility.currency_formatter(bottom_ten_agents[7].get("total sales all games")) if 0 <= 7 < len(bottom_ten_agents) else None
                        ),
                        agent19=(bottom_ten_agents[8].get("agent") if 0 <= 8 < len(bottom_ten_agents) else None),
                        agent19_sales=(
                            Utility.currency_formatter(bottom_ten_agents[8].get("total sales all games")) if 0 <= 8 < len(bottom_ten_agents) else None
                        ),
                        agent20=(bottom_ten_agents[9].get("agent") if 0 <= 9 < len(bottom_ten_agents) else None),
                        agent20_sales=(
                            Utility.currency_formatter(bottom_ten_agents[9].get("total sales all games")) if 0 <= 9 < len(bottom_ten_agents) else None
                        ),
                    )
            return "REPORT SENT SUCCESSFULLY."


@dataclass
class DommyName:
    """
    Generate a random name
    """

    length: int

    def generate_random_name(self):
        VOWELS = "aeiou"
        CONSONANTS = "".join(set(string.ascii_lowercase) - set(VOWELS))

        word = ""
        for i in range(self.length):
            if i % 2 == 0:
                word += random.choice(CONSONANTS)
            else:
                word += random.choice(VOWELS)
        return word

    def generate_name(self):
        fullname = []

        for i in range(2):
            fullname.append(self.generate_random_name())

        fullname = " ".join(fullname)

        return fullname
