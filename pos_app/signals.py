# from django.db.models.signals import pre_delete
from django.db.models.signals import post_save
from django.dispatch import receiver

# from account.models import User
# from main.models import LotteryModel, LotteryWinnersTable, LottoTicket, LottoWinners, UserProfile
# from wallet_app.models import UserWallet
# from wyse_ussd.models import SoccerPrediction
# from pos_app.models import Agent, AgentPayoutBeneficiary, AgentWallet, AgentWalletTransaction, ChargeAgentTransaction, PosLotteryWinners
from pos_app.models import LottoSuperAgents, LottoSuperAgentWallet

# @receiver(pre_delete, sender=Agent)
# def delete_user_object_related_wallet(sender, instance: Agent, **kwargs):


@receiver(post_save, sender=LottoSuperAgents)
def create_wallet_for_super_agent(sender, instance, created, **kwargs):
    if created:
        LottoSuperAgentWallet.objects.create(super_agent=instance)
