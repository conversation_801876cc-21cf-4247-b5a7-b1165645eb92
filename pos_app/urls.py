from django.urls import path

from .views import (
    AgentCommissionWithdrawalApiView,
    AgentDownLineCommissionGivingApiView,
    AgentDownLinesAccountRestrictionApiView,
    AgentDownLinesAccountUnRestrictionApiView,
    AgentEnquiry,
    AgentFundingViaAgencyBankingWebhook,
    AgentLoginView,
    AgentSalesMetricsView,
    AgentTerminalIdUnAssignmentRequestApiView,
    AgentView,
    BankerLotteryApiView,
    ChargeAgentOnAgencyBankingForRemittanceView,
    CheckInstantCashoutInstantResult,
    CreateAgentRecord,
    DynamicDataApiView,
    FetchAgentEndOfDaySalesApiView,
    FetchGameDrawTimeApiView,
    FetchTerminalIdDailyTransactionApiView,
    GameDetails,
    GameHistory,
    GamePricesApiView,
    GameResultsView,
    GetAgentDetailsOnAgencyBanking,
    GetAgentDownlinesApiView,
    GetAgentWallet,
    GetStoreWinningDataApiView,
    IndividualMobileUserWinningWithdrawal,
    InstantCashGameResult,
    InstantCashOutMultiplePlay,
    JackpotApiView,
    LeaderBoardView,
    LibertyPayVfdFundingCallback,
    LotteryPayment,
    LotteryRetailCreation,
    LotteryRetailCreationViaBonus,
    LottoAgentGuarantorAPIView,
    LottoView,
    ManuallyCheckAndCreateLottoWinningsView,
    ManuallyCreateWinningForATicketView,
    ManuallyPreFundAgentApiView,
    MoveBonusToPlayWalletView,
    NewLotteryPayment,
    NewLottoView,
    PosFundDebitPlayWinningWallet,
    PosQuikaLottoPlayApiView,
    QuikaLottoGamePlay,
    RetailBonusLotteryHistory,
    RetailLotteryHistory,
    RetailLotteryHistoryDetail,
    SalaryForLifeNumberPick,
    SoccerPredictionApiview,
    StoreWinningWithdrawalApiView,
    SuperAgentDownlineNotification,
    SupervisorRecoverTerminalView,
    SupervisorSuspendTerminalView,
    TicketDetails,
    TransactionHistory,
    UpdateAgentRemittanceAmountPaid,
    WhyseCashLottery,
    WinnersApiView,
    WinningsPayout,
    WinningsPayoutToAgencyBanking,
    WinWiseEmployeeView,
    WyseCashGameResult, SupervisorDashboardSummaryView, SupervisorSalesView, SupervisorAgentActivityView,
    SupervisorTargetAchievementView, SupervisorEarningsView, SupervisorAgentActivityFilterView,
    LottoAgentSalesWeeklyActivity, TerminalRecoverySheetDataView,
)

# ----------------------- RETAIL SERVICE -----------------------
# -----------------
retail_urls = [
    path("create_lottery_retail/", LotteryRetailCreation.as_view()),
    path("create_bonus_lottery_retail/", LotteryRetailCreationViaBonus.as_view()),
    path("lottery_retail_history/", RetailLotteryHistory.as_view()),
    path("lottery_retail_details/<str:game_id>", RetailLotteryHistoryDetail.as_view()),
    path("bonus_lottery_retail_history/", RetailBonusLotteryHistory.as_view()),
]

sport_urls = [
    path("predict/", SoccerPredictionApiview.as_view()),
]


# ----------------------- AGENT -----------------------
agent_urls = [
    path("agent_enquiry/<str:phone>/", AgentEnquiry.as_view()),
    path("get_agent_wallet/<str:user_uuid>/", GetAgentWallet.as_view()),
    path("liberty_agency_fund_callback/", AgentFundingViaAgencyBankingWebhook.as_view()),
    path("guarantor_details/", LottoAgentGuarantorAPIView.as_view()),
    path("agent_vfd_funding_callback/", LibertyPayVfdFundingCallback.as_view()),
    path("lotto_agent_pre_funding/", ManuallyPreFundAgentApiView.as_view()),
    path("agent_details_on_agency_banking/", GetAgentDetailsOnAgencyBanking.as_view()),
]


# ------------------- DYANMIC DATA -------------------
dynamic_data_urls = [
    path("dynamic_data/", DynamicDataApiView.as_view()),
    path("games_draw_time/", FetchGameDrawTimeApiView.as_view())
]

WALLET_URLS = [
    # path("lottery_payment/", LotteryPayment.as_view()),
    path("lottery_payment/", NewLotteryPayment.as_view()),
    path("manually_fund_debit_agent_account/", PosFundDebitPlayWinningWallet.as_view()),
    path("withdraw_bonus_to_wallet/<str:user_uuid>/", MoveBonusToPlayWalletView.as_view()),
]

WIN_WISE_EMPLOYEE_TABLE_URL = [
    path("wisewinagentform/", WinWiseEmployeeView.as_view()),
]


SUPER_AGENT_URLS = [
    path("super_agent/fetch_downlines/", GetAgentDownlinesApiView.as_view()),
    path("super_agent/restrict_downlines/", AgentDownLinesAccountRestrictionApiView.as_view()),
    path("super_agent/unrestrict_downlines/", AgentDownLinesAccountUnRestrictionApiView.as_view()),
    path("super_agent/down_line_commission/", AgentDownLineCommissionGivingApiView.as_view()),
    path("super_agent/down_line_assignment_notification/", SuperAgentDownlineNotification.as_view()),
]


STORE_WINNINGS_URL = [
    path("agent_store_winnings/", GetStoreWinningDataApiView.as_view()),
    path("store_winning_withdrawal/", StoreWinningWithdrawalApiView.as_view()),
    path("withdraw_commission_value/", AgentCommissionWithdrawalApiView.as_view()),
]

AGENT_METRICS = [
    path("agent_activities_metrics/", AgentSalesMetricsView.as_view()),
]

SUPERVISOR_URL = [
    path("supervisor_recover_terminal/", SupervisorRecoverTerminalView.as_view()),
    path("supervisor_suspend_terminal/", SupervisorSuspendTerminalView.as_view()),
    path("terminal/sheet/recovery/", TerminalRecoverySheetDataView.as_view())
]

# ----------------------- Main URLS -----------------------
urlpatterns = [
    path("create_agent/", CreateAgentRecord.as_view(), name="create_agent_record"),
    path("login/", AgentLoginView.as_view()),
    # path("lotto/<str:channel>/", LottoView.as_view()),
    path("lotto/<str:channel>/", NewLottoView.as_view()),
    path("fast_lottery/", QuikaLottoGamePlay.as_view()),
    path("sure_banker/", BankerLotteryApiView.as_view()),
    path("instant_cash/multiple_play/", InstantCashOutMultiplePlay.as_view()),
    path("agent/", AgentView.as_view()),
    path("wyse_cash_lottery/<str:channel>/", WhyseCashLottery.as_view()),
    # path('commission_withdraw/', CommissionPayout.as_view()),
    path("winnings_withdraw/", WinningsPayout.as_view()),
    path("winnings_withdraw_to_lib_pay/", WinningsPayoutToAgencyBanking.as_view()),
    path(
        "personal_account_winning_withdrawal/",
        IndividualMobileUserWinningWithdrawal.as_view(),
    ),
    path("game_history/", GameHistory.as_view()),
    path("ticket_details/", TicketDetails.as_view()),
    path("transaction_history/", TransactionHistory.as_view()),
    path("jackpot/", JackpotApiView.as_view()),
    path("game_result/", GameResultsView.as_view()),
    path("game_details/", GameDetails.as_view()),
    path("wyse_cash_game_result/", WyseCashGameResult.as_view()),
    path("insatnt_cash_game_result/", InstantCashGameResult.as_view()),
    path("salary_for_life_game_result/", SalaryForLifeNumberPick.as_view()),
    path("winners/", WinnersApiView.as_view()),
    path("instant_cashout_lottery_check/", CheckInstantCashoutInstantResult.as_view()),
    path("game_prices/", GamePricesApiView.as_view()),
    path("update_agent_remittance_record/", UpdateAgentRemittanceAmountPaid.as_view()),
    path("leader_board/", LeaderBoardView.as_view()),
    path(
        "manually_charge_agent_wallet_on_agency_banking/",
        ChargeAgentOnAgencyBankingForRemittanceView.as_view(),
    ),
    *retail_urls,
    *sport_urls,
    *agent_urls,
    *dynamic_data_urls,
    *WALLET_URLS,
    *WIN_WISE_EMPLOYEE_TABLE_URL,
    *SUPER_AGENT_URLS,
    path("new_quika_game/", PosQuikaLottoPlayApiView.as_view()),
    path("end_of_day_report/", FetchAgentEndOfDaySalesApiView.as_view()),
    *STORE_WINNINGS_URL,
    path("manually_create_winning_ticket/", ManuallyCreateWinningForATicketView.as_view()),
    path("manually_check_and_create_winning_ticket/", ManuallyCheckAndCreateLottoWinningsView.as_view()),
    *AGENT_METRICS,
    path("terminal_re_assignment_request/", AgentTerminalIdUnAssignmentRequestApiView.as_view()),
    path("fetch_terminal_transaction_details/", FetchTerminalIdDailyTransactionApiView.as_view()),

    # Supervisor dashboard endpoints
    path('supervisor/dashboard-summary', SupervisorDashboardSummaryView.as_view(),
         name='supervisor-dashboard-summary'),
    path('supervisor/sales', SupervisorSalesView.as_view(), name='supervisor-sales'),
    path('supervisor/agent-activity', SupervisorAgentActivityView.as_view(),
         name='supervisor-agent-activity-rate'),
    path('supervisor/agent-activity/filter', SupervisorAgentActivityFilterView.as_view(),
         name='filter-supervisor-agent-activity-rate'),
    path('agent_weekly_activity/', LottoAgentSalesWeeklyActivity.as_view(), name='agent_weekly_activity'),
    path('supervisor/target-achievement', SupervisorTargetAchievementView.as_view(),
         name='supervisor-target-achievement'),
    path('supervisor/earnings', SupervisorEarningsView.as_view(), name='supervisor-earnings'),
    *SUPERVISOR_URL
]
