from django.db.models import Sum

from main.models import LotteryModel, LottoTicket, UserProfile
from main.tasks import celery_send_whatsapp_retail_sells_notification
from pos_app.models import Agent, BoughtLotteryTickets
from pos_app.retail_helpers import check_if_register_ticket_batch_is_active
from sport_app.models import (
    AgentSoccerCashWinner,
    DualTeamFinalistPrediction,
    GoalScorerPrediction,
    TeamFinalistPrediction,
)
from wyse_ussd.models import SoccerPrediction, UssdLotteryPayment


def verify_ticket(game_play_id):
    winning_team = TeamFinalistPrediction.objects.filter(user_profile=None, game_id=game_play_id)
    if winning_team.count() > 0:
        # get game
        return {
            "status": True,
            "message": "CON Enter Ticket Pin: \n",
        }
    two_teams_to_finals = DualTeamFinalistPrediction.objects.filter(user_profile=None, game_id=game_play_id)
    if two_teams_to_finals.count() > 0:
        # get game
        return {
            "status": True,
            "message": "CON Enter Ticket Pin: \n",
        }
    highest_goal_scorer = GoalScorerPrediction.objects.filter(user_profile=None, game_id=game_play_id)
    if highest_goal_scorer.count() > 0:
        # get game
        return {
            "status": True,
            "message": "CON Enter Ticket Pin: \n",
        }
    score_prediction = SoccerPrediction.objects.filter(user_profile=None, game_id=game_play_id)
    if score_prediction.count() > 0:
        # get game
        return {
            "status": True,
            "message": "CON Enter Ticket Pin: \n",
        }
    return {
        "status": True,
        "message": "END Game already registered\nThank you",
    }


def register_and_update_ticket_to_table(pin, phone_no, ussd_code):
    bought_ticket = BoughtLotteryTickets.objects.filter(pin=pin, ussd_code=ussd_code, is_available=True)

    if not bought_ticket.exists():
        return "END Invalid game pin"
    elif bought_ticket.last().game_type == "SOCCER_CASH":
        game_play_id = bought_ticket.last().game_id
        user = UserProfile.objects.filter(phone_number=phone_no).last()
        winning_team = TeamFinalistPrediction.objects.filter(user_profile=None, game_id=game_play_id)
        if winning_team.count() > 0:
            winning_team.update(user_profile=user, phone=phone_no)

        two_teams_to_finals = DualTeamFinalistPrediction.objects.filter(user_profile=None, game_id=game_play_id)
        if two_teams_to_finals.count() > 0:
            two_teams_to_finals.update(user_profile=user, phone=phone_no)

        highest_goal_scorer = GoalScorerPrediction.objects.filter(user_profile=None, game_id=game_play_id)
        if highest_goal_scorer.count() > 0:
            highest_goal_scorer.update(user_profile=user, phone=phone_no)

        score_prediction = SoccerPrediction.objects.filter(user_profile=None, game_id=game_play_id)
        if score_prediction.count() > 0:
            score_prediction.update(user_profile=user, phone=phone_no)

        AgentSoccerCashWinner.send_sms_to_late_ticket_registering(
            agent_instane=bought_ticket.last().agent,
            game_id=game_play_id,
            phone_no=phone_no,
            user_profile=user,
        )
        return "END Game registered successfully"
    else:
        agent_instance = Agent.objects.filter(id=bought_ticket.last().agent.id).last()

        bought_ticket_instance = bought_ticket.last()

        if bought_ticket_instance.game_type == "INSTANT_CASHOUT":
            user_profile = UserProfile.objects.filter(phone_number=phone_no).last()
            if user_profile is None:
                user_profile = UserProfile.objects.create(phone_number=phone_no)

            lotto_qs = LottoTicket.objects.filter(game_play_id=bought_ticket_instance.game_id)

            if lotto_qs.exists():
                lotto_qs.update(user_profile=user_profile)
                bought_ticket.update(is_available=False)

                UssdLotteryPayment.objects.create(
                    user=user_profile,
                    amount=bought_ticket.filter(game_type="INSTANT_CASHOUT").aggregate(Sum("amount"))["amount__sum"],
                    game_play_id=bought_ticket_instance.game_id,
                    is_successful=True,
                    is_verified=True,
                    lottery_type="INSTANT_CASHOUT",
                )

                celery_send_whatsapp_retail_sells_notification.delay(
                    phone_number=phone_no,
                    amount=bought_ticket.filter(game_type="INSTANT_CASHOUT").aggregate(Sum("amount"))["amount__sum"],
                    agent_name=f"{agent_instance.first_name} {agent_instance.last_name}",
                    agent_phone=agent_instance.phone,
                    game_type="INSTANT_CASHOUT",
                )

                check_if_register_ticket_batch_is_active.delay(lotto_qs.last().id)

                return "END Game registered successfully"

            return "END Game already registered"

        elif bought_ticket_instance.game_type == "WYSE_CASH":
            user_profile = UserProfile.objects.filter(phone_number=phone_no).last()
            if user_profile is None:
                user_profile = UserProfile.objects.create(phone_number=phone_no)

            lotto_qs = LotteryModel.objects.filter(game_play_id=bought_ticket_instance.game_id)
            if lotto_qs.exists():
                lotto_qs.update(user_profile=user_profile)
                bought_ticket.update(is_available=False)

                UssdLotteryPayment.objects.create(
                    user=user_profile,
                    amount=bought_ticket.filter(game_type="WYSE_CASH").aggregate(Sum("amount"))["amount__sum"],
                    game_play_id=bought_ticket_instance.game_id,
                    is_successful=True,
                    is_verified=True,
                    lottery_type="WYSE_CASH",
                )

                celery_send_whatsapp_retail_sells_notification.delay(
                    phone_number=phone_no,
                    amount=bought_ticket.filter(game_type="WYSE_CASH").aggregate(Sum("amount"))["amount__sum"],
                    agent_name=f"{agent_instance.first_name} {agent_instance.last_name}",
                    agent_phone=agent_instance.phone,
                    game_type="WYSE_CASH",
                )

                check_if_register_ticket_batch_is_active.delay(lotto_qs.last().id)

                return "END Game registered successfully"
            return "END Game already registered"

        elif bought_ticket_instance.game_type == "SALARY_FOR_LIFE":
            user_profile = UserProfile.objects.filter(phone_number=phone_no).last()
            if user_profile is None:
                user_profile = UserProfile.objects.create(phone_number=phone_no)

            lotto_qs = LottoTicket.objects.filter(game_play_id=bought_ticket_instance.game_id)
            if lotto_qs.exists():
                lotto_qs.update(user_profile=user_profile)
                bought_ticket.update(is_available=False)

                UssdLotteryPayment.objects.create(
                    user=user_profile,
                    amount=bought_ticket.filter(game_type="WYSE_CASH").aggregate(Sum("amount"))["amount__sum"],
                    game_play_id=bought_ticket_instance.game_id,
                    is_successful=True,
                    is_verified=True,
                    lottery_type="WYSE_CASH",
                )

                celery_send_whatsapp_retail_sells_notification.delay(
                    phone_number=phone_no,
                    amount=bought_ticket.filter(game_type="SALARY_FOR_LIFE").aggregate(Sum("amount"))["amount__sum"],
                    agent_name=f"{agent_instance.first_name} {agent_instance.last_name}",
                    agent_phone=agent_instance.phone,
                    game_type="SALARY_FOR_LIFE",
                )

                check_if_register_ticket_batch_is_active.delay(lotto_qs.last().id)

                return "END Game registered successfully"
            return "END Game already registered"

        return "END Game registered successfully"
