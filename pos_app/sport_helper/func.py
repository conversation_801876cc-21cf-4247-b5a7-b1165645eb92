import random
from random import randint


def generate_ticket_pin():
    n = 6
    range_start = 10 ** (n - 1)
    range_end = (10**n) - 1
    value = randint(range_start, range_end)
    return str(value)


def total_stake_amount(game_play: dict, game_type: str):
    # print(game_play, "--------------------------game play------------------------------")
    predictions = game_play.get(game_type, None)
    if predictions:
        result = [amount["stake_amount"] for prediction in predictions for amount in prediction["predictions"]]
    else:
        result = []
    return sum(tuple(result))


def score_prediction(game_play):  # total stake amount
    return total_stake_amount(game_play=game_play, game_type="score_prediction")


def dual_team_finalist(game_play):  # total stake amount
    return total_stake_amount(game_play=game_play, game_type="dual_team_finalist")


def team_win(game_play):  # total stake amount
    return total_stake_amount(game_play=game_play, game_type="team_win")


def goal_scorer(game_play):  # total stake amount
    return total_stake_amount(game_play=game_play, game_type="goal_scorer")


def sum_stake_amount(game_play):
    # print("----------", score_prediction(game_play), "-----------")
    return score_prediction(game_play) + dual_team_finalist(game_play) + team_win(game_play) + goal_scorer(game_play)


def verify_stake_amount(game_play, amount):
    # print("amount ---------->", amount)
    # print("sum total ---------->", sum_stake_amount(game_play=game_play))
    return sum_stake_amount(game_play=game_play) == amount


def generate_withdrawal_pin():
    return str(random.randint(0, 999999)).zfill(6)
