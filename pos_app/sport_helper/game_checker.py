from django.utils import timezone

from sport_app.models import DualTeamFinalist, FootballTable, GoalScorer, TeamFinalist


def game_check(game_play):
    """This function checks if an id passed is not valid
    if not valid it returns a response that tells the
    mobile developer that the id passed is not valid
    The essence of this is help not to create object
    if for some reason there could be and invalid id"""

    score_prediction = game_play.get("score_prediction")
    goal_scorer = game_play.get("goal_scorer")
    team_win = game_play.get("team_win")
    dual_team_finalist = game_play.get("dual_team_finalist")

    if score_prediction and len(score_prediction) > 0:
        for socre_game in score_prediction:
            fixtures_id = socre_game.get("fixtures_id")
            try:
                FootballTable.objects.get(fixture_id=fixtures_id, game_completed=False, is_drawn=False, fixture_date__gte=timezone.now())
            except FootballTable.DoesNotExist:
                return {
                    "succeeded": False,
                    "message": f"invalid match {fixtures_id}",
                }

    if goal_scorer and len(goal_scorer) > 0:
        for selected_game in goal_scorer:
            player_id = selected_game.get("player_id")

            try:
                GoalScorer.objects.get(id=player_id, is_drawn=False, is_top_scorer=False)
            except GoalScorer.DoesNotExist:
                return {
                    "succeeded": False,
                    "message": f"invalid player id {player_id}",
                }

    if team_win and len(team_win) > 0:
        for selected_game in team_win:
            team_id = selected_game.get("team_id")

            try:
                TeamFinalist.objects.get(id=team_id, is_drawn=False, team_win=False)
            except TeamFinalist.DoesNotExist:
                return {
                    "succeeded": False,
                    "message": f"invalid team id {team_id}",
                }

    if dual_team_finalist and len(dual_team_finalist) > 0:
        for selected_game in dual_team_finalist:
            team_a_id = selected_game.get("team_a_id")
            team_b_id = selected_game.get("team_b_id")

            dual_team_finalist = DualTeamFinalist.get_or_create_new_object(team_a_id, team_b_id)
            if dual_team_finalist is None:
                return {
                    "succeeded": False,
                    "message": f"invalid dual {team_a_id} and {team_b_id}",
                }
