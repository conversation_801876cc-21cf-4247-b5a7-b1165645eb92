from django.utils import timezone

from main.api.api_lottery_helpers import generate_game_play_id
from pos_app.models import AgentWallet, BoughtLotteryTickets
from pos_app.pos_helpers import PosAgentHelper
from pos_app.sport_helper.func import generate_ticket_pin
from pos_app.sport_helper.game_checker import game_check
from sport_app.models import (
    DualTeamFinalist,
    DualTeamFinalistPrediction,
    FootballTable,
    GoalScorer,
    GoalScorerPrediction,
    TeamFinalist,
    TeamFinalistPrediction,
)
from wyse_ussd.models import SoccerPrediction


def create_prediction_ticket_object(game_play, agent, single, amount, pin, play_type):
    """
    Create a prediction ticket object
    :param game_play:
    :param agent:
    :param single:
    :param amount:
    :param pin:
    :param play_type:
    :return:
    """

    game_play_checker = game_check(game_play)  # check function for description

    if game_play_checker is None:
        # print("TOTAL RETAIL AMOUNT ::::", amount)

        from main.models import UserProfile

        player = UserProfile.create_user_profile_if_none_exist(phone_no=agent.phone, channel="POS")

        pos_agent_helper = PosAgentHelper(agent_instance=agent, amount=amount, pin=pin)

        agent_wallet = AgentWallet.objects.filter(agent=agent).last()
        if agent_wallet is None:
            agent_wallet = AgentWallet.objects.create(
                agent=agent,
                agent_name=agent.name,
                agent_phone_number=agent.phone,
                agent_email=agent.email,
            )

        if agent_wallet.game_play_bal < amount:
            charge_response = pos_agent_helper.charge_agent_wallet()
        else:
            charge_response = PosAgentHelper.charge_agent_play_wallet(agent_id=agent.id, amount=amount, ticket_instance="RETAIL")

        charge_response_msg = charge_response.get("message")
        if charge_response_msg == "success":  # on successful transaction from agent wallet
            score_prediction = game_play.get("score_prediction")
            goal_scorer = game_play.get("goal_scorer")
            team_win = game_play.get("team_win")
            dual_team_finalist = game_play.get("dual_team_finalist")

            ussd_code_at_top_level = BoughtLotteryTickets.generate_ussd_code()
            ticket_pin_at_top_level = generate_ticket_pin()
            game_play_id_at_top_level = generate_game_play_id()

            if score_prediction and len(score_prediction) > 0:
                count = 0
                for socre_game in score_prediction:
                    fixtures_id = socre_game.get("fixtures_id")
                    football_table = FootballTable.objects.get(fixture_id=fixtures_id, game_completed=False, is_drawn=False)
                    predictions = socre_game.get("predictions")

                    ussd_code_at_mid_level = BoughtLotteryTickets.generate_ussd_code()
                    ticket_pin_at_mid_level = generate_ticket_pin()
                    game_play_id_at_mid_level = generate_game_play_id()

                    for prediction_count in range(len(predictions)):
                        stake_amount = predictions[prediction_count]["stake_amount"]
                        home_choice = predictions[prediction_count]["home_choice"]
                        away_choice = predictions[prediction_count]["away_choice"]

                        band_played = SoccerPrediction.general_band_played(stake_amount)

                        if play_type == "PERSONAL":
                            game_id = game_play_id_at_top_level
                            win_amount = 0

                        elif play_type == "SHARED":
                            win_amount = SoccerPrediction.stake_and_win_amount_web(stake_amount).get("win_amount")

                            if single:
                                game_id = game_play_id_at_mid_level
                            else:
                                game_id = game_play_id_at_top_level

                        ticket = BoughtLotteryTickets.objects.create(
                            agent=agent,
                            football_table=football_table,
                            ussd_code=ussd_code_at_mid_level if single else ussd_code_at_top_level,
                            game_type="SOCCER_CASH",
                            game_id=game_id,
                            home_choice=home_choice,
                            away_choice=away_choice,
                            band_played=band_played,
                            amount=stake_amount,
                            pontential_win=win_amount,
                            game_fixture_id=fixtures_id,
                            pin=ticket_pin_at_mid_level if single else ticket_pin_at_top_level,
                            paid=True,
                        )

                        SoccerPrediction.objects.create(
                            user_profile=player,
                            football_table=football_table,
                            bought_lottery_ticket=ticket,
                            agent=agent,
                            game_id=game_id,
                            home_choice=home_choice,
                            away_choice=away_choice,
                            band_played=band_played,
                            stake_amount=stake_amount,
                            potential_winning=win_amount,
                            amount_paid=stake_amount,
                            paid=True,
                            game_fixture_id=fixtures_id,
                            channel="POS_AGENT",
                            paid_date=timezone.now(),
                            play_type=play_type,
                        )

                    score_prediction[count]["ussd_code"] = ussd_code_at_mid_level if single else ussd_code_at_top_level
                    score_prediction[count]["pin"] = ticket_pin_at_mid_level if single else ticket_pin_at_top_level
                    score_prediction[count]["game_id"] = game_id
                    count += 1

            if goal_scorer and len(goal_scorer) > 0:
                count = 0
                for selected_game in goal_scorer:
                    player_id = selected_game.get("player_id")
                    top_goal_scorer = GoalScorer.objects.get(id=player_id, is_drawn=False, is_top_scorer=False)
                    predictions = selected_game.get("predictions")

                    ussd_code_at_mid_level = BoughtLotteryTickets.generate_ussd_code()
                    ticket_pin_at_mid_level = generate_ticket_pin()
                    game_play_id_at_mid_level = generate_game_play_id()

                    for prediction_count in range(len(predictions)):
                        goal = predictions[prediction_count]["goal"]
                        stake_amount = predictions[prediction_count]["stake_amount"]
                        band_played = SoccerPrediction.general_band_played(stake_amount)
                        win_amount = SoccerPrediction.stake_and_win_amount_web(stake_amount).get("win_amount")

                        ticket = BoughtLotteryTickets.objects.create(
                            agent=agent,
                            goal_scorer=top_goal_scorer,
                            ussd_code=ussd_code_at_mid_level if single else ussd_code_at_top_level,
                            game_type="SOCCER_CASH",
                            game_id=game_play_id_at_mid_level if single else game_play_id_at_top_level,
                            goal=goal,
                            band_played=band_played,
                            amount=stake_amount,
                            pontential_win=win_amount,
                            pin=ticket_pin_at_mid_level if single else ticket_pin_at_top_level,
                            paid=True,
                        )

                        GoalScorerPrediction.objects.create(
                            user_profile=player,
                            goal_scorer=top_goal_scorer,
                            bought_lottery_ticket=ticket,
                            agent=agent,
                            game_id=game_play_id_at_mid_level if single else game_play_id_at_top_level,
                            goal=goal,
                            band_played=band_played,
                            stake_amount=stake_amount,
                            potential_winning=win_amount,
                            amount_paid=stake_amount,
                            paid=True,
                            channel="POS_AGENT",
                            paid_date=timezone.now(),
                        )

                    goal_scorer[count]["ussd_code"] = ussd_code_at_mid_level if single else ussd_code_at_top_level
                    goal_scorer[count]["pin"] = ticket_pin_at_mid_level if single else ticket_pin_at_top_level
                    goal_scorer[count]["game_id"] = game_play_id_at_mid_level if single else game_play_id_at_top_level
                    count += 1

            if team_win and len(team_win) > 0:
                count = 0
                for selected_game in team_win:
                    team_id = selected_game.get("team_id")
                    team_finalist = TeamFinalist.objects.get(id=team_id, is_drawn=False, team_win=False)
                    predictions = selected_game.get("predictions")

                    ussd_code_at_mid_level = BoughtLotteryTickets.generate_ussd_code()
                    ticket_pin_at_mid_level = generate_ticket_pin()
                    game_play_id_at_mid_level = generate_game_play_id()

                    for prediction_count in range(len(predictions)):
                        stake_amount = predictions[prediction_count]["stake_amount"]
                        band_played = SoccerPrediction.general_band_played(stake_amount)
                        win_amount = SoccerPrediction.stake_and_win_amount_web(stake_amount).get("win_amount")

                        ticket = BoughtLotteryTickets.objects.create(
                            agent=agent,
                            team_final_list=team_finalist,
                            ussd_code=ussd_code_at_mid_level if single else ussd_code_at_top_level,
                            game_type="SOCCER_CASH",
                            game_id=game_play_id_at_mid_level if single else game_play_id_at_top_level,
                            band_played=band_played,
                            amount=stake_amount,
                            pontential_win=win_amount,
                            pin=ticket_pin_at_mid_level if single else ticket_pin_at_top_level,
                            paid=True,
                        )
                        TeamFinalistPrediction.objects.create(
                            user_profile=player,
                            team_final_list=team_finalist,
                            bought_lottery_ticket=ticket,
                            agent=agent,
                            game_id=game_play_id_at_mid_level if single else game_play_id_at_top_level,
                            band_played=band_played,
                            stake_amount=stake_amount,
                            potential_winning=win_amount,
                            amount_paid=stake_amount,
                            paid=True,
                            channel="POS_AGENT",
                            paid_date=timezone.now(),
                        )

                    team_win[count]["ussd_code"] = ussd_code_at_mid_level if single else ussd_code_at_top_level
                    team_win[count]["pin"] = ticket_pin_at_mid_level if single else ticket_pin_at_top_level
                    team_win[count]["game_id"] = game_play_id_at_mid_level if single else game_play_id_at_top_level
                    count += 1

            if dual_team_finalist and len(dual_team_finalist) > 0:
                count = 0
                for selected_game in dual_team_finalist:
                    team_a_id = selected_game.get("team_a_id")
                    predictions = selected_game.get("predictions")
                    team_b_id = selected_game.get("team_b_id")

                    dual_qualifiers = DualTeamFinalist.get_or_create_new_object(team_a_id, team_b_id)

                    ussd_code_at_mid_level = BoughtLotteryTickets.generate_ussd_code()
                    ticket_pin_at_mid_level = generate_ticket_pin()
                    game_play_id_at_mid_level = generate_game_play_id()

                    for prediction_count in range(len(predictions)):
                        stake_amount = predictions[prediction_count]["stake_amount"]
                        band_played = SoccerPrediction.general_band_played(stake_amount)

                        win_amount = SoccerPrediction.stake_and_win_amount_web(stake_amount).get("win_amount")

                        ticket = BoughtLotteryTickets.objects.create(
                            agent=agent,
                            dual_team_finalist=dual_qualifiers,
                            ussd_code=ussd_code_at_mid_level if single else ussd_code_at_top_level,
                            game_type="SOCCER_CASH",
                            game_id=game_play_id_at_mid_level if single else game_play_id_at_top_level,
                            band_played=band_played,
                            amount=stake_amount,
                            pontential_win=win_amount,
                            pin=ticket_pin_at_mid_level if single else ticket_pin_at_top_level,
                            paid=True,
                        )
                        DualTeamFinalistPrediction.objects.create(
                            user_profile=player,
                            dual_team_finalist=dual_qualifiers,
                            agent=agent,
                            bought_lottery_ticket=ticket,
                            game_id=game_play_id_at_mid_level if single else game_play_id_at_top_level,
                            band_played=band_played,
                            stake_amount=stake_amount,
                            potential_winning=win_amount,
                            amount_paid=stake_amount,
                            paid=True,
                            channel="POS_AGENT",
                            paid_date=timezone.now(),
                        )

                    dual_team_finalist[count]["ussd_code"] = ussd_code_at_mid_level if single else ussd_code_at_top_level
                    dual_team_finalist[count]["pin"] = ticket_pin_at_mid_level if single else ticket_pin_at_top_level
                    dual_team_finalist[count]["game_id"] = game_play_id_at_mid_level if single else game_play_id_at_top_level
                    count += 1

            response = {
                "succeeded": True,
                "message": "success",
                "agent_id": agent.id,
                "game_ticket_id": None if single else game_play_id_at_top_level,
                "ussd_code": None if single else ussd_code_at_top_level,
                "pin": None if single else ticket_pin_at_top_level,
                "data": game_play,
                # "total_predictions": len(prediction_df),
                # "stake_amount": "",
                # "winning_amount": "",
            }
            return response

        else:
            response = {"succeeded": False, "message": charge_response_msg}
            return response

    elif isinstance(game_play_checker, dict):
        return game_play_checker
