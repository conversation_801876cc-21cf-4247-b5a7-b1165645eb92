from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from referral_system.models import (
    ReferralCode,
    ReferralTable,
    ReferralTransaction,
    ReferralWallet,
)


# Register your models here.
class ReferralTableResource(resources.ModelResource):
    class Meta:
        model = ReferralTable


class ReferralCodeResource(resources.ModelResource):
    class Meta:
        model = ReferralCode


class ReferralWalletResource(resources.ModelResource):
    class Meta:
        model = ReferralWallet


class ReferralTransactionResource(resources.ModelResource):
    class Meta:
        model = ReferralTransaction


class ReferralTableResourceAdmin(ImportExportModelAdmin):
    resource_class = ReferralTableResource
    search_fields = ["id", "user__phone_number", "referral_code__referral_code", "referred_by__phone_number", "date_created", "last_updated"]

    date_hierarchy = "date_created"

    raw_id_fields = ["user", "referral_code"]

    autocomplete_fields = [
        "user",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ReferralCodeResourceAdmin(ImportExportModelAdmin):
    resource_class = ReferralCodeResource
    autocomplete_fields = [
        "user",
    ]
    search_fields = ["id", "user__phone_number", "referral_code", "date_created", "last_updated"]

    date_hierarchy = "date_created"

    raw_id_fields = [
        "user",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ReferralTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = ReferralTransactionResource
    search_fields = ["id", "user__phone_number", "wallet", "amount", "transaction_type", "created_at", "updated_at"]

    date_hierarchy = "created_at"

    raw_id_fields = ["user", "wallet"]

    autocomplete_fields = [
        "user",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ReferralWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = ReferralWalletResource
    search_fields = ["id", "user__phone_number", "available_balance", "currency", "created_at"]

    date_hierarchy = "created_at"

    raw_id_fields = [
        "user",
    ]

    autocomplete_fields = [
        "user",
    ]

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(ReferralTable, ReferralTableResourceAdmin)
admin.site.register(ReferralCode, ReferralCodeResourceAdmin)
admin.site.register(ReferralWallet, ReferralWalletResourceAdmin)
admin.site.register(ReferralTransaction, ReferralTransactionResourceAdmin)
