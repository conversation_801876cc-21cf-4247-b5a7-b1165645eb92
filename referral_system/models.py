import random
import secrets
import string
import uuid

from django.conf import settings
from django.db import models
from django.db.models.signals import post_save
from django.utils import timezone

from main.helpers.redis_storage import RedisStorage

# Create your models here.
from main.models import (
    ConstantVariable,
    LotteryBatch,
    LotteryGlobalJackPot,
    LotteryModel,
    LottoTicket,
    UserProfile,
)


def random_char(y):
    return "".join(random.choice(string.ascii_letters) for x in range(y))


class ReferralTable(models.Model):
    user = models.ForeignKey(UserProfile, related_name="referal", on_delete=models.CASCADE)
    referral_code = models.ForeignKey("ReferralCode", on_delete=models.CASCADE)
    referred_by = models.ForeignKey(
        UserProfile,
        related_name="referred",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    first_deposit_amount = models.IntegerField(default=0)
    first_deposit_amount2 = models.IntegerField(default=0)
    amount_played = models.IntegerField(default=0)
    referral_amount = models.IntegerField(default=0)
    referrer_rewarded = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.user.phone_number

    class Meta:
        verbose_name = "REFERRAL TABLE"
        verbose_name_plural = "REFERRAL TABLES"

    @classmethod
    def add_referral_amount(cls, phone, amount):
        """
        This method adds the referral amount to the user's account

        args: user: user phone
              anount: user's first deposit amount

        """

        user_profile = UserProfile.objects.filter(phone_number=phone).last()

        if user_profile:
            referral = cls.objects.filter(user=user_profile, referral_amount__lte=1).last()

            if referral:
                referra_percentage = ConstantVariable.get_constant_variable().get("referral_reward_percentage")
                if referra_percentage > 0:
                    _referral_amount = float(amount) * (referra_percentage / 100)
                    referral.first_deposit_amount += amount
                    referral.referral_amount += _referral_amount
                    referral.save()

    @classmethod
    def reward_referrer(cls, user_id, amount=0):
        """
        This method rewards the referrer if the referred has played centain percentage(%)
        of his first deposit amount

        args: user: user id

        """

        referrer = cls.objects.filter(user__id=user_id, referrer_rewarded=False).last()

        if referrer and amount > 0:
            _user_profile = UserProfile.objects.filter(id=user_id).last()
            percentage_of_first_deposit = referrer.first_deposit_amount * ReferralTable.referral_percentage_to_be_played()

            amount_played_after_first_deposit = referrer.first_deposit_amount - amount

            referrer.amount_played += amount
            referrer.save()

            if amount_played_after_first_deposit >= percentage_of_first_deposit:
                _fund_referrer_wallet = ReferralWallet.fund_referer_wallet(_user_profile, referrer.referral_amount)

                if _fund_referrer_wallet:
                    referrer.referrer_rewarded = True
                    referrer.save()

    @staticmethod
    def referral_percentage_to_be_played():
        return 50 / 100

    @property
    def played_percentage(self):
        try:
            return (self.amount_played / self.first_deposit_amount) * 100
        except ZeroDivisionError:
            return 0

    @property
    def user_name(self):
        try:
            names = self.user.first_name + " " + self.user.last_name
        except Exception:
            names = self.user.first_name
            if not names:
                names = self.user.phone_number

        return names

    @property
    def user_phone(self):
        return self.user.phone_number

    @property
    def amount(self):
        return self.referral_amount

    @property
    def status(self):
        return "Successful" if self.referrer_rewarded else "Pending"


class ReferralCode(models.Model):
    user = models.ForeignKey(UserProfile, related_name="referal_code", on_delete=models.CASCADE)
    referral_code = models.CharField(max_length=20, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.referral_code

    class Meta:
        verbose_name = "REFERRAL CODE"
        verbose_name_plural = "REFERRAL CODES"

    def save(self, *args, **kwargs):
        if not self.referral_code:
            #    generated_referral_code = ReferralCode.create_referal_code(user=self.user)
            if ReferralCode.objects.filter(user=self.user).exists():
                return {"message": "RECORD ALREADY EXISTS"}

            self.referral_code = ReferralCode.manually_generate_referral_code(user=self.user)
        else:
            pass
        super(ReferralCode, self).save(*args, **kwargs)

    @staticmethod
    def referral_amount():
        return 100.00

    @staticmethod
    def manually_generate_referral_code(user):
        token = secrets.token_urlsafe(8)
        if user.first_name is not None:
            ref_code = f"{user.first_name[:2]}-{token}"

            return ref_code

        else:
            name = random_char(5)
            ref_code = f"{name[:2]}-{token}"

            return ref_code

    @staticmethod
    def create_referal_code(user):
        token = secrets.token_urlsafe(8)
        if user.first_name is not None:
            ref_code = f"{user.first_name[:2]}-{token}"
            ref_code_instance = ReferralCode.objects.create(user=user, referral_code=ref_code)

            # filter_ref_code = ReferralCode.objects.filter(referral_code=ref_code_instance).exists()

            # while filter_ref_code is True:
            #     ReferralCode.create_referal_code(user=user)

            return ref_code_instance

        else:
            name = random_char(5)
            ref_code = f"{name[:2]}-{token}"
            ref_code_instance = ReferralCode.objects.create(user=user, referral_code=ref_code)

            # filter_ref_code = ReferralCode.objects.filter(referral_code=ref_code_instance).exists()

            # while filter_ref_code is True:
            #     ReferralCode.create_referal_code()

            return ref_code_instance

    @classmethod
    def get_referer_user(cls, referral_code):
        get_user_with_referral = cls.objects.filter(referral_code=referral_code).last()
        if get_user_with_referral:
            return get_user_with_referral
        else:
            return None

    @property
    def url(self):
        return f"{settings.LOTTO_FRONTEND_LINK}?code={self.referral_code}"


def create_referral_wallet(sender, instance: ReferralCode, created, **kwargs):
    if created:
        ReferralWallet.objects.create(user=instance.user, referral_code=instance)


post_save.connect(create_referral_wallet, sender=ReferralCode)


class ReferralWallet(models.Model):
    CURRENCY_TYPE_CHOICES = [
        ("NGN", "Naira"),
        ("USD", "US Dollar"),
    ]

    user = models.ForeignKey(UserProfile, related_name="referral_wallet", on_delete=models.CASCADE)
    referral_code = models.ForeignKey(ReferralCode, on_delete=models.CASCADE)
    available_balance = models.FloatField(default=0.00)
    currency = models.CharField(max_length=3, default="NGN", choices=CURRENCY_TYPE_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)
    can_use_wallet = models.BooleanField(default=True)

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)
    #     self.__original_available_balance = self.available_balance

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "REFERRAL WALLET"
        verbose_name_plural = "REFERRAL WALLETS"

    def save(self, *args, **kwargs):
        if not self.pk:
            if ReferralWallet.objects.filter(user=self.user).exists():
                pass
            else:
                return super(ReferralWallet, self).save(*args, **kwargs)
        if self.pk:
            # ------------------- GETTING OLD VALUES ------------------- #
            __original_available_balance = 0.00
            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "available_balance":
                    __original_available_balance = field.value_from_object(old)

            # ------------------- GETTING OLD VALUES ENDS HERE ------------------- #

            amount_changed = self.available_balance - __original_available_balance
            if self.available_balance != __original_available_balance:
                if self.available_balance > __original_available_balance:
                    ReferralTransaction.create_referral_transaction(self.user, self, amount_changed, "CREDIT")
                elif self.available_balance < __original_available_balance:
                    amount_changed = __original_available_balance - self.available_balance
                    ReferralTransaction.create_referral_transaction(self.user, self, amount_changed, "DEBIT")
        return super(ReferralWallet, self).save(*args, **kwargs)

    def __str__(self) -> str:
        return str(self.available_balance)

    @staticmethod
    def fund_referer_wallet(user_instance, amount):
        """
        This method is used to fund the referal wallet of the refered user
        """
        get_refered = ReferralTable.objects.filter(user=user_instance).last()
        if get_refered and get_refered.referred_by is not None:
            get_refered_wallet = ReferralWallet.objects.filter(user=get_refered.referred_by).last()
            if get_refered_wallet:
                get_refered_wallet.available_balance += amount
                get_refered_wallet.save()
                return get_refered_wallet
            else:
                referral_wallet_instance = ReferralWallet.objects.create(
                    user=get_refered.referred_by,
                    can_use_wallet=True,
                    available_balance=amount,
                    referral_code=get_refered.referral_code,
                )
                return referral_wallet_instance

    @staticmethod
    def wallet_threshold():
        return 1000.00

    @staticmethod
    def lottery_payment(wallet_instance, amount, lottery_qs):
        """
        This method is used to pay for user lottery ticket
        """

        # check if lottery batch is still active
        lottery_instance = lottery_qs.last()

        qs_ids = list(map(lambda id_tuple: id_tuple[0], lottery_qs.values_list("id")))

        if amount < lottery_instance.expected_amount:
            data = {
                "status": "failed",
                "message": "please enter the correct amount for this lottery",
            }

            return data

        if lottery_instance.batch.is_active is False:
            # if his batch has ended, create a new batch
            # and add him to the new batch

            if lottery_instance.lottery_type == "SALARY_FOR_LIFE":
                global_jackpot = LotteryGlobalJackPot.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active=True).last()

                new_batch = LotteryBatch.objects.create(
                    lottery_type="SALARY_FOR_LIFE",
                    global_jackpot=global_jackpot,
                )

            elif lottery_instance.lottery_type == "INSTANT_CASHOUT":
                new_batch = LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT")

            elif lottery_instance.lottery_type == "WYSE_CASH":
                new_batch = LotteryBatch.objects.create(
                    lottery_type="WYSE_CASH",
                )

            # update lottery ticket batch
            lottery_qs.update(batch=new_batch)

        # update lottery payment as paid and paid amount
        lottery_qs.update(paid=True, amount_paid=amount)

        lottery_instance = LottoTicket.objects.filter(id__in=qs_ids).last()

        # if lottery_instance:
        #     is_instant_cashout = lottery_instance.lottery_type == "INSTANT_CASHOUT"

        if not lottery_instance:
            lottery_instance = LotteryModel.objects.filter(id__in=qs_ids).last()

        # save lottery payment amount to the pool of batch contribution
        redis_db = RedisStorage(lottery_instance.batch.batch_uuid)
        get_redis_item = redis_db.get_data()
        if get_redis_item is not None:
            lottery_expected_amount = float(get_redis_item.decode("utf-8"))

            # update redis item
            redis_db.set_data(lottery_expected_amount + lottery_instance.amount_paid)

            print("amount paid stored in redis", lottery_instance.amount_paid)

        else:
            redis_db.set_data(lottery_instance.amount_paid)

            print("amount paid stored in redis", lottery_instance.amount_paid)

        # update wallet
        wallet_instance.available_balance = wallet_instance.available_balance - amount
        wallet_instance.save()

        data = {"status": "success", "message": "Lottery ticket paid successfully"}
        return data


def update_wallet_useage(sender, instance, created, **kwargs):
    if instance.can_use_wallet is False:
        if instance.available_balance >= ReferralWallet.wallet_threshold():
            instance.can_use_wallet = True
            instance.save()


post_save.connect(update_wallet_useage, sender=ReferralWallet)


class ReferralTransaction(models.Model):
    TRANSACTION_TPE = [
        ("CREDIT", "CREDIT"),
        ("DEBIT", "DEBIT"),
    ]
    user = models.ForeignKey(UserProfile, related_name="referral_transaction", on_delete=models.CASCADE)
    wallet = models.ForeignKey(ReferralWallet, on_delete=models.CASCADE)
    amount = models.FloatField()
    transaction_type = models.CharField(max_length=10, choices=TRANSACTION_TPE)
    transaction_reference = models.CharField(max_length=125, default=uuid.uuid4)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.user.phone_number

    class Meta:
        verbose_name = "REFERRAL TRANSACTION"
        verbose_name_plural = "REFERRAL TRANSACTIONS"

    @staticmethod
    def create_referral_transaction(user, wallet, amount, transaction_type):
        wallet = ReferralWallet.objects.filter(user=user).last()
        ReferralTransaction.objects.create(user=user, wallet=wallet, amount=amount, transaction_type=transaction_type)
