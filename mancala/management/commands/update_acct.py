from django.core.management.base import BaseCommand

from main.models import WovenAccountDetail
from wallet_app.models import UserWallet


class Command(BaseCommand):
    help = "Description of your custom command"

    def add_arguments(self, parser):
        parser.add_argument("wallet_id", type=str, help="debited transaction id")

    def handle(self, *args, **options):
        wallet_id = options["wallet_id"]
        try:
            user_wallet_instance = UserWallet.objects.get(id=wallet_id)
            wallet_tag = "WEB"
            account_details = WovenAccountDetail.objects.filter(
                phone_number=user_wallet_instance.user.phone_number,
                acct_provider="WEMA",
                wallet_tag=wallet_tag,
            )
            # print(account_details)
            acct = account_details.first()
            # print(acct)
            # print(acct.account_ref)
            user_wallet_instance.wema_account = acct
            user_wallet_instance.wema_account_ref = acct.account_ref
            user_wallet_instance.wallet_tag = wallet_tag
            user_wallet_instance.save()

            self.stdout.write(self.style.SUCCESS("UPDATED SUCCESSFULLY"))

        except UserWallet.DoesNotExist:
            self.stdout.write(self.style.ERROR("WALLET ID NOT FOUND"))
