from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied

from account.models import BlackListed, UserProfileOtp
from main.models import ConstantVariable, UserProfile
from mancala.models import Interaction
from wallet_app.models import UserWallet, WithdrawalPINSystem
from wyse_ussd.helper.general_helper import has_enough_money_to_giveout


class BalanceSerializer(serializers.ModelSerializer):
    """
    Resend Activation Code Serializer
    """

    class Meta:
        model = Interaction
        fields = "__all__"


class WebBalanceEnquirySerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=13, min_length=11)
    verification_code = serializers.CharField(max_length=5, min_length=5)

    def validate(self, attrs):
        phone_number = attrs.get("phone_number")
        verification_code = attrs.get("verification_code")

        code_match = UserProfile.verification_code_match(phone_number=phone_number, verification_code=verification_code)

        if code_match is False:
            raise serializers.ValidationError({"message": "Verification code does not match"})

        return attrs


class UpdateVerificationCodeSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=13, min_length=11)


class WinningWithdrawalSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=13)
    amount = serializers.FloatField()
    account_name = serializers.CharField()
    account_number = serializers.CharField()
    bank_name = serializers.CharField()
    bank_code = serializers.CharField()
    pin = serializers.CharField(max_length=4, min_length=4, required=True, style={"input_type": "password"})
    save_beneficiary = serializers.BooleanField(default=False)

    def validate(self, values):
        phone_number = values.get("phone_number")
        otp = values.get("pin")
        amount = values.get("amount")

        # Retrieve the user's profile instance based on the provided phone number
        user_profile_instance = UserProfile.objects.filter(phone_number=phone_number).last()
        if not user_profile_instance:
            raise PermissionDenied(f"User {phone_number} does not exist")

        # Check if the user's profile is verified
        verified = user_profile_instance.verification_code
        if not verified:
            raise PermissionDenied("User not Verified")

        # Check if the user's profile email is verified
        if not user_profile_instance.email_is_verified:
            raise PermissionDenied("User email not verified")

        # Check if the user is blacklisted
        if BlackListed.is_blacklisted(phone=phone_number):
            raise PermissionDenied("User Black listed")

        # Check if the OTP is valid and active
        withdrawal_otp_instance = WithdrawalPINSystem.get_active_otp(otp=otp, phone_number=phone_number)
        is_otp_active = withdrawal_otp_instance.exists()

        if is_otp_active is False:
            raise serializers.ValidationError({"message": "Invalid or Used Withdrawal PIN"})

        # Retrieve the user's wallet for withdrawals
        user_wallet = UserWallet.objects.filter(user=user_profile_instance, wallet_tag="WEB").last()
        if user_wallet is None:
            raise serializers.ValidationError({"message": "No Withdrawal Wallet Found"})

        # Check if the requested withdrawal amount exceeds the available balance
        if amount > user_wallet.withdrawable_available_balance:
            raise serializers.ValidationError({"message": "Insufficient Winning Balance"})

        # Check if the payout source is available
        if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
            raise serializers.ValidationError({"message": "Payout is not available at the moment"})

        # Check if the user has enough funds to process the request
        if has_enough_money_to_giveout(phone_number, amount) is False:
            raise serializers.ValidationError({"message": "Request Processing Error. Please Try Again Later"})

        values["user_profile"] = user_profile_instance
        values["withdrawal_otp_instance"] = withdrawal_otp_instance.last()
        return values


class TopUpSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=13)
    amount = serializers.FloatField()
    pin = serializers.CharField(max_length=4, min_length=4, required=True, style={"input_type": "password"})

    def validate(self, values):
        phone_number = values.get("phone_number")
        otp = values.get("pin")
        amount = values.get("amount")

        # Retrieve the user's profile instance based on the provided phone number
        user_profile_instance = UserProfile.objects.filter(phone_number=phone_number).last()
        if not user_profile_instance:
            raise PermissionDenied(f"User {phone_number} does not exist")

        # Check if the user's profile is verified
        verified = user_profile_instance.verification_code
        if not verified:
            raise PermissionDenied("User not Verified")

        # Check if the user's profile email is verified
        if not user_profile_instance.email_is_verified:
            raise PermissionDenied("User email not verified")

        # Check if the user is blacklisted
        if BlackListed.is_blacklisted(phone=phone_number):
            raise PermissionDenied("User Black listed")

        # Check if the OTP is valid and active
        withdrawal_otp_instance = WithdrawalPINSystem.get_active_otp(otp=otp, phone_number=phone_number)
        is_otp_active = withdrawal_otp_instance.exists()

        if is_otp_active is False:
            raise serializers.ValidationError({"message": "Invalid or Used Withdrawal PIN"})

        # Retrieve the user's wallet for withdrawals
        user_wallet = UserWallet.objects.filter(user=user_profile_instance, wallet_tag="WEB").last()
        if user_wallet is None:
            raise serializers.ValidationError({"message": "No Withdrawal Wallet Found"})

        # Check if the requested withdrawal amount exceeds the available balance
        if amount > user_wallet.withdrawable_available_balance:
            raise serializers.ValidationError({"message": "Insufficient Winning Balance"})

        values["user_instance"] = user_wallet.user
        values["withdrawal_otp_instance"] = withdrawal_otp_instance.last()
        return values


class EmailVerificationSerializer(serializers.Serializer):
    email = serializers.EmailField()
    phone_number = serializers.CharField(max_length=13, min_length=11)


class EmailOtpVerificationSerializer(serializers.Serializer):
    email = serializers.EmailField()
    phone_number = serializers.CharField(max_length=13, min_length=11)
    otp = serializers.CharField(max_length=6, min_length=6)

    def validate(self, values):
        """
        Validates the input values, specifically for email and OTP verification.

        Parameters:
        - self: The instance of the serializer.
        - values (dict): A dictionary containing input values including 'email', 'otp', and 'phone_number'.

        Returns:
        - dict: A dictionary containing validated values, including 'user_instance'.

        This method performs the following validations:
        1. Checks if the provided 'phone_number' corresponds to a valid user in the UserProfile model.
        2. Compares the provided 'email' with the preverified email associated with the user while trying to get otp.
        3. Verifies the provided 'otp' using the UserProfileOtp.verify_otp method.
        If any validation fails, a serializers.ValidationError is raised with an appropriate message.

        Example:
        # >>> input_values = {'email': '<EMAIL>', 'otp': '123456', 'phone_number': '1234567890'}
        # >>> validated_values = serializer_instance.validate(input_values)
        # >>> print(validated_values)
        {'email': '<EMAIL>', 'otp': '123456', 'phone_number': '1234567890', 'user_instance': <UserProfile object>}
        """
        email = values.get("email")
        otp = values.get("otp")
        phone_number = values.get("phone_number")

        user_instance = UserProfile.objects.filter(phone_number=phone_number).last()
        if not user_instance:
            raise serializers.ValidationError({"message": "Invalid user"})

        if user_instance.preverified_email != email:
            raise serializers.ValidationError({"message": "Provided email does not match. Please double-check and try again."})

        otp_is_valid = UserProfileOtp.verify_otp(user_instance=user_instance, otp=otp)
        if not otp_is_valid.get("verified"):
            raise serializers.ValidationError({"message": otp_is_valid.get("message")})

        values["user_instance"] = user_instance
        return values
