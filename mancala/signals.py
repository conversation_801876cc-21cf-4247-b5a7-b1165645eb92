from django.db.models.signals import post_save
from django.dispatch import receiver

from ads_tracker.helpers.helpers import send_ads_tracker_postback_url
from ads_tracker.models import MobidTracker
from mancala.models import Interaction


@receiver(post_save, sender=Interaction)
def send_mancala_angel_promoter_notification(sender, instance, created, *args, **kwargs):
    """
    Notifies Angel games promoter when there is a conversion.
    A conversion is when a generated lead makes payment for a
    game played for the first time.

    Notification for a particular user is only paid once
    """
    if created:
        # Check that this user has ever paid for a game in the past
        if instance.type == "CREDIT":  # N.B Credit implies debits on the player's wallet from mancala
            player_interaction = Interaction.objects.filter(ExtraData=instance.ExtraData, type="CREDIT")

            interaction_count = player_interaction.count()  # number of player interactions
            # print(interaction_count, "\n\n\n", "COUNT")
            if interaction_count == 1:
                user_phone = instance.ExtraData
                ad_tracker_instance = MobidTracker.objects.filter(phone_number=user_phone).last()  #

                if ad_tracker_instance:
                    promoter = ad_tracker_instance.source

                    # Trigger Postback
                    ad_tracker_instance.converted = True
                    ad_tracker_instance.amount_played = instance.Amount
                    ad_tracker_instance.save()

                    send_ads_tracker_postback_url(source=promoter, click_id=ad_tracker_instance.click_id, amount=instance.Amount)
                else:
                    pass
                return "DONE"
