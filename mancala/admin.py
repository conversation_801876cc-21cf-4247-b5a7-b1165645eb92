from django.contrib import admin
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from mancala.models import Interaction, RequestPayload

# Register your models here.


# RESOURCES
class InteractionResource(resources.ModelResource):
    class Meta:
        model = Interaction


class RequestPayloadResource(resources.ModelResource):
    class Meta:
        model = RequestPayload


class InteractionResourceAdmin(ImportExportModelAdmin):
    resource_class = InteractionResource
    search_fields = ["ExtraData"]
    list_filter = (
        "type",
        "created_at",
    )
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RequestPayloadResourceAdmin(ImportExportModelAdmin):
    resource_class = InteractionResource
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(Interaction, InteractionResourceAdmin)
admin.site.register(RequestPayload, RequestPayloadResourceAdmin)
