import uuid
from datetime import datetime

from django.db import models

from main.models import ConstantVariable, UserProfile
from wallet_app.models import DebitCreditRecord, UserWallet

# from time import sleep


class Interaction(models.Model):
    TYPES = (
        ("CREDIT", "CREDIT"),
        ("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"),
        ("DEBIT", "DEBIT"),
        ("REFUND", "REFUND"),
    )

    ExtraData = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    hash = models.CharField(max_length=100, null=True, blank=True)
    SessionId = models.CharField(max_length=100, null=True, blank=True)
    RoundGuid = models.CharField(max_length=100, null=True, blank=True)
    type = models.CharField(max_length=100, choices=TYPES, null=True, blank=True)
    TransactionGuid = models.CharField(max_length=100, null=True, blank=True)
    LinesCount = models.FloatField(default=0)
    Amount = models.FloatField(default=0)
    Hash = models.CharField(max_length=100, null=True, blank=True)
    BonusTransaction = models.BooleanField(default=True)
    ExternalBonusId = models.CharField(max_length=100, null=True, blank=True)

    @classmethod
    def get_player(cls, identifier):
        """
        Retrieve a user's profile based on their phone number.

        This function queries the database to find a user's profile based on the
        provided phone number (identifier). If a matching user profile is found,
        it returns the latest (most recent) profile. If no matching profile is found,
        it returns None.

        Args:
            cls (class): The class to which this method belongs.
            identifier (str): The phone number used to identify the user.

        Returns:
            UserProfile or None: The latest UserProfile object matching the provided
            phone number, or None if no matching profile is found.

        Raises:
            None

        Example:
            identifier = "+1234567890"
            profile = cls.get_player_wallet_data(identifier)
            if profile:
                print(f"User's profile found: {profile}")
            else:
                print("User profile not found.")
        """
        user_qs = UserProfile.objects.filter(phone_number=identifier)
        if not user_qs.exists():
            return None
        else:
            return user_qs.last()

    @classmethod
    def wallet_balance(cls, user_instance) -> dict:
        """
        Retrieve the wallet balance of a user for the "WEB" (mancala) wallet tag.

        This function checks the user's wallet for a specific wallet tag ("WEB").
        If the wallet does not exist, it creates a new one for the user and returns
        the initial game available balance. If the wallet already exists, it returns
        the current game available balance.

        Args:
            cls (class): The class to which this method belongs.
            user_instance (User): The user for whom the wallet balance is retrieved.

        Returns:
            int: The game available balance of the user's "WEB" wallet.

        Raises:
            None

        Example:
            user = User.objects.get(username='example_user')
            balance = cls.wallet_balance(user)
            print(f"User's WEB wallet balance: {balance}")
        """
        user_wallet = UserWallet.objects.filter(user=user_instance, wallet_tag="WEB").last()
        if user_wallet is None:
            user_wallet = UserWallet.objects.create(user=user_instance, wallet_tag="WEB")

        _mancala_running_bal = ConstantVariable.get_mancala_running_bal()
        result = {"wallet_instance": user_wallet, "balance": 0.00 if _mancala_running_bal < 10 else user_wallet.game_available_balance}

        return result

    @classmethod
    def web_balance_enquiry(cls, user_instance) -> dict:
        """
        Perform a balance enquiry for the user's web wallet.

        Args:
            user_instance: An instance of the user model.

        Returns:
            dict: A dictionary containing information about the wallet and balances.
                  Keys include "wallet_instance", "acct_name", "acct_number", "bank_name",
                  "play_balance", and "winning_balance".
        """
        # Create or update the user's web wallet
        user_wallet = UserWallet.create_update_wallet(user_instance=user_instance, tag="WEB", acct_provider="WEMA")

        # Extract Wema account details and wallet balances
        wema_details = user_wallet.wema_account
        result = {
            "wallet_instance": user_wallet,
            "acct_name": wema_details.acct_name if wema_details else "",
            "acct_number": wema_details.vnuban if wema_details else "",
            "bank_name": wema_details.bank_name if wema_details else "",
            "play_balance": user_wallet.game_available_balance if user_wallet else 0.0,
            "winning_balance": user_wallet.withdrawable_available_balance if user_wallet else 0.0,
        }

        return result

    @classmethod
    def debit_or_credit_wallet(cls, wallet_instance, amount_to_charge, transaction_type, refund=False) -> float:
        """
        Debit or credit the user's wallet based on the specified transaction type and create transaction records.

        Args:
            cls: The class (typically the model) where this method is defined.
            wallet_instance: An instance of the user's wallet to debit or credit.
            amount_to_charge: The amount to debit or credit to the user's wallet.
            transaction_type: A string indicating the type of transaction ("DEBIT" or "CREDIT").
            refund: not notify if transaction type is reversal.

        Returns:
            float: The updated available balance in the user's wallet after the transaction.

        This method performs the following steps:
        1. Generates a unique payout reference based on a UUID and timestamp.
        2. Retrieves the user instance associated with the wallet.
        3. Creates a debit or credit transaction record for the user, specifying details like the phone number, amount, channel, and reference.
        4. Prepares payload information for the wallet transaction.
        5. Deducts or credits the specified amount to/from the user's wallet, specifying transaction details.
        6. If the transaction type is "DEBIT," it deducts the same amount from a general withdrawable wallet.
        7. Returns the updated available balance in the user's wallet.

        Note:
            # N.B Credit implies debit on the player's wallet from mancala
        - This method is typically used for handling both debit and credit transactions in a web-based application.
        - Ensure that proper error handling and validation are implemented when integrating this method into your application.

        Example usage:
        ```
        updated_balance = debit_or_credit_wallet(WalletClass, user_wallet_instance, 50.0, "CREDIT")
        print(f"Updated wallet balance: ${updated_balance:.2f}")
        ```
        """
        # print(transaction_type, "\n\n Transaction Type")

        payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"
        _user_instance = wallet_instance.user
        phone_number = _user_instance.phone_number
        if (transaction_type == "CREDIT" or transaction_type == "MANCALA_GAME_PLAY_RVSL" and amount_to_charge > 0) or transaction_type == "DEBIT":
            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=phone_number,
                amount=amount_to_charge,
                channel="WEB",
                reference=payout_reference,
                transaction_type=transaction_type,
            )

            wallet_payload = {
                "transaction_from": "MANCALA_GAME_PLAY",
                "game_type": "",
                "game_play_id": "",
            }

            if transaction_type == "DEBIT":
                # deplete mancala running balance so as to limit outrageous plays
                ConstantVariable.deplete_mancala_running_bal(play_amount=amount_to_charge)
                # charge player wallet
                UserWallet.deduct_wallet(
                    user=_user_instance,
                    amount=int(amount_to_charge),
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="GAME_PLAY_WALLET",
                    **wallet_payload,
                )

                # GeneralWithdrawableWallet.deduct_fund(amount=amount_to_charge, phone=phone_number)
                wallet_instance.refresh_from_db()
                return wallet_instance.game_available_balance

            elif transaction_type == "MANCALA_GAME_PLAY_RVSL":
                wallet_payload["transaction_from"] = "MANCALA_GAME_PLAY_RVSL"

                UserWallet.fund_wallet(
                    user=_user_instance,
                    amount=int(amount_to_charge),
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="GAME_PLAY_WALLET",
                    **wallet_payload,
                )
                wallet_instance.refresh_from_db()
                balance = wallet_instance.game_available_balance  # + float(amount_to_charge)
                return balance

            elif transaction_type == "CREDIT":
                wallet_payload["transaction_from"] = "MANCALA_WINNING"

                UserWallet.fund_wallet(
                    user=_user_instance,
                    amount=int(amount_to_charge),
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    # user_wallet_type="WINNINGS_WALLET" if refund is False else "GAME_PLAY_WALLET",
                    user_wallet_type="WINNINGS_WALLET",
                    **wallet_payload,
                )
                wallet_instance.refresh_from_db()
                balance = wallet_instance.game_available_balance  # + float(amount_to_charge)
                return balance
        else:
            wallet_instance.refresh_from_db()
            balance = wallet_instance.game_available_balance
            return balance


class RequestPayload(models.Model):
    payload = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


# class Player(models.Model):
#     phone_number = models.CharField(max_length=100, null=True, blank=True)
#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)
#     interacted = models.BooleanField(default=False)
#
#     @classmethod
#     def update_player_info(cls, player_number):
#         player_qs = cls.objects.filter(phone_number=player_number)
#
#         if player_qs.exists():
#
#             player_instance = player_qs.first()
#             interaction_qs = Interaction.objects.filter(ExtraData=player_number)
#
#             if player_instance.interacted is False and not interaction_qs:
#                 player_instance.interacted = True
#                 player_instance.save()
#
#         else:
#             cls.objects.create(phone_number=player_number)
#
#         return
