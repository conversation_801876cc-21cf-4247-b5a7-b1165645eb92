from django.urls import path

from mancala.views import (
    BalanceEnquiryView,
    BalanceView,
    BankTransfer,
    CreditView,
    DebitView,
    EmailVerification,
    RefundView,
    TopUp,
    TransactionHistory,
    UpdateUserVerificationCode,
)

main_urls = [
    path("Balance", BalanceView.as_view()),
    path("Credit", CreditView.as_view()),
    path("Debit", DebitView.as_view()),
    path("Refund", RefundView.as_view()),
]
web_request = [
    path("web/balance/", BalanceEnquiryView.as_view()),
    path("web/update_verification_code/", UpdateUserVerificationCode.as_view()),
    path("web/withdrawal/", BankTransfer.as_view()),
    path("web/top_up/", TopUp.as_view()),
    path("web/verify_email/", EmailVerification.as_view()),
    path("web/transaction_history/", TransactionHistory.as_view()),
]
urlpatterns = [
    *main_urls,
    *web_request,
]
