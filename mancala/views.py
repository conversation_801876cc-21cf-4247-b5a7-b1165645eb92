import time
import uuid
from decimal import Decimal

from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework.views import APIView

from account.models import UserProfileOtp
from main.models import ConstantVariable, PayoutTransactionTable, UserProfile
from main.permissions import IpWhiteListPermission
from main.tasks import lottery_play_engange_event, send_email  # , send_email
from main.ussd.bankdb import filter_bank
from mancala.models import Interaction, RequestPayload
from mancala.serializers import (
    BalanceSerializer,
    EmailOtpVerificationSerializer,
    EmailVerificationSerializer,
    TopUpSerializer,
    WebBalanceEnquirySerializer,
    WinningWithdrawalSerializer,
)
from wallet_app.helpers.payment_gateway import PaymentGateway
from wallet_app.models import DebitCreditRecord, UserWallet, WalletTransaction

User = get_user_model()


class CustomPageNumberPagination(PageNumberPagination):
    page_size = 10  # Adjust the page size as needed
    page_size_query_param = "page_size"
    max_page_size = 100


class BalanceView(APIView):
    permission_classes = [IpWhiteListPermission]

    def post(self, request):
        print("MANCALA REQUEST DATA -----> \n\n\n", request.data)

        data = {"Error": 213, "Msg": "Not available for now"}
        return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        RequestPayload.objects.create(payload=request.data)
        serializer = BalanceSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        serializer.save(type="BALANCE")

        identifier = validated_data.get("ExtraData")
        _interaction = Interaction()
        player_instance = _interaction.get_player(identifier=identifier)

        if player_instance is None:
            data = {"Error": 213, "Msg": "Invalid User"}
            print("BALANCE ENQUIRY RESPONSE HTTP_403_FORBIDDEN", data)
            return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        else:
            player_balance = _interaction.wallet_balance(user_instance=player_instance).get("balance")
            data = {"Error": 0, "Balance": Decimal(player_balance)}
            print("BALANCE ENQUIRY RESPONSE HTTP_200_OK", data)
            return Response(data=data, status=status.HTTP_200_OK)


class CreditView(APIView):
    permission_classes = [IpWhiteListPermission]

    def post(self, request):
        data = {"Error": 213, "Msg": "Not available for now"}
        return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        RequestPayload.objects.create(payload=request.data)
        serializer = BalanceSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        serializer.save(type="CREDIT")

        identifier = validated_data.get("ExtraData")
        _interaction = Interaction()
        player_instance = _interaction.get_player(identifier=identifier)

        if player_instance is None:
            data = {"Error": 213, "Msg": "Invalid User"}
            return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        else:
            wallet_balance_and_wallet_instance = _interaction.wallet_balance(user_instance=player_instance)
            game_play_amount = validated_data.get("Amount", 99999999999999999)

            player_balance = wallet_balance_and_wallet_instance.get("balance")

            if game_play_amount > player_balance:
                data = {"Error": 213, "Msg": "Insufficient Balance"}
                return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

            else:
                # charge user wallet
                wallet_instance = wallet_balance_and_wallet_instance.get("wallet_instance")

                print("|||||||||||||||||||||||||||")
                print("|||||||||||||||||||||||||||")
                print("CHARGE", game_play_amount)
                print("|||||||||||||||||||||||||||")
                balance_after_charge = _interaction.debit_or_credit_wallet(
                    wallet_instance=wallet_instance,
                    amount_to_charge=game_play_amount,
                    transaction_type="DEBIT",
                )

                data = {"Error": 0, "Balance": Decimal(balance_after_charge)}
                return Response(data=data, status=status.HTTP_200_OK)


class DebitView(APIView):
    permission_classes = [IpWhiteListPermission]

    def post(self, request):
        data = {"Error": 213, "Msg": "Not available for now"}
        return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        RequestPayload.objects.create(payload=request.data)
        serializer = BalanceSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        serializer.save(type="DEBIT")

        identifier = validated_data.get("ExtraData")
        _interaction = Interaction()
        player_instance = _interaction.get_player(identifier=identifier)

        if player_instance is None:
            data = {"Error": 213, "Msg": "Invalid User"}
            return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        else:
            wallet_balance_and_wallet_instance = _interaction.wallet_balance(user_instance=player_instance)
            game_play_amount = validated_data.get("Amount", 10000000000)
            # char
            wallet_instance = wallet_balance_and_wallet_instance.get("wallet_instance")
            balance_after_charge = _interaction.debit_or_credit_wallet(
                wallet_instance=wallet_instance,
                amount_to_charge=float(game_play_amount),
                transaction_type="CREDIT",
            )
            print(balance_after_charge)

            data = {"Error": 0, "Balance": Decimal(balance_after_charge)}
            return Response(data=data, status=status.HTTP_200_OK)


class RefundView(APIView):
    permission_classes = [IpWhiteListPermission]

    def post(self, request):
        data = {"Error": 213, "Msg": "Not available for now"}
        return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        RequestPayload.objects.create(payload=request.data)
        serializer = BalanceSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        serializer.save(type="REFUND")

        identifier = validated_data.get("ExtraData")
        _interaction = Interaction()
        player_instance = _interaction.get_player(identifier=identifier)

        if player_instance is None:
            data = {"Error": 213, "Msg": "Invalid User"}
            return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        else:
            wallet_balance_and_wallet_instance = _interaction.wallet_balance(user_instance=player_instance)
            validated_data.get("Amount", 0)

            wallet_instance = wallet_balance_and_wallet_instance.get("wallet_instance")

            # get players last mancala game play transaction
            user_wallet_transaction = WalletTransaction.objects.filter(
                wallet=wallet_instance,
                transaction_type="MANCALA_GAME_PLAY",
            ).last()

            balance_after_charge = _interaction.debit_or_credit_wallet(
                wallet_instance=wallet_instance,
                amount_to_charge=float(user_wallet_transaction.amount),
                transaction_type="MANCALA_GAME_PLAY_RVSL",
                refund=True,
            )

            data = {"Error": 0, "Balance": Decimal(balance_after_charge)}
            return Response(data=data, status=status.HTTP_200_OK)


class BalanceEnquiryView(APIView):
    def post(self, request):
        data = {"Error": 213, "Msg": "Not available for now"}
        return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        # print("REQUEST DATA -------->", request.data)
        serializer = WebBalanceEnquirySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        phone_number = validated_data.get("phone_number")
        interaction_object = Interaction()
        player_instance = interaction_object.get_player(identifier=phone_number)

        balance_enquiry = interaction_object.web_balance_enquiry(user_instance=player_instance)
        acct_name = balance_enquiry.get("acct_name")
        acct_number = balance_enquiry.get("acct_number")
        bank_name = balance_enquiry.get("bank_name")
        play_balance = balance_enquiry.get("play_balance")
        winning_balance = balance_enquiry.get("winning_balance")

        data = {
            "Error": 0,
            "data": {
                "acct_name": acct_name,
                "acct_number": acct_number,
                "bank_name": bank_name,
                "play_balance": play_balance,
                "winning_balance": winning_balance,
                "email_verified": player_instance.email_is_verified,
            },
        }
        # print("<--> WEB VIEW BALANCE ENQUIRY <-->")
        # print("<--> WEB VIEW BALANCE ENQUIRY <-->")
        # print("DATA ->", data)
        # print("<--> WEB VIEW BALANCE ENQUIRY <-->")
        # print("<--> WEB VIEW BALANCE ENQUIRY <-->")
        return Response(data=data, status=status.HTTP_200_OK)


class UpdateUserVerificationCode(APIView):
    def post(self, request):
        data = {"Error": 213, "Msg": "Not available for now"}
        return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        # serializer = UpdateVerificationCodeSerializer(data=request.data)
        # serializer.is_valid(raise_exception=True)
        # validated_data = serializer.validated_data
        # validated_data.get("phone_number")
        # pin_generated = UserProfile.update_verification_code(phone_number=phone_number, channel="WEB")
        # data = {"Error": 0, "Message": "Verification code generated successfully"}
        # return Response(data=data, status=status.HTTP_200_OK)


class BankTransfer(APIView):
    # permission_classes = []
    serializer_class = WinningWithdrawalSerializer

    def post(self, request):
        data = {"Error": 213, "Msg": "Not available for now"}
        return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        account_number = validated_data.get("account_number")
        bank_code = validated_data.get("bank_code")
        amount = validated_data.get("amount")
        user_profile = validated_data.get("user_profile")
        withdrawal_otp_instance = validated_data.get("withdrawal_otp_instance")

        # print(user_profile, withdrawal_otp_instance, "\n INSTANCES")

        payment_gateway_object = PaymentGateway()
        verify_bank_response = payment_gateway_object.fetch_account_name(account_number=account_number, bank_code=bank_code)
        if isinstance(verify_bank_response, dict):
            if verify_bank_response.get("status") is False:
                return Response(
                    data={"message": "Invalid bank details"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            verify_bank_response.get("data").get("account_name")
            # reference = "withdraw-{}".format(uuid.uuid4())
            _filter_bank_details = filter_bank(cbn_code=bank_code)

            if _filter_bank_details is None:
                return Response(
                    data={"message": "Invalid bank details"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            withdrawal_otp_instance.mark_as_used()
            if ConstantVariable.get_constant_variable().get("payout_source") == "WOVEN":
                PayoutTransactionTable().user_woven_disbursement(
                    amount=amount,
                    user_profile_instance=user_profile,
                    user_id=user_profile.id,
                    bank_name=_filter_bank_details.get("name"),
                    account_number=account_number,
                )

            elif (
                ConstantVariable.get_constant_variable().get("payout_source") == "VFD"
                or ConstantVariable.get_constant_variable().get("payout_source") == "BUDDY"
            ):
                PayoutTransactionTable().user_vfd_payout(
                    amount=amount,
                    user_profile_instance=user_profile,
                    user_id=user_profile.id,
                    bank_name=_filter_bank_details.get("name"),
                    account_number=account_number,
                )
            return Response(data={"message": "Withdrawal initiated"}, status=status.HTTP_200_OK)

        else:
            return Response(
                data={"message": "Invalid bank details"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class TopUp(APIView):
    serializer_class = TopUpSerializer

    def post(self, request):
        data = {"Error": 213, "Msg": "Not available for now"}
        return Response(data=data, status=status.HTTP_403_FORBIDDEN)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        amount = validated_data.get("amount")
        phone_number = validated_data.get("phone_number")
        user_instance = validated_data.get("user_instance")
        withdrawal_otp_instance = validated_data.get("withdrawal_otp_instance")

        withdrawal_otp_instance.mark_as_used()

        transaction_ref = f"{uuid.uuid4()}-{int(time.time())}"

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=phone_number,
            amount=amount,
            channel="WEB",
            reference=transaction_ref,
            transaction_type="DEBIT",
        )

        wallet_payload = {"transaction_from": "WITHDRAW_TO_PLAY_WALLET"}

        UserWallet.deduct_wallet(
            user=user_instance,
            amount=int(amount),
            channel="WEB",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **wallet_payload,
        )

        transaction_ref = f"{uuid.uuid4()}-{int(time.time())}"

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=phone_number,
            amount=amount,
            channel="WEB",
            reference=transaction_ref,
            transaction_type="CREDIT",
        )

        wallet_payload = {
            "transaction_from": "FUNDING_FROM_WITHDRAWABLE_WALLET",
        }

        UserWallet.fund_wallet(
            user=user_instance,
            amount=int(amount),
            channel="WEB",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="GAME_PLAY_WALLET",
            **wallet_payload,
        )
        engage_event_payload = {
            "event": "USER_WALLET_FUNDING",
            "properties": {
                "FUNDING_CHANNEL": "WINNING WALLET",
            },
        }
        lottery_play_engange_event.delay(
            user_id=user_instance.id,
            is_user_profile_id=True,
            **engage_event_payload,
        )

        data = {"message": "You've successfully added funds to your Play Wallet."}
        return Response(data=data, status=status.HTTP_200_OK)


class EmailVerification(APIView):
    serializer_class = EmailVerificationSerializer

    def post(self, request):
        """
        Handles the HTTP POST request for email verification.

        Parameters:
        - self: The instance of the view.
        - request: The HTTP request object containing data for email verification.

        Returns:
        - Response: A JSON response indicating the result of the email verification process.

        This method performs the following actions:
        1. Validates the incoming data using the serializer associated with the view.
        2. Retrieves validated data, including 'phone_number' and 'email'.
        3. Checks if a user with the provided 'phone_number' exists in the UserProfile model.
           If not, returns a 400 Bad Request response with an appropriate message.
        4. Generates a new OTP using UserProfileOtp.create_otp for the user, and associates the provided email
           with the user for future verification.
        5. Sends an email with the generated OTP for verification using an asynchronous task.
        6. Returns a 200 OK response indicating that the OTP has been sent successfully.

        Example:
        # >>> POST /email-verification/
        # >>> Request Body: {'phone_number': '1234567890', 'email': '<EMAIL>'}
        # >>> Response: {'error': 0, 'message': 'OTP sent successfully'}
        """
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        phone_number = validated_data.get("phone_number")
        email = validated_data.get("email")

        user_instance = UserProfile.objects.filter(phone_number=phone_number).last()
        if not user_instance:
            return Response(data={"message": "Invalid user"}, status=status.HTTP_400_BAD_REQUEST)

        otp = UserProfileOtp.create_otp(user_instance=user_instance)
        user_instance.preverified_email = email
        user_instance.email_is_verified = False
        user_instance.save()

        send_email.delay(
            recipient=email,
            subject="Email Verification",
            template_dir="email_verification.html",
            otp=otp,
        )

        return Response(
            data={"error": 0, "message": "OTP sent successfully"},
            status=status.HTTP_200_OK,
        )

    def put(self, request):
        serializer = EmailOtpVerificationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        email = validated_data.get("email")
        user_instance = validated_data.get("user_instance")
        user_instance.email = email
        user_instance.email_is_verified = True
        user_instance.save()

        return Response(
            data={"error": 0, "message": "Email verification was successful"},
            status=status.HTTP_200_OK,
        )


class TransactionHistory(APIView):
    def post(self, request):
        serializer = WebBalanceEnquirySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data

        phone_number = validated_data.get("phone_number")

        user = UserProfile.objects.filter(phone_number=phone_number).last()

        wallet_transactions = WalletTransaction.objects.filter(wallet__user=user, show_transaction=True).values(
            "id",
            "transaction_type",
            "transaction_reference",
            "transaction_from",
            "method",
            "amount",
            "status",
            "date_created",
        )

        # Create a paginator instance
        paginator = CustomPageNumberPagination()
        # paginator.page_size = 10  # Adjust the page size as needed

        # Paginate the queryset
        paginated_queryset = paginator.paginate_queryset(wallet_transactions, request)

        return paginator.get_paginated_response(paginated_queryset)
