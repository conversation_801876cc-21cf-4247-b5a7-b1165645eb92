from rest_framework import serializers


class CoralPayAuthSerializer(serializers.Serializer):
    """
    Serializer for CoralPay authentication
    """

    customerRef = serializers.CharField()
    merchantId = serializers.CharField()
    shortCode = serializers.CharField()


class OpenAIPromptSerializer(serializers.Serializer):
    prompt = serializers.CharField()



class RedoceanAfricaFetchPredictWiseGamesSerializer(serializers.Serializer):
    phone_number = serializers.CharField()


class SetRedoceanWebhookUrlsSerializer(serializers.Serializer):
    content_delivery_wehbook_url = serializers.CharField()
    subscription_delivery_webhook_url = serializers.CharField()





