from django.db.models import TextChoices
from django.utils.translation import gettext_lazy as _


class PurposeChoices(TextChoices):
    MORBID_RENEWALS = "MORBID_RENEWALS", "MOR<PERSON><PERSON>_RENEWALS"
    GAME_SUBSCRIPTION_NOTIFICATION = "GAME_SUBSCRIPTION_NOTIFICATION", "GAME_SUBSCRIPTION_NOTIFICATION"
    GAME_WINNING_NOTIFICATION = "GAME_WINNING_NOTIFICATION", "GAME_WINNING_NOTIFICATION"
    SALARY_FOR_LIFE_LOST_TICKET_SMS_NOTIFICATION = (
        "SALARY_FOR_LIFE_LOST_TICKET_SMS_NOTIFICATION",
        "SALARY_FOR_LIFE_LOST_TICKET_SMS_NOTIFICATION",
    )
    NITROSWITCH_RENEWALS = "NITROSWITCH_RENEWALS", "NITROSWITCH_RENEWALS"
