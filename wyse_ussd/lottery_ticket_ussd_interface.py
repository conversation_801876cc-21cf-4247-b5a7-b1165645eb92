

import random
from main.ussd.helpers import Utility
from scratch_cards.models import GameShowLotteryTicket
from wyse_ussd.helper.bank import BankManger
from wyse_ussd.helper.general_helper import select_bank
from wyse_ussd.helper.scratch_card_ussd_helper import UssdScratchCardHelper


def lottery_ticket_ussd(phone_number: str, service_code: str, text: str, session_id: str, network_code: str):
    splited_text = text.split("*")
    splited_text = [i for i in splited_text if i != ""]

    if text.startswith("47"):
        if text == "47":
            response = "CON Please, enter your ticket game id\n"

            return response
        else:
            if len(splited_text) == 2:
                response = "CON Please, enter your ticket serial number\n"
                return response

            seial_number_int = splited_text[1]
            pin = splited_text[2]
        
            try:
                int(pin)
            except Exception:
                return "END Invalid input. game pin  has to be a number"

            # hashed_pin = f"password{pin}"
            hashed_pin = str(int(pin))  # hashlib.md5(hashed_pin.encode()).hexdigest()

            scratch_card_instance = GameShowLotteryTicket.objects.filter(serial_number_int=seial_number_int, pin=int(hashed_pin)).last()
            
            if scratch_card_instance is None:
                response = "END Ticket not found."
                return response
            
            if not scratch_card_instance.sold:
                response = "END Ticket not yet sold out."
                return response

            if len(splited_text) == 3:
                response = "CON Please, enter your game id.\n"
                return response

            if len(splited_text) == 4:
                game_id = splited_text[3]
                
                scratch_card_instance = GameShowLotteryTicket.objects.filter(serial_number_int=seial_number_int, pin=int(hashed_pin), game_id=game_id).last()
                if not scratch_card_instance:
                    response = "END Invalid game id"
                    return response
               
                winning_status = "pending"

                if str(scratch_card_instance.instant_win_status).upper() == "WON":
                    winning_status = "winning"

                elif str(scratch_card_instance.instant_win_status).upper() == "LOST":
                    winning_status = "lost"
                    # if scratch_card_instance.winning_status is None:
                    #     winning_status = random.choices(
                    #         ["lost", "daily draw", "weekly draw", "weekly draw", "lost", "daily draw", "lost", "daily draw", "weekly draw", "lost"]
                    #     )
                    #     scratch_card_instance.winning_status = winning_status
                    #     scratch_card_instance.save()
                    # else:
                    #     winning_status = scratch_card_instance.winning_status
                
                if winning_status == "winning":
                    if scratch_card_instance.player_phone_number is not None:
                        if scratch_card_instance.player_phone_number != phone_number:
                            message = "END Sorry, this ticket has already been scratched."
                        
                        elif scratch_card_instance.scratched:
                            message = "END Sorry, You have already scratched this ticket."
                        
                        return message
                
                scratch_card_instance.player_phone_number = phone_number
                scratch_card_instance.winning_status = winning_status
                scratch_card_instance.scratched = True
                scratch_card_instance.paid = True
                scratch_card_instance.save()
                
                wk_1 = scratch_card_instance.weekly_draw_number_1
                wk_2 = scratch_card_instance.weekly_draw_number_2
                wk_3 = scratch_card_instance.weekly_draw_number_3
                wk_4 = scratch_card_instance.weekly_draw_number_4
                wk_5 = scratch_card_instance.weekly_draw_number_5
                
                if winning_status == "winning":
                    message = f"END this is a winning ticket.\nAmount won: {scratch_card_instance.earning_amount}. Dial this code to withdraw your winnings *347*180*89#\nYou have also entered the weekly draw.\nYour weekly draw numbers are {wk_1}, {wk_2}, {wk_3}, {wk_4}, {wk_5}."
                    
                elif winning_status == "lost":
                    message = f"END You have entered for the weekly draw.\nYour weekly draw numbers are {wk_1}, {wk_2}, {wk_3}, {wk_4}, {wk_5}."

                return message
                
            else:
                response = "END Invalid command."
                return response

    elif str(text).strip().startswith("89"):
        if text == "89":
            response = "CON Welcome to your scratch card winning withdrawal\n"
            response += "Enter your card serial number\n"

            return response

        if len(splited_text) == 2:
            serial_number = splited_text[-1]

            try:
                scratch_card_instance = GameShowLotteryTicket.objects.get(
                    index=serial_number, player_phone_number=phone_number, claimed=False
                )
                print(scratch_card_instance, "SCRATCH CARD INSTANCE")
            except GameShowLotteryTicket.DoesNotExist:
                return "END Invalid serial number or no ticket found"

            response = f"CON Your winning amount is: {Utility.currency_formatter(scratch_card_instance.earning_amount)}\n"
            response += "1. Continue with your withdrawal\n"
            response += "2. Exit\n"
            response += "99. Back"

            return response

        if len(splited_text) == 3:
            if splited_text[-1] == "2":
                return "END Thank you."

            serial_number = splited_text[1]

            try:
                scratch_card_instance = GameShowLotteryTicket.objects.get(
                    index=serial_number, player_phone_number=phone_number, claimed=False
                )
            except GameShowLotteryTicket.DoesNotExist:
                return "END Invalid serial number or no ticket found"
            response = select_bank()

            return response

        if len(splited_text) == 4:
            serial_number = splited_text[1]

            try:
                scratch_card_instance = GameShowLotteryTicket.objects.get(
                    index=serial_number, player_phone_number=phone_number, claimed=False
                )
            except GameShowLotteryTicket.DoesNotExist:
                return "END Invalid serial number or no ticket found"

            bank = select_bank(splited_text[3])
            bank_db = BankManger().bank_details(bank_name=bank)

            if not bank_db:
                return "END Invalid bank"

            return "CON Enter your account number \n"

        if len(splited_text) == 5:
            serial_number = splited_text[1]

            try:
                scratch_card_instance = GameShowLotteryTicket.objects.get(
                    index=serial_number, player_phone_number=phone_number, claimed=False
                )
            except GameShowLotteryTicket.DoesNotExist:
                return "END Invalid serial number or no ticket found"

            bank = select_bank(splited_text[3])
            bank_db = BankManger().bank_details(bank_name=bank)

            account_number = splited_text[4]

            if not bank_db:
                return "END Invalid bank"

            bank_code = bank_db.get("cbn_code")

            ussd_scratch_card_helper = UssdScratchCardHelper()
            try:
                ussd_scratch_card_helper.claim_winning(
                    serial_number=scratch_card_instance.index,
                    pin=scratch_card_instance.pin,
                    account_number=account_number,
                    bank_code=bank_code,
                    phone_number=phone_number,
                    use_new_withdrawal_flow=True,
                )
            except Exception as e:
                print(f"Error claiming winning: {e}")
                # pass

            return "END your request has been sent for processing"

