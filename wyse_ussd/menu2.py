from dataclasses import dataclass
from time import sleep

from django.db.models import Q

from account.models import BlackListed, Otp
from awoof_app.models import AwoofGameTable, LifeStyleTable
from main.helpers.helper_functions import fetch_account_name
from main.helpers.hops_restriction_helper import HopsRestrictor
from main.helpers.illution_feature import IllusionDatabase
from main.helpers.redis_storage import RedisStorage
from main.models import ConstantVariable, LotteryModel, LottoTicket, UserProfile
from main.ussd.helpers import Utility
from pos_app.models import (
    BoughtLotteryTickets,
    LottoAgentGuarantorDetail,
    PosLotteryWinners,
)
from pos_app.sport_helper.soccer_reg import register_and_update_ticket_to_table
from wallet_app.models import UserWallet, WalletTransaction
from wyse_ussd.helper.bank import BankManger
from wyse_ussd.helper.general_helper import (
    has_enough_money_to_giveout,
    lottery_ticket_result,
    no_option,
    select_bank,
    summary,
    text_handler,
    update_user_bank_details,
    ussd_lottery_play2,
    yes_option,
)
from wyse_ussd.helper.instant_cashout3 import (
    instant_cash_item,
    instant_cashout_amount,
    instant_cashout_validator,
    summary_amount_inscashout,
)
from wyse_ussd.helper.mega_cash3 import mega_cash_item, wyse_cash_summary_amount
from wyse_ussd.helper.redbiller_helper import RedbillerHelper
from wyse_ussd.helper.salary_for_life3 import (
    salary_for_life_amount,
    salary_for_life_item,
    salary_for_life_line_validator,
    summary_amount_salary_for_life,
)
from wyse_ussd.helper.soccer_cash import SoccerFixtures, soccer_cash_item
from wyse_ussd.models import (
    SoccerPrediction,
    UssdConstantVariable,
    create_wyse_lotto_ticket,
    create_wyse_ussd_user,
)
from wyse_ussd.tasks import (
    create_ussd_virtual_account,
    ussd_lottery_payment,
    ussd_payout,
)


@dataclass
class WhisperWyseUssd:
    @staticmethod
    def options(text, phone_number, session_id):
        if BlackListed().is_blacklisted(phone=phone_number):
            return "END You are not allowed to use this service"

        if BlackListed().can_play_instant_cashout(phone=phone_number) is False:
            return "END Instant cashout is not accessible for you at the moment"

        if BlackListed().can_play_salary_for_life(phone=phone_number) is False:
            return ("END Salary for life is not accessible for you at the moment",)

        restrictor = HopsRestrictor.is_restricted(phone_number)

        if restrictor.get("response"):
            if restrictor.get("reason") == "OPPS":
                return "END Please you are moving too fast\ntry again in 30 mins"

            elif restrictor.get("reason") == "PENDING_PAYMENT":
                return summary(phone_number)

        HopsRestrictor.increment_hops(phone_number)

        text = text_handler(text)
        # # print("text = text_handler(text)", text_handler(text))
        splited_text = text.split("*")
        splited_text = [i for i in splited_text if i != ""]

        # # print(
        #     "text",
        #     "-----",
        #     text,
        #     "TEXT LENGTH",
        #     "---------",
        #     len(text),
        #     "-------- Len of splitted text",
        #     len(splited_text),
        #     splited_text,
        # )

        if text == "":  # initial menu list
            response = "CON Welcome to WinWise:\nWin cash prizes up to 10m daily.\n\n"
            response += "1.Win Cash 4 life \n"
            response += "2.Win Quick Cash \n"
            response += "3.Win Mega Cash \n"
            response += "4.Awoof Game \n"
            response += "11.Next \n"

            # select and create first time user
            create_wyse_ussd_user(phone_number=phone_number)

        elif text == "11":
            response = "CON Welcome to WinWise:\nWin cash prizes up to 10m daily.\n\n"
            response += "5.Win Soccer Cash \n"
            response += "6.About \n"
            response += "7.My Account \n"
            response += "8.Payout \n"
            response += "9.result \n"
            response += "0.back \n"

        # -------------------- PHONE NUMBER WEB OTP/ VERIFICATION --------------------
        elif text.startswith("89*"):
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            return Otp.ussd_code_phone_mumber_activation(phone=phone, ussd_code=text)

        # SALARY FOR LIFE
        elif text == "1":
            response = salary_for_life_item[text]

        elif text == "1*1":  # Automatic Entry
            response = salary_for_life_item[text]

        elif text == "1*2":
            response = salary_for_life_item[text]
            return response

        elif text == "1*3":
            response = salary_for_life_item[text]
            return response

        elif text.startswith("1*") and ((len(text) == 5) or (len(text) == 6)):
            salary_for_life_line_validate = salary_for_life_line_validator(text)

            if salary_for_life_line_validate is not True:
                return salary_for_life_line_validate

            # SUMMARY
            jackpot = splited_text[2]  # get selected jackpot

            # print("jackpot", type(jackpot), jackpot)

            if not isinstance(salary_for_life_amount(jackpot), int):
                response = salary_for_life_amount(f"{jackpot}")
                return response

            # num_lines = lines(splited_text[-2])
            num_lines = splited_text[2]

            # # print("num_lines", num_lines, "\n\n\n")
            lotto = create_wyse_lotto_ticket(
                phone_number=phone_number,
                lottery_type="SALARY_FOR_LIFE",
                jackpot=jackpot,
                bank="",
                auto=True,
                stake_amount=salary_for_life_amount(jackpot),
            )

            # user profile
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            # get last played lottery
            lottery_instance = LottoTicket.objects.filter(user_profile=user_profile, lottery_type="SALARY_FOR_LIFE").last()

            sleep(1)

            stake_and_win_amount = summary_amount_salary_for_life.get(int(splited_text[2]))

            # ussd_lottery_payment(
            #     phone,
            #     stake_and_win_amount[0],
            #     # lotto.stake_amount * lotto.number_of_ticket,
            #     user_profile.bank_code,
            #     stake_and_win_amount[1],
            #     # lotto.potential_winning,
            #     "SALARY_FOR_LIFE",
            #     lotto.game_play_id,
            # )

            ussd_lottery_payment.apply_async(
                queue="celery1",
                args=[
                    phone,
                    stake_and_win_amount[0],
                    # lotto.stake_amount * lotto.number_of_ticket,
                    user_profile.bank_code,
                    stake_and_win_amount[1],
                    # lotto.potential_winning,
                    "SALARY_FOR_LIFE",
                    lotto.game_play_id,
                ],
            )

            response = salary_for_life_item["summary"].format(
                num_lines,
                Utility.currency_formatter(stake_and_win_amount[0]),
                # Utility.currency_formatter(lotto.stake_amount * int(num_lines)),
                lotto.ticket,
                Utility.currency_formatter(stake_and_win_amount[1]),
                # Utility.currency_formatter(lotto.potential_winning),
                # lotto.game_play_id
            )

        # ===========================================================================================

        elif text == "1*2":  # Number Pick
            return "END Invalid command"
            response = salary_for_life_item[text]

        elif text.startswith("1*2*") and (len(text) == 5):
            response = salary_for_life_item[text]

        elif text.startswith("1*2*") and (len(splited_text) == 4):  # user input
            response = salary_for_life_item["get_user_input"]

        elif text.startswith("1*2*") and len(splited_text) == 5 and len(splited_text[4].split()) == 5:  # select bank
            response = salary_for_life_item["select_bank"]
        elif text.startswith("1*2*") and len(splited_text) == 5 and len(splited_text[4].split()) < 5:
            response = """CON Please input at least any 
                5 numbers between 1 and 49 
                separated by space (eg: 1 12 9 44 18)\n0.back"""  # noqa

        elif text.startswith("1*2*") and len(splited_text) == 5 and len(splited_text[4].split()) > 5:
            response = """CON Please input at most any 
                5 numbers between 1 and 49
                separated by space (eg: 1 12 9 44 18)\n0.back"""  # noqa

        elif text.startswith("1*2*") and (len(splited_text) == 6 or not splited_text[-1].isdigit()):  # SUMMARY
            jackpot = splited_text[3]  # get selected jackpot
            # # print("splited_text for number pick", splited_text, "\n\n\n\n")
            bank = select_bank(splited_text[3])  # fetch bank

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            # create virtual account for user
            create_ussd_virtual_account.apply_async(queue="celery1", args=[phone])

            # if user select others bank
            if "Enter Bank Name" in bank:
                return bank

            if "END" in bank:
                return bank

            # update user bank
            update_user_bank_details(phone_number=phone_number, bank_name=bank)

            player_input = splited_text[4].split()
            user_input = ",".join(player_input)

            # check if user input is between 1 and 49
            if not all(1 <= int(i) <= 49 for i in player_input):
                response = "END Inavlid inpute\n"
                response += "Please input any 5 numbers between 1 and 49 "
                response += "separated by space (eg: 1 12 9 44 18)"

            # check len of user input
            elif len(player_input) < 5 or len(player_input) > 5:
                response = "END Inavlid inpute\n"
                response += "Please input any 5 numbers between 1 and 49 "
                response += "separated by space (eg: 1 12 9 44 18)"

            else:
                # # print(user_input)
                lotto = create_wyse_lotto_ticket(
                    phone_number=phone_number,
                    lottery_type="SALARY_FOR_LIFE",
                    jackpot=jackpot,
                    bank=bank,
                    player_input=str(user_input),
                    auto=False,
                )

                sleep(1)

                # user profile
                phone = LotteryModel.format_number_from_back_add_234(phone_number)
                user_profile = UserProfile.objects.filter(phone_number=phone).last()

                # create ussd collection details
                # create_collection_details_for_lottery_player.apply_async(
                #     queue="celery1",
                #     args=[
                #         phone,
                #         lotto.stake_amount * lotto.number_of_ticket,
                #         user_profile.bank_code,
                #         lotto.potential_winning,
                #         "SALARY_FOR_LIFE",
                #         lotto.game_play_id,
                #     ],
                # )
                stake_and_win_amount = summary_amount_salary_for_life.get(int(splited_text[3]))
                # ussd_lottery_payment.delay(
                #     phone,
                #     stake_and_win_amount[0],
                #     # lotto.stake_amount * lotto.number_of_ticket,
                #     user_profile.bank_code,
                #     stake_and_win_amount[1],
                #     # lotto.potential_winning,
                #     "SALARY_FOR_LIFE",
                #     lotto.game_play_id,
                # )
                ussd_lottery_payment.apply_async(
                    queue="celery1",
                    args=[
                        phone,
                        stake_and_win_amount[0],
                        # lotto.stake_amount * lotto.number_of_ticket,
                        user_profile.bank_code,
                        stake_and_win_amount[1],
                        # lotto.potential_winning,
                        "SALARY_FOR_LIFE",
                        lotto.game_play_id,
                    ],
                )

                num_lines = splited_text[3]
                response = salary_for_life_item["summary"].format(
                    num_lines,
                    Utility.currency_formatter(stake_and_win_amount[0]),
                    # Utility.currency_formatter(lotto.stake_amount * int(num_lines)),
                    lotto.ticket,
                    Utility.currency_formatter(stake_and_win_amount[1]),
                    # Utility.currency_formatter(lotto.potential_winning),
                    # lotto.game_play_id
                )

        elif text.startswith("1*2*") and len(splited_text) == 7:
            option = splited_text[-1]
            if option == "1":
                response = yes_option

                # user profile
                phone = LotteryModel.format_number_from_back_add_234(phone_number)
                user_profile = UserProfile.objects.filter(phone_number=phone).last()

                # get last played lottery
                lottery_instance = LottoTicket.objects.filter(user_profile=user_profile, lottery_type="SALARY_FOR_LIFE").last()

            elif option == "2":
                response = no_option

        # INSTANT CASH
        elif text == "2":
            response = instant_cash_item[text]

        elif text == "2*1":  # Automatic Entry
            response = instant_cash_item[text]
        elif text == "2*2":  # Automatic Entry
            response = instant_cash_item[text]

        elif text.startswith("2*") and ((len(text) == 5) or (len(text) == 6)):
            instant_cashout_validate = instant_cashout_validator(text)

            if instant_cashout_validate is not True:
                return instant_cashout_validate

            # SUMMARY
            jackpot = splited_text[2]  # get selected jackpot

            # # print("jackpot", type(jackpot), jackpot)

            if not isinstance(instant_cashout_amount(jackpot), int):
                response = instant_cashout_amount(f"{jackpot}")
                return response

            # num_lines = lines(splited_text[-2])
            num_lines = splited_text[2]
            # # print("num_lines", num_lines, "\n\n\n")
            lotto = create_wyse_lotto_ticket(
                phone_number=phone_number,
                lottery_type="INSTANT_CASHOUT",
                jackpot=jackpot,
                bank="",
                auto=True,
                stake_amount=instant_cashout_amount(jackpot),
            )

            # user profile
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            # get last played lottery
            lottery_instance = LottoTicket.objects.filter(user_profile=user_profile, lottery_type="INSTANT_CASHOUT").last()

            sleep(1)

            # # create ussd collection details
            # create_collection_details_for_lottery_player.delay(
            #     phone,
            #     lottery_instance.stake_amount * lottery_instance.number_of_ticket,
            #     user_profile.bank_code,
            #     lottery_instance.potential_winning,
            #     "SALARY_FOR_LIFE",
            #     lottery_instance.game_play_id,
            # )

            # create_collection_details_for_lottery_player.apply_async(
            #     queue="celery1",
            #     args=[
            #         phone,
            #         lotto.stake_amount * lotto.number_of_ticket,
            #         user_profile.bank_code,
            #         lotto.potential_winning,
            #         "INSTANT_CASHOUT",
            #         lotto.game_play_id,
            #     ],
            # )
            stake_and_win_amount = summary_amount_inscashout.get(int(splited_text[2]))
            ussd_lottery_payment(
                phone,
                stake_and_win_amount[0],
                # lotto.stake_amount * lotto.number_of_ticket,
                user_profile.bank_code,
                stake_and_win_amount[1],
                # lotto.potential_winning,
                "INSTANT_CASHOUT",
                lotto.game_play_id,
            )
            # ussd_lottery_payment.apply_async(
            #     queue="celery1",
            #     args=[
            #         phone,
            #         stake_and_win_amount[0],
            #         # lotto.stake_amount * lotto.number_of_ticket,
            #         user_profile.bank_code,
            #         stake_and_win_amount[1],
            #         # lotto.potential_winning,
            #         "INSTANT_CASHOUT",
            #         lotto.game_play_id,
            #     ],
            # )

            response = instant_cash_item["summary"].format(
                num_lines,
                Utility.currency_formatter(stake_and_win_amount[0]),
                # Utility.currency_formatter(lotto.stake_amount * int(num_lines)),
                lotto.ticket,
                Utility.currency_formatter(stake_and_win_amount[1]),
                # lotto.game_play_id
            )

        # ===========================================================================================

        #
        # MEGA CASH
        elif text == "3":
            response = mega_cash_item[text]
        elif text == "3*1":
            response = mega_cash_item[text]
        elif text == "3*2":
            response = mega_cash_item[text]
        elif text == "3*3":
            response = mega_cash_item[text]
        elif text == "3*4":
            response = mega_cash_item[text]

        elif text.startswith("3*") and (len(text) == 5):
            # create lottery ticket
            ussd_lottery_play2(phone_number=phone_number, lottery_type="wyse_cash", text=text)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            sleep(1)

            # create virtual account for user
            create_ussd_virtual_account.apply_async(queue="celery1", args=[phone])

            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            # get last played lottery
            lottery_instance = LotteryModel.objects.filter(user_profile=user_profile).last()

            # create ussd collection details
            # create_collection_details_for_lottery_player.apply_async(
            #     queue="celery1",
            #     args=[
            #         phone,
            #         lottery_instance.stake_amount * lottery_instance.instance_number,
            #         user_profile.bank_code,
            #         int(lottery_instance.band) * lottery_instance.instance_number,
            #         "WYSE_CASH",
            #         lottery_instance.game_play_id,
            #     ],
            #

            # create ussd collection details
            # create_collection_details_for_lottery_player.delay(
            #     phone,
            #     lottery_instance.stake_amount * lottery_instance.instance_number,
            #     user_profile.bank_code,
            #     int(lottery_instance.band) * lottery_instance.instance_number,
            #     "WYSE_CASH",
            #     lottery_instance.game_play_id,
            # )

            stake_and_win_amount = wyse_cash_summary_amount.get(text)
            # ussd_lottery_payment(
            #     phone,
            #     # lottery_instance.stake_amount * lottery_instance.instance_number,
            #     stake_and_win_amount[0],
            #     user_profile.bank_code,
            #     stake_and_win_amount[1],
            #     # int(lottery_instance.band) * lottery_instance.instance_number,
            #     "WYSE_CASH",
            #     lottery_instance.game_play_id,
            # )
            ussd_lottery_payment.apply_async(
                queue="celery1",
                args=[
                    phone,
                    # lottery_instance.stake_amount * lottery_instance.instance_number,
                    stake_and_win_amount[0],
                    user_profile.bank_code,
                    stake_and_win_amount[1],
                    # int(lottery_instane.band) * lottery_instance.instance_number,
                    "WYSE_CASH",
                    lottery_instance.game_play_id,
                ],
            )

            # user profile
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            lottery_instance = LotteryModel.objects.filter(user_profile=user_profile).last()

            response = mega_cash_item["summary"].format(
                lottery_instance.instance_number,
                Utility.currency_formatter(stake_and_win_amount[0]),
                # Utility.currency_formatter(
                #     lottery_instance.stake_amount * lottery_instance.instance_number
                # ),
                Utility.currency_formatter(stake_and_win_amount[1]),
                # Utility.currency_formatter(
                #     int(lottery_instance.band) * lottery_instance.instance_number
                # ),
            )

            return response

        # Lotto Guarantor

        elif text.startswith("33*") and len(splited_text) == 2:
            formatted_phone = LotteryModel.format_number_from_back_add_234(phone=phone_number)
            if LottoAgentGuarantorDetail.objects.filter(guarantor_phone=formatted_phone, verified=True).exists():
                return "END This Guarantor has been verified already"

            response = "CON Select preferred means of identification:"
            # response += "\n1. NIN."
            # response += "\n2. BVN."
            # response += "\n3. Voters Card."
            # response += "\n4. INTL Passport \n"
            response += "\n1. BVN."
            response += "\n2. Voters Card."
            response += "\n3. INTL Passport \n"
            return response

        elif text.startswith("33*") and len(splited_text) == 3:
            if splited_text[-1] == "1":
                response = "CON Please enter your BVN number: \n"
                return response
            elif splited_text[-1] == "2":
                response = "CON Please enter your Voters Card number: \n"
                return response
            elif splited_text[-1] == "3":
                response = "CON Please enter your INTL Passport number: \n"
                return response
            return "END Invalid input"

            # formatted_phone = LotteryModel.format_number_from_back_add_234(phone=phone_number)

            # if len(splited_text) == 10:
            #     nin = splited_text
            #     response = "END Thank you for your input. Your NIN is being verified"
            #     validate_nin_task = verify_guarantor_nin.delay(nin=nin, guarantor_phone=formatted_phone)
            # else:
            #     response = "CON Please enter a valid NIN\n"

            # return response

        elif text.startswith("33*") and len(splited_text) == 4:
            # Guarantor's unique code is splited_text[1].
            guarantor_unique_code = splited_text[1]

            # if splited_text[2] == "1":
            #     guarantor_instance = LottoAgentGuarantorDetail.objects.filter(
            #         unique_code=guarantor_unique_code
            #     ).first()
            #     # NIN VERIFICATION
            #     nin_number = splited_text[3]
            #     if nin_number == "":
            #         return "END Please enter a valid NIN"

            #     redbiller_helper = RedbillerHelper()

            #     payload = {
            #         "first_name": guarantor_instance.get_guarantor_first_name,
            #         "surname": guarantor_instance.get_guarantor_last_name,
            #         "phone_number": guarantor_instance.guarantor_phone,
            #         "nin": nin_number,
            #     }
            #     if (
            #         redbiller_helper.nin_verification(
            #             unique_ussd_code=guarantor_unique_code, **payload
            #         )
            #         is True
            #     ):
            #         return "END Your NIN has been verified, thank you."
            #     return "END Verification failed, please try again"

            if splited_text[2] == "1":
                # BVN VERIFICATION
                bvn_number = splited_text[3]
                if bvn_number == "":
                    return "END Please enter a valid BVN"

                redbiller_helper = RedbillerHelper()

                payload = {"bvn": bvn_number}
                if redbiller_helper.bvn_verification(unique_ussd_code=guarantor_unique_code, bvn_id=bvn_number) is True:
                    return "END Your BVN has been verified, thank you."
                return "END Verification failed, please try again"

            elif splited_text[2] == "2":
                # VOTERS CARD VERIFICATION
                guarantor_instance = LottoAgentGuarantorDetail.objects.filter(unique_code=guarantor_unique_code).first()
                voter_number = splited_text[3]
                if voter_number == "":
                    return "END Please enter a valid Voters' card"
                redbiller_helper = RedbillerHelper()

                payload = {
                    "first_name": guarantor_instance.get_guarantor_first_name,
                    "surname": guarantor_instance.get_guarantor_last_name,
                    "vin": voter_number,
                }
                if redbiller_helper.voters_card_verification(unique_ussd_code=guarantor_unique_code, **payload) is True:
                    return "END Your voter's card has been verified, thank you."
                return "END Verification failed, please try again"

            elif splited_text[2] == "3":
                # INTERNATIONAL PASSPORT VERIFICATION
                guarantor_instance = LottoAgentGuarantorDetail.objects.filter(unique_code=guarantor_unique_code).first()
                passport_number = splited_text[3]
                if passport_number == "":
                    return "END Please enter a valid INTL Passport"
                redbiller_helper = RedbillerHelper()

                payload = {
                    "first_name": guarantor_instance.get_guarantor_first_name,
                    "surname": guarantor_instance.get_guarantor_last_name,
                    "passport_no": passport_number,
                    "phone_no": guarantor_instance.guarantor_phone,
                }
                if redbiller_helper.international_passport_verification(unique_ussd_code=guarantor_unique_code, **payload) is True:
                    return "END Your passport has been verified, thank you."
                return "END Verification failed, please try again"

        # Soccer Cash

        elif text == "5":
            response = SoccerFixtures.welcome_screen()

        elif text.startswith("5*") and (len(splited_text) == 2):
            if isinstance(SoccerFixtures.select_league(splited_text[1]), dict):
                display_message = SoccerFixtures.select_league(splited_text[1]).get("display_msg")
            else:
                display_message = SoccerFixtures.select_league(splited_text[1])
            response = display_message

        elif text.startswith("5*") and (len(splited_text) == 3):  # score entery
            response = SoccerFixtures.score_entery(splited_text[1], splited_text[2])

        elif text.startswith("5*") and (len(splited_text) == 4):  # confirm prediction
            prediction = "-".join(splited_text[3].split(" "))
            if len(prediction) >= 3:
                response = SoccerFixtures.confirm_entery_band_options(splited_text[1], splited_text[2], prediction)
            else:
                response = "END Please enter valid scores with a space e.g (4 3)"

        elif text.startswith("5") and (len(splited_text) == 5):
            event_properties = SoccerFixtures.fixtures_properties(splited_text[1], splited_text[2])
            soccer_prediction_ins = SoccerPrediction.create_leauge_prediction_object(
                desired_play=splited_text[4],
                prediction=splited_text[3].split(" "),
                phone_no=phone_number,
                fixtures_id=event_properties[-1],
                # bank=bank,
            )

            if soccer_prediction_ins is None:
                response = "END Invalid prediction"
                return response

            sleep(2)

            phone_no = LotteryModel.format_number_from_back_add_234(phone_number)

            prediction_instance = SoccerPrediction.objects.filter(phone=phone_no).last()

            # # print(prediction_instance, ":::::::::::::::::::::")

            # create_collection_details_for_lottery_player.delay(
            #     prediction_instance.phone,
            #     prediction_instance.stake_amount,
            #     prediction_instance.bank_code,
            #     prediction_instance.potential_winning,
            #     "SOCCER_CASH",
            #     prediction_instance.game_id
            # )

            # create_collection_details_for_lottery_player.apply_async(
            #     queue="celery1",
            #     args=[
            #         prediction_instance.phone,
            #         prediction_instance.stake_amount,
            #         prediction_instance.bank_code,
            #         prediction_instance.potential_winning,
            #         "SOCCER_CASH",
            #         prediction_instance.game_id,
            #     ],
            # )
            # ussd_lottery_payment.delay(
            #     prediction_instance.phone,
            #     prediction_instance.stake_amount,
            #     prediction_instance.bank_code,
            #     prediction_instance.potential_winning,
            #     "SOCCER_CASH",
            #     prediction_instance.game_id,
            # )
            ussd_lottery_payment.apply_async(
                queue="celery1",
                args=[
                    prediction_instance.phone,
                    prediction_instance.stake_amount,
                    prediction_instance.bank_code,
                    prediction_instance.potential_winning,
                    "SOCCER_CASH",
                    prediction_instance.game_id,
                ],
            )

            response = soccer_cash_item["summary"].format(
                event_properties[0],
                event_properties[1],
                "-".join(splited_text[3].split(" ")),
                soccer_prediction_ins.stake_amount,
                soccer_prediction_ins.potential_winning,
            )

        #
        # account
        elif text == "7":
            response = "CON Select an option:\n"
            response += "1. Check Balance\n"
            response += "2. Deposit\n"
            response += "3. Transaction History\n"

        elif text == "7*1":
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            response = f"END Your play account balance is {Utility.currency_formatter(user_wallet.game_available_balance)}"
            response += f"\nYour withdrawable balance is {Utility.currency_formatter(user_wallet.withdrawable_available_balance)}"

        elif text == "7*2":
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()
            if user_profile is None:
                user_profile = UserProfile.objects.create(phone_number=phone)

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                response = "END You don't have a deposit account number yet, check back in 10 minutes"
                return response

            if user_wallet.woven_account is None:
                response = "END You don't have a deposit account number yet, check back in 10 minutes"
                return response

            response = "END Please deposit into this account details below:\n"
            response += f"Account Name: {user_wallet.woven_account.acct_name}\n"
            response += f"Account Number: {user_wallet.woven_account.vnuban}\n"
            response += f"Bank Name: {user_wallet.woven_account.bank_name}\n"

        elif text == "7*3":
            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            # user game play transactions

            # user payout transactions

            # user game won transactions

            user_game_play_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="VIRTUAL_SOCCER_GAME_PLAY")
                    | Q(transaction_from="SOCCER_CASH_GAME_PLAY")
                    | Q(transaction_from="SAL_4_LIFE_GAME_PLAY")
                    | Q(transaction_from="INSTANT_CASHOUT_GAME_PLAY")
                    | Q(transaction_from="WYSE_CASH_GAME_PLAY")
                    | Q(transaction_from="VIRTUAL_SOCCER_GAME_PLAY")
                    | Q(transaction_from="BANKER_GAME_PLAY")
                    | Q(transaction_from="QUIKA_GAME_PLAY")
                    | Q(transaction_from="AWOOF_GAME_PLAY"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_game_play_transactions:
                last_game_play_transaction_amount = user_game_play_transactions.amount
            else:
                last_game_play_transaction_amount = 0

            user_payout_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="WITHDRAWAL"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_payout_transactions:
                last_payout_transaction_amount = user_payout_transactions.amount
            else:
                last_payout_transaction_amount = 0

            user_game_won_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="VIRTUAL_SOCCER_GAME_PLAY")
                    | Q(transaction_from="SOCCER_CASH_GAME_WIN")
                    | Q(transaction_from="SAL_4_LIFE_GAME_WIN")
                    | Q(transaction_from="INSTANT_CASHOUT_GAME_WIN")
                    | Q(transaction_from="WYSE_CASH_GAME_WIN")
                    | Q(transaction_from="VIRTUAL_SOCCER_GAME_WON")
                    | Q(transaction_from="BANKER_GAME_WON")
                    | Q(transaction_from="QUIKA_GAME_WON")
                    | Q(transaction_from="AWOOF_GAME_WON"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_game_won_transactions:
                last_game_won_transaction_amount = user_game_won_transactions.amount
            else:
                last_game_won_transaction_amount = 0

            user_funding_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="PAYSTACK_FUNDING")
                    | Q(transaction_from="PABBLY_PAYSTACK")
                    | Q(transaction_from="WOVEN_FUNDING")
                    | Q(transaction_from="FUNDING")
                    | Q(transaction_from="FUNDING_FROM_WITHDRAWABLE_WALLET")
                    | Q(transaction_from="REDBILLER_FUNDING")
                    | Q(transaction_from="WATU_PAY"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_funding_transactions:
                last_funding_transaction_amount = user_funding_transactions.amount
            else:
                last_funding_transaction_amount = 0

            user_reversal_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="REVERSAL"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_reversal_transactions:
                last_reversal_transaction_amount = user_reversal_transactions.amount
            else:
                last_reversal_transaction_amount = 0

            response = f"END Last game play amount: {Utility.currency_formatter(last_game_play_transaction_amount)}\n"
            response += f"Last payout amount: {Utility.currency_formatter(last_payout_transaction_amount)}\n"
            response += f"Last game won amount: {Utility.currency_formatter(last_game_won_transaction_amount)}\n"
            response += f"Last reversal amount: {Utility.currency_formatter(last_reversal_transaction_amount)}\n"
            response += f"Last funding amount: {Utility.currency_formatter(last_funding_transaction_amount)}\n"

            return response

        elif text == "8":
            if BlackListed().can_withdraw(phone=phone_number):
                return "END Sorry you are not allowed to withdraw at the moment"

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                return "END Sorry, payout is not available at the moment"

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            if user_wallet.withdrawable_available_balance == 0:
                response = "END You have no available balance to withdraw"
                return response

            response = f"CON Withdrawal account balance is {Utility.currency_formatter(user_wallet.withdrawable_available_balance)}.\nEnter amount to withdraw:\n"

        elif text.startswith("8*") and len(splited_text) == 2:
            if BlackListed().can_withdraw(phone=phone_number):
                return "END Sorry you are not allowed to withdraw at the moment"

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                return "END Sorry, payout is not available at the moment"

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            if user_wallet.withdrawable_available_balance == 0:
                response = "END You have no available balance to withdraw"
                return response

            amount_entered = splited_text[1]

            if not amount_entered.isdigit():
                response = "END Invalid amount entered"
                return response

            if int(amount_entered) > user_wallet.withdrawable_available_balance:
                response = "END Insufficient balance"
                return response

            if float(amount_entered) < 100:
                response = "END Minimum amount to withdraw is 100"
                return response

            # check if we've enough money for payout
            if has_enough_money_to_giveout(phone, amount_entered) is False:
                return "END We're sorry, an error occured while processing your request. Please try again later"

            response = select_bank()
            return response

        elif text.startswith("8") and len(splited_text) == 3:
            if BlackListed().can_withdraw(phone=phone_number):
                return "END Sorry you are not allowed to withdraw at the moment"

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                return "END Sorry, payout is not available at the moment"

            response = "CON Enter Account Number"

        elif text.startswith("8") and len(splited_text) == 4:
            if BlackListed().can_withdraw(phone=phone_number):
                return "END Sorry you are not allowed to withdraw at the moment"

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                return "END Sorry, payout is not available at the moment"

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            if user_wallet.withdrawable_available_balance < 100:
                response = "END You do not have enough balance to withdraw"
                return response

            account_number = splited_text[3]
            # account = RedBiller.get_account_info(
            #     account_no=account_number, bank_code="000013"
            # )

            bank = select_bank(splited_text[2])
            bank_db = BankManger().bank_details(bank_name=bank)

            if not bank_db:
                response = "END Invalid bank"
                return response

            bank_code = bank_db.get("cbn_code")

            # paystack account verification
            bank_details = fetch_account_name(account_number, bank_code)

            if bank_details.get("status") is not True:
                response = "END Invalid account details"
                return response

            # # print("paystack account verification", bank_details)

            user_profile.account_name = bank_details.get("data").get("account_name")
            user_profile.account_num = account_number
            user_profile.account_name = bank_details.get("data").get("account_name")
            user_profile.save()

            response = f"""CON confirm:
            Bank: {bank_db.get("name")}
            A/No: {account_number}
            Name: {bank_details.get("data").get("account_name")}
            1.Confirm
            2.Exit
            """

            return response

        elif text.startswith("8") and len(splited_text) == 5:
            # return "END Sorry, payout is not available at the moment"

            if BlackListed().can_withdraw(phone=phone_number):
                return "END Sorry you are not allowed to withdraw at the moment"

            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            if has_enough_money_to_giveout(phone, float(splited_text[1])) is False:
                return "END We're sorry, an error occured while processing your request. Please try again later"

            # if ConstantVariable.get_constant_variable().get("payout_source") == "BUDDY":
            #     return "END Sorry, payout is not available at the moment"

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                return "END Sorry, payout is not available at the moment"

            # # print("entered disbursment", "\n\n\n\n")

            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            bank = select_bank(splited_text[2])
            bank_db = BankManger().bank_details(bank_name=bank)

            account_number = splited_text[3]

            if splited_text[-1] == "1":
                # ussd_payout.apply_async(
                #     queue="celery3",
                #     args=[user_profile.id, splited_text[1], bank],
                # )
                ussd_payout.delay(user_profile.id, splited_text[1], bank, account_number)

                response = "END Your withdrawal request has been received"

            elif splited_text[-1] == "2":
                response = "END Withdrawal request cancelled"

            # recent result

        elif text.startswith("8") and len(splited_text) > 5:
            return "END Invalid input"

        elif text == "9":
            response = "CON Enter your game play id to check result\n"

        elif text.startswith("9*") and len(splited_text) == 2:
            game_play_id = splited_text[-1]
            return lottery_ticket_result(game_play_id)

        # -------------------------------------------- RETAIL SERVICES --------------------------------------------

        elif (len(splited_text[0]) == 6) and (len(splited_text) == 2):
            pin = splited_text[1]
            ussd_code = f"*347*800*{splited_text[0]}#"
            phone_no = LotteryModel.format_number_from_back_add_234(phone_number)

            response = register_and_update_ticket_to_table(pin, phone_no, ussd_code)

        elif len(splited_text[0]) == 6:
            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            ussd_code = f"*347*800*{splited_text[0]}#"
            res = BoughtLotteryTickets().register_bonus_lottery(phone=phone, ussd_code=ussd_code)
            response = f'{res.get("message")}'

        # -------------------------------------------- GET WINNING DETAILS --------------------------------------------

        elif len(splited_text) == 1 and splited_text[0] == "77":
            response = PosLotteryWinners.get_winning_details(player_num=phone_number)

        # -------------------------------------------- AWOOF USSD --------------------------------------------
        elif text == "4":
            awoof_items = LifeStyleTable().awoof_ussd_iteams(session_id)
            if awoof_items is None:
                response = "END No awoof item available at the moment"
            else:
                response = "CON Select your choice item: \n"
                response += awoof_items

        elif text.startswith("4*") and len(splited_text) == 2:
            last_item = splited_text[-1]
            try:
                last_item = int(last_item)
            except Exception:
                response = "END Invalid selection"
                return response

            awoof_price_items = LifeStyleTable().awoof_ussd_item_price(session_id=session_id, user_number_selected=last_item)

            # # print(
            #     f"""
            # awoof_price_items: {awoof_price_items}
            # """
            # )

            if awoof_price_items is None:
                response = "END Invalid selection"
            else:
                response = awoof_price_items

        elif text.startswith("4*") and len(splited_text) == 3:
            """summary screen"""
            last_item = splited_text[-1]

            price_data_in_redis = IllusionDatabase(redis_key=f"{session_id}_price").get_data()

            if price_data_in_redis is None:
                response = "END Service Error, please start again"
                return response

            ticket_price = price_data_in_redis.get(last_item)

            if ticket_price is None:
                response = "END Invalid selection"
                return response

            """splited_text[1] is the item id,
            the iteam user selected as their second option after selecting 
            awoof game
            """  # noqa

            item_db_id = RedisStorage(redis_key=f"{session_id}_{splited_text[1]}").get_data()

            if item_db_id is None:
                response = "END Service Error, please start again"
                return response

            item_db_id = int(item_db_id)

            item = LifeStyleTable.objects.get(id=item_db_id)

            response = "CON Summary: Fast Fingers\n"
            response += f"Potential Win: {item.item_name}\n"
            response += f"No. of ticket: {last_item}\n"
            response += f"Amt: {Utility.currency_formatter(int(str(ticket_price).replace('.0','')))}\n"
            response += "1. Yes\n"
            response += "2. No\n"

            return response

        elif text.startswith("4*") and len(splited_text) == 4:
            last_item = splited_text[-1]

            # # print(
            #     "UssdConstantVariable().is_use_telco_active()",
            #     UssdConstantVariable().is_use_telco_active(),
            # )

            if last_item == "1":
                if UssdConstantVariable().is_use_telco_active() is True:
                    response = "END Please wait while we connect to your network provider."
                    return response

                else:
                    item_id_key_in_incoming_text = splited_text[-3]
                    _item_id_stored_in_redis = RedisStorage(redis_key=f"{session_id}_{item_id_key_in_incoming_text}")

                    if _item_id_stored_in_redis is None:
                        return "END Something went wrong, please try again again."

                    item_id_num = (_item_id_stored_in_redis.get_data()).decode("utf-8")

                    try:
                        item_id_num = int(item_id_num)
                    except Exception:
                        return "END Something went wrong, please try again again."

                    price_data_in_redis = IllusionDatabase(redis_key=f"{session_id}_price").get_data()

                    user_selected_chances = splited_text[-2]

                    ticket_price = price_data_in_redis.get(user_selected_chances)

                    if ticket_price is None:
                        response = "END Something went wrong, please try again again."
                        return response

                    # # print(
                    #     f"""
                    #     ticket_price: {int(str(ticket_price).replace(".0",""))}
                    #     item_id_num: {item_id_num}
                    # """
                    # )

                    stake_amount = int(str(ticket_price).replace(".0", ""))

                    item = LifeStyleTable.objects.get(id=int(item_id_num))

                    awoof_game_instance = AwoofGameTable().register_ussd_awoof_game(
                        awoof_item_db_id=int(item_id_num),
                        user_selected_chances=int(str(ticket_price).replace(".0", "")),
                        user_phone_number=phone_number,
                        band_number=int(user_selected_chances),
                    )

                    if awoof_game_instance is None:
                        response = "END Something went wrong, please try again again."

                        return response

                    phone = LotteryModel.format_number_from_back_add_234(phone_number)
                    user_profile = UserProfile.objects.filter(phone_number=phone).last()

                    # ussd_lottery_payment(
                    #     phone,
                    #     stake_amount,
                    #     user_profile.bank_code,
                    #     item.item_name,
                    #     "AWOOF",
                    #     awoof_game_instance.game_play_id,
                    # )

                    ussd_lottery_payment.apply_async(
                        queue="celery1",
                        args=[
                            phone,
                            stake_amount,
                            user_profile.bank_code,
                            item.item_name,
                            "AWOOF",
                            awoof_game_instance.game_play_id,
                        ],
                    )

                    response = "END Summary: Fast Fingers\n"
                    response += f"Potential Win: {item.item_name}\n"
                    response += f"No. of ticket: {last_item}\n"
                    response += f"Amt: {Utility.currency_formatter(int(str(ticket_price).replace('.0','')))}\n"

                    return response

            elif last_item == "2":
                response = "END Thank you for using our service. You can access our web app at www.wisewinn.com"
                return response

            else:
                response = "END Invalid selection"
                return response

        else:
            response = "END Invalid selection"

        return response
