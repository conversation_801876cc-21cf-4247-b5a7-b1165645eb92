from django.core.management.base import BaseCommand

from broad_base_communication.bbc_helper import BBCTelcoAggregator
from wyse_ussd.models import PendingAsyncTask
from wyse_ussd.tasks import celery_handle_send_sms_to_players_that_played_via_telco_but_didnt_win, new_celery_teclo_airtime_charge_datasync_handler, new_task_to_process_telco_sync, send_sms_to_players_that_played_via_telco_but_didnt_win

# from main.tasks import engage_user


def deactivate_phone_number_from_all_telco_subscription_fakes(phone_number):
    stop_subscription_service_codes = [
        "23410220000027462",
        "23410220000027463",
        "23410220000027470",
        "23410220000027471",
        "23410220000027464",
        "23410220000027465",
        "23410220000027466",
        "23410220000027467",
        "23410220000027468",
        "23410220000027469",
    ]

    for product_id in stop_subscription_service_codes:
        BBCTelcoAggregator().telco_airtime_unsubscription_request(product_id=product_id, phone=phone_number)


def deactivate_glo_phone_number_from_all_telco_subscription_fakes(phone_number):
    stop_subscription_service_codes = [
        "1000005218",
        "1000005251",
        "1000005386",
        "1000005388",
        "1000005392",
        "1000005394",
        "1000005397",
        "1000005399",
        "1000005402",
        "1000005404",
    ]

    for product_id in stop_subscription_service_codes:
        BBCTelcoAggregator().telco_airtime_unsubscription_request(product_id=product_id, phone=phone_number)


class Command(BaseCommand):
    help = "Test run script maually"

    def handle(self, *args, **kwargs):
        pending_async_data_instance = PendingAsyncTask.objects.get(id=128106240)
        celery_handle_send_sms_to_players_that_played_via_telco_but_didnt_win(batch_id = pending_async_data_instance.batch_id, lottery_type = pending_async_data_instance.game_type)
