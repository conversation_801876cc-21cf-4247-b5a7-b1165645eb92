from django.core.management.base import BaseCommand

from wyse_ussd.models import TelcoSubscriptionPlan, TelcoUnsubscriptionRequest


class Command(BaseCommand):
    help = "This command is used to treat unsubscription request"

    def handle(self, *args, **kwargs):
        queryset = TelcoUnsubscriptionRequest.objects.filter(is_treated=False)
        for obj in queryset:
            TelcoSubscriptionPlan.deactivate_subscription(
                phone_number=obj.phone_number,
                service_id=obj.service_id,
                network=obj.network,
            )
            obj.treated = True
            obj.save()

            self.stdout.write(self.style.SUCCESS(f"Unsubscription request treated successfully for {obj.phone_number}"))
