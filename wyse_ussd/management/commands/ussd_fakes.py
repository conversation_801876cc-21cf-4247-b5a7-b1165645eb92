import random

from main.api.api_lottery_helpers import generate_game_play_id
from main.models import UserProfile
from sport_app.models import FootballTable
from wyse_ussd.models import SoccerPrediction


def fake_prediction():
    users = [user for user in UserProfile.objects.all()]
    [football_table for football_table in FootballTable.objects.all()]
    band_choices = [200, 500, 1000, 2000]
    random.randint(1, 3)

    for _ in range(0, 1000):
        user = random.choice(users)
        football_table = FootballTable.objects.get(id=89)
        band_choice = random.choice(band_choices)
        game_id = generate_game_play_id()

        print("BAND CHOICE:::::::", band_choice)

        if band_choice == 200:
            potential_winning = 1500
        elif band_choice == 500:
            potential_winning = 5000
        elif band_choice == 1000:
            potential_winning = 12000
        elif band_choice == 2000:
            potential_winning = 20000

        SoccerPrediction.objects.create(
            user_profile=user,
            football_table=football_table,
            phone=user.phone_number,
            game_id=game_id,
            home_choice=random.randint(0, 2),
            away_choice=random.randint(0, 2),
            band_played=band_choice,
            stake_amount=band_choice,
            paid=True,
            potential_winning=potential_winning,
            game_fixture_id="946818",
        )
    return "FAKE PREDICTION DATA CREATED SUCCESSFULLY"


def fake_soccer_prediction():
    users = [user for user in UserProfile.objects.all()]
    # football_tables = [football_table for football_table in FootballTable.objects.filter(fixture_date__gte=timezone.now())]
    band_choices = [200, 500, 1000, 2000]
    # choices = random.randint(1, 3)

    for _ in range(0, 500):
        user = random.choice(users)
        football_table = FootballTable.objects.get(id=89)
        # football_table = random.choice(football_tables)
        band_choice = random.choice(band_choices)
        game_id = generate_game_play_id()

        print("BAND CHOICE:::::::", band_choice)

        if band_choice == 200:
            potential_winning = 1500
        elif band_choice == 500:
            potential_winning = 5000
        elif band_choice == 1000:
            potential_winning = 12000
        elif band_choice == 2000:
            potential_winning = 20000

        SoccerPrediction.objects.create(
            user_profile=user,
            football_table=football_table,
            phone=user.phone_number,
            game_id=game_id,
            home_choice=random.randint(0, 5),
            away_choice=random.randint(0, 5),
            band_played=band_choice,
            stake_amount=band_choice,
            potential_winning=potential_winning,
            paid=True,
            game_fixture_id=football_table.fixture_id,
        )
    return "FAKE PREDICTION DATA CREATED SUCCESSFULLY"
