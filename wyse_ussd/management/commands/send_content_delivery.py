from django.core.management.base import BaseCommand
from typing import List
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import date


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        from main.models import LottoTicket
        from wyse_ussd.subscription_game_plays import (
            send_telco_subscription_game_play_confirmation,
        )

        pending_content_delivery_sms = LottoTicket.objects.filter(content_delivery_sms_sent=False, played_via_telco_channel=True, date__date=date.today())

        max_workers = 100
        batch_size = 10

        def process_sms_batch(tasks: List[LottoTicket]):
            for task in tasks:
                try:
                    send_telco_subscription_game_play_confirmation(
                        task.phone,
                        task.game_play_id,
                        task.game_type,
                        is_a_new_subscription=True,
                        network="MTN",
                        product_id=task.product_id,
                        ticket=task.ticket,
                    )

                except Exception:
                    pass
                    # print(f"Error sending SMS to {task.phone_number}: {str(e)}")

                task.content_delivery_sms_sent = True
                task.save()

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            for i in range(0, len(pending_content_delivery_sms), batch_size):
                batch = pending_content_delivery_sms[i : i + batch_size]
                executor.submit(process_sms_batch, batch)