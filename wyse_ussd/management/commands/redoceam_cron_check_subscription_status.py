from django.core.management.base import BaseCommand

from wyse_ussd.helper.redocean_africa import RedoceanAfricaGatewayHelper
from wyse_ussd.models import RedoceanAfricaDailySubscription, RedoceanAfricaRequestLogs, RedoceanAfricaSubscriber
from datetime import datetime
from dateutil import parser

import pytz
from django.conf import settings


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
        qs = RedoceanAfricaRequestLogs.objects.filter(type_of_request = "SUBSCRIPTION_REQUEST", status = "PENDING", created_at__date = TODAY.date()).filter()

        for imstance in qs:

            redocean_gateway_instance = RedoceanAfricaGatewayHelper().check_the_subscription_status_of_a_phone_number(
                phone_number = imstance.phone_number
            )

            is_subscribed = redocean_gateway_instance.get("data", {}).get("is_subscribed", False)
            expiry_date = redocean_gateway_instance.get("data", {}).get("expiry_date", None)

            if expiry_date is None:
                imstance.status = "FAILED"
                imstance.save()
                continue


            dt = parser.isoparse(expiry_date)

            if dt.date() < TODAY.date():
                imstance.status = "FAILED"
                imstance.save()

                subcriber_instance, _ = RedoceanAfricaSubscriber.objects.get_or_create(phone_number=imstance.phone_number)
            
            else:
                subcriber_instance, _ = RedoceanAfricaSubscriber.objects.get_or_create(phone_number=imstance.phone_number)
                subcriber_instance.is_subscribed = True
                subcriber_instance.save()


                # register the daily subscription logs
                daily_subscription_record = RedoceanAfricaDailySubscription.objects.filter(phone_number = subcriber_instance.phone_number).latest("created_at")
                if daily_subscription_record:
                    RedoceanAfricaDailySubscription.objects.create(
                        phone_number = subcriber_instance.phone_number,
                        subscription_status = "ACTIVE",
                        subscription_type = "RENEWAL",
                        expiry_date = dt
                    )
                else:
                    RedoceanAfricaDailySubscription.objects.create(
                        phone_number = subcriber_instance.phone_number,
                        subscription_status = "ACTIVE",
                        subscription_type = "ONE_TIME",
                        expiry_date = dt
                    )





