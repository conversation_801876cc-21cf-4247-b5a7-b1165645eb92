import json
import re

from django.conf import settings
from django.db.models import Q

from account.models import BlackListed
from awoof_app.models import AwoofGameTable, LifeStyleTable
from broad_base_communication.bbc_helper import BBCTelcoAggregator, TelcoRedisStorage
from broad_base_communication.helpers import xml_data_formater
from main.helpers.helper_functions import fetch_account_name
from main.helpers.hops_restriction_helper import HopsRestrictor
from main.helpers.illution_feature import IllusionDatabase
from main.helpers.redis_storage import RedisStorage
from main.models import ConstantVariable, LotteryModel, UserProfile
from main.ussd.helpers import Utility
from prices.game_price import (
    TelcoAwoofPriceModel,
    TelcoInstantCashOutPriceModel,
    TelcoSalaryForLifePriceModel,
    TelcoWyseCashPriceModel,
)
from wallet_app.models import UserWallet, WalletTransaction
from wyse_ussd.helper.bank import BankManger
from wyse_ussd.helper.general_helper import (
    has_enough_money_to_giveout,
    lottery_ticket_result,
    select_bank,
    select_bank_telco,
    text_handler,
    ussd_lottery_play2,
)
from wyse_ussd.helper.soccer_cash import SoccerFixtures
from wyse_ussd.helper.telco_instant_cashout3 import (
    instant_cash_item,
    instant_cashout_amount,
    summary_amount_inscashout,
)
from wyse_ussd.helper.telco_mega_cash3 import mega_cash_item, wyse_cash_summary_amount
from wyse_ussd.helper.telco_salary_life3 import (
    salary_for_life_amount,
    telco_salary_life3_item,
    telco_summary_amount_salary_for_life,
)
from wyse_ussd.models import (
    TelcoConstant,
    TelcoSubscriptionRequest,
    create_wyse_lotto_ticket,
)
from wyse_ussd.tasks import (
    celery_telco_airtime_charge,
    celery_telco_ussd_backgroud,
    celery_update_teclo_users_winning_withdrawal,
    celery_update_telco_session,
    create_ussd_virtual_account,
    ussd_payout,
)


def extract_text_after_44(text):
    if "*" in text:
        matches = re.findall(r"44\*(.*?)#", text)
        try:
            return matches[0]
        except Exception:
            return ""

    return text


def telco_ussd_menu(data):
    # menu_options = "Welcome to WinWise:\nWin cash prizes up to 10m daily.\n\n"
    # menu_options += "1.Win Quick Cash\n"
    # menu_options += "2.Win Cash 4 life\n"
    # menu_options += "3.Win Mega Cash \n"
    # menu_options += "4.Awoof Game \n"
    # menu_options += "11.Next \n"

    converted_string = xml_data_formater(data)
    converted_string = json.loads(converted_string)

    phone_number = converted_string.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:notifyUssdReception", {}).get("ns2:msIsdn")

    # user_instance = UserProfile.objects.filter(phone_number=phone_number).last()
    # verification_code = user_instance.verification_code if user_instance else "Unknown"

    # menu_options = f"Welcome to WinWise:\nWin cash prizes up to 10m daily.\nVerification code{verification_code}\n\n"
    menu_options = "Welcome to WinWise:\nWin cash prizes up to 10m daily.\n\n"

    # menu_options += "1.Win InstantCash\n"
    # menu_options += "2.Win Salary For Life\n"
    # menu_options += "3.Win Wyse Cash \n"
    # menu_options += "4.Fast Fingers \n"
    # menu_options += "11.Next \n"

    # menu_options += "1.Fast Fingers\n"
    # menu_options += "2.Win InstantCash\n"
    # menu_options += "3.Win Salary For Life \n"
    # menu_options += "4.Win Wyse Cash \n"
    # menu_options += "11.Next \n"

    menu_options += "1.Win Salary For Life\n"
    menu_options += "2.Win InstantCash\n"
    menu_options += "3.Win Wyse Cash \n"
    menu_options += "4.Fast Fingers \n"
    menu_options += "11.Next \n"

    telco_aggregator = BBCTelcoAggregator()

    print(
        f"""
    request body: {converted_string}
    \n\n\n\n\n
    """
    )

    # print("type of converted_string", type(converted_string), "\n\n\n")

    # print("converted_string", converted_string, "\n\n\n\n")

    msg_type = converted_string.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:notifyUssdReception", {}).get("ns2:msgType")

    # ussd_op_type = converted_string.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:notifyUssdReception", {}).get("ns2:ussdOpType")

    code_scheme = converted_string.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:notifyUssdReception", {}).get("ns2:codeScheme")
    service_code = converted_string.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:notifyUssdReception", {}).get("ns2:serviceCode")

    sender_cb = converted_string.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:notifyUssdReception", {}).get("ns2:senderCB")

    ussd_string = converted_string.get("soapenv:Envelope", {}).get("soapenv:Body", {}).get("ns2:notifyUssdReception", {}).get("ns2:ussdString")

    password = converted_string.get("soapenv:Envelope", {}).get("soapenv:Header", {}).get("ns3:NotifySOAPHeader", {}).get("ns3:spRevpassword")

    partner_id = converted_string.get("soapenv:Envelope", {}).get("soapenv:Header", {}).get("ns3:NotifySOAPHeader", {}).get("ns3:spId")

    service_id = converted_string.get("soapenv:Envelope", {}).get("soapenv:Header", {}).get("ns3:NotifySOAPHeader", {}).get("ns3:serviceId")

    timestamp = converted_string.get("soapenv:Envelope", {}).get("soapenv:Header", {}).get("ns3:NotifySOAPHeader", {}).get("ns3:timeStamp")

    session_id = sender_cb

    phone = LotteryModel.format_number_from_back_add_234(phone_number)

    original_ussd_string = ussd_string

    payload = {
        "ussd_string": menu_options,
        "code_scheme": code_scheme,
        "service_code": service_code,
        "phone_number": phone_number,
        "sender_cb": sender_cb,
        "password": password,
        "partner_id": partner_id,
        "service_id": service_id,
        "timestamp": timestamp,
        # "ussd_op_type": ussd_op_type,
        # "msg_type": msg_type,
        "ussd_op_type": "1",
        "msg_type": "1",
    }

    """
    RESTRICTION CHECK
    """
    if BlackListed().is_blacklisted(phone=phone_number):
        menu_options = "You are not allowed to use this service"
        payload["ussd_string"] = menu_options

        return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

    if BlackListed().can_play_instant_cashout(phone=phone_number) is False:
        menu_options = "Instant cashout is not accessible for you at the moment"
        payload["ussd_string"] = menu_options

        return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

    HopsRestrictor.is_restricted(phone_number)

    # if restrictor.get("response"):
    #     if restrictor.get("reason") == "PENDING_PAYMENT":
    #         menu_options = str(summary(phone_number)).replace("CON", "")
    #         payload["ussd_string"] = menu_options

    # print("USSD PENDING_PAYMENT", menu_options)

    # return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)
    """
    END OF RESTRICTION CHECK
    """

    """
    JUST A SIMPLE ALGORITHM TO CHECK IF USER DIALS MENU OPTION AT A SINGLR DAIL.
    e.g *20144*1# or *20144*2# or *20144*3# or *20144*4#
    the string after the second * and before the # is the menu option
    """

    # if ussd_string.endswith("#"):
    #     counter = 0
    #     result = []
    #     flag = False

    #     for char in ussd_string:
    #         if char == "*":
    #             counter += 1
    #             if counter == 2:
    #                 flag = True
    #                 msg_type = "*"
    #         elif char == "#":
    #             break
    #         elif flag:
    #             result.append(char)

    #     # formate it to * string
    #     if len(ussd_string) > 1:
    #         ussd_string = "*".join(result)
    #     else:
    #         ussd_string = "".join(result)

    ussd_string = extract_text_after_44(ussd_string)

    print("msg_type", msg_type)
    print("ussd_string", ussd_string, "\n\n\n\n\n\n")

    if msg_type == "0" and ussd_string == "":
        print("msg_type is 0, msg_type == 0 ==================================: \n\n\n\n")
        # payload = {
        #     "ussd_string": menu_options,
        #     "code_scheme": code_scheme,
        #     "service_code": service_code,
        #     "phone_number": phone_number,
        #     "sender_cb": sender_cb,
        #     "password": password,
        #     "partner_id": partner_id,
        #     "service_id": service_id,
        #     "timestamp": timestamp,
        # }

        # print("msg_type is 0, msg_type == 0")

        payload["ussd_op_type"] = "1"
        payload["msg_type"] = "1"
        payload["ussd_string"] = menu_options
        # payload["msg_type"] = "1"

        # telco_aggregator.notify_ussd_reception_response(**payload)
        if settings.DEBUG is True:
            celery_telco_ussd_backgroud.delay(**payload)
        else:
            celery_telco_ussd_backgroud.apply_async(queue="telcoreq", kwargs=payload)

        # telco_aggregator.send_ussd_message(**payload)

        return telco_aggregator.ussd_response(send=False)

    else:
        """
        CONSTRUCTING USER TEXT INPUT TO LOOK LIKE THAT OF AFRICA'S TALKING
        EXAMPLE:
        "1*1*1"
        "1*1*2"
        """

        """
        CONTINUATION USSD PAYLOAD


        <?xml version="1.0" encoding="UTF-8"?>
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
            <soapenv:Header>
                <ns3:NotifySOAPHeader xmlns:ns3="http://www.huawei.com.cn/schema/common/v2_1">
                    <ns3:spRevId />
                    <ns3:spRevpassword>c38c32f923dffba3207d13b70bc00e22</ns3:spRevpassword>
                    <ns3:spId>whispaussd</ns3:spId>
                    <ns3:serviceId />
                    <ns3:timeStamp>20230627141814</ns3:timeStamp>
                </ns3:NotifySOAPHeader>
            </soapenv:Header>
            <soapenv:Body>
                <ns2:notifyUssdReception xmlns:ns2="http://www.csapi.org/schema/parlayx/ussd/notification/v1_0/local">
                    <ns2:msgType>1</ns2:msgType>
                    <ns2:senderCB>911329262</ns2:senderCB>
                    <ns2:receiveCB>911329262</ns2:receiveCB>
                    <ns2:ussdOpType>3</ns2:ussdOpType>
                    <ns2:msIsdn>2348038705895</ns2:msIsdn>
                    <ns2:serviceCode>7874</ns2:serviceCode>
                    <ns2:codeScheme>68</ns2:codeScheme>
                    <ns2:ussdString>1</ns2:ussdString>
                </ns2:notifyUssdReception>
            </soapenv:Body>
        </soapenv:Envelope>
        """

        telco_redis_db = TelcoRedisStorage(f"telco_session_{sender_cb}")
        user_ussd_string = telco_redis_db.get_data()
        if user_ussd_string is None:
            user_ussd_string = ussd_string
        else:
            user_ussd_string = f"{user_ussd_string}*{ussd_string}"
            ussd_string = user_ussd_string

        telco_redis_db.set_data(user_ussd_string)

        print(
            "user_ussd_string ussd",
            user_ussd_string,
            "user_ussd_string",
            user_ussd_string,
            "ussd_string",
            original_ussd_string,
            "\n\n\n\n\n",
        )

        text = text_handler(ussd_string)

        splited_text = text.split("*")
        splited_text = [i for i in splited_text if i != ""]

        print("text", text, "\n\n\n\n\n")

        if text == "":
            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={"user_phone": phone_number, "session_id": sender_cb},
            )

            payload["ussd_op_type"] = "1"
            payload["msg_type"] = "1"
            payload["ussd_string"] = menu_options
            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcoreq", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        if text == "11":
            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={"user_phone": phone_number, "session_id": sender_cb},
            )

            response = "Welcome to WinWise:\nWin cash prizes up to 10m daily.\n\n"
            response += "5.Soccer Cash \n"
            response += "6.About \n"
            response += "7.My Account \n"
            response += "8.Payout \n"
            response += "9.result \n"
            response += "0.back \n"

            payload["ussd_string"] = response

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcoreq", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # :::::::::::::::::::::::::::::::::: User verification code
        if text == "50":
            # get user profile instance
            try:
                user_instance = UserProfile.objects.get(phone_number=phone_number)
            except Exception:
                menu_options = "An error occured, please try again later"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if (user_instance.verification_code is None) or (user_instance.verification_code == ""):
                user_instance.verification_code = UserProfile.generate_mixed_pin()
                user_instance.save()

            menu_options = f"Your verification code is {user_instance.verification_code}"
            payload["ussd_string"] = menu_options
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # :::::::::::::::::::::::::::::::::: User verification code

        # :::::::::::::::::::::::::::::::::: SALARY FOR LIFE
        if text == "1":
            menu_options = telco_salary_life3_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcoreq", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("1*"):
            # salary_for_life_line_validate = salary_for_life_line_validator(text)
            # if salary_for_life_line_validate is not True:
            #     menu_options = (salary_for_life_line_validate).replace("CON", "")
            #     payload["ussd_string"] = menu_options

            #     return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

            collect_bank_details = TelcoConstant().get_collect_bank_details()
            if collect_bank_details is True:
                if len(splited_text) == 2:
                    user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
                    if not user_profile:
                        user_profile = UserProfile.objects.create(phone_number=phone_number, from_telco=True)

                    if user_profile.bank_code is None:
                        # collect bank details
                        response = select_bank_telco()
                        menu_options = str(response).replace("CON", "")

                        payload["ussd_string"] = menu_options

                        if settings.DEBUG is True:
                            celery_telco_ussd_backgroud.delay(**payload)
                        else:
                            celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                        return telco_aggregator.ussd_response(send=False)

                else:
                    bank = select_bank_telco(splited_text[2])
                    bank_db = BankManger().bank_details(bank_name=bank)

                    if not bank_db:
                        menu_options = "Invalid bank"
                        payload["ussd_string"] = menu_options
                        payload["ussd_op_type"] = "4"
                        payload["msg_type"] = "2"

                        if settings.DEBUG is True:
                            celery_telco_ussd_backgroud.delay(**payload)
                        else:
                            celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                        return telco_aggregator.ussd_response(send=False)

                    user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
                    if not user_profile:
                        user_profile = UserProfile.objects.create(phone_number=phone_number, from_telco=True)

                    user_profile.bank_code = bank_db.get("cbn_code")
                    user_profile.bank_name = bank_db.get("name")
                    user_profile.save()

            if len(splited_text) > 2:
                splited_text[2:] = []

            jackpot = splited_text[1]

            if not isinstance(salary_for_life_amount(jackpot), int):
                response = salary_for_life_amount(f"{jackpot}")

                menu_options = str(response).replace("CON", "")
                payload["ussd_string"] = menu_options

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

            num_lines = splited_text[1]

            lotto = create_wyse_lotto_ticket(
                phone_number=phone_number,
                lottery_type="SALARY_FOR_LIFE",
                jackpot=jackpot,
                bank="",
                auto=True,
                stake_amount=salary_for_life_amount(jackpot),
                telco=True,
            )

            print(f"lotto =================== {lotto}")
            print("\n\n\n")

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            # lottery_instance = LottoTicket.objects.filter(
            #     user_profile=user_profile, lottery_type="SALARY_FOR_LIFE"
            # ).last()

            stake_and_win_amount = telco_summary_amount_salary_for_life.get(int(splited_text[1]))

            telco_service_id_telco_product_id = TelcoSalaryForLifePriceModel().get_bbc_service_and_prodct_details(int(stake_and_win_amount[0]))

            """
            TELCO CHARGES
            """
            telco_charge_payload = {
                "phone_number": phone_number,
                "description": "salary for life lottery payment",
                "amount": stake_and_win_amount[0],
                "game_play_id": lotto.game_play_id,
                "lottery_type": "SALARY_FOR_LIFE",
                "pontential_winning": stake_and_win_amount[1],
                "service_id": telco_service_id_telco_product_id[0],
                "product_id": telco_service_id_telco_product_id[1],
            }

            celery_telco_airtime_charge.apply_async(queue="telcocharge1", kwargs=telco_charge_payload, countdown=5)

            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={"user_phone": phone_number, "session_id": sender_cb},
            )

            # response = telco_salary_life3_item["summary"].format(
            #     num_lines,
            #     Utility.currency_formatter(stake_and_win_amount[0]),
            #     lotto.ticket,
            #     Utility.currency_formatter(stake_and_win_amount[1]),
            # )

            response = telco_salary_life3_item["summary"].format(
                num_lines,
                Utility.currency_formatter(stake_and_win_amount[0]),
                lotto.ticket,
            )

            menu_options = str(response).replace("END", "")
            payload["ussd_string"] = menu_options

            # print("lotto.ticket", "\n\n\n\n\n")

            return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

        # :::::::::::::::::::::::::::::::::: END SALARY FOR LIFE

        # :::::::::::::::::::::::::::::::::: INSTANT CASHOUT
        elif text == "2":
            menu_options = instant_cash_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["ussd_op_type"] = "1"
            payload["msg_type"] = "1"
            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcoreq", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("2*"):  # and ((len(text) == 3) or (len(text) == 4))
            collect_bank_details = TelcoConstant().get_collect_bank_details()
            if collect_bank_details is True:
                if len(splited_text) == 2:
                    user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
                    if not user_profile:
                        user_profile = UserProfile.objects.create(phone_number=phone_number, from_telco=True)

                    if user_profile.bank_code is None:
                        # collect bank details
                        response = select_bank_telco()
                        menu_options = str(response).replace("CON", "")

                        payload["ussd_string"] = menu_options

                        if settings.DEBUG is True:
                            celery_telco_ussd_backgroud.delay(**payload)
                        else:
                            celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                        return telco_aggregator.ussd_response(send=False)

                else:
                    bank = select_bank_telco(splited_text[2])
                    bank_db = BankManger().bank_details(bank_name=bank)

                    if not bank_db:
                        menu_options = "Invalid bank"
                        payload["ussd_string"] = menu_options
                        payload["ussd_op_type"] = "4"
                        payload["msg_type"] = "2"

                        if settings.DEBUG is True:
                            celery_telco_ussd_backgroud.delay(**payload)
                        else:
                            celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                        return telco_aggregator.ussd_response(send=False)

                    user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
                    if not user_profile:
                        user_profile = UserProfile.objects.create(phone_number=phone_number, from_telco=True)

                    user_profile.bank_code = bank_db.get("cbn_code")
                    user_profile.bank_name = bank_db.get("name")
                    user_profile.save()

            if len(splited_text) > 2:
                splited_text[2:] = []

            splited_text[:2]

            jackpot = splited_text[1]

            if not isinstance(instant_cashout_amount(jackpot), int):
                response = instant_cashout_amount(f"{jackpot}")

                menu_options = str(response).replace("CON", "")
                payload["ussd_string"] = menu_options

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

            num_lines = splited_text[1]

            """
            HANDLE WHEN USER WANT TO PLAY INSTANT CASHOUT 300 NAIRA GAME.
            split/regiter the ticket as two seperate ticket.

            So, what this logic will do is to create two ticket of 200 naira and 150 naira will be charge for the game, first ticket will be create and payment charge will be initiated
            then the second ticket will be created and payment charge will be initiated, then the system will return the summary page for the two tickets.
            
            """  # noqa

            first_ticket = None

            stake_amount = instant_cashout_amount(jackpot)

            if "END" in str(stake_amount):
                menu_options = str(stake_amount).replace("END", "")
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge1", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if stake_amount == 300:
                lotto = create_wyse_lotto_ticket(
                    phone_number=phone_number,
                    lottery_type="INSTANT_CASHOUT",
                    jackpot=1,
                    bank="",
                    auto=True,
                    stake_amount=stake_amount,
                    telco=True,
                )

                phone = LotteryModel.format_number_from_back_add_234(phone_number)
                user_profile = UserProfile.objects.filter(phone_number=phone).last()

                # lottery_instance = LottoTicket.objects.filter(
                #     user_profile=user_profile, lottery_type="INSTANT_CASHOUT"
                # ).last()

                stake_and_win_amount = summary_amount_inscashout.get(int(splited_text[1]))

                telco_service_id_telco_product_id = TelcoInstantCashOutPriceModel().get_bbc_service_and_prodct_details(stake_amount)

                # noqa

                first_ticket = lotto.ticket
                num_lines = 2

                """
                TELCO CHARGES
                """

                telco_charge_payload = {
                    "phone_number": phone_number,
                    "description": "instant cashout lottery payment",
                    "amount": stake_amount,
                    "game_play_id": lotto.game_play_id,
                    "lottery_type": "INSTANT_CASHOUT",
                    "pontential_winning": stake_and_win_amount[1],
                    "service_id": telco_service_id_telco_product_id[0],
                    "product_id": telco_service_id_telco_product_id[1],
                }

                # celery_telco_airtime_charge.apply_async(
                #     queue="telcocharge", kwargs=telco_charge_payload, countdown=5
                # )

            if stake_amount == 300:
                stake_amount = stake_amount

            _stake_amount = stake_amount
            if _stake_amount == 150:
                _stake_amount = 300

            # lotto = create_wyse_lotto_ticket(
            #     phone_number=phone_number,
            #     lottery_type="INSTANT_CASHOUT",
            #     jackpot=jackpot,
            #     bank="",
            #     auto=True,
            #     stake_amount=300 if stake_amount == 150 else stake_amount,
            #     telco=True,
            # )
            lotto = create_wyse_lotto_ticket(
                phone_number=phone_number,
                lottery_type="INSTANT_CASHOUT",
                jackpot=jackpot,
                bank="",
                auto=True,
                stake_amount=_stake_amount,
                telco=True,
            )

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            # lottery_instance = LottoTicket.objects.filter(
            #     user_profile=user_profile, lottery_type="INSTANT_CASHOUT"
            # ).last()

            stake_and_win_amount = summary_amount_inscashout.get(int(splited_text[1]))

            telco_service_id_telco_product_id = TelcoInstantCashOutPriceModel().get_bbc_service_and_prodct_details(stake_amount)

            """
            TELCO CHARGES
            """

            telco_charge_payload = {
                "phone_number": phone_number,
                "description": "instant cashout lottery payment",
                "amount": stake_amount,
                "game_play_id": lotto.game_play_id,
                "lottery_type": "INSTANT_CASHOUT",
                "pontential_winning": stake_and_win_amount[1],
                "service_id": telco_service_id_telco_product_id[0],
                "product_id": telco_service_id_telco_product_id[1],
            }

            celery_telco_airtime_charge.apply_async(queue="telcocharge1", kwargs=telco_charge_payload, countdown=5)

            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={"user_phone": phone_number, "session_id": sender_cb},
            )

            registered_tickets = lotto.ticket
            if first_ticket is not None:
                registered_tickets = f"{first_ticket}\n{registered_tickets}"

            # response = instant_cash_item["summary"].format(
            #     num_lines,
            #     Utility.currency_formatter(stake_and_win_amount[0]),
            #     # Utility.currency_formatter(lotto.stake_amount * int(num_lines)),
            #     registered_tickets,
            #     Utility.currency_formatter(stake_and_win_amount[1]),
            #     # lotto.game_play_id
            # )
            response = instant_cash_item["summary"].format(
                num_lines,
                Utility.currency_formatter(stake_and_win_amount[0]),
                # Utility.currency_formatter(lotto.stake_amount * int(num_lines)),
                registered_tickets,
            )

            menu_options = str(response).replace("END", "")
            payload["ussd_string"] = menu_options

            # print("lotto.ticket", "\n\n\n\n\n")

            return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

        # :::::::::::::::::::::::::::::::::: END INSTANT CASHOUT

        # :::::::::::::::::::::::::::::::::: WYSE CASH

        elif text == "3":
            menu_options = mega_cash_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)
        elif text == "3*1":
            menu_options = mega_cash_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)
        elif text == "3*2":
            menu_options = mega_cash_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)
        elif text == "3*3":
            menu_options = mega_cash_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)
        elif text == "3*4":
            menu_options = mega_cash_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("3*"):
            ussd_lottery_play2(
                phone_number=phone_number,
                lottery_type="wyse_cash",
                text=text,
                from_telco_play=True,
            )

            collect_bank_details = TelcoConstant().get_collect_bank_details()
            if collect_bank_details is True:
                if len(splited_text) == 3:
                    user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
                    if not user_profile:
                        user_profile = UserProfile.objects.create(phone_number=phone_number, from_telco=True)

                    if user_profile.bank_code is None:
                        # collect bank details
                        response = select_bank_telco()
                        menu_options = str(response).replace("CON", "")

                        payload["ussd_string"] = menu_options

                        if settings.DEBUG is True:
                            celery_telco_ussd_backgroud.delay(**payload)
                        else:
                            celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                        return telco_aggregator.ussd_response(send=False)

                else:
                    bank = select_bank_telco(splited_text[3])
                    bank_db = BankManger().bank_details(bank_name=bank)

                    if not bank_db:
                        menu_options = "Invalid bank"
                        payload["ussd_string"] = menu_options
                        payload["ussd_op_type"] = "4"
                        payload["msg_type"] = "2"

                        if settings.DEBUG is True:
                            celery_telco_ussd_backgroud.delay(**payload)
                        else:
                            celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                        return telco_aggregator.ussd_response(send=False)

                    user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
                    if not user_profile:
                        user_profile = UserProfile.objects.create(phone_number=phone_number, from_telco=True)

                    user_profile.bank_code = bank_db.get("cbn_code")
                    user_profile.bank_name = bank_db.get("name")
                    user_profile.save()

            if len(splited_text) > 3:
                splited_text[2:] = []

            splited_text[:2]

            if len(text) > 5:
                text = text[:5]

            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            create_ussd_virtual_account.apply_async(queue="celery1", args=[phone])

            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            # get last played lottery
            lottery_instance = LotteryModel.objects.filter(user_profile=user_profile).last()

            stake_and_win_amount = wyse_cash_summary_amount.get(text)

            telco_service_id_telco_product_id = TelcoWyseCashPriceModel().get_bbc_service_and_prodct_details(
                int(stake_and_win_amount[0]) / int(text[-1])
            )

            """
            TELCO CHARGES
            """

            telco_charge_payload = {
                "phone_number": phone_number,
                "description": "wyse cash lottery payment",
                "amount": stake_and_win_amount[0] / int(text[-1]),
                "game_play_id": lottery_instance.game_play_id,
                "lottery_type": "WYSE_CASH",
                "pontential_winning": stake_and_win_amount[1],
                "service_id": telco_service_id_telco_product_id[0],
                "product_id": telco_service_id_telco_product_id[1],
                "charge_iteration": int(text[-1]),
            }

            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={"user_phone": phone_number, "session_id": sender_cb},
            )

            print(
                f"""
                  telco_charge_payload: {telco_charge_payload}
                  \n\n\n
                  """
            )

            celery_telco_airtime_charge.apply_async(queue="telcocharge1", kwargs=telco_charge_payload, countdown=5)

            # user profile
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            # lottery_instance = LotteryModel.objects.filter(
            #     user_profile=user_profile
            # ).last()

            # response = mega_cash_item["summary"].format(
            #     lottery_instance.instance_number,
            #     Utility.currency_formatter(stake_and_win_amount[0]),
            #     # Utility.currency_formatter(
            #     #     lottery_instance.stake_amount * lottery_instance.instance_number
            #     # ),
            #     Utility.currency_formatter(stake_and_win_amount[1]),
            #     # Utility.currency_formatter(
            #     #     int(lottery_instance.band) * lottery_instance.instance_number
            #     # ),
            # )

            response = mega_cash_item["summary"].format(
                lottery_instance.instance_number,
                Utility.currency_formatter(stake_and_win_amount[0]),
                lottery_instance.game_play_id,
            )

            menu_options = str(response).replace("END", "")
            payload["ussd_string"] = menu_options

            # print("lotto.ticket", "\n\n\n\n\n")

            return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

        # :::::::::::::::::::::::::::::::::: END WYSE CASH

        # :::::::::::::::::::::::::::::::::: AWOOF GAME / FAST FINGERS

        elif text == "4":
            awoof_items = LifeStyleTable().awoof_ussd_iteams(session_id)
            if awoof_items is None:
                menu_options = "No awoof item available at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            else:
                response = "Select your choice item: \n"
                response += awoof_items
                menu_options = response

                payload["ussd_string"] = menu_options

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        elif text.startswith("4*") and len(splited_text) == 2:
            last_item = splited_text[-1]
            try:
                last_item = int(last_item)
            except Exception:
                menu_options = "Invalid option selected"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            awoof_price_items = LifeStyleTable().awoof_telco_ussd_item_price(session_id=session_id, user_number_selected=last_item)

            if awoof_price_items is None:
                menu_options = "Invalid option selected"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"
                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)
            else:
                response = awoof_price_items

                response = str(response).replace("CON", "")

                menu_options = response

                payload["ussd_string"] = menu_options
                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        elif text.startswith("4*") and len(splited_text) == 3:
            last_item = splited_text[-1]

            item_id_key_in_incoming_text = splited_text[-2]

            awoof_price_redis_stored_key = f"{session_id}_{item_id_key_in_incoming_text}"

            print(
                "awoof_price_redis_stored_key",
                awoof_price_redis_stored_key,
                "\n\n\n\n\n",
            )

            _item_id_stored_in_redis = RedisStorage(redis_key=awoof_price_redis_stored_key)

            print(
                f"""
            _item_id_stored_in_redis.get_data()
            {_item_id_stored_in_redis.get_data()}
            {type("_item_id_stored_in_redis.get_data()"), _item_id_stored_in_redis.get_data()}
            {_item_id_stored_in_redis.get_data() is None:}

            """,
            )

            if _item_id_stored_in_redis.get_data() is None:
                menu_options = "Something went wrong, please try again again."
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"
                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            item_id_num = (_item_id_stored_in_redis.get_data()).decode("utf-8")

            try:
                item_id_num = int(item_id_num)
            except Exception:
                print("FAILED TO CONVERT TO INT")

                menu_options = "Something went wrong, please try again again."
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            price_data_in_redis = IllusionDatabase(redis_key=f"{session_id}_price").get_data()

            user_selected_chances = splited_text[-1]

            ticket_price = price_data_in_redis.get(user_selected_chances)

            # print(f"""
            #       user_selected_chances: {user_selected_chances}
            #         ticket_price: {ticket_price}
            #       """)

            if ticket_price is None:
                menu_options = "Something went wrong, please try again again."
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            stake_amount = int(str(ticket_price).replace(".0", ""))

            item = LifeStyleTable.objects.get(id=int(item_id_num))

            awoof_game_instance = AwoofGameTable().register_telco_ussd_awoof_game(
                awoof_item_db_id=int(item_id_num),
                user_selected_chances=200,
                user_phone_number=phone_number,
                band_number=int(user_selected_chances),
                from_telco_channel=True,
            )

            if awoof_game_instance is None:
                menu_options = "Something went wrong, please try again again."
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            telco_service_id_telco_product_id = TelcoAwoofPriceModel().get_bbc_service_and_prodct_details(
                int((stake_amount) / (int(user_selected_chances)))
            )

            """
            TELCO CHARGES
            """

            telco_charge_payload = {
                "phone_number": phone_number,
                "description": "fast fingers lottery payment",
                "amount": stake_amount / int(user_selected_chances),
                "game_play_id": awoof_game_instance.game_play_id,
                "lottery_type": "AWOOF",
                "pontential_winning": item.item_name,
                "service_id": telco_service_id_telco_product_id[0],
                "product_id": telco_service_id_telco_product_id[1],
                "charge_iteration": int(user_selected_chances),
            }

            celery_telco_airtime_charge.apply_async(queue="telcocharge1", kwargs=telco_charge_payload, countdown=5)

            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={"user_phone": phone_number, "session_id": sender_cb},
            )

            response = "Summary: Fast Fingers\n"
            response += f"Potential Win: {item.item_name}\n"
            response += f"No. of ticket: {user_selected_chances}\n"
            response += f"Amt: {Utility.currency_formatter(int(str(ticket_price).replace('.0','')) / int(user_selected_chances) )}\n"

            payload["ussd_string"] = response
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # :::::::::::::::::::::::::::::::::: END AWOOF GAME / FAST FINGERS

        # :::::::::::::::::::::::::::::::::: SOCCER CASH

        elif text == "5":
            menu_options = "1. Predict and Win\n2. Soccer Predictions"
            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # ::::::::::::::::::::::::::::: predict and win section
        elif text.startswith("5*1"):
            if text == "5*1":
                menu_options = SoccerFixtures.welcome_screen()
                if "END" in menu_options:
                    # ::::::::::::::::::::::::::::: end of this section
                    menu_options = menu_options.replace("END", "")
                    payload["ussd_string"] = menu_options
                    payload["ussd_op_type"] = "4"
                    payload["msg_type"] = "2"

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)
                    # :::::::::::::::::::::::::::::

                payload["ussd_string"] = str(menu_options)
                menu_options = menu_options.replace("CON", "")

                payload["ussd_string"] = menu_options

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcoreq", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            elif text.startswith("5*1*") and (len(splited_text) == 3):
                if isinstance(SoccerFixtures.select_league(splited_text[1]), dict):
                    display_message = SoccerFixtures.select_league(splited_text[1]).get("display_msg")
                else:
                    display_message = SoccerFixtures.select_league(splited_text[1])

                menu_options = str(display_message).replace("CON", "")
                if "END" in menu_options:
                    # ::::::::::::::::::::::::::::: end of this section
                    menu_options = menu_options.replace("END", "")
                    payload["ussd_string"] = menu_options
                    payload["ussd_op_type"] = "4"
                    payload["msg_type"] = "2"

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)
                    # :::::::::::::::::::::::::::::

                payload["ussd_string"] = menu_options
                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcoreq", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            elif text.startswith("5*1*") and (len(splited_text) == 4):
                menu_options = SoccerFixtures.score_entery(splited_text[2], splited_text[3])
                if "END" in menu_options:
                    # ::::::::::::::::::::::::::::: end of this section
                    menu_options = menu_options.replace("END", "")
                    payload["ussd_string"] = menu_options
                    payload["ussd_op_type"] = "4"
                    payload["msg_type"] = "2"

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)
                    # :::::::::::::::::::::::::::::

                payload["ussd_string"] = menu_options

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcoreq", kwargs=payload)

            elif text.startswith("5*") and (len(splited_text) == 5):
                prediction = "-".join(splited_text[3].split(" "))
                if len(prediction) >= 3:
                    response = SoccerFixtures.confirm_entery_band_options(splited_text[1], splited_text[2], prediction)
                else:
                    response = "END Please enter valid scores with a space e.g (4 3)"

                menu_options = str(response).replace("CON", "")
                if "END" in menu_options:
                    # ::::::::::::::::::::::::::::: end of this section
                    menu_options = menu_options.replace("END", "")
                    payload["ussd_string"] = menu_options
                    payload["ussd_op_type"] = "4"
                    payload["msg_type"] = "2"

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)
                    # :::::::::::::::::::::::::::::

                payload["ussd_string"] = menu_options

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcoreq", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            elif text.startswith("5*") and (len(splited_text) == 6):
                menu_options = "Game not available at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

                # event_properties = SoccerFixtures.fixtures_properties(splited_text[1], splited_text[2])
                # soccer_prediction_ins = SoccerPrediction.create_leauge_prediction_object(
                #     desired_play=splited_text[4],
                #     prediction=splited_text[3].split(" "),
                #     phone_no=phone_number,
                #     fixtures_id=event_properties[-1],
                #     # bank=bank,
                # )

                # if soccer_prediction_ins is None:
                #     response = "Invalid prediction"

                #     # ::::::::::::::::::::::::::::: end of this section
                #     menu_options = menu_options.replace("END", "")
                #     payload["ussd_string"] = menu_options
                #     payload["ussd_op_type"] = "4"
                #     payload["msg_type"] = "2"

                #     if settings.DEBUG is True:
                #         celery_telco_ussd_backgroud.delay(**payload)
                #     else:
                #         celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                #     return telco_aggregator.ussd_response(send=False)

                # sleep(2)

                # phone_no = LotteryModel.format_number_from_back_add_234(phone_number)

                # prediction_instance = SoccerPrediction.objects.filter(phone=phone_no).last()

                # telco_service_id_telco_product_id = TelcoSoccerCashPriceModel().get_bbc_service_and_prodct_details(int(stake_and_win_amount[0]))

        # ::::::::::::::::::::::::::::: end of predict and win section

        # ::::::::::::::::::::::::::::: soccer prediction section
        elif text.startswith("5*2") and (len(splited_text) == 2):
            response = "Pick daily subscription plan\n"
            response += "1. N50 Sub. 2 predictions\n"
            response += "2. N75 Sub. 5 predictions\n"

            payload["ussd_string"] = response

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("5*2*1"):
            menu_options = "Game not available at the moment"
            payload["ussd_string"] = menu_options
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            TelcoSubscriptionRequest.create_request(
                amount=50,
                phone_number=phone_number,
                game_type="SOCCER_CASH",
            )

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("5*2*2"):
            menu_options = "Game not available at the moment"
            payload["ussd_string"] = menu_options
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            TelcoSubscriptionRequest.create_request(
                amount=75,
                phone_number=phone_number,
                game_type="SOCCER_CASH",
            )

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("5*2*"):
            menu_options = "Invalid option selected"
            payload["ussd_string"] = menu_options
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)
        # :::::::::::::::::::::::::::::::::: END SOCCER CASH

        #
        # account
        elif text == "7":
            response = "Select an option:\n"
            response += "1. Check Balance\n"
            response += "2. Deposit\n"
            response += "3. Transaction History\n"

            menu_options = response
            payload["ussd_string"] = menu_options
            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text == "7*1":
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            response = f"Your play account balance is {Utility.currency_formatter(user_wallet.game_available_balance)}"
            response += f"\nYour withdrawable balance is {Utility.currency_formatter(user_wallet.withdrawable_available_balance)}"

            menu_options = response

            payload["ussd_string"] = menu_options
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text == "7*2":
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                response = "END You don't have a deposit account number yet, check back in 10 minutes"
                return response

            if user_wallet.woven_account is None:
                response = "END You don't have a deposit account number yet, check back in 10 minutes"
                return response

            response = "Please deposit into this account details below:\n"
            response += f"Account Name: {user_wallet.woven_account.acct_name}\n"
            response += f"Account Number: {user_wallet.woven_account.vnuban}\n"
            response += f"Bank Name: {user_wallet.woven_account.bank_name}\n"

            menu_options = response

            payload["ussd_string"] = menu_options
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text == "7*3":
            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            user_game_play_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="VIRTUAL_SOCCER_GAME_PLAY")
                    | Q(transaction_from="SOCCER_CASH_GAME_PLAY")
                    | Q(transaction_from="SAL_4_LIFE_GAME_PLAY")
                    | Q(transaction_from="INSTANT_CASHOUT_GAME_PLAY")
                    | Q(transaction_from="WYSE_CASH_GAME_PLAY")
                    | Q(transaction_from="VIRTUAL_SOCCER_GAME_PLAY")
                    | Q(transaction_from="BANKER_GAME_PLAY")
                    | Q(transaction_from="QUIKA_GAME_PLAY")
                    | Q(transaction_from="AWOOF_GAME_PLAY"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_game_play_transactions:
                last_game_play_transaction_amount = user_game_play_transactions.amount
            else:
                last_game_play_transaction_amount = 0

            user_payout_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="WITHDRAWAL"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_payout_transactions:
                last_payout_transaction_amount = user_payout_transactions.amount
            else:
                last_payout_transaction_amount = 0

            user_game_won_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="VIRTUAL_SOCCER_GAME_PLAY")
                    | Q(transaction_from="SOCCER_CASH_GAME_WIN")
                    | Q(transaction_from="SAL_4_LIFE_GAME_WIN")
                    | Q(transaction_from="INSTANT_CASHOUT_GAME_WIN")
                    | Q(transaction_from="WYSE_CASH_GAME_WIN")
                    | Q(transaction_from="VIRTUAL_SOCCER_GAME_WON")
                    | Q(transaction_from="BANKER_GAME_WON")
                    | Q(transaction_from="QUIKA_GAME_WON")
                    | Q(transaction_from="AWOOF_GAME_WON"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_game_won_transactions:
                last_game_won_transaction_amount = user_game_won_transactions.amount
            else:
                last_game_won_transaction_amount = 0

            user_funding_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="PAYSTACK_FUNDING")
                    | Q(transaction_from="PABBLY_PAYSTACK")
                    | Q(transaction_from="WOVEN_FUNDING")
                    | Q(transaction_from="FUNDING")
                    | Q(transaction_from="FUNDING_FROM_WITHDRAWABLE_WALLET")
                    | Q(transaction_from="REDBILLER_FUNDING")
                    | Q(transaction_from="WATU_PAY"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_funding_transactions:
                last_funding_transaction_amount = user_funding_transactions.amount
            else:
                last_funding_transaction_amount = 0

            user_reversal_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="REVERSAL"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_reversal_transactions:
                last_reversal_transaction_amount = user_reversal_transactions.amount
            else:
                last_reversal_transaction_amount = 0

            response = f"Last game play amount: {Utility.currency_formatter(last_game_play_transaction_amount)}\n"
            response += f"Last payout amount: {Utility.currency_formatter(last_payout_transaction_amount)}\n"
            response += f"Last game won amount: {Utility.currency_formatter(last_game_won_transaction_amount)}\n"
            response += f"Last reversal amount: {Utility.currency_formatter(last_reversal_transaction_amount)}\n"
            response += f"Last funding amount: {Utility.currency_formatter(last_funding_transaction_amount)}\n"

            menu_options = response

            payload["ussd_string"] = menu_options
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text == "8":
            if BlackListed().can_withdraw(phone=phone_number):
                menu_options = "Sorry you are not allowed to withdraw at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                menu_options = "Sorry, payout is not available at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            if user_wallet.withdrawable_available_balance == 0:
                menu_options = "You have no available balance to withdraw"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            menu_options = f"Withdrawal account balance is {Utility.currency_formatter(user_wallet.withdrawable_available_balance)}.\nEnter amount to withdraw:\n"  # noqa

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("8*") and len(splited_text) == 2:
            if BlackListed().can_withdraw(phone=phone_number):
                menu_options = "Sorry you are not allowed to withdraw at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                menu_options = "Sorry, payout is not available at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            if user_wallet.withdrawable_available_balance == 0:
                menu_options = "You have no available balance to withdrawt"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            amount_entered = splited_text[1]

            if not amount_entered.isdigit():
                menu_options = "Invalid amount entered"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if int(amount_entered) > user_wallet.withdrawable_available_balance:
                menu_options = "Insufficient balance"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if float(amount_entered) < 100:
                menu_options = "Minimum amount to withdraw is 100"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # check if we've enough money for payout
            if has_enough_money_to_giveout(phone, amount_entered) is False:
                menu_options = " We're sorry, an error occured while processing your request. Please try again later"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            response = select_bank()
            menu_options = str(response).replace("CON", "")

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("8") and len(splited_text) == 3:
            if BlackListed().can_withdraw(phone=phone_number):
                menu_options = "Sorry you are not allowed to withdraw at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                menu_options = "Sorry, payout is not available at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            response = "Enter Account Number"

            menu_options = response

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("8") and len(splited_text) == 4:
            if BlackListed().can_withdraw(phone=phone_number):
                menu_options = "Sorry you are not allowed to withdraw at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                menu_options = "Sorry, payout is not available at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            if user_wallet.withdrawable_available_balance < 100:
                menu_options = "You do not have enough balance to withdraw"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            account_number = splited_text[3]
            # account = RedBiller.get_account_info(
            #     account_no=account_number, bank_code="000013"
            # )

            bank = select_bank(splited_text[2])
            bank_db = BankManger().bank_details(bank_name=bank)

            if not bank_db:
                menu_options = "Invalid bank"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            bank_code = bank_db.get("cbn_code")

            # paystack account verification
            bank_details = fetch_account_name(account_number, bank_code)

            if bank_details.get("status") is not True:
                menu_options = "Invalid account details"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # # print("paystack account verification", bank_details)

            user_profile.account_name = bank_details.get("data").get("account_name")
            user_profile.account_num = account_number
            user_profile.account_name = bank_details.get("data").get("account_name")
            user_profile.save()

            response = f"""confirm:
            Bank: {bank_db.get("name")}
            A/No: {account_number}
            Name: {bank_details.get("data").get("account_name")}
            1.Confirm
            2.Exit
            """

            menu_options = response

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("8") and len(splited_text) == 5:
            if BlackListed().can_withdraw(phone=phone_number):
                menu_options = "Sorry you are not allowed to withdraw at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            if has_enough_money_to_giveout(phone, float(splited_text[1])) is False:
                menu_options = "We're sorry, an error occured while processing your request. Please try again later"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                menu_options = "Sorry, payout is not available at the moment"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            bank = select_bank(splited_text[2])
            bank_db = BankManger().bank_details(bank_name=bank)

            account_number = splited_text[3]

            if splited_text[-1] == "1":
                ussd_payout.delay(user_profile.id, splited_text[1], bank, account_number)

                menu_options = "Your withdrawal request has been received"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                celery_update_teclo_users_winning_withdrawal.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "phone_number": phone_number,
                    },
                )

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            elif splited_text[-1] == "2":
                menu_options = "Withdrawal request cancelled"
                payload["ussd_string"] = menu_options
                payload["ussd_op_type"] = "4"
                payload["msg_type"] = "2"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        elif text.startswith("8") and len(splited_text) > 5:
            menu_options = "Invalid input"
            payload["ussd_string"] = menu_options
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # :::::::::::::::::::::::::::::::::: END ACCOUNT

        # :::::::::::::::::::::::::::::::::: CHECK GAME RESULT
        elif text == "9":
            response = "Enter your game play id to check result\n"

            menu_options = response

            payload["ussd_string"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("9*") and len(splited_text) == 2:
            game_play_id = splited_text[-1]
            response = lottery_ticket_result(game_play_id)

            menu_options = str(response).replace("END", "")

            payload["ussd_string"] = menu_options
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        else:
            menu_options = "Invalid option selected"
            payload["ussd_string"] = menu_options
            payload["ussd_op_type"] = "4"
            payload["msg_type"] = "2"

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

    # telco_aggregator.notify_ussd_reception_response(**payload)
    telco_aggregator.send_ussd_message(**payload)
    if settings.DEBUG is True:
        celery_telco_ussd_backgroud.delay(**payload)
    else:
        celery_telco_ussd_backgroud.apply_async(queue="telcocharge", kwargs=payload)

    return telco_aggregator.ussd_response(send=False)
