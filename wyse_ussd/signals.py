import json

from django.db.models.signals import post_save
from django.dispatch import receiver

from main.helpers.redis_storage import RedisStorage
from wyse_ussd.enums import PurposeChoices
from wyse_ussd.models import (
    PendingAsyncTask,
    SecureDDataDump,
    SecureDTransaction,
    TelcoConstant,
    TelcoDataSync,
    NitroSwitchDataSync,
    PendingNitroswitchAsyncTask,
)


@receiver(post_save, sender=TelcoConstant)
def calculate_total_accumulated_on_batch(sender, instance: TelcoConstant, created, **kwargs):
    redis_storage = RedisStorage("get_collect_bank_details")
    redis_storage.set_data(str(instance.collect_bank_details))

    redis_storage = RedisStorage("get_sms_telco_bank_details_message")
    redis_storage.set_data(str(instance.sms_bank_details_message))


@receiver(post_save, sender=SecureDTransaction)
def send_angel_media_postback(sender, instance, created, **kwargs):
    from ads_tracker.tasks import run_send_ads_traffic_postback

    """
    This signal checks that the trxId is from AngelMedia.
    If trxId/ClickId is from AngelMedia, a conversion record is created.
    """
    if not created:
        if instance.is_successful:
            run_send_ads_traffic_postback.apply_async(
                kwargs={
                    "reference": instance.reference,
                },
                queue="celery_ads_postback",
            )


@receiver(post_save, sender=TelcoDataSync)
def update_morbid_renewal_counts(sender, instance, created, **kwargs):
    PendingAsyncTask.objects.create(
        purpose=PurposeChoices.MORBID_RENEWALS,
        # payload=instance.serilaized_data,
        telco_datasync_instance_id=instance.id,
    )
    #     {
    #         # "content_type": instance,
    #         "purpose": PurposeChoices.MORBID_RENEWALS,
    #         "payload": instance.serilaized_data,
    #         "is_a_new_subscription": False,
    #         "is_treated": False
    #     }
    # )


#     try:
#         data = json.loads(serialized_data)

#         update_desc = (
#             data.get("soapenv:Envelope", {}
#                 ).get("soapenv:Body", {}
#                 ).get("ns2:syncOrderRelation").get("ns2:updateDesc"
#                 ))

#         phone_number = (
#             data.get("soapenv:Envelope", {}
#                 ).get("soapenv:Body", {}
#                 ).get("ns2:syncOrderRelation").get("ns2:userID", {}
#                 ).get("ID"))

#         upselling_instance = WinwiseUpselling.objects.filter(phone_number=phone_number).last()
#         if upselling_instance:
#             upselling_game_type = upselling_instance.game_type
#             amount_paid = get_lotto_amount_paid_for_subscriber(
#                 phone_number=phone_number, game_type=upselling_game_type
#                 )
#             renewals_count = get_lotto_subscribers_phone_list(
#                 phone_number=phone_number, game_type=upselling_game_type
#                 )
#             upselling_instance.amount_paid = amount_paid
#             upselling_instance.number_of_renewals = len(renewals_count)
#             if renewals_count:
#                 upselling_instance.converted = True
#             upselling_instance.save()

#         update_single_tracker_renewals_count(
#             phone_number=phone_number
#         )

#         if isinstance(update_desc, str) and update_desc.lower() == "deletion":
#             Unsubscription.create_unsubscription(phone_number=phone_number)
#     except Exception as e:
#         pass

#     return "DONE!!!"


@receiver(post_save, sender=SecureDDataDump)
def send_marketing_partners_postback(sender, instance, created, **kwargs):
    pass

    from ads_tracker.tasks import run_send_ads_traffic_postback
    from prices.game_price import secure_d_and_upstream_service_and_prodct_details

    # import re
    """
    This signal checks that the trxId is from AngelMedia.
    If trxId/ClickId is from AngelMedia, a conversion record is created.
    """
    if created:
        try:
            com_data = instance.data
            data = json.loads(com_data)

            trans_ref = data.get("trxId")
            phone_number = data.get("msisdn")
            data.get("activation")
            description = data.get("description")
            productID = data.get("productID")
            get_game_details_object = secure_d_and_upstream_service_and_prodct_details(product_id=productID)
            subscription_amount = get_game_details_object.get("amount", 0.00)
            game_type = get_game_details_object.get("product_name")

            data = {
                "phone_number": phone_number,
                "reference": trans_ref,
                "subscription_amount": subscription_amount,
                "game_type": game_type,
                "network_provider": instance.source,
            }

            if description.lower() == "success":
                run_send_ads_traffic_postback.apply_async(
                    kwargs={
                        "reference": trans_ref,
                        "data_dumps": data,
                    },
                    queue="celery_ads_postback",
                )
        except Exception as e:
            return f"An exception occurred: {str(e)}"
        return "Done"

@receiver(post_save, sender=NitroSwitchDataSync)
def update_nitroswitch_renewal_counts(sender, instance, created, **kwargs):
    if created:
        PendingNitroswitchAsyncTask.objects.get_or_create(
            purpose=PurposeChoices.MORBID_RENEWALS,
            telco_datasync_instance_id=instance.id,
        )