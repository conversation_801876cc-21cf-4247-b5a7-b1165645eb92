from django.conf import settings
from django.http import HttpResponse
from rest_framework import status
from rest_framework.response import Response

from account.models import BlackListed


def coral_pay_auth_decorator():
    """
    Decorator for CoralPay authentication
    """

    def decorator(func):
        def wrapper(request, *args, **kwargs):
            # print("request.headers", request.headers, "\n\n\n\n\n")

            # hash_string = (
            #     f"{settings.CORAL_PAY_AUTH_USERNAME}{settings.CORAL_PAY_AUTH_PASSWORD}"
            # )

            # password = sha256(hash_string.encode()).hexdigest()

            # print("password", password, "\n\n\n\n\n")

            header = request.headers.get("Authorization")
            if header is None:
                return Response(
                    {"message": "Authorization header is missing"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            header_strip = header.strip()
            print("header_strip", header_strip)

            split_header = [x.strip() for x in header_strip.split("|")]
            # result ['Basic', 'Username:joe Password:password']
            print("split_header", split_header)

            header_username = (((split_header[-1].split(" "))[0]).split(":")[-1]).strip()
            header_password = (((split_header[-1].split(" "))[1]).split(":")[-1]).strip()

            if header_username.casefold() != settings.CORAL_PAY_AUTH_USERNAME.casefold():
                return Response(
                    {"message": "Invalid token"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            elif header_password.casefold() != settings.CORAL_PAY_AUTH_PASSWORD.casefold():
                return Response(
                    {"message": "Invalid token"},
                    status=status.HTTP_401_UNAUTHORIZED,
                )

            return func(request, *args, **kwargs)

        return wrapper

    return decorator


def validate_blacklist(view_func):
    def wrapper(request, *args, **kwargs):
        request_body = dict(request.POST.items())
        phone_number = request_body["phoneNumber"]

        if BlackListed().is_blacklisted(phone_number) is True:
            return HttpResponse("END You are not allowed to access this service")

        return view_func(request, *args, **kwargs)

    return wrapper
