import random

from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from broad_base_communication.bbc_helper import BBCTelcoAggregator
from main.models import LotteryModel
from prices.game_price import secure_d_and_upstream_service_and_prodct_details
from wyse_ussd.models import TelcoGameWebSubscribers
from wyse_ussd.tasks import celery_deactivate_telco_subscription


class MtnWebGetGamesApiView(APIView):
    authentication_classes = []

    @method_decorator(csrf_exempt)
    def get(self, request):
        game_details = [
            {
                "game_type": "salary_for_life",
                "data": [
                    {
                        "subscription_type": "daily",
                        "price": 75,
                        "game_type": "salary_for_life",
                        "product_id": "23410220000027465",
                        "service_id": "234102200006962",
                    },
                    {
                        "subscription_type": "on-demand",
                        "price": 200,
                        "game_type": "salary_for_life",
                        "product_id": "23410220000024648",
                        "service_id": "234102200006770",
                    },
                ],
            },
            {
                "game_type": "instant_cashout",
                "data": [
                    {
                        "subscription_type": "daily",
                        "price": 75,
                        "game_type": "instant_cashout",
                        "product_id": "23410220000027463",
                        "service_id": "234102200006961",
                    },
                    {
                        "subscription_type": "on-demand",
                        "price": 150,
                        "game_type": "instant_cashout",
                        "product_id": "23410220000024635",
                        "service_id": "234102200006767",
                    },
                ],
            },
            {
                "game_type": "fast_fingers",
                "data": [
                    {
                        "subscription_type": "daily",
                        "price": 75,
                        "game_type": "fast_fingers",
                        "product_id": "23410220000027467",
                        "service_id": "234102200006963",
                    },
                    {
                        "subscription_type": "on-demand",
                        "price": 75,
                        "game_type": "fast",
                        "product_id": "23410220000027467",
                        "service_id": "234102200006963",
                    },
                ],
            },
        ]

        return Response(game_details, status=status.HTTP_200_OK)

    @method_decorator(csrf_exempt)
    def post(self, request):
        product_id = request.data.get("product_id")
        service_id = request.data.get("service_id")
        phone = request.data.get("phone")

        if not product_id:
            return Response({"error": "product_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        if not service_id:
            return Response({"error": "service_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        if not phone:
            return Response({"error": "phone is required"}, status=status.HTTP_400_BAD_REQUEST)

        # make a request to the telco aggregator to subscribe the user to the game

        get_game_details_object = secure_d_and_upstream_service_and_prodct_details(product_id=product_id)

        if not get_game_details_object:
            return Response({"error": "Invalid product id"}, status=status.HTTP_400_BAD_REQUEST)

        formatted_phone_number = LotteryModel.format_number_from_back_add_234(phone)

        generated_random_id = random.randint(100000, 999999)

        telco_charge_payload = {
            "phone_number": formatted_phone_number,
            "description": f"{str(get_game_details_object.get('product_name')).replace('_', ' ')} lottery payment",
            "amount": get_game_details_object.get("amount"),
            "game_play_id": generated_random_id,
            "lottery_type": get_game_details_object.get("product_name"),
            "pontential_winning": 5000,
            "service_id": service_id,
            "product_id": product_id,
        }

        telco_charge_payload["use_json_format"] = True
        telco_charge_payload["channel"] = "USSD"

        BBCTelcoAggregator().telco_airtime_subscription_activation(**telco_charge_payload)

        try:
            TelcoGameWebSubscribers.create_subscriber(
                phone_number=formatted_phone_number,
                product_id=product_id,
                service_id=service_id,
                network_provider="GLO",
                game_type=get_game_details_object.get("product_name"),
            )
        except Exception:
            pass

        return Response(
            {"message": "Game subscription sent. You'll get authorisation notification shortly"},
            status=status.HTTP_200_OK,
        )


class UnSubscribeRequestView(APIView):
    authentication_classes = []

    @method_decorator(csrf_exempt)
    def post(self, request):
        product_id = request.data.get("product_id")
        phone = request.data.get("phone")

        if not product_id:
            return Response({"error": "product_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        if not phone:
            return Response({"error": "phone is required"}, status=status.HTTP_400_BAD_REQUEST)

        phone_number = LotteryModel.format_number_from_back_add_234(phone)

        # unsubscribe_payload = {
        #     "phone_number": phone_number,
        #     "product_id": product_id,
        # }

        celery_deactivate_telco_subscription(phone_number=phone_number, product_id=product_id)

        return Response(
            {"message": "Unsubscription request sent."},
            status=status.HTTP_200_OK,
        )
