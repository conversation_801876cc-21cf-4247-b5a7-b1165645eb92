from dataclasses import dataclass
from time import sleep

from main.helpers.hops_restriction_helper import HopsRestrictor
from main.helpers.redis_storage import RedisStorage
from main.models import LotteryModel, LottoTicket, UserProfile
from main.tasks import create_collection_details_for_lottery_player
from main.ussd.helpers import Utility
from wyse_ussd.helper.general_helper import (
    RedBiller,
    no_option,
    select_bank,
    summary,
    text_handler,
    update_user_bank_details,
    ussd_lottery_play,
    yes_option,
)
from wyse_ussd.helper.instant_cash import instant_cash_item
from wyse_ussd.helper.mega_cash import mega_cash_item
from wyse_ussd.helper.salary_for_life import (
    salary_for_life_item,
    salary_for_life_user_pick_validator,
)
from wyse_ussd.helper.soccer_cash import SoccerFixtures, soccer_cash_item
from wyse_ussd.models import (
    SoccerPrediction,
    create_wyse_lotto_ticket,
    create_wyse_ussd_user,
)
from wyse_ussd.tasks import create_ussd_virtual_account


@dataclass
class WhisperWyseUssd:
    @staticmethod
    def options(text, phone_number, session_id):
        restrictor = HopsRestrictor.is_restricted(phone_number)

        if restrictor.get("response"):
            if restrictor.get("reason") == "OPPS":
                return "END Please you are moving too fast\ntry again in 30 mins"

            elif restrictor.get("reason") == "PENDING_PAYMENT":
                return summary(phone_number)

        HopsRestrictor.increment_hops(phone_number)

        text = text_handler(text)
        splited_text = text.split("*")
        splited_text = [i for i in splited_text if i != ""]

        # print("PHONE NUM @ MAIN SECTION", phone_number)
        # print("\n")
        # print(HopsRestrictor.is_restricted(phone_number))
        # print("\n")

        print(
            "text",
            "-----",
            text,
            "TEXT LENGTH",
            "---------",
            len(text),
            splited_text,
            len(splited_text),
        )

        if text == "":  # initial menu list
            response = "CON Welcome to WinWise:\nWin cash prizes up to 10m daily.\n\n"
            response += "1.Win Cash 4 life \n"
            response += "2.Win Quick Cash \n"
            response += "3.Win Mega Cash \n"
            response += "4.Win Soccer Cash \n"
            response += "5.Recent Results \n"
            response += "6.About \n"
            response += "7.My Account \n"
            response += "8.Payout \n"

            # select and create first time user
            create_wyse_ussd_user(phone_number=phone_number)

        # SALARY FOR LIFE
        elif text == "1":
            response = salary_for_life_item[text]

        elif text == "1*1":  # Automatic Entry
            response = salary_for_life_item[text]

        elif text.startswith("1*1*") and (len(text) == 5):
            response = salary_for_life_item[text]

        elif text.startswith("1*1*") and (len(splited_text) == 4):
            # response = salary_for_life_item["select_bank"]

            response = salary_for_life_user_pick_validator(text)

        elif text.startswith("1*1*") and (len(splited_text) == 5 or not splited_text[-1].isdigit()):  # SUMMARY
            jackpot = splited_text[3]  # get selected jackpot
            bank = select_bank(splited_text[4])  # fetch bank

            # if user select others bank
            print(bank, "BANK---")
            if "Enter Bank Name" in bank:
                return bank

            if "END" in bank:
                return bank

            # update user bank
            print("--BANK---", bank, "BANK---")
            update_user_bank_details(phone_number=phone_number, bank_name=bank)

            # num_lines = lines(splited_text[-2])
            num_lines = splited_text[3]
            # print("num_lines", num_lines, "\n\n\n")
            lotto = create_wyse_lotto_ticket(
                phone_number=phone_number,
                lottery_type="SALARY_FOR_LIFE",
                jackpot=jackpot,
                bank=bank,
                auto=True,
            )

            # user profile
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            # get last played lottery
            lottery_instance = LottoTicket.objects.filter(user_profile=user_profile, lottery_type="SALARY_FOR_LIFE").last()

            sleep(1)

            # create ussd collection details
            # create_collection_details_for_lottery_player.delay(
            #     phone,
            #     lottery_instance.stake_amount * lottery_instance.number_of_ticket,
            #     user_profile.bank_code,
            #     lottery_instance.potential_winning,
            #     "SALARY_FOR_LIFE",
            #     lottery_instance.game_play_id,
            # )

            create_collection_details_for_lottery_player.apply_async(
                queue="celery1",
                args=[
                    phone,
                    lotto.stake_amount * lotto.number_of_ticket,
                    user_profile.bank_code,
                    lotto.potential_winning,
                    "SALARY_FOR_LIFE",
                    lotto.game_play_id,
                ],
            )

            response = salary_for_life_item["summary"].format(
                num_lines,
                Utility.currency_formatter(lotto.stake_amount * int(num_lines)),
                lotto.ticket,
                Utility.currency_formatter(lotto.potential_winning),
                # lotto.game_play_id
            )

        elif text.startswith("1*1*") and len(splited_text) >= 6:
            last_screen_value = splited_text[-1]

            if last_screen_value == "1":
                response = yes_option

            elif last_screen_value == "2":
                response = no_option

        # elif text.startswith("2*1*") and len(splited_text) == 6:

        #     last_screen_value = splited_text[-1]

        #     if last_screen_value == "1":
        #         response = yes_option

        #         # user profile
        #         phone = LotteryModel.format_number_from_back_add_234(phone_number)
        #         user_profile = UserProfile.objects.filter(phone_number=phone).last()

        #         # get last played lottery
        #         lottery_instance = LottoTicket.objects.filter(
        #             user_profile=user_profile, lottery_type="SALARY_FOR_LIFE"
        #         ).last()

        #         sleep(1)

        #         # create ussd collection details
        #         create_collection_details_for_lottery_player.delay(
        #             phone=phone,
        #             amount=lottery_instance.stake_amount
        #             * lottery_instance.number_of_ticket,
        #             bank_code=user_profile.bank_code,
        #             stake=lottery_instance.potential_winning,
        #             game_play_id=lottery_instance.game_play_id,
        #             lottery_type="SALARY_FOR_LIFE",
        #         )

        #     elif last_screen_value == "2":
        #         response = no_option

        # ===========================================================================================

        elif text == "1*2":  # Number Pick
            return "END Invalid command"
            response = salary_for_life_item[text]

        elif text.startswith("1*2*") and (len(text) == 5):
            response = salary_for_life_item[text]

        elif text.startswith("1*2*") and (len(splited_text) == 4):  # user input
            response = salary_for_life_item["get_user_input"]

        elif text.startswith("1*2*") and len(splited_text) == 5 and len(splited_text[4].split()) == 5:  # select bank
            response = salary_for_life_item["select_bank"]
        elif text.startswith("1*2*") and len(splited_text) == 5 and len(splited_text[4].split()) < 5:
            response = """CON Please input at least any 
                5 numbers between 1 and 49 
                separated by space (eg: 1 12 9 44 18)\n0.back"""  # noqa

        elif text.startswith("1*2*") and len(splited_text) == 5 and len(splited_text[4].split()) > 5:
            response = """CON Please input at most any 
                5 numbers between 1 and 49
                separated by space (eg: 1 12 9 44 18)\n0.back"""  # noqa

        elif text.startswith("1*2*") and (len(splited_text) == 6 or not splited_text[-1].isdigit()):  # SUMMARY
            jackpot = splited_text[3]  # get selected jackpot
            print("splited_text for number pick", splited_text, "\n\n\n\n")
            bank = select_bank(splited_text[3])  # fetch bank

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            # create virtual account for user
            create_ussd_virtual_account.apply_async(queue="celery1", args=[phone])

            # if user select others bank
            if "Enter Bank Name" in bank:
                return bank

            if "END" in bank:
                return bank

            # update user bank
            update_user_bank_details(phone_number=phone_number, bank_name=bank)

            player_input = splited_text[4].split()
            user_input = ",".join(player_input)

            # check if user input is between 1 and 49
            if not all(1 <= int(i) <= 49 for i in player_input):
                response = "END Inavlid input\n"
                response += "Please input any 5 numbers between 1 and 49 "
                response += "separated by space (eg: 1 12 9 44 18)"

            # check len of user input
            elif len(player_input) < 5 or len(player_input) > 5:
                response = "END Inavlid input\n"
                response += "Please input any 5 numbers between 1 and 49 "
                response += "separated by space (eg: 1 12 9 44 18)"

            else:
                # print(user_input)
                lotto = create_wyse_lotto_ticket(
                    phone_number=phone_number,
                    lottery_type="SALARY_FOR_LIFE",
                    jackpot=jackpot,
                    bank=bank,
                    player_input=str(user_input),
                    auto=False,
                )

                sleep(1)

                # user profile
                phone = LotteryModel.format_number_from_back_add_234(phone_number)
                user_profile = UserProfile.objects.filter(phone_number=phone).last()

                # create ussd collection details
                create_collection_details_for_lottery_player.apply_async(
                    queue="celery1",
                    args=[
                        phone,
                        lotto.stake_amount * lotto.number_of_ticket,
                        user_profile.bank_code,
                        lotto.potential_winning,
                        "SALARY_FOR_LIFE",
                        lotto.game_play_id,
                    ],
                )

                num_lines = splited_text[3]
                response = salary_for_life_item["summary"].format(
                    num_lines,
                    Utility.currency_formatter(lotto.stake_amount * int(num_lines)),
                    lotto.ticket,
                    Utility.currency_formatter(lotto.potential_winning),
                    # lotto.game_play_id
                )

        elif text.startswith("1*2*") and len(splited_text) == 7:
            option = splited_text[-1]
            if option == "1":
                response = yes_option

                # user profile
                phone = LotteryModel.format_number_from_back_add_234(phone_number)
                user_profile = UserProfile.objects.filter(phone_number=phone).last()

                # get last played lottery
                lottery_instance = LottoTicket.objects.filter(user_profile=user_profile, lottery_type="SALARY_FOR_LIFE").last()

            elif option == "2":
                response = no_option

        # INSTANT CASH
        elif text == "2":
            response = instant_cash_item[text]

        elif text == "2*1":  # Automatic Entry
            response = instant_cash_item[text]

        elif text.startswith("2*1*") and (len(text) == 5):
            response = instant_cash_item[text]

        elif text.startswith("2*1*") and (len(text) == 7):
            response = instant_cash_item["select_bank"]

        elif text.startswith("2*1*") and (len(splited_text) == 5 or not splited_text[-1].isdigit()):  # SUMMARY
            jackpot = splited_text[3]  # get selected jackpot
            bank = select_bank(splited_text[4])  # fetch bank

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            # create virtual account for user
            create_ussd_virtual_account.apply_async(queue="celery1", args=[phone])

            # if user select others bank
            if "Enter Bank Name" in bank:
                return bank

            if "END" in bank:
                return bank

            # update user bank
            update_user_bank_details(phone_number=phone_number, bank_name=bank)

            lotto = create_wyse_lotto_ticket(
                phone_number=phone_number,
                lottery_type="INSTANT_CASHOUT",
                jackpot=jackpot,
                bank=bank,
                auto=True,
            )

            # user profile
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            # get last played lottery
            lottery_instance = LottoTicket.objects.filter(user_profile=user_profile, lottery_type="INSTANT_CASHOUT").last()

            # create ussd collection details
            create_collection_details_for_lottery_player.apply_async(
                queue="celery1",
                args=[
                    phone,
                    lotto.stake_amount * lotto.number_of_ticket,
                    user_profile.bank_code,
                    lotto.potential_winning,
                    "INSTANT_CASHOUT",
                    lotto.game_play_id,
                ],
            )

            num_lines = splited_text[3]
            # num_lines = lines(splited_text[3])

            stake_amount = lotto.stake_amount * int(splited_text[3])
            response = instant_cash_item["summary"].format(
                num_lines,
                Utility.currency_formatter(stake_amount),
                lotto.ticket,
                Utility.currency_formatter(lotto.potential_winning),
                # lotto.game_play_id
            )

        elif text.startswith("2*1*") and len(splited_text) == 6:
            last_screen_value = splited_text[5]
            # print(last_screen_value, "FINAL OPTION")
            if last_screen_value == "1":
                response = yes_option

                sleep(1)

            elif last_screen_value == "2":
                response = no_option

        # ===========================================================================================

        elif text == "2*2":  # Number Pick
            return "END Invalid command"
            response = instant_cash_item[text]

        elif text.startswith("2*2*") and (len(text) == 5):
            response = instant_cash_item[text]

        elif text.startswith("2*2*") and (len(text) == 7):  # user input
            response = instant_cash_item["get_user_input"]

        elif text.startswith("2*2*") and len(splited_text) == 5 and len(splited_text[4].split()) == 5:  # select bank
            response = instant_cash_item["select_bank"]
        elif text.startswith("2*2*") and len(splited_text) == 5 and len(splited_text[4].split()) < 5:
            response = """END Please input at least any 
                5 numbers between 1 and 49 
                separated by space (eg: 1 12 9 44 18)\n0.back"""  # noqa

        elif text.startswith("2*2*") and len(splited_text) == 5 and len(splited_text[4].split()) > 5:
            response = """END Please input at most any 
                5 numbers between 1 and 49
                separated by space (eg: 1 12 9 44 18)\n0.back"""  # noqa

        elif text.startswith("2*2*") and (len(splited_text) == 6 or not splited_text[-1].isdigit()):  # SUMMARY
            jackpot = splited_text[3]  # get selected jackpot
            bank = select_bank(splited_text[-1])  # fetch bank

            # if user select others bank
            if "Enter Bank Name" in bank:
                return bank

            if "END" in bank:
                return bank

            # update user bank
            update_user_bank_details(phone_number=phone_number, bank_name=bank)

            player_input = splited_text[4].split()
            user_input = ",".join(player_input)
            # print(user_input)
            lotto = create_wyse_lotto_ticket(
                phone_number=phone_number,
                lottery_type="INSTANT_CASHOUT",
                jackpot=jackpot,
                bank=bank,
                player_input=str(user_input),
                auto=False,
            )

            # create ussd collection details
            create_collection_details_for_lottery_player.apply_async(
                queue="celery1",
                args=[
                    phone,
                    lotto.stake_amount * lotto.number_of_ticket,
                    user_profile.bank_code,
                    lotto.potential_winning,
                    "INSTANT_CASHOUT",
                    lotto.game_play_id,
                ],
            )

            response = instant_cash_item["summary"].format(
                lotto.number_of_ticket,
                Utility.currency_formatter(lotto.stake_amount),
                lotto.ticket,
                Utility.currency_formatter(lotto.potential_winning),
                # lotto.game_play_id
            )

        elif text.startswith("2*2*") and len(splited_text) == 7:
            option = splited_text[-1]
            if option == "1":
                response = yes_option

                # user profile
                phone = LotteryModel.format_number_from_back_add_234(phone_number)
                user_profile = UserProfile.objects.filter(phone_number=phone).last()

                # get last played lottery
                lottery_instance = LottoTicket.objects.filter(user_profile=user_profile, lottery_type="INSTANT_CASHOUT").last()

            elif option == "2":
                response = no_option

        #
        # MEGA CASH
        elif text == "3":
            response = mega_cash_item[text]
        elif text == "3*1":
            response = mega_cash_item[text]
        elif text == "3*2":
            response = no_option
        elif text.startswith("3*1*") and (len(text) == 5):
            response = mega_cash_item[text]

        elif text.startswith("3*1*") and (len(text) == 7):
            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            # create lottery ticket
            ussd_lottery_play(phone_number=phone_number, lottery_type="wyse_cash", text=text)

            sleep(1)

            # create virtual account for user
            create_ussd_virtual_account.apply_async(queue="celery1", args=[phone])

            # user profile

            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            # get last played lottery
            lottery_instance = LotteryModel.objects.filter(user_profile=user_profile).last()

            # create ussd collection details
            create_collection_details_for_lottery_player.apply_async(
                queue="celery1",
                args=[
                    phone,
                    lottery_instance.stake_amount * lottery_instance.instance_number,
                    user_profile.bank_code,
                    lottery_instance.band,
                    "WYSE_CASH",
                    lottery_instance.game_play_id,
                ],
            )

            response = mega_cash_item["select_bank"]

        elif text.startswith("3*1*") and ((len(text) == 9) or (len(text) == 10) or not splited_text[-1].isdigit()):
            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            print(phone, "PHONE NUMBER")

            # User profile
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            if user_profile is None:
                response = "END Ther's issue with your dailed process, please try again later"

                return response

            # get selected bank number
            selected_bank = select_bank(splited_text[-1])

            # if user select others bank
            if "Enter Bank Name" in selected_bank:
                return selected_bank

            if "END" in selected_bank:
                return selected_bank

            sleep(1)
            # update user bank
            update_user_bank_details(phone_number=phone_number, bank_name=selected_bank)

            try:
                # game_play_id.decode("utf-8")

                # get band from redis
                redis_storage = RedisStorage(f"{phone}-lotteryplay-band")
                band = (redis_storage.get_data()).decode("utf-8")

                # get nnumber of ticket from redis
                redis_storage = RedisStorage(f"{phone}-lotteryplay-numberofticket")
                number_of_ticket = (redis_storage.get_data()).decode("utf-8")

                # get stake amount from redis
                redis_storage = RedisStorage(f"{phone}-lotteryplay-stakeamount")
                stake_amount = (redis_storage.get_data()).decode("utf-8")

                response = mega_cash_item["summary"].format(
                    Utility.currency_formatter(int(band)),
                    number_of_ticket,
                    Utility.currency_formatter(int(stake_amount)),
                )

                return response

            except Exception:
                lottery_instance = LotteryModel.objects.filter(user_profile=user_profile).last()

                response = mega_cash_item["summary"].format(
                    lottery_instance.instance_number,
                    Utility.currency_formatter(lottery_instance.stake_amount * lottery_instance.instance_number),
                    Utility.currency_formatter(int(lottery_instance.band) * lottery_instance.instance_number),
                )

                return response

        elif text.startswith("3*1*") and ((len(text) == 11) or (len(text) >= 12)):
            print("I AM HERE 4")
            option = text[-1]
            if option == "1":
                response = yes_option

            elif option == "2":
                response = no_option

            return response

        # Soccer Cash
        elif text == "4":
            response = SoccerFixtures.welcome_screen()

        elif text.startswith("4") and (len(splited_text) == 2):
            if isinstance(SoccerFixtures.select_league(splited_text[1]), dict):
                display_message = SoccerFixtures.select_league(splited_text[1]).get("display_msg")
            else:
                display_message = SoccerFixtures.select_league(splited_text[1])
            response = display_message

        elif text.startswith("4") and (len(splited_text) == 3):  # score entery
            response = SoccerFixtures.score_entery(splited_text[1], splited_text[2])

        elif text.startswith("4") and (len(splited_text) == 4):  # confirm prediction
            prediction = "-".join(splited_text[3].split(" "))
            if len(prediction) >= 3:
                response = SoccerFixtures.confirm_entery_band_options(splited_text[1], splited_text[2], prediction)
            else:
                response = "END Please ensure prediction scores is with a space e.g (4 3)"

        elif text.startswith("4") and (len(splited_text) == 5):  # select bank
            response = soccer_cash_item["select_bank"]

        elif text.startswith("4") and ((len(splited_text) == 6) or not splited_text[-1].isdigit()):
            bank = select_bank(splited_text[-1])  # fetch bank
            # print(bank)
            # if user select others bank
            if "Enter Bank Name" in bank:
                return bank

            if "END" in bank:
                return bank
            # print(phone_number, bank)

            event_properties = SoccerFixtures.fixtures_properties(splited_text[1], splited_text[2])
            soccer_prediction_ins = SoccerPrediction.create_leauge_prediction_object(
                desired_play=splited_text[4],
                prediction=splited_text[3].split("-"),
                phone_no=phone_number,
                fixtures_id=event_properties[-1],
                bank=bank,
            )

            if soccer_prediction_ins is None:
                response = "END Invalid prediction"
                return response

            response = soccer_cash_item["summary"].format(
                event_properties[0],
                event_properties[1],
                "-".join(splited_text[3].split(" ")),
                soccer_prediction_ins.stake_amount,
                soccer_prediction_ins.potential_winning,
            )

        elif text.startswith("4") and ((len(splited_text) == 7)):
            if int(splited_text[-1]) == 1:
                phone_no = LotteryModel.format_number_from_back_add_234(phone_number)
                prediction_instance = SoccerPrediction.objects.filter(phone=phone_no).last()
                print(prediction_instance, ":::::::::::::::::::::")

                create_collection_details_for_lottery_player.apply_async(
                    queue="celery1",
                    args=[
                        prediction_instance.phone,
                        prediction_instance.stake_amount,
                        prediction_instance.bank_code,
                        prediction_instance.potential_winning,
                        "SOCCER_CASH",
                        prediction_instance.game_id,
                    ],
                )

                response = """END We have received your game prediction. 
                            Payment link will be sent to you via SMS, 
                            Thank you for choosing WinWise."""  # noqa

            if int(splited_text[-1]) == 2:
                response = no_option

        #
        # recent result
        elif text == "5":
            response = "END Result will be compiled and sent to you shortly"

        # About
        elif text == "6":
            response = """CON Whisper Wyse is a mobile gaming, interactive lottery, fun and entertainment plus welfare
                hub that provides participants chances to win exciting prizes including cash prizes, monthly
                salary, gift items etc.\n0.back"""

        #
        # account
        elif text == "7":
            response = "END This service is not available at the moment"

        elif text == "8":
            response = select_bank()

        elif text.startswith("8") and len(splited_text) == 2:
            response = "CON Enter Account Number"

        elif text.startswith("8") and len(splited_text) == 3:
            account_number = splited_text[2]
            account = RedBiller.get_account_info(account_no=account_number, bank_code="000013")
            if isinstance(account, dict):
                account["date_of_birth"]

                # redis.set_data(data=age(date_of_birth))

                response = f"""CON confirm account number:
                Bank name: {account["bank_name"]}
                Account no: {account["account_no"]}
                Account name: {account["account_name"]}
                1.Confirm
                2.Exit
                """

                return response

        elif text.startswith("8") and len(splited_text) == 4:
            # print(float(redis.get_data()))
            response = "END Congratulations your loan will bw disbursed shortly"

        return response
