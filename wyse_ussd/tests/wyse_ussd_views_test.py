import json

from django.conf import settings
from django.http import HttpResponse
from django.test import Client, TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from main.models import UserProfile
from wyse_ussd.models import CoralpayTransactions, CoralpayUserCode


class WyseUssdViewTests(TestCase):
    def setUp(self):
        self.client = Client()

    def test_wyse_ussd_view_post(self):
        url = reverse("wyse_ussd_view")
        data = {"sessionId": "123456", "phoneNumber": "2348123456789", "serviceCode": "*123#", "text": "1"}
        response = self.client.post(url, json.dumps(data), content_type="application/json")
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response, HttpResponse)

    def test_wyse_ussd_view_invalid_method(self):
        url = reverse("wyse_ussd_view")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), "Invalid request")

    def test_wyse_ussd_view_post_with_form_data(self):
        url = reverse("wyse_ussd_view")
        data = {"sessionId": "123456", "phoneNumber": "2348123456789", "serviceCode": "*123#", "text": "1"}
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response, HttpResponse)


class CoralpayPaymentInitiationTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.url = reverse("coralpay_ussd_payment")  # Update this to your actual URL

        # Create a test user
        self.user = UserProfile.objects.create(phone_number="23489887654")

        # Create a valid CoralpayUserCode instance
        self.coralpay_user_code = CoralpayUserCode.objects.create(code="TEST12345", user=self.user, has_expired=False, is_used=False)

        # Create a valid CoralpayTransactions instance
        self.transaction = CoralpayTransactions.objects.create(ussd_code_extension=self.coralpay_user_code, trace_id="TRACE123", amount=1000.50)

        self.valid_payload = {"customerRef": "TEST12345", "merchantId": settings.CORAL_PAY_MERCHANT_ID, "shortCode": "001"}

    def test_invalid_merchant_id(self):
        """Test that an invalid merchantId returns a 400 error."""
        payload = self.valid_payload.copy()
        payload["merchantId"] = "INVALID_ID"

        response = self.client.post(self.url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data["responseCode"], "400")
        self.assertEqual(response.data["message"], "Merchant ID is valid")

    def test_invalid_customer_ref(self):
        """Test that an invalid customerRef returns a 400 error."""
        payload = self.valid_payload.copy()
        payload["customerRef"] = "INVALID_REF"

        response = self.client.post(self.url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # self.assertEqual(response.data["responseCode"], "400")
        # self.assertEqual(response.data["message"], "Invalid user code")

    def test_missing_transaction(self):
        """Test that a request with no linked transaction returns a 400 error."""
        # Delete existing transaction to simulate missing transaction
        self.transaction.delete()

        response = self.client.post(self.url, self.valid_payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # self.assertEqual(response.data["responseCode"], "400")
        # self.assertEqual(response.data["message"], "Invalid user code")
