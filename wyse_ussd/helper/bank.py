from main.ussd.bankdb import bankdb


class BankManger:
    _LIST_OF_BANKS = bankdb()

    def get_banks_list(self):
        return self._LIST_OF_BANKS

    def bank_code(self, bank_name):
        for bank in self._LIST_OF_BANKS:
            if (
                (bank_name.title() in bank["name"])
                or (bank_name.upper() in bank["name"])
                or (bank_name.lower() in bank["bank_short_name"])
                or (bank_name.upper() in bank["bank_short_name"])
            ):
                return bank["bank_code"]

    def bank_details(self, bank_name):
        for bank in self._LIST_OF_BANKS:
            if (
                (bank_name.title() in bank["name"])
                or (bank_name.upper() in bank["name"])
                or (bank_name.lower() in bank["bank_short_name"])
                or (bank_name.upper() in bank["bank_short_name"])
            ):
                return bank

        for bank in self._LIST_OF_BANKS:
            if bank_name.lower() == bank["name"].lower():
                return bank

        return None

    def cbn_code(self, bank_name):
        pass

    def cbn_code_to_bank_code(self, cbn_code):
        this_bank_code = ""
        for bank in self._LIST_OF_BANKS:
            if cbn_code == bank["cbn_code"]:
                this_bank_code = bank["bank_code"]
                break
        return this_bank_code
