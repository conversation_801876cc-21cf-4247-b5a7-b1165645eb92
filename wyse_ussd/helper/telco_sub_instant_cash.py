# from wyse_ussd.helper.general_helper import BANK_NAMES
from datetime import datetime, timedelta

import pytz
from django.conf import settings

from prices.game_price import TelcoInstantCashOutPriceModel
from wyse_ussd.helper.general_helper import select_bank

# get weekday name
now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

# get current time + 5 minutes
get_current_time = ((datetime.now(tz=pytz.timezone(settings.TIME_ZONE)) + timedelta(minutes=5)).time()).strftime("%H:%M")
current_time = datetime.strptime(str(get_current_time), "%H:%M")
current_time = current_time.strftime("%I:%M %p")

# print(f'{result:%H:%M:%S}')


# summary_amount_inscashout = {
#     1: [200, 1000],
#     2: [500, 1600],
#     3: [750, 2000],
#     4: [800, 2400],
#     # 5: [1250, 2600],
#     6: [1300, 3000],
#     7: [1400, 3200],
# }

icash_price_model = TelcoInstantCashOutPriceModel().ticket_price_with_other_service_price()
_icash_price_data = {}
if isinstance(icash_price_model, dict):
    for key, value in icash_price_model.items():
        if key == 5:
            pass
        else:
            _icash_price_data[key] = [
                value["total_amount"],
                value["potential_winning"],
            ]

summary_amount_inscashout = _icash_price_data


def instant_cashout_amount(number):
    """
    This function returns the amount and return true if the tciket is on-demand or false if it is not
    """

    icash_price_model = TelcoInstantCashOutPriceModel().subscription_ticket_prices()

    data = {}

    for key, value in icash_price_model.items():
        data[str(key)] = {
            "total_amount": value["total_amount"],
            "type": value["type"],
        }

    item = data.get(number)
    if item:
        return (
            item.get("total_amount"),
            True if item.get("type") == "on-demand" else False,
        )
    else:
        return None, None


def instant_cashout_price_ticket(index_range: list):
    type = "daily"

    icash_price_model = TelcoInstantCashOutPriceModel().ussd_top_subscription_prices_in_threes(index_range=index_range)
    prices = ""
    for key, value in icash_price_model.items():
        if value.get("type") == "on-demand":
            type = "instant"
        prices += f"{key}. Win N{str(value.get('potential_winning')).replace('.0','')} {type} @N{str(value.get('ticket_price')).replace('.00', '').replace('.0', '')}{'Auto' if type == 'daily' else ''}\n"

    return prices


def icash_top_menu_price():
    # icash_price_model
    pass


instant_cash_sub_item = {
    "2": "Win Instant Cash up to N50k, select entry to qualify:\n\n"
    f"{instant_cashout_price_ticket(index_range = [1,2,3])}0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # 50, 000
    "2*1": "CON Select Chances:\n"
    f"{instant_cashout_price_ticket(index_range = [1])}0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # 500, 000
    "2*2": "CON Select Chances:\n"
    "4.N1000 to win N27,000\n"
    f"{instant_cashout_price_ticket(index_range = [2])}0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    "2*3": "CON Select Chances:\n"
    "4.N1000 to win N27,000\n"
    f"{instant_cashout_price_ticket(index_range = [3])}0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # get user input
    "get_user_input": "CON Instant Cash:\n"
    "Please input an any 5 numbers \n"
    "between 1 and 49 separated \n"
    f"by space (eg: 1 12 9 44 18)\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # select bank options
    "select_bank": select_bank(),
    #
    # summary
    "summary": "WinWise: N50K Instant Cash! Await auth request, ACCEPT to enter. \n\n"
    "Lines: {}\n"
    "Stake: {} \n"
    "No: {} \n"
    f"Draws: {week_name} {get_current_date}",
}


def instant_cashout_validator(text):
    new_text = text.split("*")

    # print("new_text", new_text, "\n\n\n")

    if new_text[-2] == "1":
        if new_text[-1] in ["1", "2", "3"]:
            return True
        else:
            return "END Invalid input. select 1, 2 or 3"

    elif new_text[-2] == "2":
        if new_text[-1] in ["4", "5", "6", "7"]:
            return True
        else:
            return "END Invalid input. select 4, 5, 6 or 7"

    # elif new_text[-2] == "3":
    #     if new_text[-1] in ["7", "8", "9", "10"]:
    #         return True
    #     else:
    #         return "END Invalid input. select 8, 9 or 10"
