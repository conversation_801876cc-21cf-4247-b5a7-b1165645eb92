# from wyse_ussd.helper.general_helper import BANK_NAMES
from datetime import datetime, timedelta

from wyse_ussd.helper.general_helper import select_bank

# get weekday name
now = datetime.now()
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

# get current time + 5 minutes
get_current_time = ((datetime.now() + timedelta(minutes=5)).time()).strftime("%H:%M")
current_time = datetime.strptime(str(get_current_time), "%H:%M")
current_time = current_time.strftime("%I:%M %p")

# print(f'{result:%H:%M:%S}')


instant_cash_item = {
    "2": "CON WinWise:\nInstant Draw\nStand a chance to win\ninstant cash, up to N50,000.\n\n"
    "1.Automatic Entry\n\n"
    f"Next Draw: {week_name} {get_current_date} {current_time}",
    # "2": "CON WinWise:\nInstant Draw\nStand a chance to win\ninstant cash, up to N50,000.\n\n"
    # "1.Automatic Entry\n"
    # f"2.Number Pick\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # Automatic Entry
    "2*1": "CON Select entry to qualify\nfor instant cash\n\n"
    "Win up to:\n"
    "1.N13,500\n"
    f"2.N25,000\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "3.Up to 10lines get N1,500,000\n",
    #
    # 50, 000
    "2*1*1": "CON Select Chances:\n"
    "1.N150 to win N11,250\n"
    "2.N300 to win N13,500\n"
    f"3.N450 to win N15,750\n0.back0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "2*1*1": "CON Select:\n"
    # "1.N150 to qualify for N11,250\n"
    # "2.N300 to qualify for N13,500\n"
    # f"3.N450 to qualify for N15,750\n0.back0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # 500, 000
    "2*1*2": "CON Select Chances:\n"
    "4.N600 to win N18,000\n"
    "5.N750 to win N18,750\n"
    "6.N900 to win N22,500\n"
    f"7.N1,000 to qualify for N25,000\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "4.N600 to qualify for N18,000\n"
    # "5.N750 to qualify for N18,750\n"
    # "6.N900 to qualify for N22,500\n"
    # f"7.N1,000 to qualify for N25,000\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # Number Pick
    "2*2": "CON Select Line entries to qualify\nfor instant cash\n\n"
    "Win up to:\n"
    "1.N13,500\n"
    f"2.N25,000\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "3.Up to 10lines get N1,500,000\n",
    #
    # 50, 000
    "2*2*1": "CON Select Chances:\n"
    "1.N150 to win N11,250\n"
    "2.N300 to win N13,500\n"
    f"3.N450 to win N15,750\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "2*2*1": "CON Select:\n"
    # "1.N150 to qualify for N11,250\n"
    # "2.N300 to qualify for N13,500\n"
    # f"3.N450 to qualify for N15,750\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # 500, 000
    "2*2*2": "CON Select Chances:\n"
    "4.N600 to win N18,000\n"
    "5.N750 to win N18,750\n"
    "6.N900 to win N22,500\n"
    f"7.N1,000 to win N25,000\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "2*2*2": "CON Select:\n"
    # "4.N600 to qualify for N18,000\n"
    # "5.N750 to qualify for N18,750\n"
    # "6.N900 to qualify for N22,500\n"
    # f"7.N1,000 to qualify for N25,000\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # get user input
    "get_user_input": "CON Instant Cash:\n"
    "Please input an any 5 numbers \n"
    "between 1 and 49 separated \n"
    f"by space (eg: 1 12 9 44 18)\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # select bank options
    "select_bank": select_bank(),
    #
    # summary
    "summary": "CON Summary: Instant cash \n"
    "Lines: {}\n"
    "Stake: {} \n"
    "Numbers: {} \n"
    "Potential Win: {} \n"
    "confirm \n"
    "1. Yes \n"
    f"2. Exit\n\nNext Draw: {week_name} {get_current_date} {current_time}",
}
