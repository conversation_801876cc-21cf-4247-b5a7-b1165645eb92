# from wyse_ussd.helper.general_helper import BANK_NAMES
from datetime import datetime, timedelta

import pytz
from django.conf import settings

from prices.game_price import TelcoInstantCashOutPriceModel
from wyse_ussd.helper.general_helper import select_bank

# get weekday name
now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

# get current time + 5 minutes
get_current_time = ((datetime.now(tz=pytz.timezone(settings.TIME_ZONE)) + timedelta(minutes=5)).time()).strftime("%H:%M")
current_time = datetime.strptime(str(get_current_time), "%H:%M")
current_time = current_time.strftime("%I:%M %p")

# print(f'{result:%H:%M:%S}')


# summary_amount_inscashout = {
#     1: [200, 1000],
#     2: [500, 1600],
#     3: [750, 2000],
#     4: [800, 2400],
#     # 5: [1250, 2600],
#     6: [1300, 3000],
#     7: [1400, 3200],
# }

icash_price_model = TelcoInstantCashOutPriceModel().ticket_price_with_other_service_price()
_icash_price_data = {}
if isinstance(icash_price_model, dict):
    for key, value in icash_price_model.items():
        if key == 5:
            pass
        else:
            _icash_price_data[key] = [
                value["total_amount"],
                value["potential_winning"],
            ]

summary_amount_inscashout = _icash_price_data


def instant_cashout_amount(number):
    # salary_for_life_amount_dict = {
    #     "1": 200,
    #     "2": 500,
    #     "3": 750,
    #     "4": 800,
    #     # "5": 1250,
    #     "6": 1300,
    #     "7": 1400,
    # }

    # return (
    #     salary_for_life_amount_dict.get(number)
    #     if salary_for_life_amount_dict.get(number) is not None
    #     else "END Invalid amount"
    # )

    icash_price_model = TelcoInstantCashOutPriceModel().ticket_price_with_other_service_price()

    data = {}

    for key, value in icash_price_model.items():
        if key == 5:
            pass
        else:
            data[str(key)] = value["total_amount"]

    return data.get(number) if data.get(number) is not None else "END Invalid amount"


def instant_cashout_price_ticket(index_range: list):
    icash_price_model = TelcoInstantCashOutPriceModel().ussd_top_prices_in_threes(index_range=index_range)
    prices = ""
    for key, value in icash_price_model.items():
        prices += f"{key}.N{value.get('total_amount')} to win N{str(value.get('potential_winning')).replace('.0','')}\n"

    return prices


def icash_top_menu_price():
    # icash_price_model
    pass


instant_cash_item = {
    # "2": "CON WinWise:\nInstant Draw\nStand a chance to win\ninstant cash, up to N50,000.\n\n"
    # "1.Automatic Entry\n\n"
    # f"Next Draw: {week_name} {get_current_date} {current_time}",
    # "2": "CON WinWise:\nInstant Draw\nStand a chance to win\ninstant cash, up to N50,000.\n\n"
    # "1.Automatic Entry\n"
    # f"2.Number Pick\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # Automatic Entry
    "2": "CON Select entry to qualify\nfor instant cash\n\n"
    # "Win up to:\n"
    f"{instant_cashout_price_ticket(index_range = [1,2,3,4])}0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "3.Up to 10lines get N1,500,000\n",
    #
    # 50, 000
    # "1*1": "CON Select Chances:\n"
    # f"{instant_cashout_price_ticket(index_range = [1,2,3])}0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "2*1*1": "CON Select:\n"
    # "1.N150 to qualify for N11,250\n"
    # "2.N300 to qualify for N13,500\n"
    # f"3.N450 to qualify for N15,750\n0.back0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # 500, 000
    # "1*2": "CON Select Chances:\n"
    # "4.N800 to win N2,400\n"
    # # "5.N1,250 to win N2,600\n"
    # "6.N1,300 to win N3,000\n"
    # f"7.N1,400 to win N3,200\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # f"{instant_cashout_price_ticket(index_range = [4])}0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "4.N600 to qualify for N18,000\n"
    # "5.N750 to qualify for N18,750\n"
    # "6.N900 to qualify for N22,500\n"
    # f"7.N1,000 to qualify for N25,000\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    #
    # 50, 000
    "2*2*1": "CON Select Chances:\n"
    f"{instant_cashout_price_ticket(index_range = [1,2,3])}0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "2*2*1": "CON Select:\n"
    # "1.N150 to qualify for N11,250\n"
    # "2.N300 to qualify for N13,500\n"
    # f"3.N450 to qualify for N15,750\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # 500, 000
    "2*2*2": "CON Select Chances:\n" "4.N1000 to win N27,000\n"
    # "5.N1,250 to win N2,600\n"
    # "6.N1,300 to win N3,000\n"
    # f"7.N1,400 to win N3,200\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "5.N1,250 to win N2,600\n"
    f"{instant_cashout_price_ticket(index_range = [4])}0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    # "2*2*2": "CON Select:\n"
    # "4.N600 to qualify for N18,000\n"
    # "5.N750 to qualify for N18,750\n"
    # "6.N900 to qualify for N22,500\n"
    # f"7.N1,000 to qualify for N25,000\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # get user input
    "get_user_input": "CON Instant Cash:\n"
    "Please input an any 5 numbers \n"
    "between 1 and 49 separated \n"
    f"by space (eg: 1 12 9 44 18)\n0.back\n\nNext Draw: {week_name} {get_current_date} {current_time}",
    #
    # select bank options
    "select_bank": select_bank(),
    #
    # summary
    # "summary": "END WinWise: Win instant cash up to N50,000.\n\n"
    # "You will receive an authorization request shortly, ACCEPT to enter the draw.\n\n"
    # "Quick cash\n"
    # "Lines: {}\n"
    # "Stake: {} \n"
    # "No: {} \n"
    # "Potential Win: {} \n"
    # f"Draws: {week_name} {get_current_date} {current_time}",
    "summary": "WinWise: N50K Instant Cash! Await auth request, ACCEPT to enter. \n\n"
    "Lines: {}\n"
    "Stake: {} \n"
    "No: {} \n"
    f"Draws: {week_name} {get_current_date}",
}


def instant_cashout_validator(text):
    new_text = text.split("*")

    # print("new_text", new_text, "\n\n\n")

    if new_text[-2] == "1":
        if new_text[-1] in ["1", "2", "3"]:
            return True
        else:
            return "END Invalid input. select 1, 2 or 3"

    elif new_text[-2] == "2":
        if new_text[-1] in ["4", "5", "6", "7"]:
            return True
        else:
            return "END Invalid input. select 4, 5, 6 or 7"

    # elif new_text[-2] == "3":
    #     if new_text[-1] in ["7", "8", "9", "10"]:
    #         return True
    #     else:
    #         return "END Invalid input. select 8, 9 or 10"
