def mega_cash_choices_pool(choice_band_int, band_amount_int):
    if choice_band_int == "1":
        BAND = "50,000"
        STAKE_AMOUNT = 100
        DEFAULT_AMOUNT = 10000
        POOL_CHOICE = "FIFTY_THOUSAND"

        if band_amount_int == "1":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": DEFAULT_AMOUNT,
            }

        elif band_amount_int == "2":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "3":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "4":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "5":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }
    #
    # 250 000 band

    elif choice_band_int == "2":
        BAND = "250,000"
        STAKE_AMOUNT = 200
        DEFAULT_AMOUNT = 50000
        POOL_CHOICE = "TWO_FIFTY_THOUSAND"

        if band_amount_int == "1":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": DEFAULT_AMOUNT,
            }

        elif band_amount_int == "2":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "3":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "4":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "5":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }
    #
    # 1,250,000 band
    elif choice_band_int == "3":
        BAND = "1,250,000"
        STAKE_AMOUNT = 500
        DEFAULT_AMOUNT = 250000
        POOL_CHOICE = "ONE_MILLION_TWO_FIFTY"

        if band_amount_int == "1":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": DEFAULT_AMOUNT,
            }

        elif band_amount_int == "2":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "3":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "4":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "5":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

    #
    # 2,250,000 band
    elif choice_band_int == "4":
        BAND = "2,250,000"
        STAKE_AMOUNT = 1000
        DEFAULT_AMOUNT = 500000
        POOL_CHOICE = "TWO_MILLION_TWO_FIFTY"

        if band_amount_int == "1":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": DEFAULT_AMOUNT,
            }

        elif band_amount_int == "2":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "3":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "4":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

        elif band_amount_int == "5":
            result = {
                "pool_choice": POOL_CHOICE,
                "band": BAND,
                "stake_amount": int(band_amount_int) * STAKE_AMOUNT,
                "default_stake_amount": STAKE_AMOUNT,
                "potential_winning": int(band_amount_int) * DEFAULT_AMOUNT,
            }

    print(result)
    return result
