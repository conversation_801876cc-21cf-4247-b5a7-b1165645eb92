from datetime import datetime, timedelta

import pytz
from django.conf import settings

from prices.game_price import WyseCashPriceModel
from wyse_ussd.helper.general_helper import select_bank

# get weekday name
now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

get_current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

# print("::::::::::::::: current_time", get_current_time.hour, "\n\n\n\n\n\n")
# check if current time has passed 8pm
if get_current_time.hour >= 20:
    now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)) + timedelta(days=1)
    week_name = str(now.strftime("%A"))[:3]
    get_current_date = now.strftime("%d/%m/%y")

wyse_cash_price_model = WyseCashPriceModel().ticket_price(channel="USSD")

wyse_cash_summary_amount = {
    # --------stake_amnt-----potential_winnings-----
    "3*1*1": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("total_winning_amount"),
    ],
    "3*1*2": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=2).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=2).get("total_winning_amount"),
    ],
    "3*1*3": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=3).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=3).get("total_winning_amount"),
    ],
    "3*1*4": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=4).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=4).get("total_winning_amount"),
    ],
    "3*1*5": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=5).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=5).get("total_winning_amount"),
    ],
    #
    "3*2*1": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=1).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=1).get("total_winning_amount"),
    ],
    "3*2*2": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=2).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=2).get("total_winning_amount"),
    ],
    "3*2*3": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=3).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=3).get("total_winning_amount"),
    ],
    "3*2*4": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=4).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=4).get("total_winning_amount"),
    ],
    "3*2*5": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=5).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=5).get("total_winning_amount"),
    ],
    #
    "3*3*1": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=1).get("total_winning_amount"),
    ],
    "3*3*2": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=2).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=2).get("total_winning_amount"),
    ],
    "3*3*3": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=3).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=3).get("total_winning_amount"),
    ],
    "3*3*4": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=4).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=4).get("total_winning_amount"),
    ],
    "3*3*5": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=5).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=5).get("total_winning_amount"),
    ],
    #
    "3*4*1": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=1).get("total_winning_amount"),
    ],
    "3*4*2": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=2).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=2).get("total_winning_amount"),
    ],
    "3*4*3": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=3).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=3).get("total_winning_amount"),
    ],
    "3*4*4": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=4).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=4).get("total_winning_amount"),
    ],
    "3*4*5": [
        WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=5).get("stake_amount"),
        WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=5).get("total_winning_amount"),
    ],
}


# for key, value in wyse_cash_price_model.items():
#     pass


def wyse_cash_amount(number):
    wyse_cash_amount_dict = {
        "3*1*1": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
        "3*1*2": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=2).get("stake_amount"),
        "3*1*3": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=3).get("stake_amount"),
        "3*1*4": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=4).get("stake_amount"),
        "3*1*5": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=5).get("stake_amount"),
        #
        "3*2*1": WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=1).get("stake_amount"),
        "3*2*2": WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=2).get("stake_amount"),
        "3*2*3": WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=3).get("stake_amount"),
        "3*2*4": WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=4).get("stake_amount"),
        "3*2*5": WyseCashPriceModel().ticket_price(channel="USSD", band=50000, no_of_line=5).get("stake_amount"),
        #
        "3*3*1": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
        "3*3*2": WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=2).get("stake_amount"),
        "3*3*3": WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=3).get("stake_amount"),
        "3*3*4": WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=4).get("stake_amount"),
        "3*3*5": WyseCashPriceModel().ticket_price(channel="USSD", band=100000, no_of_line=5).get("stake_amount"),
        #
        "3*4*1": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
        "3*4*2": WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=2).get("stake_amount"),
        "3*4*3": WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=3).get("stake_amount"),
        "3*4*4": WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=4).get("stake_amount"),
        "3*4*5": WyseCashPriceModel().ticket_price(channel="USSD", band=200000, no_of_line=5).get("stake_amount"),
    }

    return wyse_cash_amount_dict.get(number) if wyse_cash_amount_dict.get(number) is not None else 0


def telco_sub_wyse_cash_amount(number):
    wyse_cash_amount_dict = {
        "3*1": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
        "3*2": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
        "3*3": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
        "3*4": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
        "3*5": WyseCashPriceModel().ticket_price(channel="USSD", band=10000, no_of_line=1).get("stake_amount"),
    }

    return wyse_cash_amount_dict.get(number) if wyse_cash_amount_dict.get(number) is not None else 0


def wyse_cash_top_menu_potential_winning():
    wyse_cash_price_model = WyseCashPriceModel()
    price_model = wyse_cash_price_model.ussd_top_pontential_winnings_amount()

    if not isinstance(price_model, list):
        return ""

    potential_menu = ""

    for num, i in enumerate(price_model):
        potential_menu += f"{num+1}.N{price_model[num]}\n"

    return potential_menu


def wyse_cash_ticket_price(band, index_list: list):
    wyse_cash_price_model = WyseCashPriceModel()
    price_model = wyse_cash_price_model.ussd_wyse_cash_ticket_price(band=band, index_range=index_list)

    # print("price_model", price_model)

    prices = ""
    for key, value in price_model.items():
        prices += f"{key}.N{value.get('ticket_price')} to win N{value.get('potential_winning')}\n"

    return prices


mega_cash_item = {
    # "3": "CON Welcome to WinWise:\nDaily Draw\nWin Mega Cash up to N2,500,000\n\n"
    # "1.Get Cash advance\n"
    # f"2.Exit\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    "3": "CON Select your choice Band: \n" "Win up to:\n"
    # "1.N50,000\n"
    # "2.N250,000\n"
    # "3.N500,000\n"
    # "4.N2,500,000\n",
    f"{wyse_cash_top_menu_potential_winning()}\n",
    #
    # BAND CHOICE FOR 50,000
    "3*1": "CON Select your choice Band: \n"
    # "1.N150 to win N10,000 \n"
    # "2.N300 to win N20,000 \n"
    # "3.N450 to win N30,000 \n"
    # "4.N600 to win N40,000 \n"
    # "5.N750 to win N50,000 \n",
    f"{wyse_cash_ticket_price(band=10000, index_list=[1,2,3,4,5])}",
    #
    # BAND CHOICE FOR 250,000
    "3*2": "CON Select your choice Band: \n"
    # "1.N250 to win N50,000 \n"
    # "2.N500 to win N100,000 \n"
    # "3.N750 to win N150,000 \n"
    # "4.N1,000 to win N200,000 \n"
    # "5.N1,250 to win N250,000 \n",
    f"{wyse_cash_ticket_price(band=50000, index_list=[1,2,3,4,5])}",
    #
    # BAND CHOICE FOR 500k
    "3*3": "CON Select your choice Band: \n"
    # "1.N600 to win N100k \n"
    # "2.N1,200 to win N200k \n"
    # "3.N1,800 to win N300k \n"
    # "4.N2,400 to win N400k \n"
    # "5.N3,000 to win N500k \n",
    f"{wyse_cash_ticket_price(band=100000, index_list=[1,2,3,4,5])}",
    #
    # BAND CHOICE FOR
    "3*4": "CON Select your choice Band: \n"
    # "1.N1,200 to win N200k \n"
    # "2.N2,400 to win N400k \n"
    # "3.N3,600 to win N600k \n"
    # "4.N4,800 to win N800k \n"
    # "5.N6,000 to win N1m \n",
    f"{wyse_cash_ticket_price(band=200000, index_list=[1,2,3,4,5])}",
    #
    "select_bank": select_bank(),
    #
    # summary
    "summary": "END WinWise:\nWin Mega cash up to N2,500,000.\n\nMega Cash:\n"
    "Tickets:  {} \n"
    "Stake: {} \n"
    "Win: {} \n"
    f"\nDraws: {week_name} {get_current_date} 8 pm\n",
}
