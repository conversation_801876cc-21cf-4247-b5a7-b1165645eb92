from datetime import date, timedelta

from django.db.models import Count
from django.utils import timezone

from sport_app.models import FootballTable
from wyse_ussd.helper.general_helper import select_bank


class SoccerFixtures:
    """This class will help to feed the ui
    with realtime data from the api
    used to get necessary information
    for prediction
    """

    date = date.today()
    start_week = date - timedelta(date.weekday())
    end_week = start_week + timedelta(7)

    @classmethod
    def game_leagues(cls):
        league_string_menu = ""
        count = 1
        all_leagues: list = []
        # each_league_count = []
        leagues = (
            FootballTable.objects.filter(
                # game_completed=False
                fixture_date__gte=timezone.now(),
                game_completed=False,
            )
            .values("league_type")
            .annotate(total=Count("league_type"))
            .distinct()
        )

        if leagues.count() > 0:
            for league in list(leagues):
                league_type = league["league_type"]
                league_count = league["total"]
                league_string_menu += f"{count}.{league_type} {league_count}\n"

                all_leagues.append(
                    league_type,
                )
                # each_league_count.append(league_count)
                count += 1

            # display_msg = f"""CON Welcome to WinWise
            #         Soccer Cash\n
            #         Predict soccer events & Win up to N50,000.
            #         {league_string_menu}
            #           """
            # response = "CON Welcome to WinWise\n"
            # response = "CON Soccer Cash\n"
            # league_string_menu = league_string_menu.splitlines()

            response = "CON Predict soccer events\n"
            response += f"{league_string_menu}"
            response += "0.back"

            final_return_value = {
                "display_msg": response,
                # "display_msg": display_msg,
                "all_league_type": all_leagues,
                # "each_league_count": each_league_count,
            }
            # print(all_leagues, each_league_count)

            print("LENGTH OF SPLITTED STRING :::", len(league_string_menu.splitlines()))
            return final_return_value

        else:
            return "END Game option is currently not available"

    @classmethod
    def welcome_screen(cls):
        # welcome screen as soon as a player picks WIN SOCCER CASH
        # from the first screen

        print("WELCOME SCREEN:::::::::::::::")

        if isinstance(cls.game_leagues(), dict):
            display_message = cls.game_leagues().get("display_msg")
        else:
            display_message = cls.game_leagues()

        print("display_message", display_message, "\n\n\n")

        return display_message

    @classmethod
    def select_league(cls, league):
        index_count: int = int(league) - 1
        try:
            league = cls.game_leagues().get("all_league_type")[index_count]
            league_games = (
                FootballTable.objects.filter(
                    fixture_date__gte=timezone.now(),
                    game_completed=False,
                    league_type=league,
                )
                .values(
                    "fixture_id",
                    "home_team_code",
                    "away_team_code",
                    "home_id",
                    "away_id",
                    "fixture_date",
                )
                .order_by("fixture_date")
            )[:4]

            select_game_msg = ""
            count = 1
            fixtures_properties = []
            if league_games:
                for game in list(league_games):
                    home_team = game["home_team_code"]
                    away_team = game["away_team_code"]
                    fixture_id = game["fixture_id"]

                    fixture_date = game["fixture_date"].date().strftime("%d-%m-%y")
                    # league_type = game["league_type"]
                    select_game_msg += f"{count}. {home_team} vs {away_team} {fixture_date}\n"

                    # select_game_msg += f"{count}. {home_team} vs {away_team}\n"
                    prop = [home_team, away_team, fixture_id]
                    fixtures_properties.append(prop)
                    count += 1

                # display_msg = f"""CON {league}\n
                #                 This week  games:
                #                 {select_game_msg}
                # """
                response = f"CON {league}\n"
                response += "This week  games:\n"
                response += f"{select_game_msg}"
                response += "0.back"

                final_return_value = {
                    # "display_msg": display_msg,
                    "display_msg": response,
                    "fixtures_properties": fixtures_properties,
                }

                # print(fixtures_properties)
                # print(select_game_msg)
                print("LEAGUES:::::::::::::::", len(select_game_msg), select_game_msg)
                return final_return_value
        except IndexError:
            # e.g ['La Liga', 'Premier League', 'UEFA Champions League', 'UEFA Europa League', 'World Cup'] [4, 5, 16, 1, 1]
            # i.e if user select a number less or greater
            return "END Please select an option\nnot less or greater than the options on the screen.\nThank you"
        except Exception:
            print("ERROR OCCURED")
            return "END Something went wrong\nPlease try again later.\nThank you"

    @classmethod
    def score_entery(cls, league, selected_event):
        game = cls.select_league(league)
        try:
            index_count: int = int(selected_event) - 1
            prop = game.get("fixtures_properties")[index_count]

            response = f"CON {prop[0]} VS {prop[1]}\n"
            response += "Enter your prediction scores\n"
            response += "with space e.g (4 3)\n"
            response += "0.back"

            # display_msg = f"""CON prediction: {prop[0]} VS {prop[1]}\n
            # Enter your prediction scores with space e.g (4 3)\n0.back
            # """
            return response

        except IndexError:
            print("INDEX ERROR OCCURED")
            return "END Please select an option\nnot less or greater than the options on the screen.\nThank you"

        except ValueError:
            print("VALUE ERROR OCCURED")
            return "END Invlid selection\nPlease enter a valid option\nThank you"

    @classmethod
    def confirm_entery_band_options(cls, league, selected_event, prediction):
        game = cls.select_league(league)
        try:
            index_count: int = int(selected_event) - 1
            prop = game.get("fixtures_properties")[index_count]

            # response = f"CON {prop[0]} VS {prop[1]}\nPrediction: {prediction}\n"
            # response += "How Sure are you?\n"
            # response += "1.A Bit: N200 win N1,500\n"
            # response += "2.Quite: N500 win N5,000\n"
            # response += "3.Very: N1000 win N12,000\n"
            # response += "4.Too: N2000 win N20,000\n"
            # response += "0.back"

            response = f"CON {prop[0]} VS {prop[1]}\nPrediction: {prediction}\n"
            response += "For auto daily team H/A prediction?\n"
            response += "1.A Bit: N50 win N1,500\n"
            response += "2.Quite: N75 win N5,000\n"
            response += "0.back"

            # display_msg = f"""CON {prop[0]} VS {prop[1]}\nPrediction: {prediction}\n
            #                 How Sure are you?
            #                 1.A Bit: N200 win N1,500
            #                 2.Quite: N500 win N5,000
            #                 3.Very : N1000 win N12,000
            #                 4.Too  : N2000 win N20,000\n0.back
            #                 """
            return response
        except IndexError:
            return "END Please select an option\nnot less or greater than the options on the screen.\nThank you"

    @classmethod
    def fixtures_properties(cls, league, selected_event):
        game = cls.select_league(league)
        index_count: int = int(selected_event) - 1
        all_prop = game.get("fixtures_properties")[index_count]
        return all_prop


soccer_cash_item = {
    #
    "select_bank": select_bank(),
    #
    # summary
    "summary": "END summary:\n"
    "{} vs {}: {}\n"
    "Stake: N{}\n"
    "Win amount: N{}\n"
    "Rules: Winnings to be shared\n"
    "amongst players. (90 mins game)",
}
