# from wyse_ussd.helper.general_helper import BANK_NAMES
from datetime import datetime, timedelta

import pytz
from django.conf import settings

from prices.game_price import TelcoSalaryForLifePriceModel

# get weekday name
now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

get_current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

# print("::::::::::::::: current_time", get_current_time.hour, "\n\n\n\n\n\n")
# check if current time has passed 8pm

if get_current_time.hour >= 20:
    now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)) + timedelta(days=1)
    week_name = str(now.strftime("%A"))[:3]
    get_current_date = now.strftime("%d/%m/%y")


def salary_for_life_amount(number):
    """
    This function returns the amount and return true if the tciket is on-demand or false if it is not
    """

    sal_4_life_price_model = TelcoSalaryForLifePriceModel().subscription_ticket_prices()

    data = {}
    for key, value in sal_4_life_price_model.items():
        data[str(key)] = {
            "total_amount": value["total_amount"],
            "type": value["type"],
        }

    item = data.get(number)
    if item:
        return (
            item.get("total_amount"),
            True if item.get("type") == "on-demand" else False,
        )
    else:
        return None, None


sal_4_life_price_model = TelcoSalaryForLifePriceModel().ussd_pontential_winnings()


def sal_4_top_menu_price():
    sal_4_life_price_model = TelcoSalaryForLifePriceModel().ussd_pontential_winnings()

    potential_menu = ""

    for num, i in enumerate(sal_4_life_price_model):
        # print("io", i, type(i))
        # print("numo", num, type(num))
        potential_menu += f"{num+1}.N{sal_4_life_price_model[num]}\n"

    return potential_menu


def sal_4_life_ticket_price(index_range: list):
    sal4_for_life = TelcoSalaryForLifePriceModel().ussd_top_prices_in_threes(index_range=index_range)

    prices = ""
    for key, value in sal4_for_life.items():
        prices += f"{key}.N{value.get('ticket_price')} to win N{str(value.get('potential_winning')).replace('.0','')}\n"

    return prices


def sal_4_life_subscription_ticket_price(index_range: list):
    sal4_for_life = TelcoSalaryForLifePriceModel().ussd_top_subscription_prices_in_threes(index_range=index_range)

    prices = ""
    for key, value in sal4_for_life.items():
        type = "daily"
        if value.get("type") == "on-demand":
            type = "instant"

        prices += f"{key}. Win N{str(value.get('potential_winning')).replace('.0','')} {type} @N{str(value.get('ticket_price')).replace('.00', '').replace('.0', '')}{'Auto' if type == 'daily' else ''}\n"

    return prices


telco_sub_salary_life3_item = {
    # TELCO SUBSCRIPTION #
    "1": "Win up to N10m jackpot, select entry to qualify\:\n\n"  # noqa
    f"{sal_4_life_subscription_ticket_price                                                                                                                      (index_range=[1,2,3])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    # 50, 000
    "1*1": "Jackpot: N10,000,000\n\nSelect Chances:\n"
    f"{sal_4_life_ticket_price(index_range=[1])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    "1*2": "Jackpot: N10,000,000\n\nSelect Chances:\n"
    f"{sal_4_life_ticket_price(index_range=[2])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    "1*3": "Jackpot: N10,000,000\n\nSelect Chances:\n"
    f"{sal_4_life_ticket_price(index_range=[3])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    "summary": "WinWise: N50K Instant Cash! Await auth request, ACCEPT to enter. \n\n"
    "Lines: {}\n"
    "Stake: {} \n"
    "No: {} \n"
    f"Draws: {week_name} {get_current_date}",
}


def salary_for_life_user_pick_validator(text):
    new_text = text.split("*")

    print("new_text", new_text, "\n\n\n")

    if new_text[-2] == "1":
        if new_text[-1] in ["1", "2", "3"]:
            return telco_sub_salary_life3_item["select_bank"]
        else:
            return "END Invalid input. select 1, 2 or 3"

    elif new_text[-2] == "2":
        if new_text[-1] in ["4", "5", "6"]:
            return telco_sub_salary_life3_item["select_bank"]
        else:
            return "END Invalid input. select 4, 5, 6 or 7"

    elif new_text[-2] == "3":
        if new_text[-1] in ["7", "8", "9", "10"]:
            return telco_sub_salary_life3_item["select_bank"]
        else:
            return "END Invalid input. select 8, 9 or 10"


def salary_for_life_line_validator(text):
    new_text = text.split("*")

    print("new_text", new_text, "\n\n\n")

    if new_text[-2] == "1":
        if new_text[-1] in ["1", "2", "3"]:
            return True
        else:
            return "END Invalid input. select 1, 2 or 3"

    elif new_text[-2] == "2":
        if new_text[-1] in ["4", "5", "6"]:
            return True
        else:
            return "END Invalid input. select 4, 5, 6 or 7"

    elif new_text[-2] == "3":
        if new_text[-1] in ["7", "8", "9", "10"]:
            return True
        else:
            return "END Invalid input. select 8, 9 or 10"


sal_4_life_price_model = TelcoSalaryForLifePriceModel().ticket_price()
if sal_4_life_price_model is None:
    sal_4_life_price_model = {}

data = {}
if sal_4_life_price_model:
    for key, value in sal_4_life_price_model.items():
        data[key] = [
            value["total_amount"],
            value["potential_winning"],
        ]

telco_summary_amount_salary_for_life = data
