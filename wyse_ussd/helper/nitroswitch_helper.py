import secrets
import string
from datetime import datetime, timedelta

import requests
from decouple import config


def request_and_expiry_date_helper(num_dates):
    requestdate = datetime.now()

    datetimes = []

    if num_dates == 1:
        return [num_dates]

    # Generate the specified number of datetime points
    current_time = requestdate
    for _ in range(num_dates):
        datetimes.append(current_time)
        current_time += timedelta(days=1)  # Change the step as needed (e.g., minutes, seconds, etc.)

    # Optiona
    return datetimes


def nitroswitch_sms_gateway(phone_number, message, service_code):
    from wyse_ussd.models import NitroswitchContentDeliveryLogs

    host = "http://*************:8101/"
    headers = {"cpid": "22042281", "x-token": config("NITROSWITCH_X_TOKEN")}
    url = f"{host}jsdp/sms/moresp/push?sender=20791&dest={phone_number}&msgtext={message}&pcode={service_code}"

    response = requests.request("GET", url, headers=headers)

    NitroswitchContentDeliveryLogs.objects.create(payload=url, response_payload=response.text)

    try:
        return response.json()
    except Exception:
        return response.text


def nitroswitch_content_deliery_for_free_trials(phone_number, pcode):
    from wyse_ussd.models import NitroSwitchData

    # print("GOT TO FREE TRIAL")

    subscription_services = NitroSwitchData.subscription_service_codes()
    item = subscription_services.get(pcode)

    if item is None:
        # print("ITEM IS NONE")
        return

    service_name = item.get("name")

    # print("service_name", service_name)
    # print('"auto" in str(service_name).lower()', "auto" in str(service_name).lower())

    if pcode in ["2139", "2147", "2146", "2148", "2149", "2148"]:
        message = "Ask AI by Glo! Your smart assistant is here to provide answers and information right to your phone. Text your first question now and stand a chance to win up to N50k instant draw daily!"
        return nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=pcode)

    if pcode in ["2145", "2146", "2147", "2148", "2149", "2148"]:
        message = (
            "Welcome to Glo AI Fortune! Ready to learn, earn, and win up to N1m? Stay tuned for exciting lessons on monetizing AI. Let's get started!"
        )
        return nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=pcode)

    if "auto" in str(service_name).lower():
        print("daily here")
        print('"daily" in str(service_name).lower()', "daily" in str(service_name).lower())
        if "daily" in str(service_name).lower():
            _service_name = str(service_name).lower().replace("daily", "").replace("onetime", "").replace("auto", "")

            message = f"Dear customer, your daily auto {_service_name} subscription has been successfully activated"
        elif "weekly" in str(service_name).lower():
            _service_name = str(service_name).lower().replace("daily", "").replace("onetime", "").replace("auto", "")
            message = f"Dear customer, your weekly auto {_service_name} subscription has been successfully activated"

        elif "monthly" in str(service_name).lower():
            _service_name = str(service_name).lower().replace("daily", "").replace("onetime", "").replace("auto", "")
            message = f"Dear customer, your monthly auto {_service_name} subscription has been successfully activated"

    elif "onetime" in str(service_name).lower():
        if "daily" in str(service_name).lower():
            _service_name = str(service_name).lower().replace("daily", "").replace("onetime", "").replace("onetime", "")

            message = f"Dear customer, your daily onetime {_service_name} subscription has been successfully activated"
        elif "weekly" in str(service_name).lower():
            _service_name = str(service_name).lower().replace("daily", "").replace("onetime", "").replace("onetime", "")
            message = f"Dear customer, your weekly onetime {_service_name} subscription has been successfully activated"

        elif "monthly" in str(service_name).lower():
            _service_name = str(service_name).lower().replace("daily", "").replace("onetime", "").replace("onetime", "")
            message = f"Dear customer, your monthly onetime {_service_name} subscription has been successfully activated"

    # print("meesage", "\g\g\g", message )
    nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=pcode)


def nitroswitch_game_id():
    alphabet = string.ascii_lowercase + string.digits
    loop_condition = True
    game_id = "r"

    while loop_condition:
        game_id = "".join(secrets.choice(alphabet) for i in range(7))
        if any(c.islower() for c in game_id) and any(c.islower() for c in game_id) and sum(c.isdigit() for c in game_id) >= 5:
            loop_condition = False

    return game_id


def nitroswitch_subscribe(phone_number, pcode, service_provider_code, transaction_id, amount, validity, renewal):
    host = "http://*************:8101/"

    # url = f"{host}jsdpocg/charge/OP/glo?msisdn={phone_number}&platform=WEB&pcode={pcode}&mcode={service_provider_code}&nettype=W&mtransid={transaction_id}&amount={amount}&validity={validity}&ren={renewal}"

    # url = f"{host}jsdpocg/charge/OP/glo?msisdn={phone_number}&platform=USSD&pcode={pcode}&mcode={service_provider_code}&nettype=W&mtransid={transaction_id}&amount={amount}&validity={validity}&ren={renewal}&checksum=<Checksum>&rurl=<Redire ctURL>&apiname=<API Name if any>"

    url = f"{host}/jsdpocg/charge/OP/GLO?msisdn={phone_number}&platform=WEB&pcode={pcode}&mcode=&nettype=&mtransid=&amount=&validity=&ren=&checksum=&rurl=&apiname="

    print("url", url, "\n\n")
    headers = {"cpid": "22042281", "x-token": config("NITROSWITCH_X_TOKEN")}

    print()

    response = requests.request("GET", url, headers=headers)

    # NitroswitchContentDeliveryLogs.objects.create(payload=url, response_payload=response.text)

    try:
        return response.json()
    except Exception:
        return response.text


def nitroswitch_sms_content_delivery(phone_number, message):
    """
    This function helps find an active service code and attach to the sms delivery payload
    and sends the sms
    """
    from wyse_ussd.models import NitroSwitchData

    last_subscription = NitroSwitchData.objects.filter(phone_number=phone_number)

    if len(last_subscription) == 0:
        return

    _last_subscription = last_subscription.last()
    active_sub = last_subscription.filter(subscription_status="ACTIVE").last()
    if active_sub is not None:
        _last_subscription = active_sub

    nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=_last_subscription.service_id)
