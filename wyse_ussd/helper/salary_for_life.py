# from wyse_ussd.helper.general_helper import BANK_NAMES
from datetime import datetime, timedelta

from wyse_ussd.helper.general_helper import select_bank

# get weekday name
now = datetime.now()
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

get_current_time = datetime.now()

# print("::::::::::::::: current_time", get_current_time.hour, "\n\n\n\n\n\n")
# check if current time has passed 8pm
if get_current_time.hour >= 20:
    now = datetime.now() + timedelta(days=1)
    week_name = str(now.strftime("%A"))[:3]
    get_current_date = now.strftime("%d/%m/%y")


# salary_for_life_item = {
#     "1": "CON WhisperWyse:\n Stand a chance to earn Salary \nfor life or N10,000,000 cash.\n "
#     "1.Automatic Entry\n"
#     "2.Number Pick \n",
#     #
#     # Automatic Entry
#     "1*1": "CON Select Line entries to qualify\nfor the Salary for life draw :\n"
#     "1.Up to 3lines get N50,000 \n"
#     "2.Up to 7lines get N500,000 \n"
#     "3.Up to 10lines get N1,500,000\n",
#     #
#     # 50, 000
#     "1*1*1": "CON Select: \n"
#     # "Jackpot: N10,000,000\n"
#     "1.N150 to get N6,120 \n" "2.N300 to get N12,240 \n" "3.N450 to get N18,360 \n",
#     #
#     # 500, 000
#     "1*1*2": "CON Select: \n"
#     # Jackpot: N10,000,000\n"
#     "4.N600 to get N24,480 \n"
#     "5.N750 to get N30,600 \n"
#     "6.N900 to get N36,720 \n"
#     "7.N1,000 to get N40,800 \n",
#     #
#     # 1,500,000
#     "1*1*3": "CON Select: \n"
#     # "Jackpot: N10,000,000\n"
#     "8.N1,200 to get N48,960 \n"
#     "9.N1,400 to get N57,120 \n"
#     "10.N1,600 to get N65,280 \n",
#     #
#     # Number Pick
#     "1*2": "CON Select Line entries to qualify\nfor the Salary for life draw :\n"
#     "1.Up to 3lines get N50,000 \n"
#     "2.Up to 7lines get N500,000 \n"
#     "3.Up to 10lines get N1,500,000\n",
#     #
#     # 50, 000
#     "1*2*1": "CON Select Jackpot:\n"
#     "1.N150 to get N6,120 \n"
#     "2.N300 to get N12,240 \n"
#     "3.N450 to get N18,360 \n",
#     #
#     # 500, 000
#     "1*2*2": "CON Select Jackpot:\n"
#     "4.N600 to get N24,480 \n"
#     "5.N750 to get N30,600 \n"
#     "6.N900 to get N36,720 \n"
#     "7.N1,000 to get N40,800 \n",
#     #
#     # 1,500,000
#     "1*2*3": "CON Select Jackpot:\n"
#     "8.N1,200 to get N48,960 \n"
#     "9.N1,400 to get N57,120 \n"
#     "10.N1,600 to get N65,280 \n",
#     #
#     # get user input
#     "get_user_input": "CON Salary for Life:\n"
#     "Please input an any 5 numbers \n"
#     "between 1 and 49 separated \n"
#     "by space (eg: 1 12 9 44 18) \n",
#     #
#     # select bank options
#     "select_bank": BANK_NAMES[0],
#     #
#     # summary
#     "summary": "CON Summary: salary for life \n" "Numbers: {} \n"
#     # "Jackpot Win : 10,000,000"
#     "Potential Win: {} \n"
#     "No of Lines : {} \n"
#     "Stake amount: {} \n"
#     "Please confirm \n"
#     "1. Yes \n"
#     "2. No \n",
# }


salary_for_life_item = {
    # "1": "CON WinWise:\nDaily Draw\nStand a chance to earn Salary \nfor life or N10,000,000 cash.\n\n"
    # "1.Automatic Entry\n"
    # f"2.Number Pick\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    "1": "CON WinWise:\nDaily Draw\nStand a chance to earn Salary \nfor life or N10,000,000 cash.\n\n"
    "1.Automatic Entry\n\n"
    f"{week_name} {get_current_date} 8 pm",
    #
    # Automatic Entry
    "1*1": "CON Select entry to qualify\nfor Salary for life draw :\n\n"
    "Win up to:\n"
    "1.N50,000\n"
    "2.N500,000\n"
    f"3.N1,500,000\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    # 50, 000
    "1*1*1": "CON Jackpot: N10,000,000\n\nSelect Chances:\n"
    "1.N100 to win N5,000\n"
    "2.N300 to win N15,000\n"
    f"3.N600 to win N50,000\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    # "1.N100 to get N5,000\n"
    # "2.N300 to get N15,000\n"
    # "3.N600 to get N50,000\n0.back",
    #
    # 500, 000
    "1*1*2": "CON Jackpot: N10,000,000\n\nSelect Chances:\n"
    "4.N1,200 to win N150k\n"
    "5.N2,000 to win N250k\n"
    f"6.N2,400 to win N500k\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    # "7.N2,800 to get N750,000\n",
    #
    # 1,500,000
    "1*1*3": "CON Jackpot: N10,000,000\n\nSelect Chances:\n"
    "7.N2,800 to win N750k\n"
    "8.N3,200 to win N900k\n"
    "9.N3,600 to win N1.25m\n"
    f"10.N5,000 to win N1.5m\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    # Number Pick
    "1*2": "CON Select entry to qualify\nfor Salary for life draw :\n"
    "Win up to:\n"
    "1.N50,000\n"
    "2.N500,000\n"
    f"3.N1,500,000\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    # 50, 000
    "1*2*1": "CON Jackpot: N10,000,000\n\nSelect Chances:\n"
    "1.N100 to win N5,000\n"
    "2.N300 to win N15,000\n"
    f"3.N600 to win N50,000\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    # "1.N100 to qualify for N5,000\n"
    # "2.N300 to qualify for N15,000\n"
    # "3.N600 to qualify for N50,000\n0.back",
    #
    # 500, 000
    "1*2*2": "CON Jackpot: N10,000,000\n\nSelect Chances:\n"
    "4.N1,200 to win N150k\n"
    "5.N2,000 to win N250k\n"
    f"6.N2,400 to win N500k\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    # "4.N1,200 to get N150,000\n"
    # "5.N2,000 to get N250,000\n"
    # "6.N2,400 to get N500,000\n0.back",
    #
    # 1,500,000
    "1*2*3": "CON Jackpot: N10,000,000\n\nSelect Chances:\n"
    "7.N2,800 to win N750k\n"
    "8.N3,200 to win N900k\n"
    "9.N3,600 to win N1.25m\n"
    f"10.N5,000 to win N1.5m\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    # get user input
    "get_user_input": "CON Salary for Life:\n"
    "Please input an any 5 numbers\n"
    "between 1 and 49 separated\n"
    f"by space (eg: 1 12 9 44 18)\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    # select bank options
    "select_bank": select_bank(),
    #
    # summary
    "summary": "CON Summary: salary for life\n"
    # "Jackpot Win : 10,000,000"
    "Lines: {}\n"
    "Stake: {}\n"
    "Numbers: {}\n"
    "Potential Win: {}\n"
    "confirm\n\n"
    "1. Yes\n"
    f"2. Exit\n\nNext Draw: {week_name} {get_current_date} 8 pm",
}


def salary_for_life_user_pick_validator(text):
    new_text = text.split("*")

    print("new_text", new_text, "\n\n\n")

    if new_text[-2] == "1":
        if new_text[-1] in ["1", "2", "3"]:
            return salary_for_life_item["select_bank"]
        else:
            return "END Invalid input. select 1, 2 or 3"

    elif new_text[-2] == "2":
        if new_text[-1] in ["4", "5", "6"]:
            return salary_for_life_item["select_bank"]
        else:
            return "END Invalid input. select 4, 5, 6 or 7"

    elif new_text[-2] == "3":
        if new_text[-1] in ["7", "8", "9", "10"]:
            return salary_for_life_item["select_bank"]
        else:
            return "END Invalid input. select 8, 9 or 10"
