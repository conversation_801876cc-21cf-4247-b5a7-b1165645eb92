import uuid
from datetime import datetime, timedelta

import redis
import requests
from django.conf import settings

from main.models import PayoutTransactionTable, UserProfile
from main.tasks import check_vfd_float_account_and_notify_admin
from wallet_app.models import DebitCreditRecord, UserWallet
from wyse_ussd.models import TelcoUsers
from decouple import config


class AgencyBankingAirtimeReward:
    def __init__(self, amount, recipient, game_type=None, game_play_id=None):
        self.amount = amount
        self.recipient = recipient
        self.game_type = game_type
        self.game_play_id = game_play_id

    def agency_banking_login(self):
        redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
        agency_user_token = redis_db.get("agent_login_token")
        if agency_user_token is None:
            email = settings.AGENCY_BANKING_USEREMAIL
            password = settings.AGENCY_BANKING_PASSWORD

            url = f"{settings.AGENCY_BANKING_BASE_URL}/user/login/create/"

            payload = {
                "email": email,
                "password": password,
                "device_type": "MOBILE",
            }

            headers = {
                "Content-Type": "application/json",
            }

            try:
                response = requests.post(url, json=payload, headers=headers)
                res = response.json()
                token = res["access"]
                redis_db.set("agent_login_token", token, ex=timedelta(days=3))

                return res

            except Exception:
                return self.agency_banking_login()

        else:
            res = {"access": agency_user_token}
            return res

    def telco_users_reward(self):
        from broad_base_communication.bbc_helper import BBCTelcoAggregator

        url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/bills_payments/"

        telco_user = TelcoUsers.objects.using("external2").filter(phone_number=self.recipient).last()
        if telco_user is None:
            return {"error": "Recipient telco table instance not found"}

        packgae_slug = "MTN_VTU"
        biller = "MTN_VTU"
        sender_name = "WINWISE"

        if telco_user.network == "GLO":
            packgae_slug = "GLO_VTU"
            biller = "GLO_VTU"
            sender_name = "20144"

        # user wallet
        user_wallet = UserWallet.objects.filter(user__phone_number=self.recipient, wallet_tag="WEB").last()
        if user_wallet is None:
            return {"error": "Recipient wallet instance not found"}

        print(
            f"""
        self.recipient: {self.recipient}
        self.amount: {self.amount}
        user_wallet.telco_wallet_balance: {user_wallet.airtime_wallet_balance}
        \n\n\n\n\n\n
        """
        )
        if self.amount > user_wallet.airtime_wallet_balance:
            return {"error": "Insufficient balance"}

        payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=self.recipient,
            amount=self.amount,
            channel="USSD",
            reference=payout_reference,
            transaction_type="DEBIT",
        )

        wallet_payload = {
            "transaction_from": "AIRTIME_PURCHASE",
            "game_type": self.game_type,
            "game_play_id": self.game_play_id,
        }

        # get user profile
        user_profile = UserProfile.objects.filter(phone_number=self.recipient).last()

        UserWallet.deduct_wallet(
            user=user_profile,
            amount=int(self.amount),
            channel="WEB",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="AIRTIME_WALLET",
            **wallet_payload,
        )

        payload = {
            "from_wallet_type": "COLLECTION",
            "customerId": self.recipient,
            "packageSlug": packgae_slug,
            "channel": "USSD",
            "amount": self.amount,
            "customerName": self.recipient,
            "phoneNumber": self.recipient,
            "bills_type": "VTU",
            "biller": biller,
            "customer_reference": debit_credit_record.reference,
        }

        telco_network = None
        if user_profile.network_provider is None:
            user_profile.network_provider = telco_user.network
            user_profile.save()
            telco_network = telco_user.network
        else:
            telco_network = telco_user.network

        user_wallet.refresh_from_db()

        _withdraw_table_instance = PayoutTransactionTable.objects.create(
            source="LIBERTY_PAY",
            amount=self.amount,
            disbursement_unique_id=payout_reference,
            phone=self.recipient,
            payout_trans_ref=debit_credit_record.reference,
            channel="USSD",
            game_play_id=self.game_play_id,
            payout_payload=payload,
            unique_game_play_id=self.game_play_id,
            date_won=datetime.now(),
            is_airtime_purchase=True,
            telco_network=telco_network,
            balance_before=user_wallet.telco_wallet_balance + self.amount,
            balance_after=user_wallet.telco_wallet_balance,
            joined_since=user_profile.get_duration(),
            name=f"{user_profile.first_name} {user_profile.last_name}",
        )

        cp_withdraw_table_instance = _withdraw_table_instance

        cp_payload = payload

        cp_payload["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN

        token = (self.agency_banking_login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        res = requests.post(url, json=payload, headers=headers)

        cp_withdraw_table_instance.source_response_payload = res.text
        cp_withdraw_table_instance.save()

        check_vfd_float_account_and_notify_admin.apply_async(
            queue="celery3",
        )

        try:
            response_data = res.json()
        except Exception:
            return {"error": "Error processing request"}

        """
        sample response

        {
            "error": false,
            "success": true,
            "message": "Transaction Processing. Thank You for Using This Channel"
        }
        """

        if response_data.get("success", False) is True:
            cp_withdraw_table_instance.is_verified = True
            cp_withdraw_table_instance.disbursed = True
            cp_withdraw_table_instance.save()

            # send sms to user telling them that their airtime reward has been processed
            sms_payload = {
                "phone_number": self.recipient,
                "sender_name": sender_name,
                "message": "Your airtime reward has been processed",
            }

            sms_payload["use_json_format"] = True
            BBCTelcoAggregator().bbc_send_sms(**sms_payload)

            return {"success": "Airtime purchase successful"}

        else:
            payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=self.recipient,
                amount=self.amount,
                channel="USSD",
                reference=payout_reference,
                transaction_type="CREDIT    ",
            )

            wallet_payload = {
                "transaction_from": "AIRTIME_PURCHASE_REVERSAL",
                "game_type": self.game_type,
                "game_play_id": self.game_play_id,
            }

            # get user profile
            user_profile = UserProfile.objects.filter(phone_number=self.recipient).last()

            UserWallet.fund_wallet(
                user=user_profile,
                amount=int(self.amount),
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="AIRTIME_WALLET",
                **wallet_payload,
            )

    def airtime_reward(self, sms_message, payout_reference):
        from broad_base_communication.bbc_helper import BBCTelcoAggregator

        telco_user = TelcoUsers.objects.using("external2").filter(phone_number=self.recipient).last()
        if telco_user is None:
            return {"error": "Recipient telco table instance not found"}

        packgae_slug = "MTN_VTU"
        biller = "MTN_VTU"
        sender_name = "WINWISE"

        if telco_user.network == "GLO":
            packgae_slug = "GLO_VTU"
            biller = "GLO_VTU"
            sender_name = "20144"

        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=self.recipient,
            amount=self.amount,
            channel="USSD",
            reference=payout_reference,
            transaction_type="DEBIT",
        )

        payload = {
            "from_wallet_type": "COLLECTION",
            "customerId": self.recipient,
            "packageSlug": packgae_slug,
            "channel": "USSD",
            "amount": self.amount,
            "customerName": self.recipient,
            "phoneNumber": self.recipient,
            "bills_type": "VTU",
            "biller": biller,
            "customer_reference": debit_credit_record.reference,
        }

        _withdraw_table_instance = PayoutTransactionTable.objects.create(
            source="LIBERTY_PAY",
            amount=self.amount,
            disbursement_unique_id=payout_reference,
            phone=self.recipient,
            payout_trans_ref=debit_credit_record.reference,
            channel="USSD",
            game_play_id=self.game_play_id,
            payout_payload=payload,
            unique_game_play_id=self.game_play_id,
            date_won=datetime.now(),
            is_airtime_purchase=True,
        )

        cp_withdraw_table_instance = _withdraw_table_instance

        cp_payload = payload

        cp_payload["transaction_pin"] = settings.AGENCY_BANKING_TRANSACTION_PIN

        token = (self.agency_banking_login())["access"]

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}",
        }

        url = f"{settings.AGENCY_BANKING_BASE_URL}/accounts/bills_payments/"

        res = requests.post(url, json=payload, headers=headers)

        cp_withdraw_table_instance.source_response_payload = res.text
        cp_withdraw_table_instance.save()

        check_vfd_float_account_and_notify_admin.apply_async(
            queue="celery3",
        )

        try:
            response_data = res.json()
        except Exception:
            return {"error": "Error processing request"}

        if response_data.get("success", False) is True:
            cp_withdraw_table_instance.is_verified = True
            cp_withdraw_table_instance.disbursed = True
            cp_withdraw_table_instance.save()

            sms_payload = {
                "phone_number": self.recipient,
                "sender_name": sender_name,
                "message": sms_message,
            }

            sms_payload["use_json_format"] = True
            BBCTelcoAggregator().bbc_send_sms(**sms_payload)

            return {"successful": True, "message": "Airtime purchase successful"}
        else:
            return {"successful": False, "message": "Airtime purchase failed"}
