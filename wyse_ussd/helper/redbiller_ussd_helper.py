import base64
import json

import requests
from django.conf import settings

from main.models import UserProfile
from main.ussd.bankdb import filter_bank
from pos_app.utils import <PERSON><PERSON><PERSON><PERSON>
from wyse_ussd.models import (
    UssdPayment,
    full_name_split,
    phone_number_reformat,
    random_with_N_digits,
)


def redbiller_ussd_payment_code(
    user_profile_instance: UserProfile, amount: int, payment_for="FUNDING_TELCO_WALLET", _bank_code=None, counter: int = 0
):
    redbiller_url = f"{settings.LIBERTY_VAS_BASE_URL}/redbiller/merchant/initiate_ussd_payment/"
    username = settings.LIBERTY_VAS_AUTH_USERNAME
    password = settings.LIBERTY_VAS_AUTH_PASSWORD

    amount = int(amount)

    STRING_VALUE = f"{username}:{password}"
    AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

    transaction_ref = f"telco{random_with_N_digits()}{user_profile_instance.id}"

    transaction = UssdPayment.objects.create(
        user=user_profile_instance,
        amount=amount,
        transaction_ref=transaction_ref,
        payment_for=payment_for,
        source="REDBILLER",
    )

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Basic {AUTH_TOKEN}",
    }

    if user_profile_instance.first_name is None or user_profile_instance.first_name == "":
        # genrate random name
        name = full_name_split(DommyName(5).generate_name())
        first_name = name.get("first_name")
        last_name = name.get("last_name")

    else:
        first_name = user_profile_instance.first_name
        last_name = user_profile_instance.last_name

    if _bank_code is not None:
        bank_code = _bank_code
    else:
        bank_code_filter = filter_bank(cbn_code=user_profile_instance.bank_code)
        if bank_code_filter is None:
            bank_code = "000015"
        else:
            bank_code = bank_code_filter.get("bank_code")

    payload = {
        "first_name": first_name,
        "surname": last_name,
        "phone_no": phone_number_reformat(user_profile_instance.phone_number),
        "email": "<EMAIL>",
        "bvn": settings.USER_BVN,
        "bank_code": bank_code,
        "amount": amount,
        "payment_reference": transaction.transaction_ref,
    }

    response = requests.post(url=redbiller_url, headers=headers, data=json.dumps(payload))
    print("payload", payload)
    print("response ::::::::::::::::::::::::::::::::::::::::::::", response.text)
    if response.status_code != 200:
        if counter == 3:
            return None
        return redbiller_ussd_payment_code(
            user_profile_instance=user_profile_instance,
            amount=amount,
            counter=counter + 1,
        )

    r = response.json()

    """
        SAMPLE RESPONSE
        {
            "response":200,
            "status":"true",
            "message":"Created successfully.",
            "details":{
                "profile":{
                    "first_name":"QILUQ",
                    "surname":"VUTUP",
                    "phone_no":"*************",
                    "email":"<EMAIL>",
                    "bvn":""
                },
                "account":{
                    "bank_name":"ACCESS BANK",
                    "bank_code":"000014",
                    "ussd_code":"*901*000*5756#"
                },
                "amount":100,
                "reference":"wyse71c8519292f1c7c386b802d91fa436e63018",
                "callback_url":"https://a45a-102-67-1-49.eu.ngrok.io/api/ussd/redbiller_webhook/",
                "date":"2022-10-17 15:45:26"
            }
        }

    """
    ussd_code = r.get("details", {}).get("account", {}).get("ussd_code", None)
    transaction.ussd_code = ussd_code
    transaction.save()

    return ussd_code
