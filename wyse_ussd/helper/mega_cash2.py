from datetime import datetime, timedelta

import pytz
from django.conf import settings

from wyse_ussd.helper.general_helper import select_bank

# get weekday name
now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

get_current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

# print("::::::::::::::: current_time", get_current_time.hour, "\n\n\n\n\n\n")
# check if current time has passed 8pm
if get_current_time.hour >= 20:
    now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)) + timedelta(days=1)
    week_name = str(now.strftime("%A"))[:3]
    get_current_date = now.strftime("%d/%m/%y")


def wyse_cash_amount(number):
    wyse_cash_amount_dict = {
        "3*1*1": 100,
        "3*1*2": 200,
        "3*1*3": 300,
        "3*1*4": 400,
        "3*1*5": 500,
        #
        "3*2*1": 200,
        "3*2*2": 400,
        "3*2*3": 600,
        "3*2*4": 800,
        "3*2*5": 1000,
        #
        "3*3*1": 5000,
        "3*3*2": 1000,
        "3*3*3": 1500,
        "3*3*4": 2000,
        "3*3*5": 2500,
        #
        "3*4*1": 1000,
        "3*4*2": 2000,
        "3*4*3": 3000,
        "3*4*4": 4000,
        "3*4*5": 5000,
    }

    return wyse_cash_amount_dict.get(number) if wyse_cash_amount_dict.get(number) is not None else 0


mega_cash_item = {
    # "3": "CON Welcome to WinWise:\nDaily Draw\nWin Mega Cash up to N2,500,000\n\n"
    # "1.Get Cash advance\n"
    # f"2.Exit\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    "3": "CON Select your choice Band: \n" "Win up to:\n" "1.N50,000\n" "2.N250,000\n" "3.N1,250,000\n" "4.N2,500,000\n",
    #
    # BAND CHOICE FOR 50,000
    "3*1": "CON Select your choice Band: \n"
    "1.N100 to win N10,000 \n"
    "2.N200 to win N20,000 \n"
    "3.N300 to win N30,000 \n"
    "4.N400 to win N40,000 \n"
    "5.N500 to win N50,000 \n",
    #
    # BAND CHOICE FOR 250,000
    "3*2": "CON Select your choice Band: \n"
    "1.N200 to win N50,000 \n"
    "2.N400 to win N100,000 \n"
    "3.N600 to win N150,000 \n"
    "4.N800 to win N200,000 \n"
    "5.N1,000 to win N250,000 \n",
    #
    # BAND CHOICE FOR 1,250,000
    "3*3": "CON Select your choice Band: \n"
    "1.N500 to win N250k \n"
    "2.N1,000 to win N500k \n"
    "3.N1,500 to win N750k \n"
    "4.N2,000 to win N1.0m \n"
    "5.N2,500 to win N1.25m \n",
    #
    # BAND CHOICE FOR 2,500,000
    "3*4": "CON Select your choice Band: \n"
    "1.N1,000 to win N500k \n"
    "2.N2,000 to win N1.0m \n"
    "3.N3,000 to win N1.5m \n"
    "4.N4,000 to win N2.0m \n"
    "5.N5,000 to win N2.5m \n",
    #
    "select_bank": select_bank(),
    #
    # summary
    "summary": "END WinWise:\nWin Mega cash up to N2,500,000.\n\nMega Cash:\n"
    "Tickets:  {} \n"
    "Stake: {} \n"
    "Win: {} \n"
    f"\nDraws: {week_name} {get_current_date} 8 pm\n",
}
