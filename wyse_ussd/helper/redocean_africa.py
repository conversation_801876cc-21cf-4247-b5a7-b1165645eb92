
from decouple import config
import redis 
import requests
from datetime import timedelta



class RedoceanAfricaGatewayHelper:

    def __init__(self):
        self.base_url = config("REDOCEAN_AFRICA_BASE_BASE_URL")
    

    def get_token(self) -> str:
        """
        Retrieves the token for the Redocean Africa payment gateway.
        """
        from wyse_ussd.models import RedoceanAfricaRequestLogs, RedoceanToken


        email = config("REDOCEAN_AFRICA_USEREMAIL")
        password = config("REDOCEAN_AFRICA_PASSWORD")

        url = f"{self.base_url}/partners/login"
        
        log_instance = RedoceanAfricaRequestLogs.create_record(
            phone_number = "010",
            request_payload = "Login",
            type_of_request = "LOGIN",
            url = url
        )

        payload = {
            "email": email,
            "password": password,
        }

        headers = {
            "Content-Type": "application/json",
        }


        try:
            response = requests.post(url, json=payload, headers=headers)
            log_instance.response_payload = response.text
            log_instance.save()
            res = response.json()
            token = res.get("data", {}).get("token")
            if not token:
                return None
           

            return token

        except Exception:
            return None
        
        

    def subscribe_a_phone_number(self, phone_number, country = "KE") -> dict:
        url = f"{self.base_url}/partners/requests/subscribe"

        from wyse_ussd.models import RedoceanAfricaRequestLogs, RedoceanToken

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {RedoceanToken.retrieve_token()}",
        }

        payload = {
            "phone_number": phone_number,
            "short_code": config('REDOCEAN_AFRICA_SHORT_CODE'),
            "country": country,
        }

        s_payload = {
            "payload": payload,
            "headers": headers
        }

        log_instance = RedoceanAfricaRequestLogs.create_record(
            phone_number = phone_number,
            request_payload = s_payload,
            type_of_request = "SUBSCRIPTION_REQUEST",
            url=url
        )


        response = requests.post(url, json=payload, headers=headers)

        try:
            res = response.json()
            log_instance.response_payload = res
            log_instance.save()

            return res

        except Exception as e:
            log_instance.response_payload = str(response.text)
            log_instance.save()
            return {}
        
    
    def check_the_subscription_status_of_a_phone_number(self, phone_number, country = "KE") -> dict:
        url = f"{self.base_url}/partners/requests/checkSubscriptionStatus"

        

        from wyse_ussd.models import RedoceanAfricaRequestLogs, RedoceanToken

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {RedoceanToken.retrieve_token()}",
        }

        payload = {
            "phone_number": phone_number,
            "short_code": config('REDOCEAN_AFRICA_SHORT_CODE'),
            "country": country,
        }

        log_instance = RedoceanAfricaRequestLogs.create_record(
            phone_number = phone_number,
            request_payload = payload,
            type_of_request = "SUBSCRIPTION_INQUIRY",
            url = url
        )

        response = requests.get(url, json=payload, headers=headers)

        log_instance.response_payload = response.text
        log_instance.save()

        try:
            res = response.json()
            log_instance.save()
            return res

        except Exception as e:
            log_instance.save()
            return {}
        
    
    def send_content(self, phone_number, content, country = "KE") -> dict:
        url = f"{self.base_url}/partners/requests/sendContent"

        from wyse_ussd.models import RedoceanAfricaRequestLogs, RedoceanToken

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {RedoceanToken.retrieve_token()}",
        }

        payload = {
            "phone_number": phone_number,
            "content": content,
            "short_code": config('REDOCEAN_AFRICA_SHORT_CODE'),
            "country": country,
        }

        log_instance = RedoceanAfricaRequestLogs.create_record(
            phone_number = phone_number,
            request_payload = payload,
            type_of_request = "SEND_CONTENT_REQUEST",
            url = url
        )

        try:
            response = requests.post(url, json=payload, headers=headers)
            res = response.json()
            log_instance.response_payload = res
            log_instance.save()

            return res

        except Exception as e:
            log_instance.response_payload = str(e)
            log_instance.save()
            return {}
    

    def get_content_status(self, requestId) -> dict:
        url = f"{self.base_url}/partners/requests/checkContentStatus/{requestId}"

        from wyse_ussd.models import RedoceanAfricaRequestLogs, RedoceanToken

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {RedoceanToken.retrieve_token()}",
        }

        try:
            response = requests.get(url, headers=headers)
            res = response.json()
            return res

        except Exception as e:
            return {}
        

    def register_webhook_data(self, subscription_notification, dlr_notification) -> dict:
        url = f"{self.base_url}/partners/requests/setWebhookUrls"

        from wyse_ussd.models import RedoceanAfricaRequestLogs, RedoceanToken
        
        payload = {
            "dlr_notification": dlr_notification,
            "subscription_notification": subscription_notification
        }


        


        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {RedoceanToken.retrieve_token()}",
        }

        log_instance = RedoceanAfricaRequestLogs.create_record(
            phone_number = "002",
            request_payload = {
                "payload": payload,
                "headers": headers
            },
            type_of_request = "WEBHOOK_SETUP",
            url = url
        )

        response = requests.post(url, headers=headers, json= payload)
        log_instance.response_payload = response.text
        log_instance.save()

        

        try:
            res = response.json()
            return res

        except Exception as e:
            return str(e)