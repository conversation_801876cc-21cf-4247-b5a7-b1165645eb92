# from wyse_ussd.helper.general_helper import BANK_NAMES
from datetime import datetime, timedelta

import pytz
from django.conf import settings

from prices.game_price import SalaryForLifePriceModel
from wyse_ussd.helper.general_helper import select_bank

# get weekday name
now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

get_current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

# print("::::::::::::::: current_time", get_current_time.hour, "\n\n\n\n\n\n")
# check if current time has passed 8pm

if get_current_time.hour >= 20:
    now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)) + timedelta(days=1)
    week_name = str(now.strftime("%A"))[:3]
    get_current_date = now.strftime("%d/%m/%y")


def salary_for_life_amount(number):
    # salary_for_life_amount_dict = {
    #     "1": 100,
    #     "2": 300,
    #     "3": 600,
    #     "4": 1200,
    #     "5": 2000,
    #     "6": 2400,
    #     "7": 2800,
    #     "8": 3200,
    #     "9": 3600,
    #     "10": 5000,
    # }

    sal_4_life_price_model = SalaryForLifePriceModel().ticket_price(channel="USSD")

    data = {}
    for key, value in sal_4_life_price_model.items():
        data[str(key)] = value["total_amount"]

    return data.get(number)


sal_4_life_price_model = SalaryForLifePriceModel().ussd_top_three_pontential_winnings()


def sal_4_top_menu_price():
    sal_4_life_price_model = SalaryForLifePriceModel().ussd_top_three_pontential_winnings()

    potential_menu = ""

    for num, i in enumerate(sal_4_life_price_model):
        # print("io", i, type(i))
        # print("numo", num, type(num))
        potential_menu += f"{num+1}.N{sal_4_life_price_model[num]}\n"

    return potential_menu


def sal_4_life_ticket_price(index_range: list):
    sal4_for_life = SalaryForLifePriceModel().ussd_top_prices_in_threes(index_range=index_range)

    prices = ""
    for key, value in sal4_for_life.items():
        prices += f"{key}.N{value.get('ticket_price')} to win N{value.get('potential_winning')}\n"

    return prices


salary_for_life_item = {
    # "1": "CON WinWise:\nDaily Draw\nStand a chance to earn Salary \nfor life or N10,000,000 cash.\n\n"
    # "1.Automatic Entry\n"
    # f"2.Number Pick\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    # "1": "CON WinWise:\nDaily Draw\nStand a chance to earn Salary \nfor life or N10,000,000 cash.\n\n"
    # "1.Automatic Entry\n\n"
    # f"{week_name} {get_current_date} 8 pm",
    #
    # Automatic Entry
    "1": "CON Select entry to qualify\nfor Salary for life draw :\n\n" "Win up to:\n"
    # "1.N50,000\n"
    # "2.N500,000\n"
    # f"3.N600,000\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    f"{sal_4_top_menu_price()}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    # 50, 000
    "1*1": "CON Jackpot: N10,000,000\n\nSelect Chances:\n"
    # "1.N150 to win N5,000\n"
    # "2.N350 to win N15,000\n"
    # f"3.N700 to win N50,000\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    # "1.N100 to get N5,000\n"
    # "2.N300 to get N15,000\n"
    # "3.N600 to get N50,000\n0.back",
    f"{sal_4_life_ticket_price(index_range=[1,2,3])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    # 500, 000
    "1*2": "CON Jackpot: N10,000,000\n\nSelect Chances:\n"
    # "4.N1,400 to win N150k\n"
    # "5.N2,030 to win N250k\n"
    # f"6.N2,800 to win N500k\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    f"{sal_4_life_ticket_price(index_range=[4,5,6])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    # "7.N2,800 to get N750,000\n",
    #
    # 1,500,000
    # "1*3": "CON Jackpot: N10,000,000\n\nSelect Chances:\n"
    # "7.N3,300 to win N750k\n"
    # "8.N3,700 to win N900k\n"
    # "9.N4,200 to win N1.25m\n"
    "1*3": "CON Jackpot: N10,000,000\n\nSelect Chances:\n"
    f"{sal_4_life_ticket_price(index_range=[7,8,9])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    # select bank options
    "select_bank": select_bank(),
    #
    # summary
    "summary": "END WinWise:\nQualify up to N10,000,000 cash prize.\n\nSalary for life\n"
    # "Jackpot Win : 10,000,000"
    "Lines: {}\n" "Stake: {}\n" "No: {}\n" "Win: {}\n" "\n" f"Draws: {week_name} {get_current_date} 8 pm\n",
}


def salary_for_life_user_pick_validator(text):
    new_text = text.split("*")

    print("new_text", new_text, "\n\n\n")

    if new_text[-2] == "1":
        if new_text[-1] in ["1", "2", "3"]:
            return salary_for_life_item["select_bank"]
        else:
            return "END Invalid input. select 1, 2 or 3"

    elif new_text[-2] == "2":
        if new_text[-1] in ["4", "5", "6"]:
            return salary_for_life_item["select_bank"]
        else:
            return "END Invalid input. select 4, 5, 6 or 7"

    elif new_text[-2] == "3":
        if new_text[-1] in ["7", "8", "9", "10"]:
            return salary_for_life_item["select_bank"]
        else:
            return "END Invalid input. select 8, 9 or 10"


def salary_for_life_line_validator(text):
    new_text = text.split("*")

    print("new_text", new_text, "\n\n\n")

    if new_text[-2] == "1":
        if new_text[-1] in ["1", "2", "3"]:
            return True
        else:
            return "END Invalid input. select 1, 2 or 3"

    elif new_text[-2] == "2":
        if new_text[-1] in ["4", "5", "6"]:
            return True
        else:
            return "END Invalid input. select 4, 5, 6 or 7"

    elif new_text[-2] == "3":
        if new_text[-1] in ["7", "8", "9", "10"]:
            return True
        else:
            return "END Invalid input. select 8, 9 or 10"


# summary_amount_salary_for_life = {
#     1: [150, 5000],
#     2: [350, 15000],
#     3: [700, 50000],
#     4: [1400, 150000],
#     5: [2030, 250000],
#     6: [2800, 500000],
#     7: [3300, 750000],
#     8: [3700, 900000],
#     9: [4200, 1250000],
#     10: [5800, 1500000],
# }

sal_4_life_price_model = SalaryForLifePriceModel().ticket_price(channel="USSD")
if sal_4_life_price_model is None:
    sal_4_life_price_model = {}

data = {}
if sal_4_life_price_model:
    for key, value in sal_4_life_price_model.items():
        data[key] = [
            value["total_amount"],
            value["potential_winning"],
        ]

summary_amount_salary_for_life = data
