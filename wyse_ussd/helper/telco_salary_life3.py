# from wyse_ussd.helper.general_helper import BANK_NAMES
from datetime import datetime, timedelta

import pytz
from django.conf import settings

from prices.game_price import TelcoSalaryForLifePriceModel

# get weekday name
now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

get_current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

# print("::::::::::::::: current_time", get_current_time.hour, "\n\n\n\n\n\n")
# check if current time has passed 8pm

if get_current_time.hour >= 20:
    now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)) + timedelta(days=1)
    week_name = str(now.strftime("%A"))[:3]
    get_current_date = now.strftime("%d/%m/%y")


def salary_for_life_amount(number):
    sal_4_life_price_model = TelcoSalaryForLifePriceModel().ticket_price()

    data = {}
    for key, value in sal_4_life_price_model.items():
        data[str(key)] = value["total_amount"]

    return data.get(number)


sal_4_life_price_model = TelcoSalaryForLifePriceModel().ussd_pontential_winnings()


def sal_4_top_menu_price():
    sal_4_life_price_model = TelcoSalaryForLifePriceModel().ussd_pontential_winnings()

    potential_menu = ""

    for num, i in enumerate(sal_4_life_price_model):
        # print("io", i, type(i))
        # print("numo", num, type(num))
        potential_menu += f"{num+1}.N{sal_4_life_price_model[num]}\n"

    return potential_menu


def sal_4_life_ticket_price(index_range: list):
    sal4_for_life = TelcoSalaryForLifePriceModel().ussd_top_prices_in_threes(index_range=index_range)

    prices = ""
    for key, value in sal4_for_life.items():
        prices += f"{key}.N{value.get('ticket_price')} to win N{str(value.get('potential_winning')).replace('.0','')}\n"

    return prices


telco_salary_life3_item = {
    "1": "Select entry to qualify\nfor Salary for life draw :\n\n"
    f"{sal_4_life_ticket_price(index_range=[1,2,3,4])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    # 50, 000
    "1*1": "Jackpot: N10,000,000\n\nSelect Chances:\n"
    f"{sal_4_life_ticket_price(index_range=[1])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    "1*2": "Jackpot: N10,000,000\n\nSelect Chances:\n"
    f"{sal_4_life_ticket_price(index_range=[2])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    "1*3": "Jackpot: N10,000,000\n\nSelect Chances:\n"
    f"{sal_4_life_ticket_price(index_range=[3])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    "1*4": "Jackpot: N10,000,000\n\nSelect Chances:\n"
    f"{sal_4_life_ticket_price(index_range=[4])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    #
    # 500, 000
    # "1*2": "Jackpot: N10,000,000\n\nSelect Chances:\n"
    # f"{sal_4_life_ticket_price(index_range=[4,5,6])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    # #
    # "1*3": "Jackpot: N10,000,000\n\nSelect Chances:\n"
    # f"{sal_4_life_ticket_price(index_range=[7,8,9])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
    # "select_bank": select_bank(),
    #
    # summary
    # "summary": "END WinWise: Qualify up to N10,000,000 cash prize.\n\n"
    # "You will receive an authorization request shortly, ACCEPT to enter the draw.\n\n"
    # "Salary for life\n"
    # "Lines: {}\n"
    # "Stake: {}\n"
    # "No: {}\n"
    # "Win: {}\n"
    # "\n"
    # f"Draws: {week_name} {get_current_date} 8 pm",
    "summary": "WinWise: N50K Instant Cash! Await auth request, ACCEPT to enter. \n\n"
    "Lines: {}\n"
    "Stake: {} \n"
    "No: {} \n"
    f"Draws: {week_name} {get_current_date}",
}

# telco_salary_life3_item = {
#     "1": "Select entry to qualify\nfor Salary for life draw :\n\n" "Win up to:\n"
#     f"{sal_4_top_menu_price()}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
#     #
#     # 50, 000
#     "1*1": "Jackpot: N10,000,000\n\nSelect Chances:\n"
#     f"{sal_4_life_ticket_price(index_range=[1,2,3,4])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
#     #
#     # 500, 000
#     # "1*2": "Jackpot: N10,000,000\n\nSelect Chances:\n"
#     # f"{sal_4_life_ticket_price(index_range=[4,5,6])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
#     # #
#     # "1*3": "Jackpot: N10,000,000\n\nSelect Chances:\n"
#     # f"{sal_4_life_ticket_price(index_range=[7,8,9])}\n0.back\n\nNext Draw: {week_name} {get_current_date} 8 pm",
#     # "select_bank": select_bank(),
#     #
#     # summary
#     "summary": "END WinWise:\nQualify up to N10,000,000 cash prize.\n\nSalary for life\n"
#     # "Jackpot Win : 10,000,000"
#     "Lines: {}\n"
#     "Stake: {}\n"
#     "No: {}\n"
#     "Win: {}\n"
#     "\n"
#     f"Draws: {week_name} {get_current_date} 8 pm\n",
# }


def salary_for_life_user_pick_validator(text):
    new_text = text.split("*")

    print("new_text", new_text, "\n\n\n")

    if new_text[-2] == "1":
        if new_text[-1] in ["1", "2", "3"]:
            return telco_salary_life3_item["select_bank"]
        else:
            return "END Invalid input. select 1, 2 or 3"

    elif new_text[-2] == "2":
        if new_text[-1] in ["4", "5", "6"]:
            return telco_salary_life3_item["select_bank"]
        else:
            return "END Invalid input. select 4, 5, 6 or 7"

    elif new_text[-2] == "3":
        if new_text[-1] in ["7", "8", "9", "10"]:
            return telco_salary_life3_item["select_bank"]
        else:
            return "END Invalid input. select 8, 9 or 10"


def salary_for_life_line_validator(text):
    new_text = text.split("*")

    print("new_text", new_text, "\n\n\n")

    if new_text[-2] == "1":
        if new_text[-1] in ["1", "2", "3"]:
            return True
        else:
            return "END Invalid input. select 1, 2 or 3"

    elif new_text[-2] == "2":
        if new_text[-1] in ["4", "5", "6"]:
            return True
        else:
            return "END Invalid input. select 4, 5, 6 or 7"

    elif new_text[-2] == "3":
        if new_text[-1] in ["7", "8", "9", "10"]:
            return True
        else:
            return "END Invalid input. select 8, 9 or 10"


sal_4_life_price_model = TelcoSalaryForLifePriceModel().ticket_price()
if sal_4_life_price_model is None:
    sal_4_life_price_model = {}

data = {}
if sal_4_life_price_model:
    for key, value in sal_4_life_price_model.items():
        data[key] = [
            value["total_amount"],
            value["potential_winning"],
        ]

telco_summary_amount_salary_for_life = data
