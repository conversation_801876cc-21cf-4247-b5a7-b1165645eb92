from datetime import datetime, timedelta

import pytz
from django.conf import settings

from prices.game_price import TelcoWyseCashPriceModel
from wyse_ussd.helper.general_helper import select_bank

# get weekday name
now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

get_current_time = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

# print("::::::::::::::: current_time", get_current_time.hour, "\n\n\n\n\n\n")
# check if current time has passed 8pm
if get_current_time.hour >= 20:
    now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE)) + timedelta(days=1)
    week_name = str(now.strftime("%A"))[:3]
    get_current_date = now.strftime("%d/%m/%y")

wyse_cash_price_model = TelcoWyseCashPriceModel().ticket_price()

wyse_cash_summary_amount = {
    # --------stake_amnt-----potential_winnings-----
    "3*1*1": [
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=1).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=1).get("total_winning_amount"),
    ],
    "3*1*2": [
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=2).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=2).get("total_winning_amount"),
    ],
    "3*1*3": [
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=3).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=3).get("total_winning_amount"),
    ],
    "3*1*4": [
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=4).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=4).get("total_winning_amount"),
    ],
    "3*1*5": [
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=5).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=5).get("total_winning_amount"),
    ],
    #
    "3*2*1": [
        TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=1).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=1).get("total_winning_amount"),
    ],
    "3*2*2": [
        TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=2).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=2).get("total_winning_amount"),
    ],
    "3*2*3": [
        TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=3).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=3).get("total_winning_amount"),
    ],
    "3*2*4": [
        TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=4).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=4).get("total_winning_amount"),
    ],
    "3*2*5": [
        TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=5).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=5).get("total_winning_amount"),
    ],
    #
    "3*3*1": [
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=1).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=1).get("total_winning_amount"),
    ],
    "3*3*2": [
        TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=2).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=2).get("total_winning_amount"),
    ],
    "3*3*3": [
        TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=3).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=3).get("total_winning_amount"),
    ],
    "3*3*4": [
        TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=4).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=4).get("total_winning_amount"),
    ],
    "3*3*5": [
        TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=5).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=5).get("total_winning_amount"),
    ],
    #
    "3*4*1": [
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=1).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=1).get("total_winning_amount"),
    ],
    "3*4*2": [
        TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=2).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=2).get("total_winning_amount"),
    ],
    "3*4*3": [
        TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=3).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=3).get("total_winning_amount"),
    ],
    "3*4*4": [
        TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=4).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=4).get("total_winning_amount"),
    ],
    "3*4*5": [
        TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=5).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=5).get("total_winning_amount"),
    ],
}


sub_wyse_cash_summary_amount = {
    # --------stake_amnt-----potential_winnings-----
    "3*3": [
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=1).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=1).get("total_winning_amount"),
    ],
    "3*4": [
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=2).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=2).get("total_winning_amount"),
    ],
    "3*5": [
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=3).get("stake_amount"),
        TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=3).get("total_winning_amount"),
    ],
}


# for key, value in wyse_cash_price_model.items():
#     pass


def wyse_cash_amount(number):
    wyse_cash_amount_dict = {
        # "3*1*1": 100,
        # "3*1*2": 200,
        # "3*1*3": 300,
        # "3*1*4": 400,
        # "3*1*5": 500,
        # #
        # "3*2*1": 200,
        # "3*2*2": 400,
        # "3*2*3": 600,
        # "3*2*4": 800,
        # "3*2*5": 1000,
        # #
        # "3*3*1": 5000,
        # "3*3*2": 1000,
        # "3*3*3": 1500,
        # "3*3*4": 2000,
        # "3*3*5": 2500,
        # #
        # "3*4*1": 1000,
        # "3*4*2": 2000,
        # "3*4*3": 3000,
        # "3*4*4": 4000,
        # "3*4*5": 5000,
        "3*1*1": TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=1).get("stake_amount"),
        "3*1*2": TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=2).get("stake_amount"),
        "3*1*3": TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=3).get("stake_amount"),
        "3*1*4": TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=4).get("stake_amount"),
        "3*1*5": TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=5).get("stake_amount"),
        #
        "3*2*1": TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=1).get("stake_amount"),
        "3*2*2": TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=2).get("stake_amount"),
        "3*2*3": TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=3).get("stake_amount"),
        "3*2*4": TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=4).get("stake_amount"),
        "3*2*5": TelcoWyseCashPriceModel().ticket_price(band=50000, no_of_line=5).get("stake_amount"),
        #
        "3*3*1": TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=1).get("stake_amount"),
        "3*3*2": TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=2).get("stake_amount"),
        "3*3*3": TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=3).get("stake_amount"),
        "3*3*4": TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=4).get("stake_amount"),
        "3*3*5": TelcoWyseCashPriceModel().ticket_price(band=100000, no_of_line=5).get("stake_amount"),
        #
        "3*4*1": TelcoWyseCashPriceModel().ticket_price(band=10000, no_of_line=1).get("stake_amount"),
        "3*4*2": TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=2).get("stake_amount"),
        "3*4*3": TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=3).get("stake_amount"),
        "3*4*4": TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=4).get("stake_amount"),
        "3*4*5": TelcoWyseCashPriceModel().ticket_price(band=200000, no_of_line=5).get("stake_amount"),
    }

    return wyse_cash_amount_dict.get(number) if wyse_cash_amount_dict.get(number) is not None else 0


def wyse_cash_top_menu_potential_winning():
    wyse_cash_price_model = TelcoWyseCashPriceModel()
    price_model = wyse_cash_price_model.ussd_top_pontential_winnings_amount()

    if not isinstance(price_model, list):
        return ""

    potential_menu = ""

    for num, i in enumerate(price_model):
        potential_menu += f"{num+1}.N{price_model[num]}\n"

    return potential_menu


def wyse_cash_ticket_price(index_list: list):
    wyse_cash_price_model = TelcoWyseCashPriceModel()
    price_model = wyse_cash_price_model.ussd_top_prices_in_threes(index_range=index_list)

    # print("price_model", price_model)

    type = "daily"

    prices = ""

    for key, value in price_model.items():
        if value.get("type") == "on-demand":
            type = "instant"
            prices += f"{key}. Win N{str(value.get('potential_winning')).replace('.0','')} {type} @N{value.get('ticket_price')}\n"
        else:
            prices += f"{key}. Win N{str(value.get('potential_winning')).replace('.0','')} {type} @N{value.get('ticket_price')}{'Auto' if type == 'daily' else ''}\n"

    return prices


mega_cash_item = {
    #
    "3": "Win up to 50k Raffle, select choice below: \n" f"{wyse_cash_ticket_price(index_list=[1,2,3])}",
    #
    # BAND CHOICE FOR 250,000
    "3*1": "CON Select your choice Band: \n" f"{wyse_cash_ticket_price(index_list=[1])}",
    #
    # BAND CHOICE FOR 500k
    "3*2": "CON Select your choice Band: \n" f"{wyse_cash_ticket_price(index_list=[2])}",
    #
    # BAND CHOICE FOR
    "3*3": "CON Select your choice Band: \n" f"{wyse_cash_ticket_price(index_list=[3])}",
    #
    "3*4": "CON Select your choice Band: \n" f"{wyse_cash_ticket_price(index_list=[4])}",
    #
    "3*5": "CON Select your choice Band: \n" f"{wyse_cash_ticket_price(index_list=[5])}",
    #
    "select_bank": select_bank(),
    #
    "summary": "WinWise: N50K Instant Cash! Await auth request, ACCEPT to enter. \n\n"
    "Lines: {}\n"
    "Stake: {} \n"
    "No: {} \n"
    f"Draws: {week_name} {get_current_date}",
}


def wyse_cash_subscription_ticket_amount(number):
    """
    This function returns the amount and return true if the tciket is on-demand or false if it is not
    """

    icash_price_model = TelcoWyseCashPriceModel().subscription_ticket_prices()

    data = {}

    for key, value in icash_price_model.items():
        data[str(key)] = {
            "total_amount": value["total_amount"],
            "type": value["type"],
        }

    item = data.get(number)
    if item:
        return (
            item.get("total_amount"),
            True if item.get("type") == "on-demand" else False,
        )
    else:
        return None, None
