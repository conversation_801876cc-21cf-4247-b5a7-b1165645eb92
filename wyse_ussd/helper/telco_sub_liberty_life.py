from prices.game_price import TelcoLibertyLifePriceModel
from wyse_ussd.helper.general_helper import select_bank


def liberty_life_ticket_price(index_list: list):
    wyse_cash_price_model = TelcoLibertyLifePriceModel()
    price_model = wyse_cash_price_model.ussd_top_prices_in_threes(index_range=index_list)

    # print("price_model", price_model)

    type = "daily"

    prices = ""

    for key, value in price_model.items():
        if value.get("type") == "on-demand":
            type = "instant"
            prices += f"{key}.{type} @N{value.get('ticket_price')}\n"
        else:
            type = "daily"
            prices += f"{key}.{type} @N{value.get('ticket_price')}{'Auto' if type == 'daily' else ''}\n"

    return prices


liberty_life_menu_item = {
    #
    "8": "Subscribe, ask questions, win up to N1,000,000!: \n" f"{liberty_life_ticket_price(index_list=[1,2,3,4,5])}",
    #
    # BAND CHOICE FOR 250,000
    "8*1": "CON Select your choice Band: \n" f"{liberty_life_ticket_price(index_list=[1])}",
    #
    # BAND CHOICE FOR 500k
    "8*2": "CON Select your choice Band: \n" f"{liberty_life_ticket_price(index_list=[2])}",
    #
    # BAND CHOICE FOR
    "8*3": "CON Select your choice Band: \n" f"{liberty_life_ticket_price(index_list=[3])}",
    #
    #
    "8*4": "CON Select your choice Band: \n" f"{liberty_life_ticket_price(index_list=[4])}",
    #
    "8*5": "CON Select your choice Band: \n" f"{liberty_life_ticket_price(index_list=[5])}",
    #
    "select_bank": select_bank(),
    #
    "summary": "LIBERTY LIFE! Await auth request for this feature",
}


def liberty_life_subscription_ticket_amount(number):
    """
    This function returns the amount and return true if the tciket is on-demand or false if it is not
    """

    icash_price_model = TelcoLibertyLifePriceModel().subscription_ticket_prices()

    data = {}

    for key, value in icash_price_model.items():
        data[str(key)] = {
            "total_amount": value["total_amount"],
            "type": value["type"],
        }

    item = data.get(number)
    if item:
        return (
            item.get("total_amount"),
            True if item.get("type") == "on-demand" else False,
        )
    else:
        return None, None
