import json
import time
import uuid
from dataclasses import dataclass
from datetime import date, datetime, timedelta

import requests
from django.conf import settings
from django.db.models import Sum

from main.api.api_lottery_helpers import generate_game_play_id
from main.helpers.hops_restriction_helper import HopsRestrictor
from main.helpers.redis_storage import RedisStorage
from main.models import (
    LotteryBatch,
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    UserProfile,
    WovenAccountDetail,
)
from main.ussd.helpers import Utility
from overide_print import print
from pos_app.utils import DommyName
from prices.game_price import TelcoWyseCashPriceModel, WyseCashPriceModel
from wallet_app.models import GeneralWithdrawableWallet
from wallet_system.models import Wallet
from wyse_ussd.helper.bank import BankManger
from wyse_ussd.models import UssdLotteryPayment


def select_bank(num=None, is_glo=False, next_page=False):
    # if num is None:
    #     if is_glo is False:
    #         response = "CON Select Bank:\n"
    #         response += "1.Access. 2.Eco\n"
    #         response += "3.FistBnk. 4.FCMB\n"
    #         response += "5.GTB. 6.Heritage\n"
    #         response += "7.Keystone. 8.Stanbic\n"
    #         response += "9.UBA 10.Union.\n"
    #         response += "11.Zenith 12.STERLING.\n"
    #         response += "13.UNITY 14.WEMA.\n"
    #         response += "15.VFD 16.Others\n"
    #         return response
    #     else:
    #         if next_page is False:
    #             response = "CON Select Bank:\n"
    #             response += "1.Access. 2.Eco\n"
    #             response += "3.FistBnk. 4.FCMB\n"
    #             response += "5.GTB. 6.Heritage\n"
    #             response += "7.Keystone\n"
    #             response += "8.More Banks\n"
    #             return response
    #         else:
    #             response = "CON Select Bank:\n"
    #             response += "1.Stanbic. 2.UBA\n"
    #             response += "3.Union.\n"
    #             response += "4.Zenith 5.STERLING.\n"
    #             response += "6.UNITY 7.WEMA.\n"
    #             response += "8.VFD"
    #             return response

    if num is None:
        response = "CON Select Bank:\n"
        response += "1.Access. 2.gtb\n"
        response += "3.opay. 4.palmpay\n"
        response += "5.moniepoint. 6.wema\n"
        response += "7.polaris\n"
        response += "8.vfd\n"
        return response

    # _banks = {
    #     1: "access",
    #     2: "ecobank",
    #     3: "firstbank",
    #     4: "fcmb",
    #     5: "gtb",
    #     6: "heritage",
    #     7: "keystone",
    #     8: "stanbic",
    #     9: "uba",
    #     10: "unionbank",
    #     11: "zenith",
    #     12: "sterling",
    #     13: "unity",
    #     14: "wema",
    #     15: "vfd",
    #     16: "CON Enter Bank Name:\n",
    # }

    _banks = {
        1: "access",
        2: "gtb",
        3: "opay",
        4: "palmpay",
        5: "moniepoint",
        6: "wema",
        7: "polaris",
        8: "vfd",
        # 8: "fidelity",
        9: "firstbank",
        10: "fcmb",
        11: "uba",
        12: "stanbic",
        13: "unionbank",
        # 14: "vfd",
        15: "CON Enter Bank Name:\n",
    }

    try:
        if num.isdigit():
            no = int(num)
            return _banks[no]

        else:
            bank_db = BankManger().bank_details(bank_name=num)
            if bank_db is not None:
                return bank_db["bank_short_name"]
            else:
                return "END Invalid Bank Name"

    except KeyError:
        return "END Invalid option. please try again"


def select_bank_telco(num=None):
    if num is None:
        response = "CON Select bank for payout:\n"
        response += "1.Access. 2.Eco\n"
        response += "3.FistBnk. 4.FCMB\n"
        response += "5.GTB. 6.Heritage\n"
        response += "7.Keystone. 8.Stanbic\n"
        response += "9.UBA 10.Union.\n"
        response += "11.Zenith 12.STERLING.\n"
        response += "13.UNITY 14.WEMA.\n"
        response += "15.VFD 16.Others\n"
        return response

    _banks = {
        1: "access",
        2: "ecobank",
        3: "firstbank",
        4: "fcmb",
        5: "gtb",
        6: "heritage",
        7: "keystone",
        8: "stanbic",
        9: "uba",
        10: "unionbank",
        11: "zenith",
        12: "sterling",
        13: "unity",
        14: "wema",
        15: "vfd",
        16: "CON Enter Bank Name:\n",
    }

    try:
        if num.isdigit():
            no = int(num)
            return _banks[no]

        else:
            bank_db = BankManger().bank_details(bank_name=num)
            if bank_db is not None:
                return bank_db["bank_short_name"]
            else:
                return "END Invalid Bank Name"

    except KeyError:
        return "END Invalid option. please try again"


BANK_OPTIONS = {
    "1": "Access Bank Nigeria",
    "2": "Ecobank Bank ",
    "3": "First Bank of Nigeria",
    "4": "First City Monument Bank",
    "5": "Guaranty Trust Bank",
    "6": "Heritage Bank",
    "7": "Keystone",
    "8": "Stanbic",
    "9": "United Bank for Africa",
    "10": "Union",
    "11": "Zenith",
    "12": "Sterling",
    "13": "Unity",
    "14": "Wema",
    # "15": "Others",
}

yes_option = """END We have received your request.
If your application is selected,
you will be notified by SMS.
Thank you for choosing WinWise."""

no_option = "END Process Canceled. Thank you. \n"
no_option += f"you can visit {settings.LOTTO_FRONTEND_LINK} for more info"


def format_date_of_birth(dob):
    day = dob[:2]
    birth_month = dob[3:5]
    birth_year = dob[-4:]

    bvn_date = f"{birth_year}-{birth_month}-{day}"
    formated_date = datetime.strptime(bvn_date, "%Y-%m-%d").date()
    return formated_date


def age(birthdate):
    birthdate = format_date_of_birth(birthdate)
    today = date.today()
    age = today.year - birthdate.year - ((today.month, today.day) < (birthdate.month, birthdate.day))
    return age


# def lines_jackpot_and_amount_helper(jackpot):

#     int_count = int(jackpot)

#     if int_count == 1:
#         amount = 100
#         stake_amount = 100

#     elif int_count == 2:
#         amount = 150
#         stake_amount = 300

#     elif int_count == 3:
#         amount = 200
#         stake_amount = 600

#     elif int_count == 4:
#         amount = 300
#         stake_amount = 1200

#     elif int_count == 5:
#         amount = 400
#         stake_amount = 2000

#     elif int_count == 6:
#         amount = 400
#         stake_amount = 2400

#     elif int_count == 7:
#         amount = 400
#         stake_amount = 2800

#     elif int_count == 8:
#         amount = 400
#         stake_amount = 3200

#     elif int_count == 9:
#         amount = 400
#         stake_amount = 3600

#     elif int_count == 10:
#         amount = 500
#         stake_amount = 5000

#     expected_result = {"amount": amount, "stake_amount": stake_amount}
#     return expected_result


def stake_amount(jackpot):
    jackpot_selected_num = int(jackpot)
    AMOUNT = 150
    POTENTIAL_WINING = 6120.00
    if jackpot_selected_num == 1:
        amount = AMOUNT
        stake_amount = AMOUNT
        potential_winning = POTENTIAL_WINING

    elif jackpot_selected_num > 1:
        amount = AMOUNT
        stake_amount = AMOUNT * jackpot_selected_num
        potential_winning = POTENTIAL_WINING * jackpot_selected_num

    expected_result = {
        "amount": amount,
        "stake_amount": stake_amount,
        "potential_winning": potential_winning,
    }
    return expected_result


def lines(line):
    if int(line) == 1:
        num_lines = 3
    elif int(line) == 2:
        num_lines = 7
    elif int(line) == 3:
        num_lines = 10
    return num_lines


@dataclass
class RedBiller:
    @staticmethod
    def account_lookup(account_no, bank_code):
        url = "https://api.test.redbiller.com/1.2/kyc/bank-account/lookup"

        payload = json.dumps(
            {
                "account_no": f"{account_no}",
                "bank_code": f"{bank_code}",
                "reference": "xzsDSxODczwr",
            }
        )
        headers = {
            "Private-Key": f"{settings.RED_BILLER_PRIVATE_KEY}",
            "Content-Type": "application/json",
            "Authorization": f"Bearer {settings.RED_BILLER_AUTH_BEARER}",
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        return json.loads(response.text)

    # @staticmethod
    # def find_bank(account_no, reference):

    #     url = "https://services.libertypayng.com"

    #     payload = json.dumps(
    #         {
    #             "account_no": f"{account_no}",
    #             "bank_code": f"{bank_code}",
    #             "reference": "xzsDSxODczwr",
    #         }
    #     )
    #     headers = {
    #         "Private-Key": f"{settings.RED_BILLER_PRIVATE_KEY}",
    #         "Content-Type": "application/json",
    #         "Authorization": f"Bearer {settings.RED_BILLER_AUTH_BEARER}",
    #     }

    #     response = requests.request("POST", url, headers=headers, data=payload)

    #     return json.loads(response.text)
    # return response.text

    @classmethod
    def get_account_info(cls, account_no, bank_code):
        info = cls.account_lookup(account_no, bank_code)

        if info.get("response") == 200 and info.get("status") == "true":
            bank = info["details"].get("bank")
            personal = info["details"].get("personal")

            detail = {
                "date_of_birth": personal["date_of_birth"],
                "nationality": personal["nationality"],
                "account_name": bank["account_name"],
                "account_no": bank["account_no"],
                "bank_name": bank["bank_name"],
            }
            return detail
        else:
            return None


def text_handler(num):
    """
    use this function to take user back on ussd session
    """
    x = num.split("*")
    response = None

    while "01" in x:
        index_of_x = x.index("01")
        n_x = x.index("01") - 2
        x.pop(index_of_x)

        print("x", x)
        print("n_x", n_x)
        if len(x) > 0:
            x.pop(n_x)

        response = "*".join(x)
        if x:
            if x[-1] != "11" and x[0] == "11":
                print("x[-1] != 11 and x[0] == 11")
                index_of_x = x.index("11")
                n_x = x.index("11") - 1
                x.pop(index_of_x)
                response = "*".join(x)

    while "02" in x:
        index_of_x = x.index("02")
        n_x = x.index("02") - 2
        x.pop(index_of_x)

        if len(x) > 0:
            x.pop(n_x)

        response = "*".join(x)
        if x:
            if x[-1] != "11" and x[0] == "11":
                print("x[-1] != 11 and x[0] == 11")
                index_of_x = x.index("11")
                n_x = x.index("11") - 1
                x.pop(index_of_x)
                response = "*".join(x)

    while "0" in x:
        # print("0 in x")
        index_of_x = x.index("0")
        # print("index_of_x", index_of_x)
        n_x = x.index("0") - 1
        # print("n_x", n_x)
        # print("x", x)
        x.pop(index_of_x)
        # print("x", x)

        if len(x) > 0:
            x.pop(n_x)

        response = "*".join(x)
        if x:
            if x[-1] != "11" and x[0] == "11":
                print("x[-1] != 11 and x[0] == 11")
                index_of_x = x.index("11")
                n_x = x.index("11") - 1
                x.pop(index_of_x)
                response = "*".join(x)

    # if x:
    #     if x[-1] != "11" and x[0] == "11":
    #         print("x[-1] != 11 and x[0] == 11")
    #         index_of_x = x.index("11")
    #         n_x = x.index("11") - 1
    #         x.pop(index_of_x)
    #         response = "*".join(x)

    if response is None:
        response = "*".join(x)
        return response

    return response


# {
#     "bank_code": "000014",
#     "cbn_code": "044",
#     "name": "Access Bank Nigeria",
#     "bank_short_name": "access",
#     "ussd_code": "*901#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/access.png",
# }, {
#     "bank_code": "090001",
#     "cbn_code": "401",
#     "name": "Aso Savings And Loans",
#     "bank_short_name": "aso-savings",
#     "ussd_code": "",
#     "logo": "https://nigeria-banks.onrender.com/static/images/aso-savings.png",
# }, {
#     "bank_code": "090154",
#     "cbn_code": "50823",
#     "name": "CEMCS MFB",
#     "bank_short_name": "cemcs",
#     "ussd_code": "",
#     "logo": "https://nigeria-banks.onrender.com/static/images/cemcs.png",
# }, {
#     "bank_code": "000009",
#     "cbn_code": "023",
#     "name": "Citibank Nigeria",
#     "bank_short_name": "citibankng",
#     "ussd_code": "",
#     "logo": "https://nigeria-banks.onrender.com/static/images/citibankng.png",
# }, {
#     "bank_code": "000010",
#     "cbn_code": "050",
#     "name": "Ecobank Bank",
#     "bank_short_name": "ecobank",
#     "ussd_code": "*326#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/ecobank.png",
# }, {
#     "bank_code": "090097",
#     "cbn_code": "562",
#     "name": "Ekondo MFB",
#     "bank_short_name": "ekondo",
#     "ussd_code": "*540*178#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/ekondo.png",
# }, {
#     "bank_code": "000007",
#     "cbn_code": "070",
#     "name": "Fidelity Bank",
#     "bank_short_name": "fidelity",
#     "ussd_code": "*770#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/fidelity.png",
# }, {
#     "bank_code": "000016",
#     "cbn_code": "011",
#     "name": "First Bank of Nigeria",
#     "bank_short_name": "firstbank",
#     "ussd_code": "*894#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/firstbank.png",
# }, {
#     "bank_code": "000003",
#     "cbn_code": "214",
#     "name": "First City Monument Bank",
#     "bank_short_name": "fcmb",
#     "ussd_code": "*329#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/fcmb.png",
# }, {
#     "bank_code": "000013",
#     "cbn_code": "058",
#     "name": "Guaranty Trust Bank",
#     "bank_short_name": "gtb",
#     "ussd_code": "*737#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/gtb.png",
# }, {
#     "bank_code": "090121",
#     "cbn_code": "50383",
#     "name": "Hasal MFB",
#     "bank_short_name": "mfb",
#     "ussd_code": "*322*127#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/mfb.png",
# }, {
#     "bank_code": "000020",
#     "cbn_code": "030",
#     "name": "Heritage Bank",
#     "bank_short_name": "heritage",
#     "ussd_code": "*322#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/heritage.png",
# }, {
#     "bank_code": "000006",
#     "cbn_code": "301",
#     "name": "Jaiz Bank",
#     "bank_short_name": "jaiz",
#     "ussd_code": "*389*301#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/jaiz.png",
# }, {
#     "bank_code": "000002",
#     "cbn_code": "082",
#     "name": "Keystone Bank",
#     "bank_short_name": "keystone",
#     "ussd_code": "*7111#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/keystone.png",
# }, {
#     "bank_code": "090267",
#     "cbn_code": "50211",
#     "name": "Kuda MFB",
#     "bank_short_name": "kuda",
#     "ussd_code": "",
#     "logo": "https://nigeria-banks.onrender.com/static/images/kuda.png",
# }, {
#     "bank_code": "100026",
#     "cbn_code": "565",
#     "name": "One Finance",
#     "bank_short_name": "one-finace",
#     "ussd_code": "*1303#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/one-finace.png",
# }, {
#     "bank_code": "090004",
#     "cbn_code": "526",
#     "name": "Parallex MFB",
#     "bank_short_name": "parallex",
#     "ussd_code": "*322*318*0#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/parallex.png",
# }, {
#     "bank_code": "000008",
#     "cbn_code": "076",
#     "name": "Polaris Bank",
#     "bank_short_name": "PBL",
#     "ussd_code": "*833#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/PBL.png",
# }, {
#     "bank_code": "000023",
#     "cbn_code": "101",
#     "name": "Providus Bank",
#     "bank_short_name": "providus",
#     "ussd_code": "",
#     "logo": "https://nigeria-banks.onrender.com/static/images/providus.png",
# }, {
#     "bank_code": "090175",
#     "cbn_code": "125",
#     "name": "Rubies MFB",
#     "bank_short_name": "RMB",
#     "ussd_code": "*7797#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/RMB.png",
# }, {
#     "bank_code": "090325",
#     "cbn_code": "51310",
#     "name": "Sparkle Bank",
#     "bank_short_name": "SPKL",
#     "ussd_code": "",
# }, {
#     "bank_code": "000012",
#     "cbn_code": "221",
#     "name": "Stanbic IBTC",
#     "bank_short_name": "stanbic",
#     "ussd_code": "*909#",
# }, {
#     "bank_code": "000021",
#     "cbn_code": "068",
#     "name": "Standard Chartered Bank",
#     "bank_short_name": "standard-chartered",
#     "ussd_code": "",
# }, {
#     "bank_code": "000001",
#     "cbn_code": "232",
#     "name": "Sterling Bank",
#     "bank_short_name": "sterling",
#     "ussd_code": "*822#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/sterling.png",
# }, {
#     "bank_code": "000022",
#     "cbn_code": "100",
#     "name": "Suntrust Bank",
#     "bank_short_name": "suntrust",
#     "ussd_code": "*5230#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/suntrust.png",
# }, {
#     "bank_code": "000026",
#     "cbn_code": "302",
#     "name": "Taj Bank",
#     "bank_short_name": "taj-bank",
#     "ussd_code": "*898#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/taj-bank.png",
# }, {
#     "bank_code": "090115",
#     "cbn_code": "51211",
#     "name": "TCF MFB",
#     "bank_short_name": "tcf-bank",
#     "ussd_code": "*908#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/tcf-bank.png",
# }, {
#     "bank_code": "000025",
#     "cbn_code": "102",
#     "name": "Titan Trust Bank",
#     "bank_short_name": "titan-trust",
#     "ussd_code": "*922#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/titan-trust.png",
# }, {
#     "bank_code": "000018",
#     "cbn_code": "032",
#     "name": "Union Bank of Nigeria",
#     "bank_short_name": "unionbank",
#     "ussd_code": "*826#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/unionbank.png",
# }, {
#     "bank_code": "000004",
#     "cbn_code": "033",
#     "name": "United Bank for Africa",
#     "bank_short_name": "uba",
#     "ussd_code": "*919#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/uba.png",
# }, {
#     "bank_code": "000011",
#     "cbn_code": "215",
#     "name": "Unity Bank",
#     "bank_short_name": "unity-bank",
#     "ussd_code": "*7799#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/unity-bank.png",
# }, {
#     "bank_code": "090110",
#     "cbn_code": "566",
#     "name": "VFD MFB",
#     "bank_short_name": "vfd",
#     "ussd_code": "",
#     "logo": "https://nigeria-banks.onrender.com/static/images/vfd.png",
# }, {
#     "bank_code": "000017",
#     "cbn_code": "035",
#     "name": "Wema Bank",
#     "bank_short_name": "wema",
#     "ussd_code": "*945#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/wema.png",
# }, {
#     "bank_code": "000015",
#     "cbn_code": "057",
#     "name": "Zenith Bank",
#     "bank_short_name": "zenith",
#     "ussd_code": "*966#",
#     "logo": "https://nigeria-banks.onrender.com/static/images/zenith.png",
# }


@dataclass
class WyseCashLotteryPlayHelerp:
    def full_name_split(self, name):
        """
        This functions split and return user names in a dictonary
        """
        splited_names = name.split()
        names = {
            "first_name": splited_names[0] if len(splited_names) > 0 else "",
            "last_name": splited_names[1] if len(splited_names) > 1 else "",
            "middle_name": splited_names[2] if len(splited_names) > 2 else "",
            "full_name": name,
        }

        return names

    def generate_wyse_cash_lottery_ticket(self, phone):
        user_profile, created = UserProfile.objects.get_or_create(phone_number=phone)

        if user_profile:
            if user_profile.first_name and user_profile.last_name:
                name = user_profile.first_name + " " + user_profile.last_name
            elif user_profile.first_name != "" and user_profile.last_name == "":
                name = user_profile.first_name
            else:
                name = DommyName(6).generate_name()

        else:
            name = DommyName(6).generate_name()

        # name split
        names = self.full_name_split(name)

        lucky_number = f"{names.get('first_name')[0]}{names.get('last_name')[0]}-{Utility.generate_lucky_number()}"

        lottery_model_obj = LotteryModel.objects.filter(lucky_number=lucky_number)
        if lottery_model_obj.exists():
            return WyseCashLotteryPlayHelerp().generate_wyse_cash_lottery_ticket(phone)

        return lucky_number

    def create_wyse_cash_lottery(
        self,
        phone_number,
        band,
        pool,
        num_of_lottery_ticket,
        stake_amount,
        from_telco_play=False,
    ):
        phone = LotteryModel.format_number_from_back_add_234(phone_number)

        HopsRestrictor.increment_pending_payment(phone)  # RESTRICTOR

        current_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH", is_active=True).last()
        if not current_batch:
            current_batch = LotteryBatch.objects.create(lottery_type="WYSE_CASH", is_active=True)

        unique_id = f"{current_batch.batch_uuid[0:5]}-{str(uuid.uuid4())[0:5]}"

        get_game_play_id = generate_game_play_id()
        identity_id = f"{uuid.uuid4()}{int(time.time())}"

        user_profile, created = UserProfile.objects.get_or_create(phone_number=phone, from_telco=True)

        for i in range(0, num_of_lottery_ticket):
            lucky_number = self.generate_wyse_cash_lottery_ticket(phone)

            wyse_cash_price_model = WyseCashPriceModel().ticket_price(
                channel="USSD", band=band, no_of_line=num_of_lottery_ticket
            )
            if wyse_cash_price_model is None:
                return None

            expected_amount = wyse_cash_price_model.get("total_amount", 0)
            if from_telco_play is False:
                _stake_amount = wyse_cash_price_model.get("stake_amount") / num_of_lottery_ticket
            else:
                wyse_cash_price_model = TelcoWyseCashPriceModel().ticket_price(
                    band=band, no_of_line=num_of_lottery_ticket
                )
                _stake_amount = wyse_cash_price_model.get("amount_to_register")

            # actual_stake_amount =

            instance = LotteryModel.objects.create(
                user_profile=user_profile,
                batch=current_batch,
                band=band,
                phone=phone,
                pool=pool,
                stake_amount=_stake_amount,
                lucky_number=lucky_number,
                consent=True,
                unique_id=unique_id,
                instance_number=num_of_lottery_ticket,
                channel="USSD",
                expected_amount=expected_amount / num_of_lottery_ticket,
                game_play_id=get_game_play_id,
                lottery_type="WYSE_CASH",
                identity_id=identity_id,
                played_via_telco_channel=from_telco_play,
            )

        return instance


def ussd_lottery_play(phone_number, lottery_type, text):
    """

    This function is used to play ussd lottery

    """

    # WYSE CASH
    if lottery_type == "wyse_cash":
        band, selection = text.split("*")[-2], text.split("*")[-1]

        if band == "1":  # 10,000 band
            create_wyse_cash_obj = WyseCashLotteryPlayHelerp().create_wyse_cash_lottery(
                phone_number, 10000, "TEN_THOUSAND", int(selection), 100
            )

            # save game play id in redis
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-gameplayid")
            redis_storage.set_data(create_wyse_cash_obj.game_play_id)

            # save band in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-band")
            redis_storage.set_data(create_wyse_cash_obj.band)

            # save number of tictet in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-numberofticket")
            redis_storage.set_data(create_wyse_cash_obj.instance_number)

            # save stake amount in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-stakeamount")
            redis_storage.set_data(create_wyse_cash_obj.stake_amount)

        elif band == "2":
            create_wyse_cash_obj = WyseCashLotteryPlayHelerp().create_wyse_cash_lottery(
                phone_number, 50000, "FIFTY_THOUSAND", int(selection), 200
            )

            # save game play id in redis
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            redis_storage = RedisStorage(f"{phone}-lotteryplay-gameplayid")
            redis_storage.set_data(create_wyse_cash_obj.game_play_id)

            # save band in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-band")
            redis_storage.set_data(create_wyse_cash_obj.band)

            # save number of tictet in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-numberofticket")
            redis_storage.set_data(create_wyse_cash_obj.instance_number)

            # save stake amount in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-stakeamount")
            redis_storage.set_data(create_wyse_cash_obj.stake_amount)

        elif band == "3":
            create_wyse_cash_obj = WyseCashLotteryPlayHelerp().create_wyse_cash_lottery(
                phone_number, 250000, "TWO_FIFTY_THOUSAND", int(selection), 500
            )

            # save game play id in redis
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            redis_storage = RedisStorage(f"{phone}-lotteryplay-gameplayid")
            redis_storage.set_data(create_wyse_cash_obj.game_play_id)

            # save band in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-band")
            redis_storage.set_data(int(create_wyse_cash_obj.band) * int(create_wyse_cash_obj.instance_number))

            # save number of tictet in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-numberofticket")
            redis_storage.set_data(create_wyse_cash_obj.instance_number)

            # save stake amount in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-stakeamount")
            redis_storage.set_data(create_wyse_cash_obj.stake_amount)

        elif band == "4":
            create_wyse_cash_obj = WyseCashLotteryPlayHelerp().create_wyse_cash_lottery(
                phone_number, 500000, "FIVE_HUNDRED_THOUSAND", int(selection), 1000
            )

            # save game play id in redis
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            redis_storage = RedisStorage(f"{phone}-lotteryplay-gameplayid")
            redis_storage.set_data(create_wyse_cash_obj.game_play_id)

            # save band in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-band")
            redis_storage.set_data(int(create_wyse_cash_obj.band) * int(create_wyse_cash_obj.instance_number))

            # save number of tictet in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-numberofticket")
            redis_storage.set_data(create_wyse_cash_obj.instance_number)

            # save stake amount in redis
            redis_storage = RedisStorage(redis_key=f"{phone}-lotteryplay-stakeamount")
            redis_storage.set_data(create_wyse_cash_obj.stake_amount)

        return create_wyse_cash_obj


def update_user_bank_details(phone_number, bank_name):
    phone = LotteryModel.format_number_from_back_add_234(phone_number)
    user_profile = UserProfile.objects.filter(phone_number=phone).last()

    bank_db = BankManger().bank_details(bank_name=bank_name)

    if bank_db:
        if user_profile:
            user_profile.bank_code = bank_db.get("cbn_code")
            user_profile.bank_name = bank_db.get("name")
            user_profile.save()
            return True
    return False


def ussd_lottery_play2(phone_number, lottery_type, text, from_telco_play=False):
    from wyse_ussd.helper.mega_cash3 import wyse_cash_amount

    """

    This function is used to play ussd lottery

    """

    # WYSE CASH
    if lottery_type == "wyse_cash":
        band, selection = text.split("*")[1], text.split("*")[-1]

        if not selection.isdigit():
            return False

        if band == "1":  # 10,000 band
            create_wyse_cash_obj = WyseCashLotteryPlayHelerp().create_wyse_cash_lottery(
                phone_number,
                10000,
                "TEN_THOUSAND",
                int(selection),
                wyse_cash_amount(text) / int(selection),
                from_telco_play=from_telco_play,
            )
        elif band == "2":
            create_wyse_cash_obj = WyseCashLotteryPlayHelerp().create_wyse_cash_lottery(
                phone_number,
                50000,
                "FIFTY_THOUSAND",
                int(selection),
                wyse_cash_amount(text) / int(selection),
                from_telco_play=from_telco_play,
            )

        elif band == "3":
            create_wyse_cash_obj = WyseCashLotteryPlayHelerp().create_wyse_cash_lottery(
                phone_number,
                100000,
                "ONE_HUNDRED_THOUSAND",
                int(selection),
                wyse_cash_amount(text) / int(selection),
                from_telco_play=from_telco_play,
            )

        elif band == "4":
            create_wyse_cash_obj = WyseCashLotteryPlayHelerp().create_wyse_cash_lottery(
                phone_number,
                200000,
                "TWO_HUNDRED_THOUSAND",
                int(selection),
                wyse_cash_amount(text) / int(selection),
                from_telco_play=from_telco_play,
            )

        return create_wyse_cash_obj


def ussd_lottery_play2_subscription(phone_number, lottery_type, text, from_telco_play=False):
    from wyse_ussd.helper.mega_cash3 import telco_sub_wyse_cash_amount

    """

    This function is used to play ussd lottery

    FOR TELCO SUBSCRIPTION FEATURE, WE'RE ONLY CONSIDERING ONLY THE 10,000 BAND


    """

    # WYSE CASH
    if lottery_type == "wyse_cash":
        band, selection = "1", "1"

        if not selection.isdigit():
            return False

        if band == "1":  # 10,000 band
            create_wyse_cash_obj = WyseCashLotteryPlayHelerp().create_wyse_cash_lottery(
                phone_number,
                10000,
                "TEN_THOUSAND",
                int(selection),
                telco_sub_wyse_cash_amount(text) / int(selection),
                from_telco_play=from_telco_play,
            )

        return create_wyse_cash_obj


now = datetime.now()
week_name = str(now.strftime("%A"))[:3]
get_current_date = now.strftime("%d/%m/%y")

# get current time + 5 minutes
get_current_time = ((datetime.now() + timedelta(minutes=5)).time()).strftime("%H:%M")
current_time = datetime.strptime(str(get_current_time), "%H:%M")
current_time = current_time.strftime("%I:%M %p")


def summary(phone_number):
    phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

    # user = UserProfile.objects.filter(phone_number=phone_number)
    account = WovenAccountDetail.objects.filter(phone_number=phone_number).last()

    ussd_instance = UssdLotteryPayment.objects.filter(user__phone_number=phone_number).last()

    if ussd_instance:
        game_id = ussd_instance.game_play_id

        lottery_model = LotteryModel.objects.filter(game_play_id=game_id).last()
        lotto_ticket = LottoTicket.objects.filter(game_play_id=game_id).last()

        if lottery_model is None:
            lottery_instance = lotto_ticket
        elif lotto_ticket is None:
            lottery_instance = lottery_model

        game_type = (lottery_instance.lottery_type).replace("_", " ").title()

        if lottery_instance.lottery_type == "WYSE_CASH":
            pontential_winning = LotteryModel.objects.filter(game_play_id=game_id).aggregate(Sum("band"))["band__sum"]
            summary = f"""END Summary: {game_type}\n
            Stake Amount :  {lottery_instance.stake_amount}
            Potential Win: {pontential_winning}
            Complete your ticket\nPlease pay into:
            Bank: Wema
            Account No: {account.vnuban}\n
            Next Draw: {week_name} {get_current_date} 8 pm
            """
        else:
            summary = f"""END Summary: {game_type}\n
            Stake Amount :  {lottery_instance.stake_amount}
            Potential Win: {lottery_instance.potential_winning}
            Complete your ticket\nPlease pay into:
            Bank: Wema
            Account No: {account.vnuban}\n
            Next Draw: {week_name} {get_current_date} 8 pm
            """

        return summary


def lottery_ticket_result(game_play_id):
    # check lotto ticket
    lotto_ticket = LottoTicket.objects.filter(game_play_id=game_play_id).last()
    if lotto_ticket:
        # check if lotto batch is still on
        if lotto_ticket.batch.is_active is True:
            return "END Your ticket is still on. Please check back later"
        else:
            # check if this lotto ticket won
            winner_table_instance = LottoWinners.objects.filter(game_play_id=game_play_id).last()

            if winner_table_instance:
                return f"END Your ticket won {Utility.currency_formatter(winner_table_instance.earning)}"

            else:
                return "END Your ticket did not win. sorry, try again. dail *347*800# to play again"

    else:
        wyse_cash_ticket = LotteryModel.objects.filter(game_play_id=game_play_id).last()
        if wyse_cash_ticket:
            if wyse_cash_ticket.batch.is_active is True:
                return "END Your ticket is still on. Please check back later"

            else:
                # check if this lotto ticket won
                winner_table_instance = LotteryWinnersTable.objects.filter(game_play_id=game_play_id).last()

                if winner_table_instance:
                    return f"END Your ticket won {Utility.currency_formatter(winner_table_instance.earning)}"

                else:
                    return "END Your ticket did not win. sorry, try again. dail *347*800# to play again"

        else:
            return "END Invalid game play id"


def has_enough_money_to_giveout(phone, amount) -> bool:
    if GeneralWithdrawableWallet.get_withdrawable_wallet_amount() < float(amount):
        return False
    else:
        return True


def retail_has_enough_money_to_give_out(amount):
    return True
    if Wallet.get_retail_rtp_balance() < float(amount):
        return False
    else:
        return True


def has_enough_money_to_give_non_retail_users(amount):
    return True
    if Wallet.get_non_retail_wallet_balance() < float(amount):
        return False
    else:
        return True
