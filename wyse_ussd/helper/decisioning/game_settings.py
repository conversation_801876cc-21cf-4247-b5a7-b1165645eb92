from main.models import ConstantVariable

const = ConstantVariable.get_constant_variable()

RTO = const.get("rto") if const else 0  # THIS IS THE PERCENTAGE VALUE FOR RETURN TO OWNER
RTP = 100 - RTO  # THIS IS THE PERCENTAGE VALUE FOR RETURN TO PLAYER
# print("RTP AT SETTINGS === ", RTP)
PLAYS = 5000  # NUMBER OF PLAYERS IN A SESSION


GAME_BANDS = {
    200: {
        "band": 200,
        "win_amount": 1500,
    },
    500: {
        "band": 500,
        "win_amount": 5000,
    },
    1000: {
        "band": 1000,
        "win_amount": 12000,
    },
    2000: {
        "band": 2000,
        "win_amount": 20000,
    },
}
