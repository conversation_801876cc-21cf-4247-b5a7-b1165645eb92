import base64
import json
import random

import requests
from django.conf import settings

from pos_app.models import LottoAgentGuarantorDetail
from pos_app.tasks import send_guarantor_verification_details
from wyse_ussd.models import random_with_N_digits

SAMPLE_DATE_OF_BIRTH = [
    "1990-09-29",
    "1980-08-08",
    "1975-12-05",
    "1970-01-10",
    "1965-05-25",
    "1989-04-22",
]


class RedbillerHelper:
    def __init__(self):
        self.redbiller_url = f"{settings.LIBERTY_VAS_BASE_URL}/redbiller/merchant/know_your_customer/"
        username = settings.LIBERTY_VAS_AUTH_USERNAME
        password = settings.LIBERTY_VAS_AUTH_PASSWORD

        STRING_VALUE = f"{username}:{password}"
        AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Basic {AUTH_TOKEN}",
        }

    def nin_verification(self, unique_ussd_code, **kwargs):
        """
        NATIONAL IDENTIFICATION NUMBER
        Requires:
        - customer's first name
        - customer's surname
        - customer's phone number
        - 11 digits National Identification Number
        - unique reference
        Params:
        - first_name
        - surname
        - phone_no
        - nin
        - reference
        """
        payload = json.dumps(
            {
                "kyc_type": "NIN",
                "first_name": kwargs.get("first_name"),
                "surname": kwargs.get("surname"),
                "phone_number": kwargs.get("phone_number"),
                "nin": kwargs.get("nin"),
                "reference": f"gua-kyc{random_with_N_digits()}",
            }
        )
        redbiller_response = requests.request("POST", self.redbiller_url, headers=self.headers, data=payload)
        try:
            response = redbiller_response.json()
        except Exception:
            response = redbiller_response.text

        if isinstance(response, dict) and redbiller_response.status_code == 200:
            if (
                response.get("message") == "Verified successfully."
                or "successfully" in response.get("message")
                and response.get("meta").get("status") == "Approved"
            ):
                verified_first_name = response.get("details").get("first_name")
                verified_last_name = response.get("details").get("surname")
                verified_address = None
                verified_id_number = response.get("details").get("nin")

                guarantor = LottoAgentGuarantorDetail.objects.filter(unique_code=unique_ussd_code).first()
                guarantor.verified = True
                guarantor.means_of_verification = "NIN"
                guarantor.guarantor_id_number = verified_id_number
                guarantor.guarantor_verified_first_name = verified_first_name
                guarantor.guarantor_verified_last_name = verified_last_name
                guarantor.guarantor_verified_address = verified_address
                guarantor.payload = json.dumps(response)
                guarantor.save()

                send_guarantor_verification_details.delay(verification_id=guarantor.verification_id)
                return True
            else:
                False
        else:
            return False

    def bvn_verification(self, unique_ussd_code, bvn_id):
        """
        BANK VERIFICATION NUMBER
        Requires:
        - 11 digits Bank Verification Number.
        - unique reference
        Params:
        - bvn
        - reference
        """
        payload = json.dumps(
            {
                "kyc_type": "BVN",
                "bvn": bvn_id,
                "reference": f"gua-kyc{random_with_N_digits()}",
            }
        )
        redbiller_response = requests.request("POST", self.redbiller_url, headers=self.headers, data=payload)
        try:
            response = redbiller_response.json()
        except Exception:
            response = redbiller_response.text

        if isinstance(response, dict) and redbiller_response.status_code == 200:
            if (
                response.get("message") == "Verified successfully."
                or "successfully" in response.get("message")
                and response.get("meta").get("status") == "Approved"
            ):
                verified_first_name = response.get("details").get("personal").get("first_name")
                verified_last_name = response.get("details").get("personal").get("surname")
                verified_address = response.get("details").get("contact").get("residential_address")
                verified_id_number = response.get("details").get("identification").get("bvn")

                guarantor = LottoAgentGuarantorDetail.objects.filter(unique_code=unique_ussd_code).first()
                guarantor.verified = True
                guarantor.means_of_verification = "BVN"
                guarantor.payload = json.dumps(response)
                guarantor.guarantor_verified_first_name = verified_first_name
                guarantor.guarantor_verified_last_name = verified_last_name
                guarantor.guarantor_verified_address = verified_address
                guarantor.guarantor_id_number = verified_id_number
                guarantor.save()

                send_guarantor_verification_details.delay(verification_id=guarantor.verification_id)
                return True
            else:
                False
        else:
            return False

    def voters_card_verification(self, unique_ussd_code, **kwargs):
        """
        VOTER'S CARD
        Requires:
        - customer's first name
        - customer's surname / last name
        - voter's card identification number
        - date of birth (YYYY-MM-DD)
        - unique reference
        Params:
        - first_name
        - surname
        - vin
        - date_of_birth
        - reference
        """
        payload = json.dumps(
            {
                "kyc_type": "VOTER_CARD",
                "first_name": kwargs.get("first_name"),
                "surname": kwargs.get("surname"),
                "vin": kwargs.get("vin"),
                "date_of_birth": random.choice(SAMPLE_DATE_OF_BIRTH),
                "reference": f"gua-kyc{random_with_N_digits()}",
            }
        )
        redbiller_response = requests.request("POST", self.redbiller_url, headers=self.headers, data=payload)
        try:
            response = redbiller_response.json()
        except Exception:
            response = redbiller_response.text

        if isinstance(response, dict) and redbiller_response.status_code == 200:
            if (
                response.get("message") == "Verified successfully."
                or "successfully" in response.get("message")
                and response.get("meta").get("status") == "Approved"
            ):
                verified_first_name = response.get("details").get("first_name")
                verified_last_name = response.get("details").get("surname")
                verified_address = None
                verified_id_number = response.get("details").get("vin")

                guarantor = LottoAgentGuarantorDetail.objects.filter(unique_code=unique_ussd_code).first()
                guarantor.verified = True
                guarantor.means_of_verification = "VOTER_CARD"
                guarantor.payload = json.dumps(response)
                guarantor.guarantor_verified_first_name = verified_first_name
                guarantor.guarantor_verified_last_name = verified_last_name
                guarantor.guarantor_verified_address = verified_address
                guarantor.guarantor_id_number = verified_id_number
                guarantor.save()

                send_guarantor_verification_details.delay(verification_id=guarantor.verification_id)
                return True
            else:
                False
        else:
            return False

    def international_passport_verification(self, unique_ussd_code, **kwargs):
        """
        INTERNATIONAL PASSPORT
        Requires:
        - customer's first name
        - customer's surname / last name
        - passport no
        - phone number
        - date of birth (YYYY-MM-DD)
        - unique reference
        Params:
        - first_name
        - surname
        - passport_no
        - phone_no
        - date_of_birth
        - reference
        """
        payload = json.dumps(
            {
                "kyc_type": "INTERNATIONAL_PASSPORT",
                "first_name": kwargs.get("first_name"),
                "surname": kwargs.get("surname"),
                "passport_no": kwargs.get("passport_no"),
                "phone_no": kwargs.get("phone_no"),
                "date_of_birth": random.choice(SAMPLE_DATE_OF_BIRTH),
                "reference": f"gua-kyc{random_with_N_digits()}",
            }
        )
        redbiller_response = requests.request("POST", self.redbiller_url, headers=self.headers, data=payload)
        try:
            response = redbiller_response.json()
        except Exception:
            response = redbiller_response.text

        if isinstance(response, dict) and redbiller_response.status_code == 200:
            if (
                response.get("message") == "Verified successfully."
                or "successfully" in response.get("message")
                and response.get("meta").get("status") == "Approved"
            ):
                verified_first_name = response.get("details").get("first_name")
                verified_last_name = response.get("details").get("surname")
                verified_address = None
                verified_id_number = response.get("details").get("passport_no")

                guarantor = LottoAgentGuarantorDetail.objects.filter(unique_code=unique_ussd_code).first()
                guarantor.verified = True
                guarantor.means_of_verification = "INTERNATIONAL_PASSPORT"
                guarantor.payload = json.dumps(response)
                guarantor.guarantor_verified_first_name = verified_first_name
                guarantor.guarantor_verified_last_name = verified_last_name
                guarantor.guarantor_verified_address = verified_address
                guarantor.guarantor_id_number = verified_id_number
                guarantor.save()

                send_guarantor_verification_details.delay(verification_id=guarantor.verification_id)
                return True
            else:
                False
        else:
            return False
