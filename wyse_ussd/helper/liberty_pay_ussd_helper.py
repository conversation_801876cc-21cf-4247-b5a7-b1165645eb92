import random

from main.models import SalaryForLifeParticipant
from main.ussd.helpers import Utility
from scratch_cards.models import ScratchCard
from wyse_ussd.helper.bank import BankManger
from wyse_ussd.helper.general_helper import select_bank
from wyse_ussd.helper.scratch_card_ussd_helper import UssdScratchCardHelper
import json
from django.conf import settings
import requests

from wyse_ussd.lottery_ticket_ussd_interface import lottery_ticket_ussd

def liberty_pay_ussd_menu_imteraction(phone_number: str, service_code: str, text: str, session_id: str, network_code: str):
    splited_text = text.split("*")
    splited_text = [i for i in splited_text if i != ""]

    if text.startswith("47"):
        if text == "47":
            response = "CON Please, enter your ticket game id\n"

            return response
        else:
            if len(splited_text) == 2:
                response = "CON Please, enter your ticket pin\n"
                return response

            game_id = splited_text[1]
            pin = splited_text[2]

            try:
                int(game_id)
                int(pin)
            except Exception:
                return "END Invalid input. game pin or game id has to be a number"

            hashed_pin = f"password{pin}"
            hashed_pin = str(int(pin))  # hashlib.md5(hashed_pin.encode()).hexdigest()

            scratch_card_instance = ScratchCard.objects.filter(pin=hashed_pin, game_id=int(game_id), paid=True).last()

            if scratch_card_instance is None:
                # response = "END Ticket not found."
                # return response
                response = lottery_ticket_ussd(phone_number, service_code, text, session_id, network_code)
                return response
                
            if len(splited_text) == 3:
                response = "CON Please, enter your ticket serial no.\n"
                return response

            if len(splited_text) == 4:
                game_id = splited_text[1]
                pin = splited_text[2]

                try:
                    int(game_id)
                    int(pin)
                except Exception:
                    return "END Invalid input. game pin or game id has to be a number"

                hashed_pin = f"password{pin}"
                hashed_pin = str(int(pin))  # hashlib.md5(hashed_pin.encode()).hexdigest()

                serial_no = splited_text[3]
                letters, numbers = ScratchCard.separate_letters_and_numbers(serial_no)

                if "s" in letters.lower():
                    letters = "S"

                if "q" in letters.lower():
                    letters = "Q"

                numbers = int(numbers)

                try:
                    scratch_card_instance = ScratchCard.objects.get(
                        pin=hashed_pin, game_id=int(game_id), paid=True, serial_skew__iexact=letters, index=numbers
                    )
                except ScratchCard.DoesNotExist:
                    response = "END Invalid game id"
                    return response

                winning_status = "pending"

                if str(scratch_card_instance.earning).upper() == "WON":
                    winning_status = "winning"

                elif str(scratch_card_instance.earning).upper() == "LOST":
                    # winning_status = "lost"
                    if scratch_card_instance.winning_status is None:
                        winning_status = random.choices(
                            ["lost", "daily draw", "weekly draw", "weekly draw", "lost", "daily draw", "lost", "daily draw", "weekly draw", "lost"]
                        )
                        scratch_card_instance.winning_status = winning_status
                        scratch_card_instance.save()
                    else:
                        winning_status = scratch_card_instance.winning_status

                if winning_status == "pending":
                    message = "END please wait, this ticket has not been drawn yet"

                elif winning_status == "winning":
                    if scratch_card_instance.player_phone_number is not None:
                        if scratch_card_instance.player_phone_number != phone_number:
                            message = "END Sorry, this ticket has already been claimed."
                        else:
                            message = f"END this is a winning ticket.\nAmount won: {scratch_card_instance.earning_amount}. Dial this code to withdraw your winnings *347*180*89#"

                    else:
                        scratch_card_instance.player_phone_number = phone_number
                        scratch_card_instance.save()

                        message = f"END this is a winning ticket.\nAmount won: {scratch_card_instance.earning_amount}. Dial this code to withdraw your winnings *347*180*89#"
                else:
                    message = "END this is a lost ticket. sorry try again.!"

                return message

            else:
                response = "END Invalid command."
                return response

    elif str(text).strip().startswith("89"):
        if text == "89":
            response = "CON Welcome to your scratch card winning withdrawal\n"
            response += "Enter your card serial number\n"

            return response

        if len(splited_text) == 2:
            serial_number = splited_text[-1]

            letters, numbers = ScratchCard.separate_letters_and_numbers(serial_number)
            if "s" in letters.lower():
                letters = "S"

            if "q" in letters.lower():
                letters = "Q"

            numbers = int(numbers)

            try:
                scratch_card_instance = ScratchCard.objects.get(
                    serial_skew__iexact=letters, index=numbers, player_phone_number=phone_number, claimed=False
                )
            except ScratchCard.DoesNotExist:
                response = lottery_ticket_ussd(phone_number, service_code, text, session_id, network_code)
                return response
                # return "END Invalid serial number or no ticket found"

            response = f"CON Your winning amount is: {Utility.currency_formatter(scratch_card_instance.earning_amount)}\n"
            response += "1. Continue with your withdrawal\n"
            response += "2. Exit\n"
            response += "99. Back"

            return response

        if len(splited_text) == 3:
            if splited_text[-1] == "2":
                return "END Thank you."

            serial_number = splited_text[1]

            letters, numbers = ScratchCard.separate_letters_and_numbers(serial_number)
            if "s" in letters.lower():
                letters = "S"

            if "q" in letters.lower():
                letters = "Q"

            numbers = int(numbers)

            try:
                scratch_card_instance = ScratchCard.objects.get(
                    serial_skew__iexact=letters, index=numbers, player_phone_number=phone_number, claimed=False
                )
            except ScratchCard.DoesNotExist:
                response = lottery_ticket_ussd(phone_number, service_code, text, session_id, network_code)
                return response
                # return "END Invalid serial number or no ticket found"

            response = select_bank()

            return response

        if len(splited_text) == 4:
            serial_number = splited_text[1]

            letters, numbers = ScratchCard.separate_letters_and_numbers(serial_number)
            if "s" in letters.lower():
                letters = "S"

            if "q" in letters.lower():
                letters = "Q"

            numbers = int(numbers)

            try:
                scratch_card_instance = ScratchCard.objects.get(
                    serial_skew__iexact=letters, index=numbers, player_phone_number=phone_number, claimed=False
                )
            except ScratchCard.DoesNotExist:
                response = lottery_ticket_ussd(phone_number, service_code, text, session_id, network_code)
                return response
                # return "ENDInvalid serial number or no ticket found"

            bank = select_bank(splited_text[3])
            bank_db = BankManger().bank_details(bank_name=bank)

            if not bank_db:
                return "END Invalid bank"

            return "CON Enter your account number \n"

        if len(splited_text) == 5:
            serial_number = splited_text[1]

            letters, numbers = ScratchCard.separate_letters_and_numbers(serial_number)
            if "s" in letters.lower():
                letters = "S"

            if "q" in letters.lower():
                letters = "Q"

            numbers = int(numbers)

            try:
                scratch_card_instance = ScratchCard.objects.get(
                    serial_skew__iexact=letters, index=numbers, player_phone_number=phone_number, claimed=False
                )
            except ScratchCard.DoesNotExist:
                response = lottery_ticket_ussd(phone_number, service_code, text, session_id, network_code)
                return response
                # return "END Invalid serial number or no ticket found"

            bank = select_bank(splited_text[3])
            bank_db = BankManger().bank_details(bank_name=bank)

            account_number = splited_text[4]

            if not bank_db:
                return "END Invalid bank"

            bank_code = bank_db.get("cbn_code")

            ussd_scratch_card_helper = UssdScratchCardHelper()
            try:
                ussd_scratch_card_helper.claim_winning(
                    serial_number=f"{scratch_card_instance.serial_skew}{scratch_card_instance.serial_number}",
                    pin=scratch_card_instance.pin,
                    account_number=account_number,
                    bank_code=bank_code,
                    phone_number=phone_number,
                    use_new_withdrawal_flow=True,
                )
            except Exception:
                pass

            return "END your request has been sent for processing"



def ussd_salary_for_life_game_show_menue_interaction(phone_number: str, service_code: str, text: str, session_id: str, network_code: str):
    splited_text = text.split("*")
    splited_text = [i for i in splited_text if i != ""]

    if text.startswith("39"):
        if text == "39":
            response = "CON Welcome to salary for life auditions\n\n"
            response += "1. Register\n"
            response += "2. Contact Suport\n"

            return response
        else:
            if str(text).strip() == "39*1":
                response = "END Thank you for your interest in salary4life audition. Check your sms inbox to complete your registration"
                # check if the phone number already exist in the participant table
                if SalaryForLifeParticipant.objects.filter(phone_number=phone_number).exists():
                    return response
                else:
                    # send sms to them containing the link to register

                    message = "Please, click on this link to register for the audition: https://bit.ly/43qtQ7G"

                    payload = json.dumps(
                        {
                            "receiver": f"{phone_number}",
                            "template": "4e08c548-54ba-453e-8cad-4876713a3f05",
                            "place_holders": {"message": message},
                        }
                    )

                    

                    whisper_headers = {
                        "Authorization": f"Api_key {settings.WHISPER_KEY}",
                        "Content-Type": "application/json",
                    }

                    whisper_url = "https://whispersms.xyz/transactional/send"


                    try:
                        res = requests.request("POST", whisper_url, headers=whisper_headers, data=payload)
                        print(f"""
                                payload: {payload} \n
                                whisper_headers: {whisper_headers} \n
                                whisper_url: {whisper_url}\n
                                res" {res.text}

                            """)
                    except Exception as e:
                        # Log the error or handle it as needed
                        print(f"Error sending SMS: {e}")

                    
                    return response
                   

            
            elif str(text).strip() == "39*2":
                response = "END Please, contact support on 02013300173"
                return response