import uuid
from datetime import datetime

from rest_framework import status
from rest_framework.response import Response

from main.helpers.woven_manager import woven_verify_transaction
from main.models import PaymentTransaction, UserProfile, WovenCallBack
from main.tasks import (
    celery_send_whatsapp_payment_notification_admin_for_soccer_payment,
)
from wallet_app.models import DebitCreditRecord, UserWallet
from wallet_app.tasks import notify_admin_of_user_funding
from wyse_ussd.models import SoccerPrediction, UssdLotteryPayment
from wyse_ussd.tasks import share_ussd_payment_across_lottery_pool


def handle_woven_soccer_prediction(woven_callback_data):
    resp = woven_callback_data

    account_ref = resp.get("account_reference")
    unique_ref = resp.get("unique_reference")
    amount = resp.get("amount")

    get_wallet = UserWallet.objects.filter(account_ref=account_ref).last()

    user_profile = UserProfile.objects.filter(phone_number=get_wallet.user.phone_number).last()

    if resp.get("status") == "ACTIVE":
        check_for_transaction = PaymentTransaction.objects.filter(provider_transaction_reference=unique_ref).last()

        if check_for_transaction:
            verfiy_transaction = woven_verify_transaction(transaction_reference=unique_ref)

            if verfiy_transaction:
                if check_for_transaction.status != "SUCCESSFUL":
                    check_for_transaction.status = "SUCCESSFUL"
                    check_for_transaction.has_paid = True
                    check_for_transaction.amount = float(amount)

                    check_for_transaction.save()

                    check_for_payload = WovenCallBack.objects.filter(transaction=check_for_transaction).last()

                    if check_for_payload:
                        check_for_payload.payload = resp
                        check_for_payload.save()
                    else:
                        # Create provider raw payload

                        WovenCallBack.objects.create(transaction=check_for_transaction, payload=resp)

                    if get_wallet and get_wallet.wallet_tag == "WEB":
                        debit_credit_record = DebitCreditRecord.create_record(
                            phone_number=user_profile.phone_number,
                            amount=int(amount),
                            channel="WEB",
                            reference=f"{unique_ref}",
                            transaction_type="CREDIT",
                        )

                        wallet_payload = {
                            "transaction_from": "WOVEN_FUNDING",
                        }

                        UserWallet.fund_wallet(
                            user=user_profile,
                            amount=int(amount),
                            channel="WEB",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="GAME_PLAY_WALLET",
                            **wallet_payload,
                        )

                        # notify admin
                        notify_admin_of_user_funding.delay(amount=int(amount), phone_number=user_profile.phone_number, channel="WOVEN")

                    else:
                        game_play_id = UssdLotteryPayment.objects.filter(
                            game_play_id__isnull=False,
                            user=user_profile,
                            is_successful=False,
                            is_verified=False,
                        ).last()

                        if game_play_id is None:
                            user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                            if user_wallet is None:
                                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                            # fund user wallet
                            # user_wallet.game_available_balance += int(amount)
                            # user_wallet.transaction_from = "WOVEN_FUNDING"
                            # user_wallet.save()

                            debit_credit_record = DebitCreditRecord.create_record(
                                phone_number=user_profile.phone_number,
                                amount=int(amount),
                                channel="WEB",
                                reference=f"{unique_ref}",
                                transaction_type="CREDIT",
                            )

                            wallet_payload = {
                                "transaction_from": "WOVEN_FUNDING",
                            }

                            UserWallet.fund_wallet(
                                user=user_profile,
                                amount=int(amount),
                                channel="WEB",
                                transaction_id=debit_credit_record.reference,
                                user_wallet_type="GAME_PLAY_WALLET",
                                **wallet_payload,
                            )

                            # notify admin
                            notify_admin_of_user_funding.delay(amount=int(amount), phone_number=user_profile.phone_number, channel="WOVEN")

                            return Response(
                                {
                                    "message": "Transaction successful",
                                    "responseCode": "00",
                                },
                                status=status.HTTP_200_OK,
                            )

                        game_play_id.is_successful = True
                        game_play_id.is_verified = True
                        game_play_id.save()
                        game_play_id = game_play_id.game_play_id

                        lottoticket_qs = SoccerPrediction.objects.filter(game_id=game_play_id).last()

                        if not lottoticket_qs:
                            user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                            if user_wallet is None:
                                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                            # fund user wallet
                            # user_wallet.game_available_balance += int(amount)
                            # user_wallet.transaction_from = "WOVEN_FUNDING"
                            # user_wallet.save()

                            debit_credit_record = DebitCreditRecord.create_record(
                                phone_number=user_profile.phone_number,
                                amount=int(amount),
                                channel="WEB",
                                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                                transaction_type="CREDIT",
                            )

                            wallet_payload = {
                                "transaction_from": "WOVEN_FUNDING",
                            }

                            UserWallet.fund_wallet(
                                user=user_profile,
                                amount=int(amount),
                                channel="WEB",
                                transaction_id=debit_credit_record.reference,
                                user_wallet_type="GAME_PLAY_WALLET",
                                **wallet_payload,
                            )

                            # notify admin
                            notify_admin_of_user_funding.delay(amount=int(amount), phone_number=user_profile.phone_number, channel="WOVEN")

                            return Response(
                                {
                                    "message": "Transaction successful",
                                    "responseCode": "00",
                                },
                                status=status.HTTP_200_OK,
                            )

                        else:  # soccer prediction found
                            share_ussd_payment_across_lottery_pool(
                                user_profile.phone_number, int(amount), game_play_id, transfrom="WOVEN_FUNDING", from_web=True
                            )

                            celery_send_whatsapp_payment_notification_admin_for_soccer_payment.delay(
                                phone_number=user_profile.phone_number,
                                amount=amount,
                                paid_via="Woven",
                            )

                            return {
                                "message": "Transaction successful",
                                "responseCode": "00",
                            }

        else:
            verfiy_transaction = woven_verify_transaction(transaction_reference=unique_ref)

            if verfiy_transaction:
                # Add transaction to database
                PaymentTransaction.objects.create(
                    lottery_player=user_profile,
                    payment_channel="WOVEN_ACCOUNT",
                    amount=amount,
                    provider_transaction_reference=unique_ref,
                    unique_provider_transaction_reference=unique_ref,
                    status="SUCCESSFUL",
                    has_paid=True,
                )

            if get_wallet and get_wallet.wallet_tag == "WEB":
                # if the wallet is tagged web. we'll then fund the user web wallet balance
                # UserWallet.fund_wallet(user_profile, amount)
                # share_payment_across_pool_when_virtual_account_is_funded(user_profile)
                pass
            else:
                game_play_id = UssdLotteryPayment.objects.filter(
                    game_play_id__isnull=False,
                    user=user_profile,
                    is_successful=False,
                    is_verified=False,
                ).last()

                if game_play_id is None:
                    user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                    if user_wallet is None:
                        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                    # fund user wallet
                    # user_wallet.game_available_balance += int(amount)
                    # user_wallet.transaction_from = "WOVEN_FUNDING"
                    # user_wallet.save()

                    debit_credit_record = DebitCreditRecord.create_record(
                        phone_number=user_profile.phone_number,
                        amount=int(amount),
                        channel="WEB",
                        reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                        transaction_type="CREDIT",
                    )

                    wallet_payload = {
                        "transaction_from": "WOVEN_FUNDING",
                    }

                    UserWallet.fund_wallet(
                        user=user_profile,
                        amount=int(amount),
                        channel="WEB",
                        transaction_id=debit_credit_record.reference,
                        user_wallet_type="GAME_PLAY_WALLET",
                        **wallet_payload,
                    )

                    return Response(
                        {
                            "message": "Transaction successful",
                            "responseCode": "00",
                        },
                        status=status.HTTP_200_OK,
                    )

                game_play_id.is_successful = True
                game_play_id.is_verified = True
                game_play_id.save()
                game_play_id = game_play_id.game_play_id

                lottoticket_qs = SoccerPrediction.objects.filter(game_id=game_play_id).last()

                if not lottoticket_qs:
                    user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                    if user_wallet is None:
                        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                    # fund user wallet
                    user_wallet.game_available_balance += int(amount)
                    user_wallet.transaction_from = "WOVEN_FUNDING"
                    user_wallet.save()

                    return Response(
                        {
                            "message": "Transaction successful",
                            "responseCode": "00",
                        },
                        status=status.HTTP_200_OK,
                    )

                else:  # soccer prediction found
                    share_ussd_payment_across_lottery_pool(
                        user_profile.phone_number, int(amount), game_play_id, transfrom="WOVEN_FUNDING", from_web=True
                    )

                    celery_send_whatsapp_payment_notification_admin_for_soccer_payment.delay(
                        phone_number=user_profile.phone_number,
                        amount=amount,
                        paid_via="Woven",
                    )

                    # return {
                    #     "message": "Transaction successful",
                    #     "responseCode": "00",
                    # }

                    return Response(
                        {
                            "message": "Transaction successful",
                            "responseCode": "00",
                        },
                        status=status.HTTP_200_OK,
                    )
