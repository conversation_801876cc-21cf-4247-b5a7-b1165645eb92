import random

from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from broad_base_communication.bbc_helper import BBCTelcoAggregator
from main.models import LotteryModel
from prices.game_price import secure_d_and_upstream_service_and_prodct_details
from wyse_ussd.models import TelcoGameWebSubscribers
from wyse_ussd.tasks import celery_deactivate_telco_subscription


class GloWebGetGamesApiView(APIView):
    authentication_classes = []

    @method_decorator(csrf_exempt)
    def get(self, request):
        game_details = [
            {
                "game_type": "salary_for_life",
                "data": [
                    {
                        "subscription_type": "daily",
                        "price": 50,
                        "game_type": "salary_for_life",
                        "product_id": "1000005392",
                        "service_id": "0017182000003868",
                    },
                    {
                        "subscription_type": "one-off",
                        "price": 50,
                        "game_type": "salary_for_life",
                        "product_id": "1000005394",
                        "service_id": "0017182000003868",
                    },
                    {
                        "subscription_type": "on-demand",
                        "price": 100,
                        "game_type": "salary_for_life",
                        "product_id": "1000005394",
                        "service_id": "0017182000003868",
                    },
                ],
            },
            {
                "game_type": "instant_cashout",
                "data": [
                    {
                        "subscription_type": "daily",
                        "price": 50,
                        "game_type": "instant_cashout",
                        "product_id": "1000005218",
                        "service_id": "0017182000001707",
                    },
                    {
                        "subscription_type": "one-off",
                        "price": 50,
                        "game_type": "instant_cashout",
                        "product_id": "1000005251",
                        "service_id": "0017182000001707",
                    },
                    {
                        "subscription_type": "on-demand",
                        "price": 100,
                        "game_type": "instant_cashout",
                        "product_id": "1000005251",
                        "service_id": "0017182000001707",
                    },
                ],
            },
            {
                "game_type": "fast_fingers",
                "data": [
                    {
                        "subscription_type": "daily",
                        "price": 50,
                        "game_type": "fast_fingers",
                        "product_id": "1000005386",
                        "service_id": "0017182000003867",
                    },
                    {
                        "subscription_type": "one-off",
                        "price": 50,
                        "game_type": "fast",
                        "product_id": "1000005388",
                        "service_id": "0017182000003867",
                    },
                    {
                        "subscription_type": "on-demand",
                        "price": 100,
                        "game_type": "fast",
                        "product_id": "1000005388",
                        "service_id": "0017182000003867",
                    },
                ],
            },
        ]

        return Response(game_details, status=status.HTTP_200_OK)

    @method_decorator(csrf_exempt)
    def post(self, request):
        product_id = request.data.get("product_id")
        service_id = request.data.get("service_id")
        phone = request.data.get("phone")

        if not product_id:
            return Response({"error": "product_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        if not service_id:
            return Response({"error": "service_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        if not phone:
            return Response({"error": "phone is required"}, status=status.HTTP_400_BAD_REQUEST)

        # make a request to the telco aggregator to subscribe the user to the game

        get_game_details_object = secure_d_and_upstream_service_and_prodct_details(product_id=service_id)

        if not get_game_details_object:
            return Response({"error": "Invalid service_id"}, status=status.HTTP_400_BAD_REQUEST)

        formatted_phone_number = LotteryModel.format_number_from_back_add_234(phone)

        generated_random_id = random.randint(100000, 999999)

        telco_charge_payload = {
            "phone_number": formatted_phone_number,
            "description": f"{str(get_game_details_object.get('product_name')).replace('_', ' ')} lottery payment",
            "amount": get_game_details_object.get("amount"),
            "game_play_id": generated_random_id,
            "lottery_type": get_game_details_object.get("product_name"),
            "pontential_winning": 5000,
            "service_id": service_id,
            "product_id": product_id,
        }

        telco_charge_payload["use_json_format"] = True
        telco_charge_payload["channel"] = "USSD"

        BBCTelcoAggregator().telco_airtime_subscription_activation(**telco_charge_payload)

        try:
            TelcoGameWebSubscribers.create_subscriber(
                phone_number=formatted_phone_number,
                product_id=product_id,
                service_id=service_id,
                network_provider="GLO",
                game_type=get_game_details_object.get("product_name"),
            )
        except Exception:
            pass

        return Response(
            {"message": "Game subscription sent. You'll get authorisation notification shortly"},
            status=status.HTTP_200_OK,
        )


class UnSubscribeRequestView(APIView):
    authentication_classes = []

    @method_decorator(csrf_exempt)
    def post(self, request):
        product_id = request.data.get("product_id")
        phone = request.data.get("phone")

        if not product_id:
            return Response({"error": "product_id is required"}, status=status.HTTP_400_BAD_REQUEST)

        if not phone:
            return Response({"error": "phone is required"}, status=status.HTTP_400_BAD_REQUEST)

        phone_number = LotteryModel.format_number_from_back_add_234(phone)

        # unsubscribe_payload = {
        #     "phone_number": phone_number,
        #     "product_id": product_id,
        # }

        celery_deactivate_telco_subscription(phone_number=phone_number, product_id=product_id)

        return Response(
            {"message": "Unsubscription request sent."},
            status=status.HTTP_200_OK,
        )


class NitroSwitchAIFortune(APIView):
    def get(self, request):
        games = [
            {"pcode": "2145", "name": "AI FORTUNE DAILY AUTO", "price": 100, "validity": 1},
            {"pcode": "2146", "name": "AI FORTUNE DAILY ONETIME", "price": 100, "validity": 1},
            {
                "pcode": "2147",
                "name": "AI FORTUNE WEEKLY AUTO",
                "price": 200,
                "validity": 1,
            },
            {
                "pcode": "2148",
                "name": "AI FORTUNE WEEKLY ONETIME",
                "price": 200,
                "validity": 1,
            },
            {
                "pcode": "2149",
                "name": "AI FORTUNE MONTHLY AUTO",
                "price": 500,
                "validity": 30,
            },
            {
                "pcode": "2150",
                "name": "AI FORTUNE MONTHLY AUTO",
                "price": 500,
                "validity": 30,
            },
        ]

        return Response(data=games, status=status.HTTP_200_OK)

    def post(self, request):
        pcode = request.data.get("pcode", None)
        phone_number = request.data.get("phone_number", None)

        if phone_number is None or pcode is None:
            return Response(data={"message": "phone number or pcode is required"}, status=status.HTTP_400_BAD_REQUEST)
