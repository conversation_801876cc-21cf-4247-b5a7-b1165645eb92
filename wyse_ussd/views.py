import base64
import decimal
import json
import time
from datetime import datetime, timed<PERSON><PERSON>
from random import choice
from django.utils import timezone

import requests
from django.conf import settings
from django.http import HttpResponse, JsonResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny

from ads_tracker.tasks import send_marketing_partners_postback_task
from account.authentication import CustomBasicAuthentication, SuperUser2Permission
from broad_base_communication.bbc_helper import BBCTelcoAggregator
from main.models import LotteryModel, LottoTicket, UserProfile
from main.tasks import (
    celery_send_whatsapp_payment_notification_admin,
    lottery_play_engange_event,
)

from overide_print import print
from sport_app.models import PredictionTable
from sport_app.serializers import WebSoccerPredictionSerializer
from wallet_app.models import Debit<PERSON><PERSON>itR<PERSON>ord, UserWalle<PERSON>, WithdrawalPINSystem
from wallet_app.tasks import notify_admin_of_user_funding

# from wyse_ussd.menu import WhisperWyseUssd
from wyse_ussd.helper.liberty_pay_ussd_helper import liberty_pay_ussd_menu_imteraction, ussd_salary_for_life_game_show_menue_interaction
from wyse_ussd.helper.redocean_africa import RedoceanAfricaGatewayHelper
from wyse_ussd.live_bbc_json_menu import new_bbc_ussd_menu as live_new_bbc_ussd_menu
from wyse_ussd.menu2 import WhisperWyseUssd
from wyse_ussd.models import (
    CoralpayTransactions,
    CoralpayUserCode,
    NitroSwitchDataSync,
    NitroSwitchSMSMO,
    NitroSwitchSmsToAiSubscription,
    RedoceanAfricaContentDeliveryDataSync,
    RedoceanAfricaSubscriber,
    RedoceanAfricaSubscriptionDataSync,
    SecureDDataDump,
    UssdLotteryPayment,
    UssdPayment,
    PendingNitroswitchAsyncTask,
)
from wyse_ussd.serializers import CoralPayAuthSerializer, OpenAIPromptSerializer, \
    RedoceanAfricaFetchPredictWiseGamesSerializer, SetRedoceanWebhookUrlsSerializer

from wyse_ussd.tasks import (
    celery_handle_nitroswitch_datasync,
    celery_handle_nitroswitch_sms_mo_task,
    celery_secure_d_data_sync,
    new_task_to_process_telco_sync,
    new_telco_sms_subscription_feature,
    process_telco_sync,
    share_ussd_payment_across_lottery_pool,
    telco_sms_subscription_feature,
)
from wyse_ussd.telco_subscription_menu import telco_ussd_menu
from wyse_ussd.enums import PurposeChoices

# from wyse_ussd.telco_bbc_menu import telco_ussd_menu


@csrf_exempt
def wyse_ussd_view(request):
    if request.method == "POST":
        request_body = request.body
        try:
            request_body = json.loads(request_body.decode("utf-8").replace("'", '"'))
        except Exception:
            request_body = request.POST

        # print("request_body", request_body, "\n\n\n")

        request_body.get("serviceCode")
        session_id = request_body.get("sessionId")
        phone_number = request_body.get("phoneNumber")
        text = request_body.get("text")

        response = WhisperWyseUssd.options(
            text=text,
            phone_number=phone_number,
            session_id=session_id,
        )
        return HttpResponse(response)

    return HttpResponse("Invalid request")


class CoralpayPaymentInitiation(APIView):
    authentication_classes = [CustomBasicAuthentication]

    serializer_class = CoralPayAuthSerializer

    @method_decorator(csrf_exempt)
    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        customerRef = serializer.validated_data.get("customerRef")
        merchantid = serializer.validated_data.get("merchantId")
        shortCode = serializer.validated_data.get("shortCode")

        if merchantid != settings.CORAL_PAY_MERCHANT_ID:
            return Response(
                {
                    "message": "Merchant ID is valid",
                    "customerRef": customerRef,
                    "merchantid": merchantid,
                    "shortCode": shortCode,
                    "responseCode": "400",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        coralpay_user_code_instance = CoralpayUserCode.objects.filter(code=customerRef, has_expired=False, is_used=False).last()

        if coralpay_user_code_instance is None:
            return Response(
                {
                    "message": "Invalid user code",
                    "customerRef": customerRef,
                    "merchantid": merchantid,
                    "shortCode": shortCode,
                    "responseCode": "400",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        coral_pay_transaction_instance = CoralpayTransactions.objects.filter(ussd_code_extension=coralpay_user_code_instance).last()

        if coral_pay_transaction_instance is None:
            return Response(
                {
                    "message": "There's issue with your request. try again later",
                    # "customerRef": customerRef,
                    # "merchantid": merchantid,
                    # "shortCode": shortCode,
                    "responseCode": "400",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # update user bank code in transaction table
        coral_pay_transaction_instance.bank_code = shortCode
        coral_pay_transaction_instance.save()

        data = {
            "traceId": coral_pay_transaction_instance.trace_id,
            "customerName": f"{coralpay_user_code_instance.user.first_name} {coralpay_user_code_instance.user.last_name}",
            "amount": decimal.Decimal(coral_pay_transaction_instance.amount),
            "displayMessage": "whisperwyse payment",
            "responseCode": "00",
        }

        return Response(data, status=status.HTTP_200_OK)


class CoralpayWebhook(APIView):
    authentication_classes = [CustomBasicAuthentication]

    @method_decorator(csrf_exempt)
    def post(self, request):
        try:
            print("request.data", request.data, "\n\n\n\n")
        except Exception:
            print("request.body", request.body, "\n\n\n\n")

        body = request.data

        if not isinstance(body, dict):
            body = json.loads(body)

        trace_id = body.get("traceId")
        response_code = body.get("responseCode")

        coralpay_transaction_instance = CoralpayTransactions.objects.filter(trace_id=trace_id, is_verified=False).last()

        if response_code == "00":
            if coralpay_transaction_instance is None:
                return Response(
                    {"message": "Invalid trace id", "responseCode": "00"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            coralpay_transaction_instance.is_successful = True
            coralpay_transaction_instance.response_data = body
            coralpay_transaction_instance.payment_ref = body.get("paymentReference")
            coralpay_transaction_instance.is_verified = True
            coralpay_transaction_instance.save()

            if coralpay_transaction_instance.payment_for == "LOTTERY_PAYMENT" and coralpay_transaction_instance.channel == "USSD":
                user_profile = UserProfile.objects.filter(phone_number=coralpay_transaction_instance.customer_phone).last()

                # get the lottery game play id that this payment was made for
                game_play_id = UssdLotteryPayment.objects.filter(user=user_profile, is_successful=False, is_verified=False).last()

                # print("game_play_id", game_play_id, "\n\n\n\n")

                if game_play_id is None:
                    # update user wallet
                    user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                    if user_wallet is None:
                        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                    # fund user wallet
                    user_wallet.game_available_balance += int(body.get("amount"))
                    user_wallet.transaction_from = "CORAL_PAY"
                    user_wallet.save()
                    print("user wallet funded")

                    amount = int(body.get("amount"))

                    # notify admin
                    notify_admin_of_user_funding.delay(
                        amount=int(amount),
                        phone_number=user_profile.phone_number,
                        channel="CORAL_PAY",
                    )

                    return Response(
                        {"message": "Transaction successful", "responseCode": "00"},
                        status=status.HTTP_200_OK,
                    )

                game_play_id.is_successful = True
                game_play_id.is_verified = True
                game_play_id.save()
                game_play_id = game_play_id.game_play_id
                # get the lottery game play instance
                lottoticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id)
                if not lottoticket_qs.exists():
                    lottoticket_qs = LotteryModel.objects.filter(game_play_id=game_play_id)

                    if not lottoticket_qs.exists():
                        # update user wallet
                        user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                        if user_wallet is None:
                            user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                        # fund user wallet
                        user_wallet.game_available_balance += int(body.get("amount"))
                        user_wallet.transaction_from = "CORAL_PAY"
                        user_wallet.save()

                        amount = int(body.get("amount"))

                        # notify admin
                        notify_admin_of_user_funding.delay(
                            amount=int(amount),
                            phone_number=user_profile.phone_number,
                            channel="CORAL_PAY",
                        )

                        print("no lottery ticket found")

                        return Response(
                            {"message": "Transaction successful", "responseCode": "00"},
                            status=status.HTTP_200_OK,
                        )

                share_ussd_payment_across_lottery_pool.delay(
                    coralpay_transaction_instance.customer_phone,
                    int(body.get("amount")),
                    game_play_id,
                    transfrom="CORAL_PAY",
                    transaction_unique_id=coralpay_transaction_instance.trace_id,
                ),

                # whatsapp notification to admin
                celery_send_whatsapp_payment_notification_admin.delay(
                    phone_number=coralpay_transaction_instance.customer_phone,
                    batch_id=lottoticket_qs.last().batch.batch_uuid,
                    amount=int(body.get("amount")),
                    paid_via="CORAL PAY",
                )
            print("transaction successful")
            return Response(
                {"message": "Transaction successful", "responseCode": "00"},
                status=status.HTTP_200_OK,
            )
        else:
            coralpay_transaction_instance.is_verified = True
            coralpay_transaction_instance.save()

            # game_play_id.is_verified = True
            # game_play_id.save()

            return Response(
                {"message": "Transaction failed", "responseCode": "00"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class RedBillerCallbackView(APIView):
    def post(self, request):
        """
        SAMPLE RESPONSE

        {
            "response":200,
            "status":"true",
            "message":"Successful",
            "details":{
                "amount":100,
                "charge":1,
                "settlement":99,
                "profile":{
                    "first_name":"WECUQ",
                    "surname":"FOTAF",
                    "phone_no":"*************",
                    "email":"<EMAIL>",
                    "bvn":"***********"
                },
                "account":{
                    "bank_name":"GUARANTY TRUST BANK",
                    "bank_code":"000013",
                    "ussd_code":"*737*000*4207#"
                },
                "reference":"wyse4112b8c6e09d188310f99f549a0308393018",
                "date":"2022-10-17 16:19:38"
            },
            "meta":{
                "status":"Approved"
            }
        }
        """

        response = request.data
        reference = response.get("details").get("reference")

        ussd_payment_instance = UssdPayment.objects.filter(transaction_ref=reference, verified=False).last()

        if ussd_payment_instance:
            if isinstance(response, dict):
                try:
                    response["details"]["profile"]["bvn"] = ""
                except Exception:
                    pass

            ussd_payment_instance.data_dump = response
            ussd_payment_instance.save()

            # verify payment
            redbiller_url = f"{settings.LIBERTY_VAS_BASE_URL}/redbiller/merchant/verify/"

            payload = {
                "payment_reference": ussd_payment_instance.transaction_ref,
                "service": "USSD_PAYMENT",
            }
            username = settings.LIBERTY_VAS_AUTH_USERNAME
            password = settings.LIBERTY_VAS_AUTH_PASSWORD

            STRING_VALUE = f"{username}:{password}"
            AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

            headers = {
                "Authorization": f"Basic {AUTH_TOKEN}",
                "Content-Type": "application/json",
            }

            print("verifications started")

            verify_response = requests.request("POST", redbiller_url, headers=headers, json=payload)

            """
            SAMPLE VERIFY RESPONSE

            {
                "response":200,
                "status":"true",
                "message":"Successful",
                "details":{
                    "amount":100,
                    "charge":1,
                    "settlement":99,
                    "profile":{
                        "first_name":"WECUQ",
                        "surname":"FOTAF",
                        "phone_no":"*************",
                        "email":"<EMAIL>",
                        "bvn":"***********"
                    },
                    "account":{
                        "bank_name":"GUARANTY TRUST BANK",
                        "bank_code":"000013",
                        "ussd_code":"*737*000*4207#"
                    },
                    "status":"Approved",
                    "reference":"wyse4112b8c6e09d188310f99f549a0308393018",
                    "channel":"API",
                    "callback_url":"https://a45a-102-67-1-49.eu.ngrok.io/api/ussd/redbiller_webhook/",
                    "date":"2022-10-17 16:18:35"
                }
            }

            """
            verify_response = verify_response.json()

            print(
                'verify_response.get("details", {}).get("status")',
                verify_response.get("details", {}).get("status"),
            )

            print("verifcation", verify_response)

            if verify_response.get("details", {}).get("status") == "Approved":
                print("transaction successful")

                ussd_payment_instance.verified = True
                ussd_payment_instance.successful = True
                ussd_payment_instance.save()

                user_profile = UserProfile.objects.filter(id=ussd_payment_instance.user.id).last()

                user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()

                # USER WALLET FUNDING #

                if ussd_payment_instance.payment_for == "FUNDING_TELCO_WALLET":
                    wallet_payload = {
                        "transaction_from": "REDBILLER_TELCO_FUNDING",
                    }

                    debit_credit_record = DebitCreditRecord.create_record(
                        phone_number=user_wallet.user.phone_number,
                        amount=ussd_payment_instance.amount,
                        channel="USSD",
                        reference=f"{ussd_payment_instance.transaction_ref}{ussd_payment_instance.id}",
                        transaction_type="CREDIT",
                    )

                    UserWallet.fund_wallet(
                        user=user_wallet.user,
                        amount=int(ussd_payment_instance.amount),
                        channel="WEB",
                        transaction_id=debit_credit_record.reference,
                        user_wallet_type="GAME_PLAY_WALLET",
                        from_telco_channel=True,
                        **wallet_payload,
                    )

                    return Response(response, status=status.HTTP_200_OK)

                if ussd_payment_instance.payment_for == "WALLET_FUNDING":
                    user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                    if user_wallet is None:
                        user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                    # fund user wallet
                    user_wallet.game_available_balance += int(ussd_payment_instance.amount)
                    user_wallet.transaction_from = "REDBILLER_FUNDING"
                    user_wallet.save()
                    print("user wallet funded")

                    # notify admin
                    notify_admin_of_user_funding.delay(
                        amount=int(ussd_payment_instance.amount),
                        phone_number=user_profile.phone_number,
                        channel="REDBILLER_FUNDING",
                    )

                    # ----------------------------------- ENGAGE EVENT ----------------------------------- #

                    engage_event_payload = {
                        "event": "USER_WALLET_FUNDING",
                        "properties": {
                            "FUNDING_CHANNEL": "REDBILLER USSD PAYMENT",
                        },
                    }
                    lottery_play_engange_event.delay(
                        user_id=user_wallet.user.id,
                        is_user_profile_id=True,
                        **engage_event_payload,
                    )

                    # ----------------------------------- ENGAGE EVENT ----------------------------------- # # noqa

                    return {"message": "Transaction successful", "responseCode": "00"}

                # END OF USER WALLET FUNDING

                if (ussd_payment_instance.payment_for == "LOTTERY_PAYMENT") and (ussd_payment_instance.channel == "USSD"):
                    print("ussd payment")

                    # get the lottery game play id that this payment was made for
                    game_play_id = UssdLotteryPayment.objects.filter(
                        user=ussd_payment_instance.user,
                        is_successful=False,
                        is_verified=False,
                    ).last()

                    if game_play_id is None:
                        print("no lottery ticket found")
                        # update user wallet
                        # user_wallet = UserWallet.objects.filter(
                        #     user=user_profile, wallet_tag="WEB"
                        # ).last()
                        # if user_wallet is None:
                        #     user_wallet = UserWallet.objects.create(
                        #         user=user_profile, wallet_tag="WEB"
                        #     )

                        # # fund user wallet
                        # user_wallet.game_available_balance += (
                        #     float(ussd_payment_instance.amount) - 50
                        # )
                        # user_wallet.transaction_from = "REDBILLER_FUNDING"
                        # user_wallet.save()
                        import uuid

                        print("user wallet funded")

                        wallet_payload = {
                            "transaction_from": "PAYSTACK_FUNDING",
                        }

                        transaction_ref = f"{uuid.uuid4()}{int(time.time())}"

                        debit_credit_record = DebitCreditRecord.create_record(
                            phone_number=user_profile.phone_number,
                            amount=float(ussd_payment_instance.amount) - 50,
                            channel="USSD",
                            reference=transaction_ref,
                            transaction_type="CREDIT",
                        )

                        UserWallet.fund_wallet(
                            user=user_profile,
                            amount=float(ussd_payment_instance.amount) - 50,
                            channel="WEB",
                            transaction_id=debit_credit_record.reference,
                            user_wallet_type="GAME_PLAY_WALLET",
                            from_telco_channel=False,
                            **wallet_payload,
                        )

                        # notify admin
                        notify_admin_of_user_funding.delay(
                            amount=int(ussd_payment_instance.amount),
                            phone_number=user_profile.phone_number,
                            channel="REDBILLER_FUNDING",
                        )

                        return Response(
                            {"message": "Transaction successful", "responseCode": "00"},
                            status=status.HTTP_200_OK,
                        )
                    # else:
                    #     now = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

                    #     # time diff between now and time created of the ussd lottery payment
                    #     time_diff = now - game_play_id.created_at

                    #     if time_diff.min > 5:
                    #         return {
                    #             "status": "FAILED",
                    #             "message": "transaction has expired",
                    #         }

                # game_play_id.is_successful = True
                # game_play_id.is_verified = True
                game_play_id.amount_paid = ussd_payment_instance.amount - 50
                game_play_id.save()
                game_play_id = game_play_id.game_play_id

                # get the lottery game play instance
                lottoticket_qs = LottoTicket.objects.filter(game_play_id=game_play_id)

                print("lottery ticket found Second leg")

                if not lottoticket_qs.exists():
                    print("lottery ticket not found")

                    lottoticket_qs = LotteryModel.objects.filter(game_play_id=game_play_id)

                    if not lottoticket_qs.exists():
                        print("lottery ticket not found 2")
                        # update user wallet
                        user_wallet = user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="USSD").last()
                        if user_wallet is None:
                            user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="USSD")

                        # fund user wallet
                        user_wallet.game_available_balance += float(ussd_payment_instance.amount) - 50
                        user_wallet.transaction_from = "REDBILLER_FUNDING"
                        user_wallet.save()

                        # notify admin
                        notify_admin_of_user_funding.delay(
                            amount=int(ussd_payment_instance.amount),
                            phone_number=user_profile.phone_number,
                            channel="REDBILLER_FUNDING",
                        )

                        print("no lottery ticket found")

                        return Response(
                            {"message": "Transaction successful", "responseCode": "00"},
                            status=status.HTTP_200_OK,
                        )

                    print("lottery ticket found 2")

                print("lottery ticket found")

                share_ussd_payment_across_lottery_pool.delay(
                    ussd_payment_instance.user.phone_number,
                    float(ussd_payment_instance.amount) - 50,
                    game_play_id,
                    transfrom="REDBILLER_FUNDING",
                    transaction_unique_id=ussd_payment_instance.transaction_unique_id,
                ),

                # whatsapp notification to admin
                celery_send_whatsapp_payment_notification_admin.delay(
                    phone_number=ussd_payment_instance.user.phone_number,
                    batch_id=lottoticket_qs.last().batch.batch_uuid,
                    amount=int(ussd_payment_instance.amount),
                    paid_via="Redbiller",
                )

        print("redbiller response webhook", response, "\n\n\n\n")
        response = {"status": "true", "message": "response received"}
        return Response(response, status=status.HTTP_200_OK)


class TelcoAggregatorUssdNotification(APIView):
    def post(self, request):
        """
        SAMPLE REQUEST

        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
            xmlns:loc="http://www.csapi.org/schema/osg/ussd/notification_manager/v1_0/local">
            <soapenv:Header>
                <tns:RequestSOAPHeader xmlns:tns="http://www.huawei.com.cn/schema/common/v2_1">
                <tns:spId>000000</tns:spId>
                <tns:spPassword>000000</tns:spPassword>
                <tns:serviceId>0000000000000000</tns:serviceId>
                <tns:timeStamp>20210818120000</tns:timeStamp>
                <t:linkid xmlns:t="http://www.huawei.com.cn/schema/common/v2_1">0
                </t:linkid>
                </tns:RequestSOAPHeader>
            </soapenv:Header>
            <soapenv:Body>
                <loc:startUSSDNotification>
                    <loc:reference>ws-12345678920210818120000</loc:reference>
                    <loc:ussdServiceActivationNumber>0000000000000000</loc:ussdServiceActivationNumber>
                    <loc:extensionInfo>
                        <loc:extensionInfo>
                            <loc:key>1</loc:key>
                            <loc:value>1</loc:value>
                        </loc:extensionInfo>
                        <loc:extensionInfo>
                            <loc:key>2</loc:key>
                            <loc:value>2</loc:value>
                        </loc:extensionInfo>
                    </loc:extensionInfo>
                    <loc:linkid>0</loc:linkid>
                    <loc:timeout>0</loc:timeout>
                    <loc:ussdContent>1</loc:ussdContent>

                    <loc:criteria>2929</loc:criteria>
                </loc:startUSSDNotification>
            </soapenv:Body>
        </soapenv:Envelope>
        """

        # print("request.data", request.data, "\n\n\n\n")

        # response_payload = """
        # <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        #     xmlns:loc="http://www.csapi.org/schema/osg/ussd/notification_manager/v1_0/local">
        #     <soapenv:Header>
        #         <tns:RequestSOAPHeader xmlns:tns="http://www.huawei.com.cn/schema/common/v2_1">
        #         <tns:spId>000000</tns:spId>
        #         <tns:spPassword>000000</tns:spPassword>
        #         <tns:serviceId>0000000000000000</tns:serviceId>
        #         <tns:timeStamp>20210818120000</tns:timeStamp>
        #         <t:linkid xmlns:t="http://www.huawei.com.cn/schema/common/v2_1">0
        #         </t:linkid>
        #         </tns:RequestSOAPHeader>
        #     </soapenv:Header>
        #         <soapenv:Body>
        #             <loc:startUSSDNotificationResponse>
        #                 <loc:result>0</loc:result>
        #                 </loc:startUSSDNotificationResponse>
        #                 </soapenv:Body>
        #                     </soapenv:Envelope>
        #                     <soapenv:Envelope xmlns:soapenv=http://schemas.xmlsoap.org/soap/envelope/
        #                     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        #                 <soapenv:Body>

        #                 <ns1:startUSSDNotificationResponse
        #                 xmlns:ns1="http://www.csapi.org/schema/osg/ussd/notification_manager/v1_0/local"/>
        #             </soapenv:Body>
        #         </soapenv:Envelope>
        #         <soapenv:Envelope xmlns:soapenv=http://schemas.xmlsoap.org/soap/envelope/
        #         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        #     <soapenv:Body>
        #     <ns1:startUSSDNotificationResponse
        #     xmlns:ns1="http://www.csapi.org/schema/osg/ussd/notification_manager/v1_0/local"/>
        #     </soapenv:Body>
        #     </soapenv:Envelope>

        #     """

        # print(
        #     f"""
        # TelcoAggregatorUssdNotification: {request.data}
        # """
        # )

        bbc_aggregator = BBCTelcoAggregator()
        data = bbc_aggregator.main_menu(request.data)

        # print("data", data, "\n\n\n\n")
        response = HttpResponse(data, content_type="text/xml")

        response["SOAPAction"] = "your-soap-action"

        return response


@csrf_exempt
def telco_aggregator_ussd_notification_view(request):
    if request.method == "POST":
        xml_data = request.body
        # Parse the XML data
        # root = ET.fromstring(xml_data)

        # print(
        #     f"""
        # TelcoAggregatorUssdNotification: {xml_data}
        # \n\n\n\n\n\n\n
        # """
        # )

        BBCTelcoAggregator()
        data = telco_ussd_menu(xml_data)

        print("response data", data, "time", datetime.now().time(), "\n\n\n\n")

        # Nigeria mtn telco aggregator response

        """
        <?xml version="1.0" encoding="UTF-8"?>
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
            <soapenv:Header>
                <ns3:NotifySOAPHeader xmlns:ns3="http://www.huawei.com.cn/schema/common/v2_1">
                    <ns3:spRevId />
                    <ns3:spRevpassword>ca2e984358c47f947d880cb6bc24856c</ns3:spRevpassword>
                    <ns3:spId>whispaussd</ns3:spId>
                    <ns3:serviceId />
                    <ns3:timeStamp>20230627132738</ns3:timeStamp>
                </ns3:NotifySOAPHeader>
            </soapenv:Header>
            <soapenv:Body>
                <ns2:notifyUssdReception xmlns:ns2="http://www.csapi.org/schema/parlayx/ussd/notification/v1_0/local">
                    <ns2:msgType>0</ns2:msgType>
                    <ns2:senderCB>911213817</ns2:senderCB>
                    <ns2:receiveCB>0xFFFFFFFF</ns2:receiveCB>
                    <ns2:ussdOpType>1</ns2:ussdOpType>
                    <ns2:msIsdn>2348038705895</ns2:msIsdn>
                    <ns2:serviceCode>7874</ns2:serviceCode>
                    <ns2:codeScheme>68</ns2:codeScheme>
                    <ns2:ussdString>*7874#</ns2:ussdString>
                </ns2:notifyUssdReception>
            </soapenv:Body>
        </soapenv:Envelope>


        """

        # print("data", data, "\n\n\n\n")
        response = HttpResponse(data, content_type="text/xml")

        response["SOAPAction"] = "your-soap-action"

        return response


@csrf_exempt
def telco_aggregator_sms_notification_view(request):
    print("I HAVE BEEN ACCESSED")
    print("I HAVE BEEN ACCESSED")
    print("I HAVE BEEN ACCESSED")
    print("I HAVE BEEN ACCESSED")
    print("I HAVE BEEN ACCESSED")
    print("I HAVE BEEN ACCESSED")
    print("I HAVE BEEN ACCESSED")
    print("I HAVE BEEN ACCESSED")
    if request.method == "POST":
        xml_data = request.body
        # Parse the XML data
        # root = ET.fromstring(xml_data)

        # print(
        #     f"""
        # :::::::::::::::::::::::::::::::::::::::::::::::: SMSTelcoAggregatorSMSNotification: {xml_data} :::::::::::::::::::::::::::::::::::: \n\n\n\n\n\n\n # noqa
        # """
        # )

        # bbc_aggregator = BBCTelcoAggregator()
        # print(f"SMS DATA SUNC {xml_data} \n\n\n\n")
        # print(f"SMS DATA SUNC {xml_data}, \n\n\n", should_print = True)

        data = telco_sms_subscription_feature(xml_data)

        try:
            with open("sample-sms_log.txt", "a+") as file:
                file.write(f"{data}\n\n")
        except Exception:
            pass

        # print("data", data, "\n\n\n\n")
        response = HttpResponse(data, content_type="text/xml")

        response["SOAPAction"] = "your-soap-action"

        return response


@csrf_exempt
def telco_aggregator_datasync_view(request):
    # print("telco_aggregator_datasync_view", "\n\n\n\n")
    from random import choice

    if request.method == "POST":
        bbc_aggregator = BBCTelcoAggregator()
        response_data = bbc_aggregator.datasync_response()

        response = HttpResponse(response_data, content_type="text/xml")

        response["SOAPAction"] = "your-soap-action"

        return response

        xml_data = request.body
        xml_data_str = xml_data.decode("utf-8")

        process_telco_sync.apply_async(
            args=[xml_data_str],
            queue=choice(["process_telco_sync_queue", "process_telco_sync_queue1", "process_telco_sync_queue3"]),
        )

        bbc_aggregator = BBCTelcoAggregator()
        response_data = bbc_aggregator.datasync_response()

        response = HttpResponse(response_data, content_type="text/xml")

        response["SOAPAction"] = "your-soap-action"

        return response
    elif request.method == "GET":
        return HttpResponse("only POST request is allowed")


@csrf_exempt
def secure_d_datasync_view(request):
    [JSONRenderer]
    secured_instance = SecureDDataDump.objects.create(
        data=request.body, ip_adress=request.META.get("HTTP_X_FORWARDED_FOR"),
        source="MTN"
        )
    send_marketing_partners_postback_task.apply_async(
                kwargs={
                    "instance_id": secured_instance.id,
                },
                queue="celery_ads_postback",
            )

    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        x_forwarded_for.split(",")[0]
    else:
        request.META.get("REMOTE_ADDR")

    request_data = request.body

    data_str = request_data.decode("utf-8")

    try:
        result_dict = json.loads(data_str)
    except Exception:
        result_dict = {}

        data = {"message": "success", "timestamp": datetime.now()}

        return JsonResponse(data)

    # trans_ref = result_dict.get("trxId")
    # if "upstream" in trans_ref:
    #     if ip == settings.SECURED_D_IP_ADDRESS:
    #         return Response({"message": "success"}, status=status.HTTP_200_OK)

    # print("CALLING celery_secure_d_data_sync")
    celery_secure_d_data_sync.apply_async(queue="bbc_datasync", kwargs=result_dict)
    # print("celery_secure_d_data_sync response", response)

    data = {"message": "success", "timestamp": datetime.now()}

    return JsonResponse(data)

    # return Response({"message": "success"}, status=status.HTTP_200_OK)


class GetUserVerificationCode(APIView):
    def post(self, request):
        phone_number = request.data.get("phone_number", None)

        if not phone_number:
            return Response(
                {"message": "phone number is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        formatted_phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        user_profile_instance = UserProfile.objects.filter(phone_number=formatted_phone_number).first()
        if not user_profile_instance:
            return Response({"message": "user does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        if (user_profile_instance.verification_code is None) or (user_profile_instance.verification_code == ""):
            user_profile_instance.verification_code = UserProfile.generate_mixed_pin()
            user_profile_instance.save()

        return Response(
            {"message": "success", "code": user_profile_instance.verification_code},
            status=status.HTTP_200_OK,
        )


class GetActiveWithdrawalOTP(APIView):
    def post(self, request):
        phone_number = request.data.get("phone_number", None)

        if not phone_number:
            return Response(
                {"message": "phone number is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        formatted_phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        user_profile_instance = UserProfile.objects.filter(phone_number=formatted_phone_number).first()
        if not user_profile_instance:
            return Response({"message": "user does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        otp_str = WithdrawalPINSystem.create_otp_instance(phone_number=phone_number, game_type="MANCALA")

        return Response(data={"message": "success", "code": otp_str}, status=status.HTTP_200_OK)


@csrf_exempt
def telco_airtime_game_subscription_api_view(request):
    if request.method == "POST":
        xml_data = request.body

        broad_base_helper = BBCTelcoAggregator()
        xml_data["use_json_format"] = True
        xml_data["channel"] = "USSD"
        res = broad_base_helper.request_telco_airtime_subscription_activation(xml_data)

        return HttpResponse(res)
    else:
        return HttpResponse("Invalid request")


class NitroSwitchDataSyncApiView(APIView):
    def post(self, request):
        data = request.data

        instance = NitroSwitchDataSync.objects.create(data=data)

        try:
            PendingNitroswitchAsyncTask.objects.get_or_create(
                purpose=PurposeChoices.MORBID_RENEWALS,
                telco_datasync_instance_id=instance.id,
            )
        except Exception:
            pass

        # celery_handle_nitroswitch_datasync(data=data)

        celery_handle_nitroswitch_datasync.apply_async(queue="celerynitroswitchdatasync", kwargs=data)

        return Response(data={"message": "success"}, status=status.HTTP_200_OK)

    def get(self, request):
        query_params = request.query_params

        data = dict(query_params)

        instance = NitroSwitchDataSync.objects.create(data=data)

        try:
            PendingNitroswitchAsyncTask.objects.get_or_create(
                purpose=PurposeChoices.MORBID_RENEWALS,
                telco_datasync_instance_id=instance.id,
            )
        except Exception:
            pass

        # celery_handle_nitroswitch_datasync(data=data)
        celery_handle_nitroswitch_datasync.apply_async(queue="celerynitroswitchdatasync", kwargs=data)

        return Response(data={"message": "success"}, status=status.HTTP_200_OK)


class NitroSwitchSMSMOApiView(APIView):
    def post(self, request):
        data = request.data

        NitroSwitchSMSMO.objects.create(data=data)

        celery_handle_nitroswitch_sms_mo_task(data)

        return Response(data={"message": "data received successfully on wisewinn"}, status=status.HTTP_200_OK)

    def get(self, request):
        query_params = request.query_params

        data = dict(query_params)

        if data == {}:
            return Response(data={"message": "data received successfully on wisewinn"}, status=status.HTTP_200_OK)

        NitroSwitchSMSMO.objects.create(data=data)

        celery_handle_nitroswitch_sms_mo_task(data)

        return Response(data={"message": "data received successfully on wisewinn"}, status=status.HTTP_200_OK)


class OpenAIPromptView(APIView):
    serializer_class = OpenAIPromptSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        prompt = serializer.validated_data.get("prompt")

        ask_gpt = NitroSwitchSmsToAiSubscription.sms_to_ai_prompt(prompt)
        ask_gpt_response = ask_gpt.get("message")

        data = {"question": prompt, "gpt response": ask_gpt_response}

        return Response(data=data, status=status.HTTP_200_OK)


class LibertyPayUssdInteraction(APIView):
    def post(self, request):
        request_body = request.data

        phone_number = request_body["phoneNumber"].strip("+")
        service_code = request_body["serviceCode"]
        text = request_body["text"]
        session_id = request_body["sessionId"]
        network_code = request_body["networkCode"]

        response = liberty_pay_ussd_menu_imteraction(
            phone_number=phone_number, service_code=service_code, text=text, session_id=session_id, network_code=network_code
        )

        return HttpResponse(response)


class TelcoAggregatorJsonUssdNotificationApiview(APIView):
    def post(self, request):
        data = request.data

        live_new_bbc_ussd_menu(data)

        return Response(data={"status": 200, "message": "USSD request successfully received"}, status=status.HTTP_200_OK)


class TelcoAggregatorJsonSmsNotificationApiview(APIView):
    def post(self, request):
        data = request.data

        new_telco_sms_subscription_feature(data)

        return Response(data={"status": 200, "message": "Callback processed successfully"}, status=status.HTTP_200_OK)


class NewBroadBaseDatasyncApiView(APIView):
    def post(self, request):
        data = request.data

        new_task_to_process_telco_sync.apply_async(
            args=[data],
            queue=choice(["process_telco_sync_queue", "process_telco_sync_queue1", "process_telco_sync_queue3"]),
        )
        # new_task_to_process_telco_sync(data)

        return Response(data={"status": 200, "message": "Callback processed successfully"}, status=status.HTTP_200_OK)


class SecuredStGloDataSyncView(APIView):
    permission_classes = [AllowAny]
    authentication_classes = []

    def post(self, request):
        secured_instance = SecureDDataDump.objects.create(
            data=request.body, ip_adress=request.META.get("HTTP_X_FORWARDED_FOR"), source="ST_GLO"
            )
        send_marketing_partners_postback_task.apply_async(
                kwargs={
                    "instance_id": secured_instance.id,
                },
                queue="celery_ads_postback",
            )

        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            x_forwarded_for.split(",")[0]
        else:
            request.META.get("REMOTE_ADDR")

        request_data = request.body

        try:
            data_str = request_data.decode("utf-8")
            result_dict = json.loads(data_str)
        except Exception:
            result_dict = {}

            data = {"message": "success", "timestamp": datetime.now()}

            return Response(data, status=status.HTTP_200_OK)

        # trans_ref = result_dict.get("trxId")
        # if "upstream" in trans_ref:
        #     if ip == settings.SECURED_D_IP_ADDRESS:
        #         return Response({"message": "success"}, status=status.HTTP_200_OK)

        # print("CALLING celery_secure_d_data_sync")
        celery_secure_d_data_sync.apply_async(queue="bbc_datasync", kwargs=result_dict)
        # print("celery_secure_d_data_sync response", response)

        data = {"message": "success", "timestamp": datetime.now()}

        return Response(data, status=status.HTTP_200_OK)

    def get(self, request):
        secured_instance = SecureDDataDump.objects.create(
            data=request.body, ip_adress=request.META.get("HTTP_X_FORWARDED_FOR"), source="ST_GLO"
            )
        send_marketing_partners_postback_task.apply_async(
                kwargs={
                    "instance_id": secured_instance.id,
                },
                queue="celery_ads_postback",
            )

        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            x_forwarded_for.split(",")[0]
        else:
            request.META.get("REMOTE_ADDR")

        request_data = request.body

        try:
            data_str = request_data.decode("utf-8")
            result_dict = json.loads(data_str)
        except Exception:
            result_dict = {}

            data = {"message": "success", "timestamp": datetime.now()}

            return Response(data, status=status.HTTP_200_OK)

        # trans_ref = result_dict.get("trxId")
        # if "upstream" in trans_ref:
        #     if ip == settings.SECURED_D_IP_ADDRESS:
        #         return Response({"message": "success"}, status=status.HTTP_200_OK)

        # print("CALLING celery_secure_d_data_sync")
        celery_secure_d_data_sync.apply_async(queue="bbc_datasync", kwargs=result_dict)
        # print("celery_secure_d_data_sync response", response)

        data = {"message": "success", "timestamp": datetime.now()}

        return Response(data, status=status.HTTP_200_OK)




class UssdSalaryForLifeGameShowParticipanInterestApiview(APIView):
    def post(self, request):

        request_body = request.data

        phone_number = request_body["phoneNumber"].strip("+")
        service_code = request_body["serviceCode"]
        text = request_body["text"]
        session_id = request_body["sessionId"]
        network_code = request_body["networkCode"]

        response = ussd_salary_for_life_game_show_menue_interaction(
            phone_number=phone_number, service_code=service_code, text=text, session_id=session_id, network_code=network_code
        )

        return HttpResponse(response)
    



class RedoceanAfricaSubscriptionDataSyncApiView(APIView):
    def post(self, request):
        data = request.data

        RedoceanAfricaSubscriptionDataSync.objects.create(data=data)
    

        return Response(data={"message": "success"}, status=status.HTTP_200_OK)
    

class RedoceanAfricaContentDeliveryDataSyncApiView(APIView):
    def post(self, request):
        data = request.data

        RedoceanAfricaContentDeliveryDataSync.objects.create(data=data)
    

        return Response(data={"message": "success"}, status=status.HTTP_200_OK)
    


class RedoceanAfricaFetchPredictWiseGamesApiView(APIView):
    serializer_class = RedoceanAfricaFetchPredictWiseGamesSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data.get("phone_number")

        try:
            RedoceanAfricaSubscriber.objects.get(phone_number=phone_number, is_subscribed = True)
        except RedoceanAfricaSubscriber.DoesNotExist:

            # check if user already has an active plan
            redocean_gateway_instance = RedoceanAfricaGatewayHelper().check_the_subscription_status_of_a_phone_number(
                phone_number = phone_number
            )


            is_subscribed = redocean_gateway_instance.get("data", {}).get("is_subscribed", False)
            expiry_date = redocean_gateway_instance.get("data", {}).get("expiry_date", None)


            
            if is_subscribed is False:
                RedoceanAfricaGatewayHelper().subscribe_a_phone_number(phone_number=phone_number)
                return Response(
                    {"message": "Please authorise the subscription prompt sent to your phone",
                    "msg_code": "01",
                    },
                    status=status.HTTP_200_OK,
                )
        
        today = timezone.now().date()
        today_fixtures = PredictionTable.objects.filter(fixture_date__date=today)

        # Get tomorrow's fixtures
        tomorrow = today + timedelta(days=1)
        tomorrow_fixtures = PredictionTable.objects.filter(fixture_date__date=tomorrow)


        serialized_todays_data = WebSoccerPredictionSerializer(today_fixtures, many=True).data
        serialized_tomorrows_data = WebSoccerPredictionSerializer(tomorrow_fixtures, many=True).data

        data = {
            "message": "success",
            "msg_code": "00",
            "data": {
                "todays_data": serialized_todays_data,
                "next_day_data": serialized_tomorrows_data
            }
        }


        return Response(data=data, status=status.HTTP_200_OK)


class SetRedoceanWebhookUrlsApiView(APIView):

    serializer_class = SetRedoceanWebhookUrlsSerializer
    
    permission_classes = [SuperUser2Permission] 

    def post(self, request):
        serializer = self.serializer_class(data = request.data)
        serializer.is_valid(raise_exception=True)

        content_delivery_wehbook_url = serializer.validated_data.get("content_delivery_wehbook_url")
        subscription_delivery_webhook_url = serializer.validated_data.get("subscription_delivery_webhook_url")

        response = RedoceanAfricaGatewayHelper().register_webhook_data(subscription_notification=subscription_delivery_webhook_url, dlr_notification = content_delivery_wehbook_url)

        return Response(data={"message": response}, status=status.HTTP_200_OK)


# ::::::::::::::::::::::::::::::::::::::
# :::::::::::::::::::::::::::::::::::::::
# :::::::::::::::::::::::::::::::::::::::::
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.db import transaction
import uuid

from .models import (
    YellowDotAfricaDatasync,
    YellowDotAfricaDailySubscription,
    YellowDotAfricaSubscriptionData,
    YellowDotAfricaSubscriber,
    YellowDotAfricaSmsMODatasync,
)
from .services import YellowDotAfricaService


class YellowDotAfricaDatasyncView(APIView):
    """
    Handle subscription and modification data sync (no action_type required)
    """

    def post(self, request):

        data = request.data

        YellowDotAfricaDatasync.objects.create(
            raw_data=data
        )

        return Response({"detail": "Data received and processed."}, status=status.HTTP_200_OK)
        
        # try:
            

        #     transaction_id = data.get("transactionId")
          
        #     # Parse dates
        #     subscription_date_str = data.get("subscriptionDate")
        #     expiration_date_str = data.get("expirationDate")

        #     subscription_date = timezone.datetime.strptime(subscription_date_str, "%Y-%m-%d").date() if subscription_date_str else None
        #     expiration_date = timezone.datetime.strptime(expiration_date_str, "%Y-%m-%d").date() if expiration_date_str else None
        #     today = timezone.now().date()

        #     # Determine subscription status
        #     if subscription_date and expiration_date:
        #         if today < subscription_date:
        #             subscription_status = 'FAILED'
        #         elif subscription_date <= today <= expiration_date:
        #             subscription_status = 'ACTIVATION'
        #         else:
        #             subscription_status = 'DEACTIVATED'
        #     else:
        #         subscription_status = 'FAILED'

        #     # Create datasync entry
        #     datasync = YellowDotAfricaDatasync.objects.create(
        #         raw_data=data
        #     )

        #     # Create daily subscription log
        #     YellowDotAfricaDailySubscription.objects.create(
        #         phone_number=data.get("msisdn") or data.get("phone_number"),
        #         service_name=data.get("service_name"),
        #         subscription_type='DAILY',
        #         subscription_status=subscription_status,
        #         amount=data.get("amount", 0),
        #         service_id=data.get("serviceId") or data.get("service_id"),
        #         product_id=data.get("product_id"),
        #         transaction_id=transaction_id,
        #         subscription_date=subscription_date_str,
        #         expiration_date=expiration_date_str,
        #         datasync_reference=datasync
        #     )

        #     # Always treat this as a new or updated subscription entry
        #     YellowDotAfricaSubscriptionData.objects.update_or_create(
        #         phone_number=data.get("msisdn") or data.get("phone_number"),
        #         product_id=data.get("product_id"),
        #         status='ACTIVE',
        #         defaults={
        #             "service_name": data.get("service_name"),
        #             "subscription_type": 'DAILY',
        #             "service_id": data.get("serviceId") or data.get("service_id"),
        #             "amount": data.get("amount", 0),
        #         }
        #     )

        #     return Response({"detail": "Data received and processed."}, status=201)

        # except Exception as e:
        #     return Response({"error": str(e)}, status=500)



class YellowDotAfricaUnsubscriptionView(APIView):
    """
    Handle unsubscription datasync
    """
    def post(self, request):
        try:
            data = request.data
            transaction_id = data.get("transaction_id")
            if not transaction_id:
                return Response({"detail": "transaction_id is required."}, status=400)

            datasync = YellowDotAfricaDatasync.objects.create(
                phone_number=data.get("phone_number"),
                service_name=data.get("service_name"),
                service_id=data.get("service_id"),
                product_id=data.get("product_id"),
                action_type='DELETION',
                transaction_id=transaction_id,
                amount=data.get("amount"),
                raw_data=data
            )

            YellowDotAfricaDailySubscription.objects.create(
                phone_number=data.get("phone_number"),
                service_name=data.get("service_name"),
                subscription_type='DAILY',
                subscription_status='DEACTIVATED',
                amount=data.get("amount", 0),
                service_id=data.get("service_id"),
                product_id=data.get("product_id"),
                transaction_id=transaction_id,
                datasync_reference=datasync
            )

            # Deactivate if active subscription exists
            try:
                sub = YellowDotAfricaSubscriptionData.objects.get(
                    phone_number=data.get("phone_number"),
                    product_id=data.get("product_id"),
                    status="ACTIVE"
                )
                sub.deactivate()
            except YellowDotAfricaSubscriptionData.DoesNotExist:
                pass

            return Response({"detail": "Unsubscription processed."}, status=201)
        except Exception as e:
            return Response({"error": str(e)}, status=500)


class YellowDotAfricaSmsMoDatasyncView(APIView):
    """
    Accept SMS MO datasync and store
    """
    def post(self, request):

        YellowDotAfricaSmsMODatasync.objects.create(raw_data = request.data)

        return Response({"detail": "SMS MO stored successfully."}, status=status.HTTP_200_OK)
    
        # try:
        #     data = request.data
        #     sms_mo = YellowDotAfricaSmsMODatasync.objects.create(
        #         phone_number=data.get("phone_number"),
        #         message_content=data.get("message_content"),
        #         short_code=data.get("short_code"),
        #         keyword=data.get("keyword"),
        #         transaction_id=data.get("transaction_id"),
        #         raw_data=data
        #     )
        #     return Response({"detail": "SMS MO stored successfully."}, status=201)
        # except Exception as e:
        #     return Response({"error": str(e)}, status=500)


class YellowDotAfricaSendSmsView(APIView):
    """
    Send SMS using Yellow Dot Africa service
    """
    def post(self, request):
        try:
            phone_number = request.data.get("phone_number")
            message = request.data.get("message")
            sender_id = request.data.get("sender_id")

            if not phone_number or not message:
                return Response({"detail": "phone_number and message are required."}, status=400)

            sms_service = YellowDotAfricaService()
            result = sms_service.send_sms(phone_number, message, sender_id)
            return Response(result, status=200 if result.get('success') else 400)

        except Exception as e:
            return Response({"error": str(e)}, status=500)
