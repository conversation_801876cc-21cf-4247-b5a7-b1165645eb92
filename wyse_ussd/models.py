import base64
import random
import time
import uuid
from datetime import datetime
from time import sleep
from typing import Optional

import pandas as pd
import pytz
import requests
from django.conf import settings
from django.db import models
from django.db.models import F
from django.db.models.functions import TruncDate
from django.utils import timezone

from awoof_app.helpers.api_helpers import send_prompt_to_chatgpt
from awoof_app.models import AwoofTransaction
from function_profiler import time_execution_decorator
from main.api.api_lottery_helpers import generate_game_play_id
from main.helpers.hops_restriction_helper import HopsRestrictor
from main.helpers.redis_storage import RedisStorage
from main.models import (
    LotteryBatch,
    LotteryGlobalJackPot,
    LotteryModel,
    LottoTicket,
    UserProfile,
)
from main.tasks import (
    celery_send_whatsapp_payment_notification_admin,
    lottery_play_engange_event,
)
from main.ussd.bankdb import filter_bank
from main.ussd.helpers import Utility
from overide_print import print
from pos_app.utils import <PERSON><PERSON><PERSON>ame
from prices.game_price import (
    InstantCashOutPriceModel,
    SalaryForLifePriceModel,
    TelcoInstantCashOutPriceModel,
    TelcoSalaryForLifePriceModel,
)
from wyse_ussd.enums import PurposeChoices
from wyse_ussd.helper.bank import BankManger
from wyse_ussd.helper.nitroswitch_helper import nitroswitch_sms_gateway
from wyse_ussd.helper.redocean_africa import RedoceanAfricaGatewayHelper

# class WhisperWyseUssd(models.Model):
#     pass


def full_name_split(name):
    """
    This functions split and return user names in a dictonary
    """
    splited_names = name.split()
    names = {
        "first_name": splited_names[0] if len(splited_names) > 0 else "",
        "last_name": splited_names[1] if len(splited_names) > 1 else "",
        "middle_name": splited_names[2] if len(splited_names) > 2 else "",
        "full_name": name,
    }

    return names


def phone_number_reformat(phone_number):
    """
    This functions reformat phone number to the standard format
    """
    if phone_number.startswith("234"):
        return phone_number.replace("234", "0")

    else:
        return phone_number


def random_with_N_digits():
    unique_id = "%32x" % random.getrandbits(16 * 8)
    code = unique_id
    return code


def create_wyse_ussd_user(phone_number):
    """
    check if user exist in the database;
    if none create new user else
    pass without creating user object again
    """
    phone = LotteryModel.format_number_from_back_add_234(phone_number)
    user = UserProfile.objects.filter(phone_number=phone).last()
    if user is not None:
        pass
    else:
        UserProfile.objects.create(phone_number=phone)


def create_wyse_lotto_ticket(
    phone_number,
    lottery_type,
    jackpot,
    bank,
    player_input=None,
    auto=True,
    stake_amount=0,
    telco=False,
    is_telco_subscription=False,
):
    sleep(2)

    BankManger()
    phone = LotteryModel.format_number_from_back_add_234(phone_number)

    HopsRestrictor.increment_pending_payment(phone)

    user, created = UserProfile.objects.get_or_create(phone_number=phone)
    current_batch = LotteryBatch.objects.filter(is_active=True, lottery_type=lottery_type).last()

    if current_batch is None:
        if lottery_type == "SALARY_FOR_LIFE":
            salary_for_life_jackpot = LotteryGlobalJackPot.get_jackpot_instance()
            current_batch = LotteryBatch.objects.create(
                lottery_type="SALARY_FOR_LIFE",
                global_jackpot=salary_for_life_jackpot,
            )

        else:
            current_batch = LotteryBatch.objects.create(lottery_type=lottery_type)

        sleep(2)

    if lottery_type == "SALARY_FOR_LIFE":
        stake_win_amount = LottoTicket.salary_for_life_stake_amount_pontential_winning(int(jackpot))

        if auto is True:
            lucky_number = Utility.generate_five_seperated_lucky_num()
        else:
            lucky_number = player_input

    elif lottery_type == "INSTANT_CASHOUT":
        stake_win_amount = LottoTicket.instant_stake_amount_pontential_winning(int(jackpot))

        if auto is True:
            lucky_number = Utility.generate_four_seperated_lucky_num()
        else:
            lucky_number = player_input

    game_play_id = generate_game_play_id()
    identity_id = f"{uuid.uuid4()}{int(time.time())}"

    if int(jackpot) > 1:
        lotto_tickets = []
        iteration_count = 0
        for i in range(int(jackpot)):
            iteration_count += 1

            if lottery_type == "SALARY_FOR_LIFE":
                if auto is True:
                    lucky_number = Utility.generate_five_seperated_lucky_num()
                else:
                    lucky_number = player_input

            elif lottery_type == "INSTANT_CASHOUT":
                if auto is True:
                    lucky_number = Utility.generate_four_seperated_lucky_num()
                else:
                    lucky_number = player_input

            if stake_amount == 0:
                if lottery_type == "INSTANT_CASHOUT":
                    if iteration_count == 7:
                        user_stake_amount = 100
                    else:
                        user_stake_amount = 150
                else:
                    user_stake_amount = stake_win_amount["stake_amount"] / int(jackpot)

            else:
                user_stake_amount = stake_amount / int(jackpot)

            if lottery_type == "INSTANT_CASHOUT":
                if telco is False:
                    icash_price_model = InstantCashOutPriceModel().get_ticket_price_details_with_stake_amount(
                        channel="USSD", stake_amount=stake_amount
                    )

                    if icash_price_model is None:
                        return {
                            "status": "error",
                            "message": "stake amount is invalid 1",
                        }

                    _stake_amount = icash_price_model.get("ticket_price") / int(jackpot)

                else:
                    if is_telco_subscription is False:
                        icash_price_model = TelcoInstantCashOutPriceModel().get_ticket_price_details_with_stake_amount(stake_amount=stake_amount)
                    else:
                        icash_price_model = TelcoInstantCashOutPriceModel().get_subscription_ticket_price_details_with_stake_amount(
                            stake_amount=stake_amount
                        )

                    if icash_price_model is None:
                        return {
                            "status": "error",
                            "message": "stake amount is invalid 1",
                        }

                    _stake_amount = icash_price_model.get("amount_to_register")

                _expected_amount = icash_price_model.get("total_amount")
                _potential_winning = icash_price_model.get("potential_winning")
                illusion_amount = icash_price_model.get("illusion_price", 0)

                # if telco is True:
                #     if _stake_amount == 300:
                #         _stake_amount = 200

                lotto_tickets.append(
                    LottoTicket(
                        user_profile=user,
                        batch=current_batch,
                        phone=phone,
                        stake_amount=_stake_amount,
                        potential_winning=_potential_winning,
                        expected_amount=_expected_amount / int(jackpot),
                        # paid
                        # datline_jackpot
                        number_of_ticket=int(jackpot),
                        channel="USSD",
                        game_play_id=game_play_id,
                        lottery_type=lottery_type,
                        ticket=lucky_number,
                        identity_id=identity_id,
                        illusion=illusion_amount,
                        played_via_telco_channel=telco,
                    )
                )
            else:
                if lottery_type == "SALARY_FOR_LIFE":
                    if telco is False:
                        salary_for_life_price_model = SalaryForLifePriceModel().get_ticket_price_details_with_stake_amount(
                            channel="USSD", stake_amount=stake_amount
                        )

                        if salary_for_life_price_model is None:
                            return {
                                "status": "error",
                                "message": "stake amount is invalid 2",
                            }

                        _stake_amount = salary_for_life_price_model.get("ticket_price") / int(jackpot)

                    else:
                        if is_telco_subscription is False:
                            salary_for_life_price_model = TelcoSalaryForLifePriceModel().get_ticket_price_details_with_stake_amount(
                                stake_amount=stake_amount
                            )
                        else:
                            salary_for_life_price_model = TelcoSalaryForLifePriceModel().get_subscription_ticket_price_details_with_stake_amount(
                                stake_amount=stake_amount
                            )

                        if salary_for_life_price_model is None:
                            return {
                                "status": "error",
                                "message": "stake amount is invalid 2",
                            }

                        _stake_amount = salary_for_life_price_model.get("amount_to_register")

                    _expected_amount = salary_for_life_price_model.get("total_amount")
                    _potential_winning = salary_for_life_price_model.get("potential_winning")

                    lotto_tickets.append(
                        LottoTicket(
                            user_profile=user,
                            batch=current_batch,
                            phone=phone,
                            stake_amount=_stake_amount,
                            potential_winning=_potential_winning,
                            expected_amount=_expected_amount / int(jackpot),
                            # paid
                            # datline_jackpot
                            number_of_ticket=int(jackpot),
                            channel="USSD",
                            game_play_id=game_play_id,
                            lottery_type=lottery_type,
                            ticket=lucky_number,
                            identity_id=identity_id,
                            played_via_telco_channel=telco,
                        )
                    )
                else:
                    lotto_tickets.append(
                        LottoTicket(
                            user_profile=user,
                            batch=current_batch,
                            phone=phone,
                            stake_amount=user_stake_amount,
                            potential_winning=stake_win_amount["total_winning_amount"],
                            expected_amount=user_stake_amount,
                            # paid
                            # datline_jackpot
                            number_of_ticket=int(jackpot),
                            channel="USSD",
                            game_play_id=game_play_id,
                            lottery_type=lottery_type,
                            ticket=lucky_number,
                            identity_id=identity_id,
                            played_via_telco_channel=telco,
                        )
                    )
        LottoTicket.objects.bulk_create(lotto_tickets, batch_size=100, ignore_conflicts=True)
        # user.bank_code = bank_manager.bank_code(bank)
        # user.save()
        return LottoTicket.objects.filter(user_profile__phone_number=phone).last()

    else:
        if lottery_type == "SALARY_FOR_LIFE":
            if auto is True:
                lucky_number = Utility.generate_five_seperated_lucky_num()
            else:
                lucky_number = player_input

        elif lottery_type == "INSTANT_CASHOUT":
            if auto is True:
                lucky_number = Utility.generate_four_seperated_lucky_num()
            else:
                lucky_number = player_input

        if stake_amount == 0:
            user_stake_amount = stake_win_amount["stake_amount"] / int(jackpot)

        else:
            user_stake_amount = stake_amount / int(jackpot)

        if lottery_type == "INSTANT_CASHOUT":
            if telco is False:
                icash_price_model = InstantCashOutPriceModel().get_ticket_price_details_with_stake_amount(channel="USSD", stake_amount=stake_amount)
            else:
                if is_telco_subscription is False:
                    icash_price_model = TelcoInstantCashOutPriceModel().get_ticket_price_details_with_stake_amount(stake_amount=stake_amount)
                else:
                    icash_price_model = TelcoInstantCashOutPriceModel().get_subscription_ticket_price_details_with_stake_amount(
                        stake_amount=stake_amount
                    )

            if icash_price_model is None:
                return {
                    "status": "error",
                    "message": "stake amount is invalid 3",
                }

            _expected_amount = icash_price_model.get("total_amount")
            _potential_winning = icash_price_model.get("potential_winning")
            illusion_amount = icash_price_model.get("illusion_price", 0)
            _stake_amount = icash_price_model.get("ticket_price")

            if telco is True:
                if _stake_amount == 300:
                    _stake_amount = 200

            current_batch.save()

            LottoTicket.objects.create(
                user_profile=user,
                batch=current_batch,
                phone=phone,
                stake_amount=_stake_amount / int(jackpot),
                potential_winning=_potential_winning,
                expected_amount=_expected_amount / int(jackpot),
                # paid
                # datline_jackpot
                number_of_ticket=int(jackpot),
                channel="USSD",
                game_play_id=game_play_id,
                lottery_type=lottery_type,
                ticket=lucky_number,
                identity_id=identity_id,
                illusion=illusion_amount,
                played_via_telco_channel=telco,
            )

        elif lottery_type == "SALARY_FOR_LIFE":
            if telco is False:
                salary_for_life_price_model = SalaryForLifePriceModel().get_ticket_price_details_with_stake_amount(
                    channel="USSD", stake_amount=stake_amount
                )
            else:
                if is_telco_subscription is False:
                    salary_for_life_price_model = TelcoSalaryForLifePriceModel().get_ticket_price_details_with_stake_amount(stake_amount=stake_amount)
                else:
                    salary_for_life_price_model = TelcoSalaryForLifePriceModel().get_subscription_ticket_price_details_with_stake_amount(
                        stake_amount=stake_amount
                    )

            if salary_for_life_price_model is None:
                return {
                    "status": "error",
                    "message": "stake amount is invalid 4",
                }

            _expected_amount = salary_for_life_price_model.get("total_amount")
            _potential_winning = salary_for_life_price_model.get("potential_winning")
            _stake_amount = salary_for_life_price_model.get("ticket_price")

            LottoTicket.objects.create(
                user_profile=user,
                batch=current_batch,
                phone=phone,
                stake_amount=user_stake_amount,
                potential_winning=stake_win_amount["total_winning_amount"],
                expected_amount=user_stake_amount,
                # paid
                # datline_jackpot
                number_of_ticket=int(jackpot),
                channel="USSD",
                game_play_id=game_play_id,
                lottery_type=lottery_type,
                ticket=lucky_number,
                identity_id=identity_id,
                played_via_telco_channel=telco,
            )
        else:
            LottoTicket.objects.create(
                user_profile=user,
                batch=current_batch,
                phone=phone,
                stake_amount=user_stake_amount,
                potential_winning=stake_win_amount["total_winning_amount"],
                expected_amount=user_stake_amount,
                # paid
                # datline_jackpot
                number_of_ticket=int(jackpot),
                channel="USSD",
                game_play_id=game_play_id,
                lottery_type=lottery_type,
                ticket=lucky_number,
                identity_id=identity_id,
                played_via_telco_channel=telco,
            )
        # user.bank_code = bank_manager.bank_code(bank)
        # user.save()
        return LottoTicket.objects.filter(user_profile__phone_number=phone).last()


# Create your models here.
class CoralpayTransactions(models.Model):
    # PAYMENT_CHANNEL = (
    #     ("USSD", "USSD"),
    #     ("CARD", "CARD"),
    # )

    PAYMENT_FOR = (
        ("LOTTERY_PAYMENT", "LOTTERY_PAYMENT"),
        ("WALLET_FUNDING", "WALLET_FUNDING"),
    )

    CHANNEL = (
        ("USSD", "USSD"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
    )

    coralpay_customer_ref = models.CharField(max_length=300, blank=True, null=True)
    amount = models.FloatField(null=True, blank=True)
    trace_id = models.CharField(max_length=300, blank=True, null=True)
    payment_ref = models.CharField(max_length=300, blank=True, null=True)
    bank_code = models.CharField(max_length=300, blank=True, null=True)
    customer_name = models.CharField(max_length=300, blank=True, null=True)
    customer_phone = models.CharField(max_length=300, blank=True, null=True)
    is_successful = models.BooleanField(default=False)
    ussd_code_extension = models.ForeignKey(
        "CoralpayUserCode",
        related_name="ussd_code_transaction",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )
    payment_for = models.CharField(max_length=300, choices=PAYMENT_FOR, default="LOTTERY_PAYMENT")
    channel = models.CharField(max_length=300, choices=CHANNEL, default="USSD")

    response_data = models.TextField(blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    payment_initiated = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.trace_id

    class Meta:
        verbose_name = "CORALPAY TRANSACTION"
        verbose_name_plural = "CORALPAY TRANSACTIONS"

    def save(self, *args, **kwargs):
        if not self.pk:
            self.trace_id = self.create_trace_id()

        return super(CoralpayTransactions, self).save(*args, **kwargs)

    @classmethod
    def create_trace_id(cls):
        trace_id = Utility.generate_code(length=25)

        # check if trace id exist
        if cls.objects.filter(trace_id=trace_id).exists():
            return cls.create_trace_id()
        return trace_id


class CoralpayUserCode(models.Model):
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    code = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_used = models.BooleanField(default=False)
    has_expired = models.BooleanField(default=False)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "CORAL PAY USER CODE"
        verbose_name_plural = "CORAL PAY USER CODES"

    @classmethod
    def generate_code(cls):
        user_code = Utility.generate_code()

        # for expired codes
        # check if code exist
        code_instance = cls.objects.filter(code=user_code, has_expired=False, is_used=False).last()
        if code_instance is not None:
            # check if code has expired
            created_at = code_instance.created_at
            today = datetime.now()
            time_diff = today - created_at
            if time_diff.days > 1:
                code_instance.has_expired = True
                code_instance.save()

                return user_code
            else:
                return cls.generate_code()

        else:
            return user_code

    def __str__(self):
        return self.code

    def save(self, *args, **kwargs):
        if not self.pk:
            self.code = self.generate_code()

        return super(CoralpayUserCode, self).save(*args, **kwargs)


class UssdLotteryPayment(models.Model):
    GAME_TYPE = [
        ("LOTTERY", "LOTTERY"),
        ("SOCCER", "SOCCER"),
    ]

    CHANNEL = [
        ("USSD", "USSD"),
        ("USSD_WEB", "USSD_WEB"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
    ]

    LOTTERY_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("BANKER", "BANKER"),
        ("QUIKA", "QUIKA"),
        ("AWOOF", "AWOOF"),
        ("PREDICTION_SUBSCRIPTION", "PREDICTION_SUBSCRIPTION"),
    ]

    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    amount = models.FloatField(null=True, blank=True)
    amount_paid = models.FloatField(default=0)
    game_play_id = models.CharField(max_length=300, blank=True, null=True)
    is_successful = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    payment_initiated = models.BooleanField(default=True)
    game_type = models.CharField(max_length=300, choices=GAME_TYPE, default="LOTTERY")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    channel = models.CharField(max_length=300, choices=CHANNEL, default="USSD")
    awoof_band = models.IntegerField(default=0)
    illusion_amount = models.FloatField(default=0.0)
    has_illusion = models.BooleanField(default=False)
    lottery_type = models.CharField(max_length=300, blank=True, null=True, choices=LOTTERY_TYPE_CHOICES)
    played_via_telco_channel = models.BooleanField(default=False)
    feom_telco_and_playing_from_wallet = False
    transfrom = "TELCO_FUNDING"

    class Meta:
        verbose_name = "USSD LOTTERY PAYMENT"
        verbose_name_plural = "USSD LOTTERY PAYMENTS"

    def __str__(self):
        return self.user.phone_number

    def save(self, *args, **kwargs):
        if not self.pk:
            if UssdLotteryPayment.objects.filter(game_play_id=self.game_play_id).exists():
                pass
            else:
                return super(UssdLotteryPayment, self).save(*args, **kwargs)
        else:
            if (self.played_via_telco_channel is True) and (self.amount_paid >= self.amount) and (self.is_verified is False):
                from wyse_ussd.tasks import share_ussd_payment_across_lottery_pool

                if settings.DEBUG is True:
                    share_ussd_payment_across_lottery_pool.delay(
                        phone_number=self.user.phone_number,
                        amount=self.amount_paid,
                        game_play_id=self.game_play_id,
                        transfrom=self.transfrom,
                        from_telco_channel=True,
                        from_web=self.feom_telco_and_playing_from_wallet,
                    )
                else:
                    share_ussd_payment_across_lottery_pool.apply_async(
                        queue="telcocharge1",
                        kwargs={
                            "phone_number": self.user.phone_number,
                            "amount": self.amount_paid,
                            "game_play_id": self.game_play_id,
                            "transfrom": self.transfrom,
                            "from_telco_channel": True,
                            "from_web": self.feom_telco_and_playing_from_wallet,
                        },
                    )

                if settings.DEBUG is True:
                    celery_send_whatsapp_payment_notification_admin.delay(
                        phone_number=self.user.phone_number,
                        batch_id="",
                        amount=self.amount_paid,
                        paid_via="Telco",
                    )
                else:
                    celery_send_whatsapp_payment_notification_admin.apply_async(
                        queue="telcocharge1",
                        kwargs={
                            "phone_number": self.user.phone_number,
                            "batch_id": "",
                            "amount": self.amount_paid,
                            "paid_via": "Telco",
                        },
                    )

            return super(UssdLotteryPayment, self).save(*args, **kwargs)

    # class Meta:
    #     verbose_name = "USSD LOTTERY PAYMENT"
    #     verbose_name_plural = "USSD LOTTERY PAYMENTS"


class SoccerPrediction(models.Model):
    PLAY_TYPE = [
        ("PERSONAL", "PERSONAL"),
        ("SHARED", "SHARED"),
    ]
    LOTTO_TYPE = [("SOCCER_CASH", "SOCCER_CASH")]

    PREDICTION_CHANNEL = [
        ("USSD", "USSD"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
        ("POS_AGENT", "POS_AGENT"),
    ]

    user_profile = models.ForeignKey(UserProfile, on_delete=models.CASCADE, null=True, blank=True)
    agent = models.ForeignKey("pos_app.Agent", on_delete=models.CASCADE, null=True, blank=True)
    football_table = models.ForeignKey(
        "sport_app.FootballTable",
        on_delete=models.CASCADE,
        related_name="game_model",
        null=True,
        blank=True,
    )
    bought_lottery_ticket = models.ForeignKey("pos_app.BoughtLotteryTickets", on_delete=models.CASCADE, null=True, blank=True)
    phone = models.CharField(max_length=300)
    game_id = models.CharField(max_length=150, null=True, blank=True)
    home_choice = models.PositiveIntegerField(null=True, blank=True)
    away_choice = models.PositiveIntegerField(null=True, blank=True)
    band_played = models.CharField(max_length=150, null=True, blank=True)
    stake_amount = models.FloatField(default=0.00)
    potential_winning = models.FloatField(default=0.00)
    amount_paid = models.FloatField(default=0.00)
    paid = models.BooleanField(default=False)
    game_fixture_id = models.CharField(max_length=150, null=True, blank=True)
    account_no = models.CharField(max_length=150, null=True, blank=True)
    bank_name = models.CharField(max_length=150, null=True, blank=True)
    bank_code = models.CharField(max_length=150, null=True, blank=True)
    date = models.DateTimeField(auto_now_add=True)
    paid_date = models.DateTimeField(null=True, blank=True)
    channel = models.CharField(max_length=150, choices=PREDICTION_CHANNEL, default="USSD")
    freemium = models.BooleanField(default=False)
    is_drawn = models.BooleanField(default=False)
    score_checked = models.BooleanField(default=False)
    won = models.BooleanField(default=False)
    active = models.BooleanField(default=True)
    is_score_valid = models.BooleanField(default=False)
    play_type = models.CharField(max_length=250, choices=PLAY_TYPE, default="SHARED")
    lottery_type = models.CharField(max_length=250, choices=LOTTO_TYPE, default="SOCCER_CASH")

    class Meta:
        verbose_name = "SOCCER PREDICTION"
        verbose_name_plural = "SOCCER PREDICTIONS"
        indexes = [
            models.Index(fields=["game_id"]),
        ]

    @staticmethod
    def stake_and_win_amount(desired_play: int, from_telco: bool) -> dict:
        if desired_play == 1:
            if from_telco is True:
                eqn = {"stake_amount": 100, "win_amount": 1500}
            else:
                eqn = {"stake_amount": 200, "win_amount": 1500}
        elif desired_play == 2:
            if from_telco is True:
                eqn = {"stake_amount": 500, "win_amount": 5000}
            else:
                eqn = {"stake_amount": 500, "win_amount": 5000}
        elif desired_play == 3:
            if from_telco is True:
                eqn = {"stake_amount": 800, "win_amount": 12000}
            else:
                eqn = {"stake_amount": 1000, "win_amount": 12000}
        elif desired_play == 4:
            if from_telco is True:
                eqn = {"stake_amount": 1200, "win_amount": 20000}
            else:
                eqn = {"stake_amount": 2000, "win_amount": 20000}
        return eqn

    _SOCCER_STAKE_AMOUNT_POTENTIAL_WIN = {
        # web
        200: {"stake_amount": 200, "win_amount": 1500},
        500: {"stake_amount": 500, "win_amount": 5000},
        1000: {"stake_amount": 1000, "win_amount": 12000},
        2000: {"stake_amount": 2000, "win_amount": 20000},
        # pos
        250: {"stake_amount": 250, "win_amount": 1500},
        600: {"stake_amount": 600, "win_amount": 5000},
        1200: {"stake_amount": 1200, "win_amount": 12000},
        2500: {"stake_amount": 2500, "win_amount": 20000},
    }

    _GENERAL_BAND_PLAYED = {
        250: 200,
        600: 500,
        1200: 1000,
        2500: 2000,
    }

    @staticmethod
    def stake_and_win_amount_web(amount: int) -> dict:
        return SoccerPrediction._SOCCER_STAKE_AMOUNT_POTENTIAL_WIN.get(amount, None)

    @classmethod
    def general_band_played(cls, amount: int) -> Optional[int]:
        return cls._GENERAL_BAND_PLAYED.get(amount, None)

    @classmethod
    def create_leauge_prediction_object(
        cls, desired_play, prediction, phone_no, fixtures_id, from_telco=False, bank=None, type_of_request="SOCCER_CASH"
    ):  # USSD
        from sport_app.models import FootballTable

        phone_no = LotteryModel.format_number_from_back_add_234(phone_no)
        game_id = generate_game_play_id()

        stake_win_amount = cls.stake_and_win_amount(int(desired_play), from_telco=from_telco)
        user = UserProfile.objects.filter(phone_number=phone_no).last()
        BankManger()
        # bank_properties = bank_manager.bank_details(bank)

        football_table = FootballTable.objects.get(fixture_id=fixtures_id)

        try:
            class_ins = cls.objects.create(
                user_profile=user,
                football_table=football_table,
                phone=phone_no,
                game_id=game_id,
                home_choice=int(prediction[0]),
                away_choice=int(prediction[1]),
                band_played=stake_win_amount["stake_amount"],
                stake_amount=stake_win_amount["stake_amount"],
                potential_winning=stake_win_amount["win_amount"],
                game_fixture_id=fixtures_id,
                # account_no
                # bank_name=bank_properties["bank_short_name"],
                # bank_code=bank_properties["bank_code"],
            )
        except Exception:
            return None

        try:
            SoccerPredictionRequestLogs.objects.create(
                phone_number=phone_no, type_of_request=type_of_request, amount=float(stake_win_amount["stake_amount"])
            )
        except Exception:
            pass

        # UssdLotteryPayment.objects.create(
        #     user=user,
        #     amount=stake_win_amount["stake_amount"],
        #     game_play_id=game_id,
        #     game_type="SOCCER",
        #     lottery_type="SOCCER_CASH",
        # )

        return class_ins

    @classmethod
    def soccer_cash_web_success_response(cls, fixtures, game_play_id, user, method, play_type):  # web
        from sport_app.models import FootballTable

        # This functions structures the success response on game play

        game_play = []
        for i in range(len(fixtures)):
            games_by_fixtures_id = cls.objects.filter(game_id=game_play_id, game_fixture_id=fixtures[i])

            predictions = []
            for game in games_by_fixtures_id:
                prediction = {
                    "id": game.id,
                    "home_choice": game.home_choice,
                    "away_choice": game.away_choice,
                    "freemium": game.freemium,
                    "stake_amount": game.stake_amount,
                }

                predictions.append(prediction)
            football_fixtures_id = game.game_fixture_id
            football_table = FootballTable.objects.filter(fixture_id=football_fixtures_id).last()

            game_play_dict = {
                "fixtures_id": football_fixtures_id,
                "home_team": football_table.home_team,
                "away_team": football_table.away_team,
                "home_logo": football_table.home_logo,
                "away_logo": football_table.away_logo,
                "fixture_date": football_table.fixture_date,
                # "stake_amount": game.stake_amount,
                "predictions": predictions,
            }

            game_play.append(game_play_dict)

        prediction_query_set = cls.objects.filter(game_id=game_play_id).values()

        prediction_df = pd.DataFrame(prediction_query_set)

        to_pay_game_play_amount = prediction_df.query("freemium is False")["stake_amount"].sum()

        winning_amount = prediction_df["potential_winning"].sum()

        if method == "POST":
            UssdLotteryPayment.objects.create(
                user=user,
                amount=to_pay_game_play_amount,  # total stake amount
                game_play_id=game_play_id,
                game_type="SOCCER",
                channel="WEB",
                lottery_type="SOCCER_CASH",
            )
        else:
            pass
        if play_type == "PERSONAL":
            return {
                "succeeded": True,
                "play_type": play_type,
                "message": "game play was successful",
                "game_play_id": game_play_id,
                "game_summary": game_play,
                "matches_played": prediction_df["game_fixture_id"].nunique(),
                "total_predictions": len(prediction_df),
                "total_stake_amount": to_pay_game_play_amount,
                # "winning_amount": winning_amount,
            }
        else:
            return {
                "succeeded": True,
                "play_type": play_type,
                "message": "game play was successful",
                "game_play_id": game_play_id,
                "game_summary": game_play,
                "matches_played": prediction_df["game_fixture_id"].nunique(),
                "total_predictions": len(prediction_df),
                "total_stake_amount": to_pay_game_play_amount,
                "winning_amount": winning_amount,
            }

    @classmethod
    def create_match_prediction_web(cls, phone_no, game_play, play_type):  # web
        # create soccer prediction object

        from sport_app.models import FootballTable

        game_play_id = generate_game_play_id()
        fixtures = []
        for match_count in range(len(game_play)):
            fixtures_id = game_play[match_count].get("fixtures_id")
            fixtures.append(fixtures_id)

            # gather all predictions and create tickets
            prediction_list = []
            user = UserProfile.objects.get(phone_number=phone_no)

            football_table = FootballTable.objects.get(fixture_id=fixtures_id)

            predictions = game_play[match_count].get("predictions")

            # total_stake_amount = 0

            for prediction_count in range(len(predictions)):
                stake_amount = predictions[prediction_count].get("stake_amount")

                # print(stake_amount, "Stake Amount")
                win_amount = SoccerPrediction.stake_and_win_amount_web(stake_amount).get("win_amount")

                freemium = predictions[prediction_count].get("freemium")

                # if freemium is False:
                #     total_stake_amount += stake_amount

                prediction_list.append(
                    cls(
                        user_profile=user,
                        football_table=football_table,
                        phone=phone_no,
                        game_id=game_play_id,
                        home_choice=predictions[prediction_count].get("home_choice"),
                        away_choice=predictions[prediction_count].get("away_choice"),
                        band_played=stake_amount,
                        stake_amount=stake_amount,
                        potential_winning=0 if play_type == "PERSONAL" else win_amount,
                        amount_paid=stake_amount if freemium is True else 0,
                        paid=True if freemium is True else False,
                        game_fixture_id=fixtures_id,
                        channel="WEB",
                        freemium=freemium,
                        play_type=play_type,
                    )
                )
            SoccerPrediction.objects.bulk_create(prediction_list, batch_size=100, ignore_conflicts=True)

        engage_event_payload = {
            "event": "SOCCER CASH PLAY",
            "properties": {
                "game_id": game_play_id,
            },
        }
        lottery_play_engange_event.delay(user_id=user.id, is_user_profile_id=True, **engage_event_payload)

        return cls.soccer_cash_web_success_response(fixtures, game_play_id, user, method="POST", play_type=play_type)

    @classmethod
    def count_of_existing_yet_to_play_freemium_game(cls, phone_no, freemium_count) -> int:  # web
        from sport_app.models import FootballTable

        existing_freemium_prediction = cls.objects.filter(phone=phone_no, freemium=True).order_by("-id")[:freemium_count]

        valid_freemium_count = 0
        for game in existing_freemium_prediction:
            fixture_id = game.game_fixture_id
            valid_match = FootballTable.objects.filter(fixture_id=fixture_id, fixture_date__gte=timezone.now()).last()

            if valid_match is not None:
                valid_freemium_count += 1

            else:
                continue

        return valid_freemium_count

    @classmethod
    def agent_player_exist(cls, agent_instane, game_id) -> bool:
        return cls.objects.filter(agent=agent_instane, user_profile__isnull=False, game_id=game_id).exists()

    @classmethod
    def create_predictions_winners(cls, amount_won, prediction_ins):
        from pos_app.models import PosLotteryWinners
        from sport_app.decisioning_engine.fund_wallet import fund_user_or_agent_wallet
        from sport_app.models import AgentSoccerCashWinner, SoccerCashWinner

        agent = prediction_ins.agent
        player = prediction_ins.user_profile
        game_play_unique_id = prediction_ins.game_id
        fixtures_id = prediction_ins.game_fixture_id
        stake_amount = prediction_ins.stake_amount
        phone = prediction_ins.phone

        if prediction_ins.channel == "POS_AGENT":
            from pos_app.sport_helper.func import generate_withdrawal_pin

            pin = generate_withdrawal_pin()
            agent_player_exist = SoccerPrediction.agent_player_exist(agent_instane=agent, game_id=game_play_unique_id)
            #
            # if not player:
            #     from main.models import UserProfile
            #     player = UserProfile.create_user_profile_if_none_exist(
            #         phone_no=agent.phone, channel="POS"
            #     )
            # else:
            #     player = player

            PosLotteryWinners.objects.create(
                agent=agent,
                player=player,
                game_id=game_play_unique_id,
                # game_fixture_id=fixtures_id,
                amount_won=amount_won,
                pin=pin,
                lottery_type="SOCCER_CASH",
                expiry_date = PosLotteryWinners.get_expiry_date()
            )
            AgentSoccerCashWinner.create_winner_object(
                agent_player_exist,
                phone_no=phone,
                player_id=agent.id,
                agent=agent,
                user_profile=player,
                game_id=game_play_unique_id,
                game_fixture_id=fixtures_id,
                stake_amount=stake_amount,
                amount_won=amount_won,
                pin=pin,
            )

        else:
            SoccerCashWinner.create_winner_object(
                prediction_ins,
                phone_number=phone,
                game_play_id=game_play_unique_id,
                player_id=player.id,
                stake_amount=stake_amount,
                earning=amount_won,
                channel_played_from=prediction_ins.channel,
                game_fixture_id=fixtures_id,
            )

        fund_user_or_agent_wallet(
            agent_id=agent.id if prediction_ins.channel == "POS_AGENT" else None,
            amount_won=amount_won,
            phone_no=phone,
            game_play_id=fixtures_id,
        )

    @property
    def game_status(self):
        if not self.paid:
            status = "pending payment"
        elif self.paid and not self.is_drawn:
            status = "awaiting_draw"
        elif self.is_drawn and self.won:
            status = "won"
        elif self.is_drawn and not self.won:
            status = "lost"
        else:
            status = "false"
        return status

    # @classmethod
    # def create_predictions_winners(cls, amount_won, prediction_ins):
    #     from pos_app.models import PosLotteryWinners
    #     from sport_app.decisioning_engine.fund_wallet import fund_user_or_agent_wallet
    #     from sport_app.models import AgentSoccerCashWinner, SoccerCashWinner

    #     agent = prediction_ins.agent
    #     player = prediction_ins.user_profile
    #     game_play_unique_id = prediction_ins.game_id
    #     fixtures_id = prediction_ins.game_fixture_id
    #     stake_amount = prediction_ins.stake_amount
    #     phone = prediction_ins.phone

    #     if prediction_ins.channel == "POS_AGENT":
    #         from pos_app.sport_helper.func import generate_withdrawal_pin

    #         pin = generate_withdrawal_pin()
    #         agent_player_exist = SoccerPrediction.agent_player_exist(agent_instane=agent, game_id=game_play_unique_id)
    #         #
    #         # if not player:
    #         #     from main.models import UserProfile
    #         #     player = UserProfile.create_user_profile_if_none_exist(
    #         #         phone_no=agent.phone, channel="POS"
    #         #     )
    #         # else:
    #         #     player = player

    #         PosLotteryWinners.objects.create(
    #             agent=agent,
    #             player=player,
    #             game_id=game_play_unique_id,
    #             # game_fixture_id=fixtures_id,
    #             amount_won=amount_won,
    #             pin=pin,
    #             lottery_type="SOCCER_CASH",
    #         )
    #         AgentSoccerCashWinner.create_winner_object(
    #             agent_player_exist,
    #             phone_no=phone,
    #             player_id=agent.id,
    #             agent=agent,
    #             user_profile=player,
    #             game_id=game_play_unique_id,
    #             game_fixture_id=fixtures_id,
    #             stake_amount=stake_amount,
    #             amount_won=amount_won,
    #             pin=pin,
    #         )

    #     else:
    #         SoccerCashWinner.create_winner_object(
    #             prediction_ins,
    #             phone_number=phone,
    #             game_play_id=game_play_unique_id,
    #             player_id=player.id,
    #             stake_amount=stake_amount,
    #             earning=amount_won,
    #             channel_played_from=prediction_ins.channel,
    #             game_fixture_id=fixtures_id,
    #         )

    #     fund_user_or_agent_wallet(
    #         agent_id=agent.id if prediction_ins.channel == "POS_AGENT" else None,
    #         amount_won=amount_won,
    #         phone_no=phone,
    #         game_play_id=fixtures_id,
    #     )


class UssdPayment(models.Model):
    PAYMENT_FOR = (
        ("LOTTERY_PAYMENT", "LOTTERY_PAYMENT"),
        ("WALLET_FUNDING", "WALLET_FUNDING"),
        ("FUNDING_TELCO_WALLET", "FUNDING_TELCO_WALLET"),
    )

    SOURCE = (
        ("REDBILLER", "REDBILLER"),
        ("WATUPAY", "WATUPAY"),
    )

    CHANNEL = (
        ("USSD", "USSD"),
        ("WEB", "WEB"),
        ("MOBILE", "MOBILE"),
    )

    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    amount = models.FloatField(null=True, blank=True)
    transaction_ref = models.CharField(max_length=300, unique=True)
    ussd_code = models.CharField(max_length=300, null=True, blank=True)
    successful = models.BooleanField(default=False)
    verified = models.BooleanField(default=False)
    payment_for = models.CharField(max_length=300, choices=PAYMENT_FOR, default="LOTTERY_PAYMENT")
    source = models.CharField(max_length=300, choices=SOURCE, default="WATUPAY")
    channel = models.CharField(max_length=300, choices=CHANNEL, default="USSD")
    data_dump = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.phone_number

    class Meta:
        verbose_name = "USSD PAYMENT"
        verbose_name_plural = "USSD PAYMENTS"

    @classmethod
    def initiate_transaction(cls, user, amount, payment_for, source, transaction_ref=None, func_iterate=5):
        """
        initiate USSD transaction
        cls: model class
        user: user instance
        amount: amount to be paid
        payment_for: payment for ... eg. LOTTERY_PAYMENT, WALLET_FUNDING
        source: payment source ... eg. WATUPAY, REDBILLER
        transaction_ref: transaction reference
        func_iterate: number of times to iterate the function

        """

        print("initiating transaction")
        if transaction_ref is None:
            transaction_ref = f"wyse{random_with_N_digits()}{user.id}"
        else:
            transaction_ref = transaction_ref

        # count = _iterator.get(user.id)
        if func_iterate < 1:
            return None

        transaction_qs = cls.objects.filter(transaction_ref=transaction_ref)
        if transaction_qs.exists():
            transaction_instance = transaction_qs.last()
            transaction = transaction_instance.transaction_ref = f"wyse-{uuid.uuid4()}-{user.id}"
            transaction_instance.save()
            transaction = transaction_instance

            print("transaction already exist")

        else:
            transaction = cls.objects.create(
                user=user,
                amount=amount,
                transaction_ref=transaction_ref,
                payment_for=payment_for,
                source=source,
            )

            print("transaction created")

        if source == "REDBILLER":
            # initiate redbiller transaction
            # redbiller_url = f"{settings.LIBERTY_VAS_BASE_URL}"
            redbiller_url = f"{settings.LIBERTY_VAS_BASE_URL}/redbiller/merchant/initiate_ussd_payment/"
            username = settings.LIBERTY_VAS_AUTH_USERNAME
            password = settings.LIBERTY_VAS_AUTH_PASSWORD

            print("initiating redbiller transaction")

            # generate random alpha numeric trans ref

            STRING_VALUE = f"{username}:{password}"
            AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Basic {AUTH_TOKEN}",
            }

            if transaction.user.first_name is None or transaction.user.first_name == "":
                # genrate random name
                name = full_name_split(DommyName(5).generate_name())
                first_name = name.get("first_name")
                last_name = name.get("last_name")

            else:
                first_name = transaction.user.first_name
                last_name = transaction.user.last_name

            bank_code_filter = filter_bank(cbn_code=transaction.user.bank_code)
            if bank_code_filter is None:
                bank_code = "000015"
            else:
                bank_code = bank_code_filter.get("bank_code")

            payload = {
                "first_name": first_name,
                "surname": last_name,
                "phone_no": phone_number_reformat(transaction.user.phone_number),
                "email": "<EMAIL>",
                "bvn": settings.USER_BVN,
                # "bank_code": "000013",
                "bank_code": bank_code,
                "amount": transaction.amount,
                "payment_reference": transaction.transaction_ref,
            }

            print("redbiller payload", payload, "\n\n\n\n")

            response = requests.post(url=redbiller_url, headers=headers, json=payload)

            print(response.status_code)
            print("redbiller response", response.text, "\n\n\n\n")

            if response.status_code != 200:
                return cls.initiate_transaction(
                    user=user,
                    amount=amount,
                    payment_for=payment_for,
                    source=source,
                    transaction_ref=transaction_ref,
                    func_iterate=func_iterate - 1,
                )

            r = response.json()
            """
            SAMPLE RESPONSE
            {
                "response":200,
                "status":"true",
                "message":"Created successfully.",
                "details":{
                    "profile":{
                        "first_name":"QILUQ",
                        "surname":"VUTUP",
                        "phone_no":"*************",
                        "email":"<EMAIL>",
                        "bvn":""
                    },
                    "account":{
                        "bank_name":"ACCESS BANK",
                        "bank_code":"000014",
                        "ussd_code":"*901*000*5756#"
                    },
                    "amount":100,
                    "reference":"wyse71c8519292f1c7c386b802d91fa436e63018",
                    "callback_url":"https://a45a-102-67-1-49.eu.ngrok.io/api/ussd/redbiller_webhook/",
                    "date":"2022-10-17 15:45:26"
                }
            }

            """
            return r.get("details", {}).get("account", {}).get("ussd_code", None)


_iterator = {}


class ExtraTelcoOptions(models.Model):
    OPTIONS = (
        ("INSTANT_CASHOUT", "INSTANT CASHOUT"),
        ("ILLUSION", "ILLUSION"),
    )
    name = models.CharField(max_length=100, choices=OPTIONS)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "EXTRA TELCO OPTION"
        verbose_name_plural = "EXTRA TELCO OPTIONS"


def ai_win_keywords_func():
    return ["money", "win", "eat", "help", "airtime"]


class UssdConstantVariable(models.Model):
    """
    USSD Constant Variable
    """

    use_telco = models.BooleanField(default=False)
    telco_options = models.ManyToManyField(ExtraTelcoOptions, blank=True)
    debt_collection = models.BooleanField(default=False)
    use_awoof_instant_cashout = models.BooleanField(default=False)
    is_airtime_giver_active = models.BooleanField(default=False)
    airtime_activation_bonus = models.FloatField(default=0.0)
    giver_randomizer = models.CharField(max_length=100, default="50-500")
    available_networks = models.ManyToManyField("TelcoAvailableNetwork")
    max_telco_winning_airtime_threshold = models.FloatField(default=300.0)
    give_airtime_on_game_win = models.BooleanField(default=False)
    games_to_give_airtime_winning = models.ManyToManyField("LotteryGameNames")
    associate_lifetsyle_item_id = models.CharField(max_length=100, default="3")
    ai_win_keywords = models.JSONField(max_length=200, default=ai_win_keywords_func)
    awoof_service_wallet_pool_amount = models.FloatField(default=50000.0)
    awoof_service_withdrawal_limit = models.FloatField(default=25000.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if self.pk:
            __original_use_telco = False
            __original_debt_collection = False
            __original_use_awoof_instant_cashout = False

            old = self.__class__.objects.get(pk=self._get_pk_val())
            for field in self.__class__._meta.fields:
                if field.name == "use_telco":
                    __original_use_telco = field.value_from_object(old)

                if field.name == "debt_collection":
                    __original_debt_collection = field.value_from_object(old)

                if field.name == "use_awoof_instant_cashout":
                    __original_use_awoof_instant_cashout = field.value_from_object(old)

            if __original_use_telco != self.use_telco:
                redis_db = RedisStorage(redis_key="is_use_telco_active")
                redis_db.set_data(str(self.use_telco))

            if __original_debt_collection != self.debt_collection:
                redis_db = RedisStorage(redis_key="is_debt_collection_active")
                redis_db.set_data(str(self.debt_collection))

            if __original_use_awoof_instant_cashout != self.use_awoof_instant_cashout:
                redis_db = RedisStorage(redis_key="is_awoof_instant_cashout_active")
                redis_db.set_data(str(self.use_awoof_instant_cashout))

        return super(UssdConstantVariable, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "USSD CONSTANT VARIABLE"
        verbose_name_plural = "USSD CONSTANT VARIABLES"

    @classmethod
    def is_use_telco_active(cls):
        """
        check if use telco is active
        """
        redis_db = RedisStorage(redis_key="is_use_telco_active")
        get_data = redis_db.get_data()

        if get_data is None:
            status = cls.objects.last().use_telco if cls.objects.exists() else False
            redis_db.set_data(str(status))
        else:
            status = get_data.decode("utf-8")
            status = True if status == "True" else False
        return status

    @classmethod
    def get_telco_options(cls):
        """
        get telco options
        """
        return cls.objects.last().telco_options.all()

    @classmethod
    def is_debt_collection_active(cls):
        """
        check if debt collection is active
        """
        redis_db = RedisStorage(redis_key="is_debt_collection_active")
        get_data = redis_db.get_data()

        if get_data is None:
            status = cls.objects.last().debt_collection if cls.objects.exists() else False
            redis_db.set_data(str(status))
        else:
            status = get_data.decode("utf-8")
            status = True if status == "True" else False

        return status

    @classmethod
    def is_awoof_instant_cashout_active(cls):
        """
        check if use telco is active
        """
        redis_db = RedisStorage(redis_key="is_awoof_instant_cashout_active")
        get_data = redis_db.get_data()

        if get_data is None:
            status = cls.objects.last().use_awoof_instant_cashout if cls.objects.exists() else False
            redis_db.set_data(str(status))
        else:
            status = get_data.decode("utf-8")
            status = True if status == "True" else False
        return status

    @classmethod
    def get_awoof_service_wallet_pool_amount(cls):
        """
        get_awoof_service_wallet_pool_amount
        """
        amount = cls.objects.last().awoof_service_wallet_pool_amount if cls.objects.exists() else 15000
        return amount

    @classmethod
    def get_awoof_service_withdrawal_limit(cls):
        """
        get_awoof_service_wallet_pool_amount
        """
        amount = cls.objects.last().awoof_service_withdrawal_limit if cls.objects.exists() else 0
        return amount

    @classmethod
    def is_give_airtime_on_game_win(cls):
        """
        check if give airtime on game win is active
        """
        return cls.objects.last().give_airtime_on_game_win

    @classmethod
    def get_max_telco_winning_airtime_threshold(cls):
        """
        get max telco winning airtime threshold
        """
        return cls.objects.last().max_telco_winning_airtime_threshold

    @classmethod
    def get_games_to_give_airtime_winning(cls):
        """
        get airtime activation bonus
        """
        last_instance = cls.objects.last()

        return [game.name for game in last_instance.games_to_give_airtime_winning.all()]

    @classmethod
    def get_associate_lifestyle_item_id(cls):
        """
        get airtime activation bonus
        """
        return cls.objects.last().associate_lifetsyle_item_id

    @classmethod
    def get_ai_win_keywords(cls):
        """
        get airtime activation bonus
        """
        return cls.objects.last().ai_win_keywords


class TelcoSubscriptionPlan(models.Model):
    GAME_TYPES = (
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("FAST_FINGERS/AWOOF", "FAST_FINGERS/AWOOF"),
        ("ASK_AI", "ASK_AI"),
    )

    SUBSCRIPTION_STATUS = (
        ("ACTIVE", "ACTIVE"),
        ("INACTIVE", "INACTIVE"),
        ("FAILED", "FAILED"),
        ("DEACTIVATED", "DEACTIVATED"),
        ("STOPPED", "STOPPED"),
    )

    SUBSCRIPTION_TYPE = (("DAILY", "DAILY"), ("ONE_TIME", "ONE_TIME"), ("ON-DEMAND", "ON-DEMAND"))

    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
        ("SECURE_D_MTN", "SECURE_D_MTN"),
    )

    VERIFICATION_METHODS = (("UPDATE_N_EFFECTIVE_TIME", "UPDATE_N_EFFECTIVE_TIME"),)

    phone_number = models.CharField(max_length=100, blank=True, null=True)
    game_type = models.CharField(max_length=100, choices=GAME_TYPES, blank=True, null=True, db_index=True)
    subscription_type = models.CharField(max_length=100, choices=SUBSCRIPTION_TYPE, default="DAILY", db_index=True)
    subscription_status = models.CharField(max_length=100, choices=SUBSCRIPTION_STATUS, default="ACTIVE", db_index=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    number_of_renewal = models.PositiveIntegerField(default=0)
    service_id = models.CharField(max_length=100, null=True, blank=True)
    product_id = models.CharField(max_length=100, null=True, blank=True)
    deactivated_date = models.DateTimeField(null=True, blank=True)
    response_payload = models.TextField(null=True, blank=True)
    network_provider = models.CharField(max_length=100, choices=NETWORK_PROVIDER, default="MTN")
    renewed = models.BooleanField(default=False)
    verification_method = models.CharField(max_length=100, choices=VERIFICATION_METHODS, blank=True, null=True)
    aggregator_update_time = models.CharField(max_length=300, blank=True, null=True)
    aggregator_effective_time = models.CharField(max_length=300, blank=True, null=True)
    transaction_verified = models.BooleanField(default=False)
    effective_time_and_phone = models.CharField(max_length=300, blank=True, null=True, unique=True)
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO SUBSCRIPTION DATA"
        verbose_name_plural = "TELCO SUBSCRIPTION DATAS"
        indexes = [
            models.Index(TruncDate("created_at"), "created_at", name="created_at_date_idx"),
            models.Index(
                fields=[
                    "phone_number",
                    "service_id",
                    "subscription_status",
                    "game_type",
                    "product_id",
                ]
            ),
            models.Index(
                fields=[
                    "phone_number",
                ]
            ),
            models.Index(
                fields=[
                    "product_id",
                ]
            ),
            models.Index(
                fields=[
                    "service_id",
                ]
            ),
            models.Index(
                fields=[
                    "subscription_type",
                ]
            ),
            models.Index(
                fields=[
                    "subscription_status",
                ]
            ),
            models.Index(
                fields=[
                    "effective_time_and_phone",
                ]
            ),
        ]

    @classmethod
    def get_game_type_by_service_id(cls, service_id=None, product_id=None, network_provider="mtn"):
        data = {
            "234102200006961": "INSTANT_CASHOUT",
            "234102200006965": "WYSE_CASH",
            "234102200006962": "SALARY_FOR_LIFE",
            "234102200006963": "FAST_FINGERS/AWOOF",
            "234102200006964": "SOCCER_CASH",
            "0017182000001707": "INSTANT_CASHOUT",
            "0017182000003867": "FAST_FINGERS/AWOOF",
            "0017182000003868": "SALARY_FOR_LIFE",
            "0017182000003869": "WYSE_CASH",
            "0017182000003870": "SOCCER_CASH",
            "**********": "INSTANT_CASHOUT",
            "**********": "INSTANT_CASHOUT",
            "**********": "WYSE_CASH",
            "**********": "WYSE_CASH",
            "**********": "SALARY_FOR_LIFE",
            "**********": "SALARY_FOR_LIFE",
            "**********": "FAST_FINGERS/AWOOF",
            "**********": "FAST_FINGERS/AWOOF",
            "**********": "SOCCER_CASH",
            "**********": "SOCCER_CASH",
        }
        r = data.get(service_id, None)
        if r is None:
            r = data.get(product_id, None)
        return r

    @classmethod
    @time_execution_decorator
    def create_record(
        cls,
        phone_number,
        amount,
        service_id,
        product_id,
        response_payload,
        subscription_status="ACTIVE",
        subscription_type="DAILY",
        network_provider="MTN",
        ip=None,
        data_sync_verified=False,
        update_time=None,
        effective_time=None,
        verification_method=None,
        effective_time_and_phone=None,
    ):
        import time

        from wyse_ussd.tasks import celery_reward_subscription_activation

        start_time = time.time()

        # Step 1: Get game type
        print(f"service_id: {service_id}", "\n\n\n")
        game_type = cls.get_game_type_by_service_id(service_id, network_provider=str(network_provider).lower())
        step1_time = time.time() - start_time
        print(f"Step 1 - Get game type: {step1_time:.3f} seconds")

        print(f"game_type: {game_type}")
        if game_type is None:
            return None

        if game_type == "SOCCER_CASH":
            game_type = "ASK_AI"

        # Step 2: Create main instance
        step2_start = time.time()
        try:
            instance = cls.objects.create(
                phone_number=phone_number,
                game_type=game_type,
                amount=amount,
                service_id=service_id,
                product_id=product_id,
                subscription_status=subscription_status,
                subscription_type=subscription_type,
                response_payload=response_payload,
                network_provider=network_provider,
                transaction_verified=data_sync_verified,
                aggregator_update_time=update_time,
                aggregator_effective_time=effective_time,
                verification_method=verification_method,
                effective_time_and_phone=effective_time_and_phone,
            )
            return instance
        except Exception as e:
            return str(e)

        step2_time = time.time() - step2_start
        print(f"Step 2 - Create main instance: {step2_time:.3f} seconds")

        # Step 3: Handle analytics
        step3_start = time.time()
        # celery_handle_daily_subscription_analytics(instance.id) # (BACK LOG )
        step3_time = time.time() - step3_start
        print(f"Step 3 - Handle analytics: {step3_time:.3f} seconds")

        # Step 4: Create daily subscription
        step4_start = time.time()
        TelcoDailySubscription.objects.create(
            phone_number=phone_number,
            game_type=instance.game_type,
            subscription_type=instance.subscription_type,
            subscription_status=subscription_status,
            amount=amount,
            service_id=instance.service_id,
            product_id=instance.product_id,
            network_provider=instance.network_provider,
            ip=ip,
            is_successful=data_sync_verified,
            effective_time_and_phone=effective_time_and_phone,
        )
        step4_time = time.time() - step4_start
        print(f"Step 4 - Create daily subscription: {step4_time:.3f} seconds")

        # Step 5: Handle daily subscription reward (if applicable)
        step5_start = time.time()
        if subscription_type == "DAILY":
            celery_reward_subscription_activation.apply_async(
                queue="telcocharge",
                args=(phone_number, game_type, network_provider),
            )
        step5_time = time.time() - step5_start
        print(f"Step 5 - Handle subscription reward: {step5_time:.3f} seconds")

        total_time = time.time() - start_time
        print(f"Total execution time: {total_time:.3f} seconds")

        return instance

    @classmethod
    def update_subscription_interval(
        cls,
        phone_number,
        service_id,
        product_id,
        interval=1,
        ip=None,
        network_provider=None,
        amount=0,
        subscription_type="DAILY",
        response_payload=None,
        data_sync_verified=False,
        update_time=None,
        effective_time=None,
        effective_time_and_phone=None,
    ) -> dict:
        pass

        game_type = cls.get_game_type_by_service_id(service_id)
        # REMEMBER TO HANDLE THIS INTENTIONAL OMMISSIONS
        # print(
        #     f"""
        # phone_number: {phone_number}
        # service_id: {service_id}
        # product_id: {product_id}
        # game_type: {game_type}
        # """
        # )
        active_subscription_plan = cls.objects.filter(
            phone_number=phone_number,
            game_type=game_type,
            service_id=service_id,
            product_id=product_id,
            subscription_status="ACTIVE",
        )

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        if active_subscription_plan.exists():
            last_instance = active_subscription_plan.last()

            try:
                TelcoDailySubscription.objects.get(effective_time_and_phone=effective_time_and_phone)
                # return {"succeeded": False, "message": "subscription alreday created"}

            except TelcoDailySubscription.DoesNotExist:
                TelcoDailySubscription.objects.create(
                    phone_number=phone_number,
                    game_type=last_instance.game_type,
                    subscription_type=last_instance.subscription_type,
                    subscription_status=last_instance.subscription_status,
                    amount=amount,
                    service_id=last_instance.service_id,
                    product_id=last_instance.product_id,
                    network_provider=last_instance.network_provider,
                    ip=ip,
                    is_successful=True,
                    effective_time_and_phone=effective_time_and_phone,
                )
            active_subscription_plan.update(
                number_of_renewal=F("number_of_renewal") + interval,
                updated_at=TODAY,
                renewed=True,
                transaction_verified=True,
            )
            # reward players 10 naira for every renewal
            # celery_reward_every_auto_renewal.apply_async(
            #     queue="telcocharge",
            #     args=(phone_number, game_type, service_id, product_id),
            # )

            return {"succeeded": True, "message": "subscription updated"}
        else:
            instance = cls.create_record(
                phone_number=phone_number,
                amount=amount,
                service_id=service_id,
                product_id=product_id,
                subscription_type=subscription_type,
                response_payload=response_payload,
                network_provider=network_provider,
                ip=ip,
                data_sync_verified=data_sync_verified,
                update_time=update_time,
                effective_time=effective_time,
                verification_method="UPDATE_N_EFFECTIVE_TIME",
                effective_time_and_phone=effective_time_and_phone,
            )

            print("instance, instance", instance, "\n\n")
            if isinstance(instance, str):
                return {"succeeded": False, "message": f"subscription not created, err: {instance}"}
            elif instance is None:
                return {"succeeded": False, "message": "subscription not created"}
            else:
                return {"succeeded": True, "message": "new subscription created"}
        # return {"succeeded": True, "message": "new subscription created"}

    @classmethod
    def deactivate_subscription(cls, phone_number, service_id=None, product_id=None, network="MTN", ip=None):
        from sport_app.models import SoccerOddPredictionTable
        from wyse_ussd.subscription_game_plays import (
            send_telco_subscription_game_play_confirmation,
        )
        from wyse_ussd.tasks import celery_handle_daily_subscription_analytics

        print(
            """
        phone_number: {phone_number}
        service_id: {service_id}
        """
        )

        if service_id is not None:
            game_type = cls.get_game_type_by_service_id(service_id=service_id)

            active_subscription_plan = cls.objects.filter(
                phone_number=phone_number,
                game_type=game_type,
                service_id=service_id,
                subscription_status="ACTIVE",
            )
        else:
            game_type = cls.get_game_type_by_service_id(product_id=product_id, network_provider="glo")

            active_subscription_plan = cls.objects.filter(
                phone_number=phone_number,
                game_type=game_type,
                product_id=product_id,
                subscription_status="ACTIVE",
            )

        print("active_subscription_plan", active_subscription_plan, "\n\n")

        if active_subscription_plan.exists():
            instance = active_subscription_plan.last()

            TelcoDailySubscription.objects.create(
                phone_number=phone_number,
                game_type=instance.game_type,
                subscription_type=instance.subscription_type,
                subscription_status="DEACTIVATED",
                amount=instance.amount,
                service_id=instance.service_id,
                product_id=instance.product_id,
                network_provider=instance.network_provider,
                ip=ip,
            )

            celery_handle_daily_subscription_analytics(instance.id)

            active_subscription_plan.update(
                subscription_status="DEACTIVATED",
                deactivated_date=timezone.now(),
            )

            if instance.game_type == "SOCCER_CASH":
                SoccerOddPredictionTable.objects.filter(
                    phone_number=phone_number,
                    is_active=True,
                    amount_paid=instance.amount,
                ).update(is_active=False)

            if instance.game_type == "SALARY_FOR_LIFE":
                send_telco_subscription_game_play_confirmation(phone_number, "None", "SALARY FOR LIFE", network=network)

            return active_subscription_plan

        return None


class UserTelcoSubscription(models.Model):
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    telco_subscription_plan = models.ForeignKey(TelcoSubscriptionPlan, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "USER TELCO SUBSCRIPTION"
        verbose_name_plural = "USER TELCO SUBSCRIPTIONS"


class UserTelcoSubscriptionTransaction(models.Model):
    TRANSACTION_STATUS = (
        ("PENDING", "PENDING"),
        ("SUCCESSFUL", "SUCCESSFUL"),
        ("FAILED", "FAILED"),
    )

    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    transaction_ref = models.CharField(max_length=100, unique=True)
    transaction_status = models.CharField(max_length=100, choices=TRANSACTION_STATUS, default="PENDING")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "USER TELCO SUBSCRIPTION TRANSACTION"
        verbose_name_plural = "USER TELCO SUBSCRIPTION TRANSACTIONS"


class TelcoCharge(models.Model):
    STATUS = (
        ("PENDING", "PENDING"),
        ("SUCCESSFUL", "SUCCESSFUL"),
        ("FAILED", "FAILED"),
    )

    LOTTO_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("QUIKA", "QUIKA"),  # new game
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("BANKER", "BANKER"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("AWOOF", "AWOOF"),
        ("SOCCER_CASH", "SOCCER_CASH"),
    ]

    type = (
        ("ON-DEMAND", "ON-DEMAND"),
        ("SUBSCRIPTION", "SUBSCRIPTION"),
    )

    channel = (
        ("USSD", "USSD"),
        ("SMS", "SMS"),
    )

    CHARGE_REASON = [
        ("GAME_PLAY", "GAME_PLAY"),
        ("ASK_AI", "ASK_AI"),
    ]

    phone_number = models.CharField(max_length=100)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    pontential_winning = models.DecimalField(default=0.0, max_digits=10, decimal_places=2)
    reference = models.CharField(max_length=300, unique=True)
    payload = models.TextField()
    response = models.TextField(null=True, blank=True)
    lottery_type = models.CharField(max_length=150, choices=LOTTO_TYPE, default="SALARY_FOR_LIFE", db_index=True)
    data_sync_response = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=100, choices=STATUS, default="PENDING")
    game_play_id = models.CharField(max_length=300, null=True, blank=True)
    sms_sent = models.BooleanField(default=False)
    charge_type = models.CharField(max_length=100, choices=type, default="ON-DEMAND")
    channel = models.CharField(max_length=100, choices=channel, default="USSD")
    charge_reason = models.CharField(max_length=100, choices=CHARGE_REASON, default="GAME_PLAY")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO CHARGE"
        verbose_name_plural = "TELCO CHARGES"
        indexes = [
            models.Index(
                fields=[
                    "charge_type",
                ]
            ),
            models.Index(
                fields=[
                    "amount",
                ]
            ),
            models.Index(
                fields=[
                    "phone_number",
                ]
            ),
            models.Index(
                fields=[
                    "status",
                ]
            ),
        ]

    @classmethod
    def create_charge(
        cls,
        phone_number,
        amount,
        reference,
        payload,
        pontential_winning,
        game_play_id,
        lottery_type,
        charge_type="ON-DEMAND",
        channel="USSD",
        charge_reason="GAME_PLAY",
    ):
        return cls.objects.create(
            phone_number=phone_number,
            amount=amount,
            reference=reference,
            payload=payload,
            pontential_winning=pontential_winning,
            game_play_id=game_play_id,
            lottery_type=lottery_type,
            charge_type=charge_type,
            channel=channel,
            charge_reason=charge_reason,
        )


class TelcoDataSync(models.Model):
    data = models.TextField()
    serilaized_data = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO DATA SYNC"
        verbose_name_plural = "TELCO DATA SYNCS"


# TEMP TELCO DATA SYNC
# TO BE REMOVED AFTER MTN UAT FOR THE NEW DATA FORMAT AND NEW GAME
class TempTelcoDataSync(models.Model):
    data = models.TextField()
    serilaized_data = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TEMP TELCO DATA SYNC"
        verbose_name_plural = "TEMP TELCO DATA SYNCS"


# TEMP TELCO DATA SYNC
# TO BE REMOVED AFTER MTN UAT FOR THE NEW DATA FORMAT AND NEW GAME


class TelcoDataSyncJsons(models.Model):
    serilaized_data = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    using = "external2"

    class Meta:
        verbose_name = "TELCO DATA JSON"
        verbose_name_plural = "TELCO DATA JSONS"


class TelcoConstant(models.Model):
    SMS_GATEWAYS = (("WHISPER_SMS", "WHISPER_SMS"), ("TELCO", "TELCO"))

    SMS_BANK_DETAILS_MESSAGE_CHOICES = (
        ("ON_FAILED_CHARGE/PENDING", "ON_FAILED_CHARGE/PENDING"),
        ("ALL_TIME", "ALL_TIME"),
    )

    sms_gateway = models.CharField(max_length=100, choices=SMS_GATEWAYS, default="WHISPER_SMS")
    collect_bank_details = models.BooleanField(default=False)
    sms_bank_details_message = models.CharField(
        max_length=100,
        choices=SMS_BANK_DETAILS_MESSAGE_CHOICES,
        default="ON_FAILED_CHARGE/PENDING",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO CONSTANT"
        verbose_name_plural = "TELCO CONSTANTS"

    @classmethod
    def get_sms_gateway(cls):
        return cls.objects.last().sms_gateway if cls.objects.exists() else "TELCO"

    @classmethod
    def get_collect_bank_details(cls):
        redis_storage = RedisStorage("get_collect_bank_details")
        get_data = redis_storage.get_data()
        if get_data is None:
            status = cls.objects.last().collect_bank_details if cls.objects.exists() else False
            redis_storage.set_data(str(status))

            return status

        return True if str(get_data.decode("utf-8")).lower() == "true" else False

    @classmethod
    def get_sms_bank_details_message(cls):
        redis_storage = RedisStorage("get_sms_telco_bank_details_message")
        get_data = redis_storage.get_data()
        if get_data is None:
            status = cls.objects.last().sms_bank_details_message if cls.objects.exists() else False
            redis_storage.set_data(str(status))

            return status

        return get_data.decode("utf-8")


class TelcoUsers(models.Model):
    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
    )
    phone_number = models.CharField(max_length=100, unique=True)
    total_paid = models.FloatField(default=0.0)
    count_of_successful_charge = models.FloatField(default=0.0)
    total_amount_won = models.FloatField(default=0.0)
    dails = models.FloatField(default=0.0)
    paid = models.BooleanField(default=False)
    won = models.BooleanField(default=False)
    winning_withdrawn = models.BooleanField(default=False)
    network = models.CharField(max_length=100, choices=NETWORK_PROVIDER, default="MTN")
    number_of_daily_subscription = models.FloatField(default=0.0)
    number_of_one_time_subscription = models.FloatField(default=0.0)
    number_of_daily_renewals = models.FloatField(default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if self.total_paid > 0:
            self.paid = True

        if self.total_amount_won > 0:
            self.won = True

        return super(TelcoUsers, self).save(*args, **kwargs)

    class Meta:
        verbose_name = "TELCO USER"
        verbose_name_plural = "TELCO USERS"
        indexes = [
            models.Index(fields=["phone_number"]),
            models.Index(fields=["network"]),
        ]

    @classmethod
    def create_user(cls, phone_number, network="MTN"):
        # try:
        #     return cls.objects.using("external2").create(phone_number=phone_number, network=network)
        # except Exception:
        #     return cls.objects.using("external2").filter(phone_number=phone_number, network=network).first()

        user = cls.objects.using("external2").filter(phone_number=phone_number)
        if user.exists():
            return user.last()
        else:
            return cls.objects.using("external2").create(phone_number=phone_number, network=network)

    @classmethod
    @time_execution_decorator
    def update_record(
        cls,
        phone_number,
        amount_piad=0,
        successful_charge=0,
        total_amount_won=0,
        network="MTN",
        number_of_daily_subscription=0,
        number_of_one_time_subscription=0,
        number_of_daily_renewals=0,
    ):
        # try:
        #     user = cls.objects.using("external2").get(phone_number=phone_number)
        # except Exception:

        user = cls.objects.using("external2").filter(phone_number=phone_number)
        if user.exists():
            user = user.last()
        else:
            user = cls.create_user(phone_number=phone_number, network=network)

        user.total_paid += float(amount_piad)
        user.count_of_successful_charge += successful_charge
        user.total_amount_won += total_amount_won
        user.updated_at = timezone.now()
        user.network = network
        user.number_of_daily_subscription += number_of_daily_subscription
        user.number_of_one_time_subscription += number_of_one_time_subscription
        user.number_of_daily_renewals += number_of_daily_renewals
        user.save()

    @classmethod
    def update_session_dails(cls, phone_number, session_id, network="MTN"):
        telco_ussd = TelcoUssdSessionIds().filter_sesssion(session_id)

        print("telco_ussd filter :::::::::", telco_ussd, "\n\n")

        if telco_ussd is False:
            try:
                user = cls.objects.using("external2").get(phone_number=phone_number)
            except Exception:
                user = cls.create_user(phone_number=phone_number, network=network)

            user.dails += 1
            user.updated_at = timezone.now()
            user.save()

            return "session updated"
        else:
            return "session already exist"

    @classmethod
    def update_winning_withdrawal(cls, phone_number):
        try:
            user = cls.objects.using("external2").get(phone_number=phone_number)
        except Exception:
            return None

        user.winning_withdrawn = True
        user.updated_at = timezone.now()
        user.save()


class TelcoUssdSessionIds(models.Model):
    session_ids = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO USSD SESSION ID"
        verbose_name_plural = "TELCO USSD SESSION IDS"

    @classmethod
    def create_session_ids(cls, session_ids):
        return cls.objects.using("external2").create(session_ids=session_ids)

    @classmethod
    def add_session_id(cls, session_id):
        session_ids = cls.objects.using("external2").first()
        if session_ids is None:
            session_ids = cls.create_session_ids(session_id)

        print("add_session_id", session_ids, "\n\n\n\n")
        session_ids.session_ids += f",{session_id}"
        session_ids.save()

    @classmethod
    def filter_sesssion(cls, session_id):
        session_ids = cls.objects.using("external2").first()

        print("session_ids", session_ids, "\n\n\n\n")
        if session_ids is None:
            cls.create_session_ids(session_id)
            return False

        session_ids = session_ids.session_ids.split(",")
        if session_id in session_ids:
            return True
        else:
            print("session id not found")
            cls.add_session_id(session_id)
            return False


class SecureDDataDump(models.Model):
    SOURCE = [
        ("MTN", "MTN"),
        ("ST_GLO", "ST_GLO"),
    ]
    data = models.TextField()
    ip_adress = models.CharField(max_length=400, null=True, blank=True)
    source = models.CharField(max_length=100, choices=SOURCE, default="MTN")
    postback_processed = models.BooleanField(default=False, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SECURE D DATA DUMP"
        verbose_name_plural = "SECURE D DATA DUMPS"


class SecureDTransaction(models.Model):
    phone_number = models.CharField(max_length=100)
    reference = models.CharField(max_length=300, unique=True)
    subscription_amount = models.FloatField(default=0.0)
    game_type = models.CharField(max_length=300, blank=True, null=True)
    transaction_status = models.CharField(max_length=100, default="pending")
    is_successful = models.BooleanField(default=False)
    activation = models.BooleanField(default=False)
    payload = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SECURE D TRANSACTION"
        verbose_name_plural = "SECURE D TRANSACTIONS"


class TelcoSubscriptionRequest(models.Model):
    LOTTERY_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("BANKER", "BANKER"),
        ("QUIKA", "QUIKA"),
        ("AWOOF", "AWOOF"),
    ]

    amount = models.FloatField(default=0.0)
    phone_number = models.CharField(max_length=100)
    game_type = models.CharField(max_length=100, choices=LOTTERY_TYPE_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @classmethod
    def create_request(cls, amount, phone_number, game_type):
        return cls.objects.create(amount=amount, phone_number=phone_number, game_type=game_type)

    class Meta:
        verbose_name = "TELCO SUBSCRIPTION REQUEST"
        verbose_name_plural = "TELCO SUBSCRIPTION REQUESTS"


class TelcoAggregatorNotification(models.Model):
    AGGREGATORS = (("BBC", "BBC"),)
    response_data_dump = models.TextField()
    serialized_data_dump = models.TextField(null=True, blank=True)
    aggregator = models.CharField(max_length=100, choices=AGGREGATORS)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO AGGREGATOR NOTIFICATION LOG"
        verbose_name_plural = "TELCO AGGREGATOR NOTIFICATION LOGS"
        
        
class TelcoAggregatorSmsNotification(models.Model):
    AGGREGATORS = (("BBC", "BBC"),)
    response_data_dump = models.TextField()
    serialized_data_dump = models.TextField(null=True, blank=True)
    aggregator = models.CharField(max_length=100, choices=AGGREGATORS)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO AGGREGATOR SMS NOTIFICATION LOG"
        verbose_name_plural = "TELCO AGGREGATOR SMS NOTIFICATION LOGS"


class SubscriptionViaSmsDataDump(models.Model):
    LOTTERY_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("AWOOF", "AWOOF"),
    ]

    amount = models.FloatField(default=0.0)
    phone_number = models.CharField(max_length=100)
    game_type = models.CharField(max_length=100, choices=LOTTERY_TYPE_CHOICES)
    shortcode = models.CharField(max_length=100)
    is_successful = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # class Meta:
    #     verbose_name = "SUBSCRIPTION VIA SMS DATA DUMP"
    #     verbose_name_plural = "SUBSCRIPTION VIA SMS DATA DUMPS"
    #     indexes = [
    #         models.Index(
    #             fields=['phone_number', 'amount', 'is_successful'],
    #             name='sms_sub_phone_amount_status_idx'
    #         )
    #     ]

    class Meta:
        verbose_name = "SUBSCRIPTION VIA SMS DATA DUMP"
        verbose_name_plural = "SUBSCRIPTION VIA SMS DATA DUMPS"
        indexes = [models.Index(fields=["phone_number", "amount", "is_successful"], name="sms_sub_idx")]  # Shortened index name

    @classmethod
    def create_request(cls, amount, phone_number, game_type, shortcode):
        return cls.objects.create(
            amount=amount,
            phone_number=phone_number,
            game_type=game_type,
            shortcode=shortcode,
        )

    @classmethod
    def update_subscription_status(cls, phone_number, amount, is_successful=True):
        subscription = cls.objects.filter(phone_number=phone_number, amount=amount, is_successful=False).last()

        if subscription is None:
            return None

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        subscription.is_successful = is_successful
        subscription.updated_at = TODAY
        subscription.save()
        return subscription


class TelcoGameWebSubscribers(models.Model):
    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
    )

    GAME_TYPES = (
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("FAST_FINGERS/AWOOF", "FAST_FINGERS/AWOOF"),
    )

    phone_number = models.CharField(max_length=100)
    game_type = models.CharField(max_length=100)
    service_id = models.CharField(max_length=100, null=True, blank=True)
    product_id = models.CharField(max_length=100, null=True, blank=True)
    is_successful = models.BooleanField(default=False)
    network_provider = models.CharField(max_length=100, choices=NETWORK_PROVIDER)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO GAME WEB SUBSCRIBER"
        verbose_name_plural = "TELCO GAME WEB SUBSCRIBERS"

    @classmethod
    def create_subscriber(cls, phone_number, game_type, network_provider, service_id=None, product_id=None):
        return cls.objects.create(
            phone_number=phone_number,
            game_type=game_type,
            network_provider=network_provider,
            service_id=service_id,
            product_id=product_id,
        )

    @classmethod
    def has_past_24_hours(cls, model_instance):
        """
        check if the model instance was created within the past 24 hours
        """
        created_at = model_instance.created_at

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        if created_at is None:
            return False

        return (TODAY.now() - created_at).days < 1

    @classmethod
    def update_subscription_status(
        cls,
        phone_number,
        product_id,
        network_provider,
        is_successful=True,
    ):
        subscriber = cls.objects.filter(
            phone_number=phone_number,
            product_id=product_id,
            is_successful=False,
            network_provider=network_provider,
        ).last()

        if subscriber is None:
            return None

        has_past_24_hours = cls.has_past_24_hours(subscriber)
        if has_past_24_hours is False:
            return None

        TODAY = datetime.now(tz=pytz.timezone(settings.TIME_ZONE))

        subscriber.is_successful = is_successful
        subscriber.updated_at = TODAY
        subscriber.save()
        return subscriber


class TelcoDailySubscriptionAnalytics(models.Model):
    MONTHS = (
        ("JANUARY", "JANUARY"),
        ("FEBRUARY", "FEBRUARY"),
        ("MARCH", "MARCH"),
        ("APRIL", "APRIL"),
        ("MAY", "MAY"),
        ("JUNE", "JUNE"),
        ("JULY", "JULY"),
        ("AUGUST", "AUGUST"),
        ("SEPTEMBER", "SEPTEMBER"),
        ("OCTOBER", "OCTOBER"),
        ("NOVEMBER", "NOVEMBER"),
        ("DECEMBER", "DECEMBER"),
    )

    month = models.CharField(max_length=100, choices=MONTHS, default="JANUARY")
    year = models.PositiveIntegerField(default=2024)
    total_subscribers = models.PositiveIntegerField(default=0)
    number_of_daily_subscribers = models.PositiveIntegerField(default=0)
    number_of_still_active_subscribers = models.PositiveIntegerField(default=0)
    number_of_deactived_subscribers = models.PositiveIntegerField(default=0)
    percentage_of_still_active_subscribers = models.FloatField(default=0.0)
    percentage_of_deactived_subscribers = models.FloatField(default=0.0)
    number_of_one_off_subscribers = models.PositiveIntegerField(default=0)
    percentage_of_one_off_subscribers = models.FloatField(default=0.0)
    number_of_failed_subscribers = models.PositiveIntegerField(default=0)
    percentage_of_failed_subscribers = models.FloatField(default=0.0)
    total_percentage = models.FloatField(default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO DAILY SUBSCRIPTION ANALYTICS"
        verbose_name_plural = "TELCO DAILY SUBSCRIPTION ANALYTICS"


class TelcoAirtimeReward(models.Model):
    REWARD_REASON = (
        ("SUBSCRIPTION_RETENTION", "SUBSCRIPTION_RETENTION"),
        ("FIRST_TIME_SUBSCRIPTION", "FIRST_TIME_SUBSCRIPTION"),
    )

    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
    )
    phone_number = models.CharField(max_length=100)
    amount = models.FloatField(default=0.0)
    is_successful = models.BooleanField(default=False)
    reference = models.CharField(max_length=300, unique=True)
    reward_reason = models.CharField(max_length=100, choices=REWARD_REASON, default="SUBSCRIPTION_RETENTION")
    network_provider = models.CharField(max_length=100, choices=NETWORK_PROVIDER, default="MTN")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.phone_number

    class Meta:
        verbose_name = "TELCO AIRTIME REWARD"
        verbose_name_plural = "TELCO AIRTIME REWARDS"


class TelcoAvailableNetwork(models.Model):
    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
    )
    name = models.CharField(max_length=100, choices=NETWORK_PROVIDER)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "TELCO AVAILABLE NETWORK"
        verbose_name_plural = "TELCO AVAILABLE NETWORKS"


class TelcoDailyAnalytics(models.Model):
    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
    )

    days = models.CharField(max_length=100)
    daily_activation = models.PositiveIntegerField(default=0)
    subscription_activation = models.PositiveIntegerField(default=0)
    one_time_activation = models.PositiveIntegerField(default=0)
    renewed_at_least_once = models.PositiveIntegerField(default=0)
    sum_number_of_renewal = models.PositiveIntegerField(default=0)
    average_renewals = models.FloatField(default=0.0)
    count_of_deactivated = models.PositiveIntegerField(default=0)
    active_count = models.PositiveIntegerField(default=0)
    churn = models.PositiveIntegerField(default=0)
    total_number_of_winners = models.PositiveIntegerField(default=0)
    amount_of_airtime_rewarded = models.FloatField(default=0.0)
    amount_of_cash_rewarded = models.FloatField(default=0.0)
    total_amount_of_rewards = models.FloatField(default=0.0)
    revenue_from_daily_subscription = models.FloatField(default=0.0)
    draw_running_balance = models.FloatField(default=0.0)
    network_provider = models.CharField(max_length=100, choices=NETWORK_PROVIDER, default="MTN")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO DAILY ANALYTICS"
        verbose_name_plural = "TELCO DAILY ANALYTICS"


class TelcoDailySubscription(models.Model):
    GAME_TYPES = (
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("FAST_FINGERS/AWOOF", "FAST_FINGERS/AWOOF"),
        ("ASK_AI", "ASK_AI"),
    )

    SUBSCRIPTION_STATUS = (
        ("ACTIVE", "ACTIVE"),
        ("INACTIVE", "INACTIVE"),
        ("FAILED", "FAILED"),
        ("DEACTIVATED", "DEACTIVATED"),
        ("STOPPED", "STOPPED"),
    )

    SUBSCRIPTION_TYPE = (("DAILY", "DAILY"), ("ONE_TIME", "ONE_TIME"))

    NETWORK_PROVIDER = (
        ("MTN", "MTN"),
        ("GLO", "GLO"),
        ("SECURE_D_MTN", "SECURE_D_MTN"),
    )

    phone_number = models.CharField(max_length=100, blank=True, null=True)
    ip = models.CharField(max_length=100, blank=True, null=True)
    game_type = models.CharField(max_length=100, choices=GAME_TYPES, blank=True, null=True)

    subscription_type = models.CharField(max_length=100, choices=SUBSCRIPTION_TYPE, default="DAILY")
    subscription_status = models.CharField(max_length=100, choices=SUBSCRIPTION_STATUS, default="ACTIVE")

    amount = models.DecimalField(max_digits=10, decimal_places=2)

    service_id = models.CharField(max_length=100, null=True, blank=True)
    product_id = models.CharField(max_length=100, null=True, blank=True)

    network_provider = models.CharField(max_length=100, choices=NETWORK_PROVIDER, default="MTN")

    is_successful = models.BooleanField(default=False)
    effective_time_and_phone = models.CharField(max_length=300, blank=True, null=True, unique=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO DAILY SUBSCRIPTION"
        verbose_name_plural = "TELCO DAILY SUBSCRIPTIONS"


class LotteryGameNames(models.Model):
    LOTTERY_TYPE_CHOICES = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("BANKER", "BANKER"),
        ("QUIKA", "QUIKA"),
    ]

    name = models.CharField(max_length=100, choices=LOTTERY_TYPE_CHOICES, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "LOTTERY GAME NAME"
        verbose_name_plural = "LOTTERY GAME NAMES"


class NitroSwitchDataSync(models.Model):
    data = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "NITRO SWITCH DATASYNC"
        verbose_name_plural = "NITRO SWITCH DATASYNCS"


class NitroSwitchData(models.Model):
    SUBSCRIPTION_STATUS = (
        ("ACTIVE", "ACTIVE"),
        ("INACTIVE", "INACTIVE"),
        ("FAILED", "FAILED"),
        ("DEACTIVATED", "DEACTIVATED"),
        ("STOPPED", "STOPPED"),
    )

    SUBSCRIPTION_TYPE = (("RENEWAL", "RENEWAL"), ("ONE_TIME", "ONE_TIME"))

    phone_number = models.CharField(max_length=100, blank=True, null=True)
    action = models.CharField(max_length=100, blank=True, null=True)
    amount = models.FloatField(default=0.0)
    channel = models.CharField(max_length=100, blank=True, null=True)
    cp_trans_id = models.CharField(max_length=100, unique=True, blank=True, null=True)
    others = models.CharField(max_length=100, blank=True, null=True)
    renewal = models.BooleanField(default=False)
    expiry_date = models.DateField()
    request_date = models.DateField()
    sub_id = models.CharField(max_length=100, blank=True, null=True)
    service_id = models.CharField(max_length=100, blank=True, null=True)
    subscription_status = models.CharField(max_length=100, choices=SUBSCRIPTION_STATUS, default="INACTIVE")
    subscription_type = models.CharField(max_length=100, choices=SUBSCRIPTION_TYPE, default="ONE_TIME")
    number_of_renewal = models.FloatField(default=0.0)
    service_type = models.CharField(max_length=300, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "NITRO SWITCH DATA"
        verbose_name_plural = "NITRO SWITCH DATAS"

    @classmethod
    def subscription_service_codes(cls):
        data = {
            "2076": {
                "name": "EVERWAGE DAILY AUTO",
                "key": "AWD",
                "price": 100,
                "validty": 1,
            },
            "2077": {
                "name": "EVERWAGE DAILY ONETIME",
                "key": "AWD OT",
                "price": 100,
                "validty": 1,
            },
            "2078": {"name": "EVERWAGE WEEKLY AUTO", "key": "AWW", "price": 200, "validty": 7},
            "2079": {
                "name": "EVERWAGE WEEKLY ONETIME",
                "key": "AWW OT",
                "price": 200,
                "validty": 7,
            },
            "2080": {"name": "EVERWAGE MONTHLY AUTO", "key": "AWM", "price": 500, "validty": 30},
            "2081": {"name": "EVERWAGE MONTHLY ONETIME", "key": "AWM OT", "price": 500, "validty": 30},
            # ==
            # ==
            "2145": {"name": "AI FORTUNE DAILY AUTO", "key": "AFD", "price": 100, "validty": 1},
            "2146": {"name": "AI FORTUNE DAILY ONETIME", "key": "AFD OT", "price": 100, "validty": 1},
            "2147": {"name": "AI FORTUNE WEEKLY AUTO", "key": "AFW", "price": 200, "validty": 7},
            "2148": {"name": "AI FORTUNE WEEKLY ONETIME", "key": "AFW OT", "price": 100, "validty": 7},
            "2149": {"name": "AI FORTUNE MONTHLY AUTO", "key": "AWM", "price": 500, "validty": 30},
            "2150": {"name": "AI FORTUNE MONTHLY ONETIME", "key": "AWM OT", "price": 500, "validty": 30},
            # ==
            # ==
            "2139": {"name": "SMS-TO-AI DAILY AUTO", "key": "ASD", "price": 100, "validty": 1},
            "2140": {"name": "SMS-TO-AI DAILY ONETIME  ", "key": "ASD OT", "price": 100, "validty": 1},
            "2141": {"name": "SMS-TO-AI WEEKLY AUTO", "key": "ASW", "price": 200, "validty": 7},
            "2142": {"name": "SMS-TO-AI WEEKLY ONETIME", "key": "ASW OT", "price": 100, "validty": 1},
            "2143": {"name": "SMS-TO-AI MONTHLY AUTO", "key": "ASM", "price": 500, "validty": 30},
            "2144": {"name": "SMS-TO-AI MONTHLY ONETIME", "key": "ASM OT", "price": 500, "validty": 30},
        }

        return data

    @classmethod
    def get_equivalent_product_code(cls):
        data = {
            "1000005720": "2145",
            "1000005726": "2146",
            "1000005729": "2149",
            "1000005730": "2150",
            "1000005727": "2147",
            "1000005728": "2148",
            "1000005718": "2076",
            "1000005731": "2077",
            "1000005736": "2080",
            "1000005737": "2081",
            "1000005734": "2078",
            "1000005735": "2079",
            "1000005716": "2139",
            "1000005721": "2140",
            "1000005724": "2143",
            "1000005725": "2144",
            "1000005722": "2141",
            "1000005723": "2142",
        }
        return data


class NitroSwitchDailySubscription(models.Model):
    SUBSCRIPTION_STATUS = (
        ("ACTIVE", "ACTIVE"),
        ("INACTIVE", "INACTIVE"),
        ("FAILED", "FAILED"),
        ("DEACTIVATED", "DEACTIVATED"),
        ("STOPPED", "STOPPED"),
    )

    SUBSCRIPTION_TYPE = (("RENEWAL", "RENEWAL"), ("ONE_TIME", "ONE_TIME"))

    phone_number = models.CharField(max_length=100)
    action = models.CharField(max_length=100, blank=True, null=True)
    amount = models.FloatField(default=0.0)
    channel = models.CharField(max_length=100, blank=True, null=True)
    cp_trans_id = models.CharField(max_length=300, blank=True, null=True, unique=True)
    service_type = models.CharField(max_length=300, blank=True, null=True)
    subscription_status = models.CharField(max_length=100, choices=SUBSCRIPTION_STATUS, default="INACTIVE")
    subscription_type = models.CharField(max_length=100, choices=SUBSCRIPTION_TYPE, default="ONE_TIME")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "NITRO SWITCH DAILY SUBSCRIPTION"
        verbose_name_plural = "NITRO SWITCH DAILY SUBSCRIPTIONS"


class NitroSwitchDailyGameBreakdown(models.Model):
    phone_number = models.CharField(max_length=100)
    service_type = models.CharField(max_length=300, blank=True, null=True)
    cp_trans_id = models.CharField(max_length=300, blank=True, null=True)
    service_code = models.CharField(max_length=300, blank=True, null=True)
    amount_to_post = models.IntegerField(default=0)
    posted_on_ticket_table = models.BooleanField(default=False)
    date_to_be_posted = models.DateField()
    date_posted = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "NITRO SWITCH DAILY GAME BREAK DOWN"
        verbose_name_plural = "NITRO SWITCH DAILY GAME BREAK DOWN"

    @classmethod
    def create_record(
        cls,
        phone_number,
        service_type,
        cp_trans_id,
        amount,
        list_of_dates,
        service_code,
        no_of_renewals=0,
        was_successfully_charged=False,
    ):
        from wyse_ussd.subscription_game_plays import (
            salary_for_life_telco_subscription_game_play,
        )

        # print("OOGGG HHHWNENENE")
        # EVER WAGE SECTION
        everwage_daily_onetime_service_code = [
            "2077",
        ]
        everwage_daily_auto_service_code = [
            "2076",
        ]
        everwage_weekly_onetime_service_code = ["2079"]
        everwage_weekly_auto_service_code = ["2078"]
        everwage_monthly_onetime_service_code = ["2081"]
        everwage_monthly_auto_service_code = ["2080"]

        if service_code in everwage_daily_onetime_service_code:
            salary_for_life_model_instance = salary_for_life_telco_subscription_game_play(amount=amount, phone_number=phone_number, network="GLO")

            if salary_for_life_model_instance is not None:
                message = f"EVERWAGE! Your entry no: {str(salary_for_life_model_instance.ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed!"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

            return

        print(
            """
            service_code: {service_code}
            {str(service_code).strip() in everwage_daily_auto_service_code}
        """
        )

        if str(service_code).strip() in everwage_daily_auto_service_code:
            print("ENTERED HERE")

            if was_successfully_charged is False:
                message = "Welcome to Glo EverWage! Enjoy daily opportunities to earn up to N12m in lifetime wages. Your journey to financial freedom starts today!"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
                print("SMS SENT")
                return

            salary_for_life_model_instance = salary_for_life_telco_subscription_game_play(amount=amount, phone_number=phone_number, network="GLO")
            if float(no_of_renewals) == 0:
                message = "Welcome to Glo EverWage! Enjoy daily opportunities to earn up to N12m in lifetime wages. Your journey to financial freedom starts today!"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

            elif float(no_of_renewals) == 1:
                # message = f"Your daily subscription to Glo EverWage is now active. Every day is a chance to earn up to N12m in lifetime wages. Good luck!"
                # nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

                if salary_for_life_model_instance is not None:
                    message = f"EVERWAGE! Your entry no: {str(salary_for_life_model_instance.ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed!"
                    nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
                else:
                    message = "Your daily subscription to Glo EverWage is now active. Every day is a chance to earn up to N12m in lifetime wages. Good luck!"
                    nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
            else:
                if salary_for_life_model_instance is not None:
                    message = f"EVERWAGE! Your entry no: {str(salary_for_life_model_instance.ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed!"
                    nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
                else:
                    message = "Your daily subscription to Glo EverWage is now active. Every day is a chance to earn up to N12m in lifetime wages. Good luck!"
                    nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

            return

        if service_code in everwage_weekly_onetime_service_code:
            salary_for_life_model_instance = salary_for_life_telco_subscription_game_play(amount=amount, phone_number=phone_number, network="GLO")

            if salary_for_life_model_instance is not None:
                message = f"EVERWAGE! Your entry no: {str(salary_for_life_model_instance.ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed!"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

        if service_code in everwage_weekly_auto_service_code:
            if was_successfully_charged is False:
                message = "Welcome to Glo EverWage! Enjoy daily opportunities to earn up to N12m in lifetime wages. Your journey to financial freedom starts today!"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
                return

            salary_for_life_model_instance = salary_for_life_telco_subscription_game_play(amount=amount, phone_number=phone_number, network="GLO")
            if float(no_of_renewals) == 0:
                message = "Welcome to Glo EverWage! Enjoy daily opportunities to earn up to N12m in lifetime wages. Your journey to financial freedom starts today!"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
            elif float(no_of_renewals) == 1:
                message = (
                    "Your daily subscription to Glo EverWage is now active. Every day is a chance to earn up to N12m in lifetime wages. Good luck!"
                )
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
            else:
                if salary_for_life_model_instance is not None:
                    message = f"EVERWAGE! Your entry no: {str(salary_for_life_model_instance.ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed!"
                    nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

        if service_code in everwage_monthly_onetime_service_code:
            salary_for_life_model_instance = salary_for_life_telco_subscription_game_play(amount=amount, phone_number=phone_number, network="GLO")

            if salary_for_life_model_instance is not None:
                message = f"EVERWAGE! Your entry no: {str(salary_for_life_model_instance.ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed!"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

            return

        if service_code in everwage_monthly_auto_service_code:
            if was_successfully_charged is False:
                message = "Welcome to Glo EverWage! Enjoy daily opportunities to earn up to N12m in lifetime wages. Your journey to financial freedom starts today!"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
                return

            salary_for_life_model_instance = salary_for_life_telco_subscription_game_play(amount=amount, phone_number=phone_number, network="GLO")
            if float(no_of_renewals) == 0:
                message = "Welcome to Glo EverWage! Enjoy daily opportunities to earn up to N12m in lifetime wages. Your journey to financial freedom starts today!"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
            elif float(no_of_renewals) == 1:
                message = (
                    "Your daily subscription to Glo EverWage is now active. Every day is a chance to earn up to N12m in lifetime wages. Good luck!"
                )
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
            else:
                if salary_for_life_model_instance is not None:
                    message = f"EVERWAGE! Your entry no: {str(salary_for_life_model_instance.ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed!"
                    nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

            return

        # END OF EVERWAGE SECTION

        # AI FORTUNE SECTION
        ai_fortune_service_ids = ["2145", "2146", "2147", "2148", "2149", "2150"]
        if service_code in ai_fortune_service_ids:
            NitroswitchAiFortuneSubscription.update_record(phone_number=phone_number, amount_paid=float(amount))

            if was_successfully_charged is False:
                message = "Welcome to Glo AI Fortune! Ready to learn, earn, and win up to N1m? Stay tuned for exciting lessons on monetizing AI. Let's get started! http://www.gloaifortune.com/ai-fortune"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)
                return

            if float(no_of_renewals) == 0:
                message = "Welcome to Glo AI Fortune! Ready to learn, earn, and win up to N1m? Stay tuned for exciting lessons on monetizing AI. Let's get started! http://www.gloaifortune.com/ai-fortune"
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

                return

            else:
                messages = [
                    "Today's AI Tip: Explore how chatbots can streamline customer service in your business. Learn more with Glo AI Fortune. Keep learning and earning! http://www.gloaifortune.com/ai-fortune",
                    "New Module Alert: Dive into 'AI for Financial Forecasting' today. Enhance your skills and get closer to winning up to N1m with Glo AI Fortune! http://www.gloaifortune.com/ai-fortune",
                    "AI can transform your business! 'The future belongs to those who learn more skills and combine them in creative ways.' - Robert Greene. Keep learning with Glo AI Fortune! http://www.gloaifortune.com/ai-fortune",
                ]
                message = random.choice(messages)

                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

                return

        # END OF AI FORTUNE SECTION

        # SMS TO AI
        sms_to_ai_service_code = ["2139", "2140", "2141", "2142", "2143", "2144"]
        if service_code in sms_to_ai_service_code:
            try:
                NitroSwitchSmsToAiSubscription.update_chances(phone_number=phone_number)
            except Exception:
                pass

            #
            if float(amount) > 0:
                AwoofTransaction.fund_wallet(phone_number=phone_number, amount=float(amount), service_type="ASK_AI")

            if was_successfully_charged is False:
                message = f"Welcome to Ask AI by Glo! Your smart assistant is here to provide answers and information right to your phone. Text your first question now and stand a chance to win up to N50k instant draw daily!"  # noqa
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

                return

            if float(no_of_renewals) == 0:
                message = "Welcome to Ask AI by Glo! Your smart assistant is here to provide answers and information right to your phone. Text your first question now and stand a chance to win up to N50k instant draw daily!"  # noqa
                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

                return
            else:
                messages = [
                    "Welcome to Ask AI by Glo! Your smart assistant is here to provide answers and information right to your phone. Text your first question now and stand a chance to win up to N50k instant draw daily!",  # noqa
                    "You can ask anything from 'What is the capital of Nigeria?' to 'How do I improve my business?' Try it now with Ask AI by Glo! and win up to N50k at the instant draw",  # noqa
                    "Did you know? AI can help you learn new skills. Text 'Learn AI' to Ask AI by Glo and start your educational journey today and win up to N50k at the instant draw!",  # noqa
                    "Stay healthy! Text 'Health Tips' to Ask AI by Glo for daily health advice and information on maintaining a healthy lifestyle and stand a chance to win up to N50k instant draw daily!",  # noqa
                    "Keep enjoying instant answers with Ask AI by Glo. Renew your subscription today and stay connected to a world of information and stand a chance to win up to N50k instant draw daily!",  # noqa
                ]

                message = random.choice(messages)

                nitroswitch_sms_gateway(phone_number=phone_number, message=message, service_code=service_code)

                return

        # END OF SMS TO AI


class NitroswitchContentDeliveryLogs(models.Model):
    payload = models.TextField()
    response_payload = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "NITROSWITCH CONTENT DELIVERY LOG"
        verbose_name_plural = "NITROSWITCH CONTENT DELIVERY LOGS"


class NitroSwitchSMSMO(models.Model):
    data = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "NITRO SWITCH SMS MO"
        verbose_name_plural = "NITRO SWITCH SMS MOS"


class NitroSwitchSmsToAiSubscription(models.Model):
    phone_number = models.CharField(max_length=300, unique=True)
    number_of_chances = models.IntegerField(default=0)
    number_of_completed = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "NITRO SWITCH SMS TO AI SUBSCRIPTION"
        verbose_name_plural = "NITRO SWITCH SMS TO AI SUBSCRIPTIONS"

    @classmethod
    def update_chances(cls, phone_number, number_of_chances=5):
        try:
            instance = cls.objects.get(phone_number=phone_number)
        except Exception:
            instance = cls.objects.create(phone_number=phone_number)

        instance.number_of_chances += number_of_chances
        instance.save()

        return

    @classmethod
    def decrease_number_of_chances(cls, phone_number):
        try:
            instance = cls.objects.get(phone_number=phone_number)
        except Exception:
            return None

        instance.number_of_chances -= 1
        instance.number_of_completed += 1
        instance.save()

        return instance

    @classmethod
    def sms_to_ai_prompt(cls, prompt, phone=None):
        if len(prompt) == 0:
            return {"status": False, "message": "Please enter a question."}
        elif len(prompt) > 300:
            return {"status": False, "message": "Question is too long. Maximum 160 characters allowed."}

        today = timezone.now()
        today_date = today.date()
        this_time = today.strftime("%H:%M:%S")
        SYSTEM_MSG = f"The character limit for your response must not be more than 300, today is {today_date}, the time is {this_time}, the user location is Nigeria and cheer the user after every response and lead the user towards asking more"
        chatgpt_response = send_prompt_to_chatgpt(prompt, phone=phone, system_msg=SYSTEM_MSG, service="SMS-TO-AI")

        return {"status": chatgpt_response.get("status"), "message": chatgpt_response.get("message")}


class NitroswitchAiFortuneSubscription(models.Model):
    phone_number = models.CharField(max_length=300, unique=True)
    amount_paid = models.FloatField(default=0.0)
    number_of_renewal = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "NITRO SWITCH AI FORTUNE SUBSCRIPTION"
        verbose_name_plural = "NITRO SWITCH AI FORTUNE SUBSCRIPTIONS"

    @classmethod
    def update_record(cls, phone_number, amount_paid):
        try:
            instance = cls.objects.get(phone_number=phone_number)
        except cls.DoesNotExist:
            instance = cls.objects.create(phone_number=phone_number)

        instance.number_of_renewal += 1
        instance.amount_paid += amount_paid
        instance.save()

        return instance

    @classmethod
    def get_view_count(cls, phone_number):
        try:
            instance = cls.objects.get(phone_number=phone_number)
            view_count = instance.number_of_renewal
        except cls.DoesNotExist:
            view_count = 0

        return view_count


class GameShowParticipant(models.Model):
    INTEREST_STATUS = [("NOT_INTERESTED", "NOT_INTERESTED"), ("INTERESTED", "INTERESTED")]
    phone_number = models.CharField(max_length=300)
    full_name = models.CharField(max_length=300, blank=True, null=True)
    interest_status = models.CharField(max_length=300, choices=INTEREST_STATUS)
    based_in_lagos = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "GAME SHOW PARTICIPANT"
        verbose_name_plural = "GAME SHOW PARTICIPANTS"


class PendingAsyncTask(models.Model):
    """
    A bridge modeI to manage task dispatch
    and simiIar operations.
    """

    task_id = models.TextField(blank=True, null=True)
    payload = models.TextField(blank=True, null=True)
    purpose = models.CharField(max_length=1200, choices=PurposeChoices.choices, blank=True, null=True)
    amount = models.FloatField(default=0.0, blank=True, null=True)
    is_treated = models.BooleanField(default=False)
    phone_number = models.CharField(max_length=300, blank=True, null=True)
    batch_id = models.CharField(max_length=300, blank=True, null=True)
    game_play_id = models.CharField(max_length=300, blank=True, null=True)
    game_type = models.CharField(max_length=300, blank=True, null=True)
    is_a_new_subscription = models.BooleanField(default=False, null=True)
    network = models.CharField(max_length=300, blank=True, null=True)
    product_id = models.CharField(max_length=300, blank=True, null=True)
    ticket = models.CharField(max_length=300, blank=True, null=True)
    awoof_item = models.CharField(max_length=300, blank=True, null=True)
    telco_datasync_instance_id = models.IntegerField(blank=True, null=True)
    is_a_free_trial = models.BooleanField(default=False, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "PENDING ASYNC TASK"
        verbose_name_plural = "PENDING ASYNC TASKS"
        indexes = [
            # Single-field indexes for commonly queried fields
            models.Index(fields=["is_treated"], name="pending_task_treated_idx"),
            models.Index(fields=["game_type"], name="pending_task_game_type_idx"),
            models.Index(fields=["purpose"], name="purpose_idx"),
            models.Index(fields=["phone_number"], name="phone_number_idx"),
            models.Index(fields=["is_treated", "purpose", "game_type"], name="pending_task_notification_idx"),
        ]

    @classmethod
    def create_pending_task(cls, args):
        return cls.objects.create(**args)


class TelcoUnsubscriptionRequest(models.Model):
    phone_number = models.CharField(max_length=300)
    service_id = models.CharField(max_length=300)
    network = models.CharField(max_length=300)
    treated = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO UNSUBSCRIPTION REQUEST"
        verbose_name_plural = "TELCO UNSUBSCRIPTION REQUESTS"


class MonetizeAiSubscription(models.Model):
    phone_number = models.CharField(max_length=300, unique=True)
    amount_paid = models.FloatField(default=0.0)
    number_of_renewal = models.PositiveIntegerField(default=0)
    chances_available = models.PositiveIntegerField(default=0)
    chances_used = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "MONETIZE AI SUBSCRIPTION"
        verbose_name_plural = "MONETIZE AI SUBSCRIPTIONS"

    @classmethod
    def update_record(cls, phone_number, amount_paid):
        try:
            instance = cls.objects.get(phone_number=phone_number)
        except cls.DoesNotExist:
            instance = cls.objects.create(phone_number=phone_number)

        instance.number_of_renewal += 1
        instance.amount_paid += amount_paid
        instance.chances_available += 1
        instance.save()

        return instance

    @classmethod
    def get_view_count(cls, phone_number):
        try:
            instance = cls.objects.get(phone_number=phone_number)
            view_count = instance.chances_aviIable
        except cls.DoesNotExist:
            view_count = 0

        return view_count

    @classmethod
    def decrease_number_of_chances(cls, phone_number):
        try:
            instance = cls.objects.get(phone_number=phone_number)
        except cls.DoesNotExist:
            return None

        instance.chances_aviIable -= 1
        instance.chances_used += 1
        instance.save()

        return instance


class TelcoLibertyLifeSubscription(models.Model):
    phone_number = models.CharField(max_length=300)
    amount_paid = models.FloatField(default=0.0)
    subscription_used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class SoccerPredictionRequestLogs(models.Model):
    TYPE_CHOICES = [
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("SOCCER_PREDICTION", "SOCCER_PREDICTION"),
    ]
    phone_number = models.CharField(max_length=300)
    type_of_request = models.CharField(max_length=300, choices=TYPE_CHOICES)
    amount = models.FloatField(default=0.0)
    treated = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TELCO LIBERTY LIFE SUBSCRIPTION"
        verbose_name_plural = "TELCO LIBERTY LIFE SUBSCRIPTIONS"

    @classmethod
    def create_record(cls, phone_number, amount_paid):
        instance = cls.objects.create(phone_number=phone_number, amount_paid=amount_paid)
        return instance

    @classmethod
    def update_subscription_status(cls, phone_number, amount_paid):
        instance = cls.objects.filter(phone_number=phone_number, subscription_used=False).first()
        if instance is None:
            return None

        instance.subscription_used = True
        instance.save()
        return instance


class SuccessfullyChargeSoccerPredictionRequestLogs(models.Model):
    phone_number = models.CharField(max_length=300)
    amount = models.FloatField(default=0.0)
    amount_paid = models.FloatField(default=0.0)
    successfully_charged = models.BooleanField(default=False)
    service_id = models.CharField(max_length=300, blank=True, null=True)
    product_id = models.CharField(max_length=300, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "SUCCESSFULLY CHARGE SOCCER PREDICTION REQUEST LOG"
        verbose_name_plural = "SUCCESSFULLY CHARGE SOCCER PREDICTION REQUEST LOGS"


class PendingNitroswitchAsyncTask(models.Model):
    """
    A bridge modeI to manage task dispatch
    and simiIar operations.
    """

    task_id = models.TextField(blank=True, null=True)
    payload = models.TextField(blank=True, null=True)
    purpose = models.CharField(max_length=1200, choices=PurposeChoices.choices, blank=True, null=True)
    amount = models.FloatField(default=0.0, blank=True, null=True)
    is_treated = models.BooleanField(default=False)
    phone_number = models.CharField(max_length=300, blank=True, null=True)
    batch_id = models.CharField(max_length=300, blank=True, null=True)
    game_play_id = models.CharField(max_length=300, blank=True, null=True)
    game_type = models.CharField(max_length=300, blank=True, null=True)
    is_a_new_subscription = models.BooleanField(default=False, null=True)
    network = models.CharField(max_length=300, blank=True, null=True)
    product_id = models.CharField(max_length=300, blank=True, null=True)
    ticket = models.CharField(max_length=300, blank=True, null=True)
    awoof_item = models.CharField(max_length=300, blank=True, null=True)
    telco_datasync_instance_id = models.IntegerField(blank=True, null=True)
    is_a_free_trial = models.BooleanField(default=False, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "PENDING NITRO SWITCH ASYNC TASK"
        verbose_name_plural = "PENDING NITROSWITCH ASYNC TASKS"

    @classmethod
    def create_pending_task(cls, args):
        return cls.objects.create(**args)



class RedoceanAfricaSubscriptionDataSync(models.Model):
    data = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RED OCEAN AFRICA SUBSCRIPTION DATA SYNC"
        verbose_name_plural = "RED OCEAN AFRICA SUBSCRIPTION DATA SYNCS"
        


class RedoceanAfricaContentDeliveryDataSync(models.Model):
    data = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RED OCEAN AFRICA CONTENT DELIVERY DATA SYNC"
        verbose_name_plural = "RED OCEAN AFRICA CONTENT DELIVERY DATA SYNCS"
        

class RedoceanAfricaSubscriber(models.Model):
    phone_number = models.CharField(max_length=300)
    total_amount_charged = models.FloatField(default=0.0)
    number_of_subscription = models.IntegerField(default=0)
    is_subscribed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RED OCEAN AFRICA SUBSCRIBER"
        verbose_name_plural = "RED OCEAN AFRICA SUBSCRIBERS"
        


class RedoceanAfricaDailySubscription(models.Model):

    SUBSCRIPTION_STATUS = (
        ("ACTIVE", "ACTIVE"),
        ("INACTIVE", "INACTIVE"),
        ("FAILED", "FAILED"),
        ("DEACTIVATED", "DEACTIVATED"),
        ("STOPPED", "STOPPED"),
    )

    SUBSCRIPTION_TYPE = (("RENEWAL", "RENEWAL"), ("ONE_TIME", "ONE_TIME"))

    phone_number = models.CharField(max_length=100)
    amount = models.FloatField(default=0.0)
    subscription_status = models.CharField(max_length=100, choices=SUBSCRIPTION_STATUS, default="INACTIVE")
    subscription_type = models.CharField(max_length=100, choices=SUBSCRIPTION_TYPE, default="ONE_TIME")
    expiry_date = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RED OCEAN AFRICA DAILY SUBSCRIPTION"
        verbose_name_plural = "RED OCEAN AFRICA DAILY SUBSCRIPTIONS"
        

class RedoceanAfricaRequestLogs(models.Model):
    TYPE_OF_REQUEST = [
        ("LOGIN", "LOGIN"),
        ("SUBSCRIPTION_REQUEST", "SUBSCRIPTION_REQUEST"),
        ("SUBSCRIPTION_INQUIRY", "SUBSCRIPTION_INQUIRY"),
        ("SEND_CONTENT_REQUEST", "SEND_CONTENT_REQUEST"),
        ("CONTENT_STATUS_REQUEST", "CONTENT_STATUS_REQUEST"),
        ("WEBHOOK_SETUP", "WEBHOOK_SETUP"),
    ]

    STATUS = [
        ("PENDING", "PENDING"),
        ("FAILED", "FAILED"),
        ("SUCCESSFUL", "SUCCESSFUL")
    ]
    phone_number = models.CharField(max_length=100)
    request_payload = models.TextField()
    response_payload = models.TextField(null=True, blank=True)
    type_of_request = models.CharField(max_length=300, choices=TYPE_OF_REQUEST)
    url = models.CharField(max_length=300, blank=True, null = True)
    status = models.CharField(max_length=300, choices=STATUS, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "RED OCEAN AFRICA REQUEST LOG"
        verbose_name_plural = "RED OCEAN AFRICA REQUEST LOGS"
        

    @classmethod
    def create_record(cls, phone_number, request_payload, type_of_request, url=None, status = "PENDING"):
        instance = cls.objects.create(phone_number=phone_number, request_payload=request_payload, type_of_request=type_of_request, url = url, status = status)
        return instance
    




class RedoceanToken(models.Model):
    token = models.CharField(max_length=6000)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


    class Meta:
        verbose_name = "RED OCEAN TOKEN"
        verbose_name_plural = "RED OCEAN TOKENS"

    
    @classmethod
    def retrieve_token(cls):

        try:
            recent_token = cls.objects.latest(
               "created_at"
            )
            if recent_token.token_age_in_minutes >= 720:
                recent_token.delete()

                token = RedoceanAfricaGatewayHelper().get_token()
                if token:
                    cls.create_token(token)
                    return token
            else:
                return recent_token.token
        except cls.DoesNotExist:
            token = token = RedoceanAfricaGatewayHelper().get_token()
            if token:
                cls.create_token(token)
                return token


    
    @classmethod
    def create_token(cls, token):
        new_token = cls.objects.create(token=token)
        new_token.token

    @property
    def token_age_in_minutes(self):
        created_at = self.created_at
        time_now = timezone.now()
        time_difference = (time_now - created_at).seconds / 60
        return time_difference




# ::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
#
# """""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""
# ::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::


from django.utils import timezone

class BaseModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

class YellowDotAfricaDatasync(BaseModel):
    raw_data = models.TextField(blank=True, null=True)

class YellowDotAfricaDailySubscription(BaseModel):
    SUBSCRIPTION_TYPE_CHOICES = [
        ('DAILY', 'Daily'),
        ('ON_DEMAND', 'On Demand'),
    ]

    SUBSCRIPTION_STATUS_CHOICES = [
        ('FAILED', 'Failed'),
        ('DEACTIVATED', 'Deactivated'),
        ('ACTIVATION', 'Activation'),
        ('RENEWAL', 'Renewal'),
    ]

    phone_number = models.CharField(max_length=15)
    service_name = models.CharField(max_length=100)
    subscription_type = models.CharField(max_length=20, choices=SUBSCRIPTION_TYPE_CHOICES)
    subscription_status = models.CharField(max_length=20, choices=SUBSCRIPTION_STATUS_CHOICES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    service_id = models.CharField(max_length=50)
    product_id = models.CharField(max_length=50)
    transaction_id = models.CharField(max_length=100)
    unique_reference = models.CharField(max_length=200, unique=True, editable=False)
    datasync_reference = models.ForeignKey(YellowDotAfricaDatasync, on_delete=models.CASCADE, related_name='daily_subscriptions')
    subscription_date = models.CharField(max_length=100, blank=True, null=True)
    expiration_date = models.CharField(max_length=100, blank=True, null=True)

    def save(self, *args, **kwargs):
        if not self.unique_reference:
            date_str = timezone.now().strftime('%Y%m%d')
            self.unique_reference = f"{self.datasync_reference.id}_{date_str}"
        super().save(*args, **kwargs)

class YellowDotAfricaSubscriptionData(BaseModel):
    STATUS_CHOICES = [
        ('ACTIVE', 'Active'),
        ('DEACTIVATED', 'Deactivated'),
        ('ONE_TIME', 'One Time'),
    ]

    phone_number = models.CharField(max_length=15)
    service_name = models.CharField(max_length=100)
    subscription_type = models.CharField(max_length=20)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ACTIVE')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    service_id = models.CharField(max_length=50)
    product_id = models.CharField(max_length=50)
    subscription_date = models.DateTimeField(auto_now_add=True)
    deactivation_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['phone_number', 'product_id', 'status'], condition=models.Q(status='ACTIVE'), name='unique_active_subscription')
        ]

    def deactivate(self):
        self.status = 'DEACTIVATED'
        self.deactivation_date = timezone.now()
        self.save()

class YellowDotAfricaSubscriber(BaseModel):
    phone_number = models.CharField(max_length=15, unique=True)

class YellowDotAfricaSmsMODatasync(BaseModel):
    raw_data = models.TextField(blank=True, null=True)
