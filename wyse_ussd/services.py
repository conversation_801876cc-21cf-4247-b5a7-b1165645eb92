# services.py
import requests
import logging
import re
from django.conf import settings

logger = logging.getLogger(__name__)

class YellowDotAfricaService:
    """Service for interacting with Yellow Dot Africa API"""

    def __init__(self):
        self.base_url = getattr(settings, 'YELLOWDOT_BASE_URL', '')
        self.api_key = getattr(settings, 'YELLOWDOT_API_KEY', '')
        self.username = getattr(settings, 'YELLOWDOT_USERNAME', '')
        self.password = getattr(settings, 'YELLOWDOT_PASSWORD', '')

    def send_sms(self, phone_number: str, message: str, sender_id: str = None):
        """Send SMS to a user via Yellow Dot Africa API"""
        try:
            url = f"{self.base_url}/sms/send"
            payload = {
                "to": phone_number,
                "message": message,
                "username": self.username,
                "password": self.password,
            }

            if sender_id:
                payload["sender_id"] = sender_id

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}" if self.api_key else None,
            }
            headers = {k: v for k, v in headers.items() if v is not None}

            response = requests.post(url, json=payload, headers=headers, timeout=20)

            if response.status_code == 200:
                result = response.json()
                return {
                    "success": True,
                    "transaction_id": result.get("transaction_id"),
                    "message_id": result.get("message_id"),
                    "response": result,
                }

            logger.error(f"Failed to send SMS: {response.status_code} {response.text}")
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text}",
            }

        except requests.exceptions.RequestException as e:
            logger.error(f"SMS send error: {str(e)}")
            return {"success": False, "error": str(e)}
