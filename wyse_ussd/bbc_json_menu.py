import random
import uuid
from datetime import datetime

from django.conf import settings
from django.db.models import Q

from account.models import BlackListed
from awoof_app.models import AwoofGameTable, LifeStyleTable
from broad_base_communication.bbc_helper import BBCTelcoAggregator
from main.helpers.helper_functions import fetch_account_name
from main.helpers.redis_storage import RedisDictStorage, RedisStorage
from main.models import (
    ConstantVariable,
    LotteryModel,
    MaxWithdrawalThresold,
    PayoutTransactionTable,
    UserProfile,
)
from main.ussd.helpers import Utility
from prices.game_price import (
    TelcoAskAiPriceModel,
    TelcoAwoofPriceModel,
    TelcoInstantCashOutPriceModel,
    TelcoLibertyLifePriceModel,
    TelcoMonetizeAiPriceModel,
    TelcoSalaryForLifePriceModel,
    TelcoWyseCashPriceModel,
)
from scratch_cards.models import ScratchCard
from wallet_app.models import (
    DebitCreditRecord,
    FloatWallet,
    UserWallet,
    WalletTransaction,
)
from wyse_ussd.helper.bank import BankManger
from wyse_ussd.helper.general_helper import (
    has_enough_money_to_giveout,
    lottery_ticket_result,
    select_bank,
    text_handler,
    ussd_lottery_play2_subscription,
)
from wyse_ussd.helper.scratch_card_ussd_helper import UssdScratchCardHelper
from wyse_ussd.helper.telco_sub_ask_ai import (
    ask_ai_menu_item,
    ask_ai_subscription_ticket_amount,
)
from wyse_ussd.helper.telco_sub_instant_cash import (
    instant_cash_sub_item,
    instant_cashout_amount,
    summary_amount_inscashout,
)
from wyse_ussd.helper.telco_sub_liberty_life import (
    liberty_life_menu_item,
    liberty_life_subscription_ticket_amount,
)
from wyse_ussd.helper.telco_sub_mega_cash import (
    mega_cash_item,
    sub_wyse_cash_summary_amount,
    wyse_cash_subscription_ticket_amount,
)
from wyse_ussd.helper.telco_sub_monetize_ai import (
    monetize_ai_menu_item,
    monetize_ai_subscription_ticket_amount,
)
from wyse_ussd.helper.telco_sub_salary_life3 import (
    salary_for_life_amount,
    telco_sub_salary_life3_item,
    telco_summary_amount_salary_for_life,
)
from wyse_ussd.models import (
    GameShowParticipant,
    SoccerPredictionRequestLogs,
    TelcoSubscriptionPlan,
    TelcoSubscriptionRequest,
    create_wyse_lotto_ticket,
)
from wyse_ussd.tasks import (
    celery_deactivate_telco_subscription,
    celery_telco_airtime_charge,
    celery_telco_ussd_backgroud,
    celery_update_teclo_users_winning_withdrawal,
    celery_update_telco_session,
    create_ussd_virtual_account,
    handle_lotto_and_loans_connection,
    handle_lotto_and_loans_connection_for_insurance,
    ussd_payout,
)
from wyse_ussd.telco_bbc_menu import extract_text_after_44


def new_bbc_ussd_menu(data):
    """
    {
        "sessionId": "*************",
        "msisdn": "*************",
        "network": "MTN",
        "serviceCode": "5288",
        "text": "",
        "ussdOpType": 1,
        "messageType": 0
    }
    """

    # Get the user's phone number
    phone_number = data.get("msisdn")

    # Get the user's network
    network = data.get("network")

    # Get the session ID
    session_id = data.get("sessionId")

    # Get the service code
    service_code = data.get("serviceCode")

    # Get the text
    text = data.get("text")

    text = str(text).replace("*1001", "").replace("1001*", "").replace("1001*1001", "").replace("1001", "").strip()

    # Get the ussd operation type
    ussd_op_type = data.get("ussdOpType")

    # Get the message type
    message_type = data.get("messageType")

    sender_cb = session_id

    menu_options = "Welcome to WinWise:\nWin cash prizes up to 10m daily.\n\n"

    menu_options += "1.Win Salary For Life\n"
    menu_options += "2.Win InstantCash\n"
    menu_options += "3.Win Wyse Cash \n"
    menu_options += "4.Fast Fingers \n"
    menu_options += "5.Soccer Cash \n"
    menu_options += "01.Next \n"

    telco_aggregator = BBCTelcoAggregator()

    phone = LotteryModel.format_number_from_back_add_234(phone_number)

    payload = {
        "sessionId": session_id,
        "msisdn": phone_number,
        "network": network,
        "serviceCode": service_code,
        "text": text,
        "ussdOpType": ussd_op_type,
        "messageType": message_type,
        "use_json_format": True,
    }

    """
    RESTRICTION CHECK
    """
    if BlackListed().is_blacklisted(phone=phone_number):
        menu_options = "You are not allowed to use this service"
        payload["text"] = menu_options

        return telco_aggregator.end_ussd_session(f"telco_session_{session_id}", **payload)

    if BlackListed().can_play_instant_cashout(phone=phone_number) is False:
        menu_options = "Instant cashout is not accessible for you at the moment"
        payload["text"] = menu_options

        return telco_aggregator.end_ussd_session(f"telco_session_{session_id}", **payload)

    """
    JUST A SIMPLE ALGORITHM TO CHECK IF USER DIALS MENU OPTION AT A SINGLR DAIL.
    e.g *20144*1# or *20144*2# or *20144*3# or *20144*4#
    the string after the second * and before the # is the menu option
    """

    ussd_string = extract_text_after_44(text)
    print(
        f"""
        EXTRACTED USSD STRINGS: {ussd_string}
        \n\n\n
        """
    )

    if (message_type == "0" or message_type == 0) and ussd_string == "":
        payload["ussdOpType"] = 1
        payload["messageType"] = 1
        payload["text"] = menu_options

        if settings.DEBUG is True:
            celery_telco_ussd_backgroud.delay(**payload)
        else:
            celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

    else:
        # HANDLE CASES FOR BACK AND NEXT MENU OPTIONS

        if text == "01":
            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={
                    "user_phone": phone_number,
                    "session_id": sender_cb,
                    "network": network,
                },
            )

            response = "Welcome to WinWise:\nWin cash prizes up to 10m daily.\n\n"
            response += "6.Ask AI \n"
            response += "7.Monetize AI \n"
            response += "8.Liberty Life \n"
            response += "9.About \n"
            response += "10.My Account \n"
            response += "11. Payout\n"
            response += "02. Next \n"

            payload["text"] = response

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        if text == "02" or text == "01*02":
            response = "Welcome to WinWise:\nWin cash prizes up to 10m daily.\n\n"
            response += "12.Result \n"
            response += "13.Deactivate Subscription \n"
            response += "0.Back \n"

            payload["text"] = response

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # END OF BACK AND NEXT MENU OPTIONS

        # remove "01", "02"
        if text.startswith("01*") or text.startswith("02*"):
            text = text[3:]

        text = text_handler(text)

        print(
            f"""
            text: {text}
            type of text: {type(text)}
            \n\n\n
            """
        )

        # FOR ROUTING LOAN DIAL TO LOAN SERVER #
        # FOR MTN #

        if text == "55" or text.startswith("55*") or text.startswith("55"):
            if str(text).startswith("55"):
                text = str(text).replace("55", "").strip()
            elif str(text).startswith("55*", ""):
                text = str(text).replace("55*", "").strip()

            if str(text).startswith("*"):
                text = str(text[1:]).strip()

            req_payload = {
                "phone_number": phone_number,
                "service_code": "20144",
                "text": text,
                "session_id": f"telco_session_{sender_cb}",
                "network_code": service_code,
            }

            session_has_to_end, menu_messages = handle_lotto_and_loans_connection(**req_payload)

            if session_has_to_end is True:
                menu_options = str(menu_messages).replace("END", "")
                payload["text"] = menu_options

                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)
            else:
                menu_options = str(menu_messages).replace("CON", "")

                menu_options += "\n"

                payload["text"] = menu_options

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        # INSURANCE REQUEST INTERACTION WITH LOAN SERVER
        if text == "6*58" or text.startswith("6*58*") or text.startswith("6*58"):
            text = str(text).replace("6*", "").strip()

            req_payload = {
                "phone_number": phone_number,
                "service_code": "20144",
                "text": text,
                "session_id": f"telco_session_{sender_cb}",
                "network_code": service_code,
            }

            session_has_to_end, menu_messages = handle_lotto_and_loans_connection_for_insurance(**req_payload)

            if session_has_to_end is True:
                menu_options = str(menu_messages).replace("END", "")
                payload["text"] = menu_options

                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

            else:
                menu_options = str(menu_messages).replace("CON", "")

                menu_options += "\n"

                payload["text"] = menu_options

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        # FOR GLO
        if text == "6*55" or text.startswith("6*55*") or text.startswith("6*55"):
            if str(text).startswith("6*55"):
                text = str(text).replace("6*55", "").strip()
            elif str(text).startswith("55*", ""):
                text = str(text).replace("6*55*", "").strip()

            if str(text).startswith("*"):
                text = str(text[1:]).strip()

            req_payload = {
                "phone_number": phone_number,
                "service_code": "20144",
                "text": text,
                "session_id": f"telco_session_{sender_cb}",
                "network_code": service_code,
            }

            session_has_to_end, menu_messages = handle_lotto_and_loans_connection(**req_payload)

            if session_has_to_end is True:
                menu_options = str(menu_messages).replace("END", "")
                payload["text"] = menu_options

                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

            else:
                menu_options = str(menu_messages).replace("CON", "")

                menu_options += "\n"

                payload["text"] = menu_options

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        splited_text = text.split("*")
        splited_text = [i for i in splited_text if i != ""]

        # GAME SHOW PARTICIPANT DATA COLLECTION
        if text == "92" or text.startswith("92*") or text.startswith("92"):
            menu_content = "Welcome to Winwise game show participant data collection.\n"
            menu_content += "1. I am available for the game show\n"
            menu_content += "2. Not interested \n"

            payload["text"] = menu_content

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        if text == "92*2":
            GameShowParticipant.objects.create(phone_number=phone_number, interest_status="NOT_INTERESTED")

            payload["text"] = "Thank you, you interest status have been updated"

            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

        elif text == "92*1":
            menu_content = "1. I am based in Lagos\n"
            menu_content += "2.I am not based in Lagos \n"

            payload["text"] = menu_content

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text == "92*1*2":
            GameShowParticipant.objects.create(phone_number=phone_number, interest_status="INTERESTED")

            payload["text"] = "Thank you for dialing we will let you know when you can participate in the game later"

            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

        elif text == "92*1*1":
            menu_content = "Enter your Full Name \n"
            payload["text"] = menu_content

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("92*1*1*"):
            full_name = splited_text[-1]

            GameShowParticipant.objects.create(phone_number=phone_number, interest_status="INTERESTED", full_name=full_name, based_in_lagos=True)

            payload["text"] = "Thank you for showing interest, your details have been recorded"

            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

        if text == "":
            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={
                    "user_phone": phone_number,
                    "session_id": sender_cb,
                    "network": network,
                },
            )

            payload["ussdOpType"] = 1
            payload["messageType"] = 1
            payload["text"] = menu_options
            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # :::::::::::::::::::::::::::::::::: User verification code
        if text == "50":
            try:
                user_instance = UserProfile.objects.get(phone_number=phone_number)
            except Exception:
                menu_options = "An error occured, please try again later"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if (user_instance.verification_code is None) or (user_instance.verification_code == ""):
                user_instance.verification_code = UserProfile.generate_mixed_pin()
                user_instance.save()

            menu_options = f"Your verification code is {user_instance.verification_code}"
            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # :::::::::::::::::::::::::::::::::: User verification code

        # :::::::::::::::::::::::::::::::::: SALARY FOR LIFE
        if text == "1":
            """
            FIRST STEP WHERE PLAYERS GET TO SEE SALARY FOR LIFE SUBSCRIPTION AND ON-DEMAND GAMES
            """
            menu_options = telco_sub_salary_life3_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["text"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("1*"):
            if len(splited_text) > 2:
                splited_text[2:] = []

            jackpot = 1

            _ticket_amount, is_on_demand = salary_for_life_amount(splited_text[1])
            if not isinstance(_ticket_amount, int):
                response = salary_for_life_amount(splited_text[1])

                menu_options = str(response).replace("CON", "")
                payload["text"] = menu_options

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

            num_lines = 1

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            # user_profile = UserProfile.objects.filter(phone_number=phone).last()

            if is_on_demand is True:
                # ON DEMAND SUBSCRIPTION SECTION

                lotto = create_wyse_lotto_ticket(
                    phone_number=phone_number,
                    lottery_type="SALARY_FOR_LIFE",
                    jackpot=jackpot,
                    bank="",
                    auto=True,
                    stake_amount=_ticket_amount,
                    telco=True,
                    is_telco_subscription=True if is_on_demand is False else False,
                )

                stake_and_win_amount = telco_summary_amount_salary_for_life.get(1)

                telco_service_id_telco_product_id = TelcoSalaryForLifePriceModel().get_bbc_service_and_prodct_details(int(stake_and_win_amount[0]))

                """
                TELCO CHARGES
                """
                telco_charge_payload = {
                    "phone_number": phone_number,
                    "description": "salary for life lottery payment",
                    "amount": stake_and_win_amount[0],
                    "game_play_id": lotto.game_play_id,
                    "lottery_type": "SALARY_FOR_LIFE",
                    "pontential_winning": stake_and_win_amount[1],
                    "service_id": telco_service_id_telco_product_id[0],
                    "product_id": telco_service_id_telco_product_id[1],
                    "use_json_format": True,
                }

                if settings.DEBUG is True:
                    celery_telco_airtime_charge.apply_async(
                        kwargs=telco_charge_payload,
                    )
                else:
                    celery_telco_airtime_charge.apply_async(
                        queue="telcocharge1",
                        kwargs=telco_charge_payload,
                    )

                celery_update_telco_session.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "user_phone": phone_number,
                        "session_id": sender_cb,
                        "network": network,
                    },
                )

                response = telco_sub_salary_life3_item["summary"].format(
                    num_lines,
                    Utility.currency_formatter(stake_and_win_amount[0]),
                    lotto.ticket,
                )

                menu_options = str(response).replace("END", "")
                payload["text"] = menu_options

                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)
            else:  # DAILY SUBSCRIPTION SECTION
                """
                TELCO SUBSCRIPTION ACTIVATION
                """

                pontential_winning = 0
                subscription_prices = TelcoSalaryForLifePriceModel.subscription_ticket_prices()

                for i in subscription_prices:
                    if subscription_prices.get(i).get("ticket_price") == int(_ticket_amount):
                        pontential_winning = subscription_prices.get(i).get("ticket_price")
                        break

                if pontential_winning == 0:
                    menu_options = "Invalid option ..."
                    payload["text"] = menu_options

                    return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

                telco_subscription_service_id_telco_product_id = TelcoSalaryForLifePriceModel.subscription_plan_service_and_plan_id(_ticket_amount)

                telco_charge_payload = {
                    "phone_number": phone_number,
                    "description": "salary for life lottery payment",
                    "amount": _ticket_amount,
                    "game_play_id": None,
                    "lottery_type": "SALARY_FOR_LIFE",
                    "pontential_winning": pontential_winning,
                    "service_id": telco_subscription_service_id_telco_product_id[0],
                    "product_id": telco_subscription_service_id_telco_product_id[1],
                    "is_telco_subscription": True,
                    "use_json_format": True,
                }

                if settings.DEBUG is True:
                    celery_telco_airtime_charge.delay(
                        **telco_charge_payload,
                    )

                else:
                    celery_telco_airtime_charge.apply_async(
                        queue="telcocharge1",
                        kwargs=telco_charge_payload,
                    )

                celery_update_telco_session.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "user_phone": phone_number,
                        "session_id": sender_cb,
                        "network": network,
                    },
                )

                # menu_options = str(response).replace("END", "")
                payload["text"] = (
                    "Thank you for your interest in Winwise Salary for Life. An opt-in screen will be displayed. Please accept your subscription plan to continue."
                )

                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

        # :::::::::::::::::::::::::::::::::: INSTANT CASHOUT
        elif text == "2":
            menu_options = instant_cash_sub_item[text]
            menu_options = menu_options.replace("CON", "")

            # payload["ussdOpType"] = "1"
            # payload["messageType"] = "1"

            payload["text"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("2*"):  # and ((len(text) == 3) or (len(text) == 4))
            if len(splited_text) > 2:
                splited_text[2:] = []

            splited_text[:2]

            jackpot = 1

            num_lines = 1

            _instant_cashout_ticket_amount, _is_on_demand = instant_cashout_amount(splited_text[1])

            if not isinstance(_instant_cashout_ticket_amount, int):
                response = "Invalid option ...."

                menu_options = str(response).replace("CON", "")
                payload["text"] = menu_options

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            stake_and_win_amount = summary_amount_inscashout.get(int(splited_text[1]))

            if _is_on_demand is True:
                lotto = create_wyse_lotto_ticket(
                    phone_number=phone_number,
                    lottery_type="INSTANT_CASHOUT",
                    jackpot=jackpot,
                    bank="",
                    auto=True,
                    stake_amount=_instant_cashout_ticket_amount,
                    telco=True,
                    is_telco_subscription=True if _is_on_demand is False else False,
                )

                num_lines = splited_text[1]

                stake_and_win_amount = summary_amount_inscashout.get(1)

                telco_service_id_telco_product_id = TelcoInstantCashOutPriceModel().get_bbc_service_and_prodct_details(_instant_cashout_ticket_amount)

                """
                TELCO CHARGES
                """

                telco_charge_payload = {
                    "phone_number": phone_number,
                    "description": "instant cashout lottery payment",
                    "amount": _instant_cashout_ticket_amount,
                    "game_play_id": lotto.game_play_id,
                    "lottery_type": "INSTANT_CASHOUT",
                    "pontential_winning": stake_and_win_amount[1],
                    "service_id": telco_service_id_telco_product_id[0],
                    "product_id": telco_service_id_telco_product_id[1],
                    "use_json_format": True,
                }

                if settings.DEBUG is True:
                    celery_telco_airtime_charge.delay(
                        **telco_charge_payload,
                    )
                else:
                    celery_telco_airtime_charge.apply_async(
                        queue="telcocharge1",
                        kwargs=telco_charge_payload,
                    )

                celery_update_telco_session.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "user_phone": phone_number,
                        "session_id": sender_cb,
                        "network": network,
                    },
                )

                registered_tickets = lotto.ticket

                response = instant_cash_sub_item["summary"].format(
                    num_lines,
                    Utility.currency_formatter(stake_and_win_amount[0]),
                    registered_tickets,
                )

                menu_options = str(response).replace("END", "")
                payload["text"] = menu_options

                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

            else:
                """
                TELCO SUBSCRIPTION ACTIVATION
                """

                pontential_winning = 0

                subscription_prices = TelcoInstantCashOutPriceModel.subscription_ticket_prices()

                for i in subscription_prices:
                    if subscription_prices.get(i).get("ticket_price") == int(_instant_cashout_ticket_amount):
                        pontential_winning = subscription_prices.get(i).get("ticket_price")
                        break

                if pontential_winning == 0:
                    menu_options = "Invalid option selected ....."
                    payload["text"] = menu_options

                    return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

                telco_subscription_service_id_telco_product_id = TelcoInstantCashOutPriceModel.subscription_plan_service_and_plan_id(
                    _instant_cashout_ticket_amount
                )

                telco_charge_payload = {
                    "phone_number": phone_number,
                    "description": "instant cashout lottery payment",
                    "amount": _instant_cashout_ticket_amount,
                    "game_play_id": "None",
                    "lottery_type": "INSTANT_CASHOUT",
                    "pontential_winning": pontential_winning,
                    "service_id": telco_subscription_service_id_telco_product_id[0],
                    "product_id": telco_subscription_service_id_telco_product_id[1],
                    "is_telco_subscription": True,
                    "use_json_format": True,
                }

                if settings.DEBUG is True:
                    celery_telco_airtime_charge.delay(
                        **telco_charge_payload,
                    )
                else:
                    celery_telco_airtime_charge.apply_async(
                        queue="telcocharge1",
                        kwargs=telco_charge_payload,
                    )

                celery_update_telco_session.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "user_phone": phone_number,
                        "session_id": sender_cb,
                        "network": network,
                    },
                )

                # response = telco_sub_salary_life3_item["summary"].format(
                #     num_lines,
                #     Utility.currency_formatter(_instant_cashout_ticket_amount),
                #     lotto.ticket,
                # )

                # menu_options = str(response).replace("END", "")

                # payload["text"] = menu_options

                payload["text"] = (
                    "Thank you for your interest in Winwise Instant cashout. An opt-in screen will be displayed. Please accept your subscription plan to continue."
                )

                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

        # :::::::::::::::::::::::::::::::::: END INSTANT CASHOUT

        # :::::::::::::::::::::::::::::::::: WYSE CASH

        elif text == "3":
            menu_options = mega_cash_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["text"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("3*"):
            (
                _wyse_cash_ticket_amount,
                _is_on_demand,
            ) = wyse_cash_subscription_ticket_amount(splited_text[1])

            if len(splited_text) > 3:
                splited_text[2:] = []

            _last_wyse_cash_selection = text[-1]

            if not isinstance(int(_last_wyse_cash_selection), int):
                menu_options = "Invalid option selected ......"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if int(_last_wyse_cash_selection) in [3, 4, 5]:
                ussd_lottery_play2_subscription(
                    phone_number=phone_number,
                    lottery_type="wyse_cash",
                    text=text,
                    from_telco_play=True,
                )

                splited_text[:2]

                if len(text) > 5:
                    text = text[:5]

                phone = LotteryModel.format_number_from_back_add_234(phone_number)

                create_ussd_virtual_account.apply_async(queue="celery1", args=[phone])

                user_profile = UserProfile.objects.filter(phone_number=phone).last()

                # get last played lottery
                lottery_instance = LotteryModel.objects.filter(user_profile=user_profile).last()

                stake_and_win_amount = sub_wyse_cash_summary_amount.get(text)

                telco_service_id_telco_product_id = TelcoWyseCashPriceModel().get_bbc_service_and_prodct_details(int(stake_and_win_amount[0]) / 1)

                """
                TELCO CHARGES
                """

                telco_charge_payload = {
                    "phone_number": phone_number,
                    "description": "wyse cash lottery payment",
                    "amount": stake_and_win_amount[0],
                    "game_play_id": lottery_instance.game_play_id,
                    "lottery_type": "WYSE_CASH",
                    "pontential_winning": stake_and_win_amount[1],
                    "service_id": telco_service_id_telco_product_id[0],
                    "product_id": telco_service_id_telco_product_id[1],
                    "use_json_format": True,
                }

                celery_update_telco_session.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "user_phone": phone_number,
                        "session_id": sender_cb,
                        "network": network,
                    },
                )

                if settings.DEBUG is True:
                    celery_telco_airtime_charge.delay(
                        **telco_charge_payload,
                    )
                else:
                    celery_telco_airtime_charge.apply_async(
                        queue="telcocharge1",
                        kwargs=telco_charge_payload,
                    )

                # user profile
                phone = LotteryModel.format_number_from_back_add_234(phone_number)
                user_profile = UserProfile.objects.filter(phone_number=phone).last()

                response = mega_cash_item["summary"].format(
                    lottery_instance.instance_number,
                    Utility.currency_formatter(stake_and_win_amount[0]),
                    lottery_instance.game_play_id,
                )

                menu_options = str(response).replace("END", "")
                payload["text"] = menu_options

                # print("lotto.ticket", "\n\n\n\n\n")

                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

            else:
                """
                TELCO SUBSCRIPTION ACTIVATION
                """

                # user profile
                phone = LotteryModel.format_number_from_back_add_234(phone_number)
                user_profile = UserProfile.objects.filter(phone_number=phone).last()

                # get last played lottery
                lottery_instance = LotteryModel.objects.filter(user_profile=user_profile).last()

                pontential_winning = 0

                subscription_prices = TelcoWyseCashPriceModel.subscription_ticket_prices()

                for i in subscription_prices:
                    if subscription_prices.get(i).get("ticket_price") == int(_wyse_cash_ticket_amount):
                        pontential_winning = subscription_prices.get(i).get("ticket_price")
                        break

                if _wyse_cash_ticket_amount is None:
                    menu_options = "Invalid option selected ......."
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                telco_subscription_service_id_telco_product_id = TelcoWyseCashPriceModel.subscription_plan_service_and_plan_id(
                    _wyse_cash_ticket_amount
                )

                telco_charge_payload = {
                    "phone_number": phone_number,
                    "description": "wyse cash lottery payment",
                    "amount": _wyse_cash_ticket_amount,
                    "game_play_id": None,
                    "lottery_type": "WYSE_CASH",
                    "pontential_winning": pontential_winning,
                    "service_id": telco_subscription_service_id_telco_product_id[0],
                    "product_id": telco_subscription_service_id_telco_product_id[1],
                    "use_json_format": True,
                }

                if settings.DEBUG is True:
                    celery_telco_airtime_charge.delay(
                        **telco_charge_payload,
                    )
                else:
                    celery_telco_airtime_charge.apply_async(
                        queue="telcocharge1",
                        kwargs=telco_charge_payload,
                    )

                # print(f"""
                # WYSE CASH SUBSCRIPTION

                # \n\n\n\n\n
                # """)

                celery_update_telco_session.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "user_phone": phone_number,
                        "session_id": sender_cb,
                        "network": network,
                    },
                )

                # response = mega_cash_item["summary"].format(
                #     lottery_instance.instance_number,
                #     Utility.currency_formatter(_wyse_cash_ticket_amount),
                #     lottery_instance.game_play_id,
                # )

                # menu_options = str(response).replace("END", "")

                # payload["text"] = menu_options

                payload["text"] = (
                    "Thank you for your interest in Winwise Salary for Life. An opt-in screen will be displayed. Please accept your subscription plan to continue."
                )

                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                # print("lotto.ticket", "\n\n\n\n\n")

                return telco_aggregator.end_ussd_session(f"telco_session_{sender_cb}", **payload)

        # :::::::::::::::::::::::::::::::::: END WYSE CASH

        # :::::::::::::::::::::::::::::::::: AWOOF GAME / FAST FINGERS

        elif text == "4":
            awoof_items = LifeStyleTable().awoof_ussd_iteamsfor_telco_subscription(session_id)
            if awoof_items is None:
                menu_options = "No awoof item available at the moment"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            else:
                # response = (
                #     f"Select entry to qualify\nfor fast finger item ({awoof_items})\n\n"
                # )

                response = f"win ({awoof_items}) in the next draw\n\n"

                price_model_instance = TelcoAwoofPriceModel().subscription_ticket_prices()

                _numbering = 1
                for _i in price_model_instance:
                    if _numbering == 3:
                        response += f"{_numbering}. {Utility.currency_formatter(price_model_instance.get(_i).get('ticket_price'))} Instant\n"
                    else:
                        response += f"{_numbering}. {Utility.currency_formatter(price_model_instance.get(_i).get('ticket_price'))} Auto\n"

                    _numbering += 1

                payload["text"] = response

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        elif text.startswith("4*") and len(splited_text) == 2:
            last_item = splited_text[-1]
            try:
                last_item = int(last_item)
            except Exception:
                menu_options = "Invalid option selecte ........"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            _item_id_stored_in_redis = RedisStorage(redis_key=f"{session_id}__id")

            if _item_id_stored_in_redis.get_data() is None:
                LifeStyleTable().awoof_ussd_iteamsfor_telco_subscription(session_id)

                _item_id_stored_in_redis = RedisStorage(redis_key=f"{session_id}__id")

            item_id_num = (_item_id_stored_in_redis.get_data()).decode("utf-8")

            try:
                item_id_num = int(item_id_num)
            except Exception:
                print("FAILED TO CONVERT TO INT")

                menu_options = "Something went wrong, please try again. 1"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            (
                awoof_ticket_price,
                __is_on_demand,
            ) = TelcoAwoofPriceModel.ticket_price_and_price_type(number=last_item)

            item = LifeStyleTable.objects.get(id=int(item_id_num))

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            if __is_on_demand is True:
                awoof_game_instance = AwoofGameTable().register_telco_ussd_awoof_game(
                    awoof_item_db_id=int(item_id_num),
                    user_selected_chances=200,
                    user_phone_number=phone_number,
                    band_number=last_item,
                    from_telco_channel=True,
                )

                if awoof_game_instance is None:
                    menu_options = "Something went wrong, please try again."
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                telco_service_id_telco_product_id = TelcoAwoofPriceModel().get_bbc_service_and_prodct_details(awoof_ticket_price)

                """
                TELCO CHARGES
                """

                telco_charge_payload = {
                    "phone_number": phone_number,
                    "description": "fast fingers lottery payment",
                    "amount": awoof_ticket_price,
                    "game_play_id": awoof_game_instance.game_play_id,
                    "lottery_type": "AWOOF",
                    "pontential_winning": item.item_name,
                    "service_id": telco_service_id_telco_product_id[0],
                    "product_id": telco_service_id_telco_product_id[1],
                    "use_json_format": True,
                }

                if settings.DEBUG is True:
                    celery_telco_airtime_charge.delay(
                        **telco_charge_payload,
                    )
                else:
                    celery_telco_airtime_charge.apply_async(
                        queue="telco_menu_worker",
                        kwargs=telco_charge_payload,
                    )

                celery_update_telco_session.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "user_phone": phone_number,
                        "session_id": sender_cb,
                        "network": network,
                    },
                )

            else:
                telco_service_id_telco_product_id = TelcoAwoofPriceModel().get_bbc_service_and_prodct_details_for_subscription(awoof_ticket_price)

                """
                TELCO SUBSCRIPTION ACTIVATION
                """

                telco_charge_payload = {
                    "phone_number": phone_number,
                    "description": "fast fingers lottery payment",
                    "amount": awoof_ticket_price,
                    "game_play_id": None,
                    "lottery_type": "AWOOF",
                    "pontential_winning": item.item_name,
                    "service_id": telco_service_id_telco_product_id[0],
                    "product_id": telco_service_id_telco_product_id[1],
                    "is_telco_subscription": True,
                    "use_json_format": True,
                }

                if settings.DEBUG is True:
                    celery_telco_airtime_charge.delay(
                        **telco_charge_payload,
                    )
                else:
                    celery_telco_airtime_charge.apply_async(
                        queue="telco_menu_worker",
                        kwargs=telco_charge_payload,
                    )
                # celery_telco_airtime_charge(**telco_charge_payload)

                celery_update_telco_session.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "user_phone": phone_number,
                        "session_id": sender_cb,
                        "network": network,
                    },
                )

            response = "Summary: Fast Fingers\n"
            response += f"Potential Win: {item.item_name}\n"
            response += "No. of ticket: 1\n"
            response += f"Amt: {Utility.currency_formatter(awoof_ticket_price)}\n"

            # payload["text"] = response

            payload["text"] = (
                "Thank you for your interest in Winwise Fast finger game. An opt-in screen will be displayed. Please accept your subscription plan to continue."
            )

            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # :::::::::::::::::::::::::::::::::: END AWOOF GAME / FAST FINGERS

        # :::::::::::::::::::::::::::::::::: SOCCER CASH
        # :::::::::::::::::::::::::::::::::: SOCCER CASH

        elif text == "5":
            response = "Pick daily subscription plan\n"
            response += "1. N75 Sub. 2 predictions\n"
            response += "2. N100 Sub. 5 predictions\n"

            payload["text"] = response

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("5*1"):
            menu_options = "Please, Await auth request, ACCEPT to enter."
            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            TelcoSubscriptionRequest.create_request(
                amount=75,
                phone_number=phone_number,
                game_type="SOCCER_CASH",
            )

            telco_charge_payload = {
                "phone_number": phone_number,
                "description": "soccer cash lottery payment",
                "amount": 75,
                "game_play_id": "g",
                "lottery_type": "SOCCER_CASH",
                "pontential_winning": 1500,
                "service_id": 234102200006964,
                "product_id": 23410220000027468,
                "is_telco_subscription": True,
                "use_json_format": True,
            }

            if settings.DEBUG is True:
                celery_telco_airtime_charge.delay(
                    **telco_charge_payload,
                )
            else:
                celery_telco_airtime_charge.apply_async(
                    queue="telcocharge1",
                    kwargs=telco_charge_payload,
                )

            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={
                    "user_phone": phone_number,
                    "session_id": sender_cb,
                    "network": network,
                },
            )

            try:
                SoccerPredictionRequestLogs.objects.create(
                    phone_number=phone_number,
                    type_of_request="SOCCER_PREDICTION",
                    amount=75,
                )
            except Exception:
                pass

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("5*2"):
            menu_options = "Please, Await auth request, ACCEPT to enter."
            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            TelcoSubscriptionRequest.create_request(
                amount=100,
                phone_number=phone_number,
                game_type="SOCCER_CASH",
            )

            telco_charge_payload = {
                "phone_number": phone_number,
                "description": "soccer cash lottery payment",
                "amount": 75,
                "game_play_id": "g",
                "lottery_type": "SOCCER_CASH",
                "pontential_winning": 1500,
                "service_id": 234102200006964,
                "product_id": 23410220000027469,
                "is_telco_subscription": True,
                "use_json_format": True,
            }

            celery_telco_airtime_charge.apply_async(
                queue="telcocharge1",
                kwargs=telco_charge_payload,
            )

            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={
                    "user_phone": phone_number,
                    "session_id": sender_cb,
                    "network": network,
                },
            )

            try:
                SoccerPredictionRequestLogs.objects.create(
                    phone_number=phone_number,
                    type_of_request="SOCCER_PREDICTION",
                    amount=100,
                )
            except Exception:
                pass

            return telco_aggregator.ussd_response(send=False)
        elif text.startswith("5*"):
            menu_options = "Invalid option selected"
            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text == "9":
            payload["text"] = "Winwise is licensed by National lottery regulatory commission"
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        #
        # account
        elif text == "10":
            response = "Select an option:\n"
            response += "1. Check Balance\n"
            response += "2. Deposit\n"
            response += "3. Transaction History\n"

            menu_options = response
            payload["text"] = menu_options
            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text == "10*1":
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            if user_profile is None:
                user_profile = UserProfile.objects.create(phone_number=phone)

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            response = f"Your play account balance is {Utility.currency_formatter(user_wallet.game_available_balance)}"
            response += f"\nYour withdrawable balance is {Utility.currency_formatter(user_wallet.withdrawable_available_balance)}"

            menu_options = response

            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text == "10*2":
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

                response = "END You don't have a deposit account number yet, check back in 10 minutes"
                return response

            if user_wallet.woven_account is None:
                response = "END You don't have a deposit account number yet, check back in 10 minutes"
                return response

            response = "Please deposit into this account details below:\n"
            response += f"Account Name: {user_wallet.woven_account.acct_name}\n"
            response += f"Account Number: {user_wallet.woven_account.vnuban}\n"
            response += f"Bank Name: {user_wallet.woven_account.bank_name}\n"

            menu_options = response

            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text == "10*3":
            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            user_game_play_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="VIRTUAL_SOCCER_GAME_PLAY")
                    | Q(transaction_from="SOCCER_CASH_GAME_PLAY")
                    | Q(transaction_from="SAL_4_LIFE_GAME_PLAY")
                    | Q(transaction_from="INSTANT_CASHOUT_GAME_PLAY")
                    | Q(transaction_from="WYSE_CASH_GAME_PLAY")
                    | Q(transaction_from="VIRTUAL_SOCCER_GAME_PLAY")
                    | Q(transaction_from="BANKER_GAME_PLAY")
                    | Q(transaction_from="QUIKA_GAME_PLAY")
                    | Q(transaction_from="AWOOF_GAME_PLAY"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_game_play_transactions:
                last_game_play_transaction_amount = user_game_play_transactions.amount
            else:
                last_game_play_transaction_amount = 0

            user_payout_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="WITHDRAWAL"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_payout_transactions:
                last_payout_transaction_amount = user_payout_transactions.amount
            else:
                last_payout_transaction_amount = 0

            user_game_won_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="VIRTUAL_SOCCER_GAME_PLAY")
                    | Q(transaction_from="SOCCER_CASH_GAME_WIN")
                    | Q(transaction_from="SAL_4_LIFE_GAME_WIN")
                    | Q(transaction_from="INSTANT_CASHOUT_GAME_WIN")
                    | Q(transaction_from="WYSE_CASH_GAME_WIN")
                    | Q(transaction_from="VIRTUAL_SOCCER_GAME_WON")
                    | Q(transaction_from="BANKER_GAME_WON")
                    | Q(transaction_from="QUIKA_GAME_WON")
                    | Q(transaction_from="AWOOF_GAME_WON"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_game_won_transactions:
                last_game_won_transaction_amount = user_game_won_transactions.amount
            else:
                last_game_won_transaction_amount = 0

            user_funding_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="PAYSTACK_FUNDING")
                    | Q(transaction_from="PABBLY_PAYSTACK")
                    | Q(transaction_from="WOVEN_FUNDING")
                    | Q(transaction_from="FUNDING")
                    | Q(transaction_from="FUNDING_FROM_WITHDRAWABLE_WALLET")
                    | Q(transaction_from="REDBILLER_FUNDING")
                    | Q(transaction_from="WATU_PAY"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_funding_transactions:
                last_funding_transaction_amount = user_funding_transactions.amount
            else:
                last_funding_transaction_amount = 0

            user_reversal_transactions = (
                WalletTransaction.objects.filter(
                    Q(wallet__user__phone_number=phone),
                    Q(show_transaction=True),
                    Q(transaction_from="REVERSAL"),
                )
                .order_by("-date_created")
                .first()
            )
            if user_reversal_transactions:
                last_reversal_transaction_amount = user_reversal_transactions.amount
            else:
                last_reversal_transaction_amount = 0

            response = f"Last game play amount: {Utility.currency_formatter(last_game_play_transaction_amount)}\n"
            response += f"Last payout amount: {Utility.currency_formatter(last_payout_transaction_amount)}\n"
            response += f"Last game won amount: {Utility.currency_formatter(last_game_won_transaction_amount)}\n"
            response += f"Last reversal amount: {Utility.currency_formatter(last_reversal_transaction_amount)}\n"
            response += f"Last funding amount: {Utility.currency_formatter(last_funding_transaction_amount)}\n"

            menu_options = response

            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text == "11":
            print("ENTER 8 =================== ", "\n\n\n\n")
            if BlackListed().can_withdraw(phone=phone_number):
                menu_options = "Sorry you are not allowed to withdraw at the moment"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                menu_options = "Sorry, payout is not available at the moment"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()
            if user_profile is None:
                user_profile = UserProfile.objects.create(phone_number=phone)

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            print(
                "user_wallet.withdrawable_available_balance",
                user_wallet.withdrawable_available_balance,
                "\n\n\n",
            )

            if user_wallet.withdrawable_available_balance == 0:
                menu_options = "You have no available balance to withdraw"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            menu_options = f"Withdrawal account balance is {Utility.currency_formatter(user_wallet.withdrawable_available_balance)}.\nEnter amount to withdraw:\n"

            payload["text"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            print(
                "SENT TO AGGREGATOR ::::::::::::::::::::::::::::::::::::::::::::::",
                "\n\n\n\n",
            )
            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("11*") and len(splited_text) == 2:
            if BlackListed().can_withdraw(phone=phone_number):
                menu_options = "Sorry you are not allowed to withdraw at the moment"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                menu_options = "Sorry, payout is not available at the moment"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            if user_wallet.withdrawable_available_balance == 0:
                menu_options = "You have no available balance to withdrawt"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            amount_entered = splited_text[1]

            if not amount_entered.isdigit():
                menu_options = "Invalid amount entered"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if int(amount_entered) > user_wallet.withdrawable_available_balance:
                menu_options = "Insufficient balance"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if float(amount_entered) < 100:
                menu_options = "Minimum amount to withdraw is 100"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # check if we've enough money for payout
            if has_enough_money_to_giveout(phone, amount_entered) is False:
                menu_options = " We're sorry, an error occured while processing your request. Please try again later"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if network == "GLO":
                response = select_bank(is_glo=True)
            else:
                response = select_bank()

            menu_options = str(response).replace("CON", "")

            payload["text"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("11") and len(splited_text) == 3:
            if text.startswith("88"):
                pass

            if text.startswith("89"):
                pass

            else:
                if BlackListed().can_withdraw(phone=phone_number):
                    menu_options = "Sorry you are not allowed to withdraw at the moment"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                # -------- CHECK FOR PAYOUT STATUS -------------
                if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                    menu_options = "Sorry, payout is not available at the moment"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                response = "Enter Account Number"

                menu_options = response

                payload["text"] = menu_options

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        elif (text.startswith("11")) and (len(splited_text) == 4) and (not text.startswith("88") and not text.startswith("89")):
            if BlackListed().can_withdraw(phone=phone_number):
                menu_options = "Sorry you are not allowed to withdraw at the moment"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # -------- CHECK FOR PAYOUT STATUS -------------
            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                menu_options = "Sorry, payout is not available at the moment"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet is None:
                user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            if user_wallet.withdrawable_available_balance < 100:
                menu_options = "You do not have enough balance to withdraw"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            account_number = splited_text[3]
            if str(account_number) in ["**********"]:
                menu_options = "Sorry you are not allowed to withdraw at the moment"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # account = RedBiller.get_account_info(
            #     account_no=account_number, bank_code="000013"
            # )

            bank = select_bank(splited_text[2])
            bank_db = BankManger().bank_details(bank_name=bank)

            if not bank_db:
                menu_options = "Invalid bank"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            bank_code = bank_db.get("cbn_code")

            # paystack account verification
            bank_details = fetch_account_name(account_number, bank_code)

            if not isinstance(bank_details, dict):
                menu_options = "Invalid account details"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if bank_details.get("status") is not True:
                menu_options = "Invalid account details"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # # print("paystack account verification", bank_details)

            user_profile.account_name = bank_details.get("data").get("account_name")
            user_profile.account_num = account_number
            user_profile.account_name = bank_details.get("data").get("account_name")
            user_profile.save()

            # response = f"""confirm:
            # Bank: {bank_db.get("name")}
            # A/No: {account_number}
            # Name: {bank_details.get("data").get("account_name")}
            # 1.Confirm
            # 2.Exit
            # """
            response = "confirm:\n"
            response += f'Bank: {bank_db.get("name")}\n'
            response += f"A/No: {account_number}\n"
            response += f'Name: {bank_details.get("data").get("account_name")}\n'
            response += "1.Confirm\n"
            response += "2.Exit"

            menu_options = response

            payload["text"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif (text.startswith("11")) and (len(splited_text) == 5) and (not text.startswith("88") and not text.startswith("89")):
            if BlackListed().can_withdraw(phone=phone_number):
                menu_options = "Sorry you are not allowed to withdraw at the moment"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            phone = LotteryModel.format_number_from_back_add_234(phone_number)

            if has_enough_money_to_giveout(phone, float(splited_text[1])) is False:
                menu_options = "We're sorry, an error occured while processing your request. Please try again later"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
                menu_options = "Sorry, payout is not available at the moment"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            user_profile = UserProfile.objects.filter(phone_number=phone).last()

            bank = select_bank(splited_text[2])
            bank_db = BankManger().bank_details(bank_name=bank)

            account_number = splited_text[3]

            if splited_text[-1] == "1":
                user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
                if user_wallet.withdrawable_available_balance < float(splited_text[1]):
                    menu_options = "Insufficient funds"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    celery_update_teclo_users_winning_withdrawal.apply_async(
                        queue="telco_session_logs",
                        kwargs={
                            "phone_number": phone_number,
                        },
                    )

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                # check if user has attempted more than 1 payout in a day
                payout_count = PayoutTransactionTable.objects.filter(phone=user_profile.phone_number, date_added__date=datetime.now().date()).count()
                if payout_count >= 1:
                    menu_options = "You have reached the maximum payout limit for the day"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                # CHECK BEFORE DISURSING IF WE HAVE ENOUGH TO GIVE OUT
                if has_enough_money_to_giveout(phone, float(splited_text[1])) is False:
                    menu_options = "We're sorry, an error occured while processing your request. Please try again later"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                if FloatWallet.get_float_wallet(source="VFD").amount < float(splited_text[1]):
                    menu_options = "We're sorry, an error occured while processing your request. Please try again later"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                # CHECK IF USER HAS EXCEEDED THRESOLD

                if MaxWithdrawalThresold.check_and_update_thresold(phone_number=user_wallet.user.phone_number, amount=float(splited_text[1])) is True:
                    menu_options = "Please, try withdrawal in the next 24 hours"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                debit_credit_record = DebitCreditRecord.create_record(
                    phone_number=user_wallet.user.phone_number,
                    amount=float(splited_text[1]),
                    channel="WEB",
                    reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                    transaction_type="DEBIT",
                )

                wallet_payload = {
                    "transaction_from": "WITHDRAWAL",
                }

                UserWallet.deduct_wallet(
                    user=user_wallet.user,
                    amount=float(splited_text[1]),
                    channel="WEB",
                    transaction_id=debit_credit_record.reference,
                    user_wallet_type="WINNINGS_WALLET",
                    **wallet_payload,
                )

                ussd_payout.delay(user_profile.id, splited_text[1], bank, account_number)

                menu_options = "Your withdrawal request has been received"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                celery_update_teclo_users_winning_withdrawal.apply_async(
                    queue="telco_session_logs",
                    kwargs={
                        "phone_number": phone_number,
                    },
                )

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            elif splited_text[-1] == 2:
                menu_options = "Withdrawal request cancelled"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        elif (text.startswith("11") and len(splited_text) > 5) and (not text.startswith("88") and not text.startswith("89")):
            menu_options = "Invalid input"
            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # :::::::::::::::::::::::::::::::::: END ACCOUNT

        # :::::::::::::::::::::::::::::::::: CHECK GAME RESULT
        elif text == "12":
            response = "Enter your game play id to check result\n"

            menu_options = response

            payload["text"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("12*") and len(splited_text) == 2:
            game_play_id = splited_text[-1]
            response = lottery_ticket_result(game_play_id)

            menu_options = str(response).replace("END", "")

            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # ::::::::::::::::::::::::::::::::::::::: TELCO SUBSCRIBED SERVICES
        if text == "13":
            phone = LotteryModel.format_number_from_back_add_234(phone_number)
            subscription_plan_qs = TelcoSubscriptionPlan.objects.filter(
                phone_number=phone,
                subscription_status="ACTIVE",
            )[:4]
            if not subscription_plan_qs.exists():
                menu_options = "You do not have any active subscription"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            else:
                subscription_items = {}
                for _instance in subscription_plan_qs:
                    subscription_items[_instance.id] = str(_instance.game_type).replace("_", "")

                game_type_as_reds_db_instance = {}
                for i in subscription_items:
                    game_type_as_reds_db_instance[subscription_items[i]] = i

                RedisDictStorage(redis_key=f"{session_id}_subscription_items").set_data(game_type_as_reds_db_instance)

                response = "Select a service you wish to un-subscribe from:\n\n"

                numbering = 1
                for key in subscription_items:
                    response += f"{numbering}. {subscription_items.get(key)}\n"
                    numbering += 1

                payload["text"] = response

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        if text.startswith("13*"):
            subscription_item_stored_in_redis = RedisDictStorage(redis_key=f"{session_id}_subscription_items").get_data()

            last_item = splited_text[-1]

            try:
                last_item = int(last_item)
            except Exception:
                menu_options = "Invalid option selected .9"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            iter_helper = 1
            db_item_id = 0

            for i in subscription_item_stored_in_redis:
                if iter_helper == last_item:
                    db_item_id = subscription_item_stored_in_redis.get(i)
                    break

                iter_helper += 1

            try:
                db_instance = TelcoSubscriptionPlan.objects.get(id=db_item_id)
            except TelcoSubscriptionPlan.DoesNotExist:
                menu_options = "something happend. please try it again"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # unsubscribe_payload = {
            #     "phone_number": db_instance.phone_number,
            #     "product_id": db_instance.product_id,
            # }

            # celery_deactivate_telco_subscription.apply_async(queue="telcoreq", kwargs=unsubscribe_payload)

            celery_deactivate_telco_subscription(phone_number=db_instance.phone_number, product_id=db_instance.product_id)

            menu_options = "Deactivation request sent."
            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # # ASK AI MENU #
        elif text == "6":
            menu_options = ask_ai_menu_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["text"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("6*"):
            if len(splited_text) > 2:
                splited_text[2:] = []

            splited_text[:2]

            if splited_text[-1].isdigit() is False:
                menu_options = "Invalid option selected"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            ask_ai_request_amount, ask_ai_request_is_on_demand = ask_ai_subscription_ticket_amount(splited_text[-1])

            if not isinstance(ask_ai_request_amount, int):
                menu_options = "Invalid option selected"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            ask_ai_price_and_service_details = TelcoAskAiPriceModel.get_subscription_ticket_price_details_with_stake_amount(ask_ai_request_amount)

            telco_charge_payload = {
                "phone_number": phone_number,
                "description": "ask ai lottery payment",
                "amount": ask_ai_price_and_service_details.get("ticket_price"),
                "game_play_id": "OO",
                "lottery_type": "ASK_AI",
                "pontential_winning": ask_ai_price_and_service_details.get("potential_winning"),
                "service_id": ask_ai_price_and_service_details.get("service_id"),
                "product_id": ask_ai_price_and_service_details.get("product_id"),
                "is_telco_subscription": ask_ai_request_is_on_demand,
                "use_json_format": True,
            }

            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={
                    "user_phone": phone_number,
                    "session_id": sender_cb,
                    "network": network,
                },
            )

            if settings.DEBUG is True:
                celery_telco_airtime_charge.delay(
                    **telco_charge_payload,
                )
            else:
                celery_telco_airtime_charge.apply_async(
                    queue="telcocharge1",
                    kwargs=telco_charge_payload,
                )

            menu_options = str(ask_ai_menu_item["summary"]).replace("END", "")
            payload["text"] = menu_options

            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # # MONETIZE AI MENU #
        elif text == "7":
            menu_options = monetize_ai_menu_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["text"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("7*"):
            if len(splited_text) > 2:
                splited_text[2:] = []

            splited_text[:2]

            if splited_text[-1].isdigit() is False:
                menu_options = "Invalid option selected"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            ask_ai_request_amount, ask_ai_request_is_on_demand = monetize_ai_subscription_ticket_amount(splited_text[-1])

            if not isinstance(ask_ai_request_amount, int):
                menu_options = "Invalid option selected"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            monetize_ai_price_and_service_details = TelcoMonetizeAiPriceModel.get_subscription_ticket_price_details_with_stake_amount(
                ask_ai_request_amount
            )

            telco_charge_payload = {
                "phone_number": phone_number,
                "description": "monetize ai lottery payment",
                "amount": monetize_ai_price_and_service_details.get("ticket_price"),
                "game_play_id": "OO",
                "lottery_type": "MONETIZE_AI",
                "pontential_winning": monetize_ai_price_and_service_details.get("potential_winning"),
                "service_id": monetize_ai_price_and_service_details.get("service_id"),
                "product_id": monetize_ai_price_and_service_details.get("product_id"),
                "is_telco_subscription": ask_ai_request_is_on_demand,
                "use_json_format": True,
            }

            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={
                    "user_phone": phone_number,
                    "session_id": sender_cb,
                    "network": network,
                },
            )

            if settings.DEBUG is True:
                celery_telco_airtime_charge.delay(
                    **telco_charge_payload,
                )
            else:
                celery_telco_airtime_charge.apply_async(
                    queue="telcocharge1",
                    kwargs=telco_charge_payload,
                )

            menu_options = str(monetize_ai_menu_item["summary"]).replace("END", "")
            payload["text"] = menu_options

            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # # TELCO LIBERTY LIFE MENU #
        elif text == "8":
            menu_options = liberty_life_menu_item[text]
            menu_options = menu_options.replace("CON", "")

            payload["text"] = menu_options

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        elif text.startswith("8*"):
            if len(splited_text) > 2:
                splited_text[2:] = []

            splited_text[:2]

            if splited_text[-1].isdigit() is False:
                menu_options = "Invalid option selected"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            ask_ai_request_amount, ask_ai_request_is_on_demand = liberty_life_subscription_ticket_amount(splited_text[-1])

            if not isinstance(ask_ai_request_amount, int):
                menu_options = "Invalid option selected"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            liberty_life_price_and_service_details = TelcoLibertyLifePriceModel.get_subscription_ticket_price_details_with_stake_amount(
                ask_ai_request_amount
            )

            telco_charge_payload = {
                "phone_number": phone_number,
                "description": "liberty life lottery payment",
                "amount": liberty_life_price_and_service_details.get("ticket_price"),
                "game_play_id": "OO",
                "lottery_type": "LIBERTY_LIFE",
                "pontential_winning": liberty_life_price_and_service_details.get("potential_winning"),
                "service_id": liberty_life_price_and_service_details.get("service_id"),
                "product_id": liberty_life_price_and_service_details.get("product_id"),
                "is_telco_subscription": ask_ai_request_is_on_demand,
                "use_json_format": True,
            }

            celery_update_telco_session.apply_async(
                queue="telco_session_logs",
                kwargs={
                    "user_phone": phone_number,
                    "session_id": sender_cb,
                    "network": network,
                },
            )

            if settings.DEBUG is True:
                celery_telco_airtime_charge.delay(
                    **telco_charge_payload,
                )
            else:
                celery_telco_airtime_charge.apply_async(
                    queue="telcocharge1",
                    kwargs=telco_charge_payload,
                )

            menu_options = str(liberty_life_menu_item["summary"]).replace("END", "")
            payload["text"] = menu_options

            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)

        # SCRATCH CARD USSD WITHDRAWAL
        elif str(text).strip().startswith("88"):
            if text == "88":
                response = "Welcome to your scratch card winning withdrawal\n"
                response += "Enter your card serial number\n"

                payload["text"] = response

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if len(splited_text) == 2:
                response = "Please enter the card pin and account number. e.g 74873,********\n"

                payload["text"] = response

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            # verify the seriali number and pin
            card_pin_and_account_number = str(splited_text[2]).split(",")
            serial_number = splited_text[1]
            card_pin = card_pin_and_account_number[0]

            ussd_scratch_card_helper = UssdScratchCardHelper()
            verify_scratch_status, verified_scratch_data = ussd_scratch_card_helper.verify_pin_and_serial_number(
                serial_number=serial_number, pin=card_pin
            )
            if verify_scratch_status is False:
                menu_options = verified_scratch_data
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if len(splited_text) == 3:
                response = f'Your winning amount is: {Utility.currency_formatter(verified_scratch_data.get("amount", 0))}\n'
                response += "1. Continue with your withdrawal"
                response += "2. Exit"
                response += "99. Back"

                payload["text"] = response

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if len(splited_text) == 4 and text[-1] == 2:
                menu_options = "Thank you."
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if len(splited_text) == 4:
                response = select_bank()

                menu_options = str(response).replace("CON", "")

                payload["text"] = menu_options

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if len(splited_text) == 5:
                # response = "Enter Account Number"

                # menu_options = response

                # payload["text"] = menu_options

                # if settings.DEBUG is True:
                #     celery_telco_ussd_backgroud.delay(**payload)
                # else:
                #     celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                bank = select_bank(splited_text[4])
                bank_db = BankManger().bank_details(bank_name=bank)

                if not bank_db:
                    menu_options = "Invalid bank"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                bank_code = bank_db.get("cbn_code")

                # serial_number = splited_text[1]
                # card_pin = splited_text[2]

                card_pin_and_account_number = str(splited_text[2]).split(",")
                serial_number = splited_text[1]
                card_pin = card_pin_and_account_number[0]
                account_number = card_pin_and_account_number[1]

                ussd_scratch_card_helper = UssdScratchCardHelper()
                try:
                    ussd_scratch_card_helper.claim_winning(
                        serial_number=serial_number,
                        pin=card_pin,
                        account_number=account_number,
                        bank_code=bank_code,
                        phone_number=phone,
                    )
                except Exception:
                    pass

                menu_options = "your request has been sent for processing"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        # SCRATCH CARD WINNING TICKET
        elif str(text).strip().startswith("47"):
            if text == "47":
                response = "Please, enter your ticket game id\n"

                payload["text"] = response

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)
            else:
                if len(splited_text) == 2:
                    response = "Please, enter your ticket pin\n"

                    payload["text"] = response

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                # validate game id and pin entered

                game_id = splited_text[1]
                pin = splited_text[2]

                try:
                    int(game_id)
                    int(pin)
                except Exception:
                    menu_options = "Invalid input. game pin or game id has to be a number"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                hashed_pin = f"password{pin}"
                # hashed_pin = hashlib.md5(hashed_pin.encode()).hexdigest()
                hashed_pin = str(int(pin))

                scratch_card_instance = ScratchCard.objects.filter(pin=hashed_pin, game_id=str(int(game_id)), paid=True).last()

                if scratch_card_instance is None:
                    payload["text"] = "Ticket not found."
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                if len(splited_text) == 3:
                    response = "Please, enter your ticket serial no.\n"

                    payload["text"] = response

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                if len(splited_text) == 4:
                    game_id = splited_text[1]
                    pin = splited_text[2]

                    try:
                        int(game_id)
                        int(pin)
                    except Exception:
                        menu_options = "Invalid input. game pin or game id has to be a number"
                        payload["text"] = menu_options
                        payload["ussdOpType"] = 4
                        payload["messageType"] = 2

                        if settings.DEBUG is True:
                            celery_telco_ussd_backgroud.delay(**payload)
                        else:
                            celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                        return telco_aggregator.ussd_response(send=False)

                    hashed_pin = f"password{pin}"
                    # hashed_pin = hashlib.md5(hashed_pin.encode()).hexdigest()
                    hashed_pin = str(int(pin))

                    serial_no = splited_text[3]
                    letters, numbers = ScratchCard.separate_letters_and_numbers(serial_no)
                    if "s" in letters.lower():
                        letters = "S"

                    if "q" in letters.lower():
                        letters = "Q"

                    numbers = int(numbers)

                    try:
                        scratch_card_instance = ScratchCard.objects.get(
                            pin=hashed_pin, game_id=int(game_id), paid=True, serial_skew__iexact=letters, index=numbers
                        )
                    except ScratchCard.DoesNotExist:
                        payload["text"] = "Invalid game id"
                        payload["ussdOpType"] = 4
                        payload["messageType"] = 2

                        if settings.DEBUG is True:
                            celery_telco_ussd_backgroud.delay(**payload)
                        else:
                            celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                        return telco_aggregator.ussd_response(send=False)

                    winning_status = "pending"

                    if str(scratch_card_instance.earning).upper() == "WON":
                        winning_status = "winning"

                    elif str(scratch_card_instance.earning).upper() == "LOST":
                        # winning_status = "lost"

                        if scratch_card_instance.winning_status is None:
                            winning_status = random.choices(
                                [
                                    "lost",
                                    "daily draw",
                                    "weekly draw",
                                    "weekly draw",
                                    "lost",
                                    "daily draw",
                                    "lost",
                                    "daily draw",
                                    "weekly draw",
                                    "lost",
                                ]
                            )
                            scratch_card_instance.winning_status = winning_status
                            scratch_card_instance.save()
                        else:
                            winning_status = scratch_card_instance.winning_status

                    if winning_status == "pending":
                        message = "please wait, this ticket has not been drawn yet"

                    elif winning_status == "winning":
                        if scratch_card_instance.player_phone_number is not None:
                            if scratch_card_instance.player_phone_number != phone_number:
                                message = "Sorry, this ticket has already been claimed."
                            else:
                                message = f"This is a winning ticket.\nAmount won: {scratch_card_instance.earning_amount}. Dial this code to withdraw your winnings *20144*89#"

                        else:
                            scratch_card_instance.player_phone_number = phone_number
                            scratch_card_instance.save()
                            message = f"This is a winning ticket.\nAmount won: {scratch_card_instance.earning_amount}. Dial this code to withdraw your winnings *20144*89#"
                    else:
                        message = "this's a lost ticket. sorry try again.!"

                    payload["text"] = message
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                else:
                    payload["text"] = "Invalid command."
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

        # NEW SCRATCH CARD USSD WITHDRAWAL
        elif str(text).strip().startswith("89"):
            print("splited_text", splited_text, "\n\n")
            if text == "89":
                response = "Welcome to your scratch card winning withdrawal\n"
                response += "Enter your card serial number\n"

                payload["text"] = response

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if len(splited_text) == 2:
                serial_number = splited_text[-1]

                letters, numbers = ScratchCard.separate_letters_and_numbers(serial_number)
                if "s" in letters.lower():
                    letters = "S"

                if "q" in letters.lower():
                    letters = "Q"

                numbers = int(numbers)

                try:
                    scratch_card_instance = ScratchCard.objects.get(
                        serial_skew__iexact=letters, index=numbers, player_phone_number=phone_number, claimed=False
                    )
                except ScratchCard.DoesNotExist:
                    payload["text"] = "Invalid serial number or no ticket found"
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                response = f"Your winning amount is: {Utility.currency_formatter(scratch_card_instance.earning_amount)}\n"
                response += "1. Continue with your withdrawal\n"
                response += "2. Exit\n"
                response += "99. Back"

                payload["text"] = response

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if len(splited_text) == 3:
                if splited_text[-1] == 2:
                    menu_options = "Thank you."
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                serial_number = splited_text[1]

                letters, numbers = ScratchCard.separate_letters_and_numbers(serial_number)
                if "s" in letters.lower():
                    letters = "S"

                if "q" in letters.lower():
                    letters = "Q"

                numbers = int(numbers)

                try:
                    scratch_card_instance = ScratchCard.objects.get(
                        serial_skew__iexact=letters, index=numbers, player_phone_number=phone_number, claimed=False
                    )
                except ScratchCard.DoesNotExist:
                    payload["text"] = "Invalid serial number or no ticket found"
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                response = select_bank()

                menu_options = str(response).replace("CON", "")

                payload["text"] = menu_options

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if len(splited_text) == 4:
                serial_number = splited_text[1]

                letters, numbers = ScratchCard.separate_letters_and_numbers(serial_number)
                if "s" in letters.lower():
                    letters = "S"

                if "q" in letters.lower():
                    letters = "Q"

                numbers = int(numbers)

                try:
                    scratch_card_instance = ScratchCard.objects.get(
                        serial_skew__iexact=letters, index=numbers, player_phone_number=phone_number, claimed=False
                    )
                except ScratchCard.DoesNotExist:
                    payload["text"] = "Invalid serial number or no ticket found"
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                bank = select_bank(splited_text[3])
                bank_db = BankManger().bank_details(bank_name=bank)

                if not bank_db:
                    menu_options = "Invalid bank"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                payload["text"] = "Enter your account number"

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

            if len(splited_text) == 5:
                serial_number = splited_text[1]

                letters, numbers = ScratchCard.separate_letters_and_numbers(serial_number)
                if "s" in letters.lower():
                    letters = "S"

                if "q" in letters.lower():
                    letters = "Q"

                numbers = int(numbers)

                try:
                    scratch_card_instance = ScratchCard.objects.get(
                        serial_skew__iexact=letters, index=numbers, player_phone_number=phone_number, claimed=False
                    )
                except ScratchCard.DoesNotExist:
                    payload["text"] = "Invalid serial number or no ticket found"
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                bank = select_bank(splited_text[3])
                bank_db = BankManger().bank_details(bank_name=bank)

                account_number = splited_text[4]

                if not bank_db:
                    menu_options = "Invalid bank"
                    payload["text"] = menu_options
                    payload["ussdOpType"] = 4
                    payload["messageType"] = 2

                    if settings.DEBUG is True:
                        celery_telco_ussd_backgroud.delay(**payload)
                    else:
                        celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                    return telco_aggregator.ussd_response(send=False)

                bank_code = bank_db.get("cbn_code")

                # serial_number = splited_text[1]
                # card_pin = splited_text[2]

                ussd_scratch_card_helper = UssdScratchCardHelper()
                try:
                    ussd_scratch_card_helper.claim_winning(
                        serial_number=f"{scratch_card_instance.serial_skew}{scratch_card_instance.serial_number}",
                        pin=scratch_card_instance.pin,
                        account_number=account_number,
                        bank_code=bank_code,
                        phone_number=phone_number,
                        use_new_withdrawal_flow=True,
                    )
                except Exception:
                    pass

                menu_options = "your request has been sent for processing"
                payload["text"] = menu_options
                payload["ussdOpType"] = 4
                payload["messageType"] = 2

                if settings.DEBUG is True:
                    celery_telco_ussd_backgroud.delay(**payload)
                else:
                    celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

                return telco_aggregator.ussd_response(send=False)

        else:
            print("texttexttexttexttexttext", text)
            menu_options = "Invalid option selected ."
            payload["text"] = menu_options
            payload["ussdOpType"] = 4
            payload["messageType"] = 2

            if settings.DEBUG is True:
                celery_telco_ussd_backgroud.delay(**payload)
            else:
                celery_telco_ussd_backgroud.apply_async(queue="telco_menu_worker", kwargs=payload)

            return telco_aggregator.ussd_response(send=False)
