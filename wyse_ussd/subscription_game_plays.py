import random
import time
import uuid
from datetime import datetime

from django.db.models import Min, Sum

from awoof_app.models import AwoofGameTable, LifeStyleTable
from broad_base_communication.bbc_helper import BBCTelcoAggregator
from main.api.api_lottery_helpers import generate_game_play_id
from main.models import (
    ConstantVariable,
    LotteryBatch,
    LotteryModel,
    LottoTicket,
    UserProfile,
)
from main.ussd.helpers import Utility
from overide_print import print
from sport_app.models import OddPredictionType, SoccerOddPredictionTable
from wallet_app.models import RtoWallet
from wyse_ussd.enums import PurposeChoices
from wyse_ussd.helper.general_helper import WyseCashLotteryPlayHelerp
from wyse_ussd.helper.nitroswitch_helper import nitroswitch_sms_gateway
from wyse_ussd.models import PendingAsyncTask
from wyse_ussd.tasks import (
    celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table,
)

# tasks = PendingAsyncTask.objects.filter(is_treated=False,purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,game_type__in=["FAST FINGERS", "WYSE CASH"])
# tasks
# <QuerySet []>
# tasks = PendingAsyncTask.objects.filter(is_treated=False,purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,game_type__in=["SALARY FOR LIFE"])
# tasks<QuerySet [<PendingAsyncTask: PendingAsyncTask object (9826215)>, <PendingAsyncTask: PendingAsyncTask object (9832300)>, <PendingAsyncTask: PendingAsyncTask object (9826226)>, <PendingAsyncTask: PendingAsyncTask object (9832302)>, <PendingAsyncTask: PendingAsyncTask object (9832303)>, <PendingAsyncTask: PendingAsyncTask object (9826260)>, <PendingAsyncTask: PendingAsyncTask object (9832304)>, <PendingAsyncTask: PendingAsyncTask object (9826284)>, <PendingAsyncTask: PendingAsyncTask object (9832306)>, <PendingAsyncTask: PendingAsyncTask object (9826111)>, <PendingAsyncTask: PendingAsyncTask object (9826191)>, <PendingAsyncTask: PendingAsyncTask object (9826296)>, <PendingAsyncTask: PendingAsyncTask object (9832307)>, <PendingAsyncTask: PendingAsyncTask object (9832308)>, <PendingAsyncTask: PendingAsyncTask object (9826153)>, <PendingAsyncTask: PendingAsyncTask object (9826305)>, <PendingAsyncTask: PendingAsyncTask object (9832309)>, <PendingAsyncTask: PendingAsyncTask object (9832311)>, <PendingAsyncTask: PendingAsyncTask object (9826320)>, <PendingAsyncTask: PendingAsyncTask object (9832312)>, '...(remaining elements truncated)...']>
# process_salary_for_life_batch(tasks)


def send_telco_subscription_game_play_confirmation(
    phone_number,
    game_play_id,
    lottery_type,
    awoof_item=None,
    is_a_new_subscription=True,
    network="MTN",
    product_id=None,
    ticket=None,
    is_a_free_trial=False,
):
    from wyse_ussd.models import TelcoSubscriptionPlan

    BBCTelcoAggregator()

    if network == "GLO":
        message_1 = f"EVERWAGE! Your entry no: {str(ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed! https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/"

        message_2 = f"EVERWAGE! Your entry no: {str(ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed! Join our salary for life game: visit https://www.s4lnigeria.com"

        msg = random.choice([message_1, message_2])
        nitroswitch_sms_gateway(phone_number=phone_number, message=msg, service_code=product_id)

        return

    if is_a_free_trial is True:
        messages = [
            "You've advanced to the Diamond Tier, unlocking exclusive rewards. Your dedication also earns you an entry in our Monthly Banger for additional bonus wins. Your journey shines bright with us. https://punchng.com/mtn-subscriber-wins-n12million-with-n100-in-mtn-winwise-salary4life-using-the-code-2014411/",
            "You've advanced to the Diamond Tier, unlocking exclusive rewards. Your dedication also earns you an entry in our Monthly Banger for additional bonus wins. Your journey shines bright with us. Join our salary for life game: visit https://www.s4lnigeria.com",
            "You've qualified for our Monthly Banger! This isn't just any reward; it's your ticket to our grandest prize yet. Stay subscribed, stay winning. https://punchng.com/mtn-subscriber-wins-n12million-with-n100-in-mtn-winwise-salary4life-using-the-code-2014411/",
            "You've qualified for our Monthly Banger! This isn't just any reward; it's your ticket to our grandest prize yet. Join our salary for life game: visit https://www.s4lnigeria.com",
            "Congrats, You've won N100 Cash Airtime!!! Stay subscribed for more wins https://punchng.com/mtn-subscriber-wins-n12million-with-n100-in-mtn-winwise-salary4life-using-the-code-2014411/",
            "Congrats, You've won N100 Cash Airtime!!! Join our salary for life game: visit https://www.s4lnigeria.com",
        ]
        msg = random.choice(messages)

        nitroswitch_sms_gateway(phone_number=phone_number, message=msg, service_code=product_id)
        return

    if lottery_type == "ASK_AI":
        payload = {"phone_number": phone_number}
        BBCTelcoAggregator().ask_ai_first_prompt_sms(**payload)

        return

    if lottery_type == "MONETIZE_AI":
        return

    if lottery_type == "LIBERTY_LIFE":
        return

    # Default message
    message_1 = f"Your {lottery_type} game play subscription was successful. Your game play id is {game_play_id}. Good luck! https://punchng.com/mtn-subscriber-wins-n12million-with-n100-in-mtn-winwise-salary4life-using-the-code-2014411/"
    message_2 = f"Your {lottery_type} game play subscription was successful. Your game play id is {game_play_id}. Join our salary for life game: visit https://www.s4lnigeria.com"

    msg = random.choice([message_1, message_2])

    website = "www.wisewinn.com"

    # Early return if no product_id
    if product_id is None:
        return send_sms(phone_number, msg)

    # Fetch active subscription plan
    active_subscription_plan = TelcoSubscriptionPlan.objects.filter(
        product_id=product_id, subscription_status="ACTIVE", phone_number=phone_number
    ).last()

    if active_subscription_plan is None:
        return send_sms(phone_number, message)

    # Update website based on network provider
    if active_subscription_plan.network_provider == "GLO":
        website = "www.glomegawin.com"

    # Prepare SMS payload
    sms_payload = {
        "phone_number": phone_number,
        "sender_name": "WINWISE",
    }

    # Handle messages based on lottery type
    if lottery_type == "INSTANT_CASH" or lottery_type == "INSTANT_CASHOUT":
        message = [
            f"INSTANT_CASHOUT: Successful Entry. Your lucky numbers: {str(ticket).replace(',', '-')}. Draw comes up in 5 minutes. You now qualify for up to N50k in the coming draw. Good luck! https://punchng.com/mtn-subscribers-now-have-the-chance-to-win-n12million-with-just-n50-by-dialing-201441-in-the-winwise-salary4life-2-0/",
            f"INSTANT_CASHOUT: Successful Entry. Your lucky numbers: {str(ticket).replace(',', '-')}. Draw comes up in 5 minutes. You now qualify for up to N50k in the coming draw. Join our salary for life game: visit https://www.s4lnigeria.com"
        ]
        message = random.choice(message)

    elif lottery_type == "SALARY FOR LIFE":
        message = [
            f"SALARY FOR LIFE! Your entry no: {str(ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Stay subscribed! https://punchng.com/mtn-subscriber-wins-n12million-with-n100-in-mtn-winwise-salary4life-using-the-code-2014411/",
            f"SALARY FOR LIFE! Your entry no: {str(ticket).replace(',', '-')}. Earn wages forever with daily cash prizes! Mega draw for N12m on the 15th of every month. Join our salary for life game: visit https://www.s4lnigeria.com"
        ]
    elif lottery_type == "FAST FINGERS":
        message = [
            f"{awoof_item} awaits in our Fast Finger game! You now qualify to win TODAY with ID: {game_play_id} in the next draw! Good luck! https://punchng.com/mtn-subscriber-wins-n12million-with-n100-in-mtn-winwise-salary4life-using-the-code-2014411/",
            f"{awoof_item} awaits in our Fast Finger game! You now qualify to win TODAY with ID: {game_play_id} in the next draw! Join our salary for life game: visit https://www.s4lnigeria.com"
        ]
    elif lottery_type == "WYSE CASH":
        message = [
            f"Congratulations, Win up to N50k today! WYSE CASH subscription was successful. Ticket id: {game_play_id}. Good luck!",
            f"Congratulations, Win up to N50k today! WYSE CASH subscription was successful. Ticket id: {game_play_id}. Join our salary for life game: visit https://www.s4lnigeria.com"
        ]

    # Define renewal messages for subscription plans
    renewal_messages = {
        1: f"Congrats, You have earned N10! and now in the Bronze Tier. Stay subscribed in next game for higher wins {website}!",
        2: f"Congrats, You have totally earned N{10 * active_subscription_plan.number_of_renewal}!. Stay subscribed in next game for higher wins {website}!",
        5: f"Surprise!, You've received N50 in your Bonus game wallet as a token of our appreciation. Use it for your next big win. Stay subscribed for more surprises {website}!",
        10: f"Congrats, You've won N100 Cash Airtime!!! Stay subscribed for more wins {website}!",
        11: "You've advanced to the Diamond Tier, unlocking exclusive rewards. Your dedication also earns you an entry in our Monthly Banger for additional bonus wins. Your journey shines bright with us.",
        15: "You've qualified for our Monthly Banger! This isn't just any reward; it's your ticket to our grandest prize yet. Stay subscribed, stay winning.",
        20: f"Congrats, You've won N100 Cash Airtime!!! Stay subscribed for more wins {website}!",
    }

    # Check for renewal messages
    if lottery_type != "SALARY FOR LIFE":
        renewal_number = active_subscription_plan.number_of_renewal
        if renewal_number in renewal_messages:
            message = renewal_messages[renewal_number]

    # Send SMS
    sms_payload["message"] = message
    return send_sms(phone_number, message, active_subscription_plan.network_provider)


def send_sms(phone_number, message, network_provider=None):
    broad_base_helper = BBCTelcoAggregator()

    sms_payload = {
        "phone_number": phone_number,
        # "sender_name": "WINWISE",
        "sender_name": "20144",
        "message": message,
    }

    if network_provider == "GLO":
        sms_payload["service_id"] = "0017182000003867"
        sms_payload["sender_name"] = "20144"

    sms_payload["use_json_format"] = True
    return broad_base_helper.bbc_send_sms(**sms_payload)


def wyse_cash_telco_subscription_game_play(amount, phone_number, is_a_new_subscription=True, network="MTN"):
    try:
        user_profile = UserProfile.objects.get(phone_number=phone_number)
    except UserProfile.DoesNotExist:
        user_profile = UserProfile.objects.create(phone_number=phone_number)

    current_batch = LotteryBatch.objects.filter(lottery_type="WYSE_CASH", is_active=True).last()
    if not current_batch:
        current_batch = LotteryBatch.objects.create(lottery_type="WYSE_CASH", is_active=True)

    get_game_play_id = generate_game_play_id()
    identity_id = f"{uuid.uuid4()}{int(time.time())}"

    unique_id = f"{current_batch.batch_uuid[0:5]}-{str(uuid.uuid4())[0:5]}"

    lucky_number = WyseCashLotteryPlayHelerp().generate_wyse_cash_lottery_ticket(phone_number)

    instance = LotteryModel.objects.create(
        user_profile=user_profile,
        batch=current_batch,
        band=10000,
        phone=phone_number,
        pool="TEN_THOUSAND",
        stake_amount=150,
        lucky_number=lucky_number,
        consent=True,
        unique_id=unique_id,
        instance_number=1,
        channel="USSD",
        expected_amount=150,
        game_play_id=get_game_play_id,
        lottery_type="WYSE_CASH",
        identity_id=identity_id,
        played_via_telco_channel=True,
        telco_network=network,
    )

    instance.paid = True
    instance.amount_paid = amount
    instance.save()

    PendingAsyncTask.objects.create(
        purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,
        phone_number=phone_number,
        game_play_id=get_game_play_id,
        game_type="WYSE CASH",
        is_a_new_subscription=is_a_new_subscription,
        network=network,
    )

    # send_telco_subscription_game_play_confirmation(
    #     phone_number,
    #     get_game_play_id,
    #     "WYSE CASH",
    #     is_a_new_subscription=is_a_new_subscription,
    #     network=network,
    # )


def instant_cashout_telco_subscription_game_play(amount, phone_number, is_a_new_subscription=True, network="MTN", product_id=None):
    print("instant_cashout_telco_subscription_game_play", "\n\n\n\n\n\n")
    try:
        user_profile = UserProfile.objects.get(phone_number=phone_number)
    except UserProfile.DoesNotExist:
        user_profile = UserProfile.objects.create(phone_number=phone_number)

    # try:
    #     current_batch = LotteryBatch.objects.filter(
    #         lottery_type="INSTANT_CASHOUT", is_active=True
    #     ).last()
    # except LotteryBatch.DoesNotExist:
    #     current_batch = LotteryBatch.objects.create(
    #         lottery_type="INSTANT_CASHOUT", is_active=True
    #     )

    current_batch = LotteryBatch.objects.filter(lottery_type="INSTANT_CASHOUT", is_active=True).last()
    if not current_batch:
        current_batch = LotteryBatch.objects.create(lottery_type="INSTANT_CASHOUT", is_active=True)

    game_play_id = generate_game_play_id()
    identity_id = f"{uuid.uuid4()}{int(time.time())}"

    lucky_number = Utility.generate_five_seperated_lucky_num()

    instance = LottoTicket.objects.create(
        user_profile=user_profile,
        batch=current_batch,
        phone=phone_number,
        stake_amount=200,
        potential_winning=11250,
        expected_amount=200,
        number_of_ticket=1,
        channel="USSD",
        game_play_id=game_play_id,
        lottery_type="INSTANT_CASHOUT",
        ticket=lucky_number,
        identity_id=identity_id,
        illusion=0,
        played_via_telco_channel=True,
        telco_network=network,
        product_id=product_id,
    )

    instance.paid = True
    instance.amount_paid = amount
    instance.save()

    PendingAsyncTask.objects.create(
        purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,
        game_play_id=game_play_id,
        game_type="INSTANT_CASHOUT",
        is_a_new_subscription=is_a_new_subscription,
        network=network,
        product_id=product_id,
        ticket=instance.ticket,
        phone_number=instance.phone,
    )

    # send_telco_subscription_game_play_confirmation(
    #     phone_number,
    #     game_play_id,
    #     "INSTANT_CASHOUT",
    #     is_a_new_subscription=is_a_new_subscription,
    #     network=network,
    #     product_id=product_id,
    #     ticket=instance.ticket,
    # )


def salary_for_life_telco_subscription_game_play(amount, phone_number, is_a_new_subscription=False, network="MTN", product_id=None):
    print("salary_for_life_telco_subscription_game_play", "\n\n\n\n\n\n")
    print("salary_for_life_telco_subscription_game_play", "\n\n\n\n\n\n")
    print("salary_for_life_telco_subscription_game_play", "\n\n\n\n\n\n")
    import time

    start_time = time.time()

    try:
        user_profile = UserProfile.objects.get(phone_number=phone_number)
        print(f"Line 300 init: {(time.time() - start_time)*1000:.2f}ms")
    except UserProfile.DoesNotExist:
        user_profile = UserProfile.objects.create(phone_number=phone_number)
        print(f"Line 303 init: {(time.time() - start_time)*1000:.2f}ms")
    try:
        current_batch = LotteryBatch.objects.get(lottery_type="SALARY_FOR_LIFE", is_active=True)
        print(f"Line 306 init: {(time.time() - start_time)*1000:.2f}ms")
    except LotteryBatch.DoesNotExist:
        current_batch = LotteryBatch.objects.create(lottery_type="SALARY_FOR_LIFE")
        print(f"Line 309 init: {(time.time() - start_time)*1000:.2f}ms")
    except LotteryBatch.MultipleObjectsReturned:
        LotteryBatch.objects.filter(lottery_type="SALARY_FOR_LIFE", is_active=True).update(is_active=False)
        current_batch = LotteryBatch.objects.create(lottery_type="SALARY_FOR_LIFE")
        print(f"Line 313 init: {(time.time() - start_time)*1000:.2f}ms")

    game_play_id = generate_game_play_id()
    identity_id = f"{uuid.uuid4()}{int(time.time())}"
    print(f"Line 318 init: {(time.time() - start_time)*1000:.2f}ms")

    if current_batch.list_of_ticket_numbers is None or current_batch.list_of_ticket_numbers == []:
        lucky_number = Utility.generate_five_seperated_lucky_num()
        print(f"Line 322 init: {(time.time() - start_time)*1000:.2f}ms")
    else:
        random_number = random.sample(current_batch.list_of_ticket_numbers, 5)
        lucky_number = ",".join([str(x) for x in random_number])
        print(f"Line 326 init: {(time.time() - start_time)*1000:.2f}ms")

    current_batch.save()
    print(f"Line 329 init: {(time.time() - start_time)*1000:.2f}ms")

    print("START CREATING  SALARY FOR LIFE TICKET FOR USSD CHANNELS")

    instance = LottoTicket.objects.create(
        user_profile=user_profile,
        batch=current_batch,
        phone=phone_number,
        stake_amount=200,
        potential_winning=5000,
        expected_amount=200,
        number_of_ticket=1,
        channel="USSD",
        game_play_id=game_play_id,
        lottery_type="SALARY_FOR_LIFE",
        ticket=lucky_number,
        identity_id=identity_id,
        illusion=0,
        played_via_telco_channel=True,
        telco_network=network,
        product_id=product_id,
    )

    instance.paid = True
    instance.amount_paid = amount
    instance.save()

    print("DONE CREATING  SALARY FOR LIFE TICKET FOR USSD CHANNELS")

    last_ticket_played = LottoTicket.objects.filter(phone=phone_number).last()

    print("AN ATTEMP TO CREATE SMS RECORD AND SEND SMS")
    PendingAsyncTask.objects.create(
        purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,
        phone_number=phone_number,
        game_play_id=game_play_id,
        game_type="SALARY FOR LIFE",
        is_a_new_subscription=is_a_new_subscription,
        network=network,
        product_id=product_id,
        ticket=last_ticket_played.ticket,
    )
    # try:
    #     send_telco_subscription_game_play_confirmation(
    #         phone_number,
    #         game_play_id,
    #         "SALARY FOR LIFE",
    #         is_a_new_subscription=is_a_new_subscription,
    #         network=network,
    #         product_id=product_id,
    #         ticket=last_ticket_played.ticket,
    #     )
    # except Exception:
    #     pass

    print("DONE AN ATTEMP TO CREATE SMS RECORD AND SEND SMS")

    return last_ticket_played


def awoof_telco_subscription_game_play(amount, phone_number, is_a_new_subscription=True, network="MTN", product_id=None, service_type="AWOOF"):
    items = LifeStyleTable.objects.filter(item_status="OPEN", is_active=True, is_drawn=False, service_type="AWOOF").last()

    if not items:
        return {"error": "No item available"}

    # calculate rtp and rto and return the value
    ussd_telco_commission_constant = ConstantVariable().get_telco_commission()
    ussd_telco_commission = ussd_telco_commission_constant
    ussd_telco_commission_value = round(amount * ussd_telco_commission_constant, 2)

    ussd_telco_aggregator_commission_constant = ConstantVariable().get_aggregator_commission()

    ussd_telco_aggregator_commission = amount * ussd_telco_aggregator_commission_constant

    amount_paid_after_removing_comission = amount - (ussd_telco_commission_value + ussd_telco_aggregator_commission)

    _rtp = (ConstantVariable().telco_rtp_perc()).get("rtp")

    rtp_per = float(_rtp) * 100

    rto_per = (100 - (float(_rtp) * 100)) / 100

    rto = round(amount_paid_after_removing_comission * rto_per, 2)

    amount_paid_after_removing_comission = amount_paid_after_removing_comission - rto

    rtp = amount_paid_after_removing_comission

    awoof_game_instance = AwoofGameTable().register_telco_ussd_awoof_game(
        awoof_item_db_id=items.id,
        user_selected_chances=rtp,
        user_phone_number=phone_number,
        band_number=1,
        from_telco_channel=True,
        rto=rto,
        rtp=rtp,
        rtp_per=rtp_per,
        ussd_telco_commission=ussd_telco_commission,
        ussd_telco_commission_value=ussd_telco_commission_value,
        ussd_telco_aggregator_commission=ussd_telco_aggregator_commission_constant,
        ussd_telco_aggregator_commission_value=ussd_telco_commission_value,
        service_type=items.service_type,
        network=network,
    )

    if awoof_game_instance is None:
        return

    awoof_game_instance.paid = True
    awoof_game_instance.amount_paid = amount
    awoof_game_instance.save()

    aggregated_awoof_running_balance = AwoofGameTable.objects.filter(item__id=items.id).aggregate(Sum("awoof_amount")).get("awoof_amount__sum", 0)

    celery_update_daily_game_draw_running_balance_on_daily_analtics_report_table.delay(
        awoof_running_balance=aggregated_awoof_running_balance,
    )

    RtoWallet().update_rto_wallet_amount(
        amount=rto,
        phone_number=phone_number,
        wallet_type="DEFAULT",
    )

    if service_type == "AWOOF":
        PendingAsyncTask.objects.create(
            purpose=PurposeChoices.GAME_SUBSCRIPTION_NOTIFICATION,
            phone_number=phone_number,
            game_play_id=awoof_game_instance.game_play_id,
            game_type="FAST FINGERS",
            is_a_new_subscription=is_a_new_subscription,
            awoof_item=awoof_game_instance.item.item_name,
            network=network,
            product_id=product_id,
        )

    # send_telco_subscription_game_play_confirmation(
    #     phone_number,
    #     awoof_game_instance.game_play_id,
    #     "FAST FINGERS",
    #     awoof_item=awoof_game_instance.item.item_name,
    #     is_a_new_subscription=is_a_new_subscription,
    #     network=network,
    #     product_id=product_id,
    # )


def soccer_cash_telco_subscription_game_play(amount, phone_number, is_a_new_subscription=True, network="MTN"):
    try:
        user_profile = UserProfile.objects.get(phone_number=phone_number)
    except UserProfile.DoesNotExist:
        user_profile = UserProfile.objects.create(phone_number=phone_number)

    # Get the instance with the lowest amount
    instance_with_lowest_amount = OddPredictionType.objects.filter(is_deleted=False).aggregate(Min("subscription_amount"))

    # Now, 'instance_with_lowest_amount' is a dictionary with the minimum subscription_amount
    # You can use this value to get the corresponding instance
    min_amount = instance_with_lowest_amount["subscription_amount__min"]

    # Get the instance with the lowest amount
    odd_instance = OddPredictionType.objects.filter(subscription_amount=min_amount, is_deleted=False).first()

    if odd_instance is not None:
        game_pay_id = generate_game_play_id()

        SoccerOddPredictionTable.objects.filter(
            user_profile=user_profile,
            phone_number=phone_number,
            amount=odd_instance.subscription_amount,
            is_active=True,
        ).update(is_active=False)

        SoccerOddPredictionTable.objects.create(
            user_profile=user_profile,
            phone_number=phone_number,
            subscription_type=odd_instance,
            amount=odd_instance.subscription_amount,
            consent=True,
            game_pay_id=game_pay_id,
            amount_paid=amount,
            played_from_telco_channel=True,
            paid_date=datetime.now(),
            is_active=True,
        )

        SoccerOddPredictionTable.send_prediction_to_telco_subscriber(
            phone_number=phone_number,
            is_a_new_subscription=is_a_new_subscription,
            network=network,
        )
