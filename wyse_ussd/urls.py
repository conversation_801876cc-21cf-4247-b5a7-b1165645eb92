from django.urls import include, path, re_path

from wyse_ussd.views import (
    CoralpayPaymentInitiation,
    CoralpayWebhook,
    GetActiveWithdrawalOTP,
    GetUserVerificationCode,
    LibertyPayUssdInteraction,
    NewBroadBaseDatasyncApiView,
    NitroSwitchDataSyncApiView,
    NitroSwitchSMSMOApiView,
    OpenAIPromptView,
    RedBillerCallbackView,
    RedoceanAfricaContentDeliveryDataSyncApiView,
    RedoceanAfricaFetchPredictWiseGamesApiView,
    RedoceanAfricaSubscriptionDataSyncApiView,
    SetRedoceanWebhookUrlsApiView,
    TelcoAggregatorJsonSmsNotificationApiview,
    TelcoAggregatorJsonUssdNotificationApiview,
    SecuredStGloDataSyncView,
    UssdSalaryForLifeGameShowParticipanInterestApiview,
    secure_d_datasync_view,
    # secure_d_st_glo_datasync_view,
    telco_aggregator_datasync_view,
    telco_aggregator_sms_notification_view,
    telco_aggregator_ussd_notification_view,
    telco_airtime_game_subscription_api_view,
    YellowDotAfricaDatasyncView, YellowDotAfricaUnsubscriptionView, YellowDotAfricaSmsMoDatasyncView,
    YellowDotAfricaSendSmsView, wyse_ussd_view,
)

GLO_URLS = [
    path("glo_web/", include("wyse_ussd.glo_web.urls")),
]

MTN_URLS = [
    path("mtn_web/", include("wyse_ussd.mtn_web.urls")),
]


REDOCEANAFRICAURLS = [
    path("redocean_africa_subscription_datasync/", RedoceanAfricaSubscriptionDataSyncApiView.as_view()),
    path("redocean_africa_content_delivery_datasync/", RedoceanAfricaContentDeliveryDataSyncApiView.as_view()),
    path("redocean_africa_fetch_predict_wise_games/", RedoceanAfricaFetchPredictWiseGamesApiView.as_view()),
    path("redocean_webhook_setup/", SetRedoceanWebhookUrlsApiView.as_view()),
]


YELLODOTAFRICAURLS = [
    path("yd-datasync/", YellowDotAfricaDatasyncView.as_view(), name="yellowdot-datasync"),
    path("yd-unsubscribe/", YellowDotAfricaUnsubscriptionView.as_view(), name="yellowdot-unsubscribe"),
    path("yd-sms-mo/", YellowDotAfricaSmsMoDatasyncView.as_view(), name="yellowdot-sms-mo"),
    path("send-sms/", YellowDotAfricaSendSmsView.as_view(), name="yellowdot-send-sms"),
]


urlpatterns = [
    path("wyse_ussd/", wyse_ussd_view, name="wyse_ussd_view"),
    path("coralpay_ussd_payment/", CoralpayPaymentInitiation.as_view(), name="coralpay_ussd_payment"),
    path("coralpay_webhook/", CoralpayWebhook.as_view()),
    path("redbiller_webhook/", RedBillerCallbackView.as_view()),
    # path("telco_aggregator/", TelcoAggregatorUssdNotification.as_view()),
    path("telco_aggregator/", telco_aggregator_ussd_notification_view),
    path("bbc_ussd_menu/", TelcoAggregatorJsonUssdNotificationApiview.as_view()),
    path("sms/telco_aggregator/", telco_aggregator_sms_notification_view),
    path("bbc/sms_callback/", TelcoAggregatorJsonSmsNotificationApiview.as_view()),
    path("bbc_datasync/", telco_aggregator_datasync_view),
    path("broad_base_datasync/", NewBroadBaseDatasyncApiView.as_view()),
    path("secure_d_datasync/", secure_d_datasync_view),
    path("secure_d_st_glo_datasync/", SecuredStGloDataSyncView.as_view()),
    path("secure_d_glo_st_datasync/", SecuredStGloDataSyncView.as_view()),
    path("get_user_verification_code/", GetUserVerificationCode.as_view()),
    path("get_withdrawal_otp/", GetActiveWithdrawalOTP.as_view()),
    path("airtime_game_subscription/", telco_airtime_game_subscription_api_view),
    path("nitroswitch_datasync/", NitroSwitchDataSyncApiView.as_view()),
    re_path("nitroswitch_sms_mo/", NitroSwitchSMSMOApiView.as_view()),
    path("open_ai_prompt/", OpenAIPromptView.as_view()),
    path("liberty_pay_ussd_interaction/", LibertyPayUssdInteraction.as_view()),
    path("ussd_salary_for_life_game_show_participant_interest/", UssdSalaryForLifeGameShowParticipanInterestApiview.as_view()),
    # GLO_URLS
    *GLO_URLS,
    # MTN_URLS
    *MTN_URLS,
    *REDOCEANAFRICAURLS,
    *YELLODOTAFRICAURLS
]





