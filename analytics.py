# import django

import pandas as pd

# if __name__ == '__main__':
#     os.environ.setdefault("DJANGO_SETTINGS_MODULE", "liberty_lotto.settings")
#     django.setup()


morbid_tracker_sheet = "MobidTracker-2024-07-04.xlsx"
golgen_goose_sheet = "notifications_cpa_advertiser_id_770_2024_07_04_10_00.xlsx"
secured_sheet = "SecureDTransaction-2024-07-04.xlsx"
not_in_morbid_sheet = "golden goose logs that are not in morbid.xlsx"

morbid_df = pd.read_excel(morbid_tracker_sheet)
golden_df = pd.read_excel(golgen_goose_sheet)
secured_df = pd.read_excel(secured_sheet)
not_in_morbid_df = pd.read_excel(not_in_morbid_sheet)


morbid_subscribers_list = morbid_df["click_id"].values


golden_subscribers_list = golden_df["Click Id"].values
secured_subscribers_list = secured_df["reference"].values


golden_goose_click_ids_in_morbid_list = []
golden_goose_click_ids_not_in_morbid_list = []
golden_goose_click_ids_not_in_morbid_and_not_in_secured_list = []
golden_goose_click_ids_not_in_morbid_but_in_secured_list = []


# print(golden_df)
for i, row in not_in_morbid_df.iterrows():
    print("running")
    click_id = row["click_id"]

    duplicates_count = list(golden_subscribers_list).count(click_id)
    data = {
        # "phone_number": row["phone_number"],
        "click_id": row["click_id"],
        "source": "GOLDEN GOOSE",
        "duplicates": str(duplicates_count),
        "is_duplicate": True if duplicates_count > 1 else False,
    }

    if click_id in secured_subscribers_list:
        golden_goose_click_ids_not_in_morbid_but_in_secured_list.append(data)
    else:
        golden_goose_click_ids_not_in_morbid_and_not_in_secured_list.append(data)

df = pd.DataFrame()

in_secured_sheet = df.from_dict(golden_goose_click_ids_not_in_morbid_but_in_secured_list)
not_in_secured_sheet = df.from_dict(golden_goose_click_ids_not_in_morbid_and_not_in_secured_list)

in_secured_sheet.to_excel("Not in MorbidTracker but in SecureD.xlsx")
not_in_secured_sheet.to_excel("Not in Morbid and Not in SecureD.xlsx")


# print(":::::::::::::::In sheet::::::::::::")
# print(golden_goose_click_ids_in_morbid_list)
# print(":::::::::::::::In sheet::::::::::::")


# morbid_tracker_sheet = "MobidTracker April 29.xlsx"
# lotto_ticket_sheet = "LottoTicket April 29.xlsx"

# morbid_df = pd.read_excel(morbid_tracker_sheet)
# ticket_df = pd.read_excel(lotto_ticket_sheet)

# ticket_subscribers_list = ticket_df["phone"].values


# for i, row in morbid_df.iterrows():
#     phone_number = row["phone_number"]

#     if phone_number in ticket_subscribers_list:
#         renewals_count = list(ticket_subscribers_list).count(phone_number)

#         data = {
#             "phone_number": row["phone_number"],
#             "click_id": row["click_id"],
#             "source": row["source"],
#             "renewal_count": renewals_count
#             }
