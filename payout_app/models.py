from django.db import models

# import uuid


# Create your models here.
class Transaction(models.Model):
    """
    This class represents the Transaction model
    """

    phone = models.CharField(max_length=20)
    reference = models.CharField(max_length=255, unique=True)
    transaction_unique_reference = models.Char<PERSON>ield(max_length=255, unique=True, null=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=255, default="processing")
    account_name = models.CharField(max_length=255)
    account_number = models.Char<PERSON>ield(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_disbursed = models.BooleanField(default=False)
    disbursement_table = models.CharField(max_length=150, null=True)

    payload = models.JSONField()

    def __str__(self):
        return self.reference

    class Meta:
        db_table = "transaction"
        verbose_name = "Transaction"
        verbose_name_plural = "Transactions"
