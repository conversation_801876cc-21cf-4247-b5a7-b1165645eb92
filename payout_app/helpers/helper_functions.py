import uuid

from main.models import UserProfile
from main.ussd.bankdb import filter_bank
from payout_app.helpers.woven_gateway import WovenHelper
from payout_app.models import Transaction


def create_transaction(**kwargs):
    """
    This method creates a new transaction
    """
    phone = kwargs.get("phone")
    user_profile = UserProfile.objects.filter(phone_number=phone).last()
    disbursement_table_insatnce = kwargs.get("disbursement_table_insatnce")

    account_name = user_profile.account_name
    bank_code = user_profile.bank_code
    ref = uuid.uuid1()

    reference = f"LOTT-{ref}-{disbursement_table_insatnce.id}"
    fetch_bank = filter_bank(cbn_code=bank_code)
    amounnt = kwargs.get("amount")

    if fetch_bank is None:
        # notify the winner to select a valid bank
        pass
    else:
        transaction_instance = Transaction.objects.create(
            reference=reference,
            amount=amounnt,
            account_name=account_name,
            account_number=kwargs.get("account_number"),
            disbursement_table_id=kwargs.get("disbursement_table_insatnce"),
            payload=kwargs,
        )

        payout_payload = {
            "account_name": account_name,
            "beneficiary_nuban": phone,
            "beneficiary_bank_code": fetch_bank.bank_code,
            "reference": reference,
            "amount": amounnt,
        }
        woven_ = WovenHelper()
        disbursement_response = woven_.initaite_payout_to_winners(**payout_payload)

        if isinstance(disbursement_response, dict) and disbursement_response["data"]:
            transaction_instance.transaction_unique_reference = disbursement_response["data"]["unique_reference"]
            transaction_instance.save()
