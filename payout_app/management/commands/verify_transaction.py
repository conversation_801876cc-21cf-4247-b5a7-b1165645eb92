from django.core.management.base import BaseCommand

from payout_app.helpers.woven_gateway import <PERSON><PERSON>n<PERSON><PERSON><PERSON>
from payout_app.models import Transaction


class Command(BaseCommand):
    help = "HANDLES TRANSACTION VERIFICATIONS"

    def handle(self, *args, **kwargs):
        transaction = Transaction.objects.filter(is_disbursed=False, status="processing")
        wovenv_ = WovenHelper()
        for trans in transaction:
            if trans.transaction_unique_reference is None:
                # call woven unique ref endpoint to update this transaction endpoint
                data = wovenv_.get_transaction_with_ref(trans.reference)
                if isinstance(data, dict) and data["data"]["payout_transactions"]:
                    trans.transaction_unique_reference = data["data"]["payout_transactions"][0]["unique_reference"]
                    trans.save()

            else:
                verify_response = wovenv_.woven_payment_verification(trans.transaction_unique_reference)

                if isinstance(verify_response, dict) and verify_response["data"]["transactions"]:
                    payout_status = verify_response.get("data").get("transactions")[0].get("status")

                    if payout_status == "ACTIVE" or payout_status.casefold() == "active":
                        trans.is_disbursed = True
                        trans.status = "Successful"
                        trans.save()

                        # update disbursementtable
                        trans.disbursement_table.is_disbursed = True
                        trans.save()

                    elif payout_status == "FAILED" or payout_status.casefold() == "failed":
                        trans.status = "FAILED"
                        trans.save()
