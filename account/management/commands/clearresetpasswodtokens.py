import datetime

from django.core.management.base import BaseCommand
from django.utils import timezone

from account.helpers import clear_expired_resetpassword_token


class Command(BaseCommand):
    help = "Can be run as a cronjob or directly to clean out expired tokens"

    def handle(self, *args, **options):
        # datetime.now minus expiry hours
        now_minus_expiry_time = timezone.now() - datetime.timedelta(hours=24)
        clear_expired_resetpassword_token(now_minus_expiry_time)
