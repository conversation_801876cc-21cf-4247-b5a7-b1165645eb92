from django.core.management.base import BaseCommand

# from account.models import User
# from pos_app.models import Agent
from web_app.views import verify_bvn_with_uverify


class Command(BaseCommand):
    help = "Can be run as a cronjob or directly to clean out expired tokens"

    def handle(self, *args, **options):
        # agents = Agent.objects.all()
        # emails = [agent.email for agent in agents]
        # phone = [agent.phone for agent in agents]
        # User.objects.filter(email__in=emails).update(channel="APP/POS")
        # User.objects.filter(phone__in=phone).update(channel="APP/POS")
        test = verify_bvn_with_uverify(bvn_number="***********")
        print(test)
