from django.conf import settings
from django.contrib.admin.models import CHANGE, LogEntry
from django.contrib.auth.hashers import make_password
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ObjectDoesNotExist
from django.test import Client
from google.auth.transport import requests
from google.oauth2 import id_token
from rest_framework.authtoken.models import Token
from rest_framework.exceptions import AuthenticationFailed

from account.models import ResetPasswordToken, User
from main.models import UserProfile
from wallet_app.models import UserWallet

WEB_CLIENT = Client(HTTP_USER_AGENT="Social-Manager-Bot")


def get_password_reset_token_expiry_time():
    """
    Returns the password reset token expirty time in hours (default: 24)
    Set Django SETTINGS.DJANGO_REST_MULTITOKENAUTH_RESET_TOKEN_EXPIRY_TIME to overwrite this time
    :return: expiry time
    """
    # get token validation time
    return getattr(settings, "RESET_PASSWORD_TOKEN_EXPIRY_TIME", 24)


def clear_expired_resetpassword_token(expiry_time):
    """
    Remove all expired tokens
    :param expiry_time: Token expiration time
    """
    ResetPasswordToken.objects.filter(created_at__lte=expiry_time).delete()


class SocialAuthManager:
    """
    This manager class helps to validate and register
    new users' based on their social profile.
    """

    @staticmethod
    def validate_google_user(user_id_token):
        """
        This method Queries the Google OAuth2 API
        to fetch the authenticated user information.
        """
        try:
            profile = id_token.verify_oauth2_token(user_id_token, requests.Request())

            if "accounts.google.com" in profile["iss"]:
                # print(
                #     f"""

                #       Google profile: {profile}, \n\n\n

                #       """
                # )
                return {"status": True, "details": profile}
            else:
                raise AuthenticationFailed(detail="OAuth2 issuer is unknown")
        except Exception:
            return {
                "status": False,
                "message": "The token is either invalid or expired. Please, login again.",
            }

    @staticmethod
    def log_social_user(first_name, last_name, email, token):
        """
        This method provides a token to an authenticated social user,
        else registers user and logs them in
        """
        get_user_by_email = User.objects.filter(email=email).last()

        if get_user_by_email is not None:
            Token.objects.filter(user=get_user_by_email).delete()
            token, _ = Token.objects.get_or_create(user=get_user_by_email)
            return {"token": token.key}
        else:
            phone = User().sudo_phone_number()
            user = User.objects.create(
                first_name=first_name,
                last_name=last_name,
                email=email,
                password=make_password(f"social-authauth--split--point{token}"),
                phone=phone,
            )

            user_profile = UserProfile.objects.create(
                phone_number=phone,
                email=email,
                first_name=first_name,
                last_name=last_name,
                has_sudo_phone_number=True,
            )

            # # CREDIT USER WALLET WITH BONUS
            # bonu_amount = ConstantVariable.get_constant_variable().get("bonus_amount")
            # UserWallet().fund_user_wallet_play_wallet_via_phone(user.phone, bonu_amount)

            UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

            # password = f"social-authauth--split--point{token}"
            # login_details = {"email": email, "password": password}
            # new_user = WEB_CLIENT.post(
            #     path=reverse("email-login"),
            #     data=login_details,
            #     content_type="application/json",
            # )

            # print(
            #     f"""
            #       path=reverse("email-login"),: {reverse("email-login")}
            #       data=login_details: {login_details}
            #       new_user via google: {new_user}, \n\n\n

            #       """
            # )

            token, _ = Token.objects.get_or_create(user=user)
            return {"token": token.key}
            # return new_user.data


def log_save_operation(user, object_instance):
    try:
        # Get the content type of the object being saved
        content_type = ContentType.objects.get_for_model(object_instance)

        # Create a log entry for the save operation
        LogEntry.objects.log_action(
            user_id=user.id,
            content_type_id=content_type.id,
            object_id=object_instance.id,
            object_repr=str(object_instance),
            action_flag=CHANGE,
            change_message="Overwrote user wallet",
        )
    except ObjectDoesNotExist:
        # Handle any exceptions or errors appropriately
        pass
