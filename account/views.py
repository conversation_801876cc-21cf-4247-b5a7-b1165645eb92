import random

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from account.models import TelcosPhoneNumber
from account.serializers import TelcosPhoneNumberSerializer

# class TelcosPhoneNumberCreate(APIView):
#     serializer_class = TelcosPhoneNumberSerializer

#     def post(self, request, format=None):
#         serializer = TelcosPhoneNumberSerializer(data=request.data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TelcosPhoneNumberCreate(APIView):
    serializer_class = TelcosPhoneNumberSerializer

    def post(self, request, format=None):
        print("news")
        print("news2")
        random_entry = TelcosPhoneNumber.objects.filter(id=random.choice(range(600000, 900000)))
        print("news3")
        random_entry.last()
        print("news4")
        TelcosPhoneNumber.objects.all().count()
        print("news5")
        TelcosPhoneNumber.objects.all().count()
        TelcosPhoneNumber.objects.all().count()
        TelcosPhoneNumber.objects.all().count()
        TelcosPhoneNumber.objects.all().count()
        TelcosPhoneNumber.objects.all().count()
        TelcosPhoneNumber.objects.all().count()

        print("news")

        random_entry

        serializer = TelcosPhoneNumberSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()

            serializer.data["data"] = random_entry.last().phone_number

            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
