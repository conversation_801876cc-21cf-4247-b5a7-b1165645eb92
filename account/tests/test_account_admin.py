import pytest
from django.contrib import admin
from django.contrib.admin.sites import AdminSite

from account.admin import (
    BlackListedModelAdmin,
    OtpModelAdmin,
    ResetPasswordTokenModelAdmin,
    TelcosPhoneNumberModelAdmin,
    UserModelAdmin,
    UserProfileOtpResourceAdmin,
)
from account.models import (
    BlackListed,
    Otp,
    ResetPasswordToken,
    TelcosPhoneNumber,
    User,
    UserProfileOtp,
)


class MockRequest:
    pass


@pytest.fixture
def mock_request():
    return MockRequest()


# Test admin site registration
def test_admin_registrations():
    # Get all registered models
    registered_models = list(admin.site._registry.keys())

    # Check that our models are registered
    assert User in registered_models
    assert Otp in registered_models
    assert ResetPasswordToken in registered_models
    assert BlackListed in registered_models
    assert UserProfileOtp in registered_models
    assert TelcosPhoneNumber in registered_models


# Test admin classes
@pytest.mark.parametrize(
    "model_admin_class,model_class",
    [
        (UserModelAdmin, User),
        (OtpModelAdmin, Otp),
        (ResetPasswordTokenModelAdmin, ResetPasswordToken),
        (BlackListedModelAdmin, BlackListed),
        (UserProfileOtpResourceAdmin, UserProfileOtp),
        (TelcosPhoneNumberModelAdmin, TelcosPhoneNumber),
    ],
)
def test_admin_classes(model_admin_class, model_class, mock_request):
    site = AdminSite()
    model_admin = model_admin_class(model_class, site)

    # Check that it's an ImportExportModelAdmin
    # assert isinstance(model_admin, ImportExportModelAdmin)

    # Test get_list_display
    list_display = model_admin.get_list_display(mock_request)
    assert isinstance(list_display, list)
    assert len(list_display) > 0

    # Check field names are valid for the model
    model_fields = [field.name for field in model_class._meta.concrete_fields]
    for field in list_display:
        if field != "password":  # Password is specifically removed in UserModelAdmin
            assert field in model_fields


# Test UserModelAdmin specifically
def test_user_model_admin(mock_request):
    site = AdminSite()
    model_admin = UserModelAdmin(User, site)

    # Check list_filter
    assert model_admin.list_filter == ("channel", "created_at", "is_staff", "is_active", "is_superuser")

    # Check search_fields
    assert model_admin.search_fields == ("username", "email", "phone")

    # Check that password is removed from list_display
    list_display = model_admin.get_list_display(mock_request)
    assert "password" not in list_display

    # Check the other expected fields are present
    expected_fields = [field.name for field in User._meta.concrete_fields if field.name != "password"]
    for field in expected_fields:
        assert field in list_display


# Test TelcosPhoneNumberModelAdmin specifically
def test_telcos_phone_number_model_admin():
    site = AdminSite()
    model_admin = TelcosPhoneNumberModelAdmin(TelcosPhoneNumber, site)

    # Check list_filter
    assert model_admin.list_filter == (
        "network",
        "created_at",
        "state",
        "lga",
        "gender",
    )

    # Check search_fields
    assert model_admin.search_fields == (
        "first_name",
        "last_name",
        "phone_number",
        "address",
    )

    # Check date_hierarchy
    assert model_admin.date_hierarchy == "created_at"


# Test OtpModelAdmin specifically
def test_otp_model_admin():
    site = AdminSite()
    model_admin = OtpModelAdmin(Otp, site)

    # Check search_fields
    assert model_admin.search_fields == ("user__phone", "user__email", "phone")

    # Check ordering
    assert model_admin.ordering == ("-created_at", "user", "otp", "is_verified", "otp_type")
