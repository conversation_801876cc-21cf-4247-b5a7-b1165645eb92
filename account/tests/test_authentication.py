# import base64
# from datetime import timedelta
# from unittest import mock

# import pytest
# from django.conf import settings
# from django.test import RequestFactory, TestCase
# from django.utils import timezone
# from rest_framework.exceptions import AuthenticationFailed
# from rest_framework.test import APIRequestFactory

# from account.authentication import (
#     AgentAuthenticationBackend,
#     CustomBasicAuthentication,
#     EmailBackend,
#     ExpiringTokenAuthentication,
#     IsAgentRemittanceDuePermission,
#     IsBlackListedPermission,
#     PhoneBackend,
#     PhoneNumberVerifedPermission,
#     SuperUserPermission,
#     get_authorization_header,
# )
# from account.models import BlackListed, Token, User
# from main.models import LotteryModel
# from pos_app.models import Agent, AgentConstantVariables, LottoAgentRemittanceTable


# class GetAuthorizationHeaderTest(TestCase):
#     def test_get_authorization_header_with_string(self):
#         factory = RequestFactory()
#         auth_header = "Bearer token123"
#         request = factory.get("/", HTTP_AUTHORIZATION=auth_header)

#         result = get_authorization_header(request)
#         self.assertEqual(result, auth_header.encode("utf-8"))

#     def test_get_authorization_header_with_bytes(self):
#         factory = RequestFactory()
#         auth_header = b"Bearer token123"
#         request = factory.get("/", HTTP_AUTHORIZATION=auth_header)

#         result = get_authorization_header(request)
#         self.assertEqual(result, auth_header)


# class ExpiringTokenAuthenticationTest(TestCase):
#     def setUp(self):
#         self.user = User.objects.create_user(username="testuser", email="<EMAIL>", password="password123")
#         self.token = Token.objects.create(user=self.user)
#         self.auth = ExpiringTokenAuthentication()

#     def test_authenticate_valid_token(self):
#         request = RequestFactory().get("/")
#         user, token = self.auth.authenticate_credentials(self.token.key, request)

#         self.assertEqual(user, self.user)
#         self.assertEqual(token, self.token)

#     def test_authenticate_invalid_token(self):
#         request = RequestFactory().get("/")

#         with self.assertRaises(AuthenticationFailed):
#             self.auth.authenticate_credentials("invalid_token", request)

#     def test_authenticate_inactive_user(self):
#         request = RequestFactory().get("/")
#         self.user.is_active = False
#         self.user.save()

#         with self.assertRaises(AuthenticationFailed):
#             self.auth.authenticate_credentials(self.token.key, request)

#     def test_authenticate_expired_token(self):
#         request = RequestFactory().get("/")
#         # Set token created time to before the expiration window
#         self.token.created = timezone.now() - settings.TOKEN_TTL - timedelta(hours=1)
#         self.token.save()

#         with self.assertRaises(AuthenticationFailed):
#             self.auth.authenticate_credentials(self.token.key, request)


# class EmailBackendTest(TestCase):
#     def setUp(self):
#         self.backend = EmailBackend()
#         self.user = User.objects.create_user(username="testuser", email="<EMAIL>", phone="2348123456789", password="password123")

#     def test_authenticate_with_email(self):
#         user = self.backend.authenticate(None, email="<EMAIL>", password="password123")
#         self.assertEqual(user, self.user)

#     def test_authenticate_with_email_wrong_password(self):
#         with self.assertRaises(Exception) as context:
#             self.backend.authenticate(None, email="<EMAIL>", password="wrongpassword")

#         self.assertEqual(str(context.exception), "Invalid Credentials")

#     def test_authenticate_with_nonexistent_email(self):
#         user = self.backend.authenticate(None, email="<EMAIL>", password="password123")
#         self.assertIsNone(user)

#     def test_authenticate_with_phone(self):
#         # Mock the format_number_from_back_add_234 function
#         with mock.patch.object(LotteryModel, "format_number_from_back_add_234", return_value="2348123456789"):
#             user = self.backend.authenticate(None, phone="08123456789", password="password123")
#             self.assertEqual(user, self.user)

#     def test_get_user_valid_id(self):
#         user = self.backend.get_user(self.user.id)
#         self.assertEqual(user, self.user)

#     def test_get_user_invalid_id(self):
#         user = self.backend.get_user(999)  # Non-existent ID
#         self.assertIsNone(user)


# class PhoneBackendTest(TestCase):
#     def setUp(self):
#         self.backend = PhoneBackend()
#         self.user = User.objects.create_user(username="testuser", email="<EMAIL>", phone="2348123456789", password="password123")

#     def test_authenticate_with_phone(self):
#         user = self.backend.authenticate(None, phone="2348123456789", password="password123")
#         self.assertEqual(user, self.user)

#     def test_authenticate_with_wrong_password(self):
#         with self.assertRaises(Exception) as context:
#             self.backend.authenticate(None, phone="2348123456789", password="wrongpassword")

#         self.assertEqual(str(context.exception), "Invalid Credentials")

#     def test_authenticate_with_nonexistent_phone(self):
#         user = self.backend.authenticate(None, phone="2347000000000", password="password123")
#         self.assertIsNone(user)


# @pytest.mark.django_db
# class TestCustomBasicAuthentication:
#     def setup_method(self):
#         self.auth = CustomBasicAuthentication()
#         self.factory = APIRequestFactory()

#         # Mock settings
#         self.original_username = settings.CORAL_PAY_AUTH_USERNAME
#         self.original_password = settings.CORAL_PAY_AUTH_PASSWORD
#         settings.CORAL_PAY_AUTH_USERNAME = "coraluser"
#         settings.CORAL_PAY_AUTH_PASSWORD = "coralpass"

#     def teardown_method(self):
#         # Restore settings
#         settings.CORAL_PAY_AUTH_USERNAME = self.original_username
#         settings.CORAL_PAY_AUTH_PASSWORD = self.original_password

#     def test_authenticate_valid_credentials(self):
#         # Create valid auth header
#         auth_string = f"coraluser:coralpass"
#         auth_header = f"Basic {base64.b64encode(auth_string.encode()).decode()}"
#         request = self.factory.get("/", HTTP_AUTHORIZATION=auth_header)

#         result = self.auth.authenticate(request)
#         assert result is None  # Valid authentication returns None for this class

#     def test_authenticate_invalid_username(self):
#         auth_string = f"wronguser:coralpass"
#         auth_header = f"Basic {base64.b64encode(auth_string.encode()).decode()}"
#         request = self.factory.get("/", HTTP_AUTHORIZATION=auth_header)

#         with pytest.raises(AuthenticationFailed):
#             self.auth.authenticate(request)

#     def test_authenticate_invalid_password(self):
#         auth_string = f"coraluser:wrongpass"
#         auth_header = f"Basic {base64.b64encode(auth_string.encode()).decode()}"
#         request = self.factory.get("/", HTTP_AUTHORIZATION=auth_header)

#         with pytest.raises(AuthenticationFailed):
#             self.auth.authenticate(request)


# @pytest.mark.django_db
# class TestAgentAuthenticationBackend:
#     def setup_method(self):
#         self.backend = AgentAuthenticationBackend()
#         self.user = User.objects.create_user(username="agentuser", email="<EMAIL>", phone="2348123456789", password="agentpass")
#         self.agent = Agent.objects.create(user_uuid="agent-uuid-123", phone="2348123456789", name="Agent Test")

#     def test_authenticate_valid_credentials(self):
#         user = self.backend.authenticate(None, user_uuid="agent-uuid-123", password="agentpass")
#         assert user == self.user

#     def test_authenticate_invalid_uuid(self):
#         with pytest.raises(Exception) as excinfo:
#             self.backend.authenticate(None, user_uuid="invalid-uuid", password="agentpass")
#         assert "Invalid Credentials" in str(excinfo.value)

#     def test_authenticate_wrong_password(self):
#         with pytest.raises(Exception) as excinfo:
#             self.backend.authenticate(None, user_uuid="agent-uuid-123", password="wrongpass")
#         assert "Invalid Credentials" in str(excinfo.value)


# @pytest.mark.django_db
# class TestPermissions:
#     def setup_method(self):
#         self.factory = APIRequestFactory()
#         self.user = User.objects.create_user(
#             username="permissionuser", email="<EMAIL>", phone="2348123456789", password="userpass", phone_is_verified=True
#         )

#     def test_phone_number_verified_permission(self):
#         request = self.factory.get("/")
#         request.user = self.user
#         permission = PhoneNumberVerifedPermission()

#         # Verified phone
#         assert permission.has_permission(request, None) is True

#         # Unverified phone
#         self.user.phone_is_verified = False
#         with pytest.raises(AuthenticationFailed):
#             permission.has_permission(request, None)

#     @mock.patch.object(BlackListed, "is_blacklisted")
#     def test_is_blacklisted_permission(self, mock_is_blacklisted):
#         request = self.factory.get("/")
#         request.user = self.user
#         permission = IsBlackListedPermission()

#         # Not blacklisted
#         mock_is_blacklisted.return_value = False
#         assert permission.has_permission(request, None) is True

#         # Blacklisted
#         mock_is_blacklisted.return_value = True
#         with pytest.raises(AuthenticationFailed):
#             permission.has_permission(request, None)

#     def test_superuser_permission(self):
#         request = self.factory.get("/")
#         request.user = self.user
#         permission = SuperUserPermission()

#         # Not superuser
#         with pytest.raises(AuthenticationFailed):
#             permission.has_permission(request, None)

#         # Superuser
#         self.user.is_superuser = True
#         assert permission.has_permission(request, None) is True

#     @mock.patch.object(AgentConstantVariables, "get_unable_to_remit_but_allow_to_access_lotto")
#     @mock.patch.object(LottoAgentRemittanceTable, "is_remittance_due")
#     def test_agent_remittance_due_permission(self, mock_is_due, mock_allow):
#         request = self.factory.get("/")
#         request.user = self.user
#         permission = IsAgentRemittanceDuePermission()
#         agent = Agent.objects.create(phone=self.user.phone, name="Test Agent")

#         # No due remittance
#         mock_is_due.return_value = {"status": False, "amount": 0}
#         assert permission.has_permission(request, None) is True

#         # Due remittance but allowed to access
#         mock_is_due.return_value = {"status": True, "amount": 1000}
#         mock_allow.return_value = True
#         assert permission.has_permission(request, None) is True

#         # Due remittance and not allowed to access
#         mock_allow.return_value = False
#         with pytest.raises(AuthenticationFailed):
#             permission.has_permission(request, None)
