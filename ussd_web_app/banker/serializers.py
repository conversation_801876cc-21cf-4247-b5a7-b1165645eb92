from django.template.defaultfilters import date
from rest_framework import serializers

from main.helpers.helper_functions import mask_winners_phone_number
from main.models import LottoTicket, LottoWinners


class FetchBankerGamePlayersForUssdWebAppSerializer(serializers.ModelSerializer):
    class Meta:
        model = LottoWinners
        fields = [
            "id",
            "lottery",
        ]

    def to_representation(self, obj):
        serialized_data = super(FetchBankerGamePlayersForUssdWebAppSerializer, self).to_representation(obj)
        serialized_data["phone"] = mask_winners_phone_number(obj.lottery.user_profile.phone_number)
        serialized_data["date"] = date(obj.date_won, "d/m/y, h:i A")

        del serialized_data["lottery"]

        return serialized_data


class BankerPlayersSerializer(serializers.Serializer):
    class Meta:
        model = LottoTicket
        fields = [
            "id",
            "user_profile",
        ]

    def to_representation(self, obj):
        serialized_data = super(BankerPlayersSerializer, self).to_representation(obj)
        serialized_data["phone"] = mask_winners_phone_number(obj.user_profile.phone_number)

        try:
            del serialized_data["user_profile"]
        except KeyError:
            pass

        return serialized_data


class PlayBankerGameSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=100)
    amount = serializers.IntegerField()
    number_of_tickets = serializers.IntegerField()
