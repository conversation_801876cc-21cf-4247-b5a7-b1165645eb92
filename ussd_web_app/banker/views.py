from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from main.models import LotteryBatch, LottoTicket, LottoWinners
from pos_app.pos_helpers import CustomPaginator, machine_number_serializer
from ussd_web_app.banker.serializers import (
    BankerPlayersSerializer,
    FetchBankerGamePlayersForUssdWebAppSerializer,
    PlayBankerGameSerializer,
)
from ussd_web_app.banker.ussd_web_banker_helper import register_banker_game
from ussd_web_app.tasks import celery_get_woven_account_or_create_woven_account
from wallet_app.models import UserWallet
from wyse_ussd.tasks import ussd_lottery_payment

custom_response_code = {
    "success": 200,
    "error": 400,
    "not_found": 404,
    "unauthorized": 401,
    "010": "ticket paid for",
    "011": "ticket not paid for",
    "012": "ticket not found",
    "013": "ticket already paid for",
    "014": "ticket pending payment",
}


class FetchRealTimePlayersAndWinnersForBankerApiView(APIView):
    def get(self, request):
        winners_qs = LottoWinners.objects.filter(lotto_type="BANKER").order_by("-id")
        winners_paginated = CustomPaginator.paginate(request, queryset=winners_qs)
        serialized_winners_data = FetchBankerGamePlayersForUssdWebAppSerializer(winners_paginated, many=True).data

        drawn_batches_with_winning = LotteryBatch.objects.filter(
            lottery_type="BANKER",
            is_active=False,
            lottery_winner_ticket_number__isnull=False,
        ).order_by("-id")

        system_pick_numbers = []

        if drawn_batches_with_winning:
            for system_pick in drawn_batches_with_winning:
                serialized_system_pick = machine_number_serializer(system_pick.lottery_winner_ticket_number)
                if len(serialized_system_pick) > 1:
                    for index, num in enumerate(serialized_system_pick):
                        if index == 0:
                            pass
                        else:
                            list_num = [int(i) for i in num.split(",")]
                            system_pick_numbers.append(list_num)

        active_active_batch = LotteryBatch.objects.filter(lottery_type="BANKER", is_active=True).last()
        now = timezone.now()
        if active_active_batch:
            batch_time = active_active_batch.created_date
            time_diff = now - batch_time
            time_diff_in_minutes = time_diff.total_seconds() / 60
            if time_diff_in_minutes > 30:
                minutes_left = 0
            else:
                minutes_left = 30 - time_diff_in_minutes
        else:
            minutes_left = 0

        minutes_left = round(minutes_left, 1)

        minutes_left = str(minutes_left).split(".")
        minutes_left = f"{minutes_left[0]}:{minutes_left[1]}"

        players = LottoTicket.objects.filter(lottery_type="BANKER", paid=True).order_by("-id")
        winners_paginated = CustomPaginator.paginate(request, queryset=players)
        serialized_players_data = BankerPlayersSerializer(winners_paginated, many=True)

        data = {
            "winners": serialized_winners_data,
            "system_pick_numbers": system_pick_numbers,
            "minutes_left": minutes_left,
            "players": serialized_players_data.data,
        }

        return Response(data, status=status.HTTP_200_OK)


class UssdWebBankerTicket(APIView):
    def get(self, request):
        charges = 50
        data = [
            {
                "number_of_tickets": 1,
                "amount": 500 + charges,
                "potential_winning": 12500,
            },
            {
                "number_of_tickets": 2,
                "amount": 1500 + charges,
                "potential_winning": 37500,
            },
            {
                "number_of_tickets": 3,
                "amount": 2000 + charges,
                "potential_winning": 50000,
            },
            {
                "number_of_tickets": 4,
                "amount": 2500 + charges,
                "potential_winning": 62500,
            },
            {
                "number_of_tickets": 5,
                "amount": 5000 + charges,
                "potential_winning": 125000,
            },
            {
                "number_of_tickets": 6,
                "amount": 10000 + charges,
                "potential_winning": 250000,
            },
        ]

        return Response(data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = PlayBankerGameSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        banker_game_data = {
            500: {
                "number_of_tickets": 1,
                "amount": 500,
                "potential_winning": 12500,
            },
            1500: {
                "number_of_tickets": 2,
                "amount": 1500,
                "potential_winning": 37500,
            },
            2000: {
                "number_of_tickets": 3,
                "amount": 2000,
                "potential_winning": 50000,
            },
            2500: {
                "number_of_tickets": 4,
                "amount": 2500,
                "potential_winning": 62500,
            },
            5000: {
                "number_of_tickets": 5,
                "amount": 5000,
                "potential_winning": 125000,
            },
            10000: {
                "number_of_tickets": 6,
                "amount": 10000,
                "potential_winning": 250000,
            },
        }

        phone_number = serializer.validated_data["phone_number"]
        amount = serializer.validated_data["amount"]
        number_of_tickets = serializer.validated_data["number_of_tickets"]

        if banker_game_data.get(int(amount)) is None:
            data = {
                "message": "Invalid amount",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        ticket_creation_status, user_instance, game_id = register_banker_game(
            phone_number=phone_number,
            amount=banker_game_data.get(int(amount)).get("amount"),
            number_of_tickets=banker_game_data.get(int(amount)).get("number_of_tickets"),
            potential_winning=banker_game_data.get(int(amount)).get("potential_winning"),
        )

        user_wallet = UserWallet.objects.filter(user=user_instance, wallet_tag="WEB").last()

        if user_wallet is None:
            data = {
                "message": "Insufficient balance",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if (ticket_creation_status is True) and (user_wallet.game_available_balance >= banker_game_data.get(int(amount)).get("amount")):
            ussd_lottery_payment.apply_async(
                queue="celery1",
                args=[
                    user_instance.phone_number,
                    banker_game_data.get(int(amount)).get("amount"),
                    user_instance.bank_code,
                    banker_game_data.get(int(amount)).get("potential_winning"),
                    "BANKER",
                    game_id,
                ],
            )

            data = {
                "messsage": "Ticket paid for",
                "status_code": custom_response_code["010"],
            }

            return Response(data, status=status.HTTP_200_OK)

        else:
            user_wallet = UserWallet.objects.filter(user=user_instance, wallet_tag="USSD").last()

            if user_wallet is None:
                data = {
                    "message": "Insufficient balance",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if user_wallet.woven_account is None:
                celery_get_woven_account_or_create_woven_account(phone=user_instance.phone_number)
                payment_datails = {}
            else:
                payment_datails = {
                    "account_name": user_wallet.woven_account.acct_name,
                    "account_number": user_wallet.woven_account.vnuban,
                    "bank_name": user_wallet.woven_account.bank_name,
                }

            data = {
                "number_of_ticket": number_of_tickets,
                "ticket_price": banker_game_data.get(int(amount)).get("amount"),
                "payment_details": payment_datails,
            }

            return Response(data, status=status.HTTP_200_OK)
