import random
import uuid
from datetime import datetime

from main.api.api_lottery_helpers import generate_game_play_id
from main.models import LotteryBatch, LotteryModel, LottoTicket, UserProfile
from pos_app.pos_helpers import generate_pin
from wyse_ussd.models import UssdLotteryPayment


def register_banker_game(phone_number, amount, number_of_tickets, potential_winning):
    phone = LotteryModel.format_number_from_back_add_234(phone_number)

    user_profile = UserProfile.objects.filter(phone_number=phone).last()
    if user_profile is None:
        user_profile = UserProfile.objects.create(phone_number=phone)

    game_lines_selected = number_of_tickets
    game_play_id = generate_game_play_id()
    ticket_pin = generate_pin()
    identity_id = f"{uuid.uuid4()}{datetime.now().timestamp()}"
    line_count = game_lines_selected

    active_bnaker_batch = LotteryBatch.objects.filter(lottery_type="BANKER", is_active=True).last()

    for line in range(0, line_count):
        unqiue_digit = set()
        while len(unqiue_digit) < 5:
            unqiue_digit.add(random.randint(0, 49))

        ticket = "".join([str(x) for x in unqiue_digit])

        LottoTicket.objects.create(
            user_profile_id=user_profile.id,
            agent_profile=None,
            batch=active_bnaker_batch,
            phone=phone,
            stake_amount=round(amount / line_count, 2),
            expected_amount=round(amount / line_count, 2),
            potential_winning=potential_winning,
            channel="USSD_WEB",
            game_play_id=game_play_id,
            lottery_type="BANKER",
            number_of_ticket=line_count,
            ticket=ticket,
            pin=ticket_pin,
            identity_id=identity_id,
        )

    UssdLotteryPayment.objects.create(
        user=user_profile,
        amount=amount,
        game_play_id=game_play_id,
        lottery_type="BANKER",
    )

    return True, user_profile, game_play_id
