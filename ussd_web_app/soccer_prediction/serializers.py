from rest_framework import serializers

from main.api.api_lottery_helpers import generate_game_play_id
from main.models import LotteryModel, UserProfile
from main.tasks import create_wema_collection_account
from sport_app.models import OddPredictionType, SoccerOddPredictionTable
from ussd_web_app.tasks import celery_get_woven_account_or_create_woven_account
from wallet_app.models import UserWallet
from wyse_ussd.models import UssdLotteryPayment
from wyse_ussd.tasks import ussd_lottery_payment


class PredictOddsSerializer(serializers.Serializer):
    phone_number = serializers.CharField()
    subscription_id = serializers.CharField()
    consent = serializers.BooleanField()
    amount = serializers.FloatField()

    def validate(self, attrs):
        if not attrs.get("phone_number").isnumeric():
            raise serializers.ValidationError({"phone_number": "phone_number must be numeric"})
        if len(attrs.get("phone_number")) < 11 or len(attrs.get("phone_number")) > 11:
            raise serializers.ValidationError({"phone_number": "Ensure this field has 11 characters."})
        phone_number = LotteryModel.format_number_from_back_add_234(attrs.get("phone_number"))

        if SoccerOddPredictionTable.objects.filter(phone_number=phone_number).exists():
            raise serializers.ValidationError({"phone_number": f"{phone_number} subscription already exist!"})
        attrs["phone_number"] = phone_number
        subscription_id = attrs.get("subscription_id")
        consent = attrs.get("consent")
        get_subscription = OddPredictionType.objects.filter(id=subscription_id, is_active=True).first()
        if not get_subscription:
            raise serializers.ValidationError({"subscription_id": "subscription_id is invalid"})
        if not consent:
            raise serializers.ValidationError({"consent": "consent is must be True"})
        if get_subscription.subscription_amount != attrs.get("amount"):
            raise serializers.ValidationError(
                {"amount": f"{get_subscription.subscription_name} amount must be {get_subscription.subscription_amount}"}
            )

        user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
        if user_profile is None:
            user_profile = UserProfile.objects.create(phone_number=phone_number)

        user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="USSD").last()
        if user_wallet is None:
            user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="USSD")

        # check if user have wema virtual account
        if user_wallet.wema_account is None:
            create_wema_collection_account(phone_number=phone_number)

        game_pay_id = generate_game_play_id()
        amount = attrs.get("amount")
        try:
            SoccerOddPredictionTable.objects.create(
                user_profile=user_profile,
                phone_number=phone_number,
                subscription_type=get_subscription,
                amount=amount,
                consent=consent,
                game_pay_id=game_pay_id,
            )
        except Exception as ex:
            raise serializers.ValidationError({"message": ex})

        UssdLotteryPayment.objects.create(
            user=user_profile,
            amount=amount,
            game_play_id=game_pay_id,
            channel="USSD",
            lottery_type="PREDICTION_SUBSCRIPTION",
        )

        ussd_play_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
        if ussd_play_wallet is None:
            ussd_play_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")
        if ussd_play_wallet.game_available_balance >= amount:
            ussd_lottery_payment.apply_async(
                queue="celery1",
                args=[
                    user_profile.phone_number,
                    int(amount),
                    user_profile.bank_code,
                    get_subscription.subscription_name,
                    "PREDICTION_SUBSCRIPTION",
                    game_pay_id,
                ],
            )
            attrs["status"] = "ACCEPTED"
            attrs["message"] = "Paid"
            attrs["game_id"] = game_pay_id
            attrs["paid"] = True
            attrs["amount"] = amount
            attrs["subscription_type"] = "PREDICTION_SUBSCRIPTION"

        else:
            if user_wallet.woven_account is None:
                celery_get_woven_account_or_create_woven_account(phone=user_profile.phone_number)
                payment_details = {}
            else:
                payment_details = {
                    "account_name": user_wallet.wema_account.acct_name if user_wallet.wema_account else None,
                    "account_number": user_wallet.wema_account.vnuban if user_wallet.wema_account else None,
                    "bank_name": user_wallet.wema_account.bank_name if user_wallet.wema_account else None,
                }

            attrs["status"] = "ACCEPTED"
            attrs["message"] = "Payment processing"
            attrs["game_id"] = game_pay_id
            attrs["paid"] = False
            attrs["amount"] = amount + 50
            attrs["subscription_type"] = "PREDICTION_SUBSCRIPTION"
            attrs["payment_details"] = payment_details

        return attrs


class PredictionSubscriptionPaymentSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=100)
    amount = serializers.IntegerField()
