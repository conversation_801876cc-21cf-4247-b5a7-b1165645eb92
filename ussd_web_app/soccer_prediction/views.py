import uuid

from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from main.models import LotteryModel, UserProfile
from sport_app.serializers import (
    PredictionSubscriptionPaymentSerializer,
    PredictOddsSerializer,
)
from wallet_app.helpers.payment_gateway import PaymentGateway
from wallet_app.models import PaystackTransaction


class PredictOddAPIView(APIView):
    def post(self, request):
        serializer = PredictOddsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.validated_data, status=status.HTTP_200_OK)


class PayForPredictionSubscription(APIView):
    serializer_class = PredictionSubscriptionPaymentSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data.get("phone_number")
        amount = serializer.validated_data.get("amount")
        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        amount = serializer.validated_data.get("amount")

        user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
        if user_profile is None:
            user_profile = UserProfile.objects.create(phone_number=phone_number)

        transaction_ref = transaction_ref = f"LTT-PAY{uuid.uuid4()}-web_ussd"

        paystack_payload = {
            "email": "<EMAIL>",
            "amount": float(amount) * 100,
            "currency": "NGN",
            "reference": transaction_ref,
        }

        # create transaction
        PaystackTransaction.objects.create(
            user=user_profile,
            amount=float(amount),
            reference=transaction_ref,
            created_at=timezone.now(),
            paid_at=timezone.now(),
            channel="USSD",
            raw_data=paystack_payload,
            payment_reason="LOTTERY_PAYMENT",
        )

        # initiate payment
        paystack_api = PaymentGateway().paystack_link_request(**paystack_payload)

        payment_link = paystack_api.get("authorization_url")

        data = {
            "message": "success",
            "payment_link": payment_link,
        }

        return Response(data, status=status.HTTP_200_OK)
