from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from awoof_app.models import AwoofDrawTable, AwoofGameTable, LifeStyleTable
from main.models import LotteryModel, UserProfile
from main.tasks import create_wema_collection_account
from pos_app.pos_helpers import CustomPaginator
from ussd_web_app.awoof.serializers import (
    AcceptUssdWebGame,
    AwoofWinnersSerializer,
    FetchAwoofGamePlayersForUssdWebAppSerializer,
)
from ussd_web_app.tasks import celery_get_woven_account_or_create_woven_account
from wallet_app.models import UserWallet
from wyse_ussd.tasks import ussd_lottery_payment

custom_response_code = {
    "success": 200,
    "error": 400,
    "not_found": 404,
    "unauthorized": 401,
    "010": "ticket paid for",
    "011": "ticket not paid for",
    "012": "ticket not found",
    "013": "ticket already paid for",
    "014": "ticket pending payment",
}


class FetchRealTimePlayersAndWinnersApiView(APIView):
    def get(self, request):
        active_life_style_item = LifeStyleTable.objects.filter(item_status="OPEN", is_active=True, is_drawn=False).last()

        winners_qs = AwoofDrawTable.objects.all().order_by("-id")
        winners_paginated = CustomPaginator.paginate(request, queryset=winners_qs)
        serialized_winners_data = AwoofWinnersSerializer(winners_paginated, many=True).data

        if active_life_style_item is None:
            data = {
                "bid_percentage": 0,
                "players": [],
                "winners": serialized_winners_data,
            }
            return Response(data, status=status.HTTP_200_OK)

        games = active_life_style_item.awoof_game.filter(paid=True)

        players_paginated = CustomPaginator.paginate(request, queryset=games)
        serializer_players_data = FetchAwoofGamePlayersForUssdWebAppSerializer(players_paginated, many=True).data

        bid_percentage = (games.count() / active_life_style_item.ticket_draw_count) * 100

        data = {
            "bid_percentage": bid_percentage,
            "players": serializer_players_data,
            "winners": serialized_winners_data,
        }

        return Response(data, status=status.HTTP_200_OK)


class UssdWebAwoofTicket(APIView):
    def get(self, request):
        charges = 50
        active_life_style_item = LifeStyleTable.objects.filter(item_status="OPEN", is_active=True, is_drawn=False).last()

        if active_life_style_item is None:
            data = {
                "message": "No active life style item",
            }
            return Response(data, status=status.HTTP_204_NO_CONTENT)

        item_amount = str(active_life_style_item.item_amount).replace(",", "")

        if (
            (int(item_amount) == 15000)
            or (int(item_amount) == 30000)
            or (int(item_amount) == 50000)
            or (int(item_amount) == 100000)
            or (int(item_amount) == 150000)
            or (int(item_amount) == 300000)
            or (int(item_amount) == 500000)
        ):
            min_ticket_price = active_life_style_item.min_gift_amount + charges
            mid_ticket_price = active_life_style_item.mid_gift_amount + charges
            max_ticket_price = active_life_style_item.max_gift_amount + charges

            data = [
                {
                    "number_of_ticket": 1,
                    "ticket_price": min_ticket_price,
                    "item_id": active_life_style_item.id,
                },
                {
                    "number_of_ticket": 2,
                    "ticket_price": mid_ticket_price,
                    "item_id": active_life_style_item.id,
                },
                {
                    "number_of_ticket": 3,
                    "ticket_price": max_ticket_price,
                    "item_id": active_life_style_item.id,
                },
            ]

            return Response(data, status=status.HTTP_200_OK)

        else:
            data = {
                "message": "Invalid life style item",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request):
        pass

        serializers = AcceptUssdWebGame(data=request.data)
        serializers.is_valid(raise_exception=True)

        ticket_id = serializers.validated_data["ticket_id"]
        option_selected = serializers.validated_data["option_selected"]
        phone_number = serializers.validated_data["phone_number"]

        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        print("phone_number", phone_number)

        user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
        if user_profile is None:
            user_profile = UserProfile.objects.create(phone_number=phone_number)

        user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="USSD").last()
        if user_wallet is None:
            user_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="USSD")

        # check if user have wema virtual account
        if user_wallet.wema_account is None:
            create_wema_collection_account(phone_number=phone_number)

        try:
            life_style_item = LifeStyleTable.objects.get(id=ticket_id)
        except LifeStyleTable.DoesNotExist:
            data = {
                "message": "Ticket not found",
            }
            return Response(data, status=status.HTTP_404_NOT_FOUND)

        if (life_style_item.item_status != "OPEN") or (life_style_item.is_active is False) or (life_style_item.is_drawn is True):
            return Response({"message": "Ticket not found"}, status=status.HTTP_404_NOT_FOUND)

        item_amount = str(life_style_item.item_amount).replace(",", "")

        if (
            (int(item_amount) == 15000)
            or (int(item_amount) == 30000)
            or (int(item_amount) == 50000)
            or (int(item_amount) == 100000)
            or (int(item_amount) == 150000)
            or (int(item_amount) == 300000)
            or (int(item_amount) == 500000)
        ):
            min_ticket_price = life_style_item.min_gift_amount
            mid_ticket_price = life_style_item.mid_gift_amount
            max_ticket_price = life_style_item.max_gift_amount

            if option_selected == "1":
                ticket_price = min_ticket_price
            elif option_selected == "2":
                ticket_price = mid_ticket_price
            elif option_selected == "3":
                ticket_price = max_ticket_price

            else:
                data = {
                    "message": "Invalid option selected",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            awoof_game_instance = AwoofGameTable().register_ussd_awoof_game(
                awoof_item_db_id=int(ticket_id),
                user_selected_chances=int(str(ticket_price).replace(".0", "")),
                user_phone_number=phone_number,
                band_number=int(option_selected),
                channel="USSD_WEB",
            )

            if awoof_game_instance is None:
                data = {
                    "message": "Ticket already paid for",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            ussd_play_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if ussd_play_wallet is None:
                ussd_play_wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")
            if ussd_play_wallet.game_available_balance >= ticket_price:
                ussd_lottery_payment.apply_async(
                    queue="celery1",
                    args=[
                        user_profile.phone_number,
                        int(str(ticket_price).replace(".0", "")),
                        user_profile.bank_code,
                        life_style_item.item_name,
                        "AWOOF",
                        awoof_game_instance.game_play_id,
                    ],
                )

                data = {
                    "messsage": "Ticket paid for",
                    "status_code": custom_response_code["010"],
                }

                return Response(data, status=status.HTTP_200_OK)

            else:
                if user_wallet.woven_account is None:
                    celery_get_woven_account_or_create_woven_account(phone=user_profile.phone_number)
                    payment_datails = {}
                else:
                    payment_datails = {
                        "account_name": user_wallet.wema_account.acct_name if user_wallet.wema_account else None,
                        "account_number": user_wallet.wema_account.vnuban if user_wallet.wema_account else None,
                        "bank_name": user_wallet.wema_account.bank_name if user_wallet.wema_account else None,
                    }

                data = {
                    "number_of_ticket": option_selected,
                    "ticket_price": ticket_price + 50,
                    "payment_details": payment_datails,
                }

                return Response(data, status=status.HTTP_200_OK)

        else:
            data = {
                "message": "Invalid life style item",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
