from django.template.defaultfilters import date
from rest_framework import serializers

from awoof_app.models import AwoofDrawTable, AwoofGameTable
from main.helpers.helper_functions import mask_winners_phone_number


class AwoofWinnersSerializer(serializers.ModelSerializer):
    class Meta:
        model = AwoofDrawTable
        fields = ["id", "item_winner", "date_created"]

    def to_representation(self, obj):
        serialized_data = super(AwoofWinnersSerializer, self).to_representation(obj)
        serialized_data["phone"] = mask_winners_phone_number(obj.item_winner.phone_number)
        serialized_data["date"] = date(obj.date_created, "d/m/y, h:i A")

        try:
            del serialized_data["item_winner"]
            del serialized_data["date_created"]
        except Exception:
            pass

        return serialized_data


class FetchAwoofGamePlayersForUssdWebAppSerializer(serializers.ModelSerializer):
    class Meta:
        model = AwoofGameTable
        fields = [
            "id",
            "user_profile",
        ]

    def to_representation(self, obj):
        serialized_data = super(FetchAwoofGamePlayersForUssdWebAppSerializer, self).to_representation(obj)
        serialized_data["phone"] = mask_winners_phone_number(obj.user_profile.phone_number)
        serialized_data["date"] = date(obj.date_created, "d/m/y, h:i A")

        del serialized_data["user_profile"]

        return serialized_data


class AcceptUssdWebGame(serializers.Serializer):
    phone_number = serializers.CharField(max_length=100)
    ticket_id = serializers.CharField(max_length=100)
    option_selected = serializers.CharField(max_length=100)
