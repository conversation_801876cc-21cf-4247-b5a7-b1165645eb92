from celery import shared_task

from main.helpers.woven_manager import generate_woven_collection_account_number
from main.models import WovenAccountDetail
from wallet_app.models import UserWallet


@shared_task
def celery_get_woven_account_or_create_woven_account(phone):
    print("phone celery_get_woven_account_or_create_woven_account", phone)
    user_account = UserWallet.objects.filter(user__phone_number=phone, wallet_tag="USSD").last()

    if not user_account:
        print("user_account", user_account)
        return None

    # get_old_woven = WovenAccountDetail.objects.filter(
    #     Q(phone_number=phone) & Q(is_active=True) & Q(wallet_tag="USSD")
    # ).last()
    get_old_woven = WovenAccountDetail.objects.filter(phone_number=phone, wallet_tag="USSD").last()

    if get_old_woven is None:
        check_woven_account = generate_woven_collection_account_number(phone_number=phone, func_count=3, tag="USSD")

        if check_woven_account is not None:
            woven_account_number = check_woven_account["vnuban"]
            woven_bank_name = check_woven_account["bank_name"]

            # Use this to fill model
            woven_account_name = check_woven_account["account_name"]
            woven_account_reference = check_woven_account["account_reference"]
            check_woven_account["woven_payload"]

        else:
            woven_account_number = None
            woven_bank_name = None
            woven_account_name = None
            woven_account_reference = None

    else:
        woven_account_number = get_old_woven.vnuban
        woven_bank_name = get_old_woven.bank_name
        woven_account_name = get_old_woven.acct_name
        woven_account_reference = get_old_woven.account_ref

    _woven = WovenAccountDetail.objects.filter(phone_number=phone, wallet_tag="USSD").last()

    if _woven is None:
        _woven = WovenAccountDetail.objects.create(
            phone_number=phone,
            vnuban=woven_account_number,
            acct_name=woven_account_name,
            bank_name=woven_bank_name,
            account_ref=woven_account_reference,
            payload={},
            wallet_tag="USSD",
        )

    print("woven_account_number", woven_account_number)
    print("_woven", _woven)
    user_account.woven_account = _woven
    user_account.account_ref = _woven.account_ref
    user_account.save()
