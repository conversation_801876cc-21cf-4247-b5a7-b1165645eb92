from django.urls import include, path

from ussd_web_app.views import (
    FetchAllBanksDetails,
    GenerateRedbillerUssdPayment,
    PaystackUssdWebPayment,
    VerifyPaymentApiView,
)

urlpatterns = [
    path("fetch_bank_details/", FetchAllBanksDetails.as_view()),
    path("generate_ussd_payment_code/", GenerateRedbillerUssdPayment.as_view()),
    path("verify_payment/", VerifyPaymentApiView.as_view()),
    path("awoof/", include("ussd_web_app.awoof.urls")),
    path("generate_paystack_payment_link/", PaystackUssdWebPayment.as_view()),
    path("banker/", include("ussd_web_app.banker.urls")),
    path("soccer_prediction/", include("ussd_web_app.soccer_prediction.urls")),
]
