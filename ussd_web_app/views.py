import uuid

from django.utils import timezone
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from main.models import LotteryModel, UserProfile
from main.ussd.bankdb import bankdb
from ussd_web_app.serializers import (
    GenerateRedbillerUssdPaymentSerializer,
    PaystackUssdWebPaymentSerializer,
    PhoneNumbersSerializer,
)
from wallet_app.helpers.payment_gateway import PaymentGateway
from wallet_app.models import PaystackTransaction
from wyse_ussd.helper.redbiller_ussd_helper import redbiller_ussd_payment_code
from wyse_ussd.models import UssdLotteryPayment


class GenerateRedbillerUssdPayment(APIView):
    serializer_class = GenerateRedbillerUssdPaymentSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        amount = serializer.validated_data.get("amount")
        phone_number = serializer.validated_data.get("phone_number")
        bank_code = serializer.validated_data.get("bank_code")

        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
        if user_profile is None:
            user_profile = UserProfile.objects.create(phone_number=phone_number)

        ussd_payment_code = redbiller_ussd_payment_code(
            user_profile_instance=user_profile,
            amount=amount,
            payment_for="LOTTERY_PAYMENT",
            _bank_code=bank_code,
        )

        if ussd_payment_code is None:
            return Response(
                {"message": "Unable to generate ussd payment code"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        data = {
            "ussd_code": ussd_payment_code,
        }

        return Response(data, status=status.HTTP_200_OK)


class FetchAllBanksDetails(APIView):
    def get(self, request):
        banks = bankdb()

        return Response(banks, status=status.HTTP_200_OK)


class VerifyPaymentApiView(APIView):
    serializer_class = PhoneNumbersSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data.get("phone_number")
        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        usssd_lottery_paymentt_instance = UssdLotteryPayment.objects.filter(user__phone_number=phone_number).last()

        if usssd_lottery_paymentt_instance.is_successful is True:
            data = {
                "message": "Payment successful",
            }
            return Response(data, status=status.HTTP_200_OK)

        else:
            if usssd_lottery_paymentt_instance.is_verified is True:
                data = {
                    "message": "Payment not successful",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = {
                    "message": "pending",
                }

                return Response(data, status=status.HTTP_200_OK)

    def get(self, request):
        phone_number = request.GET.get("phone_number", None)

        if phone_number is None:
            data = {
                "message": "phone_number is required",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        usssd_lottery_paymentt_instance = UssdLotteryPayment.objects.filter(user__phone_number=phone_number).last()

        if usssd_lottery_paymentt_instance.is_successful is True:
            data = {
                "message": "Payment successful",
            }
            return Response(data, status=status.HTTP_200_OK)

        else:
            if usssd_lottery_paymentt_instance.is_verified is True:
                data = {
                    "message": "Payment not successful",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = {
                    "message": "pending",
                }

                return Response(data, status=status.HTTP_200_OK)


class PaystackUssdWebPayment(APIView):
    serializer_class = PaystackUssdWebPaymentSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data.get("phone_number")
        amount = serializer.validated_data.get("amount")
        phone_number = LotteryModel.format_number_from_back_add_234(phone_number)

        user_profile = UserProfile.objects.filter(phone_number=phone_number).last()
        if user_profile is None:
            user_profile = UserProfile.objects.create(phone_number=phone_number)

        transaction_ref = transaction_ref = f"LTT-PAY{uuid.uuid4()}-web_ussd"

        paystack_payload = {
            "email": "<EMAIL>",
            "amount": float(amount) * 100,
            "currency": "NGN",
            "reference": transaction_ref,
        }

        # create transaction
        PaystackTransaction.objects.create(
            user=user_profile,
            amount=float(amount),
            reference=transaction_ref,
            created_at=timezone.now(),
            paid_at=timezone.now(),
            channel="USSD",
            raw_data=paystack_payload,
            payment_reason="LOTTERY_PAYMENT",
        )

        # initiate payment
        paystack_api = PaymentGateway().paystack_link_request(**paystack_payload)

        payment_link = paystack_api.get("authorization_url")

        data = {
            "message": "success",
            "payment_link": payment_link,
        }

        return Response(data, status=status.HTTP_200_OK)
