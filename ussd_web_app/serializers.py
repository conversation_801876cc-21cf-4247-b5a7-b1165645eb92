from rest_framework import serializers


class GenerateRedbillerUssdPaymentSerializer(serializers.Serializer):
    amount = serializers.IntegerField()
    phone_number = serializers.CharField(max_length=100)
    bank_code = serializers.CharField(max_length=100)


class PhoneNumbersSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=100)


class PaystackUssdWebPaymentSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=100)
    amount = serializers.IntegerField()
