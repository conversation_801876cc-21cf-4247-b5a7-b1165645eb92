from collections import OrderedDict

from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied

from main.models import UserProfile
from referral_system.models import ReferralTransaction
from wallet_app.models import BeneficiaryDetail, UserWallet, WalletTransaction


# Create your serializer(s) here.
class WalletSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserWallet
        fields = ["user", "woven_account", "account_ref", "currency", "created_at"]
        read_only_fields = [
            "user",
            "woven_account",
            "account_ref",
            "currency",
            "created_at",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        representation["cash_balancce"] = instance.withdrawable_available_balance + instance.game_available_balance
        return representation


class CreateTransactionPinSerializer(serializers.Serializer):
    pin = serializers.CharField(
        min_length=4,
        max_length=4,
        required=True,
        style={"input_type": "password"},
        write_only=True,
    )
    confirm_pin = serializers.CharField(
        min_length=4,
        max_length=4,
        required=True,
        style={"input_type": "password"},
        write_only=True,
    )

    def validate(self, data):
        # check if pin is numeric
        if not data["pin"].isnumeric():
            raise serializers.ValidationError("Pin must be numeric")

        if not data["confirm_pin"].isnumeric():
            raise serializers.ValidationError("Pin must be numeric")

        if data["pin"] != data["confirm_pin"]:
            raise serializers.ValidationError("pin does not match")
        return data


class FundAndWithdrawalWalletSerializer(serializers.ModelSerializer):
    amount = serializers.IntegerField()

    class Meta:
        model = WalletTransaction
        fields = "__all__"
        read_only_fields = [
            "wallet",
            "transaction_type",
            "transaction_reference",
            "method",
            "status",
            "date_created",
        ]

    def validate(self, data):
        if data["amount"] <= 0:
            raise serializers.ValidationError("invalid amount")
        return data


class BeneficiaryDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BeneficiaryDetail
        fields = [
            "id",
            "user",
            "bank_name",
            "account_name",
            "account_number",
            "created_at",
            "bank_code",
            "logo",
        ]


class CreateBeneficiarySerializer(serializers.Serializer):
    bank_code = serializers.CharField()
    account_name = serializers.CharField()
    account_number = serializers.CharField()
    bank_name = serializers.CharField()


class ChangeTransctionPinSerializer(serializers.Serializer):
    old_pin = serializers.CharField(max_length=4, min_length=4, required=True, style={"input_type": "password"})
    new_pin = serializers.CharField(max_length=4, min_length=4, required=True, style={"input_type": "password"})
    confirm_new_pin = serializers.CharField(max_length=4, min_length=4, required=True, style={"input_type": "password"})

    def validate(self, data):
        if not data["new_pin"].isnumeric():
            raise serializers.ValidationError("old_pin must be numeric")

        if not data["confirm_new_pin"].isnumeric():
            raise serializers.ValidationError("new_pin must be numeric")

        if not data["old_pin"].isnumeric():
            raise serializers.ValidationError("confirm_new_pin must be numeric")

        if data["new_pin"] != data["confirm_new_pin"]:
            raise serializers.ValidationError("pin does not match")
        return data


class ReferralTransactionSerializer(serializers.ModelSerializer):
    method = serializers.CharField(default="REFERRAL")
    status = serializers.CharField(default="SUCCESSFUL")
    transaction_reference = serializers.CharField(default="")

    class Meta:
        model = ReferralTransaction
        fields = [
            "id",
            "transaction_type",
            "transaction_reference",
            "method",
            "amount",
            "status",
            "created_at",
        ]


class WalletTransactionHistorySerializer(serializers.ModelSerializer):
    depth = 1

    class Meta:
        model = WalletTransaction
        fields = [
            "id",
            "transaction_type",
            "transaction_reference",
            "transaction_from",
            "method",
            "amount",
            "status",
            "date_created",
        ]


class UserTransactionSerializer(serializers.ModelSerializer):
    referral_transaction = ReferralTransactionSerializer(many=True, read_only=True)

    class Meta:
        model = UserProfile
        fields = ["id", "referral_transaction"]

    def to_representation(self, obj):
        serialized_data = super(UserTransactionSerializer, self).to_representation(obj)
        wallet_transaction = WalletTransaction.objects.filter(wallet__user=obj, show_transaction=True)
        serialize_wallet_transction = WalletTransactionHistorySerializer(wallet_transaction, many=True, read_only=True)
        serialized_data["wallet_transaction"] = serialize_wallet_transction.data
        serialized_data["all_transactions"] = serialized_data["wallet_transaction"] + serialized_data["referral_transaction"]

        del serialized_data["wallet_transaction"]
        del serialized_data["referral_transaction"]

        d = []
        for k, v in serialized_data.items():
            if k == "all_transactions":
                for i in v:
                    d2 = OrderedDict([("date_created", v) if k == "created_at" else (k, v) for k, v in i.items()])
                    d.append(d2)

        sort_serialized_transactions = sorted(d, key=lambda x: x["date_created"], reverse=True)
        serialized_data["all_transactions"] = sort_serialized_transactions
        return serialized_data


class FilterTransactionSerializer(serializers.Serializer):
    start_date = serializers.DateField(required=False, allow_null=True)
    end_date = serializers.DateField(required=False, allow_null=True)
    status = serializers.CharField(required=False, allow_null=True)
    method = serializers.CharField(required=False, allow_null=True)


class ChangeTransactionPinSerilaizer(serializers.Serializer):
    otp = serializers.CharField(max_length=6, min_length=6, required=True)


class WithdrawalSerializer(serializers.Serializer):
    amount = serializers.FloatField()
    account_name = serializers.CharField()
    account_number = serializers.CharField()
    bank_name = serializers.CharField()
    bank_code = serializers.CharField()
    pin = serializers.CharField(max_length=4, min_length=4, required=True, style={"input_type": "password"})
    save_beneficiary = serializers.BooleanField(default=False)


class PayoutReferrenceSerializer(serializers.Serializer):
    reference = serializers.CharField()


class PosFundDebitPlayWinningWalletSerializer(serializers.Serializer):
    WALLET_TYPE_CHOICE_FIELD = (
        ("WINNINGS_WALLET", "WINNINGS_WALLET"),
        ("GAME_PLAY_WALLET", "GAME_PLAY_WALLET"),
        ("BONUS_WALLET", "BONUS_WALLET"),
        ("COMMISSION_WALLET", "COMMISSION_WALLET"),
    )

    TRANSACTION_TYPE = (
        ("DEBIT", "DEBIT"),
        ("CREDIT", "CREDIT"),
    )

    phone = serializers.CharField()
    amount = serializers.FloatField()
    transaction_from = serializers.CharField()
    game_type = serializers.CharField()
    game_play_id = serializers.CharField()
    transaction_id = serializers.CharField()
    wallet_type = serializers.ChoiceField(choices=WALLET_TYPE_CHOICE_FIELD)
    transaction_type = serializers.ChoiceField(choices=TRANSACTION_TYPE)


class UpdateAgentRemittanceAmountPaidSerializer(serializers.Serializer):
    amount = serializers.FloatField()
    db_id = serializers.IntegerField()


class ManuallyAgentAndUserWalletSerializer(serializers.Serializer):
    USER_CHOICE_FIELD = (
        ("WEB/USSD_USER", "WEB/USSD_USER"),
        ("MOBILE/POS_USER", "MOBILE/POS_USER"),
    )

    WALLET_TYPE_CHOICE_FIELD = (
        ("WINNINGS_WALLET", "WINNINGS_WALLET"),
        ("GAME_PLAY_WALLET", "GAME_PLAY_WALLET"),
    )

    amount = serializers.FloatField()
    phone_number = serializers.CharField()
    user_type = serializers.ChoiceField(choices=USER_CHOICE_FIELD)
    wallet_type = serializers.ChoiceField(choices=WALLET_TYPE_CHOICE_FIELD)


class GenerateWithdrawalPinSerializer(serializers.Serializer):
    phone_number = serializers.CharField(max_length=13)
    game_type = serializers.CharField(max_length=200)

    def validate(self, values):
        phone_number = values.get("phone_number")
        is_verified = UserProfile.have_verification_code(phone_number=phone_number)

        if is_verified is False:
            raise PermissionDenied("User is not Verified")

        return values
