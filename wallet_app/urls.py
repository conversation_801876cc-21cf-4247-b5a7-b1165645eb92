from django.urls import path

from wallet_app.views import (
    BeneficiaryDetailsAPIView,
    ChangeTransactionPinView,
    CreateTransactionPinAPIView,
    ForgotTransactionPinAPIView,
    FundWalletAPIView,
    GenerateWithdrawalPin,
    LibertyAgencyVFDPayoutVerification,
    ManuallyUpdateAgentAndUserWallet,
    TransactionHistoryAPIView,
    WalletDetailsAPIView,
    WithdrawalView,
    paystackpayment,
)

urlpatterns = [
    path("get_details/", WalletDetailsAPIView.as_view()),
    path("create_pin/", CreateTransactionPinAPIView.as_view()),
    path("change_pin/", ChangeTransactionPinView.as_view()),
    path("add_fund/", FundWalletAPIView.as_view()),
    path("paystack_payment/", paystackpayment),
    path("transaction_history/", TransactionHistoryAPIView.as_view()),
    path("beneficiary/", BeneficiaryDetailsAPIView.as_view()),
    path("forgot_pin/", ForgotTransactionPinAPIView.as_view()),
    path("withdraw/", WithdrawalView.as_view()),
    path("vfd_payout_verification/", LibertyAgencyVFDPayoutVerification.as_view()),
    path("manaully_update_agent_user_wallet/", ManuallyUpdateAgentAndUserWallet.as_view()),
    path("generate_withdrawal_otp/", GenerateWithdrawalPin.as_view()),
]
