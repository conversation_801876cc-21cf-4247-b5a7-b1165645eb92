from django.core.management.base import BaseCommand

from main.models import PayoutTransactionTable
from pos_app.models import AgentWallet, PosLotteryWinners
from wallet_app.models import GeneralWithdrawableWalletTransaction, UserWallet


def disbuggrsennt_reversal(obj):
    if obj.channel == "POS":
        agent_wallet = AgentWallet.objects.filter(agent__phone=obj.phone).last()

        if agent_wallet:
            agent_wallet.winnings_bal += obj.amount
            agent_wallet.transaction_from = "WINNING"
            agent_wallet.transaction_type = "PAYOUT_REVERSAL"
            agent_wallet.save()

            # refund the amount to general wallet
            general_wallet = GeneralWithdrawableWalletTransaction.objects.last()
            general_wallet.amount += obj.amount
            general_wallet.transaction_type = "REVERSAL"
            general_wallet.save()

            update_pos_lottery_winners_table(ref=obj.payout_trans_ref)

            return True

        return False

    else:
        user_wallet = UserWallet.objects.filter(user__phone_number=obj.phone, wallet_tag="WEB").last()

        if user_wallet:
            user_wallet.withdrawable_available_balance += obj.amount
            user_wallet.transaction_from = "REVERSAL"
            user_wallet.save()

            return True

        # refund the amount to general wallet
        general_wallet = GeneralWithdrawableWalletTransaction.objects.last()
        general_wallet.amount += obj.amount
        general_wallet.transaction_type = "REVERSAL"
        general_wallet.save()

    return False


def update_pos_lottery_winners_table(ref, status="FAILED"):
    pos_lottery_winner_instance = PosLotteryWinners.objects.filter(payout_ref=ref).last()
    if pos_lottery_winner_instance:
        if status == "SUCCESSFUL":
            pos_lottery_winner_instance.payout_successful = True
            pos_lottery_winner_instance.payout_verified = True
            pos_lottery_winner_instance.save()
        elif status == "FAILED":
            pos_lottery_winner_instance.payout_successful = False
            pos_lottery_winner_instance.payout_verified = False
            pos_lottery_winner_instance.is_win_claimed = False
            pos_lottery_winner_instance.withdrawl_initiated = False
            pos_lottery_winner_instance.save()


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        vfd_payout_data = PayoutTransactionTable.objects.filter(
            source="VFD",
            is_verified=False,
            source_response_payload__contains="do not have sufficient",
        )
        if vfd_payout_data:
            for payout in vfd_payout_data:
                payout.is_verified = True
                payout.save()

                disbuggrsennt_reversal(payout)
