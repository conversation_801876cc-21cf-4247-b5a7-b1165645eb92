from django.core.management.base import BaseCommand

from main.helpers.vfd_disbursement_helper import VfdDisbursementHelperFunc
from main.models import PayoutTransactionTable, UserProfile
from pos_app.models import AgentWallet, AgentWalletTransaction, PosLotteryWinners
from wallet_app.models import (
    DebtCollection,
    GeneralWithdrawableWalletTransaction,
    UserWallet,
)
import pytz
from django.conf import settings
from datetime import datetime


def disbuggrsennt_reversal(obj):
    if obj.channel == "POS":
        agent_wallet = AgentWallet.objects.filter(agent__phone=obj.phone).last()

        if agent_wallet:
            agent_wallet.winnings_bal += obj.amount
            agent_wallet.transaction_from = "WINNING"
            agent_wallet.transaction_type = "PAYOUT_REVERSAL"
            agent_wallet.save()

            # refund the amount to general wallet
            general_wallet = GeneralWithdrawableWalletTransaction.objects.last()
            general_wallet.amount += obj.amount
            general_wallet.transaction_type = "REVERSAL"
            general_wallet.save()

            update_pos_lottery_winners_table(ref=obj.payout_trans_ref)

            return True

        return False

    else:
        user_wallet = UserWallet.objects.filter(user__phone_number=obj.phone, wallet_tag="WEB").last()

        if user_wallet:
            user_wallet.withdrawable_available_balance += obj.amount
            user_wallet.transaction_from = "REVERSAL"
            user_wallet.save()

            return True

        # refund the amount to general wallet
        general_wallet = GeneralWithdrawableWalletTransaction.objects.last()
        general_wallet.amount += obj.amount
        general_wallet.transaction_type = "REVERSAL"
        general_wallet.save()

    return False


def update_pos_lottery_winners_table(ref, status="FAILED"):
    pos_lottery_winner_instance = PosLotteryWinners.objects.filter(payout_ref=ref).last()
    if pos_lottery_winner_instance:
        if status == "SUCCESSFUL":
            pos_lottery_winner_instance.payout_successful = True
            pos_lottery_winner_instance.payout_verified = True
            pos_lottery_winner_instance.save()
        elif status == "FAILED":
            pos_lottery_winner_instance.payout_successful = False
            pos_lottery_winner_instance.payout_verified = False
            pos_lottery_winner_instance.is_win_claimed = False
            pos_lottery_winner_instance.withdrawl_initiated = False
            pos_lottery_winner_instance.save()


# def agent_buddy_reward():

#     payload = {
#         "from_wallet_type": "COLLECTION",
#         "to_wallet_type": "COLLECTION",
#         "data": [
#             {
#                 "buddy_phone_number": "2349076242454",
#                 "amount": 10,
#                 "narration": "COMMISSION",
#                 "save_beneficiary": "True",
#                 "remove_beneficiary": "False",
#                 "is_recurring": "False",
#             }
#         ]
#     }
#     payload = {
#         "from_wallet_type": "COLLECTION",
#         "to_wallet_type": "COLLECTION",
#         "data": [
#             {
#                 "buddy_phone_number": "2349076242454",
#                 "amount": 10,
#                 "narration": "COMMISSION"
#                 "is_beneficiary": "False",
#                 "save_beneficiary": "True",
#                 "remove_beneficiary": "False",
#                 "is_recurring": "False",
#             }
#         ]}


def updating_user_debts():
    users = UserProfile.objects.filter(debt_amount__gt=0)

    for user in users:
        user_debt_amount = user.debt_amount
        user_wallets = UserWallet.objects.filter(user=user)

        ussd_wallet = user_wallets.filter(wallet_tag="USSD").last()
        web_wallet = user_wallets.filter(wallet_tag="WEB").last()

        if ussd_wallet:
            ussd_user_game_play_balance = ussd_wallet.game_available_balance
            ussd_withdrawal_balance = ussd_wallet.withdrawable_available_balance

            if ussd_user_game_play_balance > 0:
                if user_debt_amount > ussd_user_game_play_balance:
                    ussd_wallet.game_available_balance = 0
                    ussd_wallet.transaction_from = "DEBT_COLLECTION"
                    ussd_wallet.save()

                    DebtCollection.create_record(
                        user=user,
                        amount=ussd_user_game_play_balance,
                        wallet_type="USER_PLAY_WALLET",
                        debt_after=(user_debt_amount - ussd_user_game_play_balance),
                    )

                    user.recovered_debt += ussd_user_game_play_balance
                    user.debt_amount -= ussd_user_game_play_balance
                    user.save()

                    user_debt_amount -= ussd_user_game_play_balance

                else:
                    ussd_wallet.game_available_balance -= user_debt_amount
                    ussd_wallet.transaction_from = "DEBT_COLLECTION"
                    ussd_wallet.save()

                    DebtCollection.create_record(
                        user=user,
                        amount=user_debt_amount,
                        wallet_type="USER_PLAY_WALLET",
                        debt_after=0,
                    )

                    user.recovered_debt += user_debt_amount
                    user.debt_amount -= user_debt_amount
                    user.save()

                    user_debt_amount = 0

            if user_debt_amount > 0:
                if ussd_withdrawal_balance > 0:
                    if user_debt_amount > ussd_withdrawal_balance:
                        ussd_wallet.withdrawable_available_balance = 0
                        ussd_wallet.transaction_from = "DEBT_COLLECTION"
                        ussd_wallet.save()

                        DebtCollection.create_record(
                            user=user,
                            amount=ussd_withdrawal_balance,
                            wallet_type="USER_WINNING_WALLET",
                            debt_after=(user_debt_amount - ussd_withdrawal_balance),
                        )

                        user.recovered_debt += ussd_withdrawal_balance
                        user.debt_amount -= ussd_withdrawal_balance
                        user.save()

                        user_debt_amount -= ussd_withdrawal_balance

                    else:
                        ussd_wallet.withdrawable_available_balance -= user_debt_amount
                        ussd_wallet.transaction_from = "DEBT_COLLECTION"
                        ussd_wallet.save()

                        DebtCollection.create_record(
                            user=user,
                            amount=user_debt_amount,
                            wallet_type="USER_WINNING_WALLET",
                            debt_after=0,
                        )

                        user.recovered_debt += user_debt_amount
                        user.debt_amount -= user_debt_amount
                        user.save()

                        user_debt_amount = 0

        if user_debt_amount > 0:
            if web_wallet:
                web_user_game_play_balance = web_wallet.game_available_balance
                web_withdrawal_balance = web_wallet.withdrawable_available_balance

                if web_user_game_play_balance > 0:
                    if user_debt_amount > web_user_game_play_balance:
                        web_wallet.game_available_balance = 0
                        web_wallet.transaction_from = "DEBT_COLLECTION"
                        web_wallet.save()

                        DebtCollection.create_record(
                            user=user,
                            amount=web_user_game_play_balance,
                            wallet_type="USER_PLAY_WALLET",
                            debt_after=(user_debt_amount - web_user_game_play_balance),
                        )

                        user.recovered_debt += web_user_game_play_balance
                        user.debt_amount -= web_user_game_play_balance
                        user.save()

                        user_debt_amount -= web_user_game_play_balance

                    else:
                        web_wallet.game_available_balance -= user_debt_amount
                        web_wallet.transaction_from = "DEBT_COLLECTION"
                        web_wallet.save()

                        DebtCollection.create_record(
                            user=user,
                            amount=user_debt_amount,
                            wallet_type="USER_PLAY_WALLET",
                            debt_after=0,
                        )

                        user.recovered_debt += user_debt_amount
                        user.debt_amount -= user_debt_amount
                        user.save()

                        user_debt_amount = 0

                if user_debt_amount > 0:
                    if web_withdrawal_balance > 0:
                        if user_debt_amount > web_withdrawal_balance:
                            web_wallet.withdrawable_available_balance = 0
                            web_wallet.transaction_from = "DEBT_COLLECTION"
                            web_wallet.save()

                            DebtCollection.create_record(
                                user=user,
                                amount=web_withdrawal_balance,
                                wallet_type="USER_WINNING_WALLET",
                                debt_after=(user_debt_amount - web_withdrawal_balance),
                            )

                            user.recovered_debt += web_withdrawal_balance
                            user.debt_amount -= web_withdrawal_balance
                            user.save()

                            user_debt_amount -= web_withdrawal_balance

                        else:
                            web_wallet.withdrawable_available_balance -= user_debt_amount
                            web_wallet.transaction_from = "DEBT_COLLECTION"
                            web_wallet.save()

                            DebtCollection.create_record(
                                user=user,
                                amount=user_debt_amount,
                                wallet_type="USER_WINNING_WALLET",
                                debt_after=0,
                            )

                            user.recovered_debt += user_debt_amount
                            user.debt_amount -= user_debt_amount
                            user.save()

                            user_debt_amount = 0


def re_verify_buddy_transactions():
    from datetime import datetime

    import pytz

    now_utc = datetime.now(pytz.utc)

    tz = pytz.timezone("Africa/Lagos")
    now_utc.astimezone(tz)

    ids = [
        297458,
        297439,
        297426,
        297421,
        297407,
        297406,
        297405,
        297374,
        297367,
        297078,
        297072,
        297071,
        297070,
        297069,
        297068,
        294884,
        294371,
        293225,
        293103,
        291964,
        291829,
    ]

    un_verified_withdrawals = PayoutTransactionTable.objects.filter(id__in=ids, is_verified=False, disbursed=False)

    for record in un_verified_withdrawals:
        vfd_disbursement_helper = VfdDisbursementHelperFunc()
        verify_payout_response = vfd_disbursement_helper.verify_payout(record.payout_trans_ref)

        record.source_response_payload = verify_payout_response
        record.save()

        if isinstance(verify_payout_response, dict):
            status = verify_payout_response.get("data", {}).get("status", "")
            if status == "SUCCESSFUL":
                record.is_verified = True
                record.disbursed = True
                record.status = "SUCCESS"
                record.save()

                update_pos_lottery_winners_table(
                    ref=record.payout_trans_ref,
                    status="SUCCESSFUL",
                )


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # verify airtime purchase transactions
        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)
        reversed_commission = AgentWalletTransaction.objects.filter(transaction_from = "COMMISSION_REWARD_REVERSAL", date_created__date = current_datetime.date()).distinct("agent_wallet")
        print("coun of reversed_commission", len(reversed_commission))

        for instance in reversed_commission:
            agent_reversal_for_today = AgentWalletTransaction.objects.filter(agent_wallet = instance.agent_wallet, transaction_from = "COMMISSION_REWARD_REVERSAL", date_created__date = current_datetime.date()).order_by("id")

            if len(agent_reversal_for_today) > 1:
                ids = []
                for sub_revseral in agent_reversal_for_today:
                    ids.append(sub_revseral.id)
                
                # check the id id that is less
                min_id = max(ids)
                trans = AgentWalletTransaction.objects.get(id = min_id)
                agent_wallet = AgentWallet.objects.get(id = trans.agent_wallet.id)
                agent_wallet.commission_bal -= trans.amount
                agent_wallet.rewarded_commission -=  trans.amount
                agent_wallet.save()

                agent_wallet.refresh_from_db()

                trans.delete()



