import json

from django.core.management.base import BaseCommand

from main.models import PayoutTransactionTable


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        vfd_payout_data = PayoutTransactionTable.objects.filter(source="VFD", is_verified=False, source_unique_ref__isnull=True)
        if vfd_payout_data:
            for payout in vfd_payout_data:
                source_response = payout.source_response_payload
                try:
                    serialized_source_response = json.loads(source_response)
                except Exception:
                    print("ERROR SERIALIZING SOURCE RESPONSE")
                    print(source_response)
                    print("\n\n\n\n\n")
                    continue
                """ sample response
                {"message":"success","data":{"message":"Transaction completed successfully","amount_sent":10000.0,"escrow_id":"24618c60-75c6-439f-be71-66ba2870a7da","trans_started":"2023-01-14T18:21:34.671799","trans_stopped":"2023-01-14T18:21:38.727479","trans_time":"0:00:04.055680"},"date_completed":"2023-01-14T18:21:38.727504"}
                """

                if serialized_source_response.get("message") == "success":
                    escrow_id = serialized_source_response.get("data").get("escrow_id")

                    if escrow_id:
                        payout.source_unique_ref = escrow_id
                        payout.save()

                    else:
                        print("NO ESCROW ID FOUND")
                        print("serialized_source_response", serialized_source_response)

                else:
                    print("serialized_source_response", serialized_source_response)

        else:
            print("NO VFD PAYOUT DATA FOUND")
