from django.core.management.base import BaseCommand

from wallet_app.models import UserWallet, WebWinnersFundingTable


class Command(BaseCommand):
    help = "Fund winners wallet"

    def handle(self, *args, **kwargs):
        _winner_funding_table_qs = WebWinnersFundingTable.objects.filter(is_funded=False)

        if _winner_funding_table_qs:
            for _fund in _winner_funding_table_qs:
                user_wallet = UserWallet.objects.filter(user=_fund.user, wallet_tag="WEB").last()
                if user_wallet:
                    user_wallet.withdrawable_available_balance += _fund.amount
                    user_wallet.transaction_from = "GAME"
                    user_wallet.save()

                    _fund.is_funded = True
                    _fund.save()
