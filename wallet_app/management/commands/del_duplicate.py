from django.core.management.base import BaseCommand
from django.db.models import Sum

from wallet_app.models import UserWallet, WalletTransaction


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # handle mutable web wallets

        wallets = UserWallet.objects.filter(wallet_tag="WEB")

        if wallets:
            for wallet in wallets:
                wallet_trans = WalletTransaction.objects.filter(wallet=wallet).aggregate(Sum("amount"))["amount__sum"]
                total_trans_amount = 0 if wallet_trans is None else wallet_trans
                if wallet.game_available_balance < 1 and total_trans_amount < 1:
                    wallet.delete()
                    print(f"Deleted wallet: {wallet}")

        else:
            print("No wallets to delete")
