from datetime import datetime

import pyotp
import redis

from main.helpers.whisper_sms_managers import chnage_transaction_pin_otp_sms
from decouple import config

redis_instance = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0)


# Helper Functions
def standard_str_to_dt(date_str: str) -> datetime:
    return datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%S.%fZ")


def generate_otp(user_instance):
    """
    Generate an otp for a user
    """
    sec_key = pyotp.random_base32()
    hotp = pyotp.HOTP(sec_key)
    otp = hotp.at(1401)
    store_data = f"{otp}, {sec_key}"
    redis_instance.set(f"{user_instance.phone_number}-otp", store_data)

    # send otp to user
    name = ""
    if user_instance.first_name:
        name = user_instance.first_name

    chnage_transaction_pin_otp_sms(user_instance.phone_number, name, otp)
    return otp


def verify_otp(user_instance, otp):
    """
    Verify an otp for a user
    """
    saved_in_redis = redis_instance.get(f"{user_instance.phone_number}-otp")
    if saved_in_redis is not None:
        saved_in_redis_list = (saved_in_redis.decode("utf-8")).split(",")
        response = [i.strip() for i in saved_in_redis_list]

        sec_key = response[1]
        hotp = pyotp.HOTP(sec_key)
        otp_verify = hotp.verify(otp, 1401)
        if otp_verify:
            redis_instance.delete(f"{user_instance.phone_number}-otp")

            data = {"otp_verified": True, "message": "otp verified"}
            return data
        else:
            data = {"otp_verified": False, "message": "Invalid otp"}
            return data
    else:
        data = {"otp_verified": False, "message": "Please generate otp"}
        return data
