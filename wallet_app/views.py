import json
import uuid
from datetime import datetime

from django.contrib.auth.hashers import check_password, make_password
from django.http.response import HttpResponseRedirect
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.decorators import api_view
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from account.authentication import (
    CustomTokenAuthentication,
    IsBlackListedPermission,
    PhoneNumberVerifedPermission,
    SuperUser2Permission,
    UserDetailsPermission,
    WithdrawalPermission,
)
from account.helpers import log_save_operation
from main.api.permission import CanLogin
from main.helpers.vfd_disbursement_helper import VfdDisbursementHelperFunc
from main.models import ConstantVariable, PayoutTransactionTable, UserProfile
from main.ussd.bankdb import filter_bank
from overide_print import print
from pos_app.models import AgentWallet
from wallet_app.helpers.helper_functions import generate_otp, verify_otp
from wallet_app.helpers.payment_gateway import PaymentGateway
from wallet_app.models import (
    BeneficiaryDetail,
    DebitCreditRecord,
    FloatWallet,
    PaystackTransaction,
    UserWallet,
    WalletTransaction,
    WithdrawalPINSystem,
)
from wallet_app.serializers import (  # PlayGameSerializer,
    BeneficiaryDetailsSerializer,
    ChangeTransactionPinSerilaizer,
    ChangeTransctionPinSerializer,
    CreateBeneficiarySerializer,
    CreateTransactionPinSerializer,
    FilterTransactionSerializer,
    FundAndWithdrawalWalletSerializer,
    GenerateWithdrawalPinSerializer,
    ManuallyAgentAndUserWalletSerializer,
    PayoutReferrenceSerializer,
    UserTransactionSerializer,
    WalletSerializer,
    WithdrawalSerializer,
)
from wyse_ussd.helper.general_helper import has_enough_money_to_giveout


# Create your view(s) here.
class WalletDetailsAPIView(APIView):
    permission_classes = [
        CanLogin,
    ]

    def get(self, request):
        user = request.user
        wallet = UserWallet.objects.filter(user=user).last()
        if wallet:
            serializer = WalletSerializer(wallet)
            data = {"message": "success", "data": serializer.data}
            return Response(data=data, status=status.HTTP_200_OK)

        return Response(data={"message": "no balance"}, status=status.HTTP_200_OK)


class CreateTransactionPinAPIView(APIView):
    authentication_classes = (TokenAuthentication, CustomTokenAuthentication)
    permission_classes = (IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        serializer = CreateTransactionPinSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        pin = serializer.validated_data["pin"]

        user_instance = UserProfile.objects.filter(phone_number=request.user.phone).last()

        if not user_instance:
            return Response(
                data={"message": "user profile does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if user_instance.pin is None:
            user_instance.pin = make_password(pin)

            UserWallet.objects.update_or_create(user=user_instance, wallet_tag="WEB")

            user_instance.has_pin = True
            user_instance.save()

            return Response(
                data={"message": "transaction pin created successfully"},
                status=status.HTTP_201_CREATED,
            )

        return Response(
            data={"error": "user already has transaction pin"},
            status=status.HTTP_400_BAD_REQUEST,
        )


class FundWalletAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = (IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = FundAndWithdrawalWalletSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        amount = serializer.validated_data["amount"]

        user_profile = UserProfile.objects.filter(phone_number=user.phone).last()

        wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()

        if not wallet:
            wallet = UserWallet.objects.create(user=user_profile, wallet_tag="WEB")

        reference = f"LTT-{uuid.uuid4()}"
        payload = {
            "email": user_profile.email,
            "amount": float(amount) * 100,
            "reference": reference,
            "callback_url": "https://whisper-wyze-lotto.vercel.app/dashboard",
        }
        payment_details = PaymentGateway().paystack_link_request(**payload)

        payment_link = payment_details["authorization_url"]

        WalletTransaction.objects.create(
            wallet=wallet,
            transaction_reference=reference,
            amount=amount,
            status="pending",
        )

        # create transaction
        PaystackTransaction.objects.create(
            user=user_profile,
            amount=float(amount),
            reference=reference,
            created_at=timezone.now(),
            paid_at=timezone.now(),
            channel="WEB",
            raw_data=payload,
            payment_reason="WALLET_FUNDING",
        )

        data = {"message": "success", "payment_auth_url": payment_link}

        return Response(data=data, status=status.HTTP_200_OK)


class BeneficiaryDetailsAPIView(APIView):
    authentication_classes = [TokenAuthentication, CustomTokenAuthentication]
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        user = UserProfile.objects.filter(phone_number=request.user.phone).last()
        beneficiary = BeneficiaryDetail.objects.filter(user=user, is_active=True)
        serializer = BeneficiaryDetailsSerializer(beneficiary, many=True)

        return Response(data=serializer.data, status=status.HTTP_200_OK)

    def post(self, request, *args, **kwargs):
        user = UserProfile.objects.filter(phone_number=request.user.phone).last()
        serializer = CreateBeneficiarySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        bank_code = serializer.validated_data.get("bank_code")
        account_number = serializer.validated_data.get("account_number")
        account_name = serializer.validated_data.get("account_name")
        serializer.validated_data.get("bank_name")

        # check if beneficiary already exists
        beneficiary = BeneficiaryDetail.objects.filter(
            user=user,
            is_active=True,
            account_number=account_number,
            bank_code=bank_code,
        )
        if beneficiary.exists():
            data = {"message": "beneficiary already exists"}
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        _bank_details = filter_bank(cbn_code=bank_code)
        if _bank_details is None:
            return Response(data={"error": "bank not found"}, status=status.HTTP_400_BAD_REQUEST)

        # verify bank details with paystack
        paystack = PaymentGateway()
        verify_bank_response = paystack.fetch_account_name(account_number=account_number, bank_code=bank_code)
        if isinstance(verify_bank_response, dict):
            if verify_bank_response.get("status") is False:
                data = {"message": "Invalid bank details"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        BeneficiaryDetail.objects.create(
            user=user,
            bank_code=bank_code,
            account_number=account_number,
            account_name=account_name,
            logo=_bank_details.get("logo"),
            bank_name=_bank_details.get("name"),
        )

        return Response(
            data={"message": "success", "data": serializer.data},
            status=status.HTTP_201_CREATED,
        )


@api_view(["POST", "GET"])
def paystackpayment(request):
    print("request method", request.method)
    if request.method == "POST":
        data = request.data
        data["raw_data"] = json.dumps(data)

        # check of this webhook is for lottery payment
        trans_ref = data.get("data").get("reference")
        if trans_ref is not None:
            # Whyse cash lottery Payment
            get_transaction = PaystackTransaction.objects.filter(reference=trans_ref, is_verified=False).last()

            if not get_transaction:
                return Response(status=status.HTTP_200_OK)

            if get_transaction.payment_reason == "LOTTERY_PAYMENT":
                # call verify lottery and spread payment function
                response = PaystackTransaction.verify_lottery_payment(get_transaction, data)

                return Response(data=response, status=status.HTTP_200_OK)

            elif get_transaction.payment_reason == "MOBILE_LOTTERY_PAYMENT":
                response = PaystackTransaction.verify_lottery_payment(get_transaction, data, payment_for="MOBILE_LOTTERY_PAYMENT")

                return Response(data=response, status=status.HTTP_200_OK)

            elif get_transaction.payment_reason == "MOBILE_AGENT_FUNDING":
                response = AgentWallet.agent_wallet_funding_via_paystack(get_transaction, data)

            elif get_transaction.payment_reason == "WALLET_FUNDING":
                response = PaystackTransaction.fund_play_via_paystack(get_transaction, data)

                return Response(data=response, status=status.HTTP_200_OK)

            else:
                email = data.get("data").get("customer").get("email")
                user = UserProfile.objects.get(email=email)
                PaystackTransaction.create_transaction(user=user, data=data, raw_data=str(request.data))

    else:
        return HttpResponseRedirect(redirect_to="https://whispersms.com")  # Testing; to be changed

    return Response(status=status.HTTP_200_OK)


method_decorator(csrf_exempt, name="dispatch")


class ChangeTransactionPinView(APIView):
    authentication_classes = (TokenAuthentication, CustomTokenAuthentication)
    permission_classes = (IsAuthenticated,)

    serializer_class = ChangeTransctionPinSerializer

    def post(self, request):
        serializer = ChangeTransctionPinSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_profile = UserProfile.objects.get(phone_number=request.user.phone)
        if not user_profile:
            return Response(
                data={"message": "user profile not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        pin = serializer.validated_data["old_pin"]
        if user_profile.pin is None:
            data = {
                "status": status.HTTP_400_BAD_REQUEST,
                "message": "You have not set a pin yet",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        elif check_password(pin, user_profile.pin) is False:
            data = {"status": status.HTTP_400_BAD_REQUEST, "message": "Incorrect pin"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        else:
            user_profile.pin = make_password(serializer.validated_data.get("new_pin"))
            user_profile.save()
        return Response(data={"message": "changed successful"}, status=status.HTTP_200_OK)


class TransactionHistoryAPIView(APIView):
    authentication_classes = (TokenAuthentication, CustomTokenAuthentication)
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        user = request.user
        user_profile = UserProfile.objects.filter(phone_number=user.phone).last()

        if not user_profile:
            return Response(
                data={"message": "user profile not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = UserTransactionSerializer(instance=user_profile)
        return Response(data=serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        """
        Filter user Transaction history
        """
        user = request.user
        user_profile = UserProfile.objects.filter(phone_number=user.phone).last()

        if not user_profile:
            return Response(
                data={"message": "user profile not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = FilterTransactionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        start_date = serializer.validated_data.get("start_date")
        end_date = serializer.validated_data.get("end_date")
        transaction_status = serializer.validated_data.get("status")
        method = serializer.validated_data.get("method")

        filtered_transaction = WalletTransaction.filter_transaction_history(
            user=user_profile,
            start_date=start_date,
            end_date=end_date,
            status=transaction_status,
            method=method,
            req=request,
        )
        return Response(data=filtered_transaction, status=status.HTTP_200_OK)


class ForgotTransactionPinAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        """
        Generate and Send OTP to user's phone number
        """

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()
        user = user_profile
        if user.pin is None:
            data = {
                "status": status.HTTP_400_BAD_REQUEST,
                "message": "You have not set a pin yet",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        generate_otp(user)
        data = {
            "status": status.HTTP_200_OK,
            "message": "OTP has been sent to your phone number",
        }
        return Response(data=data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = ChangeTransactionPinSerilaizer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = request.user
        otp = serializer.validated_data["otp"]

        is_otp_verified = verify_otp(user, otp)
        if is_otp_verified.get("otp_verified") is True:
            user.pin = None
            user.has_pin = False
            user.save()
            data = {"status": status.HTTP_200_OK, "message": "Pin has been reset"}

            return Response(data=data, status=status.HTTP_200_OK)
        else:
            data = {
                "status": status.HTTP_400_BAD_REQUEST,
                "message": is_otp_verified.get("message"),
            }
            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)


class WithdrawalView(APIView):
    authentication_classes = [TokenAuthentication, CustomTokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        WithdrawalPermission,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
        UserDetailsPermission,
    ]

    def post(self, request):
        serializer = WithdrawalSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        amount = serializer.validated_data.get("amount")
        # account_name = serializer.validated_data.get("account_name")
        account_number = serializer.validated_data.get("account_number")
        serializer.validated_data.get("bank_name")
        bank_code = serializer.validated_data.get("bank_code")

        user_profile = UserProfile.objects.filter(phone_number=request.user.phone).last()

        if not user_profile:
            return Response(
                data={"message": "user profile not found"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        pin = serializer.validated_data.get("pin")

        # validate user pin
        if user_profile.pin is None or user_profile.pin == "":
            data = {
                "status": status.HTTP_400_BAD_REQUEST,
                "message": "You have not set a pin yet",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        if check_password(pin, user_profile.pin) is False:
            data = {"status": status.HTTP_400_BAD_REQUEST, "message": "Wrong pin"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if amount < 50:
            data = {"message": "Amount must be greater than ₦50"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # user wallet
        _user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
        if _user_wallet is None:
            data = {"message": "You have no withdrawal wallet"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if amount > _user_wallet.withdrawable_available_balance:
            data = {"message": "Insufficient balance in your withdrawable wallet"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # verify if user has referred someone
        # user_testimonial = TestimonalTable.objects.filter(user=user_profile).last()
        # if user_testimonial is None:
        #     data = {"message": "You have not referred anyone"}
        #     return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # -------- CHECK FOR PAYOUT STATUS -------------
        if ConstantVariable.get_constant_variable().get("payout_source") == "NOT_AVAILABLE":
            data = {
                "message": "Payout is not available at the moment",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        # check general wallet and make sure we've the amount tto give out
        if has_enough_money_to_giveout(user_profile.phone_number, amount) is False:
            data = {"message": "We're sorry, an error occured while processing your request. Please try again later"}

            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        # VERIFY BANK DETAILS
        print(
            f"""
        WEB WITHDRAWAL:
        account_number: {account_number}
        bank_code: {bank_code}
        \n\n\n\n
        """
        )

        paystack = PaymentGateway()

        verify_bank_response = paystack.fetch_account_name(account_number=account_number, bank_code=bank_code)
        if isinstance(verify_bank_response, dict):
            if verify_bank_response.get("status") is False:
                data = {"message": "Invalid bank details"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            name = verify_bank_response.get("data").get("account_name")
            "withdraw-{}".format(uuid.uuid4())

            _filter_bank_details = filter_bank(cbn_code=bank_code)
            if _filter_bank_details is None:
                data = {"message": "Invalid bank details"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            user_wallet = UserWallet.objects.filter(user=user_profile, wallet_tag="WEB").last()
            if user_wallet.withdrawable_available_balance < float(amount):
                data = {"message": "Insufficient balance"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # CHECK BEFORE DISURSING IF WE HAVE ENOUGH TO GIVE OUT
            if has_enough_money_to_giveout(user_profile.phone_number, float(amount)) is False:
                data = {"message": "We're sorry, an error occured while processing your request. Please try again later"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if FloatWallet.get_float_wallet(source="VFD").amount < float(amount):
                data = {"message": "We're sorry, an error occured while processing your request. Please try again later"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            wallet_payload = {
                "transaction_from": "WITHDRAWAL",
            }

            debit_credit_record = DebitCreditRecord.create_record(
                phone_number=user_wallet.user.phone_number,
                amount=float(amount),
                channel="WEB",
                reference=f"{uuid.uuid4()}{datetime.now().timestamp()}",
                transaction_type="DEBIT",
            )

            UserWallet.deduct_wallet(
                user=user_wallet.user,
                amount=int(amount),
                channel="WEB",
                transaction_id=debit_credit_record.reference,
                user_wallet_type="WINNINGS_WALLET",
                **wallet_payload,
            )

            if ConstantVariable.get_constant_variable().get("payout_source") == "WOVEN":
                PayoutTransactionTable().user_woven_disbursement(
                    amount=amount,
                    user_profile_instance=user_profile,
                    user_id=user_profile.id,
                    bank_name=_filter_bank_details.get("name"),
                    account_number=account_number,
                )

            elif (
                ConstantVariable.get_constant_variable().get("payout_source") == "VFD"
                or ConstantVariable.get_constant_variable().get("payout_source") == "BUDDY"
            ):
                PayoutTransactionTable().user_vfd_payout(
                    amount=amount,
                    user_profile_instance=user_profile,
                    user_id=user_profile.id,
                    bank_name=_filter_bank_details.get("name"),
                    account_number=account_number,
                )

            if serializer.validated_data.get("save_beneficiary") is True:
                # check if beneficiary already exists
                beneficiary = BeneficiaryDetail.objects.filter(
                    user=request.user,
                    is_active=True,
                    account_number=account_number,
                    bank_code=bank_code,
                )
                if beneficiary.exists():
                    pass

                else:
                    _bank_details = filter_bank(cbn_code=bank_code)
                    if _bank_details is None:
                        pass
                    else:
                        BeneficiaryDetail.objects.create(
                            user=user_profile,
                            bank_code=bank_code,
                            account_number=account_number,
                            account_name=name,
                            logo=_bank_details.get("logo"),
                            bank_name=_bank_details.get("name"),
                        )

            data = {"message": "Withdrawal initiated"}
            return Response(data, status=status.HTTP_200_OK)

        else:
            data = {"message": "Invalid bank details"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class LibertyAgencyVFDPayoutVerification(APIView):
    serializer_class = PayoutReferrenceSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        reference = serializer.validated_data.get("reference")

        liberty_agency_vfd_helper = VfdDisbursementHelperFunc()
        response = liberty_agency_vfd_helper.verify_payout(reference=reference)

        return Response(response, status=status.HTTP_200_OK)


class ManuallyUpdateAgentAndUserWallet(APIView):
    permission_classes = [IsAuthenticated, SuperUser2Permission]

    serializer_class = ManuallyAgentAndUserWalletSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        amount = serializer.validated_data.get("amount")
        user_type = serializer.validated_data.get("user_type")
        wallet_type = serializer.validated_data.get("wallet_type")
        phone_number = serializer.validated_data.get("phone_number")

        user_instantance = request.user

        if user_type == "WEB/USSD_USER":
            user_wallet = UserWallet.objects.filter(user__phone_number=phone_number, wallet_tag="WEB").first()
            if user_wallet is None:
                return Response(
                    data={"message": "User wallet not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if wallet_type == "WINNINGS_WALLET":
                user_wallet.withdrawable_available_balance = amount
                user_wallet.save()

                # save in log entry
                if user_instantance is not None:
                    log_save_operation(user_instantance, user_wallet)

                return Response(data={"message": "success"}, status=status.HTTP_200_OK)
            else:
                user_wallet.game_available_balance = amount
                user_wallet.save()

                if user_instantance is not None:
                    log_save_operation(user_instantance, user_wallet)

                return Response(data={"message": "success"}, status=status.HTTP_200_OK)

        elif user_type == "MOBILE/POS_USER":
            agent_wallet = AgentWallet.objects.filter(agent__phone=phone_number).first()
            if agent_wallet is None:
                return Response(
                    data={"message": "Agent wallet not found"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if wallet_type == "WINNINGS_WALLET":
                agent_wallet.winnings_bal = amount
                agent_wallet.save()

                if user_instantance is not None:
                    log_save_operation(user_instantance, agent_wallet)

                return Response(data={"message": "success"}, status=status.HTTP_200_OK)

            else:
                agent_wallet.game_play_bal = amount
                agent_wallet.save()

                if user_instantance is not None:
                    log_save_operation(user_instantance, agent_wallet)

                return Response(data={"message": "success"}, status=status.HTTP_200_OK)

        else:
            return Response(
                data={"message": "Invalid user type"},
                status=status.HTTP_400_BAD_REQUEST,
            )


class GenerateWithdrawalPin(APIView):
    serializer_class = GenerateWithdrawalPinSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        phone_number = serializer.validated_data.get("phone_number")
        game_type = serializer.validated_data.get("game_type")
        WithdrawalPINSystem.create_otp_instance(phone_number=phone_number, game_type=game_type)
        data = {
            "status": "success",
            "message": "OTP was created successfully.",
            # "otp_str": otp_str
        }
        return Response(data=data, status=status.HTTP_200_OK)
