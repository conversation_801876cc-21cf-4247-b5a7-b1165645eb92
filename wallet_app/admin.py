from django.conf import settings
from django.contrib import admin
from import_export import fields, resources
from import_export.admin import ImportExportModelAdmin
from import_export.widgets import ForeignKeyWidget

from wallet_app.models import (
    BankTransferFunding,
    BeneficiaryDetail,
    CommissionWallet,
    CommissionWalletTransaction,
    CreditCardDetail,
    DebitCreditRecord,
    DebtCollection,
    DebtRecovery,
    DebtRecoveryTransaction,
    FloatWallet,
    GeneralWithdrawableWallet,
    GeneralWithdrawableWalletTransaction,
    IllusionWallet,
    IllusionWalletTransaction,
    PaystackTransaction,
    RtoWallet,
    RtoWalletTransaction,
    ServiceChargeWallet,
    ServiceChargeWalletTransaction,
    UserWallet,
    VirtualSoccerExcessWallet,
    VirtualSoccerExcessWalletTransaction,
    WalletTransaction,
    WithdrawalPINSystem,
)
from wyse_ussd.models import LotteryGameNames


class BeneficiaryModelResource(resources.ModelResource):
    class Meta:
        model = BeneficiaryDetail


class CreditCardDetailsModelResource(resources.ModelResource):
    class Meta:
        model = CreditCardDetail


class PaystackTransactionsModelResource(resources.ModelResource):
    class Meta:
        model = PaystackTransaction


class UserWalletModelResource(resources.ModelResource):
    class Meta:
        model = UserWallet


class WalletTransactionsModelResource(resources.ModelResource):
    user = fields.Field(
        column_name="wallet",
        attribute="wallet",
        widget=ForeignKeyWidget(UserWallet, "user__id"),
    )

    class Meta:
        model = WalletTransaction


class GeneralWithdrawableWalletTransactionModelResource(resources.ModelResource):
    class Meta:
        model = GeneralWithdrawableWalletTransaction


class GeneralWithdrawableWalletModelResource(resources.ModelResource):
    class Meta:
        model = GeneralWithdrawableWallet


class BankTransferFundingModelResource(resources.ModelResource):
    class Meta:
        model = BankTransferFunding


class FloatWalletModelResource(resources.ModelResource):
    class Meta:
        model = FloatWallet


class CommissionWalletModelResource(resources.ModelResource):
    class Meta:
        model = CommissionWallet


class CommissionWalletTransactionModelResource(resources.ModelResource):
    class Meta:
        model = CommissionWalletTransaction


class RtoWalletModelResource(resources.ModelResource):
    class Meta:
        model = RtoWallet


class RtoWalletTransactionModelResource(resources.ModelResource):
    class Meta:
        model = RtoWalletTransaction


class VirtualSoccerExcessWalletModelResource(resources.ModelResource):
    class Meta:
        model = VirtualSoccerExcessWallet


class VirtualSoccerExcessWalletTransactionModelResource(resources.ModelResource):
    class Meta:
        model = VirtualSoccerExcessWalletTransaction


class IllusionWalletModelResource(resources.ModelResource):
    class Meta:
        model = IllusionWallet


class IllusionWalletTransactionModelResource(resources.ModelResource):
    class Meta:
        model = IllusionWalletTransaction


class DebtCollectionModelResource(resources.ModelResource):
    class Meta:
        model = DebtCollection


class DebitCreditRecordModelResource(resources.ModelResource):
    class Meta:
        model = DebitCreditRecord


class ServiceChargeWalletModelResource(resources.ModelResource):
    class Meta:
        model = ServiceChargeWallet


class ServiceChargeWalletTransactionModelResource(resources.ModelResource):
    class Meta:
        model = ServiceChargeWalletTransaction


class WithdrawalPINSystemResource(resources.ModelResource):
    class Meta:
        model = WithdrawalPINSystem


class LotteryGameNamesResource(resources.ModelResource):
    class Meta:
        model = LotteryGameNames


class DebtRecoveryResource(resources.ModelResource):
    class Meta:
        model = DebtRecovery


class DebtRecoveryTransactionResource(resources.ModelResource):
    class Meta:
        model = DebtRecoveryTransaction


class BeneficiaryDetailsResourceAdmin(ImportExportModelAdmin):
    resource_class = BeneficiaryModelResource
    search_fields = ["user", "bank_name", "account_number", "account_name"]
    list_filter = ("created_at",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CreditCardDetailsResourceAdmin(ImportExportModelAdmin):
    resource_class = CreditCardDetailsModelResource
    search_fields = ["user", "bank_name", "account_name", "card_type", "country_code"]
    list_filter = ("date_created",)
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PaystackTransactionsResourceAdmin(ImportExportModelAdmin):
    resource_class = PaystackTransactionsModelResource
    search_fields = ["user__phone_number", "reference", "channel", "mobile"]
    list_filter = ("received_at", "is_verified")
    date_hierarchy = "received_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class UserWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = UserWalletModelResource
    autocomplete_fields = [
        "user",
    ]
    search_fields = [
        "user__phone_number",
    ]
    list_filter = ("wallet_tag",)
    raw_id_fields = ["user", "woven_account"]

    date_hierarchy = "created_at"

    if settings.DEBUG is False:
        readonly_fields = ("withdrawable_available_balance", "game_available_balance")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WalletTransactionsResourceAdmin(ImportExportModelAdmin):
    resource_class = WalletTransactionsModelResource
    search_fields = ["wallet__user__phone_number", "transaction_reference"]
    list_filter = ("transaction_from", "general_action_status")
    raw_id_fields = [
        "wallet",
    ]
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        fields = [field.name for field in self.model._meta.concrete_fields] + ["user"]
        fields.remove("method")
        return fields


# class WithdrawalTableModelResource(resources.ModelResource):
#     class Meta:
#         model = WithdrawalTable


# class WithdrawalTableResourceAdmin(ImportExportModelAdmin):
#     resource_class = WithdrawalTableModelResource
#     search_fields = ["phone", "payout_trans_ref", "name", "source_unique_ref"]
#     list_filter = ("source",)
#     date_hierarchy = "date_added"

#     def get_list_display(self, request):
#         return [field.name for field in self.model._meta.concrete_fields]


class GeneralWithdrawableWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = GeneralWithdrawableWalletModelResource
    # search_fields = ["amount", "payout_trans_ref", "name", "source_unique_ref"]
    # list_filter = ("source",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class GeneralWithdrawableWalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = GeneralWithdrawableWalletTransactionModelResource
    search_fields = [
        "phone_number",
        "game_type",
    ]
    list_filter = ("transaction_type",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class BankTransferFundingResourceAdmin(ImportExportModelAdmin):
    resource_class = BankTransferFundingModelResource
    search_fields = [
        "phone",
    ]
    list_filter = ("status",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class FloatWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = FloatWalletModelResource
    # search_fields = [
    #     "phone",
    # ]
    list_filter = ("source",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CommissionWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = CommissionWalletModelResource
    # search_fields = [
    #     "phone",
    # ]
    # list_filter = ("transaction_type",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class CommissionWalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = CommissionWalletTransactionModelResource
    # search_fields = [
    #     "phone",
    # ]
    list_filter = ("transaction_type",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RtoWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = RtoWalletModelResource
    # search_fields = [
    #     "phone",
    # ]
    # list_filter = ("transaction_type",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class RtoWalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = RtoWalletTransactionModelResource
    # search_fields = [
    #     "phone",
    # ]
    list_filter = ("transaction_type", "type_of_rto")
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class VirtualSoccerExcessWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = VirtualSoccerExcessWalletModelResource
    # search_fields = [
    #     "phone",
    # ]
    list_filter = ("created_at",)
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class VirtualSoccerExcessWalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = VirtualSoccerExcessWalletTransactionModelResource
    search_fields = [
        "phone",
    ]
    list_filter = ["created_at", "transaction_type"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class IllusionWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = IllusionWalletModelResource
    # search_fields = [
    #     "phone",
    # ]
    # list_filter = ["created_at","transaction_type"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class IllusionWalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = IllusionWalletTransactionModelResource
    search_fields = [
        "phone_number",
    ]
    list_filter = ["created_at", "game_type", "transaction_type"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DebtCollectionResourceAdmin(ImportExportModelAdmin):
    resource_class = DebtCollectionModelResource
    search_fields = [
        "user__phone_number",
    ]
    list_filter = ["created_at", "wallet_type"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DebitCreditRecordResourceAdmin(ImportExportModelAdmin):
    resource_class = DebitCreditRecordModelResource
    search_fields = ["reference", "phone_number"]
    list_filter = ["created_at", "transaction_type", "status"]
    date_hierarchy = "created_at"

    if settings.DEBUG is False:
        readonly_fields = ("amount",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ServiceChargeWalletResourceAdmin(ImportExportModelAdmin):
    resource_class = ServiceChargeWalletModelResource

    list_filter = ["created_at", "service"]
    date_hierarchy = "created_at"

    if settings.DEBUG is False:
        readonly_fields = ("amount", "previous_balance")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ServiceChargeWalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = ServiceChargeWalletTransactionModelResource

    list_filter = ["created_at", "service", "transaction_type"]
    date_hierarchy = "created_at"

    if settings.DEBUG is False:
        readonly_fields = ("amount", "previous_balance", "balance_after")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WithdrawalPINSystemResourceAdmin(ImportExportModelAdmin):
    resource_class = WithdrawalPINSystemResource

    list_filter = ["used", "is_active"]
    date_hierarchy = "created_at"

    if settings.DEBUG is False:
        readonly_fields = ("otp",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class LotteryGameNamesResourceAdmin(ImportExportModelAdmin):
    resource_class = LotteryGameNamesResource

    # list_filter = ["used", "is_active"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DebtRecoveryResourceAdmin(ImportExportModelAdmin):
    resource_class = DebtRecoveryResource

    list_filter = ["created_at", "channel", "completed"]

    search_fields = ["full_name", "phone_number"]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class DebtRecoveryTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = DebtRecoveryTransactionResource

    list_filter = ["created_at", "channel"]

    search_fields = ["full_name", "phone_number"]

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(BeneficiaryDetail, BeneficiaryDetailsResourceAdmin)
admin.site.register(CreditCardDetail, CreditCardDetailsResourceAdmin)
admin.site.register(PaystackTransaction, PaystackTransactionsResourceAdmin)
admin.site.register(UserWallet, UserWalletResourceAdmin)
admin.site.register(WalletTransaction, WalletTransactionsResourceAdmin)
# admin.site.register(WithdrawalTable, WithdrawalTableResourceAdmin)
admin.site.register(GeneralWithdrawableWallet, GeneralWithdrawableWalletResourceAdmin)
admin.site.register(
    GeneralWithdrawableWalletTransaction,
    GeneralWithdrawableWalletTransactionResourceAdmin,
)
admin.site.register(
    BankTransferFunding,
    BankTransferFundingResourceAdmin,
)
admin.site.register(
    FloatWallet,
    FloatWalletResourceAdmin,
)
admin.site.register(
    CommissionWallet,
    CommissionWalletResourceAdmin,
)
admin.site.register(
    CommissionWalletTransaction,
    CommissionWalletTransactionResourceAdmin,
)
admin.site.register(
    RtoWallet,
    RtoWalletResourceAdmin,
)

admin.site.register(
    RtoWalletTransaction,
    RtoWalletTransactionResourceAdmin,
)
admin.site.register(
    VirtualSoccerExcessWallet,
    VirtualSoccerExcessWalletResourceAdmin,
)
admin.site.register(
    VirtualSoccerExcessWalletTransaction,
    VirtualSoccerExcessWalletTransactionResourceAdmin,
)

admin.site.register(
    IllusionWallet,
    IllusionWalletResourceAdmin,
)

admin.site.register(
    IllusionWalletTransaction,
    IllusionWalletTransactionResourceAdmin,
)

# admin.site.register(
#     DebtCollection,
#     DebtCollectionResourceAdmin,
# )

admin.site.register(
    DebitCreditRecord,
    DebitCreditRecordResourceAdmin,
)

admin.site.register(
    ServiceChargeWallet,
    ServiceChargeWalletResourceAdmin,
)

admin.site.register(
    ServiceChargeWalletTransaction,
    ServiceChargeWalletTransactionResourceAdmin,
)
admin.site.register(
    WithdrawalPINSystem,
    WithdrawalPINSystemResourceAdmin,
)
admin.site.register(
    LotteryGameNames,
    LotteryGameNamesResourceAdmin,
)
admin.site.register(
    DebtRecovery,
    DebtRecoveryResourceAdmin,
)
admin.site.register(
    DebtRecoveryTransaction,
    DebtRecoveryTransactionResourceAdmin,
)
