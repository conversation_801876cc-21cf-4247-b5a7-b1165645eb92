from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver

from account.models import User
from main.models import (
    LotteryModel,
    LotteryWinnersTable,
    LottoTicket,
    LottoWinners,
    UserProfile,
)
from main.tasks import create_wema_collection_account
from wallet_app.models import UserWallet
from wyse_ussd.models import SoccerPrediction


@receiver(pre_delete, sender=User)
def delete_user_object_related_wallet(sender, instance: User, **kwargs):
    # delete user wallet
    UserWallet.objects.filter(user__phone_number=instance.phone).delete()

    # delete user lottery data
    LotteryModel.objects.filter(user_profile__phone_number=instance.phone).delete()

    LottoTicket.objects.filter(user_profile__phone_number=instance.phone).delete()

    SoccerPrediction.objects.filter(user_profile__phone_number=instance.phone).delete()

    # delete user winnings
    LotteryWinnersTable.objects.filter(phone_number=instance.phone).delete()

    LottoWinners.objects.filter(phone_number=instance.phone).delete()

    # delete user profile
    UserProfile.objects.filter(phone_number=instance.phone).delete()


@receiver(post_save, sender=UserWallet)
def calculate_total_accumulated_on_batch(sender, instance: UserWallet, created, **kwargs):
    if created:
        create_wema_collection_account.delay(phone_number=instance.user.phone_number)
