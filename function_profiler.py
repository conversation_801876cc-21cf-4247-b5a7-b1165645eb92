import time


def time_execution_decorator(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()  # Record start time
        result = func(*args, **kwargs)
        end_time = time.time()  # Record end time

        # Calculate and print the function name and time taken
        print(f"\n\n\n\n\nFunction '{func.__name__}' executed in {end_time - start_time:.4f} seconds\n\n\n\n\n")

        return result

    return wrapper
