import uuid
from datetime import datetime

import pytz
import redis
import requests
from dateutil.relativedelta import relativedelta
from decouple import config
from django.conf import settings
from django.db import models

from retail_metrics.models import RetailWalletTransactions


# Create your models here.
class Wallet(models.Model):
    WALLET_TYPE = [
        ("AGENT_FUNDING_WALLET", "AGENT_FUNDING_WALLET"),
        ("NON_RETAIL_WALLET", "NON_RETAIL_WALLET"),
        ("RETAIL_RTP_WALLET", "RETAIL_RTP_WALLET"),
        ("GHANA_RTP_WALLET", "<PERSON><PERSON>NA_RTP_WALLET"),
        ("MOVEABLE_GHANA_RTP_WALLET", "MOVEABLE_GHANA_RTP_WALLET"),
        ("GHANA_RTO_WALLET", "GHANA_RTO_WALLET"),
        ("RTO_WALLET", "RTO_WALLET"),
        ("COMMISSION", "COMMISSION"),
        ("PENDING_LATE_WITHDRAWAL_WALLET", "PENDING_LATE_WITHDRAWAL_WALLET"),
        ("PRE_FUNDING_WALLET", "PRE_FUNDING_WALLET"),
        ("EXCESS_WALLET", "EXCESS_WALLET"),
    ]

    balance = models.FloatField(default=0.0)
    wallet_phone_number = models.CharField(max_length=20, null=True, blank=True)
    previous_balance = models.FloatField(default=0.0)
    wallet_type = models.CharField(max_length=50, choices=WALLET_TYPE)
    user_id = models.CharField(max_length=50, null=True, blank=True)
    vfd_account_number = models.CharField(max_length=50, null=True, blank=True)
    wema_account_number = models.CharField(max_length=50, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.wallet_type} wallet"

    def get_duration(self):
        """Calculate the duration since the user was created."""
        if not self.created_at:
            return "Unknown"

        current_time = datetime.now()
        created_time = self.created_at.replace(tzinfo=None)  # Ensure timezone compatibility
        delta = relativedelta(current_time, created_time)

        duration_parts = []
        if delta.years:
            duration_parts.append(f"{delta.years} year{'s' if delta.years > 1 else ''}")
        if delta.months:
            duration_parts.append(f"{delta.months} month{'s' if delta.months > 1 else ''}")

        return ", ".join(duration_parts) if duration_parts else "Less than a month"

    class Meta:
        verbose_name = "WALLET"
        verbose_name_plural = "WALLETS"

    @classmethod
    def get_retail_rtp_balance(cls):
        try:
            wallet = Wallet.objects.get(wallet_type="RETAIL_RTP_WALLET")
        except Wallet.DoesNotExist:
            wallet = Wallet.objects.create(wallet_type="RETAIL_RTP_WALLET")
        return wallet.balance

    @classmethod
    def get_non_retail_wallet_balance(cls):
        try:
            wallet = Wallet.objects.get(wallet_type="NON_RETAIL_WALLET")
        except Wallet.DoesNotExist:
            wallet = Wallet.objects.create(wallet_type="NON_RETAIL_WALLET")
        return wallet.balance

    @classmethod
    def fund_wallet(
        cls,
        wallet_type,
        amount,
        game_type=None,
        is_reversal=False,
        is_vertical_lead_commission=False,
        is_supervisor_commission=False,
    ):
        if amount <= 0:
            return None

        if wallet_type == "RETAIL_COMMISSION_WALLET":
            wallet_type = "COMMISSION"

        try:
            wallet = Wallet.objects.get(wallet_type=wallet_type)
        except Wallet.DoesNotExist:
            wallet = Wallet.objects.create(wallet_type=wallet_type)

        amount = round(amount, 2)

        balance_before = wallet.balance
        balance_after = balance_before + amount

        wallet.previous_balance = round(balance_before, 2)
        wallet.balance = round(balance_after, 2)
        wallet.save()

        # create wallet transaction
        WalletTransaction.objects.create(
            wallet=wallet,
            wallet_type=wallet_type,
            amount=amount,
            previous_balance=balance_before,
            current_balance=balance_after,
            transaction_type="CREDIT",
            game_type=game_type,
            is_reversal=is_reversal,
            is_vertical_lead_commission=is_vertical_lead_commission,
            is_supervisor_commission=is_supervisor_commission,
        )
        return wallet

    @classmethod
    def debit_wallet(cls, wallet_type, amount, game_type=None, user_phone=None, user_name=None, game_play_id=None):
        return
        if amount <= 0:
            return None
        try:
            wallet = Wallet.objects.get(wallet_type=wallet_type)
        except Wallet.DoesNotExist:
            return None

        amount = round(amount, 2)

        balance_before = wallet.balance
        balance_after = balance_before - amount

        wallet.previous_balance = balance_before
        wallet.balance = balance_after
        wallet.save()

        # create wallet transaction
        WalletTransaction.objects.create(
            wallet=wallet,
            wallet_type=wallet_type,
            amount=amount,
            previous_balance=balance_before,
            current_balance=balance_after,
            transaction_type="DEBIT",
            game_type=game_type,
            user_phone=user_phone,
            user_name=user_name,
            game_play_id=game_play_id,
        )
        return wallet

    @classmethod
    def wallet_to_wallet_transfer(cls, amount, from_wallet_type, to_wallet_type):

        from pos_app.models import AgencyBankingToken

        if amount <= 0:
            return False, "Amount must be greater than 0"

        if from_wallet_type == to_wallet_type:
            return False, "Wallet types must be different"

        if from_wallet_type == "RTO_WALLET":
            if to_wallet_type == "RETAIL_RTP_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "*************"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM RTO WALLET TO RETAIL RTP WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_RTO_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RTO_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"rto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type=from_wallet_type,
                            to_wallet_type=to_wallet_type,
                        )

                    res = response.json()

                    if res.get("message") == "success":
                        try:
                            RetailWalletTransactions.create_credit_record_for_payout(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                        try:
                            RetailWalletTransactions.create_debit_record_for_rto_wallet(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

        if from_wallet_type == "RTO_WALLET":
            if to_wallet_type == "PRE_FUNDING_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "2349115030289"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM RTO WALLET TO PRE FUNDING WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_RTO_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RTO_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"rto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type=from_wallet_type,
                            to_wallet_type=to_wallet_type,
                        )

                    res = response.json()

                    if res.get("message") == "success":

                        try:
                            RetailWalletTransactions.create_debit_record_for_rto_wallet(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

        if from_wallet_type == "PRE_FUNDING_WALLET":
            if to_wallet_type == "RTO_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "*************"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM PRE FUNDING WALLET TO RTO WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("PRE_FUNDING_WALLET_PIN")

                token = AgencyBankingToken.retrieve_token("PRE_FUNDING_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"rto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type=from_wallet_type,
                            to_wallet_type=to_wallet_type,
                        )

                    res = response.json()

                    if res.get("message") == "success":

                        try:
                            RetailWalletTransactions.create_credit_record_for_rto_wallet(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

        if from_wallet_type == "RETAIL_RTP_WALLET":
            if to_wallet_type == "RTO_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "*************"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM RETAIL RTP WALLET TO RTO WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RETAIL_RTP_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"rto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type=from_wallet_type,
                            to_wallet_type=to_wallet_type,
                        )

                    res = response.json()

                    if res.get("message") == "success":
                        try:
                            RetailWalletTransactions.create_credit_record_for_rto_wallet(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                        try:
                            RetailWalletTransactions.create_debit_record_for_payout(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

            elif to_wallet_type == "AGENT_FUNDING_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "*************"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM RTO WALLET TO RETAIL FUNDING WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_RTO_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RTO_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"rto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type=from_wallet_type,
                            to_wallet_type=to_wallet_type,
                        )

                    res = response.json()

                    if res.get("message") == "success":
                        try:
                            RetailWalletTransactions.create_debit_record_for_rto_wallet(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

        elif from_wallet_type == "COMMISSION_WALLET":
            if to_wallet_type == "AGENT_FUNDING_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "*************"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "COMMISSION WALLET TO RETAIL FUNDING WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_RETAIL_COMMISSION_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RETAIL_COMMISSION_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"agency_banking_retail_commission_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type="AGENT_FUNDING_WALLET",
                            to_wallet_type="RETAIL_RTP_WALLET",
                        )

                    return True, response.json()
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

        elif from_wallet_type == "GHANA_RTP_WALLET":
            if to_wallet_type == "RTO_WALLET":

                phone = "*************"

                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM GHANA RTP WALLET TO RTO WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_GHANA_LOTTO_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("GHANA_RTP_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"ghana_lotto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type="GHANA_RTP_WALLET",
                            to_wallet_type="RTO_WALLET",
                        )

                    res = response.json()

                    if res.get("message") == "success":
                        try:
                            RetailWalletTransactions.create_credit_record_for_rto_wallet(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, response.res
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

        elif from_wallet_type == "AGENT_FUNDING_WALLET":
            if to_wallet_type == "RTO_WALLET":

                phone = "*************"

                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM FUNDING WALLET TO RTO WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"ghana_lotto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type="GHANA_RTP_WALLET",
                            to_wallet_type="RTO_WALLET",
                        )

                    res = response.json()

                    if res.get("message") == "success":
                        try:
                            RetailWalletTransactions.create_credit_record_for_rto_wallet(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res

                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

            elif to_wallet_type == "PENDING_LATE_WITHDRAWAL_WALLET":

                phone = "2349067696234"

                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM FUNDING WALLET TO LATE WITHDRAWAL WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"ghana_lotto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type="GHANA_RTP_WALLET",
                            to_wallet_type="RTO_WALLET",
                        )

                    return True, response.json()
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

            elif to_wallet_type == "RETAIL_RTP_WALLET":

                phone = "*************"

                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM GHANA RTP WALLET TO RTO WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("VFD_DEFAULT_ACCOUNT")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"ghana_lotto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type="GHANA_RTP_WALLET",
                            to_wallet_type="RTO_WALLET",
                        )

                    res = response.json()

                    if res.get("message") == "success":
                        try:
                            RetailWalletTransactions.create_credit_record_for_payout(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res

                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

        elif from_wallet_type == "EXCESS_WALLET":
            if to_wallet_type == "RTO_WALLET":

                phone = "*************"

                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM EXCESS WALLET TO RTO WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_RETAIL_EXCESS_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("EXCESS_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"ghana_lotto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type="GHANA_RTP_WALLET",
                            to_wallet_type="RTO_WALLET",
                        )

                    res = response.json()

                    if res.get("message") == "success":
                        try:
                            RetailWalletTransactions.create_credit_record_for_rto_wallet(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res

                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

            elif to_wallet_type == "RETAIL_RTP_WALLET":
                phone = "*************"

                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM EXCESS WALLET TO RETAIL RTP WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_RETAIL_EXCESS_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("EXCESS_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"ghana_lotto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type="GHANA_RTP_WALLET",
                            to_wallet_type="RTO_WALLET",
                        )

                    res = response.json()

                    if res.get("message") == "success":
                        try:
                            RetailWalletTransactions.create_credit_record_for_payout(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res

                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

            elif to_wallet_type == "PRE_FUNDING_WALLET":
                phone = "2349115030289"

                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM EXCESS WALLET TO PRE FUNDING WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_RETAIL_EXCESS_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("EXCESS_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"ghana_lotto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type="GHANA_RTP_WALLET",
                            to_wallet_type="RTO_WALLET",
                        )

                    res = response.json()

                    if res.get("message") == "success":
                        try:
                            RetailWalletTransactions.create_credit_record_for_payout(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res

                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"
        
        
        elif from_wallet_type == "NON_RETAIL_WALLET":

            if to_wallet_type == "RETAIL_RTP_WALLET":
                phone = "*************"

                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM NON RETAIL WALLET TO RETAIL RTP WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_NON_RETAIL_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("NON_RETAIL_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)
                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"ghana_lotto_wallet_login")

                        # return cls.wallet_to_wallet_transfer(
                        #     amount=amount,
                        #     from_wallet_type="GHANA_RTP_WALLET",
                        #     to_wallet_type="RTO_WALLET",
                        # )

                    res = response.json()

                    if res.get("message") == "success":
                        try:
                            RetailWalletTransactions.create_credit_record_for_payout(
                                amount=amount, transaction_ref=payout_reference
                            )
                        except Exception:
                            pass

                    return True, res

                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"

            elif to_wallet_type == "PRE_FUNDING_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "2349115030289"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM NON RETAIL WALLET TO PRE FUNDING WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_NON_RETAIL_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("NON_RETAIL_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"rto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type=from_wallet_type,
                            to_wallet_type=to_wallet_type,
                        )

                    res = response.json()

                    # if res.get("message") == "success":

                    #     try:
                    #         RetailWalletTransactions.create_debit_record_for_rto_wallet(
                    #             amount=amount, transaction_ref=payout_reference
                    #         )
                    #     except Exception:
                    #         pass

                    return True, res
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"
            
            elif to_wallet_type == "RTO_WALLET":
                if amount > 700000:
                    amount = 700000

                payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"

                phone = "*************"

                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "FUNDS MOVEMENT FROM PRE NON RETAIL WALLET TO RTO WALLET",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": payout_reference,
                        }
                    ],
                }

                payload["transaction_pin"] = config("AGENCY_BANKING_NON_RETAIL_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("NON_RETAIL_WALLET")

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                }

                url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
                try:
                    response = requests.post(url, headers=headers, json=payload)

                    if response.status_code == 401:
                        redis_db = redis.StrictRedis(
                            host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8"
                        )
                        redis_db.delete(f"rto_wallet_login")

                        return cls.wallet_to_wallet_transfer(
                            amount=amount,
                            from_wallet_type=from_wallet_type,
                            to_wallet_type=to_wallet_type,
                        )

                    res = response.json()

                    # if res.get("message") == "success":

                    #     try:
                    #         RetailWalletTransactions.create_credit_record_for_rto_wallet(
                    #             amount=amount, transaction_ref=payout_reference
                    #         )
                    #     except Exception:
                    #         pass

                    return True, res
                except requests.exceptions.RequestException as e:
                    return True, str(e)
                except:
                    return False, "An unexpected error occurred"
                
                 
                
        return False, "This wallet feature has not beem implemented"

    @classmethod
    def get_user_id(cls, wallet_type):
        try:
            wallet = Wallet.objects.get(wallet_type=wallet_type)
            return wallet.user_id
        except Wallet.DoesNotExist:
            return None


class WalletTransaction(models.Model):
    TRANSACTION_TYPE = [
        ("CREDIT", "CREDIT"),
        ("DEBIT", "DEBIT"),
    ]

    WALLET_TYPE = [
        ("AGENT_FUNDING_WALLET", "AGENT_FUNDING_WALLET"),
        ("NON_RETAIL_WALLET", "NON_RETAIL_WALLET"),
        ("RETAIL_RTP_WALLET", "RETAIL_RTP_WALLET"),
        ("GHANA_RTP_WALLET", "GHANA_RTP_WALLET"),
        ("MOVEABLE_GHANA_RTP_WALLET", "MOVEABLE_GHANA_RTP_WALLET"),
        ("GHANA_RTO_WALLET", "GHANA_RTO_WALLET"),
        ("RTO_WALLET", "RTO_WALLET"),
        ("COMMISSION", "COMMISSION"),
    ]

    GAME_TYPE = [
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("QUIKA", "QUIKA"),
        ("BANKER", "BANKER"),
    ]

    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE)
    wallet_type = models.CharField(max_length=50, choices=WALLET_TYPE)
    wallet_phone_number = models.CharField(max_length=20, null=True, blank=True)
    user_phone = models.CharField(max_length=20, null=True, blank=True)
    user_name = models.CharField(max_length=50, null=True, blank=True)
    game_play_id = models.CharField(max_length=100, null=True, blank=True)
    amount = models.FloatField(default=0.0)
    previous_balance = models.FloatField(default=0.0)
    current_balance = models.FloatField(default=0.0)
    transaction_type = models.CharField(max_length=50, choices=TRANSACTION_TYPE)
    game_type = models.CharField(max_length=50, choices=GAME_TYPE, null=True, blank=True)
    is_reversal = models.BooleanField(default=False)
    settled = models.BooleanField(default=False)
    is_vertical_lead_commission = models.BooleanField(default=False)
    is_supervisor_commission = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "WALET TRANSACTION"
        verbose_name_plural = "WALLET TRANSACTIONS"


class ExcessTransfer(models.Model):

    GAME_TYPE = [
        ("SALARY_FOR_LIFE", "SALARY_FOR_LIFE"),
        ("INSTANT_CASHOUT", "INSTANT_CASHOUT"),
        ("WYSE_CASH", "WYSE_CASH"),
        ("VIRTUAL_SOCCER", "VIRTUAL_SOCCER"),
        ("SOCCER_CASH", "SOCCER_CASH"),
        ("BANKER", "BANKER"),
        ("QUIKA", "QUIKA"),
        ("GHANA_LOTTO", "GHANA_LOTTO"),
        ("NIGERIA_LOTTO", "NIGERIA_LOTTO"),
        ("KENYA_LOTTO", "KENYA_LOTTO"),
        ("KENYA_30_LOTTO", "KENYA_30_LOTTO"),
        ("K_NOW", "K_NOW"),
    ]

    TRANSACTION_TYPE = [
        ("CREDIT", "CREDIT"),
        ("DEBIT", "DEBIT"),
    ]

    SOURCE_WALLET = [("RTO", "RTO"), ("RTP", "RTP"), ("EXCESS_WALLET", "EXCESS_WALLET")]

    amount = models.FloatField(default=0.0)
    agency_banking_balance_before = models.FloatField(default=0.0)
    agency_banking_balance_after = models.FloatField(default=0.0)
    transaction_reference = models.CharField(max_length=300, unique=True)
    agency_banking_reference = models.CharField(max_length=300, unique=True, blank=True, null=True)
    game_type = models.CharField(max_length=200, choices=GAME_TYPE, blank=True, null=True)
    transaction_type = models.CharField(max_length=200, choices=TRANSACTION_TYPE)
    source_wallet = models.CharField(max_length=200, choices=SOURCE_WALLET)
    recipient_wallet = models.CharField(max_length=200, choices=SOURCE_WALLET)
    request_payload = models.TextField(blank=True, null=True)
    response_payload = models.TextField(blank=True, null=True)

    re_initiated = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "EXCESS TRANSFER"
        verbose_name_plural = "EXCESS TRANSFERS"


class TerminalPrefundingMoneyTransfer(models.Model):

    TRANSACTION_TYPE = [
        ("CREDIT", "CREDIT"),
        ("DEBIT", "DEBIT"),
    ]

    STATUS = [
        ("PENDING", "PENDING"),
        ("SUCCESSFUL", "SUCCESSFUL"),
        ("FAILED", "FAILED"),
    ]

    amount = models.FloatField(default=0.0)
    agency_banking_balance_before = models.FloatField(default=0.0)
    agency_banking_balance_after = models.FloatField(default=0.0)
    transaction_reference = models.CharField(max_length=300, unique=True)
    agency_banking_reference = models.CharField(max_length=300, unique=True, blank=True, null=True)
    agent_name = models.CharField(max_length=300, blank=True, null=True)
    agent_phone = models.CharField(max_length=300, blank=True, null=True)
    agent_terminal_id = models.CharField(max_length=300, blank=True, null=True)
    transaction_type = models.CharField(max_length=200, choices=TRANSACTION_TYPE)
    status = models.CharField(max_length=200, choices=STATUS, default="PENDING")
    request_payload = models.TextField(blank=True, null=True)
    response_payload = models.TextField(blank=True, null=True)
    re_initiated = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "TERMINAL PRE-FUNDING MONEY TRANSFER"
        verbose_name_plural = "TERMINAL PRE-FUNDING MONEY TRANSFERS"

    @classmethod
    def create_record(cls, amount, agent_phone, agent_name, agent_terminal_id):

        from pos_app.models import AgencyBankingToken, Agent
        from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry

        def make_mini_third_party_request(token, payload):

            url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}",
            }

            response = requests.post(url, headers=headers, json=payload)

            try:
                return response.json()
            except:
                return response.text

        transaction_reference = str(uuid.uuid4())

        instance = cls.objects.create(
            amount=amount,
            transaction_reference=transaction_reference,
            agent_name=agent_name,
            agent_phone=agent_phone,
            agent_terminal_id=agent_terminal_id,
            transaction_type="DEBIT",
        )

        # get agency banking balance
        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="PRE_FUNDING_WALLET")
        pre_funding_wallet_account_balance = 0

        if isinstance(vfd_enquiries, dict):
            pre_funding_wallet_account_balance = vfd_enquiries.get("available_balance", 0)

        instance.agency_banking_balance_before = pre_funding_wallet_account_balance
        instance.save()
        instance.refresh_from_db()

        phone = "*************"

        payload = {
            "from_wallet_type": "COLLECTION",
            "to_wallet_type": "COLLECTION",
            "data": [
                {
                    "buddy_phone_number": phone,
                    "amount": amount,
                    "narration": "NEW AGENT PRE FUNDING AMOUNT TO FUNDING TABLE",
                    "is_beneficiary": "False",
                    "save_beneficiary": "True",
                    "remove_beneficiary": "False",
                    "is_recurring": "False",
                    "customer_reference": transaction_reference,
                }
            ],
        }

        instance.request_payload = payload
        instance.save()
        instance.refresh_from_db()

        cp_payload = payload.copy()

        cp_payload["transaction_pin"] = config("PRE_FUNDING_WALLET_PIN")

        token = AgencyBankingToken.retrieve_token("PRE_FUNDING_WALLET")

        res = make_mini_third_party_request(token=token, payload=cp_payload)

        vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_RTP_WALLET")
        pre_funding_wallet_balance_after = 0
        if isinstance(vfd_enquiries, dict):
            pre_funding_wallet_balance_after = vfd_enquiries.get("available_balance", 0)

        instance.agency_banking_balance_after = pre_funding_wallet_balance_after
        instance.response_payload = res

        if isinstance(res, dict):
            if res.get("message") == "success":
                instance.status = "SUCCESSFUL"
            else:
                instance.status = "FAILED"

        instance.save()
        instance.refresh_from_db()

        if instance.status == "SUCCESSFUL":
            try:
                agent_instance = Agent.objects.get(phone=instance.agent_phone)
                agent_instance.has_settled_pre_funding = True
                agent_instance.save()

            except Agent.DoesNotExist:
                pass
