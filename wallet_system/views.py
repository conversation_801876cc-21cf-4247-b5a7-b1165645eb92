# Create your views here.
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from account.authentication import SuperUser2Permission
from wallet_system.models import Wallet
from wallet_system.serializers import WalletToWalletTransferSerializer



class WalletToWalletTransferApiView(APIView):

    permission_classes = [SuperUser2Permission]
    serializer_class = WalletToWalletTransferSerializer

    def post(self, request):
        """
        Handles the wallet-to-wallet transfer notification.
        """
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)


        res_status, res = Wallet.wallet_to_wallet_transfer(
            amount = serializer.validated_data["amount"],
            from_wallet_type = serializer.validated_data["sender_wallet"],
            to_wallet_type = serializer.validated_data["receiver_wallet"],
        )

        if res_status:
            return Response(
                {"message": "Wallet transfer successful", "data": res},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"message": "Wallet transfer failed", "data": res},
                status=status.HTTP_400_BAD_REQUEST,
            )