from django.core.management.base import BaseCommand

from django.db.models import Sum
from wallet_system.models import Wallet, WalletTransaction
from wallet_system.wallet_system_helper import move_ghana_rto_value_to_rto_wallet, move_ghana_rtp_value_to_ghana_rtp_wallet, move_moveable_ghana_rtp_value_to_retails_wallet, move_retail_commission_value_to_retail_commission_wallet, move_retail_rtp_value_to_retail_rtp_wallet, move_rto_value_to_rto_wallet

class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        try:
            ghana_rto_wallet = Wallet.objects.get(balance__gte=30, wallet_type = "GHANA_RTO_WALLET")
            move_ghana_rto_value_to_rto_wallet(ghana_rto_wallet.balance)
        except Exception:
            pass

        
        try:
            ghana_rtp_wallet = Wallet.objects.get(balance__gte=30, wallet_type = "GHANA_RTP_WALLET")
            move_ghana_rtp_value_to_ghana_rtp_wallet(ghana_rtp_wallet.balance)
        except Exception:
            pass

        try:
            retail_rtp_wallet = Wallet.objects.get(balance__gte=30, wallet_type = "RETAIL_RTP_WALLET")
            move_retail_rtp_value_to_retail_rtp_wallet(retail_rtp_wallet.balance)
        except Exception:
            pass

        try:
            rto_wallet = Wallet.objects.get(balance__gte=30, wallet_type = "RTO_WALLET")
            move_rto_value_to_rto_wallet(rto_wallet.balance)
        except Exception:
            pass

        try:
            rto_wallet = Wallet.objects.get(balance__gte=30, wallet_type = "MOVEABLE_GHANA_RTP_WALLET")
            move_moveable_ghana_rtp_value_to_retails_wallet(rto_wallet.balance)
        except Exception:
            pass
        try:
            commission_wallet = Wallet.objects.get(balance__gte=30, wallet_type = "COMMISSION")
            move_retail_commission_value_to_retail_commission_wallet(commission_wallet.balance)
        except Exception:
            pass





