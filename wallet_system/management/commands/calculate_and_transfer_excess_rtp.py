from django.core.management.base import BaseCommand
from datetime import datetime, time, timedelta
import pytz
from django.conf import settings

from pos_app.models import AgencyBankingToken, GamesDailyActivities
from django.db.models import Q, Sum

from pos_app.pos_helpers import PosAgentHelper, liberty_pay_vfd_account_enquiry
import uuid
from decouple import config

from wallet_system.models import ExcessTransfer
import json
from django.utils import timezone as tm_zone

import requests



def make_mini_third_party_request(token, payload):

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"


    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }
    
    response = requests.post(url, headers=headers, json=payload)

    try:
        return response.json()
    except:
        return response.text



class Command(BaseCommand):
    help = ""


    def handle(self, *args, **kwargs):

        timezone = pytz.timezone(settings.TIME_ZONE)
        current_datetime = datetime.now(tz=timezone)
        yesterday_datetime = current_datetime - timedelta(days=1)

        previous_day_game_activity_qs = GamesDailyActivities.objects.filter(Q(created_at__date = current_datetime.date()), ~Q(game_type = "TOTAL"))

        for instance in previous_day_game_activity_qs:
            if instance.excess_winnings == 0:
                continue


            if instance.excess_winnings > 0:

                amount = instance.excess_winnings

                # GET TOTAL EXCESS TRAMSACTION FOR THIS A GAME TYPE IN A DAY
                exclude_datetime = tm_zone.make_aware(datetime(2025, 6, 28, 1, 1, 0))
                total_disbursed_today = ExcessTransfer.objects.filter(
                    game_type=instance.game_type, 
                    created_at__date=current_datetime.date()
                ).exclude(
                    created_at=exclude_datetime
                ).aggregate(Sum("amount")).get("amount__sum") or 0

                if total_disbursed_today > 0:
                    amount = total_disbursed_today - amount


                amount = abs(amount)

                if amount < 50:
                    continue


                

                # should got out from RTP WALLET TO EXCESS WALLET
                vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_RTP_WALLET")
                rtp_wallet_balance_before = 0
                if isinstance(vfd_enquiries, dict):
                    rtp_wallet_balance_before = vfd_enquiries.get("available_balance", 0)
            
                transaction_reference = str(uuid.uuid4())

                phone = config("AGENCY_BANKING_RETAIL_EXCESS_WALLET_PHONE_NUMBER")

                
                # amount = 50
                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "CREDITING EXCESS WALLET FROM RTP",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": transaction_reference
                        }
                    ],
                }

                cp_payload = payload.copy()

                excess_transfer = ExcessTransfer.objects.create(
                    agency_banking_balance_before = rtp_wallet_balance_before,
                    amount = amount,
                    transaction_reference = transaction_reference,
                    game_type = instance.game_type,
                    transaction_type = "CREDIT",
                    source_wallet = "RTP",
                    recipient_wallet = "EXCESS_WALLET",
                    request_payload = json.dumps(payload)
                )

                cp_payload["transaction_pin"] = config("AGENCY_BANKING_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RETAIL_RTP_WALLET")

                res = make_mini_third_party_request(token = token, payload = cp_payload)

                # res = "TESTING"


                vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RETAIL_RTP_WALLET")
                rtp_wallet_balance_after = 0
                if isinstance(vfd_enquiries, dict):
                    rtp_wallet_balance_after = vfd_enquiries.get("available_balance", 0)

                excess_transfer.response_payload = res
                excess_transfer.agency_banking_balance_after = rtp_wallet_balance_after
                excess_transfer.save()


            elif instance.excess_winnings < 0:
                

                amount = abs(instance.excess_winnings)
                # amount = 50


                # GET TOTAL EXCESS TRAMSACTION FOR THIS A GAME TYPE IN A DAY
                exclude_datetime = tm_zone.make_aware(datetime(2025, 6, 28, 1, 1, 0))
                total_disbursed_today = ExcessTransfer.objects.filter(
                    game_type=instance.game_type, 
                    created_at__date=current_datetime.date()
                ).exclude(
                    created_at=exclude_datetime
                ).aggregate(Sum("amount")).get("amount__sum") or 0

                if total_disbursed_today > 0:
                    amount = total_disbursed_today - amount


                amount = abs(amount)


                
                if amount < 50:
                    continue



                # should got out from RTO WALLET TO RTP WALLET
                vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RTO_WALLET")
                rtp_wallet_balance_before = 0
                if isinstance(vfd_enquiries, dict):
                    rtp_wallet_balance_before = vfd_enquiries.get("available_balance", 0)
            
                transaction_reference = str(uuid.uuid4())

                phone = "*************"
                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "RTO to RTP account - excess amount adjustment",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": transaction_reference
                        }
                    ],
                }

                cp_payload = payload.copy()

                excess_transfer = ExcessTransfer.objects.create(
                    agency_banking_balance_before = rtp_wallet_balance_before,
                    amount = amount,
                    transaction_reference = transaction_reference,
                    game_type = instance.game_type,
                    transaction_type = "CREDIT",
                    source_wallet = "RTO",
                    recipient_wallet = "RTP",
                    request_payload = json.dumps(payload)
                )

                cp_payload["transaction_pin"] = config("AGENCY_BANKING_RTO_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RTO_WALLET")

                res = make_mini_third_party_request(token = token, payload = cp_payload)


                # res = "TESTING"


                vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RTO_WALLET")
                rtp_wallet_balance_after = 0
                if isinstance(vfd_enquiries, dict):
                    rtp_wallet_balance_after = vfd_enquiries.get("available_balance", 0)

                excess_transfer.response_payload = res
                excess_transfer.agency_banking_balance_after = rtp_wallet_balance_after
                excess_transfer.save()




                

