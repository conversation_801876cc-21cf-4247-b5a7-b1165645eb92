from django.core.management.base import BaseCommand
from pos_app.models import AgencyBankingToken
from pos_app.pos_helpers import ghana_lotto_wallet_login, rto_wallet_login
from wallet_system.models import WalletTransaction, Wallet
from django.db.models import Sum
import uuid
from datetime import datetime
import requests
from django.conf import settings
from decouple import config

import redis



def move_funds_from_ghana_lotto_to_funding_wallet():
    phone = "*************"
    amount = 89728
    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"
    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": phone,
                "amount": amount,
                "narration": "FUNDS MOVEMENT FROM GHANA LOTTO TO FUNDING WALLET",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    payload["transaction_pin"] = config("AGENCY_BANKING_GHANA_LOTTO_WALLET_TRANSACTION_PIN")

    token = AgencyBankingToken.retrieve_token("GHANA_RTP_WALLET")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
    

    try:
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"ghana_lotto_wallet_login")

            return move_funds_from_ghana_lotto_to_funding_wallet()
        
    except requests.exceptions.RequestException:
        pass
    else:
        print(response.text)


def move_funds_from_rto_wallet_to_funding_wallet(amount, phone, narration):
    # phone = "*************"
    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"
    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": phone,
                "amount": amount,
                "narration": narration,
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    payload["transaction_pin"] = config("AGENCY_BANKING_RTO_WALLET_TRANSACTION_PIN")

    token = AgencyBankingToken.retrieve_token("RTO_WALLET")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
    

    try:
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"rto_wallet_login")

            return move_funds_from_rto_wallet_to_funding_wallet(amount, phone, narration)
        
    except requests.exceptions.RequestException:
        pass
    else:
        print(response.text)

def move_funds_from_ghana_rtp_to_rto_wallet(amount):
    phone = "*************"
    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"
    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": phone,
                "amount": amount,
                "narration": "GHANA RTP MONEY MOVEMENT TO RTO WALLET",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    payload["transaction_pin"] = config("AGENCY_BANKING_GHANA_LOTTO_WALLET_TRANSACTION_PIN")

    token = AgencyBankingToken.retrieve_token("GHANA_RTP_WALLET")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
    

    try:
        response = requests.post(url, headers=headers, json=payload)

        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"ghana_lotto_wallet_login")

            return move_funds_from_ghana_rtp_to_rto_wallet(amount)
            
    except requests.exceptions.RequestException:
        pass
    else:
        print(response.text)



def move_funds_from_rto_wallet_to_retail_rtp_wallet(amount):
    phone = "*************"
    # amount = 89728
    payout_reference = f"{uuid.uuid4()}{datetime.now().timestamp()}"
    payload = {
        "from_wallet_type": "COLLECTION",
        "to_wallet_type": "COLLECTION",
        "data": [
            {
                "buddy_phone_number": phone,
                "amount": amount,
                "narration": "FUNDS MOVEMENT FROM RTO WALLET TO RETAIL RTP WALLET",
                "is_beneficiary": "False",
                "save_beneficiary": "True",
                "remove_beneficiary": "False",
                "is_recurring": "False",
                "customer_reference": payout_reference,
            }
        ],
    }

    payload["transaction_pin"] = config("AGENCY_BANKING_RTO_WALLET_TRANSACTION_PIN")

    token = AgencyBankingToken.retrieve_token("RTO_WALLET")

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }

    url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"
    

    try:
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 401:
            redis_db = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")
            redis_db.delete(f"rto_wallet_login")

            return move_funds_from_rto_wallet_to_retail_rtp_wallet(amount)
        
        
    except requests.exceptions.RequestException:
        pass
    else:
        print(response.text)

class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        # move_funds_from_ghana_rtp_to_rto_wallet(260170)

        # rtp wallet phon = *************
        move_funds_from_rto_wallet_to_retail_rtp_wallet(700000)
        move_funds_from_rto_wallet_to_retail_rtp_wallet(700000)
        move_funds_from_rto_wallet_to_retail_rtp_wallet(100000)


        # move_funds_from_rto_wallet_to_funding_wallet(300000)
        
        



        
        

    
