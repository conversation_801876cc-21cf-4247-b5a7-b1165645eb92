from rest_framework import serializers


class WalletToWalletTransferSerializer(serializers.Serializer):

    WALLET_TYPE = [
        ("AGENT_FUNDING_WALLET", "AGENT_FUNDING_WALLET"),
        ("NON_<PERSON><PERSON><PERSON><PERSON>_WALLET", "NON_<PERSON><PERSON><PERSON><PERSON>_WALLET"),
        ("<PERSON><PERSON><PERSON><PERSON>_RTP_WALLET", "RET<PERSON>IL_RTP_WALLET"),
        ("GHANA_RTP_WALLET", "GHANA_RTP_WALLET"),
        ("MOVEABLE_GHANA_RTP_WALLET", "MOVE<PERSON>LE_GHANA_RTP_WALLET"),
        ("<PERSON><PERSON><PERSON>_RTO_WALLET", "GHA<PERSON>_RTO_WALLET"),
        ("RTO_WALLET", "R<PERSON>_WALLET"),
        ("COMMISSION_WALLET", "COMMISSION_WALLET"),
        ("PENDING_LATE_WITHDRAWAL_WALLET", "PENDING_LATE_WITHDRAWAL_WALLET"),
        ("PRE_FUNDING_WALLET", "PRE_FUNDING_WALLET"),
        ("<PERSON>X<PERSON><PERSON>_WALLET", "EXCE<PERSON>_WALLET"),
    ]

    amount = serializers.FloatField()
    sender_wallet = serializers.ChoiceField(choices=WALLET_TYPE)
    receiver_wallet = serializers.ChoiceField(choices=WALLET_TYPE)
