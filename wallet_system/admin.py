import json
import uuid

import requests
from decouple import config
from django.conf import settings
from django.contrib import admin, messages
from import_export import resources
from import_export.admin import ImportExportModelAdmin

# from wallet_app.models import WalletTransaction
from pos_app.models import AgencyBankingToken
from pos_app.pos_helpers import liberty_pay_vfd_account_enquiry
from wallet_system.models import (
    ExcessTransfer,
    TerminalPrefundingMoneyTransfer,
    Wallet,
    WalletTransaction,
)


# Register your models here.
class WalletResource(resources.ModelResource):
    class Meta:
        model = Wallet


class WalletTransactionResource(resources.ModelResource):
    class Meta:
        model = WalletTransaction


class ExcessTransferResource(resources.ModelResource):
    class Meta:
        model = ExcessTransfer


class TerminalPrefundingMoneyTransferResource(resources.ModelResource):
    class Meta:
        model = TerminalPrefundingMoneyTransfer


class WalletResourceAdmin(ImportExportModelAdmin):
    resource_class = WalletResource
    # raw_id_fields = ("user_profile", "agent_profile")
    # autocomplete_fields = ["batch", "user_profile"]

    search_fields = ["wallet_type", "wallet_phone_number"]

    list_filter = ["created_at", "wallet_type"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class WalletTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = WalletTransactionResource
    raw_id_fields = [
        "wallet",
    ]
    autocomplete_fields = ["wallet"]

    search_fields = ["wallet__wallet_type", "wallet__wallet_phone_number"]

    list_filter = [
        "created_at",
        "settled",
        "is_reversal",
        "wallet_type",
        "transaction_type",
        "game_type",
        "is_vertical_lead_commission",
        "is_supervisor_commission",
    ]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class ExcessTransferResourceAdmin(ImportExportModelAdmin):
    resource_class = ExcessTransferResource

    search_fields = ["transaction_reference", "agency_banking_reference"]

    list_filter = ["created_at", "game_type", "transaction_type", "source_wallet", "recipient_wallet"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = ["retryfailedtransfer"]

    @admin.action(description="RE-TRY FAILED TRANSFER")
    def retryfailedtransfer(modeladmin, request, queryset):

        def make_mini_third_party_request(token, payload):

            url = f"{settings.AGENCY_BANKING_BASE_URL}/send/send_money_paybuddy/"

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}",
            }

            response = requests.post(url, headers=headers, json=payload)

            try:
                return response.json()
            except:
                return response.text

        for obj in queryset:
            if obj.re_initiated is True:
                messages.error(request, f"{obj.id} --------------- Already Re-initiated")
                continue

            if obj.source_wallet == "RTO":

                # should got out from RTO WALLET TO RTP WALLET
                vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RTO_WALLET")
                rtp_wallet_balance_before = 0
                if isinstance(vfd_enquiries, dict):
                    rtp_wallet_balance_before = vfd_enquiries.get("available_balance", 0)

                transaction_reference = str(uuid.uuid4())

                amount = obj.amount

                phone = "*************"
                payload = {
                    "from_wallet_type": "COLLECTION",
                    "to_wallet_type": "COLLECTION",
                    "data": [
                        {
                            "buddy_phone_number": phone,
                            "amount": amount,
                            "narration": "RTO to RTP account - excess amount adjustment",
                            "is_beneficiary": "False",
                            "save_beneficiary": "True",
                            "remove_beneficiary": "False",
                            "is_recurring": "False",
                            "customer_reference": transaction_reference,
                        }
                    ],
                }

                cp_payload = payload.copy()

                excess_transfer = ExcessTransfer.objects.create(
                    agency_banking_balance_before=rtp_wallet_balance_before,
                    amount=amount,
                    transaction_reference=transaction_reference,
                    game_type=obj.game_type,
                    transaction_type="CREDIT",
                    source_wallet="RTO",
                    recipient_wallet="RTP",
                    request_payload=json.dumps(payload),
                )

                cp_payload["transaction_pin"] = config("AGENCY_BANKING_RTO_WALLET_TRANSACTION_PIN")

                token = AgencyBankingToken.retrieve_token("RTO_WALLET")

                res = make_mini_third_party_request(token=token, payload=cp_payload)

                vfd_enquiries = liberty_pay_vfd_account_enquiry(source="RTO_WALLET")
                rtp_wallet_balance_after = 0
                if isinstance(vfd_enquiries, dict):
                    rtp_wallet_balance_after = vfd_enquiries.get("available_balance", 0)

                excess_transfer.response_payload = res
                excess_transfer.agency_banking_balance_after = rtp_wallet_balance_after
                excess_transfer.save()

                obj.re_initiated = True
                obj.save()


class TerminalPrefundingMoneyTransferResourceAdmin(ImportExportModelAdmin):
    resource_class = TerminalPrefundingMoneyTransferResource

    search_fields = [
        "transaction_reference",
        "agency_banking_reference",
        "agent_name",
        "agent_phone",
        "agent_terminal_id",
    ]

    list_filter = ["created_at", "transaction_type", "status", "re_initiated"]
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = ["retryfailedtransfer"]

    @admin.action(description="RE-TRY FAILED TRANSFER")
    def retryfailedtransfer(modeladmin, request, queryset):
        for obj in queryset:
            if obj.status != "FAILED":
                messages.error(request, f"{obj.id} --------------- {obj.status}")
                continue

            if obj.re_initiated == True:
                messages.error(request, f"{obj.id} --------------- Already Re-initiated")
                continue

            obj.re_initiated = True
            obj.save()

            TerminalPrefundingMoneyTransfer.create_record(
                amount=obj.amount,
                agent_phone=obj.agent_phone,
                agent_name=obj.agent_name,
                agent_terminal_id=obj.agent_terminal_id,
            )


admin.site.register(Wallet, WalletResourceAdmin)
admin.site.register(WalletTransaction, WalletTransactionResourceAdmin)
admin.site.register(ExcessTransfer, ExcessTransferResourceAdmin)
admin.site.register(TerminalPrefundingMoneyTransfer, TerminalPrefundingMoneyTransferResourceAdmin)
