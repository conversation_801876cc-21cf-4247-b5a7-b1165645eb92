from django.core.management.base import BaseCommand

from broad_base_communication.bbc_helper import BBCTelcoAggregator
from wyse_ussd.models import TelcoSubscriptionPlan


class Command(BaseCommand):
    help = "Test run script maually"

    def handle(self, *args, **kwargs):
        # # # LifeStyleTable().awoof_ussd_iteams()
        # # prices = LifeStyleTable().awoof_game_prices(main_prices=True, band=15000)

        # # print(prices)

        # awoof_ussd_games = AwoofGameTable.objects.filter(channel = "USSD")

        # if awoof_ussd_games:
        #     for awoof_ussd_game in awoof_ussd_games:
        #         band = awoof_ussd_game.awoof_amount + awoof_ussd_game.instant_cashout_amount + awoof_ussd_game.illusion_amount

        #         _item_amt = str(awoof_ussd_game.item.item_amount).replace(",", "")

        #         user_percent = ((awoof_ussd_game.awoof_amount) / float(_item_amt)) * 100

        #         real_percent = truncate(user_percent)

        #         awoof_ussd_game.percentage = real_percent
        #         awoof_ussd_game.band = band
        #         awoof_ussd_game.save()

        # data = {
        #     "soapenv:Envelope": {
        #         "@xmlns:soapenv": "http://schemas.xmlsoap.org/soap/envelope/",
        #         "soapenv:Header": None,
        #         "soapenv:Body": {
        #             "ns2:syncOrderRelation": {
        #                 "@xmlns:ns2": "http://www.csapi.org/schema/parlayx/data/sync/v1_0/local",
        #                 "ns2:userID": {"ID": "2347039115243", "type": "0"},
        #                 "ns2:spID": "whispasub",
        #                 "ns2:productID": "23410220000027466",
        #                 "ns2:serviceID": "234102200006963",
        #                 "ns2:updateType": "1",
        #                 "ns2:updateTime": "20231207155003",
        #                 "ns2:updateDesc": "Addition",
        #                 "ns2:effectiveTime": "20231207160246",
        #                 "ns2:expiryTime": "20231208160246",
        #                 "ns2:extensionInfo": {
        #                     "item": [
        #                         {"key": "serviceType", "value": "Broad_WHISP_6963"},
        #                         {"key": "result", "value": "Success"},
        #                         {"key": "chargingMode", "value": "S"},
        #                         {"key": "validityType", "value": "DD"},
        #                         {"key": "fee", "value": "5000"},
        #                         {"key": "resultCode", "value": "0"},
        #                         {"key": "renFlag", "value": "Y"},
        #                         {"key": "autoRenew", "value": "Y"},
        #                         {"key": "operationId", "value": "SN"},
        #                         {"key": "chargeAmount", "value": "50.0"},
        #                         {"key": "validityDays", "value": "1"},
        #                         {"key": "channelID", "value": "1"},
        #                     ]
        #                 },
        #             }
        #         },
        #     }
        # }

        # subscription_item = (
        #     data.get("soapenv:Envelope", {})
        #     .get("soapenv:Body", {})
        #     .get("ns2:syncOrderRelation", {})
        #     .get("ns2:extensionInfo", {})
        #     .get("item")
        # )

        # if isinstance(subscription_item, list):
        #     list_item = subscription_item[0]
        #     print(
        #         f"""
        #     list_item: {list_item}
        #     """
        #     )

        product_ids = [
            23410220000027462,
            23410220000027463,
            23410220000027470,
            23410220000027471,
            23410220000027464,
            23410220000027465,
            23410220000027466,
            23410220000027467,
            23410220000027468,
            23410220000027469,
        ]
        phones = ["2348038705895", "2349069090865"]
        for phone in phones:
            instance = BBCTelcoAggregator()
            for product_id in product_ids:
                print(instance.telco_airtime_unsubscription_request(product_id, phone))

            TelcoSubscriptionPlan.objects.filter(phone_number=phone).update(subscription_status="DEACTIVATED")
