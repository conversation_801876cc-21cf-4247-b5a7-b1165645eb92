import os
import random

from django.db.models import Case, Q, When
from django.http import FileResponse
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.views.decorators.csrf import csrf_exempt
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, generics, status
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from account.authentication import (
    IsAgentRemittanceDuePermission,
    IsAgentSuspendedPermission,
    IsBlackListedPermission,
    PhoneNumberVerifedPermission,
)
from awoof_app.helpers.api_helpers import (
    check_price_validity,
    send_ask_ai_user_chat_link,
    validate_pos_life_style_cash_price,
)
from awoof_app.helpers.func import awoof_stake_amounts
from awoof_app.models import LifeStyleTable
from awoof_app.paginator import CustomPagination
from awoof_app.serializers import (
    AskAIHistorySerializer,
    AskAISubscriptionSerializer,
    AwoofItemSerializer,
    GetAwoofResultSerializer,
    LibertyLiveTicketSerializers,
    ListFortuneAIModule,
    PosLifeStylePlaySerializers,
    WebLifeStylePlaySerializers,
)
from broad_base_communication.bbc_helper import BBCTelcoAggregator
from main.models import LotteryModel, UserProfile
from main.ussd.helpers import Utility
from pos_app.models import Agent, LottoAgentRemittanceTable
from prices.game_price import secure_d_and_upstream_service_and_prodct_details
from referral_system.models import ReferralWallet
from wallet_app.models import UserWallet
from web_app.ip_whitelisting import ip_whitelist_middleware
from wyse_ussd.models import NitroswitchAiFortuneSubscription, TelcoGameWebSubscribers

from .models import AwoofGameTable, FortuneAIModule, FortuneAISubModule, OpenAiDataDump

# Create your views here.


class PlayLifeStyleAPIVIew(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def post(self, request):
        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        serializer = PosLifeStylePlaySerializers(data=request.data)
        serializer.is_valid(raise_exception=True)
        pin = serializer.validated_data["pin"]
        data = serializer.data
        total_stake_amount = awoof_stake_amounts(data=data)

        # validate_remittance_amount
        response = LottoAgentRemittanceTable().valuate_amount_to_be_added(amount=total_stake_amount, agent=agent_instance)
        if response.get("is_amount_much"):
            response = {
                "message": f"Hi, kindly top up your lottowallet with this amount {Utility.currency_formatter(response.get('amount'))} to continue game play"
                
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        phone = serializer.data["phone_no"]

        if phone == "" or phone is None:
            phone = agent_instance.phone

        serializer.data["phone_no"] = agent_instance.phone

        phone_no = LotteryModel.format_number_from_back_add_234(phone)

        # if AgentConstantVariables().get_phone_number_is_required() is True:

        # if agent_instance.phone == phone_no:

        #     response = {"message": "You cannot use your own phone number"}
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # if phone is None or phone == "":
        #     phone = _agent_instance.phone

        # serializer.data["phone_number"] = phone

        # get or create user profile
        user_instance = UserProfile.create_user_profile_if_none_exist(phone_no=phone_no, channel="POS")
        games = serializer.validated_data.get("games")
        message_data = validate_pos_life_style_cash_price(games, "AWOOF")
        if "is_price_valid" in message_data.keys():
            if message_data["is_price_valid"] is False:
                return Response(data=message_data, status=status.HTTP_400_BAD_REQUEST)
        price_data_val, price_data = check_price_validity(games)
        if price_data_val is False:
            return Response(data=price_data, status=status.HTTP_400_BAD_REQUEST)

        reg_and_payment = AwoofGameTable.register_ticket(
            request_payload=data,
            user_profile=user_instance,
            agent_profile=agent_instance,
            phone=phone_no,
            channel="POS_AGENT",
            service_type="AWOOF",
            pin=pin,
        )

        return Response(reg_and_payment, status=status.HTTP_200_OK)


class PlayInsuranceAPIVIew(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def post(self, request):
        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        serializer = PosLifeStylePlaySerializers(data=request.data)
        serializer.is_valid(raise_exception=True)
        pin = serializer.validated_data["pin"]
        data = serializer.data
        total_stake_amount = awoof_stake_amounts(data=data)

        # validate_remittance_amount
        response = LottoAgentRemittanceTable().valuate_amount_to_be_added(amount=total_stake_amount, agent=agent_instance)
        if response.get("is_amount_much"):
            response = {
                "message": f"Hi, kindly top up your lottowallet with this amount {Utility.currency_formatter(response.get('amount'))} to continue game play"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        phone = serializer.data["phone_no"]

        if phone == "" or phone is None:
            phone = agent_instance.phone

        serializer.data["phone_no"] = agent_instance.phone

        phone_no = LotteryModel.format_number_from_back_add_234(phone)

        # if AgentConstantVariables().get_phone_number_is_required() is True:

        # if agent_instance.phone == phone_no:

        #     response = {"message": "You cannot use your own phone number"}
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # if phone is None or phone == "":
        #     phone = _agent_instance.phone

        # serializer.data["phone_number"] = phone

        # get or create user profile
        user_instance = UserProfile.create_user_profile_if_none_exist(phone_no=phone_no, channel="POS")
        games = serializer.validated_data.get("games")
        message_data = validate_pos_life_style_cash_price(games, "INSURANCE")
        if "is_price_valid" in message_data.keys():
            if message_data["is_price_valid"] is False:
                return Response(data=message_data, status=status.HTTP_400_BAD_REQUEST)
        price_data_val, price_data = check_price_validity(games)
        if price_data_val is False:
            return Response(data=price_data, status=status.HTTP_400_BAD_REQUEST)

        reg_and_payment = AwoofGameTable.register_ticket(
            request_payload=data,
            user_profile=user_instance,
            agent_profile=agent_instance,
            phone=phone_no,
            channel="POS_AGENT",
            service_type="INSURANCE",
            pin=pin,
        )

        return Response(reg_and_payment, status=status.HTTP_200_OK)


class GetAwoofItemsAPIView(APIView):
    permission_classes = [TokenAuthentication]

    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        desired_order = ["OPEN", "OPENING_SOON", "CLOSED"]
        cases = [When(item_status=choice, then=pos) for pos, choice in enumerate(desired_order)]
        items = LifeStyleTable.objects.filter(service_type="AWOOF").order_by(Case(*cases, default=len(desired_order)))
        serializer = AwoofItemSerializer(items, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class GetInsuranceItemsAPIView(APIView):
    permission_classes = [TokenAuthentication]

    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
    ]

    def get(self, request):
        desired_order = ["OPEN", "OPENING_SOON", "CLOSED"]
        cases = [When(item_status=choice, then=pos) for pos, choice in enumerate(desired_order)]
        items = LifeStyleTable.objects.filter(service_type="INSURANCE").order_by(Case(*cases, default=len(desired_order)))
        serializer = AwoofItemSerializer(items, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class GetAskAIServiceAPIView(APIView):
    @method_decorator(csrf_exempt)
    @method_decorator(ip_whitelist_middleware())
    def get(self, request):
        desired_order = ["OPEN", "OPENING_SOON", "CLOSED"]
        cases = [When(item_status=choice, then=pos) for pos, choice in enumerate(desired_order)]
        items = LifeStyleTable.objects.filter(service_type="ASK_AI").order_by(Case(*cases, default=len(desired_order)))
        serializer = AwoofItemSerializer(items, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class WEBLifeStyleApiView(APIView):
    authentication_classes = [
        TokenAuthentication,
    ]
    permission_classes = [
        IsAuthenticated,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
    ]

    @method_decorator(csrf_exempt)
    @method_decorator(ip_whitelist_middleware())
    def post(self, request, instance=None):
        player = UserProfile.objects.filter(Q(email=request.user.email) | Q(phone_number=request.user.phone)).last()

        if player is None:
            return Response(
                data={"message": "User profile does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if player.has_sudo_phone_number is True:
            data = {
                "message": "Please update your phone number to continue",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        serializer = WebLifeStylePlaySerializers(data=request.data)
        serializer.is_valid(raise_exception=True)
        from_referral_wallet = serializer.data["from_referral_wallet"]
        serializer.data["paystack"]
        from_play_wallet = serializer.data["from_play_wallet"]
        data = serializer.data

        total_stake_amount = awoof_stake_amounts(data=data)

        print("TOTAL STAKE AMOUNT ------------------------>", total_stake_amount)

        if from_referral_wallet is True:  # Pay using referral wallet
            lottery_wallet = ReferralWallet.objects.filter(user=player).last()

            if lottery_wallet is None:
                return Response(
                    data={"message": "Referral wallet not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            if lottery_wallet.can_use_wallet is False:
                data = {"message": "You cannot use referral wallet yet"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if lottery_wallet.available_balance < total_stake_amount:
                data = {"message": "Insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if from_play_wallet is True:  # pay using play wallet
            main_wallet = UserWallet.objects.filter(user=player, wallet_tag="WEB").last()
            if main_wallet is None:
                UserWallet.objects.create(user=player, wallet_tag="WEB")
                data = {"message": "insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if main_wallet.game_available_balance < total_stake_amount:
                data = {"message": "insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        games = serializer.validated_data.get("games")
        message_data = validate_pos_life_style_cash_price(games, "AWOOF")
        if "is_price_valid" in message_data.keys():
            if message_data["is_price_valid"] is False:
                return Response(data=message_data, status=status.HTTP_400_BAD_REQUEST)
        price_data_val, price_data = check_price_validity(games)
        if price_data_val is False:
            return Response(data=price_data, status=status.HTTP_400_BAD_REQUEST)
        reg_and_payment = AwoofGameTable.register_ticket(
            request_payload=data,
            user_profile=player,
            agent_profile=None,
            phone=player.phone_number,
            channel="WEB",
            service_type="AWOOF",
            play_wallet=from_play_wallet,
        )

        return Response(reg_and_payment, status=status.HTTP_200_OK)


class RegisterLibertyLive(APIView):
    @method_decorator(csrf_exempt)
    @method_decorator(ip_whitelist_middleware())
    def post(self, request, instance=None):
        serializer = LibertyLiveTicketSerializers(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone_number = serializer.validated_data.get("phone_number")
        amount = serializer.validated_data.get("amount")

        phone = LotteryModel.format_number_from_back_add_234(phone_number)

        user_profile = UserProfile.create_user_profile_if_none_exist(phone_no=phone, channel="WEB")

        get_insurance = LifeStyleTable.objects.filter(service_type="INSURANCE", item_status="OPEN").first()
        if not get_insurance:
            return Response({"message": "no life style item opened"}, status=status.HTTP_400_BAD_REQUEST)

        AwoofGameTable.register_liberty_live_ticket(user_profile=user_profile, phone=phone, amount=amount, item=get_insurance)

        return Response({"message": "successful"}, status=status.HTTP_200_OK)


class WEBInsuranceApiView(APIView):
    authentication_classes = [
        TokenAuthentication,
    ]
    permission_classes = [
        IsAuthenticated,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
    ]

    @method_decorator(csrf_exempt)
    @method_decorator(ip_whitelist_middleware())
    def post(self, request, instance=None):
        player = UserProfile.objects.filter(Q(email=request.user.email) | Q(phone_number=request.user.phone)).last()

        if player is None:
            return Response(
                data={"message": "User profile does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if player.has_sudo_phone_number is True:
            data = {
                "message": "Please update your phone number to continue",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        serializer = WebLifeStylePlaySerializers(data=request.data)
        serializer.is_valid(raise_exception=True)
        from_referral_wallet = serializer.data["from_referral_wallet"]
        serializer.data["paystack"]
        from_play_wallet = serializer.data["from_play_wallet"]
        data = serializer.data

        total_stake_amount = awoof_stake_amounts(data=data)

        print("TOTAL STAKE AMOUNT ------------------------>", total_stake_amount)

        if from_referral_wallet is True:  # Pay using referral wallet
            lottery_wallet = ReferralWallet.objects.filter(user=player).last()

            if lottery_wallet is None:
                return Response(
                    data={"message": "Referral wallet not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            if lottery_wallet.can_use_wallet is False:
                data = {"message": "You cannot use referral wallet yet"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if lottery_wallet.available_balance < total_stake_amount:
                data = {"message": "Insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if from_play_wallet is True:  # pay using play wallet
            main_wallet = UserWallet.objects.filter(user=player, wallet_tag="WEB").last()
            if main_wallet is None:
                UserWallet.objects.create(user=player, wallet_tag="WEB")
                data = {"message": "insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if main_wallet.game_available_balance < total_stake_amount:
                data = {"message": "insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        games = serializer.validated_data.get("games")
        message_data = validate_pos_life_style_cash_price(games, "INSURANCE")
        if "is_price_valid" in message_data.keys():
            if message_data["is_price_valid"] is False:
                return Response(data=message_data, status=status.HTTP_400_BAD_REQUEST)
        price_data_val, price_data = check_price_validity(games)
        if price_data_val is False:
            return Response(data=price_data, status=status.HTTP_400_BAD_REQUEST)
        reg_and_payment = AwoofGameTable.register_ticket(
            request_payload=data,
            user_profile=player,
            agent_profile=None,
            phone=player.phone_number,
            channel="WEB",
            service_type="INSURANCE",
            play_wallet=from_play_wallet,
        )

        return Response(reg_and_payment, status=status.HTTP_200_OK)


class GetAwoofApiView(APIView):
    permission_classes = [TokenAuthentication]

    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
    ]

    def post(self, request):
        serializer = GetAwoofResultSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        game_id = serializer.validated_data.get("game_id")
        game_pin = serializer.validated_data.get("game_pin")
        lifestyle = []
        all_games = AwoofGameTable.objects.filter(ticket_id=game_id, game_pin=game_pin)
        if all_games:
            for game in all_games:
                lifestyle.append(
                    {
                        "user_profile": game.user_profile,
                        "agent": game.agent_profile,
                        "item": game.item.item_name,
                        "band": game.band,
                        "amount_paid": game.amount_paid,
                        "instant_cashout_amount": game.instant_cashout_amount,
                        "illusion_amount": game.illusion_amount,
                        "awoof_amount": game.awoof_amount,
                        "paid": game.paid,
                        "channel": game.channel,
                        "game_id": game.ticket_id,
                        "game_pin": game.game_pin,
                        "unique_ticket_id": game.unique_ticket_id,
                        "game_play_id": game.game_play_id,
                    }
                )

            data = {
                "message": "success",
                "awoof": lifestyle,
            }

            return Response(data, status=status.HTTP_200_OK)
        else:
            data = {"message": "invalid game_id"}
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class AskAwoofAIAPIView(APIView):
    permission_classes = [TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        IsAgentSuspendedPermission,
        IsAgentRemittanceDuePermission,
        IsBlackListedPermission,
    ]

    def post(self, request):
        agent_instance = Agent.objects.filter(Q(phone=request.user.phone) | Q(email=request.user.email)).last()

        if agent_instance is None:
            response = {"message": "Agent does not exist"}
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        serializer = PosLifeStylePlaySerializers(data=request.data)
        serializer.is_valid(raise_exception=True)
        pin = serializer.validated_data["pin"]
        serializer.data["ai_message"]
        data = serializer.data
        total_stake_amount = awoof_stake_amounts(data=data)

        # validate_remittance_amount
        response = LottoAgentRemittanceTable().valuate_amount_to_be_added(amount=total_stake_amount, agent=agent_instance)
        if response.get("is_amount_much"):
            response = {
               "message": f"Hi, kindly top up your lottowallet with this amount {Utility.currency_formatter(response.get('amount'))} to continue game play"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        phone = serializer.data["phone_no"]

        if phone == "" or phone is None:
            phone = agent_instance.phone

        serializer.data["phone_no"] = agent_instance.phone

        phone_no = LotteryModel.format_number_from_back_add_234(phone)

        # if AgentConstantVariables().get_phone_number_is_required() is True:

        # if agent_instance.phone == phone_no:

        #     response = {"message": "You cannot use your own phone number"}
        #     return Response(response, status=status.HTTP_400_BAD_REQUEST)

        # if phone is None or phone == "":
        #     phone = _agent_instance.phone

        # serializer.data["phone_number"] = phone

        # get or create user profile
        user_instance = UserProfile.create_user_profile_if_none_exist(phone_no=phone_no, channel="POS")
        games = serializer.validated_data.get("games")
        message_data = validate_pos_life_style_cash_price(games, "ASK_AI")
        if "is_price_valid" in message_data.keys():
            if message_data["is_price_valid"] is False:
                return Response(data=message_data, status=status.HTTP_400_BAD_REQUEST)
        price_data_val, price_data = check_price_validity(games)
        if price_data_val is False:
            return Response(data=price_data, status=status.HTTP_400_BAD_REQUEST)

        reg_and_payment = AwoofGameTable.register_ticket(
            request_payload=data,
            user_profile=user_instance,
            agent_profile=agent_instance,
            phone=phone_no,
            channel="POS_AGENT",
            service_type="ASK_AI",
            pin=pin,
        )

        return Response(reg_and_payment, status=status.HTTP_200_OK)


class WEBAskAIApiView(APIView):
    authentication_classes = [
        TokenAuthentication,
    ]
    permission_classes = [
        IsAuthenticated,
        PhoneNumberVerifedPermission,
        IsBlackListedPermission,
    ]

    @method_decorator(csrf_exempt)
    @method_decorator(ip_whitelist_middleware())
    def post(self, request, instance=None):
        player = UserProfile.objects.filter(Q(email=request.user.email) | Q(phone_number=request.user.phone)).last()

        if player is None:
            return Response(
                data={"message": "User profile does not exist"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if player.has_sudo_phone_number is True:
            data = {
                "message": "Please update your phone number to continue",
            }

            return Response(data=data, status=status.HTTP_400_BAD_REQUEST)

        serializer = WebLifeStylePlaySerializers(data=request.data)
        serializer.is_valid(raise_exception=True)
        from_referral_wallet = serializer.data["from_referral_wallet"]
        serializer.data["paystack"]
        from_play_wallet = serializer.data["from_play_wallet"]
        serializer.data["ai_message"]
        data = serializer.data

        total_stake_amount = awoof_stake_amounts(data=data)

        print("TOTAL STAKE AMOUNT ------------------------>", total_stake_amount)

        if from_referral_wallet is True:  # Pay using referral wallet
            lottery_wallet = ReferralWallet.objects.filter(user=player).last()

            if lottery_wallet is None:
                return Response(
                    data={"message": "Referral wallet not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            if lottery_wallet.can_use_wallet is False:
                data = {"message": "You cannot use referral wallet yet"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if lottery_wallet.available_balance < total_stake_amount:
                data = {"message": "Insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        if from_play_wallet is True:  # pay using play wallet
            main_wallet = UserWallet.objects.filter(user=player, wallet_tag="WEB").last()
            if main_wallet is None:
                UserWallet.objects.create(user=player, wallet_tag="WEB")
                data = {"message": "insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if main_wallet.game_available_balance < total_stake_amount:
                data = {"message": "insufficient funds"}
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        games = serializer.validated_data.get("games")
        message_data = validate_pos_life_style_cash_price(games, "ASK_AI")
        if "is_price_valid" in message_data.keys():
            if message_data["is_price_valid"] is False:
                return Response(data=message_data, status=status.HTTP_400_BAD_REQUEST)
        price_data_val, price_data = check_price_validity(games)
        if price_data_val is False:
            return Response(data=price_data, status=status.HTTP_400_BAD_REQUEST)
        reg_and_payment = AwoofGameTable.register_ticket(
            request_payload=data,
            user_profile=player,
            agent_profile=None,
            phone=player.phone_number,
            channel="WEB",
            service_type="ASK_AI",
            play_wallet=from_play_wallet,
        )

        return Response(reg_and_payment, status=status.HTTP_200_OK)


class AskAISubscriptionAPIView(APIView):
    @method_decorator(csrf_exempt)
    @method_decorator(ip_whitelist_middleware())
    def post(self, request, instance=None):
        serializer = AskAISubscriptionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        phone = serializer.data.get("phone_number")
        product_id = "1000005388"
        service_id = "0017182000003867"

        # make a request to the telco aggregator to subscribe the user to the game

        get_game_details_object = secure_d_and_upstream_service_and_prodct_details(product_id=service_id)

        if not get_game_details_object:
            return Response({"error": "Invalid service_id"}, status=status.HTTP_400_BAD_REQUEST)

        formatted_phone_number = LotteryModel.format_number_from_back_add_234(phone)

        generated_random_id = random.randint(100000, 999999)

        telco_charge_payload = {
            "phone_number": formatted_phone_number,
            "description": f"{str(get_game_details_object.get('product_name')).replace('_', ' ')} lottery payment",
            "amount": get_game_details_object.get("amount"),
            "game_play_id": generated_random_id,
            "lottery_type": get_game_details_object.get("product_name"),
            "pontential_winning": 5000,
            "service_id": service_id,
            "product_id": product_id,
        }

        telco_charge_payload["use_json_format"] = True
        telco_charge_payload["channel"] = "USSD"

        BBCTelcoAggregator().telco_airtime_subscription_activation(**telco_charge_payload)

        try:
            TelcoGameWebSubscribers.create_subscriber(
                phone_number=formatted_phone_number,
                product_id=product_id,
                service_id=service_id,
                network_provider="GLO",
                game_type=get_game_details_object.get("product_name"),
            )
        except Exception:
            pass

        send_ask_ai_user_chat_link(formatted_phone_number)
        return Response(
            {"message": "Game subscription sent. You'll get authorisation notification shortly"},
            status=status.HTTP_200_OK,
        )


class AskAIHistoryAPIView(APIView):
    @method_decorator(csrf_exempt)
    @method_decorator(ip_whitelist_middleware())
    def post(self, request):
        phone_number = request.query_params.get("phone")
        if not phone_number:
            return Response({"message": "phone is a required parameter"}, status=status.HTTP_400_BAD_REQUEST)

        all_questions_answers = OpenAiDataDump.objects.filter(user__phone_number=phone_number).order_by("-created_at")
        serializer = AskAIHistorySerializer(all_questions_answers, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class GetFortuneAI(generics.ListAPIView):
    pagination_class = CustomPagination
    serializer_class = ListFortuneAIModule
    filter_backends = (DjangoFilterBackend, filters.SearchFilter)
    search_fields = ("module_name",)

    def get_queryset(self):
        # Get all fortune AI Data
        all_sender_id = FortuneAIModule.objects.all().order_by("created_at")
        return all_sender_id

    @method_decorator(cache_page(60 * 2))
    def list(self, request, *args, **kwargs):
        phone_number = request.query_params.get("phone_number")
        view_count = NitroswitchAiFortuneSubscription.get_view_count(phone_number)

        queryset = self.filter_queryset(self.get_queryset())
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(page, many=True, context={"view_count": view_count})
        response_data = {
            "count": paginator.page.paginator.count,
            "next": paginator.get_next_link(),
            "previous": paginator.get_previous_link(),
            "results": serializer.data,
        }
        return Response(response_data, status=status.HTTP_200_OK)


class ProtectFortuneAIFile(APIView):
    def get(self, request, *args, **kwargs):
        file_path = kwargs.get("file_path", "")
        phone_number = request.query_params.get("pin")

        if not file_path or not phone_number:
            return Response("invalid file", status=status.HTTP_403_FORBIDDEN)

        # Normalize the file path to match how it's stored in the database
        relative_path = os.path.join("files/fortune_ai/", file_path)

        # Check if the phone_number is permitted
        view_count = NitroswitchAiFortuneSubscription.get_view_count(phone_number)
        if view_count <= 0:
            return Response("You are forbidden to access this file", status=status.HTTP_403_FORBIDDEN)
        try:
            document = FortuneAISubModule.objects.get(module_file=relative_path)
            file_count = document.module_day
        except FortuneAISubModule.DoesNotExist:
            return Response("File not found.", status=status.HTTP_404_NOT_FOUND)

        # Get the absolute file path
        full_path = document.module_file.path

        if not os.path.exists(full_path):
            return Response("File not found.", status=status.HTTP_404_NOT_FOUND)

        if view_count < file_count:
            return Response("You are not allowed to access this file.", status=status.HTTP_403_FORBIDDEN)

        # Serve the file
        return FileResponse(open(full_path, "rb"))
