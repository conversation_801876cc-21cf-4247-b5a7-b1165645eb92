from django.contrib import admin, messages
from import_export import resources
from import_export.admin import ImportExportModelAdmin

from awoof_app.models import (
    AskAiData,
    AwoofDrawTable,
    AwoofGameTable,
    AwoofTransaction,
    AwoofWalletSystem,
    AwoofWinnerPool,
    FortuneAIModule,
    FortuneAISubModule,
    LifeStyleTable,
    MoneyGiverTable,
    OpenAiDataDump,
    PerplexityDataDump,
    TrivialData,
)
from wyse_ussd.models import NitroSwitchSmsToAiSubscription, UssdConstantVariable

# Register your models here.


# RESOURCES
class LifeStyleTableResource(resources.ModelResource):
    class Meta:
        model = LifeStyleTable


class AwoofGameTableResource(resources.ModelResource):
    class Meta:
        model = AwoofGameTable


class AwoofDrawTableResource(resources.ModelResource):
    class Meta:
        model = AwoofDrawTable


class AwoofWinnerPoolResource(resources.ModelResource):
    class Meta:
        model = AwoofWinnerPool


class OpenAiDataDumpResource(resources.ModelResource):
    class Meta:
        model = OpenAiDataDump


class AskAiDataResource(resources.ModelResource):
    class Meta:
        model = AskAiData


class TrivialDataResource(resources.ModelResource):
    class Meta:
        model = TrivialData


class NitroSwitchSmsToAiSubscriptionResource(resources.ModelResource):
    class Meta:
        model = NitroSwitchSmsToAiSubscription


class FortuneAIModuleResource(resources.ModelResource):
    class Meta:
        model = FortuneAIModule


class FortuneAISubModuleResource(resources.ModelResource):
    class Meta:
        model = FortuneAISubModule


class PerplexityDataDumpResource(resources.ModelResource):
    class Meta:
        model = PerplexityDataDump


class AwoofWalletSystemResource(resources.ModelResource):
    class Meta:
        model = AwoofWalletSystem


class AwoofTransactionResource(resources.ModelResource):
    class Meta:
        model = AwoofTransaction


class MoneyGiverTableResource(resources.ModelResource):
    class Meta:
        model = MoneyGiverTable


#
# ADMINS


class LifeStyleTableResourceAdmin(ImportExportModelAdmin):
    resource_class = LifeStyleTableResource
    search_fields = [
        "id",
        "item_name",
        "item_uuid",
    ]
    list_filter = ("item_status", "item_given", "is_drawn", "is_active", "service_type", "auto_start", "is_auto_start_done", "is_cash_value")

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]

    actions = ("disassociate_lifestyle", "duplicate_lifestyle", "close_lifestyle", "open_lifestyle")

    @admin.action(description="Disassociate LifeStyle Items")
    def disassociate_lifestyle(modeladmin, request, queryset):
        associated_lifestyle_id = int(UssdConstantVariable().get_associate_lifestyle_item_id())
        get_associated_lifestyle = LifeStyleTable.objects.filter(id=associated_lifestyle_id).first()
        if get_associated_lifestyle:
            for obj in queryset:
                all_lifestyle = AwoofGameTable.objects.filter(item=obj)
                if all_lifestyle:
                    all_lifestyle.update(item=get_associated_lifestyle)
            queryset.delete()
            messages.success(request, "Successfully Disassociated LifeStyle Item(s)")
        else:
            messages.success(request, "Could not find Associated LifeStyle Item")

    def duplicate_lifestyle(modeladmin, request, queryset):
        for obj in queryset:
            LifeStyleTable.duplicate_life_style(obj)
        messages.success(request, "Successfully Duplicated LifeStyle Items(s)")

    def close_lifestyle(modeladmin, request, queryset):
        for obj in queryset:
            LifeStyleTable.close_lifestyle(obj)
        messages.success(request, "Successfully Closed LifeStyle Items(s)")

    def open_lifestyle(modeladmin, request, queryset):
        for obj in queryset:
            LifeStyleTable.open_lifestyle(obj)
        messages.success(request, "Successfully Opened LifeStyle Items(s)")


class AwoofGameTableResourceAdmin(ImportExportModelAdmin):
    resource_class = AwoofGameTableResource
    search_fields = [
        "ticket_id",
        "game_play_id",
        "unique_ticket_id",
        "item__item_name",
        "phone",
        "user_profile__phone_number",
        "lottery_type",
        "game_pin",
        "item__item_uuid",
    ]
    list_filter = (
        "date_created",
        "telco_network",
        "channel",
        "paid",
        "played_via_telco_channel",
        "is_utilized",
        "service_type",
    )
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AwoofDrawTableResourceAdmin(ImportExportModelAdmin):
    resource_class = AwoofDrawTableResource

    autocomplete_fields = ["lifestyle_item", "item_winner", "agent"]
    search_fields = [
        "ticket_id",
        "ticket_pin",
        "item_winner__phone_number",
        "agent__first_name",
        "agent__last_name",
        "lifestyle_item__item_uuid",
    ]
    list_filter = ("is_redeemed",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AwoofWinnerPoolResourceAdmin(ImportExportModelAdmin):
    resource_class = AwoofWinnerPoolResource

    # autocomplete_fields = ["lifestyle_item", "item_winner", "agent"]
    search_fields = ["item__item_uuid", "winner__phone_number", "drawable_tickets", "winning_ticket", "ticket_id,"]
    # list_filter = ("is_redeemed",)

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class OpenAiDataDumpResourceAdmin(ImportExportModelAdmin):
    resource_class = OpenAiDataDumpResource

    search_fields = ["user__phone_number", "phone"]
    list_filter = ("created_at", "service")
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AskAiDataResourceAdmin(ImportExportModelAdmin):
    resource_class = AskAiDataResource

    search_fields = [
        "phone_number",
    ]
    list_filter = ("created_at",)

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class TrivialDataResourceAdmin(ImportExportModelAdmin):
    resource_class = TrivialDataResource

    search_fields = [
        "question",
    ]
    list_filter = ("created_at",)

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class NitroSwitchSmsToAiSubscriptionResourceAdmin(ImportExportModelAdmin):
    resource_class = NitroSwitchSmsToAiSubscriptionResource

    search_fields = [
        "phone_number",
    ]
    list_filter = ("created_at",)

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class FortuneAIModuleResourceAdmin(ImportExportModelAdmin):
    resource_class = FortuneAIModuleResource

    search_fields = ["module_name", "module_overview"]
    list_filter = ("created_at",)

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class FortuneAISubModuleResourceAdmin(ImportExportModelAdmin):
    resource_class = FortuneAISubModuleResource

    search_fields = ["module_day", "module_name", "module__module_name"]
    list_filter = ("created_at",)

    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class PerplexityDataDumpResourceAdmin(ImportExportModelAdmin):
    resource_class = PerplexityDataDumpResource

    search_fields = ["user__phone_number", "phone"]
    list_filter = ("created_at", "service")
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AwoofWalletSystemResourceAdmin(ImportExportModelAdmin):
    resource_class = AwoofWalletSystemResource
    list_filter = ("created_at", "wallet_type")
    date_hierarchy = "created_at"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class AwoofTransactionResourceAdmin(ImportExportModelAdmin):
    resource_class = AwoofTransactionResource
    search_fields = ["user__phone_number"]
    list_filter = ("date_created", "type_of_trans", "entry")
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


class MoneyGiverTableResourceAdmin(ImportExportModelAdmin):
    resource_class = MoneyGiverTableResource
    search_fields = ["user__phone_number"]
    list_filter = ("date_created", "service_type")
    date_hierarchy = "date_created"

    def get_list_display(self, request):
        return [field.name for field in self.model._meta.concrete_fields]


admin.site.register(LifeStyleTable, LifeStyleTableResourceAdmin)
admin.site.register(AwoofGameTable, AwoofGameTableResourceAdmin)
admin.site.register(AwoofDrawTable, AwoofDrawTableResourceAdmin)
admin.site.register(AwoofWinnerPool, AwoofWinnerPoolResourceAdmin)
admin.site.register(OpenAiDataDump, OpenAiDataDumpResourceAdmin)
admin.site.register(AskAiData, AskAiDataResourceAdmin)
admin.site.register(TrivialData, TrivialDataResourceAdmin)
admin.site.register(NitroSwitchSmsToAiSubscription, NitroSwitchSmsToAiSubscriptionResourceAdmin)
admin.site.register(FortuneAIModule, FortuneAIModuleResourceAdmin)
admin.site.register(FortuneAISubModule, FortuneAISubModuleResourceAdmin)
admin.site.register(PerplexityDataDump, PerplexityDataDumpResourceAdmin)
admin.site.register(AwoofWalletSystem, AwoofWalletSystemResourceAdmin)
admin.site.register(AwoofTransaction, AwoofTransactionResourceAdmin)
admin.site.register(MoneyGiverTable, MoneyGiverTableResourceAdmin)
