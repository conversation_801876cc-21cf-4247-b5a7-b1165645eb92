import random
import time
import uuid
from datetime import datetime

import cloudinary
from celery import shared_task
from django.db.models import Q, Sum
from django.utils.timezone import make_aware

from awoof_app.helpers.api_helpers import notify_awoof_non_winners, notify_awoof_winner
from main.helpers.helper_functions import mask_winners_phone_number


@shared_task
def remove_cloudinary_image(image1, image2, image3, image4):
    cloudinary.uploader.destroy(image1)
    cloudinary.uploader.destroy(image2)
    cloudinary.uploader.destroy(image3)
    cloudinary.uploader.destroy(image4)


@shared_task
def last_game_daily_draw():
    from awoof_app.models import (
        AwoofDrawTable,
        AwoofGameTable,
        AwoofWinnerPool,
        LifeStyleTable,
    )

    all_daily_draw_items = LifeStyleTable.objects.filter(daily_draw=True, daily_draw_count=1, daily_draw_is_completed=False)
    for single_daily_draw in all_daily_draw_items:
        draw_count = single_daily_draw.ticket_draw_count
        if draw_count > 0 and single_daily_draw.item_amount != "0":
            item_id = single_daily_draw.id

            required_amount = float(str(single_daily_draw.item_amount).replace(",", ""))
            all_tickets_played = AwoofGameTable.objects.filter(Q(item__id=item_id, paid=True, item__is_drawn=False))
            all_tickets = all_tickets_played.filter(Q(unique_ticket_id__isnull=False))
            amount_contributed = all_tickets_played.aggregate(total=Sum("awoof_amount"))["total"] or 0

            if amount_contributed >= required_amount:
                draw_ticket_list = list(all_tickets.values_list("unique_ticket_id", flat=True))
                random.shuffle(draw_ticket_list)  # Shuffle once
                winning_ticket = random.choice(draw_ticket_list)

                ticket_instance = all_tickets.filter(unique_ticket_id=winning_ticket).first()
                if ticket_instance:
                    # Fetch winner and associated data
                    user = ticket_instance.user_profile
                    agent = ticket_instance.agent_profile
                    ticket_id = ticket_instance.ticket_id
                    game_pin = ticket_instance.game_pin
                    service_type = ticket_instance.service_type

                    # Update the draw table
                    AwoofDrawTable.update_winner(
                        single_daily_draw,
                        user,
                        agent,
                        ticket_id,
                        game_pin,
                        service_type,
                    )
                    winner_phone = user.phone_number
                    item_name = ticket_instance.item.item_name
                    notify_awoof_winner(winner_phone, item_name, winning_ticket)

                    all_users = (
                        AwoofGameTable.objects.exclude(
                            paid=False,
                            item__is_drawn=True,
                            user_profile__phone_number=user.phone_number,
                        )
                        .filter(Q(item__id=item_id, unique_ticket_id__isnull=False) & ~Q(user_profile__phone_number=user.phone_number))
                        .order_by()
                        .values("user_profile__phone_number")
                        .distinct()
                    )
                    AwoofWinnerPool.objects.create(
                        item=single_daily_draw,
                        drawable_tickets=draw_ticket_list,
                        winning_ticket=winning_ticket,
                        winner=user,
                        ticket_draw_count=all_tickets.count(),
                        amount_contributed=amount_contributed,
                        drawable_phone_numbers=[user["user_profile__phone_number"] for user in all_users],
                        ticket_id=ticket_id,
                        service_type=service_type,
                    )
                    non_winners_phone = [user["user_profile__phone_number"] for user in all_users]
                    for phone in non_winners_phone:
                        masked_phone = mask_winners_phone_number(winner_phone)
                        notify_awoof_non_winners(phone, item_name, masked_phone)
                    # Awoof credit winner if the item is a cash item
                    if single_daily_draw.is_cash_value:
                        cash_value = float(str(single_daily_draw.item_amount).replace(",", ""))
                        credit_winner.delay(user_id=user.id, cash_value=cash_value, winner_phone=winner_phone)

            send_sms_to_awoof_telco_players.delay(item_id)

        single_daily_draw.daily_draw_is_completed = True
        single_daily_draw.daily_draw_count = 0
        single_daily_draw.item_status = "CLOSED"
        single_daily_draw.is_active = False
        single_daily_draw.save()

    return "LAST DAILY DRAW DONE"


@shared_task
def send_sms_to_awoof_telco_players(item_id):
    from awoof_app.models import AwoofGameTable, AwoofWinnerPool, LifeStyleTable
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    broad_base_helper = BBCTelcoAggregator()

    awoof_game_table = AwoofGameTable.objects.filter(item__id=item_id, played_via_telco_channel=True)

    winners_pool = AwoofWinnerPool.objects.filter(item__id=item_id)

    winners_phone_number = list(winners_pool.values_list("winner__phone_number", flat=True))

    if len(winners_phone_number) == 0:
        return "NO WINNER"

    losers = awoof_game_table.exclude(user_profile__phone_number__in=winners_phone_number)

    losers_phone_number = list(losers.values_list("user_profile__phone_number", flat=True))

    if len(losers_phone_number) == 0:
        return "NO LOSER"

    list_item = LifeStyleTable.objects.filter(id=item_id).first()

    for phone in losers_phone_number:
        message = f"WinWise  {list_item.item_name} was Won in our fast finger game draw. stay subscribed. Dial *20144#"

        sms_payload = {
            "phone_number": phone,
            # "sender_name": "WINWISE",
            "sender_name": "20144",
            "message": message,
        }

        sms_payload["use_json_format"] = True
        broad_base_helper.bbc_send_sms(**sms_payload)


@shared_task
def signal_lifestyle_winner(item_id):
    from awoof_app.models import (
        AwoofDrawTable,
        AwoofGameTable,
        AwoofWinnerPool,
        LifeStyleTable,
    )

    specified_date = make_aware(datetime(2025, 2, 22))
    instance = LifeStyleTable.objects.filter(id=item_id).first()
    if instance:
        if instance.service_type == "AWOOF" and instance.item_amount != "0":
            required_amount = float(str(instance.item_amount).replace(",", ""))
            all_tickets_played = AwoofGameTable.objects.filter(Q(item__id=item_id, paid=True, item__is_drawn=False))
            all_tickets = all_tickets_played.filter(Q(unique_ticket_id__isnull=False))
            amount_contributed = all_tickets_played.aggregate(total=Sum("awoof_amount"))["total"] or 0

            if amount_contributed >= required_amount:
                # Draw a random winner
                draw_ticket_list = list(all_tickets.values_list("unique_ticket_id", flat=True))
                random.shuffle(draw_ticket_list)  # Shuffle once
                winning_ticket = random.choice(draw_ticket_list)

                ticket_instance = all_tickets.filter(unique_ticket_id=winning_ticket).first()
                if ticket_instance:
                    # Fetch winner and associated data
                    user = ticket_instance.user_profile
                    agent = ticket_instance.agent_profile
                    ticket_id = ticket_instance.ticket_id
                    game_pin = ticket_instance.game_pin

                    if instance.date_created > specified_date:
                        # Update the draw table
                        winner_updated = AwoofDrawTable.update_winner(instance, user, agent, ticket_id, game_pin, instance.service_type)
                        if winner_updated is True:
                            # Notify the winner
                            winner_phone = user.phone_number
                            item_name = ticket_instance.item.item_name

                            # Notify non-winners
                            all_users = (
                                AwoofGameTable.objects.exclude(
                                    paid=False,
                                    item__is_drawn=True,
                                    user_profile__phone_number=user.phone_number,
                                )
                                .filter(Q(item__id=item_id, unique_ticket_id__isnull=False) & ~Q(user_profile__phone_number=user.phone_number))
                                .order_by()
                                .values("user_profile__phone_number")
                                .distinct()
                            )
                            AwoofWinnerPool.objects.create(
                                item=instance,
                                drawable_tickets=draw_ticket_list,
                                winning_ticket=winning_ticket,
                                winner=user,
                                ticket_draw_count=all_tickets.count(),
                                amount_contributed=amount_contributed,
                                drawable_phone_numbers=[user["user_profile__phone_number"] for user in all_users],
                                ticket_id=ticket_id,
                            )
                            non_winners_phone = [user["user_profile__phone_number"] for user in all_users]
                            for phone in non_winners_phone:
                                masked_phone = mask_winners_phone_number(winner_phone)
                                notify_awoof_non_winners(phone, item_name, masked_phone)
                            # Handle cash-based prizes
                            if instance.is_cash_value:
                                cash_value = required_amount
                                credit_winner.delay(user_id=user.id, cash_value=cash_value, winner_phone=winner_phone)
                                notify_awoof_winner(winner_phone, item_name, winning_ticket)
            return True
        elif instance.service_type == "INSURANCE" and instance.item_amount != "0":
            required_amount = float(instance.ai_ticket_draw_amount)
            if required_amount > 0:
                all_tickets_played = AwoofGameTable.objects.filter(Q(item__id=item_id, paid=True, item__is_drawn=False))
                all_tickets = all_tickets_played.filter(Q(unique_ticket_id__isnull=False))
                amount_contributed = all_tickets_played.aggregate(total=Sum("awoof_amount"))["total"] or 0

                if amount_contributed >= required_amount:
                    # Draw a random winner
                    draw_ticket_list = list(all_tickets.values_list("unique_ticket_id", flat=True))
                    random.shuffle(draw_ticket_list)  # Shuffle once
                    winning_ticket = random.choice(draw_ticket_list)

                    ticket_instance = all_tickets.filter(unique_ticket_id=winning_ticket).first()
                    if ticket_instance:
                        # Fetch winner and associated data
                        user = ticket_instance.user_profile
                        agent = ticket_instance.agent_profile
                        ticket_id = ticket_instance.ticket_id
                        game_pin = ticket_instance.game_pin

                        if instance.date_created > specified_date:
                            # Update the draw table
                            winner_updated = AwoofDrawTable.update_winner(instance, user, agent, ticket_id, game_pin, instance.service_type)
                            if winner_updated is True:
                                # Notify the winner
                                winner_phone = user.phone_number
                                item_name = ticket_instance.item.item_name

                                # Notify non-winners
                                all_users = (
                                    AwoofGameTable.objects.exclude(
                                        paid=False,
                                        item__is_drawn=True,
                                        user_profile__phone_number=user.phone_number,
                                    )
                                    .filter(Q(item__id=item_id, unique_ticket_id__isnull=False) & ~Q(user_profile__phone_number=user.phone_number))
                                    .order_by()
                                    .values("user_profile__phone_number")
                                    .distinct()
                                )
                                AwoofWinnerPool.objects.create(
                                    item=instance,
                                    drawable_tickets=draw_ticket_list,
                                    winning_ticket=winning_ticket,
                                    winner=user,
                                    ticket_draw_count=all_tickets.count(),
                                    amount_contributed=amount_contributed,
                                    drawable_phone_numbers=[user["user_profile__phone_number"] for user in all_users],
                                    ticket_id=ticket_id,
                                )
                                non_winners_phone = [user["user_profile__phone_number"] for user in all_users]
                                for phone in non_winners_phone:
                                    masked_phone = mask_winners_phone_number(winner_phone)
                                    notify_awoof_non_winners(phone, item_name, masked_phone)
                                # Handle cash-based prizes
                                if instance.is_cash_value:
                                    cash_value = required_amount
                                    credit_winner.delay(user_id=user.id, cash_value=cash_value, winner_phone=winner_phone)
                                    notify_awoof_winner(winner_phone, item_name, winning_ticket)
            return True
        else:
            pass
        # elif instance.service_type == "ASK_AI" and instance.item_amount != "0":
        # all_tickets_played = AwoofGameTable.objects.filter(
        #     Q(item__id=item_id, paid=True, item__is_drawn=False)
        # )
        # all_tickets = all_tickets_played.filter(Q(unique_ticket_id__isnull=False))
        # amount_contributed = all_tickets_played.aggregate(total=Sum("awoof_amount"))["total"] or 0

        # if float(instance.ai_ticket_draw_amount) > 0 and float(amount_contributed) >= float(instance.ai_ticket_draw_amount):
        #     # Draw a random winner
        #     draw_ticket_list = list(all_tickets.values_list("unique_ticket_id", flat=True))
        #     random.shuffle(draw_ticket_list)  # Shuffle once
        #     winning_ticket = random.choice(draw_ticket_list)
        #     # Fetch winner and associated data
        #     ticket_instance = all_tickets.filter(unique_ticket_id=winning_ticket).first()
        #     if ticket_instance:
        #         user = ticket_instance.user_profile
        #         agent = ticket_instance.agent_profile
        #         ticket_id = ticket_instance.ticket_id
        #         game_pin = ticket_instance.game_pin

        #         # Update the draw table
        #         AwoofDrawTable.update_winner(
        #             instance,
        #             user,
        #             agent,
        #             ticket_id,
        #             game_pin,
        #             instance.service_type
        #         )
        #     else:
        #         pass
        # else:
        #     pass
        # else:
        #     pass


@shared_task
def credit_winner(user_id, cash_value, winner_phone):
    from main.models import UserProfile
    from wallet_app.models import DebitCreditRecord, UserWallet

    user = UserProfile.objects.filter(id=user_id).first()
    if user:
        transaction_ref = f"{uuid.uuid4()}-{int(time.time())}"
        debit_credit_record = DebitCreditRecord.create_record(
            phone_number=winner_phone,
            amount=cash_value,
            channel="WEB",
            reference=transaction_ref,
            transaction_type="CREDIT",
        )
        wallet_payload = {"transaction_from": "AWOOF_GAME_WON"}
        UserWallet.fund_wallet(
            user=user,
            amount=cash_value,
            channel="WEB",
            transaction_id=debit_credit_record.reference,
            user_wallet_type="WINNINGS_WALLET",
            **wallet_payload,
        )
