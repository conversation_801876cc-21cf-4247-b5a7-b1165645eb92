import random

from django.db.models import Q, Sum
from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver

from awoof_app.helpers.api_helpers import notify_awoof_non_winners, notify_awoof_winner
from awoof_app.models import (
    AwoofDrawTable,
    AwoofGameTable,
    AwoofWinnerPool,
    LifeStyleTable,
)
from awoof_app.tasks import remove_cloudinary_image
from main.helpers.helper_functions import mask_winners_phone_number


@receiver(pre_delete, sender=LifeStyleTable)
def photo_delete(sender, instance, **kwargs):
    image1 = instance.item_image_1.public_id
    image2 = instance.item_image_2.public_id
    image3 = instance.item_image_3.public_id
    image4 = instance.item_image_4.public_id
    remove_cloudinary_image.delay(image1, image2, image3, image4)


@receiver(post_save, sender=LifeStyleTable)
def winner_awoof_ticket(sender, instance, created, **kwargs):
    from awoof_app.tasks import credit_winner

    """
    This method is used to select the winner ticket for Awoof
    """
    if not created:
        if instance.daily_draw and instance.item_amount != "0":
            if instance.daily_draw_is_completed is False:
                get_daily_count = instance.daily_draw_count
                if get_daily_count > 1:
                    draw_count = instance.ticket_draw_count
                    if draw_count > 0:
                        required_amount = float(str(instance.item_amount).replace(",", ""))
                        item_id = instance.id

                        all_tickets_played = AwoofGameTable.objects.filter(Q(item__id=item_id, paid=True, item__is_drawn=False))
                        all_tickets = all_tickets_played.filter(Q(unique_ticket_id__isnull=False))
                        amount_contributed = all_tickets_played.aggregate(total=Sum("awoof_amount"))["total"] or 0
                        if instance.service_type == "AWOOF":
                            if amount_contributed >= required_amount:
                                # Draw a random winner
                                draw_ticket_list = list(all_tickets.values_list("unique_ticket_id", flat=True))
                                random.shuffle(draw_ticket_list)  # Shuffle once
                                winning_ticket = random.choice(draw_ticket_list)

                                ticket_instance = all_tickets.filter(unique_ticket_id=winning_ticket).first()
                                if ticket_instance:
                                    # Fetch winner and associated data
                                    user = ticket_instance.user_profile
                                    agent = ticket_instance.agent_profile
                                    ticket_id = ticket_instance.ticket_id
                                    game_pin = ticket_instance.game_pin

                                    # Update the draw table
                                    status = AwoofDrawTable.update_winner(instance, user, agent, ticket_id, game_pin, instance.service_type)
                                    if status is True:
                                        # Notify the winner
                                        winner_phone = user.phone_number
                                        item_name = ticket_instance.item.item_name

                                        all_users = (
                                            AwoofGameTable.objects.exclude(
                                                paid=False,
                                                item__is_drawn=True,
                                                user_profile__phone_number=user.phone_number,
                                            )
                                            .filter(
                                                Q(item__id=item_id, unique_ticket_id__isnull=False) & ~Q(user_profile__phone_number=user.phone_number)
                                            )
                                            .order_by()
                                            .values("user_profile__phone_number")
                                            .distinct()
                                        )
                                        AwoofWinnerPool.objects.create(
                                            item=instance,
                                            drawable_tickets=draw_ticket_list,
                                            winning_ticket=winning_ticket,
                                            winner=user,
                                            ticket_draw_count=all_tickets.count(),
                                            amount_contributed=amount_contributed,
                                            drawable_phone_numbers=[user["user_profile__phone_number"] for user in all_users],
                                            ticket_id=ticket_id,
                                        )
                                        non_winners_phone = [user["user_profile__phone_number"] for user in all_users]
                                        for phone in non_winners_phone:
                                            masked_phone = mask_winners_phone_number(winner_phone)
                                            notify_awoof_non_winners(phone, item_name, masked_phone)

                                        # Handle cash-based prizes
                                        if instance.is_cash_value:
                                            cash_value = required_amount
                                            credit_winner.delay(user_id=user.id, cash_value=cash_value, winner_phone=winner_phone)
                                            notify_awoof_winner(winner_phone, item_name, winning_ticket)

                                        LifeStyleTable.objects.create(
                                            item_name=instance.item_name,
                                            item_image_1=instance.item_image_1,
                                            item_image_2=instance.item_image_2,
                                            item_image_3=instance.item_image_3,
                                            item_image_4=instance.item_image_4,
                                            item_amount=instance.item_amount,
                                            ticket_price=instance.ticket_price,
                                            min_base_amount=instance.min_base_amount,
                                            mid_base_amount=instance.mid_base_amount,
                                            max_base_amount=instance.max_base_amount,
                                            min_gift_amount=instance.min_gift_amount,
                                            mid_gift_amount=instance.mid_gift_amount,
                                            max_gift_amount=instance.max_gift_amount,
                                            min_instant_cashout_amount=instance.min_instant_cashout_amount,
                                            mid_instant_cashout_amount=instance.mid_instant_cashout_amount,
                                            max_instant_cashout_amount=instance.max_instant_cashout_amount,
                                            min_illusion_amount=instance.min_illusion_amount,
                                            mid_illusion_amount=instance.mid_illusion_amount,
                                            max_illusion_amount=instance.max_illusion_amount,
                                            daily_draw=True,
                                            is_active=True,
                                            item_status="OPEN",
                                            is_cash_value=instance.is_cash_value,
                                            daily_draw_uuid=instance.daily_draw_uuid,
                                            daily_draw_count=instance.daily_draw_count - 1,
                                            telco_instant_cashout_amount=instance.telco_instant_cashout_amount,
                                            telco_instant_cashout_rtp=instance.telco_instant_cashout_rtp,
                                            telco_instant_cashout_rto=instance.telco_instant_cashout_rto,
                                            telco_aggregator_amount=instance.telco_aggregator_amount,
                                            telco_commission_amount=instance.telco_commission_amount,
                                        )
                                        instance.daily_draw_count = 0
                                        instance.daily_draw_is_completed = True
                                        instance.save()
                                        return True

                                else:
                                    pass
                            else:
                                pass
                        else:
                            pass
                    else:
                        pass

                else:
                    pass
            else:
                pass
        else:
            pass
