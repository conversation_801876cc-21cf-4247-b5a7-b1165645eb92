import math
import operator as op
import re
import secrets
import string
import sys
from datetime import datetime
from time import sleep

import openai
import requests
from django.conf import settings
from django.db.models import Q, Sum
from django.utils import timezone

nigerian_prefixes = {
    # MTN Nigeria
    "234803": "MTN",
    "234806": "MTN",
    "234703": "MTN",
    "234706": "MTN",
    "234813": "MTN",
    "234816": "MTN",
    "234810": "MTN",
    "234814": "MTN",
    "234903": "MTN",
    "234906": "MTN",
    # Airtel Nigeria
    "234802": "AIRT<PERSON>",
    "234808": "AIRTEL",
    "234708": "AIRT<PERSON>",
    "234812": "AIRTEL",
    "234902": "AIRTEL",
    "234907": "AIRTEL",
    # Glo Mobile (Globacom)
    "234805": "GLO",
    "234705": "<PERSON>L<PERSON>",
    "234905": "<PERSON><PERSON><PERSON>",
    # 9mobile
    "234809": "9<PERSON><PERSON><PERSON>",
    "234817": "9<PERSON><PERSON><PERSON>",
    "234818": "9<PERSON><PERSON><PERSON>",
    "234908": "9<PERSON><PERSON><PERSON>",
    "234909": "9MO<PERSON><PERSON>",
    "23491": "9MO<PERSON>LE",
}


def validate_pos_life_style_cash_price(serialized_data, service_type):
    from awoof_app.helpers.api_helpers import instant_cashout_pricing_by_lines
    from awoof_app.models import LifeStyleTable

    message = {}
    i_count = 0
    for item in serialized_data:
        if item:
            item_id = item.get("item_id")
            item["life_style"] = sorted(item.get("life_style"), key=lambda x: x["tier"])
            item["i_cashout"] = sorted(item.get("i_cashout"), key=lambda x: x["stake_amount"])
            life_style = item.get("life_style")
            instant_cashout = item.get("i_cashout")
            awoof_instance = LifeStyleTable.objects.filter(id=item_id, item_status="OPEN", service_type=service_type).first()
            if awoof_instance:
                # if float(awoof_instance.item_amount.replace(",", "")) <= 0 or awoof_instance.percentage >= 100:
                #     message = {
                #         "is_price_valid": False,
                #         "item_id": item_id,
                #         "band": 0,
                #         "message": f"this item is not available for play",
                #     }
                #     break
                awoof_game = []
                instant_cashout_list = []
                life_style_list = []
                for awoof in life_style:
                    tiers = awoof.get("tier")
                    tickets = awoof.get("tickets")
                    item_tiers = [
                        awoof_instance.min_base_amount,
                        awoof_instance.mid_base_amount,
                        awoof_instance.max_base_amount,
                    ]
                    item_tier = []
                    for i in range(0, len(item_tiers)):
                        if item_tiers[i] == tiers:
                            if i == 0:
                                item_tier.append("life_style_min")
                                awoof["illusion_stake_amount"] = awoof_instance.min_illusion_amount
                                awoof["i_cash_stake_amount"] = awoof_instance.min_instant_cashout_amount
                                awoof["gift_amount"] = awoof_instance.min_gift_amount
                                awoof["base_amount"] = awoof_instance.min_base_amount
                                all_tier_tickets = {}
                                for ticket in tickets:
                                    stake_amount = int(ticket.get("stake_amount"))
                                    if stake_amount == int(awoof_instance.min_gift_amount):
                                        instant_cashout_list.append(awoof_instance.min_instant_cashout_amount)
                                        if stake_amount in all_tier_tickets.keys():
                                            all_tier_tickets[stake_amount] += 1
                                            all_tier_tickets["total_instant_cashout"] += int(awoof_instance.min_instant_cashout_amount)

                                        else:
                                            all_tier_tickets.update(
                                                {
                                                    "type": "min_gift_amount",
                                                    stake_amount: 1,
                                                    "life_style": int(awoof_instance.min_gift_amount),
                                                    "instant_cashout": int(awoof_instance.min_instant_cashout_amount),
                                                    "total_instant_cashout": int(awoof_instance.min_instant_cashout_amount),
                                                }
                                            )

                                    else:
                                        message = {
                                            "is_price_valid": False,
                                            "item_id": item_id,
                                            "band": tiers,
                                            "message": f"the correct stake amount is {int(awoof_instance.min_gift_amount)} not {stake_amount}",
                                        }
                                awoof_game.append(all_tier_tickets)
                                break
                            elif i == 1:
                                item_tier.append("life_style_mid")
                                awoof["illusion_stake_amount"] = awoof_instance.mid_illusion_amount
                                awoof["i_cash_stake_amount"] = awoof_instance.mid_instant_cashout_amount
                                awoof["gift_amount"] = awoof_instance.mid_gift_amount
                                awoof["base_amount"] = awoof_instance.mid_base_amount
                                all_tier_tickets = {}
                                for ticket in tickets:
                                    stake_amount = int(ticket.get("stake_amount"))
                                    instant_cashout_list.append(awoof_instance.mid_instant_cashout_amount)
                                    if stake_amount == int(awoof_instance.mid_gift_amount):
                                        # print(stake_amount, "STAKE AMOUNT>>>>>>>>")
                                        if stake_amount in all_tier_tickets.keys():
                                            all_tier_tickets[stake_amount] += 1
                                            all_tier_tickets["total_instant_cashout"] += int(awoof_instance.mid_instant_cashout_amount)

                                        else:
                                            all_tier_tickets.update(
                                                {
                                                    "type": "mid_gift_amount",
                                                    stake_amount: 1,
                                                    "life_style": int(awoof_instance.mid_gift_amount),
                                                    "instant_cashout": int(awoof_instance.mid_instant_cashout_amount),
                                                    "total_instant_cashout": int(awoof_instance.mid_instant_cashout_amount),
                                                }
                                            )

                                    else:
                                        message = {
                                            "is_price_valid": False,
                                            "item_id": item_id,
                                            "band": tiers,
                                            "message": f"the correct stake amount is {int(awoof_instance.mid_gift_amount)} not {stake_amount}",
                                        }
                                awoof_game.append(all_tier_tickets)
                                break
                            elif i == 2:
                                item_tier.append("life_style_max")
                                awoof["illusion_stake_amount"] = awoof_instance.max_illusion_amount
                                awoof["i_cash_stake_amount"] = awoof_instance.max_instant_cashout_amount
                                awoof["gift_amount"] = awoof_instance.max_gift_amount
                                awoof["base_amount"] = awoof_instance.max_base_amount
                                all_tier_tickets = {}
                                for ticket in tickets:
                                    stake_amount = int(ticket.get("stake_amount"))
                                    instant_cashout_list.append(awoof_instance.max_instant_cashout_amount)
                                    if stake_amount == int(awoof_instance.max_gift_amount):
                                        if stake_amount in all_tier_tickets.keys():
                                            all_tier_tickets[stake_amount] += 1
                                            all_tier_tickets["total_instant_cashout"] += int(awoof_instance.max_instant_cashout_amount)

                                        else:
                                            all_tier_tickets.update(
                                                {
                                                    "type": "max_gift_amount",
                                                    stake_amount: 1,
                                                    "life_style": int(awoof_instance.max_gift_amount),
                                                    "instant_cashout": int(awoof_instance.max_instant_cashout_amount),
                                                    "total_instant_cashout": int(awoof_instance.max_instant_cashout_amount),
                                                }
                                            )

                                    else:
                                        message = {
                                            "is_price_valid": False,
                                            "item_id": item_id,
                                            "band": tiers,
                                            "message": f"the correct stake amount is {int(awoof_instance.max_gift_amount)} not {stake_amount}",
                                        }
                                awoof_game.append(all_tier_tickets)
                                break
                            else:
                                message = {
                                    "is_price_valid": False,
                                    "item_id": item_id,
                                    "band": tiers,
                                    "message": "this is not a valid tier",
                                }

                                break
                for style in instant_cashout:
                    stake_amount = style.get("stake_amount")
                    i_cash_count = len(style.get("lines"))
                    stake = instant_cashout_pricing_by_lines(i_cash_count)
                    real_stake = stake.get("total_amount", 0)
                    if real_stake == stake_amount:
                        if stake["lines"] == i_cash_count:
                            pass
                        else:
                            message = {"is_price_valid": False, "item_id": item_id, "message": "invalid ticket"}
                            break
                        life_style_list.append(style.get("stake_amount"))
                    # elif real_stake <= stake_amount:
                    else:
                        life_style_list.append(style.get("stake_amount"))
                        message = {"is_price_valid": False, "item_id": item_id, "message": "invalid ticket"}
                        break

                check_id = checkIdentical(instant_cashout_list, life_style_list)
                # print(instant_cashout_list, "I C LIST")
                # print(life_style_list, i_count, check_id, "L S LIST")

                if check_id is False or i_count > 0:
                    message = {"is_price_valid": False, "item_id": item_id, "message": "invalid ticket"}
                    break

            else:
                message = {"is_price_valid": False, "item_id": item_id, "message": "please select a valid item_id"}
                break

    return message


def split_life_style_ticket(serialized_data):
    games = serialized_data.get("games")
    life_style = [ls for item in games for ls in item.get("life_style", [])]
    instant_cashout = [ic for item in games for ic in item.get("i_cashout", [])]
    return life_style, instant_cashout


def checkIdentical(test_list1, test_list2):
    for i in test_list1:
        if op.countOf(test_list1, i) != op.countOf(test_list2, i):
            return False
    if len(test_list1) != len(test_list2):
        return False
    return True


def check_price_validity(data):
    from awoof_app.models import LifeStyleTable

    is_valid = False
    valid_count = 0
    for game in data:
        stake = 0
        game_data = {}
        stake = 0
        for lifestyle in game.get("life_style", []):
            tier = lifestyle.get("tier", 0)
            tickets = lifestyle.get("tickets")
            total_stake = lifestyle.get("total_stake_amount")
            result = tier * len(tickets)
            percent = lifestyle.get("percent", 0)
            stake_amount = tickets[0]["stake_amount"]
            item_percentage_id = game.get("item_id")
            item_percentage_instance = LifeStyleTable.objects.filter(id=item_percentage_id).first()
            item_percentage_price = float(item_percentage_instance.item_amount.replace(",", ""))
            user_percent = (stake_amount / item_percentage_price) * 100
            real_percent = truncate(user_percent)
            if percent == real_percent:
                # #print(percent, "This is user percentage", real_percent, "This is real percentage")
                correct_stake = 0
                for ticket in tickets:
                    correct_stake += ticket["stake_amount"]
                    # #print(correct_stake, "correct total stake")
                if correct_stake == total_stake:
                    continue
                else:
                    game_data.update(
                        {
                            "item_id": item_percentage_id,
                            "band": tier,
                            "message": f"correct total_stake_amount for {item_percentage_instance.item_name} is {correct_stake}",
                        }
                    )
                    valid_count += 1
                    break
            else:
                # #print(percent, "This is user percentage", real_percent, "This is real percentage")
                game_data.update(
                    {
                        "item_id": item_percentage_id,
                        "band": tier,
                        "message": f"correct percentage for {item_percentage_instance.item_name} is {real_percent}",
                    }
                )
                valid_count += 1
                break

            stake += result
            # print(stake)

        item_id = game.get("item_id")

        item_instance = LifeStyleTable.objects.filter(id=item_id).first()
        if item_instance:
            item_price = float(item_instance.item_amount.replace(",", ""))
            percentage = (stake / item_price) * 100
            percent = 5 / 100
            discount = item_price * percent
            if percentage > discount:
                is_valid = False
                game_data.update(
                    {
                        "item_id": item_id,
                        "stake": stake,
                        "message": f"maximum stake_amount for {item_instance.item_name} is {discount}",
                    }
                )
                valid_count += 1
                break

        else:
            is_valid = False
    # print(valid_count)
    if valid_count == 0:
        is_valid = True
    else:
        is_valid = False
    return is_valid, game_data


def get_instant_cashout_result(game_play_id):
    from main.helpers.helper_functions import mask_winners_phone_number
    from main.models import LottoTicket, LottoWinners
    from pos_app.models import PosLotteryWinners

    instant_cashout_qs = LottoTicket.objects.filter(
        Q(lottery_type="QUIKA") | Q(lottery_type="INSTANT_CASHOUT"),
        game_play_id=game_play_id,
        paid=True,
    )

    instant_cashout_qs.aggregate(Sum("stake_amount"))["stake_amount__sum"]

    if not instant_cashout_qs.exists():
        data = {
            "message": "Invalid game_play_id",
        }
        return data

    instant_cashout_instance = instant_cashout_qs.last()

    tickets = []

    get_total_paid_amount = LottoTicket.instant_cashout_stake_amount_pontential_winning_for_lotto_agents(instant_cashout_qs.count())

    # check if the ticket with won jackpot

    won_jackpot = PosLotteryWinners.objects.filter(game_id=instant_cashout_instance.game_play_id, jackpot=True).last()

    data = {
        "ticket_owner": mask_winners_phone_number(instant_cashout_instance.user_profile.phone_number),
        "game_id": instant_cashout_instance.game_play_id,
        "game_type": instant_cashout_qs.last().lottery_type,
        "stake_per_pick": get_total_paid_amount.get("stake_amount") / instant_cashout_qs.count(),
        "total_stake": get_total_paid_amount.get("stake_amount"),
        "total_ticket": instant_cashout_qs.count(),
        "date": instant_cashout_instance.date,
        "pin": instant_cashout_instance.pin,
    }

    # check winners status
    winning_tickets = []
    winning_instance = LottoWinners.objects.filter(
        game_play_id=game_play_id,
        lottery__user_profile__phone_number=instant_cashout_instance.user_profile.phone_number,
    )
    if winning_instance.exists():
        winning_ins = winning_instance.last()
        if winning_ins.win_flavour == "WHITE":
            data["status"] = "won"
            data["amount_won"] = winning_instance.aggregate(Sum("earning"))["earning__sum"]
            won_tickets = [ticket.ticket for ticket in winning_instance]
            for x in won_tickets:
                winning_tickets.append(x)
        else:
            cash_back_perc = winning_ins.get_cashback_percentage
            data["status"] = f"{cash_back_perc}% cashback"
            data["cashback_amount"] = winning_instance.aggregate(Sum("earning"))["earning__sum"]
            won_tickets = [ticket.ticket for ticket in winning_instance]
            for x in won_tickets:
                winning_tickets.append(x)
    else:
        data["status"] = "lost"
        data["amount_won"] = 0

    for ticket in instant_cashout_qs:
        ticket_data = {
            "ticket": [int(x) for x in ticket.ticket.split(",")],
            "status": "lost",
        }
        if ticket.ticket in winning_tickets:
            if winning_ins.win_flavour == "WHITE":
                ticket_data["status"] = "won"
            else:
                cash_back_perc = winning_ins.get_cashback_percentage
                ticket_data["status"] = f"{cash_back_perc}% cashback"

        tickets.append(ticket_data)

    data["tickets"] = tickets
    data["system_pick"] = (
        [] if instant_cashout_instance.system_generated_num is None else [int(x) for x in instant_cashout_instance.system_generated_num.split(",")]
    )

    if instant_cashout_instance.channel == "POS_AGENT":
        data["agent_id"] = instant_cashout_instance.agent_profile.user_uuid
        if (instant_cashout_instance.pin is not None) or (instant_cashout_instance.pin != ""):
            data["pin"] = instant_cashout_instance.pin
        else:
            LottoTicket().generate_ticket_pin(instant_cashout_instance.game_play_id)
            sleep(1)
            data["pin"] = instant_cashout_instance.pin

    # print("winning_tickets ", winning_tickets)
    # print("winning_instance", winning_instance)
    # #print("tickets ", tickets)
    # print("ticket count", instant_cashout_qs.count())

    # Add Jackpot winning checks
    data["jackpot"] = {
        "won": True if won_jackpot is not None else False,
        "win_amount": won_jackpot.amount_won if won_jackpot is not None else 0.0,
        "pin": won_jackpot.pin if won_jackpot is not None else None,
    }

    # print(
    #     f"""

    # INSTANT CASHOUT RESULT:
    # {data}
    # \n\n\n\n\n\n

    # """
    # )
    return data


def generate_ticket_id():
    from awoof_app.models import AwoofGameTable

    alphabet = string.ascii_letters + string.digits
    loop_condition = True
    while loop_condition:
        ticket_id = "".join(secrets.choice(alphabet) for i in range(8))
        if any(c.isupper() for c in ticket_id) and any(c.isupper() for c in ticket_id) and sum(c.isdigit() for c in ticket_id) >= 6:
            loop_condition = False

    if AwoofGameTable.objects.filter(ticket_id=ticket_id).exists():
        generate_ticket_id()

    return ticket_id


def generate_unique_ticket_id():
    from awoof_app.models import AwoofGameTable

    alphabet = string.ascii_letters + string.digits
    loop_condition = True
    while loop_condition:
        ticket_id = "".join(secrets.choice(alphabet) for i in range(8))
        if any(c.isupper() for c in ticket_id) and any(c.isupper() for c in ticket_id) and sum(c.isdigit() for c in ticket_id) >= 6:
            loop_condition = False

    if AwoofGameTable.objects.filter(unique_ticket_id=ticket_id).exists():
        generate_unique_ticket_id()

    return ticket_id


def truncate(user_percent):
    return math.floor(user_percent * 10**1) / 10**1


def truncate_ticket(ticket_percent):
    return math.floor(ticket_percent * 10**8) / 10**8


def truncate_ticket_two_dp(ticket_percent):
    return math.floor(ticket_percent * 10**2) / 10**2


def notify_awoof_winner(phone, item, ticket_id):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    aggregator_helper = BBCTelcoAggregator()
    operator_line = None

    # whisper_url = "https://whispersms.xyz/transactional/send"
    # whisper_payload = {
    #     "receiver": f"{phone}",
    #     "template": "06af7b26-9f3e-4b59-a960-c1687ad5c20c",
    #     "place_holders": {
    #         "item": f"{item}",
    #         "ticket_Id": f"{ticket_id}",
    #         "date": f"{datetime.now().date()}",
    #     },
    # }
    # whisper_headers = {
    #     "Authorization": f"Api_key {settings.WHISPER_KEY}",
    #     "Content-Type": "application/json",
    # }
    # winner_notification = requests.request(
    #         "POST", whisper_url, headers=whisper_headers, json=whisper_payload
    #     )
    # print(winner_notification.text, "SMS SENT")

    _message = f"WinWise You have just won the {item} bid in the Awoof game with {ticket_id} drawn on {datetime.now().date()} Don't miss the next draw www.wisewinn.com"
    for prefix, operator in nigerian_prefixes.items():
        if phone.startswith(prefix):
            operator_line = operator
    if operator_line == "MTN":
        sms_payload = {"phone_number": phone, "sender_name": "WINWISE", "message": _message}
    elif operator_line == "GLO":
        sms_payload = {
            "phone_number": phone,
            # "sender_name": "WINWISE",
            "message": _message,
            "service_id": "0017182000003867",
            "sender_name": "20144",
        }
    else:
        sms_payload = {"phone_number": phone, "sender_name": "WINWISE", "message": _message}

    sms_payload["use_json_format"] = True
    aggregator_helper.bbc_send_sms(**sms_payload)


def notify_awoof_non_winners(phone, item, masked_phone):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    aggregator_helper = BBCTelcoAggregator()
    operator_line = None
    # whisper_url = "https://whispersms.xyz/transactional/send"
    # whisper_payload = {
    #     "receiver": f"{phone}",
    #     "template": "d71ff93f-a44e-41a3-94f0-1ab8b7b4b78b",
    #     "place_holders": {
    #         "item": f"{item}",
    #         "masked_phone": f"{masked_phone}",
    #         "date": f"{datetime.now().date()}",
    #     },
    # }
    # whisper_headers = {
    #     "Authorization": f"Api_key {settings.WHISPER_KEY}",
    #     "Content-Type": "application/json",
    # }

    # non_winners_notification = requests.request(
    #         "POST", whisper_url, headers=whisper_headers, json=whisper_payload
    #     )
    # print(non_winners_notification.text, "SMS SENT")

    _message = (
        f"WinWise {item} was Won by {masked_phone} in the Awoof game drawn on {datetime.now().date()} Don't miss the next draw www.wisewinn.com"
    )
    for prefix, operator in nigerian_prefixes.items():
        if phone.startswith(prefix):
            operator_line = operator
    if operator_line == "MTN":
        sms_payload = {"phone_number": phone, "sender_name": "WINWISE", "message": _message}
    elif operator_line == "GLO":
        sms_payload = {
            "phone_number": phone,
            # "sender_name": "WINWISE",
            "message": _message,
            "service_id": "0017182000003867",
            "sender_name": "20144",
        }
    else:
        sms_payload = {"phone_number": phone, "sender_name": "WINWISE", "message": _message}

    sms_payload["use_json_format"] = True
    aggregator_helper.bbc_send_sms(**sms_payload)


def send_ask_ai_user_chat_link(phone):
    from broad_base_communication.bbc_helper import BBCTelcoAggregator

    aggregator_helper = BBCTelcoAggregator()
    operator_line = None

    _message = f"Hello User, use the following link to ask any questions https://glo-earn-wage-b2lq.vercel.app/ask-ai/chat/{phone}"
    for prefix, operator in nigerian_prefixes.items():
        if phone.startswith(prefix):
            operator_line = operator
    if operator_line == "MTN":
        sms_payload = {"phone_number": phone, "sender_name": "WINWISE", "message": _message}
    elif operator_line == "GLO":
        sms_payload = {
            "phone_number": phone,
            # "sender_name": "WINWISE",
            "message": _message,
            "service_id": "0017182000003867",
            "sender_name": "20144",
        }
    else:
        sms_payload = {"phone_number": phone, "sender_name": "WINWISE", "message": _message}
    sms_payload["use_json_format"] = True
    aggregator_helper.bbc_send_sms(**sms_payload)


def instant_cashout_pricing_by_lines(lines):
    from main.models import ConstantVariable
    from main.signals import winnings

    const_obj = ConstantVariable.objects.all().last()
    get_instant_cashout = winnings(const_obj)
    max_len = len(get_instant_cashout)
    keys = list(get_instant_cashout.keys())
    if lines <= 0:
        data = {}
    elif lines > 0 and lines <= max_len:
        key = keys[lines - 1]
        value = get_instant_cashout[key]
        if lines == 1:
            data = {
                "stake_amount": key,
                "expected_amount": key,
                "lines": lines,
                "potential_win": value["max_win"],
                "total_amount": key,
            }
        else:
            data = {
                "stake_amount": key / lines,
                "expected_amount": key / lines,
                "lines": lines,
                "potential_win": value["max_win"],
                "total_amount": key,
            }
    else:
        key = keys[max_len - 1]
        value = get_instant_cashout[key]
        data = {
            "stake_amount": key / lines,
            "expected_amount": key,
            "lines": max_len,
            "potential_win": value["max_win"],
            "total_amount": key,
        }
    # print(data)
    # print(type(data))
    return data


def instant_cashout_pricing_by_stake(stake):
    from main.models import ConstantVariable
    from main.signals import winnings

    const_obj = ConstantVariable.objects.all().last()
    get_instant_cashout = winnings(const_obj)
    keys = list(get_instant_cashout.keys())
    lines = 0

    keys = [key for key in get_instant_cashout]
    count = 0
    for key, value in get_instant_cashout.items():
        stake_amount = key
        if stake_amount == stake:
            lines = keys.index(key) + 1
            count += 1
            break

    if count == 0:
        index = None
        diff = sys.float_info.max

        for i, key in enumerate(keys):
            curr_diff = abs(key - stake)
            diff, index = min((diff, index), (curr_diff, i))
        lines = index + 1
    # return index
    return lines


def send_prompt_to_chatgpt(prompt, phone=None, system_msg=None, model="gpt-4o", service=None, model_ins=None):
    import json

    from awoof_app.models import OpenAiDataDump, PerplexityDataDump
    from main.models import UserProfile

    this_user = None
    if phone:
        this_user = UserProfile.objects.filter(phone_number=phone).last()

    is_given, message = ai_money_giver(prompt=prompt, user=this_user, service_type=service)

    last_message = None
    last_response = None

    last_conversation = OpenAiDataDump.objects.filter(phone=phone).last()
    if last_conversation:
        if last_conversation.created_at.date() == timezone.localtime().date():
            try:
                get_response = json.loads(last_conversation.response)
                try:
                    last_response = get_response.get("choices")[0].get("message")
                    last_message = {"role": "user", "content": last_conversation.prompt}
                except (IndexError, AttributeError):
                    last_response = None
                    last_message = {"role": "assistant", "content": last_conversation.prompt}
            except json.JSONDecodeError:
                last_response = None
                last_message = {"role": "user", "content": last_conversation.prompt}
    # last_question =
    # last_answer =
    # api_keys = str(settings.OPENAI_API_KEY).split(",")
    # # Shuffle the API keys randomly
    # random.shuffle(api_keys)
    api_key = settings.OPENAI_API_KEY

    if is_given is True:
        # Log the response
        OpenAiDataDump.objects.create(
            prompt=prompt,
            response=message,
            phone=phone,
            user=this_user,
            api_key=f"{api_key[:5]}xx{api_key[-5:]}",
            last_question=last_message,
            last_answer=last_response,
            service=service,
        )
        return {"status": True, "message": message}

    # Try each API key until a successful request is made or all keys are exhausted
    # for api_key in api_keys:
    try:
        openai.api_key = api_key

        if last_response:
            messages = []

            new_prompt = f"""
                Recent conversation:
                {last_response}
                \n\n
                
                New message: {prompt}
                
            """  # noqa

            messages.append({"role": "user", "content": new_prompt})
        else:
            messages = []
            new_prompt = prompt
            messages.append({"role": "user", "content": new_prompt})

        messages.append({"role": "user", "content": f"{prompt}\n please don't add emoji in your response"})

        if system_msg:
            messages.append({"role": "system", "content": system_msg})

        # Send prompt to ChatGPT model
        response = openai.ChatCompletion.create(
            model=model,
            messages=messages,
            temperature=0,
        )

        # get the model service and append the data the request ticket
        if model_ins:
            if service == "ASK_AI":
                model_ins.ai_request = prompt
                model_ins.ai_response = response
                model_ins.is_utilized = True
                model_ins.save()

                # Log the response
                OpenAiDataDump.objects.create(
                    prompt=prompt,
                    response=response,
                    api_key=f"{api_key[:5]}xx{api_key[-5:]}",
                    user=model_ins.user_profile,
                    phone=model_ins.phone,
                    unique_ticket=model_ins.unique_ticket_id,
                    last_question=last_message,
                    last_answer=last_response,
                    service=service,
                )
        else:
            # Log the response
            OpenAiDataDump.objects.create(
                prompt=prompt,
                response=response,
                phone=phone,
                user=this_user,
                api_key=f"{api_key[:5]}xx{api_key[-5:]}",
                last_question=last_message,
                last_answer=last_response,
                service=service,
            )
        # Extract and return the response
        if "I'm sorry, but" in response.choices[0].message["content"]:
            fall_back_message = perplexity_ai(prompt, system_msg)
            message = fall_back_message.get("message")
            # Remove markdown formatting (** or __)
            message = re.sub(r"\*\*|\_\_", "", message)

            # Remove line breaks
            message = message.replace("\n", " ").replace("  ", " ")

            # Remove emojis (matches most emojis using a regex pattern)
            emoji_pattern = re.compile(
                "["
                "\U0001f600-\U0001f64f"  # emoticons
                "\U0001f300-\U0001f5ff"  # symbols & pictographs
                "\U0001f680-\U0001f6ff"  # transport & map symbols
                "\U0001f1e0-\U0001f1ff"  # flags (iOS)
                "\U00002500-\U00002bef"  # Chinese characters
                "\U00002702-\U000027b0"
                "\U00002702-\U000027b0"
                "\U000024c2-\U0001f251"
                "]+",
                flags=re.UNICODE,
            )
            message = emoji_pattern.sub(r"", message)

            if fall_back_message.get("success") is False:
                per_response = fall_back_message.get("data")
                PerplexityDataDump.objects.create(
                    prompt=prompt,
                    response=per_response,
                    phone=phone,
                    user=this_user,
                    # api_key=f"{api_key[:5]}xx{api_key[-5:]}",
                    last_question=last_message,
                    last_answer=last_response,
                    service=service,
                )
                return {"status": True, "message": response.choices[0].message["content"]}
            else:
                per_response = fall_back_message.get("data")

                PerplexityDataDump.objects.create(
                    prompt=prompt,
                    response=per_response,
                    phone=phone,
                    user=this_user,
                    # api_key=f"{api_key[:5]}xx{api_key[-5:]}",
                    last_question=last_message,
                    last_answer=last_response,
                    service=service,
                )
                return {"status": True, "message": message}
        else:
            return {"status": True, "message": response.choices[0].message["content"]}

    except Exception as e:
        # Log the exception
        print(f"Error with API key {api_key}: {e}")

        # Clean the error message by removing null characters
        error_message = str(e).replace("\x00", "")

        try:
            OpenAiDataDump.objects.create(
                prompt=prompt,
                response=error_message,
                user=this_user,
                phone=phone,
                api_key=f"{api_key[:5]}xx{api_key[-5:]}",
                last_question=last_message,
                last_answer=last_response,
                service=service,
            )
        except Exception as db_error:
            print(f"Failed to log error: {db_error}")

    # If all API keys are exhausted, raise an exception
    return {"status": False, "message": "an error occurred try again"}


def perplexity_ai(prompt, system_msg=None):
    perplexity_token = settings.PERPLEXITY_API_TOKEN

    url = "https://api.perplexity.ai/chat/completions"
    payload = {
        "model": "llama-3.1-sonar-small-128k-online",
        "messages": [
            {
                "role": "system",
                "content": (
                    f"{system_msg} Format responses as an unbroken string with no markdown"
                    if system_msg
                    else "Be precise and concise, Format responses as an unbroken string with no markdown"
                ),
            },
            {"role": "user", "content": f"{prompt}\n"},
        ],
        # "max_tokens": "",
        "temperature": 0.2,
        "top_p": 0.9,
        "search_domain_filter": ["perplexity.ai"],
        "return_images": False,
        "return_related_questions": False,
        "search_recency_filter": "month",
        "top_k": 0,
        "stream": False,
        "presence_penalty": 0,
        "frequency_penalty": 1,
    }
    headers = {"Authorization": f"Bearer {perplexity_token}", "Content-Type": "application/json"}

    response = requests.request("POST", url, json=payload, headers=headers)
    resp = response.json()
    if response.status_code != 200:
        if response.status_code == 400:
            return {"success": False, "message": resp.get("error", {}).get("message"), "data": resp}
        else:
            return {"success": False, "message": resp.get("details")[0].get("message"), "data": resp}
    else:
        return {"success": True, "message": resp.get("choices")[0].get("message").get("content"), "data": resp}


def send_user_money(user, service_type):
    from awoof_app.models import AwoofTransaction, AwoofWalletSystem
    from awoof_app.tasks import credit_winner

    selected_amount, balance = AwoofWalletSystem.distribute_balance(service_type)
    if selected_amount <= 0:
        return False, selected_amount
    else:
        debited_wallet = AwoofTransaction.debit_wallet(user_profile_id=user.id, amount=selected_amount, service_type=service_type)
        if debited_wallet.get("success") is True:
            credit_winner.delay(user_id=user.id, cash_value=selected_amount, winner_phone=user.phone_number)
            return True, selected_amount
        else:
            return False, selected_amount


def ai_money_giver(prompt, user, service_type):
    from wyse_ussd.models import UssdConstantVariable

    raw_keywords = UssdConstantVariable().get_ai_win_keywords()
    if raw_keywords is None:
        return False, "No win keywords"
    if prompt is None:
        return False, "No question to answer"
    
    keywords = set()
    for phrase in raw_keywords:
        keywords.update(phrase.lower().split())

    # Clean prompt and split into words
    cleaned_prompt = prompt.lower().translate(str.maketrans('', '', string.punctuation))
    prompt_words = set(cleaned_prompt.split())

    # prompt_words = set(prompt.lower().split())
    # keywords = set(ai_key_words)
    print(keywords)
    print(type(keywords))
    print(prompt_words)
    print(type(prompt_words))
    print(keywords & prompt_words)
    if keywords & prompt_words:
        print("HERE AS USUAL")
        money_given, selected_amount = send_user_money(user, service_type)
        if money_given is True:
            return True, f"congratulations, you have been credited with N{selected_amount}. Good luck, and feel free to ask more questions!"
        else:
            return False, "sorry no withdrawals at this time"
    else:
        return False, "keyword not found"
