# def awoof_stake_amounts(data):
#     total_stake = 0
#     for game in data.get('games', []):
#         total_stake += sum(cashout.get('stake_amount', 0) for cashout in game.get('i_cashout', []))
#         total_stake += sum(ticket.get('stake_amount', 0) for lifestyle in game.get('life_style', []) for ticket in
#                            lifestyle.get('tickets', []))
#     return total_stake
def awoof_stake_amounts(data):
    total_amount = 0
    for game in data.get("games", []):
        for lifestyle in game["life_style"]:
            total_amount += lifestyle["tier"] * len(lifestyle["tickets"])
    return total_amount
