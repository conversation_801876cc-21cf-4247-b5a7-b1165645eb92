from rest_framework import serializers

from awoof_app.models import (
    FortuneAIModule,
    FortuneAISubModule,
    LifeStyleTable,
    OpenAiDataDump,
)


class PosLifeStylePlaySerializers(serializers.Serializer):
    games = serializers.ListField(child=serializers.JSONField())
    phone_no = serializers.CharField(max_length=150, required=True, allow_null=False, allow_blank=False)
    pin = serializers.CharField(max_length=4, allow_null=True, allow_blank=True)
    ai_message = serializers.CharField(required=False, max_length=300, allow_null=True, allow_blank=True)

    def validate(self, data):
        item_selection = data.get("games")
        phone_number = data.get("phone_no")
        if len(phone_number) < 11 or len(phone_number) > 11:
            raise serializers.ValidationError({"phone_no": "phone number must be 11 digits"})

        if len(item_selection) <= 0:
            raise serializers.ValidationError(
                {
                    "games": "Ensure this value has at least one object e.g"
                    '[{"item_name": "item_id": 1,"band": 200, "stake_amount":200},'
                    '{"item_name": "item_id": 2,"band": 2000, "stake_amount":2000}]}.'
                }
            )
        for game in item_selection:
            if "item_id" in game.keys() and "i_cashout" in game.keys() and "life_style" in game.keys():
                if game.get("i_cashout") == [] or game.get("life_style") == []:
                    raise serializers.ValidationError(
                        {
                            "games": "Ensure this value has at least one object e.g"
                            '[{"item_name": "item_id": 1,"band": 200, "stake_amount":200},'
                            '{"item_name": "item_id": 2,"band": 2000, "stake_amount":2000}]}.'
                        }
                    )
                else:
                    pass
            else:
                raise serializers.ValidationError(
                    {
                        "games": "Ensure this value has at least one object e.g"
                        '[{"item_name": "item_id": 1,"band": 200, "stake_amount":200},'
                        '{"item_name": "item_id": 2,"band": 2000, "stake_amount":2000}]}.'
                    }
                )
        return data


# class ApiLifeStyleSerializer(serializers.Serializer):
#     five_hundred = serializers.IntegerField(required=False)

# class LifeStylePlaySerializers(serializers.Serializer):
#     five_hundred = ten = serializers.ListSerializer(child=ApiLifeStyleSerializer(), required=False)


class AwoofItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = LifeStyleTable
        fields = [
            "id",
            "item_name",
            "item_image_1",
            "item_image_2",
            "item_image_3",
            "item_image_4",
            "item_amount",
            "item_amount",
            "percentage",
            "item_status",
            "min_base_amount",
            "mid_base_amount",
            "max_base_amount",
            "min_gift_amount",
            "mid_gift_amount",
            "max_gift_amount",
            "min_instant_cashout_amount",
            "mid_instant_cashout_amount",
            "max_instant_cashout_amount",
            "min_illusion_amount",
            "mid_illusion_amount",
            "max_illusion_amount",
            "service_type",
        ]

    def to_representation(self, obj):
        serialized_data = super(AwoofItemSerializer, self).to_representation(obj)
        image_1 = f"https://res.cloudinary.com/dhgp0fmlh/{serialized_data['item_image_1']}" if serialized_data["item_image_1"] is not None else None
        image_2 = f"https://res.cloudinary.com/dhgp0fmlh/{serialized_data['item_image_2']}" if serialized_data["item_image_2"] is not None else None
        image_3 = f"https://res.cloudinary.com/dhgp0fmlh/{serialized_data['item_image_3']}" if serialized_data["item_image_3"] is not None else None
        image_4 = f"https://res.cloudinary.com/dhgp0fmlh/{serialized_data['item_image_4']}" if serialized_data["item_image_4"] is not None else None
        all_images = [image_1, image_2, image_3, image_4]
        serialized_data["images"] = all_images
        item_price = float(serialized_data["item_amount"].replace(",", ""))
        serialized_data["item_price"] = item_price
        serialized_data["base"] = {
            "min": serialized_data["min_base_amount"],
            "mid": serialized_data["mid_base_amount"],
            "max": serialized_data["max_base_amount"],
        }
        serialized_data["life_style"] = {
            "min": serialized_data["min_gift_amount"],
            "mid": serialized_data["mid_gift_amount"],
            "max": serialized_data["max_gift_amount"],
        }
        serialized_data["instant_cashout"] = {
            "min": serialized_data["min_instant_cashout_amount"],
            "mid": serialized_data["mid_instant_cashout_amount"],
            "max": serialized_data["max_instant_cashout_amount"],
        }
        serialized_data["illusion"] = {
            "min": serialized_data["min_illusion_amount"],
            "mid": serialized_data["mid_illusion_amount"],
            "max": serialized_data["max_illusion_amount"],
        }
        percentage = serialized_data["percentage"]
        if percentage >= 100:
            del serialized_data["illusion"]
            del serialized_data["instant_cashout"]
            del serialized_data["life_style"]
            del serialized_data["base"]

        del serialized_data["min_base_amount"]
        del serialized_data["mid_base_amount"]
        del serialized_data["max_base_amount"]

        del serialized_data["min_gift_amount"]
        del serialized_data["mid_gift_amount"]
        del serialized_data["max_gift_amount"]

        del serialized_data["min_instant_cashout_amount"]
        del serialized_data["mid_instant_cashout_amount"]
        del serialized_data["max_instant_cashout_amount"]

        del serialized_data["min_illusion_amount"]
        del serialized_data["mid_illusion_amount"]
        del serialized_data["max_illusion_amount"]

        del serialized_data["item_amount"]

        del serialized_data["item_image_1"]
        del serialized_data["item_image_2"]
        del serialized_data["item_image_3"]
        del serialized_data["item_image_4"]

        return serialized_data


class WebLifeStylePlaySerializers(serializers.Serializer):
    from_referral_wallet = serializers.BooleanField()
    paystack = serializers.BooleanField()
    from_play_wallet = serializers.BooleanField()
    games = serializers.ListField(child=serializers.JSONField())
    ai_message = serializers.CharField(required=False, max_length=300, allow_null=True, allow_blank=True)

    def validate(self, data):
        item_selection = data.get("games")
        if len(item_selection) <= 0:
            raise serializers.ValidationError(
                {
                    "games": "Ensure this value has at least one object e.g"
                    '[{"item_name": "item_id": 1,"band": 200, "stake_amount":200},'
                    '{"item_name": "item_id": 2,"band": 2000, "stake_amount":2000}]}.'
                }
            )
        for game in item_selection:
            if "item_id" in game.keys() and "i_cashout" in game.keys() and "life_style" in game.keys():
                if game.get("i_cashout") == [] or game.get("life_style") == []:
                    raise serializers.ValidationError(
                        {
                            "games": "Ensure this value has at least one object e.g"
                            '[{"item_name": "item_id": 1,"band": 200, "stake_amount":200},'
                            '{"item_name": "item_id": 2,"band": 2000, "stake_amount":2000}]}.'
                        }
                    )
                else:
                    pass
            else:
                raise serializers.ValidationError(
                    {
                        "games": "Ensure this value has at least one object e.g"
                        '[{"item_name": "item_id": 1,"band": 200, "stake_amount":200},'
                        '{"item_name": "item_id": 2,"band": 2000, "stake_amount":2000}]}.'
                    }
                )
        return data


class LibertyLiveTicketSerializers(serializers.Serializer):
    amount = serializers.FloatField(required=True, min_value=1)
    phone_number = serializers.CharField(required=True)

    def validate(self, attrs):
        phone_number = attrs.get("phone_number")
        if not phone_number.isnumeric():
            raise serializers.ValidationError({"phone_number": "invalid phone number"})
        if len(phone_number) < 11 or len(phone_number) > 11:
            raise serializers.ValidationError({"phone_no": "phone number must be 11 digits"})
        return attrs


class GetAwoofResultSerializer(serializers.Serializer):
    game_id = serializers.CharField(max_length=150, allow_null=True, allow_blank=True)
    game_pin = serializers.CharField(max_length=150, allow_null=True, allow_blank=True)

    def validate(self, data):
        game_id = data.get("game_id")
        game_pin = data.get("game_pin")
        if game_id == "" or game_pin == "":
            raise serializers.ValidationError({"data": "game_id and game_pin cannot be empty"})
        return data


class AskAISubscriptionSerializer(serializers.Serializer):
    phone_number = serializers.CharField(required=True, max_length=150)

    def validate(self, data):
        phone_number = data.get("phone_number")
        if not phone_number.isnumeric():
            raise serializers.ValidationError({"phone_number": "invalid phone number"})
        if len(phone_number) < 11:
            raise serializers.ValidationError({"phone_number": "invalid phone number"})

        return data


class AskAIHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = OpenAiDataDump
        fields = [
            # "id",
            "prompt",
            "response",
            "created_at",
        ]


class ListFortuneAIModule(serializers.ModelSerializer):
    class Meta:
        model = FortuneAIModule
        fields = ("id", "module_name", "module_overview")

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        view_count = self.context.get("view_count", 0)
        this_module = []

        all_sub_module = FortuneAISubModule.objects.filter(module=instance).order_by("module_day")

        for sub_module in all_sub_module:
            if sub_module.module_day:
                allowed_count = sub_module.module_day
            else:
                allowed_count = 0
            if sub_module.module_file:
                sub_module_file = "http://127.0.0.1:8000/media/" + str(sub_module.module_file)
            else:
                sub_module_file = ""
            this_module.append(
                {
                    "module_day": f"Day {sub_module.module_day}",
                    "sub_module_name": sub_module.module_name,
                    "sub_module_file": sub_module_file if allowed_count <= view_count else "",
                }
            )
        representation["sub_modules"] = this_module
        return representation
