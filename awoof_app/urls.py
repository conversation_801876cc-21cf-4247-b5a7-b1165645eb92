from django.urls import path

from awoof_app import views

awoof_url = [
    path("pos_life_style/", views.PlayLifeStyleAPIVIew.as_view()),
    path("pos_insurance/", views.PlayInsuranceAPIVIew.as_view()),
    path("get_items/", views.GetAwoofItemsAPIView.as_view()),
    path("get_insurance/", views.GetInsuranceItemsAPIView.as_view()),
    path("web_life_style/", views.WEBLifeStyleApiView.as_view()),
    path("liberty_live_ticket/", views.RegisterLibertyLive.as_view()),
    path("web_insurance/", views.WEBInsuranceApiView.as_view()),
    path("get_awoof_result/", views.GetAwoofApiView.as_view()),
    path("get_ask_ai_items/", views.GetAskAIServiceAPIView.as_view()),
    path("web_ask_ai/", views.WEBAskAIApiView.as_view()),
    path("pos_ask_ai/", views.AskAwoofAIAPIView.as_view()),
    path("ask_ai_subscription/", views.AskAISubscriptionAPIView.as_view()),
    path("ask_ai_history/", views.AskAIHistoryAPIView.as_view()),
    path("fetch_fortune_ai/", views.GetFortuneAI.as_view()),
]

urlpatterns = [
    *awoof_url,
]
