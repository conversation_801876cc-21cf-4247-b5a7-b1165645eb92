from decouple import config

from liberty_lotto.settings import *  # noqa

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "github_actions",
        "USER": config("DATABASE_USER"),
        "PASSWORD": config("DATABASE_PASSWORD"),
        "HOST": "localhost",
        "PORT": "5432",
        "TEST": {
            "NAME": None,
        },
        "OPTIONS": {
            "options": "-c statement_timeout=10000",
        },
    }
}

DEBUG = False
