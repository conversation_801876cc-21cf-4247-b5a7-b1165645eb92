import os
from django.core.asgi import get_asgi_application
from channels.routing import Protocol<PERSON><PERSON><PERSON><PERSON><PERSON>, URLRouter
from channels.auth import AuthMiddlewareStack
import africa_lotto.routing

# Add the new ConnectionLimitMiddleware
class ConnectionLimitMiddleware:
    def __init__(self, inner):
        self.inner = inner
        self.active_connections = 0
        self.max_connections = 3000 

    async def __call__(self, scope, receive, send):
        if scope["type"] == "websocket":
            if self.active_connections >= self.max_connections:
                # Reject the connection with close code 1013 (Try Again Later)
                async def close_connection():
                    await send({"type": "websocket.close", "code": 1013})
                return await close_connection()
            
            self.active_connections += 1
            try:
                return await self.inner(scope, receive, send)
            finally:
                self.active_connections -= 1
        else:
            return await self.inner(scope, receive, send)

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "liberty_lotto.settings")

# Get the base ASGI application
http_application = get_asgi_application()

# Define the WebSocket part of the application with the new middleware
websocket_application = ConnectionLimitMiddleware(
    AuthMiddlewareStack(
        URLRouter(
            africa_lotto.routing.websocket_urlpatterns
        )
    )
)

# Combine them in the ProtocolTypeRouter
application = ProtocolTypeRouter({
    "http": http_application,
    "websocket": websocket_application,
})