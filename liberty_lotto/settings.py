import os
from datetime import datetime, timedelta
from pathlib import Path

import cloudinary
import cloudinary.api
import cloudinary.uploader
import sentry_sdk
from decouple import Csv, config

# from drf_yasg.generators import OpenAPISchemaGenerator
from sentry_sdk.integrations.django import DjangoIntegration

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config("SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = int(config("DEBUG", default=False))

ALLOWED_HOSTS = config("ALLOWED_HOSTS", cast=Csv())
# ALLOWED_HOSTS = []

# Application definition

INSTALLED_APPS = [
    # Whitenoise
    "whitenoise.runserver_nostatic",
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "channels",
    # RestFrameWork
    "rest_framework",
    "rest_framework.authtoken",
    # Import Export
    "import_export",
    # Apps
    "main",
    "payout_app",
    "wallet_app",
    "referral_system",
    "account.apps.AccountConfig",
    "pos_app.apps.PosAppConfig",
    "web_app.apps.WebAppConfig",
    "mobile_app.apps.MobileAppConfig",
    "sport_app",
    "awoof_app",
    "banker_lottery",
    "scratch_cards",
    "staff_commission.apps.StaffCommissionConfig",
    "africa_lotto.apps.AfricaLottoConfig",
    "wallet_system.apps.WalletSystemConfig",
    # Others
    "wyse_ussd",
    # "bot_app",
    "sms_campaign",
    # Celery
    "django_celery_results",
    "django_celery_beat",
    # cors
    "corsheaders",
    # documentation
    "drf_yasg",
    "coreapi",
    # === third party
    "redisboard",  # redis gui
    "cloudinary_storage",
    "cloudinary",  # cloudinary,
    "admin_dashboard",
    "ticket_price.apps.TicketPriceConfig",
    "resources_app.apps.ResourcesAppConfig",
    "mancala",
    "ads_tracker",
    "payout_process.apps.PayoutProcessConfig",
    "retail_metrics.apps.RetailMetricsConfig"
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django_prometheus.middleware.PrometheusBeforeMiddleware",
    "django_prometheus.middleware.PrometheusAfterMiddleware",
]


ASGI_APPLICATION = "liberty_lotto.asgi.application"

ENVIRONMENT = config("ENVIRONMENT", default="dev")

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [("127.0.0.1", 6379)],
            "capacity": 1500,
            "expiry": 60, 
        },
        
    },
}


DATA_UPLOAD_MAX_MEMORY_SIZE = 52428800

STATICFILES_STORAGE = "whitenoise.storage.CompressedStaticFilesStorage"

CORS_ORIGIN_ALLOW_ALL = True

ROOT_URLCONF = "liberty_lotto.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "liberty_lotto.wsgi.application"


# User
AUTH_USER_MODEL = "account.User"

# Auth Token
REST_AUTH_TOKEN_MODEL = "account.models.Token"

# CREATE USER TOKEN
REST_AUTH_TOKEN_CREATOR = "account.utils.custom_create_token"

# TOKEN EXPIRATION
TOKEN_TTL = timedelta(days=1)


# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases


EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# if DEBUG:
#     DATABASES = {
#         "default": {
#             "ENGINE": "django.db.backends.postgresql_psycopg2",
#             "NAME": config("DATABASE_NAME"),
#             "USER": config("DATABASE_USER"),
#             "PASSWORD": config("DATABASE_PASSWORD"),
#             "HOST": config("DB_HOST"),
#             "PORT": "",
#         }
#     }
# else:
#     DATABASES = {
#         "default": {
#             "ENGINE": "django.db.backends.postgresql_psycopg2",
#             "NAME": config("DATABASE_NAME"),
#             "USER": config("DATABASE_USER"),
#             "PASSWORD": config("DATABASE_PASSWORD"),
#             "HOST": config("DB_HOST"),
#             "PORT": "",
#         }
#     }


DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": config("DATABASE_NAME"),
        "USER": config("DATABASE_USER"),
        "PASSWORD": config("DATABASE_PASSWORD"),
        "HOST": config("DB_HOST"),
        "PORT": config("DB_PORT"),
        "DISABLE_SERVER_SIDE_CURSORS": True,
        "sslmode": "require",
    },
    "external": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": config("EXTERNAL_DB_NAME"),
        "USER": config("EXTERNAL_DB_USERNAME"),
        "PASSWORD": config("EXTERNAL_DB_PASSWORD"),
        "HOST": config("EXTERNAL_DB_HOST"),
        "PORT": "25061",
        "sslmode": "require",
    },
    "external2": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": config("EXTERNAL_DB2_NAME"),
        "USER": config("EXTERNAL_DB2_USERNAME"),
        "PASSWORD": config("EXTERNAL_DB2_PASSWORD"),
        "HOST": config("EXTERNAL_DB2_HOST"),
        "PORT": "25061",
        "sslmode": "require",
    },
}

# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
        "OPTIONS": {
            "min_length": 6,
        },
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Africa/Lagos"

USE_I18N = True

# USE_TZ = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/
STATIC_URL = "static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")

# Base url to serve media files
MEDIA_URL = "/media/"

# Path where media is stored
MEDIA_ROOT = os.path.join(BASE_DIR, "media/")

# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# AUTH_USER_MODEL = "main.User"

# DJOSER = {
#     "LOGIN_FIELD": "email",
#     "USER_CREATE_PASSWORD_RETYPE": True,
#     "USERNAME_CHANGED_EMAIL_CONFIRMATION": True,
#     "PASSWORD_CHANGED_EMAIL_CONFIRMATION": True,
#     "SEND_ACTIVATION_EMAIL": True,
#     "SEND_CONFIRMATION_EMAIL": True,
#     "PASSWORD_RESET_SHOW_EMAIL_NOT_FOUND": True,
#     "PASSWORD_RESET_CONFIRM_URL": "password/reset/confirm/?uid={uid}&token={token}",
#     "USERNAME_RESET_CONFIRM_URL": "password/reset/confirm/{uid}/{token}",
#     "ACTIVATION_URL": "activate/?uid={uid}&token={token}",
#     "TOKEN_MODEL": "rest_framework.authtoken.models.Token",
#     "SERIALIZERS": {
#         "user_create": "main.serializers.UserCreateSerializer",
#         "user": "main.serializers.UserCreateSerializer",
#         "user_delete": "djoser.serializers.UserDeleteSerializer",
#         "token_create": "main.serializers.CustomTokenCreateSerializer",  # path to serializer
#     },
# }

IMPORT_EXPORT_USE_TRANSACTIONS = True

REST_FRAMEWORK = {
    "DEFAULT_SCHEMA_CLASS": "rest_framework.schemas.coreapi.AutoSchema",
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework.authentication.TokenAuthentication",
        "account.authentication.ExpiringTokenAuthentication",
        "account.authentication.CustomBasicAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ),
}


# Authentication Backend
AUTHENTICATION_BACKENDS = [
    "django.contrib.auth.backends.ModelBackend",
    "account.authentication.EmailBackend",
    # "account.authentication.PhoneBackend",
]


# SIMPLE_JWT = {
#     # 'ACCESS_TOKEN_LIFETIME': timedelta(minutes=30),
#     "ACCESS_TOKEN_LIFETIME": timedelta(days=13),
#     "REFRESH_TOKEN_LIFETIME": timedelta(days=10),
# }


DJANGO_SETTINGS_MODULE = config("DJANGO_SETTINGS_MODULE")

# # TOGGLE BASE_URL
# if DEBUG:
#     BASE_URL = "http://localhost:8000"
# else:
#     BASE_URL = "http://libertypaypos.com"


# WHISPER
WHISPER_KEY = config("WHISPER_KEY")
LIBERTY_LOTTO_PAYMENT_COLLECTION = config("LIBERTY_LOTTO_PAYMENT_COLLECTION")
LIBERTY_LOTTO_WINNERS_LIST = config("LIBERTY_LOTTO_WINNERS_LIST")
LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE = config("LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE")
LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE_WITH_ACCOUNT = config("LIBERTY_LOTTO_WINNER_CONGRATS_MESSAGE_WITH_ACCOUNT")
LIBERTY_LOTTO_CHECK_WINNERS_LINK_LIST = config("LIBERTY_LOTTO_CHECK_WINNERS_LINK_LIST")
LIBERTY_LOTTO_PAYMENT_RECEIPT_TEMPLATE = config("LIBERTY_LOTTO_PAYMENT_RECEIPT_TEMPLATE")
LIBERTY_LOTTO_DEMO_SMS_FOR_NON_WINNERS = config("LIBERTY_LOTTO_DEMO_SMS_FOR_NON_WINNERS")
LIBERTY_LOTTO_RESEND_ACCOUNT_COLLECT_SMS = config("LIBERTY_LOTTO_RESEND_ACCOUNT_COLLECT_SMS")

WINWISE_LOTTO_COLLECTION_S4L_YCASH = config("WINWISE_LOTTO_COLLECTION_S4L_YCASH")
WINWISE_LOTTO_COLLECTION_INSTANTCASH = config("WINWISE_LOTTO_COLLECTION_INSTANTCASH")
WINWISE_LOTTO_BACKEND_URL = config("WINWISE_LOTTO_BACKEND_URL")


TRANSACTION_OTP_TEMPLATE = config("TRANSACTION_OTP_TEMPLATE")
REFERRAL_SMS_TEMPLATE = config("REFERRAL_SMS_TEMPLATE")

ACCOUNT_ACTIVATION_OTP_TEMPLATE = config("ACCOUNT_ACTIVATION_OTP_TEMPLATE")


# WHISPER ====================================================

# PICKYASSIST
PICKY_ASSIST_TOKEN = config("PICKY_ASSIST_TOKEN")
AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID = config("AGENT_PICKYASSIST_DRAW_NOTIFICATION_GROUP_ID")

# WOVEN
WOVEN_API_KEY = config("WOVEN_API_KEY_LIVE")
WOVEN_PAYOUT_PIN = config("WOVEN_PAYOUT_PIN")
WOVEN_DEVELOPMENT_MODE = int(config("WOVEN_DEVELOPMENT_MODE", default=False))
TEST_DESTINATION_BANK = config("TEST_DESTINATION_BANK")
LIVE_DESTINATION_BANK = config("LIVE_DESTINATION_BANK")

# # SENDGRID
# SENDGRID_API_KEY = config("SENDGRID_API_KEY")

# WATUPAY
WATUPAY_PUBLIC_KEY = config("WATUPAY_PUBLIC_KEY")
WATUPAY_MERCHANT_REFERENCE = config("WATUPAY_MERCHANT_REFERENCE")

# PAYSTACK
PAYSTACK_BEARER = config("PAYSTACK_BEARER")
PAYSTACK_PUBLIC_KEY = config("PAYSTACK_PUBLIC_KEY")
PAYSTACK_ENVIRONMENT = config("PAYSTACK_ENVIRONMENT")
PAYSTACK_BS64 = config("PAYSTACK_BS64")
PAYSTACK_CALLBACK_URL = config("PAYSTACK_CALLBACK_URL")

# Pabbly
PABBLY_AUTH_USER = config("PABBLY_AUTH_USER")
PABBLY_AUTH_PASS = config("PABBLY_AUTH_PASS")

# PABBLY WEBHOOK AUTHERIZATION
PABBLY_AUTH_USERNAME = config("PABBLY_AUTH_USERNAME")
PABBLY_AUTH_PASSWORD = config("PABBLY_AUTH_PASSWORD")

# Bitly Link
BITLY_TOKEN = config("BITLY_TOKEN")

# Woven
WOVEN_API_KEY_TEST = config("WOVEN_API_KEY_TEST")
WOVEN_API_KEY_LIVE = config("WOVEN_API_KEY_LIVE")
WEMA_BANK_CODE = config("WEMA_BANK_CODE")
CMB_BANK_CODE = config("CMB_BANK_CODE")
SPARKLE_BANK_CODE = config("SPARKLE_BANK_CODE")
WOVEN_CALL_BACK = config("WOVEN_CALL_BACK")

# LOAN DISK
LOAN_DISK_SEC_KEY = config("LOAN_DISK_SEC_KEY")
LOAN_DISK_PUBLICK_KEY = config("LOAN_DISK_PUBLICK_KEY")
LOAN_DISK_BRANCH_ID = config("LOAN_DISK_BRANCH_ID")

# WWOVEN PAYOUT DISBURSEMENT
WOVEN_DISBURSEMENT_SOURCE_ACCOUNT = config("WOVEN_DISBURSEMENT_SOURCE_ACCOUNT")
WOVEN_DISBURSEMENT_PAYMENT_PIN = config("WOVEN_DISBURSEMENT_PAYMENT_PIN")
WOVEN_DISBURSEMENT_API_SECRET = config("WOVEN_DISBURSEMENT_API_SECRET")
RED_BILLER_PRIVATE_KEY = config("RED_BILLER_PRIVATE_KEY")
RED_BILLER_AUTH_BEARER = config("RED_BILLER_AUTH_BEARER")

REDIS_HOST = "127.0.0.1"
ENVIRONMENT = config("ENVIRONMENT")

# CELERY
CORE_REDIS_HOST = config("CORE_REDIS_HOST")


CELERY_BROKER_URL = f"redis://{CORE_REDIS_HOST}:6379"
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = TIME_ZONE
CELERY_RESULT_BACKEND = "django-db"

# AGENCY BANKING
AGENCY_BANKING_BASE_URL = config("AGENCY_BANKING_BASE_URL")
AGENCY_BANKING_USEREMAIL = config("AGENCY_BANKING_USEREMAIL")
AGENCY_BANKING_PASSWORD = config("AGENCY_BANKING_PASSWORD")
AGENCY_BANKING_TRANSACTION_PIN = config("AGENCY_BANKING_TRANSACTION_PIN")
AGENCY_BANKING_SUPER_TOKEN = config("AGENCY_BANKING_SUPER_TOKEN")
AGENCY_BANKING_TOKEN = config("AGENCY_BANKING_TOKEN")

LOTTO_FRONTEND_LINK = config("LOTTO_FRONTEND_LINK")
# CELERY_RESULT_BACKEND = "redis://localhost:6379"

# CORAL PAY
CORAL_PAY_USERNAME = config("CORAL_PAY_USERNAME")
CORAL_PAY_PASSWORD = config("CORAL_PAY_PASSWORD")
CORAL_PAY_MERCHANT_ID = config("CORAL_PAY_MERCHANT_ID")

# this variable will be use when coralpay are sending
# request to ouver server for ussd payment
CORAL_PAY_AUTH_USERNAME = config("CORAL_PAY_AUTH_USERNAME")
CORAL_PAY_AUTH_PASSWORD = config("CORAL_PAY_AUTH_PASSWORD")

# LIBERTY VAS
LIBERTY_VAS_BASE_URL = config("LIBERTY_VAS_BASE_URL")
LIBERTY_VAS_AUTH_USERNAME = config("LIBERTY_VAS_AUTH_USERNAME")
LIBERTY_VAS_AUTH_PASSWORD = config("LIBERTY_VAS_AUTH_PASSWORD")
USER_BVN = config("USER_BVN")


# MAILGUN
MAILGUN_API_KEY = config("MAILGUN_API_KEY")

# Engage
ENGAGE_API_KEY = config("ENGAGE_API_KEY")
ENGAGE_SECRET_KEY = config("ENGAGE_SECRET_KEY")


# RAPID API FOR FOOTBALL
FOOTBALL_API_KEY = config("FOOTBALL_API_KEY")
CHAMPIONS_LEAGUE_SEASON_YEAR = config("CHAMPIONS_LEAGUE_SEASON_YEAR")
ENGLISH_LEAGUE_SEASON_YEAR = config("ENGLISH_LEAGUE_SEASON_YEAR")
EUROPA_LEAGUE_SEASON_YEAR = config("EUROPA_LEAGUE_SEASON_YEAR")
LA_LIGA_SEASON_YEAR = config("LA_LIGA_SEASON_YEAR")
WORLD_CUP_SEASON_YEAR = config("WORLD_CUP_SEASON_YEAR")

POS_INSTANT_CASHOUT_TEST_FOR_POS_AGENT = config("POS_INSTANT_CASHOUT_TEST_FOR_POS_AGENT")
UVERIFY_TOKEN = config("UVERIFY_TOKEN")

# CUTTLY
CUTTLY_API_KEY = config("CUTTLY_API_KEY")


# TELEGRAM BOT
TELEGRAM_BOT_TOKEN = config("TELEGRAM_BOT_TOKEN")
ACTIVATE_BOT = config("ACTIVATE_BOT")

MAILGUN_WEBHOOK_SIGNING_KEY = config("MAILGUN_WEBHOOK_SIGNING_KEY")

LIBERTYPAY_USER_AUTH = config("LIBERTYPAY_USER_AUTH")
LIBERTYPAY_AGENTS_AUTH = config("LIBERTYPAY_AGENTS_AUTH")

SELF_SERVICE_AUTH = config("SELF_SERVICE_AUTH")

DEFAULT_AGENT_ID = config("DEFAULT_AGENT_ID")


# BROAD BASE COMMUNICATION(BBC) CONFIGS
BBC_PARTNER_ID = config("BBC_PARTNER_ID")
BBC_PARTNER_PASSWORD = config("BBC_PARTNER_PASSWORD")
BBC_SERVICE_ID = config("BBC_SERVICE_ID")
BBC_USSD_SERVICE_ACTIVATION_NUMBER = config("BBC_USSD_SERVICE_ACTIVATION_NUMBER")
BBC_IP = config("BBC_IP")
BBC_ON_DEMAND_SERVICE_USERNAME = config("BBC_ON_DEMAND_SERVICE_USERNAME")
BBC_ON_DEMAND_SERVICE_PASSWORD = config("BBC_ON_DEMAND_SERVICE_PASSWORD")
BBC_ON_DEMAND_SERVICE_ID = config("BBC_ON_DEMAND_SERVICE_ID")
BBC_ON_DEMAND_SERVICE_PRODUCT_ID = config("BBC_ON_DEMAND_SERVICE_PRODUCT_ID")
BBC_SMS_SERVICE_USERNAME = config("BBC_SMS_SERVICE_USERNAME")
BBC_SMS_SERVICE_PASSWORD = config("BBC_SMS_SERVICE_PASSWORD")


# CELERY BEAT
# CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"

if DEBUG:
    pass
else:
    sentry_sdk.init(
        dsn="https://<EMAIL>/6533852",
        integrations=[DjangoIntegration()],
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production.
        traces_sample_rate=0.001,
        profiles_sample_rate=0.01,
        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,
    )


AUTH_PROVIDERS = {
    "email": "email",
    "google": "google",
}


# # CELERY BEAT
# CELERYBEAT_SCHEDULE = {
#    'installer_recalc_hour': {
#         'task': 'stats.installer.tasks.recalc_last_hour',
#         'schedule': 15,  # every 15 sec for test
#         'options': {'queue' : 'celery2'},  # options are mapped to apply_async options
#     },
# }


# --------------- WHITELITING IP & DOMAIN ----------------
IP_WHITELIST = config("IP_WHITELIST")
DOMAIN_WHITELIST = config("DOMAIN_WHITELIST")
WHITELISTING_TO_USE = config("WHITELISTING_TO_USE")


# --------------- REDIS GUI ----------------
REDISBOARD_CONNECTION_POOL_OPTIONS = {
    "socket_timeout": 60,
    "socket_connect_timeout": 10,
}

REDISBOARD_DETAIL_FILTERS = ["uptime.*", "db.*"]

REDIS_SERVERS = dict(
    redis_server_a=dict(host=f"{REDIS_HOST}", port=6379, db=0),
)


# ------------- TEST USER FOR TESTING ----------------
DEV_TEST_USERS = config("DEV_TEST_USERS")

REMITTANCE_EMAIL_LIST = config("REMITTANCE_EMAIL_LIST", default=[], cast=Csv())


cloudinary.config(
    cloud_name=config("CLOUDINARY_CLOUD_NAME"),
    api_key=config("CLOUDINARY_API_KEY"),
    api_secret=config("CLOUDINARY_API_SECRET"),
)

ZENITH_USER = config("ZENITH_USER")
ZENITH_PROTECTOR = config("ZENITH_PROTECTOR")
ZENITH_SOURCE_ACCOUNT = config("ZENITH_SOURCE_ACCOUNT")

CORE_BANKING_EMAIL = config("CORE_BANKING_EMAIL")
CORE_BANKING_PASSWORD = config("CORE_BANKING_PASSWORD")

LIBERTY_USSD_BASE_URL = config("LIBERTY_USSD_BASE_URL")

OPENAI_API_KEY = config("OPENAI_API_KEY")
PERPLEXITY_API_TOKEN = config("PERPLEXITY_API_TOKEN")
SCRATCH_CARD_EMAIL = config("SCRATCH_CARD_EMAIL")
SCRATCH_CARD_PASSWORD = config("SCRATCH_CARD_PASSWORD")


DIGITALOCEAN_KEY = config("DIGITALOCEAN_KEY")
GITHUB_TOKEN = config("GITHUB_TOKEN")


DATA_UPLOAD_MAX_MEMORY_SIZE = **********  # 1GB
FILE_UPLOAD_MAX_MEMORY_SIZE = **********  # 1GB


# CELERY_BEAT_SCHEDULE = {
#     "seed-global-tickets-every-second": {
#         "task": "main.tasks.seed_global_tickets",
#         "schedule": crontab(),
#     },
#     "turn-rapid-fire-to-winnings-every-second": {
#         "task": "main.tasks.turn_rapid_fire_to_winnings",
#         "schedule": crontab(),
#     },
# }


BASE_DIR = os.path.dirname(os.path.abspath(__file__))
LOGS_DIR = os.path.join(BASE_DIR, "logs")

# Ensure logs directory exists with proper permissions
os.makedirs(LOGS_DIR, mode=0o775, exist_ok=True)

LOG_FILE = os.path.join(LOGS_DIR, f"errors_{datetime.now().strftime('%Y-%m-%d')}.log")  # Log file with date

# Test if log file can be created
try:
    with open(LOG_FILE, "a") as f:
        pass
except IOError:
    LOG_FILE = "/dev/null"  # Fallback if writing to the log file fails

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "%(levelname)s %(asctime)s %(module)s %(message)s",
        },
        "simple": {
            "format": "%(levelname)s %(message)s",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
        "file": {
            "level": "ERROR",
            "class": "logging.FileHandler",
            "filename": LOG_FILE,
            "formatter": "verbose",
            "delay": True,  # Prevents crash if file can't be opened immediately
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": True,
        },
        "django.utils.autoreload": {
            "handlers": [],
            "level": "CRITICAL",
            "propagate": False,
        },
    },
    "root": {
        "handlers": ["console", "file"],
        "level": "DEBUG",
    },
}


CSRF_TRUSTED_ORIGINS = [
    "https://libertydraw.com",
]

# print("REDIS_HOST", REDIS_HOST)
