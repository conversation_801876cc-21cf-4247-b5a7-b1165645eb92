import os

from celery import Celery

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "liberty_lotto.settings")

celery = Celery("liberty_lotto")

celery.config_from_object("django.conf:settings", namespace="CELERY")
celery.autodiscover_tasks()


# Celery task to clear log file every two days
@celery.task(bind=True)
def clear_log_file(self):
    # log_file_path = os.path.join(settings.BASE_DIR)
    # open(f"{settings.BASE_DIR}/general.log", "w").close()
    pass
