import json
import random
from dataclasses import dataclass

import requests
from decouple import config
from django.conf import settings

from main.helpers.redis_storage import RedisStore


@dataclass
class WemaBank:
    REDIS_OBJECT = RedisStore()
    base_url = "https://banking.libertypayng.com"
    email = settings.CORE_BANKING_EMAIL
    password = settings.CORE_BANKING_PASSWORD
    user_ref = f"WEMA-{password}"

    def core_banking_login(self, reset_token=False):
        url = f"{self.base_url}/api/v1/companies/auth/login/"

        payload = json.dumps({"email": self.email, "password": self.password})
        headers = {"Content-Type": "application/json"}

        response = requests.request("POST", url, headers=headers, data=payload)
        # print(response.text, "@ log in method")
        json_response = response.json()

        if response.status_code == 200:
            response_data = json_response.get("data")
            # print(response_data)
            if reset_token:
                # print("Reset token")
                self.REDIS_OBJECT.delete_data(key=self.user_ref)

            self.REDIS_OBJECT.set_data(key=self.user_ref, value=response_data.get("access"))
            return response

        else:
            return response

    def retrieve_access_token(self):
        retrieve_token = self.REDIS_OBJECT.get_data(key=self.user_ref)
        access_token = retrieve_token.decode("utf-8") if retrieve_token is not None else ""
        # print(access_token, "Access token \n")
        return access_token

    def create_account(self, first_name: str, last_name: str):
        url = f"{self.base_url}/api/v1/wema/virtual_accounts/"

        payload = json.dumps({"first_name": first_name, "last_name": last_name})
        token = self.retrieve_access_token()
        # print(token, "\n\n")

        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {token}"}

        response = requests.request("POST", url, headers=headers, data=payload)
        response_Code = response.status_code
        # response_Code = 201
        print(response.text)

        if response_Code == 401:
            reset_token = self.core_banking_login(reset_token=True)

            if reset_token.status_code == 200:
                return self.create_account(first_name=first_name, last_name=last_name)
            else:
                return reset_token.json()

        else:
            json_response = response.json()
            # json_response = {'status': 'success', 'status_code': 201,
            #                  'data': {'message': 'account created successfully.',
            #                           'account_details': {'id': 'dc476510-19a3-4456-98a0-447f43677464',
            #                                               'created_at': '2023-10-13T21:17:53.977375+01:00',
            #                                               'updated_at': '2023-10-13T21:17:53.977398+01:00',
            #                                               'first_name': 'WinWise', 'middle_name': None,
            #                                               'last_name': '*************', 'bvn': None, 'email': None,
            #                                               'phone': None, 'date_of_birth': None,
            #                                               'account_number': '**********',
            #                                               'company': '18acd4a2-492d-40b0-b180-678ff138b3cc'}},
            #                  'errors': None}
            return json_response

    def instant_accounts(self, reference):
        url = "https://banking.libertypayng.com/api/v1/wema/instant_accounts/"

        print(' instant_accounts config("ENVIRONMENT")', config("ENVIRONMENT"))
        if config("ENVIRONMENT") != "staging" and config("ENVIRONMENT") != "development" and config("ENVIRONMENT") != "dev":
            token = self.retrieve_access_token()

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}",
            }

            payload = {"request_reference": reference}

            response = requests.request("POST", url, headers=headers, json=payload)

            if response.status_code == 401:
                self.core_banking_login(reset_token=True)

                return self.instant_accounts(reference)

            """
                SAMPLE RESPONSE

                {
                    "status":"success",
                    "status_code":200,
                    "data":{
                        "message":"success.",
                        "account_details":{
                            "bank_name":"Wema Bank Plc",
                            "bank_code":"000017",
                            "account_number":"**********",
                            "one_time":true,
                            "request_reference":"TEST-**********.8919399",
                            "request_active":true,
                            "company":"Liberty Life Insurance",
                            "sub_company":"None",
                            "account_name":"Liberty/Liberty Life Insurance  PAYMENTS"
                        }
                    },
                    "errors":"None"
                }
            """

            try:
                return response.json()
            except Exception:
                return response.text
        else:
            data = {
                "status": "success",
                "status_code": 200,
                "data": {
                    "message": "success.",
                    "account_details": {
                        "bank_name": "Wema Bank Plc",
                        "bank_code": "000017",
                        "account_number": "**********",
                        "one_time": True,
                        "request_reference": "TEST-**********.8919399",
                        "request_active": True,
                        "company": "Liberty Life Insurance",
                        "sub_company": "None",
                        "account_name": "Liberty/Liberty Life Insurance  PAYMENTS",
                    },
                },
                "errors": "None",
            }

            # generate a random account number
            random_account_number = random.randint(**********, **********)
            data["data"]["account_details"]["account_number"] = random_account_number

            return data


# if __name__ == "__main__":
#     bank = WemaBank()
#     create_account = bank.create_account(first_name="Oguntuga", last_name="Dare")
#     print(create_account)
