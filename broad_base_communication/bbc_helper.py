import hashlib
import time
import uuid
from dataclasses import dataclass
from datetime import datetime, timezone
from xml.etree.ElementTree import Element, SubElement, tostring

import redis
import requests
from decouple import config
from django.conf import settings

from overide_print import print
from wyse_ussd.models import TelcoCharge
from wyse_ussd.tasks import celery_telco_ussd_backgroud


@dataclass
class TelcoRedisStorage:
    redis_key: str
    redis_client: object = redis.StrictRedis(host=config("REDIS_HOST"), port="6379", db=0, decode_responses=True, encoding="utf-8")

    def set_data(self, data):
        """
        Set data in Redis.
        """
        self.redis_client.set(self.redis_key, data, ex=10800)

    def get_data(self):
        """
        Get data from Redis.
        """
        return self.redis_client.get(self.redis_key)

    def delete_data(self):
        """
        Delete data from Redis.
        """
        self.redis_client.delete(self.redis_key)

    def clear_data(self):
        """
        Clear data from Redis.
        """
        self.redis_client.flushdb()


class BBCTelcoAggregator:
    def __init__(self):
        self.partner_id = settings.BBC_PARTNER_ID
        self.partner_password = settings.BBC_PARTNER_PASSWORD
        self.service_id = settings.BBC_SERVICE_ID

    @classmethod
    def bcc_sp_password(cls):
        partner_id = settings.BBC_PARTNER_ID
        partner_password = settings.BBC_PARTNER_PASSWORD
        timestamp = str(datetime.now().strftime("%Y%m%d%H%M%S"))

        data = partner_id + partner_password + timestamp

        md5_hash = hashlib.md5()

        md5_hash.update(data.encode("utf-8"))

        encrypted_string = md5_hash.hexdigest()

        return encrypted_string, timestamp

    @classmethod
    def on_demand_service_password(cls):
        partner_id = settings.BBC_ON_DEMAND_SERVICE_USERNAME
        # partner_id = settings.BBC_PARTNER_ID
        password = settings.BBC_ON_DEMAND_SERVICE_PASSWORD

        timestamp = str(datetime.now().strftime("%Y%m%d%H%M%S"))

        data = partner_id + password + timestamp

        md5_hash = hashlib.md5()

        md5_hash.update(data.encode("utf-8"))

        encrypted_string = md5_hash.hexdigest()

        return encrypted_string, timestamp

    @classmethod
    def bbc_sms_service_sp_password(cls):
        partner_id = settings.BBC_SMS_SERVICE_USERNAME
        # partner_id = settings.BBC_PARTNER_ID
        password = settings.BBC_SMS_SERVICE_PASSWORD

        timestamp = str(datetime.now().strftime("%Y%m%d%H%M%S"))

        data = partner_id + password + timestamp

        md5_hash = hashlib.md5()

        md5_hash.update(data.encode("utf-8"))

        encrypted_string = md5_hash.hexdigest()

        return encrypted_string, timestamp

    @classmethod
    def end_ussd_session(cls, session_redis_key, **kwargs):
        # kwargs["ussd_string"] = kwargs.get("ussd_string")
        use_json_format = kwargs.get("use_json_format", False)

        kwargs["ussd_op_type"] = "4"
        kwargs["msg_type"] = "2"

        print("end_ussd_session", kwargs, "\n\n\n\n")

        telco_redis_db = TelcoRedisStorage(session_redis_key)
        try:
            telco_redis_db.delete_data()
        except Exception:
            pass

        if use_json_format is False:
            celery_telco_ussd_backgroud.delay(**kwargs)

            return cls.ussd_response(send=False)
        else:
            kwargs["ussdOpType"] = 4
            kwargs["messageType"] = 2

            celery_telco_ussd_backgroud.delay(**kwargs)

    @classmethod
    def start_ussd_notification(cls):
        partner_id = settings.BBC_PARTNER_ID
        sp_password, timestamp = cls.bcc_sp_password()
        service_id = settings.BBC_SERVICE_ID

        partner_id

        f"ws-{uuid.uuid4()}{timestamp}"
        ussd_service_activation_number = settings.BBC_USSD_SERVICE_ACTIVATION_NUMBER

        endpoint = "https://dev.whisperwyse.com/api/ussd/telco_aggregator/"

        correlator = "123456"

        # headers = {
        #     "spId": partner_id,
        #     "spPassword": sp_password,
        #     "serviceId": service_id,
        #     "timeStamp": timestamp,
        #     "reference": reference,
        #     "ussdServiceActivationNumber": ussd_service_activation_number,
        #     "endpoint": endpoint,
        #     "interfaceName": "startUSSDNotification"
        # }

        # <loc:criteria>2929</loc:criteria>

        payload = f"""
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
            xmlns:loc="http://www.csapi.org/schema/osg/ussd/notification_manager/v1_0/local">
            <soapenv:Header>
                <tns:RequestSOAPHeader xmlns:tns="http://www.huawei.com.cn/schema/common/v2_1">
                <tns:spId>{partner_id}</tns:spId>
                <tns:spPassword>{sp_password}</tns:spPassword>
                <tns:serviceId>{service_id}</tns:serviceId>
                <tns:timeStamp>{timestamp}</tns:timeStamp>
                </tns:RequestSOAPHeader>
            </soapenv:Header>
            <soapenv:Body>
                <loc:startUSSDNotification>
                    <loc:reference>
                        <endpoint>{endpoint}</endpoint>
                        <interfaceName>notifyUssdReception</interfaceName>
                        <correlator>{correlator}</correlator>
                    </loc:reference>
                    <loc:ussdServiceActivationNumber>{ussd_service_activation_number}</loc:ussdServiceActivationNumber>

                </loc:startUSSDNotification>
                </soapenv:Body>
                    </soapenv:Envelope>
                    <soapenv:Envelope xmlns:soapenv=http://schemas.xmlsoap.org/soap/envelope/
                    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <soapenv:Body>

                <ns1:startUSSDNotificationResponse
                xmlns:ns1="http://www.csapi.org/schema/osg/ussd/notification_manager/v1_0/local"/>
            </soapenv:Body>
        </soapenv:Envelope>

        """

        print("payload", payload, "\n\n\n")

        url = "http://************:8082/USSDNotificationManagerService/services/USSDNotificationManager"

        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "http://www.csapi.org/schema/osg/ussd/notification_manager/v1_0/local/startUSSDNotification",
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        print(response.text)
        print(response)

    @classmethod
    def send_ussd_message(cls, **kwargs):
        partner_id = settings.BBC_PARTNER_ID
        service_id = settings.BBC_SERVICE_ID
        sp_password, timestamp = cls.bcc_sp_password()

        use_json_format = kwargs.get("use_json_format", False)

        if use_json_format is True:
            return BBCTelcoAggregatorJsonHelper().send_ussd_response(**kwargs)

        # Create the SOAP/XML envelope
        envelope = Element("soapenv:Envelope")
        envelope.set("xmlns:soapenv", "http://schemas.xmlsoap.org/soap/envelope/")
        envelope.set("xmlns:loc", "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local")

        # Create the SOAP/XML header
        header = SubElement(envelope, "soapenv:Header")
        requestSOAPHeader = SubElement(header, "RequestSOAPHeader")

        # Construct the header elements
        spId = SubElement(requestSOAPHeader, "spId")
        spId.text = partner_id

        spPassword = SubElement(requestSOAPHeader, "spPassword")
        spPassword.text = sp_password

        serviceId = SubElement(requestSOAPHeader, "serviceId")
        serviceId.text = service_id

        timeStamp = SubElement(requestSOAPHeader, "timeStamp")
        timeStamp.text = timestamp

        OA = SubElement(requestSOAPHeader, "OA")
        OA.text = kwargs.get("phone_number")

        FA = SubElement(requestSOAPHeader, "FA")
        FA.text = "8613300000010"

        linkid = SubElement(requestSOAPHeader, "linkid")
        linkid.text = "12345678901111"

        # Create the SOAP/XML body
        body = SubElement(envelope, "soapenv:Body")
        sendUssd = SubElement(body, "loc:sendUssd")

        # Construct the body elements
        msgType = SubElement(sendUssd, "loc:msgType")
        msgType.text = {kwargs.get("msg_type")}

        senderCB = SubElement(sendUssd, "loc:senderCB")
        senderCB.text = kwargs.get("sender_cb")

        receiveCB = SubElement(sendUssd, "loc:receiveCB")
        receiveCB.text = kwargs.get("sender_cb")

        ussdOpType = SubElement(sendUssd, "loc:ussdOpType")
        ussdOpType.text = {kwargs.get("ussd_op_type")}

        msIsdn = SubElement(sendUssd, "loc:msIsdn")
        msIsdn.text = kwargs.get("phone_number")

        serviceCode = SubElement(sendUssd, "loc:serviceCode")
        serviceCode.text = kwargs.get("service_code")

        codeScheme = SubElement(sendUssd, "loc:codeScheme")
        codeScheme.text = kwargs.get("code_scheme")

        ussdString = SubElement(sendUssd, "loc:ussdString")
        ussdString.text = kwargs.get("ussd_string")

        # Convert the XML envelope to a string
        # xml_string = tostring(envelope, encoding="unicode")

        # Print the XML string

        # print(
        #     f"""
        # xml_string: {xml_string}
        # \n\n\n\n
        # """
        # )

        # return xml_string

        xml_string = f"""
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:loc="http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local">
            <soapenv:Header>
                <RequestSOAPHeader>
                    <spId>{partner_id}</spId>
                    <spPassword>{sp_password}</spPassword>
                    <serviceId>{service_id}</serviceId>
                    <timeStamp>{timestamp}</timeStamp>
                    <OA>{kwargs.get("phone_number")}</OA>
                    <FA>{kwargs.get("phone_number")}</FA>
                    <linkid>12345678901111</linkid>
                </RequestSOAPHeader>
            </soapenv:Header>
            <soapenv:Body>
                <loc:sendUssd>
                    <loc:msgType>{kwargs.get("msg_type")}</loc:msgType>
                    <loc:senderCB>{kwargs.get("sender_cb")}</loc:senderCB>
                    <loc:receiveCB>{kwargs.get("sender_cb")}</loc:receiveCB>
                    <loc:ussdOpType>{kwargs.get("ussd_op_type")}</loc:ussdOpType>
                    <loc:msIsdn>{kwargs.get("phone_number")}</loc:msIsdn>
                    <loc:serviceCode>{kwargs.get("service_code")}</loc:serviceCode>
                    <loc:codeScheme>68</loc:codeScheme>
                    <loc:ussdString>{kwargs.get("ussd_string")}</loc:ussdString>
                </loc:sendUssd>
            </soapenv:Body>
        </soapenv:Envelope>
        """

        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local/sendUssd",
        }

        url = "http://************:8084/SendUssdService/services/SendUssd"

        print(
            f"""
        SEND USSD: {xml_string}, \n time: {datetime.now().time()} \n\n\n\n\n
        """
        )

        response = requests.request("POST", url, headers=headers, data=xml_string)

        print("send_ussd_message", response.text, "\n\n\n")
        return response.text

    @classmethod
    def notify_ussd_reception_response(cls, **kwargs):
        settings.BBC_PARTNER_ID
        settings.BBC_SERVICE_ID
        sp_password, timestamp = cls.bcc_sp_password()

        # Create the SOAP/XML envelope
        envelope = Element("soapenv:Envelope")
        envelope.set("xmlns:soapenv", "http://schemas.xmlsoap.org/soap/envelope/")
        envelope.set("xmlns:loc", "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local")

        # Create the SOAP/XML body
        body = SubElement(envelope, "soapenv:Body")
        sendUssd = SubElement(body, "ns1:sendUssdResponse")
        sendUssd.set("xmlns:ns1", "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local")
        result = SubElement(sendUssd, "ns1:result")
        result.text = "0"

        xml_string = tostring(envelope, encoding="unicode")

        # xml_string = f"""
        #             <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        #     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        #      <soapenv:Header>
        #      <ns1:NotifySOAPHeader xmlns:ns1="http://www.huawei.com.cn/schema/common/v2_1">
        #      <ns1:spRevId></ns1:spRevId>
        #      <ns1:spRevpassword>{kwargs.get("password")}</ns1:spRevpassword>
        #      <ns1:spId>{kwargs.get("partner_id")}</ns1:spId>
        #      <ns1:serviceId>{kwargs.get("service_id")}</ns1:serviceId>
        #      <ns1:traceUniqueID>0005202000001050</ns1:traceUniqueID>
        #      <ns1:timeStamp>{kwargs.get("timestamp")}</ns1:timeStamp>
        #     <ns1:OperatorID>1</ns1:OperatorID>
        #      </ns1:NotifySOAPHeader>
        #      </soapenv:Header>
        #      <soapenv:Body>
        #      <ns2:notifyUssdReception
        #     xmlns:ns2="http://www.csapi.org/schema/parlayx/ussd/notification/v1_0/local">
        #      <ns2:msgType>0</ns2:msgType>
        #      <ns2:senderCB>{kwargs.get("sender_cb")}</ns2:senderCB>
        #      <ns2:receiveCB>{kwargs.get("sender_cb")}F</ns2:receiveCB>
        #      <ns2:ussdOpType>1</ns2:ussdOpType>
        #      <ns2:msIsdn>{kwargs.get("phone_number")}</ns2:msIsdn>
        #      <ns2:serviceCode>{kwargs.get("service_code")}</ns2:serviceCode>
        #      <ns2:codeScheme>68</ns2:codeScheme>
        #      <ns2:ussdString>{kwargs.get("ussd_string")}</ns2:ussdString>
        #      </ns2:notifyUssdReception>
        #      </soapenv:Body>
        #     </soapenv:Envelope>
        #             """

        # url = "http://************:8082/USSDNotificationManagerService/services/USSDNotificationManager"
        url = "http://************:8084/SendUssdService/services/SendUssd"

        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "http://www.csapi.org/schema/osg/ussd/notification_manager/v1_0/local/startUSSDNotification",
        }

        print(
            f"""
        notify_ussd_reception_response: request body: {xml_string}
        \n\n\n\n\n
        """
        )

        response = requests.request("POST", url, headers=headers, data=xml_string)
        print("notify_ussd_reception_response", response.text, "\n\n\n")
        return response.text

    @classmethod
    def ussd_response(cls, send=True):
        xml_string = """
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><soapenv:Body><ns1:sendUssdResponse xmlns:ns1="http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local"><ns1:result>0</ns1:result></ns1:sendUssdResponse></soapenv:Body></soapenv:Envelope>
        """

        if send is False:
            return xml_string

        url = "http://************:8082/USSDNotificationManagerService/services/USSDNotificationManager"

        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "http://www.csapi.org/schema/osg/ussd/notification_manager/v1_0/local/startUSSDNotification",
        }

        response = requests.request("POST", url, headers=headers, data=xml_string)
        print("notify_ussd_reception_response", response.text, "\n\n\n")
        return response.text

    @classmethod
    def telco_airtime_charge(cls, **kwargs):
        use_json_format = kwargs.get("use_json_format", False)

        use_json_format = False

        channel = kwargs.get("channel", "USSD")

        # partner_id = settings.BBC_PARTNER_ID
        partner_id = settings.BBC_ON_DEMAND_SERVICE_USERNAME
        # service_id = settings.BBC_ON_DEMAND_SERVICE_ID

        print(
            f"""
              telco_airtime_charge kwargs: {kwargs}
              \n\n\n\n\n
              """
        )

        service_id = kwargs.get("service_id")
        product_id = kwargs.get("product_id")
        sp_password, timestamp = cls.on_demand_service_password()
        # sp_password, timestamp = cls.bcc_sp_password()

        # <productId>35100001000012</productId> in the RequestSOAPHeader

        reference = f"{int(time.time())}{str(uuid.uuid4())[:8]}{str(uuid.uuid4())[8:12]}"

        # <amount>kwargs.get("amount")</amount>

        if use_json_format is False:
            xml_string = f"""
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:loc="http://www.csapi.org/schema/parlayx/payment/amount_charging/v2_1/local">
                <soapenv:Header>
                    <RequestSOAPHeader xmlns="http://www.huawei.com.cn/schema/common/v2_1">
                        <spId>{partner_id}</spId>
                        <spPassword>{sp_password}</spPassword>
                        <serviceId>{service_id}</serviceId>
                        <timeStamp>{timestamp}</timeStamp>
                        <productId>{product_id}</productId>
                    </RequestSOAPHeader>
                </soapenv:Header>
                <soapenv:Body>
                    <loc:chargeAmount>
                        <loc:endUserIdentifier>{kwargs.get("phone_number")}</loc:endUserIdentifier>
                        <loc:charge>
                            <description>{kwargs.get("description")}</description>
                            <currency>NGN</currency>
                            <amount>{int(kwargs.get("amount") * 100)}</amount>
                        </loc:charge>
                        <loc:referenceCode>{reference}</loc:referenceCode>
                    </loc:chargeAmount>
                </soapenv:Body>
            </soapenv:Envelope>
            """

            telco_charge_instance = TelcoCharge().create_charge(
                phone_number=kwargs.get("phone_number"),
                amount=kwargs.get("amount"),
                reference=reference,
                payload=xml_string,
                pontential_winning=(kwargs.get("pontential_winning") if kwargs.get("lottery_type") != "AWOOF" else 0),
                game_play_id=kwargs.get("game_play_id"),
                lottery_type=kwargs.get("lottery_type"),
            )

            # print(
            #     f"""
            #       xml_string: {xml_string}
            #       \n\n\n\n
            #       """
            # )

            headers = {
                "Content-Type": "text/xml;charset=UTF-8",
                "SOAPAction": "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local/sendUssd",
            }

            url = "http://************:6999/AmountChargingService/services/AmountCharging"

            response = requests.request("POST", url, headers=headers, data=xml_string)

            telco_charge_instance.response = response.text
            telco_charge_instance.save()

            """
            SAMPLE RESPONSE:

            SUCCESS:
                <?xml version="1.0" encoding="UTF-8"?>
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <soapenv:Body>
                    <ns1:chargeAmountResponse xmlns:ns1="http://www.csapi.org/schema/parlayx/payment/amount_charging/v2_1/local" />
                </soapenv:Body>
                </soapenv:Envelope>

            FAILURE:
                <?xml version="1.0" encoding="UTF-8"?>
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <soapenv:Body>
                    <soapenv:Fault>
                        <faultcode>3001</faultcode>
                        <faultstring />
                        <detail>
                            <ns1:ServiceException xmlns:ns1="http://www.csapi.org/schema/parlayx/common/v2_1">
                            <messageId>3001</messageId>
                            <text />
                            <variables />
                            </ns1:ServiceException>
                        </detail>
                    </soapenv:Fault>
                </soapenv:Body>
                </soapenv:Envelope>
            """

            print("telco_airtime_charge", response.text, "\n\n\n")

            return response.text
        else:
            if str(channel).upper() == "USSD":
                channel_id = 3
            else:
                channel_id = 2

            return BBCTelcoAggregatorJsonHelper().on_demand_charging_request(
                msisdn=kwargs.get("phone_number"),
                serviceId=service_id,
                productId=product_id,
                amount=int(kwargs.get("amount") * 100),
                transactionId=reference,
                channelId=channel_id,
                pontential_winning=(kwargs.get("pontential_winning") if kwargs.get("lottery_type") != "AWOOF" else 0),
                game_play_id=kwargs.get("game_play_id"),
                lottery_type=kwargs.get("lottery_type"),
            )

    @classmethod
    def telco_airtime_subscription_activation(cls, **kwargs):
        # partner_id = settings.BBC_PARTNER_ID
        partner_id = settings.BBC_ON_DEMAND_SERVICE_USERNAME

        print("telco_airtime_subscription_activation", kwargs, "\n\n\n")
        # service_id = settings.BBC_ON_DEMAND_SERVICE_ID

        use_json_format = kwargs.get("use_json_format", False)

        channel = kwargs.get("channel", "USSD")

        service_id = kwargs.get("service_id")
        product_id = kwargs.get("product_id")
        sp_password, timestamp = cls.on_demand_service_password()

        reference = f"{int(time.time())}{str(uuid.uuid4())[:8]}{str(uuid.uuid4())[8:12]}"

        # <amount>kwargs.get("amount")</amount>

        # xml_string = f"""
        # <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:loc="http://www.csapi.org/schema/parlayx/payment/amount_charging/v2_1/local">
        #     <soapenv:Header>
        #         <RequestSOAPHeader xmlns="http://www.huawei.com.cn/schema/common/v2_1">
        #             <v2:spId>{partner_id}</v2:spId>
        #             <v2:spPassword>{sp_password}</v2:spPassword>
        #             <v2:serviceId>{service_id}</v2:serviceId>
        #             <v2:timeStamp>{timestamp}</v2:timeStamp>
        #             <v2:transactionId>{reference}</v2:transactionId>
        #         </RequestSOAPHeader>
        #     </soapenv:Header>
        #     <soapenv:Body>
        #         <loc:subscribeProductRequest>
        #             <loc:subscribeProductReq>
        #                 <userID>
        #                     <ID>{kwargs.get("phone_number")}</ID>
        #                     <type>0</type>
        #                 </userID>
        #                 <subInfo>
        #                     <productID>{product_id}</productID>
        #                     <operCode>en</operCode>
        #                     <isAutoExtend>1</isAutoExtend>
        #                     <channelID>1</channelID>
        #                 </subInfo>
        #             </loc:subscribeProductReq>
        #         </loc:subscribeProductRequest>
        #     </soapenv:Body>
        # </soapenv:Envelope>
        # """

        if use_json_format is False:
            xml_string = f"""
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:loc="http://www.csapi.org/schema/parlayx/subscribe/manage/v1_0/local" xmlns:v2="http://www.huawei.com.cn/schema/common/v2_1">
                <soapenv:Header>
                    <v2:RequestSOAPHeader>
                        <v2:spId>{partner_id}</v2:spId>
                        <v2:spPassword>{sp_password}</v2:spPassword>
                        <v2:serviceId>{service_id}</v2:serviceId>
                        <v2:timeStamp>{timestamp}</v2:timeStamp>
                        <v2:transactionId>{reference}</v2:transactionId>
                    </v2:RequestSOAPHeader>
                </soapenv:Header>
                <soapenv:Body>
                    <loc:subscribeProductRequest>
                        <loc:subscribeProductReq>
                            <userID>
                                <ID>{kwargs.get("phone_number")}</ID>
                                <type>0</type>
                            </userID>
                            <subInfo>
                                <productID>{product_id}</productID>
                                <operCode>en</operCode>
                                <isAutoExtend>1</isAutoExtend>
                                <channelID>3</channelID>
                            </subInfo>
                        </loc:subscribeProductReq>
                    </loc:subscribeProductRequest>
                </soapenv:Body>
            </soapenv:Envelope>

            """

            telco_charge_instance = TelcoCharge().create_charge(
                phone_number=kwargs.get("phone_number"),
                amount=kwargs.get("amount"),
                reference=reference,
                payload=xml_string,
                pontential_winning=(kwargs.get("pontential_winning") if kwargs.get("lottery_type") != "AWOOF" else 0),
                game_play_id=kwargs.get("game_play_id"),
                lottery_type=kwargs.get("lottery_type"),
                charge_type="SUBSCRIPTION",
                channel=kwargs.get("channel", "USSD"),
                charge_reason=kwargs.get("charge_reason", "GAME_PLAY"),
            )

            headers = {
                "Content-Type": "text/xml;charset=UTF-8",
                "SOAPAction": "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local/sendUssd",
            }

            url = "http://************:6991/SubscribeManageService/services/SubscribeManage"

            response = requests.request("POST", url, headers=headers, data=xml_string)

            telco_charge_instance.response = response.text
            telco_charge_instance.save()

            """
            SAMPLE RESPONSE:

            SUCCESS:
                <?xml version="1.0" encoding="UTF-8"?>
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <soapenv:Body>
                    <ns1:chargeAmountResponse xmlns:ns1="http://www.csapi.org/schema/parlayx/payment/amount_charging/v2_1/local" />
                </soapenv:Body>
                </soapenv:Envelope>

            FAILURE:
                <?xml version="1.0" encoding="UTF-8"?>
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
                <soapenv:Body>
                    <soapenv:Fault>
                        <faultcode>3001</faultcode>
                        <faultstring />
                        <detail>
                            <ns1:ServiceException xmlns:ns1="http://www.csapi.org/schema/parlayx/common/v2_1">
                            <messageId>3001</messageId>
                            <text />
                            <variables />
                            </ns1:ServiceException>
                        </detail>
                    </soapenv:Fault>
                </soapenv:Body>
                </soapenv:Envelope>
            """

            # print("telco_airtime_charge", response.text, "\n\n\n")

            # return response.text

        else:
            if str(channel).upper() == "USSD":
                channel_id = 3
            else:
                channel_id = 2

            print("telco_airtime_subscription_activation", kwargs, "\n\n\n")
            return BBCTelcoAggregatorJsonHelper().subscription_charging_request(
                msisdn=kwargs.get("phone_number"),
                serviceId=service_id,
                productId=product_id,
                amount=kwargs.get("amount"),
                transactionId=reference,
                channelId=channel_id,
                pontential_winning=(kwargs.get("pontential_winning") if kwargs.get("lottery_type") != "AWOOF" else 0),
                game_play_id=kwargs.get("game_play_id"),
                lottery_type=kwargs.get("lottery_type"),
            )

    def telco_airtime_unsubscription_request(cls, product_id, phone, service_id=None, use_json_format=False):
        partner_id = settings.BBC_ON_DEMAND_SERVICE_USERNAME

        # service_id = settings.BBC_ON_DEMAND_SERVICE_ID

        # service_id = kwargs.get("service_id")
        # product_id = kwargs.get("product_id")

        if use_json_format is False:
            sp_password, timestamp = cls.on_demand_service_password()

            f"{int(time.time())}{str(uuid.uuid4())[:8]}{str(uuid.uuid4())[8:12]}"

            xml_string = f"""
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                    xmlns:loc="http://www.csapi.org/schema/parlayx/subscribe/manage/v1_0/local">
                <soapenv:Header>
                    <tns:RequestSOAPHeader xmlns:tns="http://www.huawei.com.cn/schema/common/v2_1">
                        <tns:spId>{partner_id}</tns:spId>
                        <tns:spPassword>{sp_password}</tns:spPassword>
                        <tns:timeStamp>{timestamp}</tns:timeStamp>
                    </tns:RequestSOAPHeader>
                </soapenv:Header>
                <soapenv:Body>
                    <loc:unSubscribeProductRequest>
                        <loc:unSubscribeProductReq>
                            <userID>
                                <ID>{phone}</ID>
                                <type>0</type>
                            </userID>
                            <subInfo>
                                <productID>{product_id}</productID>
                                <operCode>en</operCode>
                                <isAutoExtend>0</isAutoExtend>
                                <channelID>1</channelID>
                                <extensionInfo>
                                    <namedParameters>
                                        <key>SubType</key>
                                        <value>0</value>
                                    </namedParameters>
                                </extensionInfo>
                            </subInfo>
                        </loc:unSubscribeProductReq>
                    </loc:unSubscribeProductRequest>
                </soapenv:Body>
            </soapenv:Envelope>

            """

            headers = {
                "Content-Type": "text/xml;charset=UTF-8",
                "SOAPAction": "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local/sendUssd",
            }

            url = "http://************:6991/SubscribeManageService/services/SubscribeManage"

            # print("payload", "\n\n\n")
            print(xml_string, "\n")

            # print(res)

            response = requests.request("POST", url, headers=headers, data=xml_string)

            print("response", response.text, "\n\n\n")

            return response.text
        else:
            return BBCTelcoAggregatorJsonHelper().unsubscription_request(
                msisdn=phone,
                serviceId=service_id,
                productId=product_id,
            )

    @classmethod
    def request_telco_airtime_subscription_activation(cls, data):
        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local/sendUssd",
        }

        url = "http://************:6991/SubscribeManageService/services/SubscribeManage"

        response = requests.request("POST", url, headers=headers, data=data)

        return response.text

    @classmethod
    def request_telco_airtime_unsubscribe_request(cls, data):
        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local/sendUssd",
        }

        url = "http://************:6991/SubscribeManageService/services/SubscribeManagethi"

        response = requests.request("POST", url, headers=headers, data=data)

        return response.text

    @classmethod
    def bbc_send_sms(cls, **kwargs):
        partner_id = settings.BBC_SMS_SERVICE_USERNAME
        # service_id = settings.BBC_ON_DEMAND_SERVICE_ID
        service_id = settings.BBC_SERVICE_ID
        sp_password, timestamp = cls.bbc_sms_service_sp_password()

        use_json_format = kwargs.get("use_json_format", False)

        # use_json_format = False

        if use_json_format is True:
            return BBCTelcoAggregatorJsonHelper().send_sms_via_bbc(
                msisdn=kwargs.get("phone_number"),
                message=kwargs.get("message"),
            )

        # <v2:linkid>12345678901111</v2:linkid> passed in the headers
        # <v2:FA>8613300000010</v2:FA>

        # kwargs["sender_name"] = "20144"

        # xml_string = f"""
        # <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:loc="http://www.csapi.org/schema/parlayx/sms/send/v2_2/local" xmlns:v2="http://www.huawei.com.cn/schema/common/v2_1">
        #     <soapenv:Header>
        #         <v2:RequestSOAPHeader>
        #             <v2:spId>{partner_id}</v2:spId>
        #             <v2:spPassword>{sp_password}</v2:spPassword>
        #             <v2:serviceId>{service_id}</v2:serviceId>
        #             <v2:timeStamp>{timestamp}</v2:timeStamp>
        #             <v2:OA>{kwargs.get("phone_number")}</v2:OA>
        #         </v2:RequestSOAPHeader>
        #     </soapenv:Header>
        #     <soapenv:Body>
        #         <loc:sendSms>
        #             <loc:addresses>{kwargs.get("phone_number")}</loc:addresses>
        #             <loc:senderName>{kwargs.get("sender_name")}</loc:senderName>
        #             <loc:message>{kwargs.get("message")}</loc:message>
        #         </loc:sendSms>
        #     </soapenv:Body>
        # </soapenv:Envelope>
        # """

        phone_number = str(kwargs.get("phone_number"))
        # sender = kwargs.get("sender_name")

        payload_service_id = kwargs.get("service_id", None)
        if payload_service_id is not None:
            service_id = payload_service_id
            # sender = "20144"

        sender = "20144"

        message = kwargs.get("message", None)

        xml_string = f"""
            <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
            <soap:Header>
                <ns3:RequestSOAPHeader xmlns:ns3="http://www.huawei.com.cn/schema/common/v2_1" xmlns:ns2="http://www.csapi.org/schema/parlayx/sms/send/v2_2/local" xmlns:ns4="http://www.csapi.org/schema/parlayx/common/v2_1">
                    <ns3:spId>{partner_id}</ns3:spId>
                    <ns3:spPassword>{sp_password}</ns3:spPassword>
                    <ns3:serviceId>{service_id}</ns3:serviceId>
                    <ns3:timeStamp>{timestamp}</ns3:timeStamp>
                    <ns3:OA>{phone_number}</ns3:OA>
                    <ns3:FA>{phone_number}</ns3:FA>
                </ns3:RequestSOAPHeader>
            </soap:Header>
            <soap:Body>
                <ns2:sendSms xmlns:ns2="http://www.csapi.org/schema/parlayx/sms/send/v2_2/local" xmlns:ns3="http://www.huawei.com.cn/schema/common/v2_1" xmlns:ns4="http://www.csapi.org/schema/parlayx/common/v2_1">
                    <ns2:addresses>{phone_number}</ns2:addresses>
                    <ns2:senderName>{sender}</ns2:senderName>
                    <ns2:message>{message}</ns2:message>
                </ns2:sendSms>
            </soap:Body>
            </soap:Envelope>
        """

        # print(f"bbc_send_sms xml_string: {xml_string}")

        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local/sendUssd",
        }

        url = "http://************:8083/SendSmsService/services/SendSms"

        try:
            response = requests.request("POST", url, headers=headers, data=xml_string, timeout=0.5)
        except requests.exceptions.RequestException as err:
            return str(err)

        except Exception as e:
            return str(e)

        print("bbc_send_sms", response.text, "\n\n\n")

        return response.text

    @classmethod
    def bbc_send_sms_2(cls, **kwargs):
        partner_id = settings.BBC_SMS_SERVICE_USERNAME
        # service_id = settings.BBC_ON_DEMAND_SERVICE_ID
        service_id = settings.BBC_SERVICE_ID
        sp_password, timestamp = cls.bbc_sms_service_sp_password()

        use_json_format = kwargs.get("use_json_format", False)

        if use_json_format is True:
            return BBCTelcoAggregatorJsonHelper().send_sms_via_bbc(
                msisdn=kwargs.get("phone_number"),
                message=kwargs.get("message"),
            )

        # <v2:linkid>12345678901111</v2:linkid> passed in the headers
        # <v2:FA>8613300000010</v2:FA>

        # kwargs["sender_name"] = "20144"

        # xml_string = f"""
        # <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:loc="http://www.csapi.org/schema/parlayx/sms/send/v2_2/local" xmlns:v2="http://www.huawei.com.cn/schema/common/v2_1">
        #     <soapenv:Header>
        #         <v2:RequestSOAPHeader>
        #             <v2:spId>{partner_id}</v2:spId>
        #             <v2:spPassword>{sp_password}</v2:spPassword>
        #             <v2:serviceId>{service_id}</v2:serviceId>
        #             <v2:timeStamp>{timestamp}</v2:timeStamp>
        #             <v2:OA>{kwargs.get("phone_number")}</v2:OA>
        #         </v2:RequestSOAPHeader>
        #     </soapenv:Header>
        #     <soapenv:Body>
        #         <loc:sendSms>
        #             <loc:addresses>{kwargs.get("phone_number")}</loc:addresses>
        #             <loc:senderName>{kwargs.get("sender_name")}</loc:senderName>
        #             <loc:message>{kwargs.get("message")}</loc:message>
        #         </loc:sendSms>
        #     </soapenv:Body>
        # </soapenv:Envelope>
        # """

        phone_number = str(kwargs.get("phone_number"))
        sender = kwargs.get("sender_name")

        payload_service_id = kwargs.get("service_id", None)
        if payload_service_id is not None:
            service_id = payload_service_id
            # sender = "20144"

        message = kwargs.get("message", None)

        xml_string = f"""
            <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
            <soap:Header>
                <ns3:RequestSOAPHeader xmlns:ns3="http://www.huawei.com.cn/schema/common/v2_1" xmlns:ns2="http://www.csapi.org/schema/parlayx/sms/send/v2_2/local" xmlns:ns4="http://www.csapi.org/schema/parlayx/common/v2_1">
                    <ns3:spId>{partner_id}</ns3:spId>
                    <ns3:spPassword>{sp_password}</ns3:spPassword>
                    <ns3:serviceId>{service_id}</ns3:serviceId>
                    <ns3:timeStamp>{timestamp}</ns3:timeStamp>
                    <ns3:OA>{phone_number}</ns3:OA>
                    <ns3:FA>{phone_number}</ns3:FA>
                </ns3:RequestSOAPHeader>
            </soap:Header>
            <soap:Body>
                <ns2:sendSms xmlns:ns2="http://www.csapi.org/schema/parlayx/sms/send/v2_2/local" xmlns:ns3="http://www.huawei.com.cn/schema/common/v2_1" xmlns:ns4="http://www.csapi.org/schema/parlayx/common/v2_1">
                    <ns2:addresses>{phone_number}</ns2:addresses>
                    <ns2:senderName>{sender}</ns2:senderName>
                    <ns2:message>{message}</ns2:message>
                </ns2:sendSms>
            </soap:Body>
            </soap:Envelope>
        """

        # print(f"bbc_send_sms xml_string: {xml_string}")

        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local/sendUssd",
        }

        url = "http://************:8083/SendSmsService/services/SendSms"

        try:
            response = requests.request("POST", url, headers=headers, data=xml_string, timeout=0.5)
        except requests.exceptions.RequestException as err:
            return {"payload": xml_string, "response": str(err)}

        except Exception as e:
            return {"payload": xml_string, "response": str(e)}

        # print("bbc_send_sms", response.text, "\n\n\n")

        return {"payload": xml_string, "response": response.text}

    @classmethod
    def datasync_response(cls):
        xml_string = """
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:loc="http://www.csapi.org/schema/parlayx/data/sync/v1_0/local"> <soapenv:Header /> <soapenv:Body> <loc:syncOrderRelationResponse> <loc:result>0</loc:result> <loc:resultDescription>OK</loc:resultDescription> </loc:syncOrderRelationResponse> </soapenv:Body> </soapenv:Envelope>
        """

        return xml_string

    @classmethod
    def ask_ai_first_prompt_sms(cls, **kwargs):
        partner_id = settings.BBC_SMS_SERVICE_USERNAME
        # service_id = settings.BBC_ON_DEMAND_SERVICE_ID
        service_id = settings.BBC_SERVICE_ID
        sp_password, timestamp = cls.bbc_sms_service_sp_password()

        use_json_format = kwargs.get("use_json_format", False)

        sender = "20144"

        phone_number = str(kwargs.get("phone_number"))

        payload_service_id = kwargs.get("service_id", None)

        if payload_service_id is not None or payload_service_id != "":
            service_id = payload_service_id
        # else:
        #     phone_number = phone_number[:3]

        message = kwargs.get("message", None)
        if message is None:
            message = "Welcome to wisewinn 'Ask AI'.\nYou can ask us anything\n\n"

        if use_json_format is True:
            return BBCTelcoAggregatorJsonHelper().send_sms_via_bbc(
                msisdn=kwargs.get("phone_number"),
                message=message,
            )

        xml_string = f"""
            <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
            <soap:Header>
                <ns3:RequestSOAPHeader xmlns:ns3="http://www.huawei.com.cn/schema/common/v2_1" xmlns:ns2="http://www.csapi.org/schema/parlayx/sms/send/v2_2/local" xmlns:ns4="http://www.csapi.org/schema/parlayx/common/v2_1">
                    <ns3:spId>{partner_id}</ns3:spId>
                    <ns3:spPassword>{sp_password}</ns3:spPassword>
                    <ns3:serviceId>{service_id}</ns3:serviceId>
                    <ns3:timeStamp>{timestamp}</ns3:timeStamp>
                    <ns3:OA>{phone_number}</ns3:OA>
                    <ns3:FA>{phone_number}</ns3:FA>
                </ns3:RequestSOAPHeader>
            </soap:Header>
            <soap:Body>
                <ns2:sendSms xmlns:ns2="http://www.csapi.org/schema/parlayx/sms/send/v2_2/local" xmlns:ns3="http://www.huawei.com.cn/schema/common/v2_1" xmlns:ns4="http://www.csapi.org/schema/parlayx/common/v2_1">
                    <ns2:addresses>{phone_number}</ns2:addresses>
                    <ns2:senderName>{sender}</ns2:senderName>
                    <ns2:message>{message}</ns2:message>
                </ns2:sendSms>
            </soap:Body>
            </soap:Envelope>
        """

        print(f"ask_ai_first_prompt_sms xml_string: {xml_string}")

        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "SOAPAction": "http://www.csapi.org/schema/parlayx/ussd/send/v1_0/local/sendUssd",
        }

        url = "http://************:8083/SendSmsService/services/SendSms"

        try:
            response = requests.request("POST", url, headers=headers, data=xml_string)
        except requests.exceptions.RequestException as err:
            return str(err)

        except Exception as e:
            return str(e)

        # print("bbc_send_sms", response.text, "\n\n\n")

        return response.text


class BBCTelcoAggregatorJsonHelper:
    def __init__(self):
        self.partner_id = config("BROAD_BASE_PARTNER_ID")
        self.partner_password = config("BROADBASE_PARTNER_PASSWORD")
        self.service_id = config("BROADBASE_PARTNER_SERVICE_ID")
        self.base_url = config("BBC_JSON_BASE_URL")

    def bcc_sp_password(self):
        partner_id = config("BROAD_BASE_PARTNER_ID")
        partner_password = config("BROADBASE_PARTNER_PASSWORD")
        # timestamp = str(datetime.now().strftime("%Y%m%d%H%M%S"))
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")

        data = partner_id + partner_password + timestamp

        md5_hash = hashlib.md5()

        md5_hash.update(data.encode("utf-8"))

        encrypted_string = md5_hash.hexdigest()

        return encrypted_string, timestamp

    def on_demand_service_password(self):
        partner_id = config("BROADBASE_ON_DEMAND_SERVICE_USERNAME")
        # partner_id = settings.BBC_PARTNER_ID
        password = config("BROADBASE_ON_DEMAND_SERVICE_PASSWORD")

        # timestamp = str(datetime.now().strftime("%Y%m%d%H%M%S"))

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")

        data = partner_id + password + timestamp

        md5_hash = hashlib.md5()

        md5_hash.update(data.encode("utf-8"))

        encrypted_string = md5_hash.hexdigest()

        return encrypted_string, timestamp

    def bbc_sms_service_sp_password(self):
        partner_id = config("BROADBASE_SMS_SERVICE_USERNAME")
        # partner_id = settings.BBC_PARTNER_ID
        password = config("BROADBASE_SMS_SERVICE_PASSWORD")

        # timestamp = str(datetime.now().strftime("%Y%m%d%H%M%S"))

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")

        data = partner_id + password + timestamp

        md5_hash = hashlib.md5()

        md5_hash.update(data.encode("utf-8"))

        encrypted_string = md5_hash.hexdigest()

        return encrypted_string, timestamp

    def bcc_airtime_charge_password(self):
        partner_id = config("BROADBASE_AIRTIME_CHARGE_PARTNER_ID")
        partner_password = config("BROADBASE_AIRTIME_CHARGE_PARTNER_PASSWORD")
        # timestamp = str(datetime.now().strftime("%Y%m%d%H%M%S"))
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")

        data = partner_id + partner_password + timestamp

        md5_hash = hashlib.md5()

        md5_hash.update(data.encode("utf-8"))

        encrypted_string = md5_hash.hexdigest()

        return encrypted_string, timestamp

    def on_demand_charging_request(
        self,
        msisdn: str,
        serviceId: str,
        productId: str,
        amount: float,
        transactionId: str,
        channelId=3,
        pontential_winning=0,
        game_play_id=None,
        lottery_type=None,
    ):
        payload = {
            "msisdn": msisdn,
            "serviceId": serviceId,
            "productId": productId,
            "amount": amount,
            "channelId": channelId,
            "transactionId": transactionId,
        }

        url = self.base_url + "/vas/initiateSubscription"

        sp_password, timestamp = self.on_demand_service_password()

        headers = {
            "Content-Type": "application/json",
            "spId": config("BROADBASE_ON_DEMAND_SERVICE_USERNAME"),
            "spPassword": sp_password,
            "timeStamp": timestamp,
        }

        telco_charge_instance = TelcoCharge().create_charge(
            phone_number=msisdn,
            amount=amount,
            reference=transactionId,
            payload=payload,
            pontential_winning=pontential_winning,
            game_play_id=game_play_id,
            lottery_type=lottery_type,
        )

        response = requests.request("POST", url, headers=headers, json=payload)

        telco_charge_instance.response = response.text
        telco_charge_instance.save()

        return response.text

    def subscription_charging_request(
        self,
        msisdn: str,
        serviceId: str,
        productId: str,
        amount: float,
        transactionId: str,
        channelId=3,
        pontential_winning=0,
        game_play_id=None,
        lottery_type=None,
    ):
        print(
            "subscription_charging_request PAYLOAD",
            msisdn,
            serviceId,
            productId,
            amount,
            transactionId,
            channelId,
            pontential_winning,
            game_play_id,
            lottery_type,
        )

        msisdn = str(msisdn)
        phone_number = f"0{msisdn[-10:]}"
        payload = {
            "msisdn": phone_number,
            "serviceId": str(serviceId),
            "productId": str(productId),
            "amount": amount,
            "channelId": channelId,
            "transactionId": transactionId,
        }

        url = self.base_url + "/vas/initiateSubscription"

        print("payload", payload, "\n\n\n")

        print("subscription_charging_request URL", url)

        sp_password, timestamp = self.bcc_airtime_charge_password()

        headers = {
            "Content-Type": "application/json",
            "spId": config("BROADBASE_AIRTIME_CHARGE_PARTNER_ID"),
            "spPassword": sp_password,
            "timeStamp": timestamp,
        }

        print("subscription_charging_request HEADERS", headers)

        telco_charge_instance = TelcoCharge().create_charge(
            phone_number=msisdn,
            amount=amount,
            reference=transactionId,
            payload=payload,
            pontential_winning=pontential_winning,
            game_play_id=game_play_id,
            lottery_type=lottery_type,
        )

        response = requests.request("POST", url, headers=headers, json=payload)

        telco_charge_instance.response = response.text
        telco_charge_instance.save()

        print("subscription_charging_request RESPONSE", response.text)

        return response.text

    def unsubscription_request(self, msisdn, serviceId, productId):
        url = self.base_url + "/vas/initiateUnsubscription"

        sp_password, timestamp = self.bcc_sp_password()

        headers = {"Content-Type": "application/json", "spId": config("BROAD_BASE_PARTNER_ID"), "spPassword": sp_password, "timeStamp": timestamp}

        payload = {"msisdn": msisdn, "serviceId": serviceId, "productId": productId}

        response = requests.request("POST", url, headers=headers, json=payload)

        return response.text

    def send_ussd_response(self, **kwargs):
        try:
            del kwargs["use_json_format"]
        except Exception:
            pass

        payload = {
            "msisdn": kwargs.get("msisdn"),
            "network": kwargs.get("network"),
            "serviceCode": kwargs.get("serviceCode"),
            "sessionId": kwargs.get("sessionId"),
            "text": kwargs.get("text"),
            "ussdOpType": kwargs.get("ussdOpType"),
            "messageType": kwargs.get("messageType"),
        }

        sp_password, timestamp = self.bcc_sp_password()

        headers = {"Content-Type": "application/json", "spId": config("BROAD_BASE_PARTNER_ID"), "spPassword": sp_password, "timeStamp": timestamp}

        url = self.base_url + "/ussd/sendUssdRequest"

        response = requests.request("POST", url, headers=headers, json=payload)

        return response.text

    def send_sms_via_bbc(self, msisdn, message, network="MTN", service_code="20144"):
        reference = f"{int(time.time())}{str(uuid.uuid4())[:8]}{str(uuid.uuid4())[8:12]}"

        phone_number = f"0{msisdn[-10:]}"

        payload = {"network": network, "msisdn": phone_number, "serviceCode": service_code, "message": message, "messageId": reference}

        url = self.base_url + "/sms/sendSmsRequest"

        print(f"url: {url}")
        print(f"payload: {payload}")

        sp_password, timestamp = self.bbc_sms_service_sp_password()

        headers = {
            "Content-Type": "application/json",
            "spId": config("BROADBASE_SMS_SERVICE_USERNAME"),
            "spPassword": sp_password,
            "timeStamp": timestamp,
        }
        print(f"headers: {headers}")

        response = requests.request("POST", url, headers=headers, json=payload)
        print(f"response: {response.text}")

        return response.text
